from _typeshed import Incomplete, ReadableBuffer
from typing import Final

DEBUG: Final[bool]
SCRAMBLE_LENGTH: Final[int]
sha1_new: Incomplete

def scramble_native_password(password: ReadableBuffer | None, message: ReadableBuffer | None) -> bytes: ...
def ed25519_password(password: ReadableBuffer, scramble: ReadableBuffer) -> bytes: ...
def sha2_rsa_encrypt(password: bytes, salt: bytes, public_key: bytes) -> bytes: ...
def sha256_password_auth(conn, pkt): ...
def scramble_caching_sha2(password: ReadableBuffer, nonce: ReadableBuffer) -> bytes: ...
def caching_sha2_password_auth(conn, pkt) -> Incomplete | None: ...
