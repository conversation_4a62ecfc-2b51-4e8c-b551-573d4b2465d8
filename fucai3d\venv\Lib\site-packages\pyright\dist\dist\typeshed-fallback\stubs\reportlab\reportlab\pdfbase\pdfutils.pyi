from typing import Final

__version__: Final[str]

def makeA85Image(filename, IMG=None, detectJpeg: bool = False): ...
def makeRawImage(filename, IMG=None, detectJpeg: bool = False): ...
def cacheImageFile(filename, returnInMemory: int = 0, IMG=None): ...
def preProcessImages(spec) -> None: ...
def cachedImageExists(filename): ...
def readJPEGInfo(image): ...

class _fusc:
    def __init__(self, k, n) -> None: ...
    def encrypt(self, s): ...
    def decrypt(self, s): ...
