import sys
from _typeshed import ReadableBuffer
from collections.abc import Mapping
from logging import _ExcInfoType
from socket import _RetAddress, socket
from threading import Thread
from typing import Final, Protocol

from paramiko.channel import Channel
from paramiko.message import Message, _LikeBytes
from paramiko.pkey import P<PERSON>ey
from paramiko.transport import Transport

class _AgentProxy(Protocol):
    def connect(self) -> None: ...
    def close(self) -> None: ...

cSSH2_AGENTC_REQUEST_IDENTITIES: Final[bytes]
SSH2_AGENT_IDENTITIES_ANSWER: Final = 12
cSSH2_AGENTC_SIGN_REQUEST: Final[bytes]
SSH2_AGENT_SIGN_RESPONSE: Final = 14

SSH_AGENT_RSA_SHA2_256: Final = 2
SSH_AGENT_RSA_SHA2_512: Final = 4
ALGORITHM_FLAG_MAP: Final[dict[str, int]]
key: str
value: int

class AgentSSH:
    def __init__(self) -> None: ...
    def get_keys(self) -> tuple[<PERSON><PERSON><PERSON>, ...]: ...

class AgentProxyThread(Thread):
    def __init__(self, agent: _AgentProxy) -> None: ...
    def run(self) -> None: ...

class AgentLocalProxy(AgentProxyThread):
    def __init__(self, agent: AgentServerProxy) -> None: ...
    def get_connection(self) -> tuple[socket, _RetAddress]: ...

class AgentRemoteProxy(AgentProxyThread):
    def __init__(self, agent: AgentClientProxy, chan: Channel) -> None: ...
    def get_connection(self) -> tuple[socket, _RetAddress]: ...

if sys.platform == "win32":
    from .win_openssh import OpenSSHAgentConnection
    from .win_pageant import PageantConnection

    def get_agent_connection() -> PageantConnection | OpenSSHAgentConnection | None: ...

else:
    def get_agent_connection() -> socket | None: ...

class AgentClientProxy:
    thread: Thread
    def __init__(self, chanRemote: Channel) -> None: ...
    def __del__(self) -> None: ...
    def connect(self) -> None: ...
    def close(self) -> None: ...

class AgentServerProxy(AgentSSH):
    thread: Thread
    def __init__(self, t: Transport) -> None: ...
    def __del__(self) -> None: ...
    def connect(self) -> None: ...
    def close(self) -> None: ...
    def get_env(self) -> dict[str, str]: ...

class AgentRequestHandler:
    def __init__(self, chanClient: Channel) -> None: ...
    def __del__(self) -> None: ...
    def close(self) -> None: ...

class Agent(AgentSSH):
    def __init__(self) -> None: ...
    def close(self) -> None: ...

class AgentKey(PKey):
    agent: AgentSSH
    blob: bytes
    public_blob: None
    name: str
    comment: str
    def __init__(self, agent: AgentSSH, blob: ReadableBuffer, comment: str = "") -> None: ...
    def log(
        self,
        level: int,
        msg: object,
        *args: object,
        exc_info: _ExcInfoType = None,
        stack_info: bool = False,
        stacklevel: int = 1,
        extra: Mapping[str, object] | None = None,
    ) -> None: ...
    def asbytes(self) -> bytes: ...
    def get_name(self) -> str: ...
    def sign_ssh_data(self, data: _LikeBytes, algorithm: str | None = None) -> Message: ...
