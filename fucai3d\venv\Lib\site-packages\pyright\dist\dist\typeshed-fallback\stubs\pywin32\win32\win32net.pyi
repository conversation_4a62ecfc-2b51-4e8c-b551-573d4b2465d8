from _typeshed import Incomplete

from win32.lib.pywintypes import error as error

def NetGetJoinInformation() -> tuple[str, Incomplete]: ...
def NetGroupGetInfo(server: str, groupname: str, level, /): ...
def NetGroupGetUsers(
    server: str, groupName: str, level, resumeHandle: int = ..., prefLen: int = ..., /
) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
def NetGroupSetUsers(server: str, group: str, level, members: tuple[Incomplete, Incomplete], /) -> None: ...
def NetGroupSetInfo(server: str, groupname: str, level, data, /) -> None: ...
def NetGroupAdd(server: str, level, data, /) -> None: ...
def NetGroupAddUser(server: str, group: str, username: str, /) -> None: ...
def NetGroupDel(server: str, groupname: str, /) -> None: ...
def NetGroupDelUser(server: str, group: str, username: str, /) -> None: ...
def NetGroupEnum(server: str, level, prefLen, resumeHandle=..., /) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
def NetLocalGroupAddMembers(server: str, group: str, level, members: tuple[Incomplete, Incomplete], /) -> None: ...
def NetLocalGroupDelMembers(server: str, group: str, members: list[str], /) -> None: ...
def NetLocalGroupGetMembers(
    server: str, groupName: str, level, resumeHandle: int = ..., prefLen: int = ..., /
) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
def NetLocalGroupSetMembers(server: str, group: str, level, members: tuple[Incomplete, Incomplete], /) -> None: ...
def NetMessageBufferSend(domain: str, userName: str, fromName: str, message: str, /) -> None: ...
def NetMessageNameAdd(server, msgname, /) -> None: ...
def NetMessageNameDel(server, msgname, /) -> None: ...
def NetMessageNameEnum(Server, /) -> None: ...
def NetServerEnum(
    server: str, level, _type, prefLen, domain: str | None = ..., resumeHandle: int = ..., /
) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
def NetServerGetInfo(server: str, level, /): ...
def NetServerSetInfo(server: str, level, data, /) -> None: ...
def NetShareAdd(server: str, level, data, /) -> None: ...
def NetShareDel(server: str, shareName: str, reserved: int = ..., /) -> None: ...
def NetShareCheck(server: str, deviceName: str, /) -> tuple[Incomplete, Incomplete]: ...
def NetShareEnum(
    server: str, level, prefLen, serverName, resumeHandle=..., /
) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
def NetShareGetInfo(server: str, netname: str, level, /): ...
def NetShareSetInfo(server: str, netname: str, level, data, /) -> None: ...
def NetUserAdd(server: str, level, data, /) -> None: ...
def NetUserChangePassword(server: str, username: str, oldPassword: str, newPassword: str, /) -> None: ...
def NetUserEnum(
    server: str, level, arg, prefLen, resumeHandle=..., /
) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
def NetUserGetGroups(serverName: str, userName: str, /) -> list[tuple[Incomplete, Incomplete]]: ...
def NetUserGetInfo(server: str, username: str, level, /): ...
def NetUserGetLocalGroups(serverName: str, userName: str, flags, /) -> list[Incomplete]: ...
def NetUserSetInfo(server: str, username: str, level, data, /) -> None: ...
def NetUserDel(server: str, username: str, /) -> None: ...
def NetUserModalsGet(server: str, level, /): ...
def NetUserModalsSet(server: str, level, data, /) -> None: ...
def NetWkstaUserEnum(
    server: str, level, prefLen, resumeHandle=..., /
) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
def NetWkstaGetInfo(server: str, level, /): ...
def NetWkstaSetInfo(server: str, level, data, /) -> None: ...
def NetWkstaTransportEnum(
    server: str, level, prefLen, resumeHandle=..., /
) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
def NetWkstaTransportAdd(server: str, level, data, /) -> None: ...
def NetWkstaTransportDel(server: str, TransportName: str, ucond: int = ..., /) -> None: ...
def NetServerDiskEnum(server: str, level, /): ...
def NetUseAdd(server: str, level, data, /) -> None: ...
def NetUseDel(server: str, useName: str, forceCond: int = ..., /) -> None: ...
def NetUseEnum(server: str, level, prefLen, resumeHandle=..., /) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
def NetUseGetInfo(server: str, usename: str, level: int = ..., /): ...
def NetGetAnyDCName(server: str | None = ..., domain: str | None = ..., /) -> str: ...
def NetGetDCName(server: str | None = ..., domain: str | None = ..., /) -> str: ...
def NetSessionEnum(
    level, server: str | None = ..., client: str | None = ..., username: str | None = ..., /
) -> tuple[Incomplete, ...]: ...
def NetSessionDel(server: str, client: str | None = ..., username: str | None = ..., /) -> None: ...
def NetSessionGetInfo(level, server: str, client: str, username: str, /): ...
def NetFileEnum(
    level, servername: str | None = ..., basepath: str | None = ..., username: str | None = ..., /
) -> tuple[Incomplete, ...]: ...
def NetFileClose(servername: str, fileid, /) -> None: ...
def NetFileGetInfo(level, servername: str, fileid, /): ...
def NetStatisticsGet(server: str, service: str, level, options, /): ...
def NetServerComputerNameAdd(ServerName: str, EmulatedDomainName: str, EmulatedServerName: str, /) -> None: ...
def NetServerComputerNameDel(ServerName: str, EmulatedServerName: str, /) -> None: ...
def NetValidateName(Server: str, Name: str, NameType, Account: str | None = ..., Password: str | None = ..., /) -> None: ...
def NetValidatePasswordPolicy(Server: str, Qualifier, ValidationType, arg, /) -> None: ...
def NetLocalGroupAdd(*args): ...  # incomplete
def NetLocalGroupDel(*args): ...  # incomplete
def NetLocalGroupEnum(*args): ...  # incomplete
def NetLocalGroupGetInfo(*args): ...  # incomplete
def NetLocalGroupSetInfo(*args): ...  # incomplete

SERVICE_SERVER: str
SERVICE_WORKSTATION: str
USE_FORCE: int
USE_LOTS_OF_FORCE: int
USE_NOFORCE: int
