from _typeshed import Incomplete, Unused
from collections.abc import Callable
from typing import ClassVar

from ..cmd import Command

def show_formats() -> None: ...

class sdist(Command):
    description: ClassVar[str]

    def checking_metadata(self): ...

    user_options: ClassVar[list[tuple[str, str | None, str]]]
    boolean_options: ClassVar[list[str]]
    help_options: ClassVar[list[tuple[str, str | None, str, Callable[[], Unused]]]]
    negative_opt: ClassVar[dict[str, str]]
    READMES: ClassVar[tuple[str, ...]]
    template: Incomplete
    manifest: Incomplete
    use_defaults: bool
    prune: bool
    manifest_only: bool
    force_manifest: bool
    formats: Incomplete
    keep_temp: bool
    dist_dir: Incomplete
    archive_files: Incomplete
    metadata_check: int  # Soon to be updated to boolean upstream
    owner: Incomplete
    group: Incomplete
    def initialize_options(self) -> None: ...
    def finalize_options(self) -> None: ...
    filelist: Incomplete
    def run(self) -> None: ...
    def get_file_list(self) -> None: ...
    def add_defaults(self) -> None: ...
    def read_template(self) -> None: ...
    def prune_file_list(self) -> None: ...
    def write_manifest(self) -> None: ...
    def read_manifest(self) -> None: ...
    def make_release_tree(self, base_dir, files) -> None: ...
    def make_distribution(self) -> None: ...
    def get_archive_files(self): ...

def is_comment(line: str) -> bool: ...
