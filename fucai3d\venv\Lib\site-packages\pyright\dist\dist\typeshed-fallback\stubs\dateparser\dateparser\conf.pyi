from typing import Any
from typing_extensions import Self

class Settings:
    def __new__(cls, *args, **kw) -> Self: ...
    def __init__(self, settings: dict[str, Any] | None = None) -> None: ...
    @classmethod
    def get_key(cls, settings: dict[str, Any] | None = None) -> str: ...
    def replace(self, mod_settings: dict[str, Any] | None = None, **kwds) -> Self: ...

settings: Settings

def apply_settings(f): ...

class SettingValidationError(ValueError): ...

def check_settings(settings: Settings) -> None: ...
