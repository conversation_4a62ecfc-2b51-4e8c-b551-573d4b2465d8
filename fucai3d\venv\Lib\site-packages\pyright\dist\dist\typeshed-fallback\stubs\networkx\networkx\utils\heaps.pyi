from _typeshed import Incomplete

__all__ = ["MinHea<PERSON>", "PairingHeap", "BinaryHeap"]

class MinHeap:
    class _Item:
        key: Incomplete
        value: Incomplete
        def __init__(self, key, value) -> None: ...

    def __init__(self) -> None: ...
    def min(self) -> tuple[Incomplete, Incomplete]: ...
    def pop(self) -> tuple[Incomplete, Incomplete]: ...
    def get(self, key, default=None): ...
    def insert(self, key, value, allow_increase: bool = False) -> bool: ...
    def __nonzero__(self): ...
    def __bool__(self) -> bool: ...
    def __len__(self) -> int: ...
    def __contains__(self, key) -> bool: ...

class PairingHeap(MinHeap):
    class _Node(MinHeap._Item):
        left: Incomplete
        next: Incomplete
        prev: Incomplete
        parent: Incomplete
        def __init__(self, key, value) -> None: ...

    def __init__(self) -> None: ...
    def min(self) -> tuple[Incomplete, Incomplete]: ...
    def pop(self) -> tuple[Incomplete, Incomplete]: ...
    def get(self, key, default=None): ...
    def insert(self, key, value, allow_increase: bool = False) -> bool: ...

class BinaryHeap(MinHeap):
    def __init__(self) -> None: ...
    def min(self) -> tuple[Incomplete, Incomplete]: ...
    def pop(self) -> tuple[Incomplete, Incomplete]: ...
    def get(self, key, default=None): ...
    def insert(self, key, value, allow_increase: bool = False) -> bool: ...
