def to_unicode(obj: float | bytes | str, encoding: str | None = None, from_server: bool = False) -> str: ...
def to_raw(obj, encoding: str = "utf-8"): ...
def escape_filter_chars(text: float | bytes | str, encoding: str | None = None) -> str: ...
def unescape_filter_chars(text, encoding=None): ...
def escape_bytes(bytes_value: str | bytes) -> str: ...
def prepare_for_stream(value): ...
def json_encode_b64(obj): ...
def check_json_dict(json_dict) -> None: ...
def json_hook(obj): ...
def format_json(obj, iso_format: bool = False): ...
def is_filter_escaped(text): ...
def ldap_escape_to_bytes(text): ...
