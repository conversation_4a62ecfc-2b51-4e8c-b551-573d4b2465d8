from _typeshed import Incomplete
from typing_extensions import Self

from braintree.validation_error import ValidationError

class ValidationErrorCollection:
    data: dict[str, Incomplete]
    def __init__(self, data: dict[str, Incomplete] | None = None) -> None: ...
    @property
    def deep_errors(self) -> list[ValidationError]: ...
    def for_index(self, index: int | str) -> Self: ...
    def for_object(self, nested_key: str) -> Self: ...
    def on(self, attribute: str) -> list[ValidationError]: ...
    @property
    def deep_size(self) -> int: ...
    @property
    def errors(self) -> list[ValidationError]: ...
    @property
    def size(self) -> int: ...
    def __getitem__(self, index: int) -> ValidationError: ...
    def __len__(self) -> int: ...
