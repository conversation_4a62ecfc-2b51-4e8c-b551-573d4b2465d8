from _typeshed import Incomplete
from typing_extensions import Self

from braintree.graphql.inputs.monetary_amount_input import MonetaryAmountInput
from braintree.graphql.inputs.paypal_payee_input import PayPalPayeeInput

class PayPalPurchaseUnitInput:
    def __init__(self, amount: MonetaryAmountInput | None = None, payee: PayPalPayeeInput | None = None) -> None: ...
    def to_graphql_variables(self) -> dict[str, Incomplete]: ...
    @staticmethod
    def builder(amount: MonetaryAmountInput) -> Builder: ...

    class Builder:
        def __init__(self, amount: MonetaryAmountInput) -> None: ...
        def payee(self, payee: PayPalPayeeInput) -> Self: ...
        def build(self) -> PayPalPurchaseUnitInput: ...
