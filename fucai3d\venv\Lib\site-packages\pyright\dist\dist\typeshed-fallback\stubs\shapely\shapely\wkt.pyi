from ._typing import SupportsRead, SupportsWrite
from .geometry.base import BaseGeometry
from .lib import Geometry

def loads(data: str) -> BaseGeometry: ...
def load(fp: SupportsRead[str]) -> BaseGeometry: ...
def dumps(ob: Geometry, trim: bool = False, rounding_precision: int = -1, **kw) -> str: ...
def dump(ob: Geometry, fp: SupportsWrite[str], *, trim: bool = False, rounding_precision: int = -1, **kw) -> None: ...
