from logging import Logger

from authlib.integrations.base_client.sync_app import OAuth1B<PERSON>, OAuth2Base

log: Logger

__all__ = ["AsyncOAuth1Mixin", "AsyncOAuth2Mixin"]

class AsyncOAuth1Mixin(OAuth1Base):
    async def request(self, method, url, token=None, **kwargs): ...
    async def create_authorization_url(self, redirect_uri=None, **kwargs): ...
    async def fetch_access_token(self, request_token=None, **kwargs): ...

class AsyncOAuth2Mixin(OAuth2Base):
    async def load_server_metadata(self): ...
    async def request(self, method, url, token=None, **kwargs): ...
    async def create_authorization_url(self, redirect_uri=None, **kwargs): ...
    async def fetch_access_token(self, redirect_uri=None, **kwargs): ...
