from _typeshed import Incomplete, Unused
from collections.abc import Callable
from typing import Final, Literal
from typing_extensions import TypeAlias

from reportlab.lib.colors import Color
from reportlab.pdfbase.ttfonts import ShapedStr
from reportlab.pdfgen.canvas import Canvas

# NOTE: This is slightly different from what toColor accepts and interprets
_Color: TypeAlias = Color | tuple[float, float, float, float] | tuple[float, float, float] | list[float] | str

__version__: Final[str]
log2vis: Callable[..., str | None]
BidiStr: type[str]
BidiList: type[list[Incomplete]]
BidiIndex: Incomplete

def bidiText(text: str, direction: str | None) -> str: ...
def bidiShapedText(
    text: str, direction: str = "RTL", clean: bool = True, fontName: str = "Helvetica", fontSize: int = 10, shaping: bool = False
) -> tuple[ShapedStr | str, float]: ...
def isBidiStr(_: Unused) -> bool: ...
def isBidiList(_: Unused) -> bool: ...
def innerBidiStrWrap(s: str, bidiV: int = -1, bidiL: int = -1) -> str: ...
def bidiStrWrap(s: str, orig: str) -> str: ...
def bidiListWrap(L, orig) -> list[Incomplete]: ...
def bidiFragWord(w: str, direction: str | None = None, bidiV: int = -1, bidiL: int = -1, clean: bool = True): ...
def bidiWordList(
    words: list[str] | tuple[str], direction: str = "RTL", clean: bool = True, wx: bool = False
) -> list[Incomplete]: ...

rtlSupport: bool

class _PDFColorSetter:
    def setFillColorCMYK(self, c: float, m: float, y: float, k: float, alpha: float | None = None) -> None: ...
    def setStrokeColorCMYK(self, c: float, m: float, y: float, k: float, alpha: float | None = None) -> None: ...
    def setFillColorRGB(self, r: float, g: float, b: float, alpha: float | None = None) -> None: ...
    def setStrokeColorRGB(self, r: float, g: float, b: float, alpha: float | None = None) -> None: ...
    def setFillColor(self, aColor: _Color, alpha: float | None = None) -> None: ...
    def setStrokeColor(self, aColor: _Color, alpha: float | None = None) -> None: ...
    def setFillGray(self, gray: float, alpha: float | None = None) -> None: ...
    def setStrokeGray(self, gray: float, alpha: float | None = None) -> None: ...
    def setStrokeAlpha(self, a: float) -> None: ...
    def setFillAlpha(self, a: float) -> None: ...
    def setStrokeOverprint(self, a) -> None: ...
    def setFillOverprint(self, a) -> None: ...
    def setOverprintMask(self, a) -> None: ...

class PDFTextObject(_PDFColorSetter):
    direction: Literal["LTR", "RTL"]
    def __init__(self, canvas: Canvas, x: float = 0, y: float = 0, direction: Literal["LTR", "RTL"] | None = None) -> None: ...
    def getCode(self) -> str: ...
    def setTextOrigin(self, x: float, y: float) -> None: ...
    def setTextTransform(self, a: float, b: float, c: float, d: float, e: float, f: float) -> None: ...
    def moveCursor(self, dx: float, dy: float) -> None: ...
    def setXPos(self, dx: float) -> None: ...
    def getCursor(self) -> tuple[float, float]: ...
    def getStartOfLine(self) -> tuple[float, float]: ...
    def getX(self) -> float: ...
    def getY(self) -> float: ...
    def setFont(self, psfontname: str, size: float, leading: float | None = None) -> None: ...
    def setCharSpace(self, charSpace: float) -> None: ...
    def setWordSpace(self, wordSpace: float) -> None: ...
    def setHorizScale(self, horizScale: float) -> None: ...
    def setLeading(self, leading: float) -> None: ...
    def setTextRenderMode(self, mode: Literal[0, 1, 2, 3, 4, 5, 6, 7]) -> None: ...
    def setRise(self, rise: float) -> None: ...
    def textOut(self, text: str) -> None: ...
    def textLine(self, text: str = "") -> None: ...
    def textLines(self, stuff: list[str] | tuple[str, ...] | str, trim: int = 1) -> None: ...
    def __nonzero__(self) -> bool: ...
