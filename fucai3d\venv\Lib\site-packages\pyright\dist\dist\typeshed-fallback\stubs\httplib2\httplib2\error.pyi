from _typeshed import Incomplete

from httplib2 import Response

class HttpLib2Error(Exception): ...

class HttpLib2ErrorWithResponse(HttpLib2Error):
    response: Response | dict[str, Incomplete] | None
    content: str | bytes | None
    def __init__(
        self, desc: str | None, response: Response | dict[str, Incomplete] | None, content: str | bytes | None
    ) -> None: ...

class RedirectMissingLocation(HttpLib2ErrorWithResponse): ...
class RedirectLimit(HttpLib2ErrorWithResponse): ...
class FailedToDecompressContent(HttpLib2ErrorWithResponse): ...
class UnimplementedDigestAuthOptionError(HttpLib2ErrorWithResponse): ...
class UnimplementedHmacDigestAuthOptionError(HttpLib2ErrorWithResponse): ...
class MalformedHeader(HttpLib2Error): ...
class RelativeURIError(HttpLib2Error): ...
class ServerNotFoundError(HttpLib2Error): ...
class ProxiesUnavailableError(HttpLib2Error): ...
