from .compilers.C.errors import (
    CompileError as CompileError,
    Error as _Error,
    LibError as LibError,
    LinkError as <PERSON><PERSON><PERSON>r,
    PreprocessError as PreprocessError,
    UnknownFileType as _UnknownFileType,
)

CCompilerError = _Error
UnknownFileError = _UnknownFileType

class DistutilsError(Exception): ...
class DistutilsModuleError(DistutilsError): ...
class DistutilsClassError(DistutilsError): ...
class DistutilsGetoptError(DistutilsError): ...
class DistutilsArgError(DistutilsError): ...
class DistutilsFileError(DistutilsError): ...
class DistutilsOptionError(DistutilsError): ...
class DistutilsSetupError(DistutilsError): ...
class DistutilsPlatformError(DistutilsError): ...
class DistutilsExecError(DistutilsError): ...
class DistutilsInternalError(DistutilsError): ...
class DistutilsTemplateError(DistutilsError): ...
class DistutilsByteCompileError(DistutilsError): ...
