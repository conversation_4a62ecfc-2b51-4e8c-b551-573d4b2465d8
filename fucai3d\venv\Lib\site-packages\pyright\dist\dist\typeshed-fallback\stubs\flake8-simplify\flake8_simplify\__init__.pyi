import ast
import logging
from collections.abc import Generator
from typing import Any, ClassVar

logger: logging.Logger

class Visitor(ast.NodeVisitor):
    def __init__(self) -> None: ...
    def visit_Assign(self, node: ast.Assign) -> None: ...
    def visit_Call(self, node: ast.Call) -> None: ...
    def visit_With(self, node: ast.With) -> None: ...
    def visit_Expr(self, node: ast.Expr) -> None: ...
    def visit_BoolOp(self, node: ast.BoolOp) -> None: ...
    def visit_If(self, node: ast.If) -> None: ...
    def visit_For(self, node: ast.For) -> None: ...
    def visit_Subscript(self, node: ast.Subscript) -> None: ...
    def visit_Try(self, node: ast.Try) -> None: ...
    def visit_UnaryOp(self, node_v: ast.UnaryOp) -> None: ...
    def visit_IfExp(self, node: ast.IfExp) -> None: ...
    def visit_Compare(self, node: ast.Compare) -> None: ...
    def visit_ClassDef(self, node: ast.ClassDef) -> None: ...

class Plugin:
    name: ClassVar[str]
    version: ClassVar[str]
    def __init__(self, tree: ast.AST) -> None: ...
    def run(self) -> Generator[tuple[int, int, str, type[Any]], None, None]: ...

def add_meta(root: ast.AST, level: int = 0) -> None: ...
