import logging
from collections.abc import Sequence
from re import <PERSON>tern
from typing import Final, NamedTuple

COMMA_SEPARATED_LIST_RE: Final[Pattern[str]]
LOCAL_PLUGIN_LIST_RE: Final[Pattern[str]]
NORMALIZE_PACKAGE_NAME_RE: Final[Pattern[str]]

def parse_comma_separated_list(value: str, regexp: Pattern[str] = ...) -> list[str]: ...

class _Token(NamedTuple):
    tp: str
    src: str

def parse_files_to_codes_mapping(value_: Sequence[str] | str) -> list[tuple[str, list[str]]]: ...
def normalize_paths(paths: Sequence[str], parent: str = ".") -> list[str]: ...
def normalize_path(path: str, parent: str = ".") -> str: ...
def stdin_get_value() -> str: ...
def stdin_get_lines() -> list[str]: ...
def is_using_stdin(paths: list[str]) -> bool: ...
def fnmatch(filename: str, patterns: Sequence[str]) -> bool: ...
def matches_filename(path: str, patterns: Sequence[str], log_message: str, logger: logging.Logger) -> bool: ...
def get_python_version() -> str: ...
def normalize_pypi_name(s: str) -> str: ...
