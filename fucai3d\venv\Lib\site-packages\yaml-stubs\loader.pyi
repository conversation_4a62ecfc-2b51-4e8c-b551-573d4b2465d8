from typing_extensions import TypeA<PERSON><PERSON>

from yaml.composer import Composer
from yaml.constructor import BaseConstructor, <PERSON><PERSON><PERSON><PERSON>, FullConstructor, SafeConstructor
from yaml.parser import Parser
from yaml.reader import Reader
from yaml.resolver import BaseResolver, Resolver
from yaml.scanner import Scanner

from .reader import _ReadStream

_Loader: TypeAlias = Loader | BaseLoader | FullLoader | SafeLoader | UnsafeLoader  # noqa: Y047  # Used in other modules

class BaseLoader(<PERSON>, Scanne<PERSON>, Parser, Composer, BaseConstructor, BaseResolver):
    def __init__(self, stream: _ReadStream) -> None: ...

class FullLoader(<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, FullConstructor, Resolver):
    def __init__(self, stream: _ReadStream) -> None: ...

class SafeLoader(<PERSON>, Scanner, Parser, Composer, SafeConstructor, Resolver):
    def __init__(self, stream: _ReadStream) -> None: ...

class Loader(<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Composer, Constructor, Resolver):
    def __init__(self, stream: _ReadStream) -> None: ...

class UnsafeLoader(<PERSON>, <PERSON><PERSON><PERSON>, Pa<PERSON><PERSON>, Composer, Constructor, Resolver):
    def __init__(self, stream: _ReadStream) -> None: ...

__all__ = ["BaseLoader", "FullLoader", "SafeLoader", "Loader", "UnsafeLoader"]
