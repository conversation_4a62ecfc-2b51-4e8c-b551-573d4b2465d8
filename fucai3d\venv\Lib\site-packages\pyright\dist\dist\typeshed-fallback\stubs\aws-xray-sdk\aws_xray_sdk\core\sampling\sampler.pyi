from logging import Logger

log: Logger

class DefaultSampler:
    def __init__(self) -> None: ...
    def start(self) -> None: ...
    def should_trace(self, sampling_req=None): ...
    def load_local_rules(self, rules) -> None: ...
    def load_settings(self, daemon_config, context, origin=None) -> None: ...
    @property
    def xray_client(self): ...
    @xray_client.setter
    def xray_client(self, v) -> None: ...
