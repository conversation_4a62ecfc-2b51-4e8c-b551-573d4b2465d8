from typing import Final

from google.protobuf.descriptor_pb2 import FileDescriptorProto

__author__: Final[str]

class Error(Exception): ...
class DescriptorDatabaseConflictingDefinitionError(Error): ...

class DescriptorDatabase:
    def __init__(self) -> None: ...
    def Add(self, file_desc_proto) -> None: ...
    def FindFileByName(self, name): ...
    def FindFileContainingSymbol(self, symbol): ...
    def FindFileContainingExtension(self, extendee_name, extension_number) -> FileDescriptorProto | None: ...
    def FindAllExtensionNumbers(self, extendee_name) -> list[int]: ...
