import pathlib
import re
from collections.abc import Generator
from datetime import datetime, timedelta, tzinfo
from typing import Final

class StaticTzInfo(tzinfo):
    def __init__(self, name: str, offset: timedelta) -> None: ...
    def tzname(self, dt) -> str: ...
    def utcoffset(self, dt) -> timedelta: ...
    def dst(self, dt) -> timedelta: ...
    def localize(self, dt: datetime, is_dst: bool = False) -> datetime: ...
    def __getinitargs__(self) -> tuple[str, timedelta]: ...

def pop_tz_offset_from_string(date_string: str, as_offset: bool = True) -> tuple[str, StaticTzInfo | str | None]: ...
def word_is_tz(word: str) -> bool: ...
def convert_to_local_tz(datetime_obj: datetime, datetime_tz_offset: timedelta) -> datetime: ...
def build_tz_offsets(search_regex_parts: list[str]) -> Generator[tuple[str, dict[str, re.Pattern[str] | timedelta]]]: ...
def get_local_tz_offset() -> timedelta: ...

local_tz_offset: timedelta

CACHE_PATH: Final[pathlib.Path]
current_hash: int | None
