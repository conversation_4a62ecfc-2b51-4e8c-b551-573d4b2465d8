from _typeshed import Unused
from collections.abc import Hashable

__all__ = ("hashkey", "methodkey", "typedkey", "typedmethodkey")

def hashkey(*args: <PERSON>hable, **kwargs: <PERSON>hable) -> tuple[<PERSON><PERSON><PERSON>, ...]: ...
def methodkey(self: Unused, *args: <PERSON><PERSON><PERSON>, **kwargs: <PERSON><PERSON><PERSON>) -> tuple[<PERSON><PERSON><PERSON>, ...]: ...
def typedkey(*args: <PERSON>hab<PERSON>, **kwargs: <PERSON><PERSON><PERSON>) -> tuple[<PERSON>hable, ...]: ...
def typedmethodkey(self: Unused, *args: <PERSON><PERSON><PERSON>, **kwargs: <PERSON><PERSON><PERSON>) -> tuple[<PERSON><PERSON><PERSON>, ...]: ...
