from collections.abc import Sequence

from networkx.classes.graph import Graph, _Node
from networkx.utils.backends import _dispatchable
from numpy.random import RandomState

__all__ = ["is_threshold_graph", "find_threshold_graph"]

@_dispatchable
def is_threshold_graph(G: Graph[_Node]) -> bool: ...
def is_threshold_sequence(degree_sequence: Sequence[list[int]]) -> bool: ...
def creation_sequence(degree_sequence, with_labels=False, compact=False): ...
def make_compact(creation_sequence): ...
def uncompact(creation_sequence): ...
def creation_sequence_to_weights(creation_sequence): ...
def weights_to_creation_sequence(weights, threshold=1, with_labels=False, compact=False): ...
@_dispatchable
def threshold_graph(creation_sequence, create_using=None): ...
@_dispatchable
def find_alternating_4_cycle(G: Graph[_Node]): ...
@_dispatchable
def find_threshold_graph(G: Graph[_Node], create_using: Graph[_Node] | None = None): ...
@_dispatchable
def find_creation_sequence(G: Graph[_Node]): ...
def triangles(creation_sequence): ...
def triangle_sequence(creation_sequence): ...
def cluster_sequence(creation_sequence): ...
def degree_sequence(creation_sequence): ...
def density(creation_sequence): ...
def degree_correlation(creation_sequence): ...
def shortest_path(creation_sequence, u, v): ...
def shortest_path_length(creation_sequence, i): ...
def betweenness_sequence(creation_sequence, normalized=True): ...
def eigenvectors(creation_sequence): ...
def spectral_projection(u, eigenpairs): ...
def eigenvalues(creation_sequence): ...
def random_threshold_sequence(n, p, seed: int | RandomState | None = None): ...
def right_d_threshold_sequence(n: int, m: int) -> list[str]: ...
def left_d_threshold_sequence(n: int, m: int) -> list[str]: ...
def swap_d(cs, p_split=1.0, p_combine=1.0, seed: int | RandomState | None = None): ...
