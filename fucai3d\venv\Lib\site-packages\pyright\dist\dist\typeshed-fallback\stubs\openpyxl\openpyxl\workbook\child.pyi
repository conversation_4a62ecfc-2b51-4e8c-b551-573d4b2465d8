from collections.abc import Iterable
from re import Pattern
from typing import Final

from openpyxl import _Decodable
from openpyxl.workbook.workbook import Workbook
from openpyxl.worksheet.header_footer import Header<PERSON>ooter, HeaderFooterItem

INVALID_TITLE_REGEX: Final[Pattern[str]]

def avoid_duplicate_name(names: Iterable[str], value: str) -> str: ...

class _WorkbookChild:
    HeaderFooter: <PERSON>er<PERSON>ooter
    def __init__(self, parent: Workbook | None = None, title: str | _Decodable | None = None) -> None: ...
    @property
    def parent(self) -> Workbook | None: ...
    @property
    def encoding(self) -> str: ...  # Will error without a parent.
    @property
    def title(self) -> str: ...
    @title.setter
    def title(self, value: str | _Decodable) -> None: ...
    @property
    def oddHeader(self) -> HeaderFooterItem | None: ...
    @oddHeader.setter
    def oddHeader(self, value: HeaderFooterItem | None) -> None: ...
    @property
    def oddFooter(self) -> HeaderFooterItem | None: ...
    @oddFooter.setter
    def oddFooter(self, value: HeaderFooterItem | None) -> None: ...
    @property
    def evenHeader(self) -> HeaderFooterItem | None: ...
    @evenHeader.setter
    def evenHeader(self, value: HeaderFooterItem | None) -> None: ...
    @property
    def evenFooter(self) -> HeaderFooterItem | None: ...
    @evenFooter.setter
    def evenFooter(self, value: HeaderFooterItem | None) -> None: ...
    @property
    def firstHeader(self) -> HeaderFooterItem | None: ...
    @firstHeader.setter
    def firstHeader(self, value: HeaderFooterItem | None) -> None: ...
    @property
    def firstFooter(self) -> HeaderFooterItem | None: ...
    @firstFooter.setter
    def firstFooter(self, value: HeaderFooterItem | None) -> None: ...
    @property
    def path(self) -> str: ...
