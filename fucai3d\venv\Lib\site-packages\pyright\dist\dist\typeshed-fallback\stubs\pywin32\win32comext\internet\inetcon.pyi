from _typeshed import Incomplete

INET_E_USE_DEFAULT_PROTOCOLHANDLER: int
INET_E_USE_DEFAULT_SETTING: int
INET_E_DEFAULT_ACTION: int
INET_E_QUERYOPTION_UNKNOWN: int
INET_E_REDIRECTING: int
INET_E_INVALID_URL: int
INET_E_NO_SESSION: int
INET_E_CANNOT_CONNECT: int
INET_E_RESOURCE_NOT_FOUND: int
INET_E_OBJECT_NOT_FOUND: int
INET_E_DATA_NOT_AVAILABLE: int
INET_E_DOWNLOAD_FAILURE: int
INET_E_AUTHENTICATION_REQUIRED: int
INET_E_NO_VALID_MEDIA: int
INET_E_CONNECTION_TIMEOUT: int
INET_E_INVALID_REQUEST: int
INET_E_UNKNOWN_PROTOCOL: int
INET_E_SECURITY_PROBLEM: int
INET_E_CANNOT_LOAD_DATA: int
INET_E_CANNOT_INSTANTIATE_OBJECT: int
INET_E_INVALID_CERTIFICATE: int
INET_E_REDIRECT_FAILED: int
INET_E_REDIRECT_TO_DIR: int
INET_E_CANNOT_LOCK_REQUEST: int
INET_E_USE_EXTEND_BINDING: int
INET_E_TERMINATED_BIND: int
INET_E_CODE_DOWNLOAD_DECLINED: int
INET_E_RESULT_DISPATCHED: int
INET_E_CANNOT_REPLACE_SFP_FILE: int
INET_E_CODE_INSTALL_SUPPRESSED: int
INET_E_CODE_INSTALL_BLOCKED_BY_HASH_POLICY: int
MKSYS_URLMONIKER: int
URL_MK_LEGACY: int
URL_MK_UNIFORM: int
URL_MK_NO_CANONICALIZE: int
FIEF_FLAG_FORCE_JITUI: int
FIEF_FLAG_PEEK: int
FIEF_FLAG_SKIP_INSTALLED_VERSION_CHECK: int
FMFD_DEFAULT: int
FMFD_URLASFILENAME: int
FMFD_ENABLEMIMESNIFFING: int
FMFD_IGNOREMIMETEXTPLAIN: int
URLMON_OPTION_USERAGENT: int
URLMON_OPTION_USERAGENT_REFRESH: int
URLMON_OPTION_URL_ENCODING: int
URLMON_OPTION_USE_BINDSTRINGCREDS: int
URLMON_OPTION_USE_BROWSERAPPSDOCUMENTS: int
CF_NULL: int
Uri_CREATE_ALLOW_RELATIVE: int
Uri_CREATE_ALLOW_IMPLICIT_WILDCARD_SCHEME: int
Uri_CREATE_ALLOW_IMPLICIT_FILE_SCHEME: int
Uri_CREATE_NOFRAG: int
Uri_CREATE_NO_CANONICALIZE: int
Uri_CREATE_CANONICALIZE: int
Uri_CREATE_FILE_USE_DOS_PATH: int
Uri_CREATE_DECODE_EXTRA_INFO: int
Uri_CREATE_NO_DECODE_EXTRA_INFO: int
Uri_CREATE_CRACK_UNKNOWN_SCHEMES: int
Uri_CREATE_NO_CRACK_UNKNOWN_SCHEMES: int
Uri_CREATE_PRE_PROCESS_HTML_URI: int
Uri_CREATE_NO_PRE_PROCESS_HTML_URI: int
Uri_CREATE_IE_SETTINGS: int
Uri_CREATE_NO_IE_SETTINGS: int
Uri_CREATE_NO_ENCODE_FORBIDDEN_CHARACTERS: int
Uri_DISPLAY_NO_FRAGMENT: int
Uri_PUNYCODE_IDN_HOST: int
Uri_DISPLAY_IDN_HOST: int
Uri_ENCODING_USER_INFO_AND_PATH_IS_PERCENT_ENCODED_UTF8: int
Uri_ENCODING_USER_INFO_AND_PATH_IS_CP: int
Uri_ENCODING_HOST_IS_IDN: int
Uri_ENCODING_HOST_IS_PERCENT_ENCODED_UTF8: int
Uri_ENCODING_HOST_IS_PERCENT_ENCODED_CP: int
Uri_ENCODING_QUERY_AND_FRAGMENT_IS_PERCENT_ENCODED_UTF8: int
Uri_ENCODING_QUERY_AND_FRAGMENT_IS_CP: int
Uri_ENCODING_RFC: Incomplete
UriBuilder_USE_ORIGINAL_FLAGS: int
WININETINFO_OPTION_LOCK_HANDLE: int
URLOSTRM_USECACHEDCOPY_ONLY: int
URLOSTRM_USECACHEDCOPY: int
URLOSTRM_GETNEWESTVERSION: int
SET_FEATURE_ON_THREAD: int
SET_FEATURE_ON_PROCESS: int
SET_FEATURE_IN_REGISTRY: int
SET_FEATURE_ON_THREAD_LOCALMACHINE: int
SET_FEATURE_ON_THREAD_INTRANET: int
SET_FEATURE_ON_THREAD_TRUSTED: int
SET_FEATURE_ON_THREAD_INTERNET: int
SET_FEATURE_ON_THREAD_RESTRICTED: int
GET_FEATURE_FROM_THREAD: int
GET_FEATURE_FROM_PROCESS: int
GET_FEATURE_FROM_REGISTRY: int
GET_FEATURE_FROM_THREAD_LOCALMACHINE: int
GET_FEATURE_FROM_THREAD_INTRANET: int
GET_FEATURE_FROM_THREAD_TRUSTED: int
GET_FEATURE_FROM_THREAD_INTERNET: int
GET_FEATURE_FROM_THREAD_RESTRICTED: int
PROTOCOLFLAG_NO_PICS_CHECK: int
MUTZ_NOSAVEDFILECHECK: int
MUTZ_ISFILE: int
MUTZ_ACCEPT_WILDCARD_SCHEME: int
MUTZ_ENFORCERESTRICTED: int
MUTZ_RESERVED: int
MUTZ_REQUIRESAVEDFILECHECK: int
MUTZ_DONT_UNESCAPE: int
MUTZ_DONT_USE_CACHE: int
MUTZ_FORCE_INTRANET_FLAGS: int
MUTZ_IGNORE_ZONE_MAPPINGS: int
MAX_SIZE_SECURITY_ID: int
URLACTION_MIN: int
URLACTION_DOWNLOAD_MIN: int
URLACTION_DOWNLOAD_SIGNED_ACTIVEX: int
URLACTION_DOWNLOAD_UNSIGNED_ACTIVEX: int
URLACTION_DOWNLOAD_CURR_MAX: int
URLACTION_DOWNLOAD_MAX: int
URLACTION_ACTIVEX_MIN: int
URLACTION_ACTIVEX_RUN: int
URLPOLICY_ACTIVEX_CHECK_LIST: int
URLACTION_ACTIVEX_OVERRIDE_OBJECT_SAFETY: int
URLACTION_ACTIVEX_OVERRIDE_DATA_SAFETY: int
URLACTION_ACTIVEX_OVERRIDE_SCRIPT_SAFETY: int
URLACTION_SCRIPT_OVERRIDE_SAFETY: int
URLACTION_ACTIVEX_CONFIRM_NOOBJECTSAFETY: int
URLACTION_ACTIVEX_TREATASUNTRUSTED: int
URLACTION_ACTIVEX_NO_WEBOC_SCRIPT: int
URLACTION_ACTIVEX_OVERRIDE_REPURPOSEDETECTION: int
URLACTION_ACTIVEX_OVERRIDE_OPTIN: int
URLACTION_ACTIVEX_SCRIPTLET_RUN: int
URLACTION_ACTIVEX_DYNSRC_VIDEO_AND_ANIMATION: int
URLACTION_ACTIVEX_CURR_MAX: int
URLACTION_ACTIVEX_MAX: int
URLACTION_SCRIPT_MIN: int
URLACTION_SCRIPT_RUN: int
URLACTION_SCRIPT_JAVA_USE: int
URLACTION_SCRIPT_SAFE_ACTIVEX: int
URLACTION_CROSS_DOMAIN_DATA: int
URLACTION_SCRIPT_PASTE: int
URLACTION_ALLOW_XDOMAIN_SUBFRAME_RESIZE: int
URLACTION_SCRIPT_CURR_MAX: int
URLACTION_SCRIPT_MAX: int
URLACTION_HTML_MIN: int
URLACTION_HTML_SUBMIT_FORMS: int
URLACTION_HTML_SUBMIT_FORMS_FROM: int
URLACTION_HTML_SUBMIT_FORMS_TO: int
URLACTION_HTML_FONT_DOWNLOAD: int
URLACTION_HTML_JAVA_RUN: int
URLACTION_HTML_USERDATA_SAVE: int
URLACTION_HTML_SUBFRAME_NAVIGATE: int
URLACTION_HTML_META_REFRESH: int
URLACTION_HTML_MIXED_CONTENT: int
URLACTION_HTML_INCLUDE_FILE_PATH: int
URLACTION_HTML_MAX: int
URLACTION_SHELL_MIN: int
URLACTION_SHELL_INSTALL_DTITEMS: int
URLACTION_SHELL_MOVE_OR_COPY: int
URLACTION_SHELL_FILE_DOWNLOAD: int
URLACTION_SHELL_VERB: int
URLACTION_SHELL_WEBVIEW_VERB: int
URLACTION_SHELL_SHELLEXECUTE: int
URLACTION_SHELL_EXECUTE_HIGHRISK: int
URLACTION_SHELL_EXECUTE_MODRISK: int
URLACTION_SHELL_EXECUTE_LOWRISK: int
URLACTION_SHELL_POPUPMGR: int
URLACTION_SHELL_RTF_OBJECTS_LOAD: int
URLACTION_SHELL_ENHANCED_DRAGDROP_SECURITY: int
URLACTION_SHELL_EXTENSIONSECURITY: int
URLACTION_SHELL_SECURE_DRAGSOURCE: int
URLACTION_SHELL_CURR_MAX: int
URLACTION_SHELL_MAX: int
URLACTION_NETWORK_MIN: int
URLACTION_CREDENTIALS_USE: int
URLPOLICY_CREDENTIALS_SILENT_LOGON_OK: int
URLPOLICY_CREDENTIALS_MUST_PROMPT_USER: int
URLPOLICY_CREDENTIALS_CONDITIONAL_PROMPT: int
URLPOLICY_CREDENTIALS_ANONYMOUS_ONLY: int
URLACTION_AUTHENTICATE_CLIENT: int
URLPOLICY_AUTHENTICATE_CLEARTEXT_OK: int
URLPOLICY_AUTHENTICATE_CHALLENGE_RESPONSE: int
URLPOLICY_AUTHENTICATE_MUTUAL_ONLY: int
URLACTION_COOKIES: int
URLACTION_COOKIES_SESSION: int
URLACTION_CLIENT_CERT_PROMPT: int
URLACTION_COOKIES_THIRD_PARTY: int
URLACTION_COOKIES_SESSION_THIRD_PARTY: int
URLACTION_COOKIES_ENABLED: int
URLACTION_NETWORK_CURR_MAX: int
URLACTION_NETWORK_MAX: int
URLACTION_JAVA_MIN: int
URLACTION_JAVA_PERMISSIONS: int
URLPOLICY_JAVA_PROHIBIT: int
URLPOLICY_JAVA_HIGH: int
URLPOLICY_JAVA_MEDIUM: int
URLPOLICY_JAVA_LOW: int
URLPOLICY_JAVA_CUSTOM: int
URLACTION_JAVA_CURR_MAX: int
URLACTION_JAVA_MAX: int
URLACTION_INFODELIVERY_MIN: int
URLACTION_INFODELIVERY_NO_ADDING_CHANNELS: int
URLACTION_INFODELIVERY_NO_EDITING_CHANNELS: int
URLACTION_INFODELIVERY_NO_REMOVING_CHANNELS: int
URLACTION_INFODELIVERY_NO_ADDING_SUBSCRIPTIONS: int
URLACTION_INFODELIVERY_NO_EDITING_SUBSCRIPTIONS: int
URLACTION_INFODELIVERY_NO_REMOVING_SUBSCRIPTIONS: int
URLACTION_INFODELIVERY_NO_CHANNEL_LOGGING: int
URLACTION_INFODELIVERY_CURR_MAX: int
URLACTION_INFODELIVERY_MAX: int
URLACTION_CHANNEL_SOFTDIST_MIN: int
URLACTION_CHANNEL_SOFTDIST_PERMISSIONS: int
URLPOLICY_CHANNEL_SOFTDIST_PROHIBIT: int
URLPOLICY_CHANNEL_SOFTDIST_PRECACHE: int
URLPOLICY_CHANNEL_SOFTDIST_AUTOINSTALL: int
URLACTION_CHANNEL_SOFTDIST_MAX: int
URLACTION_BEHAVIOR_MIN: int
URLACTION_BEHAVIOR_RUN: int
URLPOLICY_BEHAVIOR_CHECK_LIST: int
URLACTION_FEATURE_MIN: int
URLACTION_FEATURE_MIME_SNIFFING: int
URLACTION_FEATURE_ZONE_ELEVATION: int
URLACTION_FEATURE_WINDOW_RESTRICTIONS: int
URLACTION_FEATURE_SCRIPT_STATUS_BAR: int
URLACTION_FEATURE_FORCE_ADDR_AND_STATUS: int
URLACTION_FEATURE_BLOCK_INPUT_PROMPTS: int
URLACTION_AUTOMATIC_DOWNLOAD_UI_MIN: int
URLACTION_AUTOMATIC_DOWNLOAD_UI: int
URLACTION_AUTOMATIC_ACTIVEX_UI: int
URLACTION_ALLOW_RESTRICTEDPROTOCOLS: int
URLACTION_ALLOW_APEVALUATION: int
URLACTION_WINDOWS_BROWSER_APPLICATIONS: int
URLACTION_XPS_DOCUMENTS: int
URLACTION_LOOSE_XAML: int
URLACTION_LOWRIGHTS: int
URLACTION_WINFX_SETUP: int
URLPOLICY_ALLOW: int
URLPOLICY_QUERY: int
URLPOLICY_DISALLOW: int
URLPOLICY_NOTIFY_ON_ALLOW: int
URLPOLICY_NOTIFY_ON_DISALLOW: int
URLPOLICY_LOG_ON_ALLOW: int
URLPOLICY_LOG_ON_DISALLOW: int
URLPOLICY_MASK_PERMISSIONS: int
URLPOLICY_DONTCHECKDLGBOX: int
URLZONE_ESC_FLAG: int
SECURITY_IE_STATE_GREEN: int
SECURITY_IE_STATE_RED: int
SOFTDIST_FLAG_USAGE_EMAIL: int
SOFTDIST_FLAG_USAGE_PRECACHE: int
SOFTDIST_FLAG_USAGE_AUTOINSTALL: int
SOFTDIST_FLAG_DELETE_SUBSCRIPTION: int
SOFTDIST_ADSTATE_NONE: int
SOFTDIST_ADSTATE_AVAILABLE: int
SOFTDIST_ADSTATE_DOWNLOADED: int
SOFTDIST_ADSTATE_INSTALLED: int
CONFIRMSAFETYACTION_LOADOBJECT: int
