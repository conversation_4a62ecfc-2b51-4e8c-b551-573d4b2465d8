{"version": 3, "file": "pyright.js", "mappings": "UAAIA,ECAAC,ECIAC,E,YCJJ,SAASC,EAAoBC,GAC5B,IAAIC,EAAI,IAAIC,MAAM,uBAAyBF,EAAM,KAEjD,MADAC,EAAEE,KAAO,mBACHF,CACP,CACAF,EAAoBK,KAAO,IAAM,GACjCL,EAAoBM,QAAUN,EAC9BA,EAAoBO,GAAK,KACzBC,EAAOC,QAAUT,C,+BCNjB,EAFA,QAEAU,O,wBCFAF,EAAOC,QAAUE,QAAQ,W,wBCAzBH,EAAOC,QAAUE,QAAQ,gB,wBCAzBH,EAAOC,QAAUE,QAAQ,S,wBCAzBH,EAAOC,QAAUE,QAAQ,S,wBCAzBH,EAAOC,QAAUE,QAAQ,K,uBCAzBH,EAAOC,QAAUE,QAAQ,K,wBCAzBH,EAAOC,QAAUE,QAAQ,O,uBCAzBH,EAAOC,QAAUE,QAAQ,U,wBCAzBH,EAAOC,QAAUE,QAAQ,W,wBCAzBH,EAAOC,QAAUE,QAAQ,S,wBCAzBH,EAAOC,QAAUE,QAAQ,M,wBCAzBH,EAAOC,QAAUE,QAAQ,M,wBCAzBH,EAAOC,QAAUE,QAAQ,O,wBCAzBH,EAAOC,QAAUE,QAAQ,K,wBCAzBH,EAAOC,QAAUE,QAAQ,iB,wBCAzBH,EAAOC,QAAUE,QAAQ,O,GCCrBC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaN,QAGrB,IAAID,EAASI,EAAyBE,GAAY,CACjDP,GAAIO,EACJG,QAAQ,EACRR,QAAS,CAAC,GAUX,OANAS,EAAoBJ,GAAUK,KAAKX,EAAOC,QAASD,EAAQA,EAAOC,QAASI,GAG3EL,EAAOS,QAAS,EAGTT,EAAOC,OACf,CAGAI,EAAoBO,EAAIF,EAGxBL,EAAoBQ,EAAI,KAGvB,IAAIC,EAAsBT,EAAoBU,OAAEP,EAAW,CAAC,IAAI,MAAM,IAAOH,EAAoB,QAEjG,OADsBA,EAAoBU,EAAED,EAClB,ErBpCvBzB,EAAW,GACfgB,EAAoBU,EAAI,CAACC,EAAQC,EAAUC,EAAIC,KAC9C,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIjC,EAASkC,OAAQD,IAAK,CAGzC,IAFA,IAAKL,EAAUC,EAAIC,GAAY9B,EAASiC,GACpCE,GAAY,EACPC,EAAI,EAAGA,EAAIR,EAASM,OAAQE,MACpB,EAAXN,GAAsBC,GAAgBD,IAAaO,OAAO7B,KAAKQ,EAAoBU,GAAGY,OAAOC,GAASvB,EAAoBU,EAAEa,GAAKX,EAASQ,MAC9IR,EAASY,OAAOJ,IAAK,IAErBD,GAAY,EACTL,EAAWC,IAAcA,EAAeD,IAG7C,GAAGK,EAAW,CACbnC,EAASwC,OAAOP,IAAK,GACrB,IAAIQ,EAAIZ,SACEV,IAANsB,IAAiBd,EAASc,EAC/B,CACD,CACA,OAAOd,CAnBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIjC,EAASkC,OAAQD,EAAI,GAAKjC,EAASiC,EAAI,GAAG,GAAKH,EAAUG,IAAKjC,EAASiC,GAAKjC,EAASiC,EAAI,GACrGjC,EAASiC,GAAK,CAACL,EAAUC,EAAIC,EAqBjB,EsBzBdd,EAAoB0B,EAAI,CAAC9B,EAAS+B,KACjC,IAAI,IAAIJ,KAAOI,EACX3B,EAAoB4B,EAAED,EAAYJ,KAASvB,EAAoB4B,EAAEhC,EAAS2B,IAC5EF,OAAOQ,eAAejC,EAAS2B,EAAK,CAAEO,YAAY,EAAMC,IAAKJ,EAAWJ,IAE1E,ECNDvB,EAAoBgC,EAAI,CAAC,EAGzBhC,EAAoBX,EAAK4C,GACjBC,QAAQC,IAAId,OAAO7B,KAAKQ,EAAoBgC,GAAGI,QAAO,CAACC,EAAUd,KACvEvB,EAAoBgC,EAAET,GAAKU,EAASI,GAC7BA,IACL,KCNJrC,EAAoBsC,EAAKL,IAEZ,CAAC,IAAM,SAAS,IAAM,oBAAoBA,GAAW,OCHlEjC,EAAoB4B,EAAI,CAACW,EAAKC,IAAUnB,OAAOoB,UAAUC,eAAepC,KAAKiC,EAAKC,GCClFxC,EAAoByB,EAAK7B,IACH,oBAAX+C,QAA0BA,OAAOC,aAC1CvB,OAAOQ,eAAejC,EAAS+C,OAAOC,YAAa,CAAEC,MAAO,WAE7DxB,OAAOQ,eAAejC,EAAS,aAAc,CAAEiD,OAAO,GAAO,ECL9D7C,EAAoB8C,IAAOnD,IAC1BA,EAAOoD,MAAQ,GACVpD,EAAOqD,WAAUrD,EAAOqD,SAAW,IACjCrD,GzBCJT,EAAkB,CACrB,IAAK,GAGNc,EAAoBU,EAAEZ,QAAWmC,GAAa/C,EAAgB+C,GAgB9DjC,EAAoBgC,EAAElC,QAAU,CAACmC,EAASI,KAErCnD,EAAgB+C,IAhBF,CAACgB,IACnB,IAAIC,EAAcD,EAAME,QAASvC,EAAWqC,EAAMG,IAAKC,EAAUJ,EAAMI,QACvE,IAAI,IAAIpD,KAAYiD,EAChBlD,EAAoB4B,EAAEsB,EAAajD,KACrCD,EAAoBO,EAAEN,GAAYiD,EAAYjD,IAG7CoD,GAASA,EAAQrD,GACpB,IAAI,IAAIiB,EAAI,EAAGA,EAAIL,EAASM,OAAQD,IACnC/B,EAAgB0B,EAASK,IAAM,EAChCjB,EAAoBU,GAAG,EAQrB4C,CAAaxD,QAAQ,KAAOE,EAAoBsC,EAAEL,IAEpD,ED9BGhD,EAAOe,EAAoBQ,EAC/BR,EAAoBQ,EAAI,KACvBR,EAAoBX,EAAE,KACtBW,EAAoBX,EAAE,KACfJ,K2BHkBe,EAAoBQ,G", "sources": ["webpack/runtime/chunk loaded", "webpack/runtime/startup chunk dependencies", "webpack/runtime/require chunk loading", "pyright-internal/node_modules/vscode-languageserver-textdocument/lib/umd/ sync", "pyright/src/pyright.ts", "external commonjs2 \"fsevents\"", "external node-commonjs \"child_process\"", "external node-commonjs \"crypto\"", "external node-commonjs \"events\"", "external node-commonjs \"fs\"", "external node-commonjs \"os\"", "external node-commonjs \"path\"", "external node-commonjs \"process\"", "external node-commonjs \"readline\"", "external node-commonjs \"stream\"", "external node-commonjs \"tty\"", "external node-commonjs \"url\"", "external node-commonjs \"util\"", "external node-commonjs \"v8\"", "external node-commonjs \"worker_threads\"", "external node-commonjs \"zlib\"", "webpack/bootstrap", "webpack/runtime/define property getters", "webpack/runtime/ensure chunk", "webpack/runtime/get javascript chunk filename", "webpack/runtime/hasOwnProperty shorthand", "webpack/runtime/make namespace object", "webpack/runtime/node module decorator", "webpack/startup"], "names": ["deferred", "next", "installedChunks", "webpackEmptyContext", "req", "e", "Error", "code", "keys", "resolve", "id", "module", "exports", "main", "require", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "loaded", "__webpack_modules__", "call", "m", "x", "__webpack_exports__", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "length", "fulfilled", "j", "Object", "every", "key", "splice", "r", "d", "definition", "o", "defineProperty", "enumerable", "get", "f", "chunkId", "Promise", "all", "reduce", "promises", "u", "obj", "prop", "prototype", "hasOwnProperty", "Symbol", "toStringTag", "value", "nmd", "paths", "children", "chunk", "moreModules", "modules", "ids", "runtime", "installChunk"], "sourceRoot": ""}