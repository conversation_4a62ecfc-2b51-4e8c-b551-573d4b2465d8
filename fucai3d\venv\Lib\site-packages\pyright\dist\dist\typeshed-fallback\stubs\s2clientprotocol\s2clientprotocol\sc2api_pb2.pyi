"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import s2clientprotocol.common_pb2
import s2clientprotocol.data_pb2
import s2clientprotocol.debug_pb2
import s2clientprotocol.error_pb2
import s2clientprotocol.query_pb2
import s2clientprotocol.raw_pb2
import s2clientprotocol.score_pb2
import s2clientprotocol.spatial_pb2
import s2clientprotocol.ui_pb2

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _Status:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _StatusEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_Status.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    launched: _Status.ValueType  # 1
    """Game has been launch and is not yet doing anything."""
    init_game: _Status.ValueType  # 2
    """Create game has been called, and the host is awaiting players."""
    in_game: _Status.ValueType  # 3
    """In a single or multiplayer game."""
    in_replay: _Status.ValueType  # 4
    """In a replay."""
    ended: _Status.ValueType  # 5
    """Game has ended, can still request game info, but ready for a new game."""
    quit: _Status.ValueType  # 6
    """Application is shutting down."""
    unknown: _Status.ValueType  # 99
    """Should not happen, but indicates an error if it occurs."""

class Status(_Status, metaclass=_StatusEnumTypeWrapper): ...

launched: Status.ValueType  # 1
"""Game has been launch and is not yet doing anything."""
init_game: Status.ValueType  # 2
"""Create game has been called, and the host is awaiting players."""
in_game: Status.ValueType  # 3
"""In a single or multiplayer game."""
in_replay: Status.ValueType  # 4
"""In a replay."""
ended: Status.ValueType  # 5
"""Game has ended, can still request game info, but ready for a new game."""
quit: Status.ValueType  # 6
"""Application is shutting down."""
unknown: Status.ValueType  # 99
"""Should not happen, but indicates an error if it occurs."""
global___Status = Status

class _Difficulty:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DifficultyEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_Difficulty.ValueType], builtins.type
):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    VeryEasy: _Difficulty.ValueType  # 1
    Easy: _Difficulty.ValueType  # 2
    Medium: _Difficulty.ValueType  # 3
    MediumHard: _Difficulty.ValueType  # 4
    Hard: _Difficulty.ValueType  # 5
    Harder: _Difficulty.ValueType  # 6
    VeryHard: _Difficulty.ValueType  # 7
    CheatVision: _Difficulty.ValueType  # 8
    CheatMoney: _Difficulty.ValueType  # 9
    CheatInsane: _Difficulty.ValueType  # 10

class Difficulty(_Difficulty, metaclass=_DifficultyEnumTypeWrapper):
    """
    Game Setup
    """

VeryEasy: Difficulty.ValueType  # 1
Easy: Difficulty.ValueType  # 2
Medium: Difficulty.ValueType  # 3
MediumHard: Difficulty.ValueType  # 4
Hard: Difficulty.ValueType  # 5
Harder: Difficulty.ValueType  # 6
VeryHard: Difficulty.ValueType  # 7
CheatVision: Difficulty.ValueType  # 8
CheatMoney: Difficulty.ValueType  # 9
CheatInsane: Difficulty.ValueType  # 10
global___Difficulty = Difficulty

class _PlayerType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _PlayerTypeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_PlayerType.ValueType], builtins.type
):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    Participant: _PlayerType.ValueType  # 1
    Computer: _PlayerType.ValueType  # 2
    Observer: _PlayerType.ValueType  # 3

class PlayerType(_PlayerType, metaclass=_PlayerTypeEnumTypeWrapper): ...

Participant: PlayerType.ValueType  # 1
Computer: PlayerType.ValueType  # 2
Observer: PlayerType.ValueType  # 3
global___PlayerType = PlayerType

class _AIBuild:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _AIBuildEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_AIBuild.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    RandomBuild: _AIBuild.ValueType  # 1
    Rush: _AIBuild.ValueType  # 2
    Timing: _AIBuild.ValueType  # 3
    Power: _AIBuild.ValueType  # 4
    Macro: _AIBuild.ValueType  # 5
    Air: _AIBuild.ValueType  # 6

class AIBuild(_AIBuild, metaclass=_AIBuildEnumTypeWrapper): ...

RandomBuild: AIBuild.ValueType  # 1
Rush: AIBuild.ValueType  # 2
Timing: AIBuild.ValueType  # 3
Power: AIBuild.ValueType  # 4
Macro: AIBuild.ValueType  # 5
Air: AIBuild.ValueType  # 6
global___AIBuild = AIBuild

class _Alert:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _AlertEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_Alert.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    AlertError: _Alert.ValueType  # 3
    AddOnComplete: _Alert.ValueType  # 4
    BuildingComplete: _Alert.ValueType  # 5
    BuildingUnderAttack: _Alert.ValueType  # 6
    LarvaHatched: _Alert.ValueType  # 7
    MergeComplete: _Alert.ValueType  # 8
    MineralsExhausted: _Alert.ValueType  # 9
    MorphComplete: _Alert.ValueType  # 10
    MothershipComplete: _Alert.ValueType  # 11
    MULEExpired: _Alert.ValueType  # 12
    NuclearLaunchDetected: _Alert.ValueType  # 1
    NukeComplete: _Alert.ValueType  # 13
    NydusWormDetected: _Alert.ValueType  # 2
    ResearchComplete: _Alert.ValueType  # 14
    TrainError: _Alert.ValueType  # 15
    TrainUnitComplete: _Alert.ValueType  # 16
    TrainWorkerComplete: _Alert.ValueType  # 17
    TransformationComplete: _Alert.ValueType  # 18
    UnitUnderAttack: _Alert.ValueType  # 19
    UpgradeComplete: _Alert.ValueType  # 20
    VespeneExhausted: _Alert.ValueType  # 21
    WarpInComplete: _Alert.ValueType  # 22

class Alert(_Alert, metaclass=_AlertEnumTypeWrapper): ...

AlertError: Alert.ValueType  # 3
AddOnComplete: Alert.ValueType  # 4
BuildingComplete: Alert.ValueType  # 5
BuildingUnderAttack: Alert.ValueType  # 6
LarvaHatched: Alert.ValueType  # 7
MergeComplete: Alert.ValueType  # 8
MineralsExhausted: Alert.ValueType  # 9
MorphComplete: Alert.ValueType  # 10
MothershipComplete: Alert.ValueType  # 11
MULEExpired: Alert.ValueType  # 12
NuclearLaunchDetected: Alert.ValueType  # 1
NukeComplete: Alert.ValueType  # 13
NydusWormDetected: Alert.ValueType  # 2
ResearchComplete: Alert.ValueType  # 14
TrainError: Alert.ValueType  # 15
TrainUnitComplete: Alert.ValueType  # 16
TrainWorkerComplete: Alert.ValueType  # 17
TransformationComplete: Alert.ValueType  # 18
UnitUnderAttack: Alert.ValueType  # 19
UpgradeComplete: Alert.ValueType  # 20
VespeneExhausted: Alert.ValueType  # 21
WarpInComplete: Alert.ValueType  # 22
global___Alert = Alert

class _Result:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ResultEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_Result.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    Victory: _Result.ValueType  # 1
    Defeat: _Result.ValueType  # 2
    Tie: _Result.ValueType  # 3
    Undecided: _Result.ValueType  # 4

class Result(_Result, metaclass=_ResultEnumTypeWrapper): ...

Victory: Result.ValueType  # 1
Defeat: Result.ValueType  # 2
Tie: Result.ValueType  # 3
Undecided: Result.ValueType  # 4
global___Result = Result

@typing.final
class Request(google.protobuf.message.Message):
    """
    Request/Response
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CREATE_GAME_FIELD_NUMBER: builtins.int
    JOIN_GAME_FIELD_NUMBER: builtins.int
    RESTART_GAME_FIELD_NUMBER: builtins.int
    START_REPLAY_FIELD_NUMBER: builtins.int
    LEAVE_GAME_FIELD_NUMBER: builtins.int
    QUICK_SAVE_FIELD_NUMBER: builtins.int
    QUICK_LOAD_FIELD_NUMBER: builtins.int
    QUIT_FIELD_NUMBER: builtins.int
    GAME_INFO_FIELD_NUMBER: builtins.int
    OBSERVATION_FIELD_NUMBER: builtins.int
    ACTION_FIELD_NUMBER: builtins.int
    OBS_ACTION_FIELD_NUMBER: builtins.int
    STEP_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    QUERY_FIELD_NUMBER: builtins.int
    SAVE_REPLAY_FIELD_NUMBER: builtins.int
    MAP_COMMAND_FIELD_NUMBER: builtins.int
    REPLAY_INFO_FIELD_NUMBER: builtins.int
    AVAILABLE_MAPS_FIELD_NUMBER: builtins.int
    SAVE_MAP_FIELD_NUMBER: builtins.int
    PING_FIELD_NUMBER: builtins.int
    DEBUG_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    id: builtins.int
    @property
    def create_game(self) -> global___RequestCreateGame:
        """Game Setup
        Send to host to initialize game.
        """

    @property
    def join_game(self) -> global___RequestJoinGame:
        """Send to host and all clients for game to begin."""

    @property
    def restart_game(self) -> global___RequestRestartGame:
        """Single player only. Reinitializes the game with the same player setup."""

    @property
    def start_replay(self) -> global___RequestStartReplay:
        """Start playing a replay."""

    @property
    def leave_game(self) -> global___RequestLeaveGame:
        """Multiplayer only. Disconnects from a multiplayer game, equivalent to surrender."""

    @property
    def quick_save(self) -> global___RequestQuickSave:
        """Saves game to an in-memory bookmark."""

    @property
    def quick_load(self) -> global___RequestQuickLoad:
        """Loads from an in-memory bookmark."""

    @property
    def quit(self) -> global___RequestQuit:
        """Terminates the application."""

    @property
    def game_info(self) -> global___RequestGameInfo:
        """During Game
        Static data about the current game and map.
        """

    @property
    def observation(self) -> global___RequestObservation:
        """Snapshot of the current game state."""

    @property
    def action(self) -> global___RequestAction:
        """Executes an action for a participant."""

    @property
    def obs_action(self) -> global___RequestObserverAction:
        """Executes an action for an observer."""

    @property
    def step(self) -> global___RequestStep:
        """Advances the game simulation."""

    @property
    def data(self) -> global___RequestData:
        """Data about different gameplay elements. May be different for different games."""

    @property
    def query(self) -> s2clientprotocol.query_pb2.RequestQuery:
        """Additional methods for inspecting game state."""

    @property
    def save_replay(self) -> global___RequestSaveReplay:
        """Generates a replay."""

    @property
    def map_command(self) -> global___RequestMapCommand:
        """Execute a particular trigger through a string interface"""

    @property
    def replay_info(self) -> global___RequestReplayInfo:
        """Other.
        Returns metadata about a replay file. Does not load the replay.
        """

    @property
    def available_maps(self) -> global___RequestAvailableMaps:
        """Returns directory of maps that can be played on."""

    @property
    def save_map(self) -> global___RequestSaveMap:
        """Saves binary map data to the local temp directory."""

    @property
    def ping(self) -> global___RequestPing:
        """Debugging
        Network ping for testing connection.
        """

    @property
    def debug(self) -> global___RequestDebug:
        """Display debug information and execute debug actions."""

    def __init__(
        self,
        *,
        create_game: global___RequestCreateGame | None = ...,
        join_game: global___RequestJoinGame | None = ...,
        restart_game: global___RequestRestartGame | None = ...,
        start_replay: global___RequestStartReplay | None = ...,
        leave_game: global___RequestLeaveGame | None = ...,
        quick_save: global___RequestQuickSave | None = ...,
        quick_load: global___RequestQuickLoad | None = ...,
        quit: global___RequestQuit | None = ...,
        game_info: global___RequestGameInfo | None = ...,
        observation: global___RequestObservation | None = ...,
        action: global___RequestAction | None = ...,
        obs_action: global___RequestObserverAction | None = ...,
        step: global___RequestStep | None = ...,
        data: global___RequestData | None = ...,
        query: s2clientprotocol.query_pb2.RequestQuery | None = ...,
        save_replay: global___RequestSaveReplay | None = ...,
        map_command: global___RequestMapCommand | None = ...,
        replay_info: global___RequestReplayInfo | None = ...,
        available_maps: global___RequestAvailableMaps | None = ...,
        save_map: global___RequestSaveMap | None = ...,
        ping: global___RequestPing | None = ...,
        debug: global___RequestDebug | None = ...,
        id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "action",
            b"action",
            "available_maps",
            b"available_maps",
            "create_game",
            b"create_game",
            "data",
            b"data",
            "debug",
            b"debug",
            "game_info",
            b"game_info",
            "id",
            b"id",
            "join_game",
            b"join_game",
            "leave_game",
            b"leave_game",
            "map_command",
            b"map_command",
            "obs_action",
            b"obs_action",
            "observation",
            b"observation",
            "ping",
            b"ping",
            "query",
            b"query",
            "quick_load",
            b"quick_load",
            "quick_save",
            b"quick_save",
            "quit",
            b"quit",
            "replay_info",
            b"replay_info",
            "request",
            b"request",
            "restart_game",
            b"restart_game",
            "save_map",
            b"save_map",
            "save_replay",
            b"save_replay",
            "start_replay",
            b"start_replay",
            "step",
            b"step",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "action",
            b"action",
            "available_maps",
            b"available_maps",
            "create_game",
            b"create_game",
            "data",
            b"data",
            "debug",
            b"debug",
            "game_info",
            b"game_info",
            "id",
            b"id",
            "join_game",
            b"join_game",
            "leave_game",
            b"leave_game",
            "map_command",
            b"map_command",
            "obs_action",
            b"obs_action",
            "observation",
            b"observation",
            "ping",
            b"ping",
            "query",
            b"query",
            "quick_load",
            b"quick_load",
            "quick_save",
            b"quick_save",
            "quit",
            b"quit",
            "replay_info",
            b"replay_info",
            "request",
            b"request",
            "restart_game",
            b"restart_game",
            "save_map",
            b"save_map",
            "save_replay",
            b"save_replay",
            "start_replay",
            b"start_replay",
            "step",
            b"step",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing.Literal["request", b"request"]
    ) -> (
        typing.Literal[
            "create_game",
            "join_game",
            "restart_game",
            "start_replay",
            "leave_game",
            "quick_save",
            "quick_load",
            "quit",
            "game_info",
            "observation",
            "action",
            "obs_action",
            "step",
            "data",
            "query",
            "save_replay",
            "map_command",
            "replay_info",
            "available_maps",
            "save_map",
            "ping",
            "debug",
        ]
        | None
    ): ...

global___Request = Request

@typing.final
class Response(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CREATE_GAME_FIELD_NUMBER: builtins.int
    JOIN_GAME_FIELD_NUMBER: builtins.int
    RESTART_GAME_FIELD_NUMBER: builtins.int
    START_REPLAY_FIELD_NUMBER: builtins.int
    LEAVE_GAME_FIELD_NUMBER: builtins.int
    QUICK_SAVE_FIELD_NUMBER: builtins.int
    QUICK_LOAD_FIELD_NUMBER: builtins.int
    QUIT_FIELD_NUMBER: builtins.int
    GAME_INFO_FIELD_NUMBER: builtins.int
    OBSERVATION_FIELD_NUMBER: builtins.int
    ACTION_FIELD_NUMBER: builtins.int
    OBS_ACTION_FIELD_NUMBER: builtins.int
    STEP_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    QUERY_FIELD_NUMBER: builtins.int
    SAVE_REPLAY_FIELD_NUMBER: builtins.int
    REPLAY_INFO_FIELD_NUMBER: builtins.int
    AVAILABLE_MAPS_FIELD_NUMBER: builtins.int
    SAVE_MAP_FIELD_NUMBER: builtins.int
    MAP_COMMAND_FIELD_NUMBER: builtins.int
    PING_FIELD_NUMBER: builtins.int
    DEBUG_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    STATUS_FIELD_NUMBER: builtins.int
    id: builtins.int
    status: global___Status.ValueType
    """Should be sent back with all responses."""
    @property
    def create_game(self) -> global___ResponseCreateGame: ...
    @property
    def join_game(self) -> global___ResponseJoinGame: ...
    @property
    def restart_game(self) -> global___ResponseRestartGame: ...
    @property
    def start_replay(self) -> global___ResponseStartReplay: ...
    @property
    def leave_game(self) -> global___ResponseLeaveGame: ...
    @property
    def quick_save(self) -> global___ResponseQuickSave: ...
    @property
    def quick_load(self) -> global___ResponseQuickLoad: ...
    @property
    def quit(self) -> global___ResponseQuit: ...
    @property
    def game_info(self) -> global___ResponseGameInfo: ...
    @property
    def observation(self) -> global___ResponseObservation: ...
    @property
    def action(self) -> global___ResponseAction: ...
    @property
    def obs_action(self) -> global___ResponseObserverAction: ...
    @property
    def step(self) -> global___ResponseStep: ...
    @property
    def data(self) -> global___ResponseData: ...
    @property
    def query(self) -> s2clientprotocol.query_pb2.ResponseQuery: ...
    @property
    def save_replay(self) -> global___ResponseSaveReplay: ...
    @property
    def replay_info(self) -> global___ResponseReplayInfo: ...
    @property
    def available_maps(self) -> global___ResponseAvailableMaps: ...
    @property
    def save_map(self) -> global___ResponseSaveMap: ...
    @property
    def map_command(self) -> global___ResponseMapCommand: ...
    @property
    def ping(self) -> global___ResponsePing:
        """Debugging"""

    @property
    def debug(self) -> global___ResponseDebug: ...
    @property
    def error(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """If command is missing, this will contain the error. Otherwise this will contain any warnings."""

    def __init__(
        self,
        *,
        create_game: global___ResponseCreateGame | None = ...,
        join_game: global___ResponseJoinGame | None = ...,
        restart_game: global___ResponseRestartGame | None = ...,
        start_replay: global___ResponseStartReplay | None = ...,
        leave_game: global___ResponseLeaveGame | None = ...,
        quick_save: global___ResponseQuickSave | None = ...,
        quick_load: global___ResponseQuickLoad | None = ...,
        quit: global___ResponseQuit | None = ...,
        game_info: global___ResponseGameInfo | None = ...,
        observation: global___ResponseObservation | None = ...,
        action: global___ResponseAction | None = ...,
        obs_action: global___ResponseObserverAction | None = ...,
        step: global___ResponseStep | None = ...,
        data: global___ResponseData | None = ...,
        query: s2clientprotocol.query_pb2.ResponseQuery | None = ...,
        save_replay: global___ResponseSaveReplay | None = ...,
        replay_info: global___ResponseReplayInfo | None = ...,
        available_maps: global___ResponseAvailableMaps | None = ...,
        save_map: global___ResponseSaveMap | None = ...,
        map_command: global___ResponseMapCommand | None = ...,
        ping: global___ResponsePing | None = ...,
        debug: global___ResponseDebug | None = ...,
        id: builtins.int | None = ...,
        error: collections.abc.Iterable[builtins.str] | None = ...,
        status: global___Status.ValueType | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "action",
            b"action",
            "available_maps",
            b"available_maps",
            "create_game",
            b"create_game",
            "data",
            b"data",
            "debug",
            b"debug",
            "game_info",
            b"game_info",
            "id",
            b"id",
            "join_game",
            b"join_game",
            "leave_game",
            b"leave_game",
            "map_command",
            b"map_command",
            "obs_action",
            b"obs_action",
            "observation",
            b"observation",
            "ping",
            b"ping",
            "query",
            b"query",
            "quick_load",
            b"quick_load",
            "quick_save",
            b"quick_save",
            "quit",
            b"quit",
            "replay_info",
            b"replay_info",
            "response",
            b"response",
            "restart_game",
            b"restart_game",
            "save_map",
            b"save_map",
            "save_replay",
            b"save_replay",
            "start_replay",
            b"start_replay",
            "status",
            b"status",
            "step",
            b"step",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "action",
            b"action",
            "available_maps",
            b"available_maps",
            "create_game",
            b"create_game",
            "data",
            b"data",
            "debug",
            b"debug",
            "error",
            b"error",
            "game_info",
            b"game_info",
            "id",
            b"id",
            "join_game",
            b"join_game",
            "leave_game",
            b"leave_game",
            "map_command",
            b"map_command",
            "obs_action",
            b"obs_action",
            "observation",
            b"observation",
            "ping",
            b"ping",
            "query",
            b"query",
            "quick_load",
            b"quick_load",
            "quick_save",
            b"quick_save",
            "quit",
            b"quit",
            "replay_info",
            b"replay_info",
            "response",
            b"response",
            "restart_game",
            b"restart_game",
            "save_map",
            b"save_map",
            "save_replay",
            b"save_replay",
            "start_replay",
            b"start_replay",
            "status",
            b"status",
            "step",
            b"step",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing.Literal["response", b"response"]
    ) -> (
        typing.Literal[
            "create_game",
            "join_game",
            "restart_game",
            "start_replay",
            "leave_game",
            "quick_save",
            "quick_load",
            "quit",
            "game_info",
            "observation",
            "action",
            "obs_action",
            "step",
            "data",
            "query",
            "save_replay",
            "replay_info",
            "available_maps",
            "save_map",
            "map_command",
            "ping",
            "debug",
        ]
        | None
    ): ...

global___Response = Response

@typing.final
class RequestCreateGame(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------
    If successful, puts the game into the status: init_game.
    The next expected request should be RequestJoinGame. Can also quit (exit).
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_MAP_FIELD_NUMBER: builtins.int
    BATTLENET_MAP_NAME_FIELD_NUMBER: builtins.int
    PLAYER_SETUP_FIELD_NUMBER: builtins.int
    DISABLE_FOG_FIELD_NUMBER: builtins.int
    RANDOM_SEED_FIELD_NUMBER: builtins.int
    REALTIME_FIELD_NUMBER: builtins.int
    battlenet_map_name: builtins.str
    """Map published to BattleNet"""
    disable_fog: builtins.bool
    random_seed: builtins.int
    """Sets the pseudo-random seed for the game."""
    realtime: builtins.bool
    """If set, the game plays in real time."""
    @property
    def local_map(self) -> global___LocalMap:
        """Local .SC2Map file"""

    @property
    def player_setup(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___PlayerSetup]: ...
    def __init__(
        self,
        *,
        local_map: global___LocalMap | None = ...,
        battlenet_map_name: builtins.str | None = ...,
        player_setup: collections.abc.Iterable[global___PlayerSetup] | None = ...,
        disable_fog: builtins.bool | None = ...,
        random_seed: builtins.int | None = ...,
        realtime: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "Map",
            b"Map",
            "battlenet_map_name",
            b"battlenet_map_name",
            "disable_fog",
            b"disable_fog",
            "local_map",
            b"local_map",
            "random_seed",
            b"random_seed",
            "realtime",
            b"realtime",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "Map",
            b"Map",
            "battlenet_map_name",
            b"battlenet_map_name",
            "disable_fog",
            b"disable_fog",
            "local_map",
            b"local_map",
            "player_setup",
            b"player_setup",
            "random_seed",
            b"random_seed",
            "realtime",
            b"realtime",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing.Literal["Map", b"Map"]
    ) -> typing.Literal["local_map", "battlenet_map_name"] | None: ...

global___RequestCreateGame = RequestCreateGame

@typing.final
class LocalMap(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MAP_PATH_FIELD_NUMBER: builtins.int
    MAP_DATA_FIELD_NUMBER: builtins.int
    map_path: builtins.str
    """A map can be specified either by a file path or the data of the .SC2Map file.
    If you provide both, it will play the game using map_data and store map_path
    into the replay. (260 character max)
    """
    map_data: builtins.bytes
    def __init__(self, *, map_path: builtins.str | None = ..., map_data: builtins.bytes | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["map_data", b"map_data", "map_path", b"map_path"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["map_data", b"map_data", "map_path", b"map_path"]) -> None: ...

global___LocalMap = LocalMap

@typing.final
class ResponseCreateGame(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Error:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ErrorEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ResponseCreateGame._Error.ValueType], builtins.type
    ):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        MissingMap: ResponseCreateGame._Error.ValueType  # 1
        InvalidMapPath: ResponseCreateGame._Error.ValueType  # 2
        InvalidMapData: ResponseCreateGame._Error.ValueType  # 3
        InvalidMapName: ResponseCreateGame._Error.ValueType  # 4
        InvalidMapHandle: ResponseCreateGame._Error.ValueType  # 5
        MissingPlayerSetup: ResponseCreateGame._Error.ValueType  # 6
        InvalidPlayerSetup: ResponseCreateGame._Error.ValueType  # 7
        MultiplayerUnsupported: ResponseCreateGame._Error.ValueType  # 8
        """Multiplayer is not supported in the current build."""

    class Error(_Error, metaclass=_ErrorEnumTypeWrapper): ...
    MissingMap: ResponseCreateGame.Error.ValueType  # 1
    InvalidMapPath: ResponseCreateGame.Error.ValueType  # 2
    InvalidMapData: ResponseCreateGame.Error.ValueType  # 3
    InvalidMapName: ResponseCreateGame.Error.ValueType  # 4
    InvalidMapHandle: ResponseCreateGame.Error.ValueType  # 5
    MissingPlayerSetup: ResponseCreateGame.Error.ValueType  # 6
    InvalidPlayerSetup: ResponseCreateGame.Error.ValueType  # 7
    MultiplayerUnsupported: ResponseCreateGame.Error.ValueType  # 8
    """Multiplayer is not supported in the current build."""

    ERROR_FIELD_NUMBER: builtins.int
    ERROR_DETAILS_FIELD_NUMBER: builtins.int
    error: global___ResponseCreateGame.Error.ValueType
    error_details: builtins.str
    def __init__(
        self, *, error: global___ResponseCreateGame.Error.ValueType | None = ..., error_details: builtins.str | None = ...
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error", "error_details", b"error_details"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error", "error_details", b"error_details"]) -> None: ...

global___ResponseCreateGame = ResponseCreateGame

@typing.final
class RequestJoinGame(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------
    If successful, puts the game into the status: in_game. Will be able to
    request actions, observations and step the game.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RACE_FIELD_NUMBER: builtins.int
    OBSERVED_PLAYER_ID_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    SERVER_PORTS_FIELD_NUMBER: builtins.int
    CLIENT_PORTS_FIELD_NUMBER: builtins.int
    SHARED_PORT_FIELD_NUMBER: builtins.int
    PLAYER_NAME_FIELD_NUMBER: builtins.int
    HOST_IP_FIELD_NUMBER: builtins.int
    race: s2clientprotocol.common_pb2.Race.ValueType
    """Join as participant"""
    observed_player_id: builtins.int
    """Join as observer"""
    shared_port: builtins.int
    """Currently only a singe client is supported.
    deprecated
    """
    player_name: builtins.str
    """Use this to set the player's name to something other than autogenerated name."""
    host_ip: builtins.str
    """Both game creator and joiner should provide the ip address of the game creator in order to play remotely. Defaults to localhost."""
    @property
    def options(self) -> global___InterfaceOptions:
        """This is limited to what is specified in RequestCreateGame, but you can request less information if you want."""

    @property
    def server_ports(self) -> global___PortSet:
        """Do not set in the single-player case. This is the port a server will use."""

    @property
    def client_ports(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___PortSet]:
        """Do not set in the single-player case. These are the ports clients will use to initialize communication."""

    def __init__(
        self,
        *,
        race: s2clientprotocol.common_pb2.Race.ValueType | None = ...,
        observed_player_id: builtins.int | None = ...,
        options: global___InterfaceOptions | None = ...,
        server_ports: global___PortSet | None = ...,
        client_ports: collections.abc.Iterable[global___PortSet] | None = ...,
        shared_port: builtins.int | None = ...,
        player_name: builtins.str | None = ...,
        host_ip: builtins.str | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "host_ip",
            b"host_ip",
            "observed_player_id",
            b"observed_player_id",
            "options",
            b"options",
            "participation",
            b"participation",
            "player_name",
            b"player_name",
            "race",
            b"race",
            "server_ports",
            b"server_ports",
            "shared_port",
            b"shared_port",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "client_ports",
            b"client_ports",
            "host_ip",
            b"host_ip",
            "observed_player_id",
            b"observed_player_id",
            "options",
            b"options",
            "participation",
            b"participation",
            "player_name",
            b"player_name",
            "race",
            b"race",
            "server_ports",
            b"server_ports",
            "shared_port",
            b"shared_port",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing.Literal["participation", b"participation"]
    ) -> typing.Literal["race", "observed_player_id"] | None: ...

global___RequestJoinGame = RequestJoinGame

@typing.final
class PortSet(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GAME_PORT_FIELD_NUMBER: builtins.int
    BASE_PORT_FIELD_NUMBER: builtins.int
    game_port: builtins.int
    """Game right now needs two internal ports to establish a multiplay game on the local host."""
    base_port: builtins.int
    def __init__(self, *, game_port: builtins.int | None = ..., base_port: builtins.int | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["base_port", b"base_port", "game_port", b"game_port"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["base_port", b"base_port", "game_port", b"game_port"]) -> None: ...

global___PortSet = PortSet

@typing.final
class ResponseJoinGame(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Error:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ErrorEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ResponseJoinGame._Error.ValueType], builtins.type
    ):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        MissingParticipation: ResponseJoinGame._Error.ValueType  # 1
        InvalidObservedPlayerId: ResponseJoinGame._Error.ValueType  # 2
        MissingOptions: ResponseJoinGame._Error.ValueType  # 3
        MissingPorts: ResponseJoinGame._Error.ValueType  # 4
        GameFull: ResponseJoinGame._Error.ValueType  # 5
        LaunchError: ResponseJoinGame._Error.ValueType  # 6
        FeatureUnsupported: ResponseJoinGame._Error.ValueType  # 7
        """Multiplayer specific.
        Multiplayer is not supported in the current build for the requested features.
        """
        NoSpaceForUser: ResponseJoinGame._Error.ValueType  # 8
        MapDoesNotExist: ResponseJoinGame._Error.ValueType  # 9
        CannotOpenMap: ResponseJoinGame._Error.ValueType  # 10
        ChecksumError: ResponseJoinGame._Error.ValueType  # 11
        NetworkError: ResponseJoinGame._Error.ValueType  # 12
        OtherError: ResponseJoinGame._Error.ValueType  # 13

    class Error(_Error, metaclass=_ErrorEnumTypeWrapper): ...
    MissingParticipation: ResponseJoinGame.Error.ValueType  # 1
    InvalidObservedPlayerId: ResponseJoinGame.Error.ValueType  # 2
    MissingOptions: ResponseJoinGame.Error.ValueType  # 3
    MissingPorts: ResponseJoinGame.Error.ValueType  # 4
    GameFull: ResponseJoinGame.Error.ValueType  # 5
    LaunchError: ResponseJoinGame.Error.ValueType  # 6
    FeatureUnsupported: ResponseJoinGame.Error.ValueType  # 7
    """Multiplayer specific.
    Multiplayer is not supported in the current build for the requested features.
    """
    NoSpaceForUser: ResponseJoinGame.Error.ValueType  # 8
    MapDoesNotExist: ResponseJoinGame.Error.ValueType  # 9
    CannotOpenMap: ResponseJoinGame.Error.ValueType  # 10
    ChecksumError: ResponseJoinGame.Error.ValueType  # 11
    NetworkError: ResponseJoinGame.Error.ValueType  # 12
    OtherError: ResponseJoinGame.Error.ValueType  # 13

    PLAYER_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    ERROR_DETAILS_FIELD_NUMBER: builtins.int
    player_id: builtins.int
    error: global___ResponseJoinGame.Error.ValueType
    error_details: builtins.str
    def __init__(
        self,
        *,
        player_id: builtins.int | None = ...,
        error: global___ResponseJoinGame.Error.ValueType | None = ...,
        error_details: builtins.str | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing.Literal["error", b"error", "error_details", b"error_details", "player_id", b"player_id"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing.Literal["error", b"error", "error_details", b"error_details", "player_id", b"player_id"]
    ) -> None: ...

global___ResponseJoinGame = ResponseJoinGame

@typing.final
class RequestRestartGame(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___RequestRestartGame = RequestRestartGame

@typing.final
class ResponseRestartGame(google.protobuf.message.Message):
    """The defaultRestartGameLoops is specified to be (1<<18) by default"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Error:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ErrorEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ResponseRestartGame._Error.ValueType], builtins.type
    ):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        LaunchError: ResponseRestartGame._Error.ValueType  # 1

    class Error(_Error, metaclass=_ErrorEnumTypeWrapper): ...
    LaunchError: ResponseRestartGame.Error.ValueType  # 1

    ERROR_FIELD_NUMBER: builtins.int
    ERROR_DETAILS_FIELD_NUMBER: builtins.int
    NEED_HARD_RESET_FIELD_NUMBER: builtins.int
    error: global___ResponseRestartGame.Error.ValueType
    error_details: builtins.str
    need_hard_reset: builtins.bool
    """This will occur once the simulation_loop is greater then defaultRestartGameLoops"""
    def __init__(
        self,
        *,
        error: global___ResponseRestartGame.Error.ValueType | None = ...,
        error_details: builtins.str | None = ...,
        need_hard_reset: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal["error", b"error", "error_details", b"error_details", "need_hard_reset", b"need_hard_reset"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal["error", b"error", "error_details", b"error_details", "need_hard_reset", b"need_hard_reset"],
    ) -> None: ...

global___ResponseRestartGame = ResponseRestartGame

@typing.final
class RequestStartReplay(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REPLAY_PATH_FIELD_NUMBER: builtins.int
    REPLAY_DATA_FIELD_NUMBER: builtins.int
    MAP_DATA_FIELD_NUMBER: builtins.int
    OBSERVED_PLAYER_ID_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    DISABLE_FOG_FIELD_NUMBER: builtins.int
    REALTIME_FIELD_NUMBER: builtins.int
    RECORD_REPLAY_FIELD_NUMBER: builtins.int
    replay_path: builtins.str
    replay_data: builtins.bytes
    map_data: builtins.bytes
    """Overrides the map path stored in the replay."""
    observed_player_id: builtins.int
    disable_fog: builtins.bool
    realtime: builtins.bool
    record_replay: builtins.bool
    """Allow RequestSaveReplay from a replay. Useful for truncating a replay, or restoring tracker.events."""
    @property
    def options(self) -> global___InterfaceOptions: ...
    def __init__(
        self,
        *,
        replay_path: builtins.str | None = ...,
        replay_data: builtins.bytes | None = ...,
        map_data: builtins.bytes | None = ...,
        observed_player_id: builtins.int | None = ...,
        options: global___InterfaceOptions | None = ...,
        disable_fog: builtins.bool | None = ...,
        realtime: builtins.bool | None = ...,
        record_replay: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "disable_fog",
            b"disable_fog",
            "map_data",
            b"map_data",
            "observed_player_id",
            b"observed_player_id",
            "options",
            b"options",
            "realtime",
            b"realtime",
            "record_replay",
            b"record_replay",
            "replay",
            b"replay",
            "replay_data",
            b"replay_data",
            "replay_path",
            b"replay_path",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "disable_fog",
            b"disable_fog",
            "map_data",
            b"map_data",
            "observed_player_id",
            b"observed_player_id",
            "options",
            b"options",
            "realtime",
            b"realtime",
            "record_replay",
            b"record_replay",
            "replay",
            b"replay",
            "replay_data",
            b"replay_data",
            "replay_path",
            b"replay_path",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing.Literal["replay", b"replay"]
    ) -> typing.Literal["replay_path", "replay_data"] | None: ...

global___RequestStartReplay = RequestStartReplay

@typing.final
class ResponseStartReplay(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Error:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ErrorEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ResponseStartReplay._Error.ValueType], builtins.type
    ):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        MissingReplay: ResponseStartReplay._Error.ValueType  # 1
        InvalidReplayPath: ResponseStartReplay._Error.ValueType  # 2
        InvalidReplayData: ResponseStartReplay._Error.ValueType  # 3
        InvalidMapData: ResponseStartReplay._Error.ValueType  # 4
        InvalidObservedPlayerId: ResponseStartReplay._Error.ValueType  # 5
        MissingOptions: ResponseStartReplay._Error.ValueType  # 6
        LaunchError: ResponseStartReplay._Error.ValueType  # 7

    class Error(_Error, metaclass=_ErrorEnumTypeWrapper): ...
    MissingReplay: ResponseStartReplay.Error.ValueType  # 1
    InvalidReplayPath: ResponseStartReplay.Error.ValueType  # 2
    InvalidReplayData: ResponseStartReplay.Error.ValueType  # 3
    InvalidMapData: ResponseStartReplay.Error.ValueType  # 4
    InvalidObservedPlayerId: ResponseStartReplay.Error.ValueType  # 5
    MissingOptions: ResponseStartReplay.Error.ValueType  # 6
    LaunchError: ResponseStartReplay.Error.ValueType  # 7

    ERROR_FIELD_NUMBER: builtins.int
    ERROR_DETAILS_FIELD_NUMBER: builtins.int
    error: global___ResponseStartReplay.Error.ValueType
    error_details: builtins.str
    def __init__(
        self, *, error: global___ResponseStartReplay.Error.ValueType | None = ..., error_details: builtins.str | None = ...
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error", "error_details", b"error_details"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error", "error_details", b"error_details"]) -> None: ...

global___ResponseStartReplay = ResponseStartReplay

@typing.final
class RequestMapCommand(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRIGGER_CMD_FIELD_NUMBER: builtins.int
    trigger_cmd: builtins.str
    def __init__(self, *, trigger_cmd: builtins.str | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["trigger_cmd", b"trigger_cmd"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["trigger_cmd", b"trigger_cmd"]) -> None: ...

global___RequestMapCommand = RequestMapCommand

@typing.final
class ResponseMapCommand(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Error:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ErrorEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ResponseMapCommand._Error.ValueType], builtins.type
    ):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        NoTriggerError: ResponseMapCommand._Error.ValueType  # 1

    class Error(_Error, metaclass=_ErrorEnumTypeWrapper): ...
    NoTriggerError: ResponseMapCommand.Error.ValueType  # 1

    ERROR_FIELD_NUMBER: builtins.int
    ERROR_DETAILS_FIELD_NUMBER: builtins.int
    error: global___ResponseMapCommand.Error.ValueType
    error_details: builtins.str
    def __init__(
        self, *, error: global___ResponseMapCommand.Error.ValueType | None = ..., error_details: builtins.str | None = ...
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error", "error_details", b"error_details"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error", "error_details", b"error_details"]) -> None: ...

global___ResponseMapCommand = ResponseMapCommand

@typing.final
class RequestLeaveGame(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___RequestLeaveGame = RequestLeaveGame

@typing.final
class ResponseLeaveGame(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___ResponseLeaveGame = ResponseLeaveGame

@typing.final
class RequestQuickSave(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___RequestQuickSave = RequestQuickSave

@typing.final
class ResponseQuickSave(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___ResponseQuickSave = ResponseQuickSave

@typing.final
class RequestQuickLoad(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___RequestQuickLoad = RequestQuickLoad

@typing.final
class ResponseQuickLoad(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___ResponseQuickLoad = ResponseQuickLoad

@typing.final
class RequestQuit(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___RequestQuit = RequestQuit

@typing.final
class ResponseQuit(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___ResponseQuit = ResponseQuit

@typing.final
class RequestGameInfo(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___RequestGameInfo = RequestGameInfo

@typing.final
class ResponseGameInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MAP_NAME_FIELD_NUMBER: builtins.int
    MOD_NAMES_FIELD_NUMBER: builtins.int
    LOCAL_MAP_PATH_FIELD_NUMBER: builtins.int
    PLAYER_INFO_FIELD_NUMBER: builtins.int
    START_RAW_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    map_name: builtins.str
    local_map_path: builtins.str
    @property
    def mod_names(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def player_info(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___PlayerInfo]: ...
    @property
    def start_raw(self) -> s2clientprotocol.raw_pb2.StartRaw:
        """Populated if Raw interface is enabled."""

    @property
    def options(self) -> global___InterfaceOptions: ...
    def __init__(
        self,
        *,
        map_name: builtins.str | None = ...,
        mod_names: collections.abc.Iterable[builtins.str] | None = ...,
        local_map_path: builtins.str | None = ...,
        player_info: collections.abc.Iterable[global___PlayerInfo] | None = ...,
        start_raw: s2clientprotocol.raw_pb2.StartRaw | None = ...,
        options: global___InterfaceOptions | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "local_map_path", b"local_map_path", "map_name", b"map_name", "options", b"options", "start_raw", b"start_raw"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "local_map_path",
            b"local_map_path",
            "map_name",
            b"map_name",
            "mod_names",
            b"mod_names",
            "options",
            b"options",
            "player_info",
            b"player_info",
            "start_raw",
            b"start_raw",
        ],
    ) -> None: ...

global___ResponseGameInfo = ResponseGameInfo

@typing.final
class RequestObservation(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DISABLE_FOG_FIELD_NUMBER: builtins.int
    GAME_LOOP_FIELD_NUMBER: builtins.int
    disable_fog: builtins.bool
    game_loop: builtins.int
    """In realtime the request will only return once the simulation game loop has reached this value. When not realtime this value is ignored."""
    def __init__(self, *, disable_fog: builtins.bool | None = ..., game_loop: builtins.int | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["disable_fog", b"disable_fog", "game_loop", b"game_loop"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["disable_fog", b"disable_fog", "game_loop", b"game_loop"]) -> None: ...

global___RequestObservation = RequestObservation

@typing.final
class ResponseObservation(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ACTIONS_FIELD_NUMBER: builtins.int
    ACTION_ERRORS_FIELD_NUMBER: builtins.int
    OBSERVATION_FIELD_NUMBER: builtins.int
    PLAYER_RESULT_FIELD_NUMBER: builtins.int
    CHAT_FIELD_NUMBER: builtins.int
    @property
    def actions(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Action]:
        """Actions this player did since the last Observation."""

    @property
    def action_errors(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ActionError]:
        """Equivalent of UI "red text" errors."""

    @property
    def observation(self) -> global___Observation: ...
    @property
    def player_result(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___PlayerResult]:
        """Only populated if the game ended during this step."""

    @property
    def chat(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ChatReceived]: ...
    def __init__(
        self,
        *,
        actions: collections.abc.Iterable[global___Action] | None = ...,
        action_errors: collections.abc.Iterable[global___ActionError] | None = ...,
        observation: global___Observation | None = ...,
        player_result: collections.abc.Iterable[global___PlayerResult] | None = ...,
        chat: collections.abc.Iterable[global___ChatReceived] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["observation", b"observation"]) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "action_errors",
            b"action_errors",
            "actions",
            b"actions",
            "chat",
            b"chat",
            "observation",
            b"observation",
            "player_result",
            b"player_result",
        ],
    ) -> None: ...

global___ResponseObservation = ResponseObservation

@typing.final
class ChatReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PLAYER_ID_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    player_id: builtins.int
    message: builtins.str
    def __init__(self, *, player_id: builtins.int | None = ..., message: builtins.str | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["message", b"message", "player_id", b"player_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["message", b"message", "player_id", b"player_id"]) -> None: ...

global___ChatReceived = ChatReceived

@typing.final
class RequestAction(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ACTIONS_FIELD_NUMBER: builtins.int
    @property
    def actions(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Action]: ...
    def __init__(self, *, actions: collections.abc.Iterable[global___Action] | None = ...) -> None: ...
    def ClearField(self, field_name: typing.Literal["actions", b"actions"]) -> None: ...

global___RequestAction = RequestAction

@typing.final
class ResponseAction(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    @property
    def result(
        self,
    ) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[s2clientprotocol.error_pb2.ActionResult.ValueType]: ...
    def __init__(
        self, *, result: collections.abc.Iterable[s2clientprotocol.error_pb2.ActionResult.ValueType] | None = ...
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["result", b"result"]) -> None: ...

global___ResponseAction = ResponseAction

@typing.final
class RequestObserverAction(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ACTIONS_FIELD_NUMBER: builtins.int
    @property
    def actions(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ObserverAction]: ...
    def __init__(self, *, actions: collections.abc.Iterable[global___ObserverAction] | None = ...) -> None: ...
    def ClearField(self, field_name: typing.Literal["actions", b"actions"]) -> None: ...

global___RequestObserverAction = RequestObserverAction

@typing.final
class ResponseObserverAction(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___ResponseObserverAction = ResponseObserverAction

@typing.final
class RequestStep(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COUNT_FIELD_NUMBER: builtins.int
    count: builtins.int
    """Number of game loops to simulate for the next frame."""
    def __init__(self, *, count: builtins.int | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["count", b"count"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["count", b"count"]) -> None: ...

global___RequestStep = RequestStep

@typing.final
class ResponseStep(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SIMULATION_LOOP_FIELD_NUMBER: builtins.int
    simulation_loop: builtins.int
    """ Max simulation_loop is (1<<19) before "end of time" will occur
     The "end of time" is classified as the maximum number of game loops or absolute game time
     representable as a positive fixed point number.
     When we reach the "end of time", permanently pause the game and end the game for all.
    """
    def __init__(self, *, simulation_loop: builtins.int | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["simulation_loop", b"simulation_loop"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["simulation_loop", b"simulation_loop"]) -> None: ...

global___ResponseStep = ResponseStep

@typing.final
class RequestData(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ABILITY_ID_FIELD_NUMBER: builtins.int
    UNIT_TYPE_ID_FIELD_NUMBER: builtins.int
    UPGRADE_ID_FIELD_NUMBER: builtins.int
    BUFF_ID_FIELD_NUMBER: builtins.int
    EFFECT_ID_FIELD_NUMBER: builtins.int
    ability_id: builtins.bool
    unit_type_id: builtins.bool
    upgrade_id: builtins.bool
    buff_id: builtins.bool
    effect_id: builtins.bool
    def __init__(
        self,
        *,
        ability_id: builtins.bool | None = ...,
        unit_type_id: builtins.bool | None = ...,
        upgrade_id: builtins.bool | None = ...,
        buff_id: builtins.bool | None = ...,
        effect_id: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "ability_id",
            b"ability_id",
            "buff_id",
            b"buff_id",
            "effect_id",
            b"effect_id",
            "unit_type_id",
            b"unit_type_id",
            "upgrade_id",
            b"upgrade_id",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "ability_id",
            b"ability_id",
            "buff_id",
            b"buff_id",
            "effect_id",
            b"effect_id",
            "unit_type_id",
            b"unit_type_id",
            "upgrade_id",
            b"upgrade_id",
        ],
    ) -> None: ...

global___RequestData = RequestData

@typing.final
class ResponseData(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ABILITIES_FIELD_NUMBER: builtins.int
    UNITS_FIELD_NUMBER: builtins.int
    UPGRADES_FIELD_NUMBER: builtins.int
    BUFFS_FIELD_NUMBER: builtins.int
    EFFECTS_FIELD_NUMBER: builtins.int
    @property
    def abilities(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[s2clientprotocol.data_pb2.AbilityData]: ...
    @property
    def units(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[s2clientprotocol.data_pb2.UnitTypeData]: ...
    @property
    def upgrades(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[s2clientprotocol.data_pb2.UpgradeData]: ...
    @property
    def buffs(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[s2clientprotocol.data_pb2.BuffData]: ...
    @property
    def effects(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[s2clientprotocol.data_pb2.EffectData]: ...
    def __init__(
        self,
        *,
        abilities: collections.abc.Iterable[s2clientprotocol.data_pb2.AbilityData] | None = ...,
        units: collections.abc.Iterable[s2clientprotocol.data_pb2.UnitTypeData] | None = ...,
        upgrades: collections.abc.Iterable[s2clientprotocol.data_pb2.UpgradeData] | None = ...,
        buffs: collections.abc.Iterable[s2clientprotocol.data_pb2.BuffData] | None = ...,
        effects: collections.abc.Iterable[s2clientprotocol.data_pb2.EffectData] | None = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "abilities", b"abilities", "buffs", b"buffs", "effects", b"effects", "units", b"units", "upgrades", b"upgrades"
        ],
    ) -> None: ...

global___ResponseData = ResponseData

@typing.final
class RequestSaveReplay(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___RequestSaveReplay = RequestSaveReplay

@typing.final
class ResponseSaveReplay(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    data: builtins.bytes
    def __init__(self, *, data: builtins.bytes | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data"]) -> None: ...

global___ResponseSaveReplay = ResponseSaveReplay

@typing.final
class RequestReplayInfo(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REPLAY_PATH_FIELD_NUMBER: builtins.int
    REPLAY_DATA_FIELD_NUMBER: builtins.int
    DOWNLOAD_DATA_FIELD_NUMBER: builtins.int
    replay_path: builtins.str
    """Limitation: might fail if the replay file is currently loaded."""
    replay_data: builtins.bytes
    download_data: builtins.bool
    """Ensure the data and binary are downloaded if this is an old version replay."""
    def __init__(
        self,
        *,
        replay_path: builtins.str | None = ...,
        replay_data: builtins.bytes | None = ...,
        download_data: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "download_data", b"download_data", "replay", b"replay", "replay_data", b"replay_data", "replay_path", b"replay_path"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "download_data", b"download_data", "replay", b"replay", "replay_data", b"replay_data", "replay_path", b"replay_path"
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing.Literal["replay", b"replay"]
    ) -> typing.Literal["replay_path", "replay_data"] | None: ...

global___RequestReplayInfo = RequestReplayInfo

@typing.final
class PlayerInfoExtra(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PLAYER_INFO_FIELD_NUMBER: builtins.int
    PLAYER_RESULT_FIELD_NUMBER: builtins.int
    PLAYER_MMR_FIELD_NUMBER: builtins.int
    PLAYER_APM_FIELD_NUMBER: builtins.int
    player_mmr: builtins.int
    player_apm: builtins.int
    @property
    def player_info(self) -> global___PlayerInfo: ...
    @property
    def player_result(self) -> global___PlayerResult: ...
    def __init__(
        self,
        *,
        player_info: global___PlayerInfo | None = ...,
        player_result: global___PlayerResult | None = ...,
        player_mmr: builtins.int | None = ...,
        player_apm: builtins.int | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "player_apm",
            b"player_apm",
            "player_info",
            b"player_info",
            "player_mmr",
            b"player_mmr",
            "player_result",
            b"player_result",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "player_apm",
            b"player_apm",
            "player_info",
            b"player_info",
            "player_mmr",
            b"player_mmr",
            "player_result",
            b"player_result",
        ],
    ) -> None: ...

global___PlayerInfoExtra = PlayerInfoExtra

@typing.final
class ResponseReplayInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Error:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ErrorEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ResponseReplayInfo._Error.ValueType], builtins.type
    ):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        MissingReplay: ResponseReplayInfo._Error.ValueType  # 1
        InvalidReplayPath: ResponseReplayInfo._Error.ValueType  # 2
        InvalidReplayData: ResponseReplayInfo._Error.ValueType  # 3
        ParsingError: ResponseReplayInfo._Error.ValueType  # 4
        DownloadError: ResponseReplayInfo._Error.ValueType  # 5

    class Error(_Error, metaclass=_ErrorEnumTypeWrapper): ...
    MissingReplay: ResponseReplayInfo.Error.ValueType  # 1
    InvalidReplayPath: ResponseReplayInfo.Error.ValueType  # 2
    InvalidReplayData: ResponseReplayInfo.Error.ValueType  # 3
    ParsingError: ResponseReplayInfo.Error.ValueType  # 4
    DownloadError: ResponseReplayInfo.Error.ValueType  # 5

    MAP_NAME_FIELD_NUMBER: builtins.int
    LOCAL_MAP_PATH_FIELD_NUMBER: builtins.int
    PLAYER_INFO_FIELD_NUMBER: builtins.int
    GAME_DURATION_LOOPS_FIELD_NUMBER: builtins.int
    GAME_DURATION_SECONDS_FIELD_NUMBER: builtins.int
    GAME_VERSION_FIELD_NUMBER: builtins.int
    DATA_VERSION_FIELD_NUMBER: builtins.int
    DATA_BUILD_FIELD_NUMBER: builtins.int
    BASE_BUILD_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    ERROR_DETAILS_FIELD_NUMBER: builtins.int
    map_name: builtins.str
    local_map_path: builtins.str
    game_duration_loops: builtins.int
    game_duration_seconds: builtins.float
    game_version: builtins.str
    data_version: builtins.str
    data_build: builtins.int
    base_build: builtins.int
    error: global___ResponseReplayInfo.Error.ValueType
    error_details: builtins.str
    @property
    def player_info(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___PlayerInfoExtra]: ...
    def __init__(
        self,
        *,
        map_name: builtins.str | None = ...,
        local_map_path: builtins.str | None = ...,
        player_info: collections.abc.Iterable[global___PlayerInfoExtra] | None = ...,
        game_duration_loops: builtins.int | None = ...,
        game_duration_seconds: builtins.float | None = ...,
        game_version: builtins.str | None = ...,
        data_version: builtins.str | None = ...,
        data_build: builtins.int | None = ...,
        base_build: builtins.int | None = ...,
        error: global___ResponseReplayInfo.Error.ValueType | None = ...,
        error_details: builtins.str | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "base_build",
            b"base_build",
            "data_build",
            b"data_build",
            "data_version",
            b"data_version",
            "error",
            b"error",
            "error_details",
            b"error_details",
            "game_duration_loops",
            b"game_duration_loops",
            "game_duration_seconds",
            b"game_duration_seconds",
            "game_version",
            b"game_version",
            "local_map_path",
            b"local_map_path",
            "map_name",
            b"map_name",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "base_build",
            b"base_build",
            "data_build",
            b"data_build",
            "data_version",
            b"data_version",
            "error",
            b"error",
            "error_details",
            b"error_details",
            "game_duration_loops",
            b"game_duration_loops",
            "game_duration_seconds",
            b"game_duration_seconds",
            "game_version",
            b"game_version",
            "local_map_path",
            b"local_map_path",
            "map_name",
            b"map_name",
            "player_info",
            b"player_info",
        ],
    ) -> None: ...

global___ResponseReplayInfo = ResponseReplayInfo

@typing.final
class RequestAvailableMaps(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___RequestAvailableMaps = RequestAvailableMaps

@typing.final
class ResponseAvailableMaps(google.protobuf.message.Message):
    """This will only contain locally cached BattleNet maps.
    To download all ladder maps, log in and queue into a ladder match.
    To download any other map, play a custom game on that map.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_MAP_PATHS_FIELD_NUMBER: builtins.int
    BATTLENET_MAP_NAMES_FIELD_NUMBER: builtins.int
    @property
    def local_map_paths(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """All the maps in the "Maps/" directory."""

    @property
    def battlenet_map_names(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """All the maps in the BattleNet cache."""

    def __init__(
        self,
        *,
        local_map_paths: collections.abc.Iterable[builtins.str] | None = ...,
        battlenet_map_names: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing.Literal["battlenet_map_names", b"battlenet_map_names", "local_map_paths", b"local_map_paths"]
    ) -> None: ...

global___ResponseAvailableMaps = ResponseAvailableMaps

@typing.final
class RequestSaveMap(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------
    Copies map data into the path specified.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MAP_PATH_FIELD_NUMBER: builtins.int
    MAP_DATA_FIELD_NUMBER: builtins.int
    map_path: builtins.str
    """Path the game process will write to, relative to the temp directory. (260 character max)"""
    map_data: builtins.bytes
    """Binary map data of a .SC2Map."""
    def __init__(self, *, map_path: builtins.str | None = ..., map_data: builtins.bytes | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["map_data", b"map_data", "map_path", b"map_path"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["map_data", b"map_data", "map_path", b"map_path"]) -> None: ...

global___RequestSaveMap = RequestSaveMap

@typing.final
class ResponseSaveMap(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Error:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ErrorEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ResponseSaveMap._Error.ValueType], builtins.type
    ):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        InvalidMapData: ResponseSaveMap._Error.ValueType  # 1

    class Error(_Error, metaclass=_ErrorEnumTypeWrapper): ...
    InvalidMapData: ResponseSaveMap.Error.ValueType  # 1

    ERROR_FIELD_NUMBER: builtins.int
    error: global___ResponseSaveMap.Error.ValueType
    def __init__(self, *, error: global___ResponseSaveMap.Error.ValueType | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error"]) -> None: ...

global___ResponseSaveMap = ResponseSaveMap

@typing.final
class RequestPing(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___RequestPing = RequestPing

@typing.final
class ResponsePing(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GAME_VERSION_FIELD_NUMBER: builtins.int
    DATA_VERSION_FIELD_NUMBER: builtins.int
    DATA_BUILD_FIELD_NUMBER: builtins.int
    BASE_BUILD_FIELD_NUMBER: builtins.int
    game_version: builtins.str
    data_version: builtins.str
    data_build: builtins.int
    base_build: builtins.int
    def __init__(
        self,
        *,
        game_version: builtins.str | None = ...,
        data_version: builtins.str | None = ...,
        data_build: builtins.int | None = ...,
        base_build: builtins.int | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "base_build",
            b"base_build",
            "data_build",
            b"data_build",
            "data_version",
            b"data_version",
            "game_version",
            b"game_version",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "base_build",
            b"base_build",
            "data_build",
            b"data_build",
            "data_version",
            b"data_version",
            "game_version",
            b"game_version",
        ],
    ) -> None: ...

global___ResponsePing = ResponsePing

@typing.final
class RequestDebug(google.protobuf.message.Message):
    """-----------------------------------------------------------------------------"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEBUG_FIELD_NUMBER: builtins.int
    @property
    def debug(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[s2clientprotocol.debug_pb2.DebugCommand]: ...
    def __init__(self, *, debug: collections.abc.Iterable[s2clientprotocol.debug_pb2.DebugCommand] | None = ...) -> None: ...
    def ClearField(self, field_name: typing.Literal["debug", b"debug"]) -> None: ...

global___RequestDebug = RequestDebug

@typing.final
class ResponseDebug(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(self) -> None: ...

global___ResponseDebug = ResponseDebug

@typing.final
class PlayerSetup(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    RACE_FIELD_NUMBER: builtins.int
    DIFFICULTY_FIELD_NUMBER: builtins.int
    PLAYER_NAME_FIELD_NUMBER: builtins.int
    AI_BUILD_FIELD_NUMBER: builtins.int
    type: global___PlayerType.ValueType
    race: s2clientprotocol.common_pb2.Race.ValueType
    """Only used for a computer player."""
    difficulty: global___Difficulty.ValueType
    player_name: builtins.str
    ai_build: global___AIBuild.ValueType
    def __init__(
        self,
        *,
        type: global___PlayerType.ValueType | None = ...,
        race: s2clientprotocol.common_pb2.Race.ValueType | None = ...,
        difficulty: global___Difficulty.ValueType | None = ...,
        player_name: builtins.str | None = ...,
        ai_build: global___AIBuild.ValueType | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "ai_build", b"ai_build", "difficulty", b"difficulty", "player_name", b"player_name", "race", b"race", "type", b"type"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "ai_build", b"ai_build", "difficulty", b"difficulty", "player_name", b"player_name", "race", b"race", "type", b"type"
        ],
    ) -> None: ...

global___PlayerSetup = PlayerSetup

@typing.final
class SpatialCameraSetup(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESOLUTION_FIELD_NUMBER: builtins.int
    MINIMAP_RESOLUTION_FIELD_NUMBER: builtins.int
    WIDTH_FIELD_NUMBER: builtins.int
    CROP_TO_PLAYABLE_AREA_FIELD_NUMBER: builtins.int
    ALLOW_CHEATING_LAYERS_FIELD_NUMBER: builtins.int
    width: builtins.float
    """Below are only relevant for feature layers.
    Set the screen camera width in world units.
    """
    crop_to_playable_area: builtins.bool
    """Crop minimap to the playable area."""
    allow_cheating_layers: builtins.bool
    """Return unit_type on the minimap, and potentially other cheating layers."""
    @property
    def resolution(self) -> s2clientprotocol.common_pb2.Size2DI: ...
    @property
    def minimap_resolution(self) -> s2clientprotocol.common_pb2.Size2DI: ...
    def __init__(
        self,
        *,
        resolution: s2clientprotocol.common_pb2.Size2DI | None = ...,
        minimap_resolution: s2clientprotocol.common_pb2.Size2DI | None = ...,
        width: builtins.float | None = ...,
        crop_to_playable_area: builtins.bool | None = ...,
        allow_cheating_layers: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "allow_cheating_layers",
            b"allow_cheating_layers",
            "crop_to_playable_area",
            b"crop_to_playable_area",
            "minimap_resolution",
            b"minimap_resolution",
            "resolution",
            b"resolution",
            "width",
            b"width",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "allow_cheating_layers",
            b"allow_cheating_layers",
            "crop_to_playable_area",
            b"crop_to_playable_area",
            "minimap_resolution",
            b"minimap_resolution",
            "resolution",
            b"resolution",
            "width",
            b"width",
        ],
    ) -> None: ...

global___SpatialCameraSetup = SpatialCameraSetup

@typing.final
class InterfaceOptions(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RAW_FIELD_NUMBER: builtins.int
    SCORE_FIELD_NUMBER: builtins.int
    FEATURE_LAYER_FIELD_NUMBER: builtins.int
    RENDER_FIELD_NUMBER: builtins.int
    SHOW_CLOAKED_FIELD_NUMBER: builtins.int
    SHOW_BURROWED_SHADOWS_FIELD_NUMBER: builtins.int
    SHOW_PLACEHOLDERS_FIELD_NUMBER: builtins.int
    RAW_AFFECTS_SELECTION_FIELD_NUMBER: builtins.int
    RAW_CROP_TO_PLAYABLE_AREA_FIELD_NUMBER: builtins.int
    raw: builtins.bool
    """Interface options"""
    score: builtins.bool
    show_cloaked: builtins.bool
    """By default cloaked units are completely hidden. This shows some details."""
    show_burrowed_shadows: builtins.bool
    """By default burrowed units are completely hidden. This shows some details for those that produce a shadow."""
    show_placeholders: builtins.bool
    """Return placeholder units (buildings to be constructed), both for raw and feature layers."""
    raw_affects_selection: builtins.bool
    """By default raw actions select, act and revert the selection. This is useful
    if you're playing simultaneously with the agent so it doesn't steal your
    selection. This inflates APM (due to deselect) and makes the actions hard
    to follow in a replay. Setting this to true will cause raw actions to do
    select, act, but not revert the selection.
    """
    raw_crop_to_playable_area: builtins.bool
    """Changes the coordinates in raw.proto to be relative to the playable area.
    The map_size and playable_area will be the diagonal of the real playable area.
    """
    @property
    def feature_layer(self) -> global___SpatialCameraSetup:
        """Omit to disable."""

    @property
    def render(self) -> global___SpatialCameraSetup:
        """Omit to disable."""

    def __init__(
        self,
        *,
        raw: builtins.bool | None = ...,
        score: builtins.bool | None = ...,
        feature_layer: global___SpatialCameraSetup | None = ...,
        render: global___SpatialCameraSetup | None = ...,
        show_cloaked: builtins.bool | None = ...,
        show_burrowed_shadows: builtins.bool | None = ...,
        show_placeholders: builtins.bool | None = ...,
        raw_affects_selection: builtins.bool | None = ...,
        raw_crop_to_playable_area: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "feature_layer",
            b"feature_layer",
            "raw",
            b"raw",
            "raw_affects_selection",
            b"raw_affects_selection",
            "raw_crop_to_playable_area",
            b"raw_crop_to_playable_area",
            "render",
            b"render",
            "score",
            b"score",
            "show_burrowed_shadows",
            b"show_burrowed_shadows",
            "show_cloaked",
            b"show_cloaked",
            "show_placeholders",
            b"show_placeholders",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "feature_layer",
            b"feature_layer",
            "raw",
            b"raw",
            "raw_affects_selection",
            b"raw_affects_selection",
            "raw_crop_to_playable_area",
            b"raw_crop_to_playable_area",
            "render",
            b"render",
            "score",
            b"score",
            "show_burrowed_shadows",
            b"show_burrowed_shadows",
            "show_cloaked",
            b"show_cloaked",
            "show_placeholders",
            b"show_placeholders",
        ],
    ) -> None: ...

global___InterfaceOptions = InterfaceOptions

@typing.final
class PlayerInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PLAYER_ID_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    RACE_REQUESTED_FIELD_NUMBER: builtins.int
    RACE_ACTUAL_FIELD_NUMBER: builtins.int
    DIFFICULTY_FIELD_NUMBER: builtins.int
    AI_BUILD_FIELD_NUMBER: builtins.int
    PLAYER_NAME_FIELD_NUMBER: builtins.int
    player_id: builtins.int
    """Identifier that will be used to reference this player.
    SC2 will always assign playerIds starting from 1 in standard Melee maps. This may not be true in custom maps.
    """
    type: global___PlayerType.ValueType
    race_requested: s2clientprotocol.common_pb2.Race.ValueType
    race_actual: s2clientprotocol.common_pb2.Race.ValueType
    """Only populated for your player or when watching replay"""
    difficulty: global___Difficulty.ValueType
    ai_build: global___AIBuild.ValueType
    player_name: builtins.str
    def __init__(
        self,
        *,
        player_id: builtins.int | None = ...,
        type: global___PlayerType.ValueType | None = ...,
        race_requested: s2clientprotocol.common_pb2.Race.ValueType | None = ...,
        race_actual: s2clientprotocol.common_pb2.Race.ValueType | None = ...,
        difficulty: global___Difficulty.ValueType | None = ...,
        ai_build: global___AIBuild.ValueType | None = ...,
        player_name: builtins.str | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "ai_build",
            b"ai_build",
            "difficulty",
            b"difficulty",
            "player_id",
            b"player_id",
            "player_name",
            b"player_name",
            "race_actual",
            b"race_actual",
            "race_requested",
            b"race_requested",
            "type",
            b"type",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "ai_build",
            b"ai_build",
            "difficulty",
            b"difficulty",
            "player_id",
            b"player_id",
            "player_name",
            b"player_name",
            "race_actual",
            b"race_actual",
            "race_requested",
            b"race_requested",
            "type",
            b"type",
        ],
    ) -> None: ...

global___PlayerInfo = PlayerInfo

@typing.final
class PlayerCommon(google.protobuf.message.Message):
    """
    During Game
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PLAYER_ID_FIELD_NUMBER: builtins.int
    MINERALS_FIELD_NUMBER: builtins.int
    VESPENE_FIELD_NUMBER: builtins.int
    FOOD_CAP_FIELD_NUMBER: builtins.int
    FOOD_USED_FIELD_NUMBER: builtins.int
    FOOD_ARMY_FIELD_NUMBER: builtins.int
    FOOD_WORKERS_FIELD_NUMBER: builtins.int
    IDLE_WORKER_COUNT_FIELD_NUMBER: builtins.int
    ARMY_COUNT_FIELD_NUMBER: builtins.int
    WARP_GATE_COUNT_FIELD_NUMBER: builtins.int
    LARVA_COUNT_FIELD_NUMBER: builtins.int
    player_id: builtins.int
    minerals: builtins.int
    vespene: builtins.int
    food_cap: builtins.int
    food_used: builtins.int
    food_army: builtins.int
    food_workers: builtins.int
    idle_worker_count: builtins.int
    army_count: builtins.int
    warp_gate_count: builtins.int
    larva_count: builtins.int
    def __init__(
        self,
        *,
        player_id: builtins.int | None = ...,
        minerals: builtins.int | None = ...,
        vespene: builtins.int | None = ...,
        food_cap: builtins.int | None = ...,
        food_used: builtins.int | None = ...,
        food_army: builtins.int | None = ...,
        food_workers: builtins.int | None = ...,
        idle_worker_count: builtins.int | None = ...,
        army_count: builtins.int | None = ...,
        warp_gate_count: builtins.int | None = ...,
        larva_count: builtins.int | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "army_count",
            b"army_count",
            "food_army",
            b"food_army",
            "food_cap",
            b"food_cap",
            "food_used",
            b"food_used",
            "food_workers",
            b"food_workers",
            "idle_worker_count",
            b"idle_worker_count",
            "larva_count",
            b"larva_count",
            "minerals",
            b"minerals",
            "player_id",
            b"player_id",
            "vespene",
            b"vespene",
            "warp_gate_count",
            b"warp_gate_count",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "army_count",
            b"army_count",
            "food_army",
            b"food_army",
            "food_cap",
            b"food_cap",
            "food_used",
            b"food_used",
            "food_workers",
            b"food_workers",
            "idle_worker_count",
            b"idle_worker_count",
            "larva_count",
            b"larva_count",
            "minerals",
            b"minerals",
            "player_id",
            b"player_id",
            "vespene",
            b"vespene",
            "warp_gate_count",
            b"warp_gate_count",
        ],
    ) -> None: ...

global___PlayerCommon = PlayerCommon

@typing.final
class Observation(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GAME_LOOP_FIELD_NUMBER: builtins.int
    PLAYER_COMMON_FIELD_NUMBER: builtins.int
    ALERTS_FIELD_NUMBER: builtins.int
    ABILITIES_FIELD_NUMBER: builtins.int
    SCORE_FIELD_NUMBER: builtins.int
    RAW_DATA_FIELD_NUMBER: builtins.int
    FEATURE_LAYER_DATA_FIELD_NUMBER: builtins.int
    RENDER_DATA_FIELD_NUMBER: builtins.int
    UI_DATA_FIELD_NUMBER: builtins.int
    game_loop: builtins.int
    @property
    def player_common(self) -> global___PlayerCommon: ...
    @property
    def alerts(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[global___Alert.ValueType]: ...
    @property
    def abilities(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[s2clientprotocol.common_pb2.AvailableAbility]:
        """Abilities available in the selection. Enabled if in this list, disabled otherwise."""

    @property
    def score(self) -> s2clientprotocol.score_pb2.Score: ...
    @property
    def raw_data(self) -> s2clientprotocol.raw_pb2.ObservationRaw:
        """Populated if Raw interface is enabled."""

    @property
    def feature_layer_data(self) -> s2clientprotocol.spatial_pb2.ObservationFeatureLayer:
        """Populated if Feature Layer interface is enabled."""

    @property
    def render_data(self) -> s2clientprotocol.spatial_pb2.ObservationRender:
        """Populated if Render interface is enabled."""

    @property
    def ui_data(self) -> s2clientprotocol.ui_pb2.ObservationUI:
        """Populated if Feature Layer or Render interface is enabled."""

    def __init__(
        self,
        *,
        game_loop: builtins.int | None = ...,
        player_common: global___PlayerCommon | None = ...,
        alerts: collections.abc.Iterable[global___Alert.ValueType] | None = ...,
        abilities: collections.abc.Iterable[s2clientprotocol.common_pb2.AvailableAbility] | None = ...,
        score: s2clientprotocol.score_pb2.Score | None = ...,
        raw_data: s2clientprotocol.raw_pb2.ObservationRaw | None = ...,
        feature_layer_data: s2clientprotocol.spatial_pb2.ObservationFeatureLayer | None = ...,
        render_data: s2clientprotocol.spatial_pb2.ObservationRender | None = ...,
        ui_data: s2clientprotocol.ui_pb2.ObservationUI | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "feature_layer_data",
            b"feature_layer_data",
            "game_loop",
            b"game_loop",
            "player_common",
            b"player_common",
            "raw_data",
            b"raw_data",
            "render_data",
            b"render_data",
            "score",
            b"score",
            "ui_data",
            b"ui_data",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "abilities",
            b"abilities",
            "alerts",
            b"alerts",
            "feature_layer_data",
            b"feature_layer_data",
            "game_loop",
            b"game_loop",
            "player_common",
            b"player_common",
            "raw_data",
            b"raw_data",
            "render_data",
            b"render_data",
            "score",
            b"score",
            "ui_data",
            b"ui_data",
        ],
    ) -> None: ...

global___Observation = Observation

@typing.final
class Action(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ACTION_RAW_FIELD_NUMBER: builtins.int
    ACTION_FEATURE_LAYER_FIELD_NUMBER: builtins.int
    ACTION_RENDER_FIELD_NUMBER: builtins.int
    ACTION_UI_FIELD_NUMBER: builtins.int
    ACTION_CHAT_FIELD_NUMBER: builtins.int
    GAME_LOOP_FIELD_NUMBER: builtins.int
    game_loop: builtins.int
    """Populated for actions in ResponseObservation. The game loop on which the action was executed."""
    @property
    def action_raw(self) -> s2clientprotocol.raw_pb2.ActionRaw:
        """Populated if Raw interface is enabled."""

    @property
    def action_feature_layer(self) -> s2clientprotocol.spatial_pb2.ActionSpatial:
        """Populated if Feature Layer interface is enabled."""

    @property
    def action_render(self) -> s2clientprotocol.spatial_pb2.ActionSpatial:
        """Not implemented. Populated if Render interface is enabled."""

    @property
    def action_ui(self) -> s2clientprotocol.ui_pb2.ActionUI:
        """Populated if Feature Layer or Render interface is enabled."""

    @property
    def action_chat(self) -> global___ActionChat:
        """Chat messages as a player typing into the chat channel."""

    def __init__(
        self,
        *,
        action_raw: s2clientprotocol.raw_pb2.ActionRaw | None = ...,
        action_feature_layer: s2clientprotocol.spatial_pb2.ActionSpatial | None = ...,
        action_render: s2clientprotocol.spatial_pb2.ActionSpatial | None = ...,
        action_ui: s2clientprotocol.ui_pb2.ActionUI | None = ...,
        action_chat: global___ActionChat | None = ...,
        game_loop: builtins.int | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "action_chat",
            b"action_chat",
            "action_feature_layer",
            b"action_feature_layer",
            "action_raw",
            b"action_raw",
            "action_render",
            b"action_render",
            "action_ui",
            b"action_ui",
            "game_loop",
            b"game_loop",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "action_chat",
            b"action_chat",
            "action_feature_layer",
            b"action_feature_layer",
            "action_raw",
            b"action_raw",
            "action_render",
            b"action_render",
            "action_ui",
            b"action_ui",
            "game_loop",
            b"game_loop",
        ],
    ) -> None: ...

global___Action = Action

@typing.final
class ActionChat(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Channel:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ChannelEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ActionChat._Channel.ValueType], builtins.type
    ):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        Broadcast: ActionChat._Channel.ValueType  # 1
        Team: ActionChat._Channel.ValueType  # 2

    class Channel(_Channel, metaclass=_ChannelEnumTypeWrapper): ...
    Broadcast: ActionChat.Channel.ValueType  # 1
    Team: ActionChat.Channel.ValueType  # 2

    CHANNEL_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    channel: global___ActionChat.Channel.ValueType
    message: builtins.str
    def __init__(
        self, *, channel: global___ActionChat.Channel.ValueType | None = ..., message: builtins.str | None = ...
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["channel", b"channel", "message", b"message"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["channel", b"channel", "message", b"message"]) -> None: ...

global___ActionChat = ActionChat

@typing.final
class ActionError(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    UNIT_TAG_FIELD_NUMBER: builtins.int
    ABILITY_ID_FIELD_NUMBER: builtins.int
    RESULT_FIELD_NUMBER: builtins.int
    unit_tag: builtins.int
    """Only populated when using raw interface."""
    ability_id: builtins.int
    result: s2clientprotocol.error_pb2.ActionResult.ValueType
    def __init__(
        self,
        *,
        unit_tag: builtins.int | None = ...,
        ability_id: builtins.int | None = ...,
        result: s2clientprotocol.error_pb2.ActionResult.ValueType | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing.Literal["ability_id", b"ability_id", "result", b"result", "unit_tag", b"unit_tag"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing.Literal["ability_id", b"ability_id", "result", b"result", "unit_tag", b"unit_tag"]
    ) -> None: ...

global___ActionError = ActionError

@typing.final
class ObserverAction(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PLAYER_PERSPECTIVE_FIELD_NUMBER: builtins.int
    CAMERA_MOVE_FIELD_NUMBER: builtins.int
    CAMERA_FOLLOW_PLAYER_FIELD_NUMBER: builtins.int
    CAMERA_FOLLOW_UNITS_FIELD_NUMBER: builtins.int
    @property
    def player_perspective(self) -> global___ActionObserverPlayerPerspective:
        """Not implemented"""

    @property
    def camera_move(self) -> global___ActionObserverCameraMove: ...
    @property
    def camera_follow_player(self) -> global___ActionObserverCameraFollowPlayer: ...
    @property
    def camera_follow_units(self) -> global___ActionObserverCameraFollowUnits:
        """Not implemented"""

    def __init__(
        self,
        *,
        player_perspective: global___ActionObserverPlayerPerspective | None = ...,
        camera_move: global___ActionObserverCameraMove | None = ...,
        camera_follow_player: global___ActionObserverCameraFollowPlayer | None = ...,
        camera_follow_units: global___ActionObserverCameraFollowUnits | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing.Literal[
            "action",
            b"action",
            "camera_follow_player",
            b"camera_follow_player",
            "camera_follow_units",
            b"camera_follow_units",
            "camera_move",
            b"camera_move",
            "player_perspective",
            b"player_perspective",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing.Literal[
            "action",
            b"action",
            "camera_follow_player",
            b"camera_follow_player",
            "camera_follow_units",
            b"camera_follow_units",
            "camera_move",
            b"camera_move",
            "player_perspective",
            b"player_perspective",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing.Literal["action", b"action"]
    ) -> typing.Literal["player_perspective", "camera_move", "camera_follow_player", "camera_follow_units"] | None: ...

global___ObserverAction = ObserverAction

@typing.final
class ActionObserverPlayerPerspective(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PLAYER_ID_FIELD_NUMBER: builtins.int
    player_id: builtins.int
    """0 to observe "Everyone" """
    def __init__(self, *, player_id: builtins.int | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["player_id", b"player_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["player_id", b"player_id"]) -> None: ...

global___ActionObserverPlayerPerspective = ActionObserverPlayerPerspective

@typing.final
class ActionObserverCameraMove(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WORLD_POS_FIELD_NUMBER: builtins.int
    DISTANCE_FIELD_NUMBER: builtins.int
    distance: builtins.float
    """Distance between camera and terrain. Larger value zooms out camera.
    Defaults to standard camera distance if set to 0.
    """
    @property
    def world_pos(self) -> s2clientprotocol.common_pb2.Point2D: ...
    def __init__(
        self, *, world_pos: s2clientprotocol.common_pb2.Point2D | None = ..., distance: builtins.float | None = ...
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["distance", b"distance", "world_pos", b"world_pos"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["distance", b"distance", "world_pos", b"world_pos"]) -> None: ...

global___ActionObserverCameraMove = ActionObserverCameraMove

@typing.final
class ActionObserverCameraFollowPlayer(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PLAYER_ID_FIELD_NUMBER: builtins.int
    player_id: builtins.int
    """Not implemented. Value must be [1, 15]"""
    def __init__(self, *, player_id: builtins.int | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["player_id", b"player_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["player_id", b"player_id"]) -> None: ...

global___ActionObserverCameraFollowPlayer = ActionObserverCameraFollowPlayer

@typing.final
class ActionObserverCameraFollowUnits(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    UNIT_TAGS_FIELD_NUMBER: builtins.int
    @property
    def unit_tags(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(self, *, unit_tags: collections.abc.Iterable[builtins.int] | None = ...) -> None: ...
    def ClearField(self, field_name: typing.Literal["unit_tags", b"unit_tags"]) -> None: ...

global___ActionObserverCameraFollowUnits = ActionObserverCameraFollowUnits

@typing.final
class PlayerResult(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PLAYER_ID_FIELD_NUMBER: builtins.int
    RESULT_FIELD_NUMBER: builtins.int
    player_id: builtins.int
    result: global___Result.ValueType
    def __init__(self, *, player_id: builtins.int | None = ..., result: global___Result.ValueType | None = ...) -> None: ...
    def HasField(self, field_name: typing.Literal["player_id", b"player_id", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["player_id", b"player_id", "result", b"result"]) -> None: ...

global___PlayerResult = PlayerResult
