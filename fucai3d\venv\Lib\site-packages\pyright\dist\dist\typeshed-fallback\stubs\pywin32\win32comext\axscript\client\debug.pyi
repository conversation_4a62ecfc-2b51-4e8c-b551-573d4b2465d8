from _typeshed import Incomplete

from win32comext.axdebug import gateways
from win32comext.axdebug.codecontainer import SourceCodeContainer

debuggingTrace: int

def trace(*args) -> None: ...

class DebugManager:
    scriptEngine: Incomplete
    adb: Incomplete
    rootNode: Incomplete
    debugApplication: Incomplete
    ccProvider: Incomplete
    scriptSiteDebug: Incomplete
    activeScriptDebug: Incomplete
    codeContainers: Incomplete
    def __init__(self, scriptEngine) -> None: ...
    def Close(self) -> None: ...
    def IsAnyHost(self): ...
    def IsSimpleHost(self): ...
    def HandleRuntimeError(self): ...
    def OnEnterScript(self) -> None: ...
    def OnLeaveScript(self) -> None: ...
    def AddScriptBlock(self, codeBlock) -> None: ...

class DebugCodeBlockContainer(SourceCodeContainer):
    codeBlock: Incomplete
    def __init__(self, codeBlock, site) -> None: ...
    def GetName(self, dnt): ...

class EnumDebugCodeContexts(gateways.EnumDebugCodeContexts): ...

class ActiveScriptDebug:
    debugMgr: Incomplete
    scriptSiteDebug: Incomplete
    codeContainers: Incomplete
    def __init__(self, debugMgr, codeContainers) -> None: ...
    def GetScriptTextAttributes(self, code, delim, flags): ...
    def GetScriptletTextAttributes(self, code, delim, flags): ...
    def EnumCodeContextsOfPosition(self, context, charOffset, numChars): ...
