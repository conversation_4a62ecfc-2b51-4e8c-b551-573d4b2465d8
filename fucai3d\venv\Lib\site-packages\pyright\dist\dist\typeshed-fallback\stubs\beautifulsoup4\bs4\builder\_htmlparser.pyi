from html.parser import <PERSON><PERSON><PERSON>ars<PERSON>
from typing import Any

from bs4.builder import HTMLTreeBuilder

class BeautifulSoupHTMLParser(HTMLParser):
    IGNORE: str
    REPLACE: str
    on_duplicate_attribute: Any
    already_closed_empty_element: Any
    def __init__(self, *args, **kwargs) -> None: ...
    def handle_startendtag(self, name, attrs) -> None: ...
    def handle_starttag(self, name, attrs, handle_empty_element: bool = True) -> None: ...
    def handle_endtag(self, name, check_already_closed: bool = True) -> None: ...
    def handle_data(self, data) -> None: ...
    def handle_charref(self, name) -> None: ...
    def handle_entityref(self, name) -> None: ...
    def handle_comment(self, data) -> None: ...
    def handle_decl(self, data) -> None: ...
    def unknown_decl(self, data) -> None: ...
    def handle_pi(self, data) -> None: ...

class HTMLParserTreeBuilder(HTMLTreeBuilder):
    is_xml: bool
    picklable: bool
    NAME: Any
    features: Any
    TRACKS_LINE_NUMBERS: bool
    parser_args: Any
    def __init__(self, parser_args=None, parser_kwargs=None, **kwargs) -> None: ...
    def prepare_markup(
        self, markup, user_specified_encoding=None, document_declared_encoding=None, exclude_encodings=None
    ) -> None: ...
    def feed(self, markup) -> None: ...
