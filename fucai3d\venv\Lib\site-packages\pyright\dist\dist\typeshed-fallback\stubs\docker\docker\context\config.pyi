METAFILE: str

def get_current_context_name() -> str: ...
def write_context_name_to_docker_config(name: str | None = None) -> Exception | None: ...
def get_context_id(name: str) -> str: ...
def get_context_dir() -> str: ...
def get_meta_dir(name: str | None = None) -> str: ...
def get_meta_file(name: str) -> str: ...
def get_tls_dir(name: str | None = None, endpoint: str = "") -> str: ...
def get_context_host(path: str | None = None, tls: bool = False) -> str: ...
