from typing import Literal, overload

from ._typing import SupportsRead, SupportsWrite
from .geometry.base import BaseGeometry
from .lib import Geometry

def loads(data: str | bytes, hex: bool = False) -> BaseGeometry: ...
def load(fp: SupportsRead[str] | SupportsRead[bytes], hex: bool = False) -> BaseGeometry: ...
@overload
def dumps(ob: Geometry, hex: Literal[False] = False, srid: int | None = None, **kw) -> bytes: ...
@overload
def dumps(ob: Geometry, hex: Literal[True], srid: int | None = None, **kw) -> str: ...
@overload
def dump(ob: Geometry, fp: SupportsWrite[bytes], hex: Literal[False] = False, *, srid: int | None = None, **kw) -> None: ...
@overload
def dump(ob: Geometry, fp: SupportsWrite[str], hex: Literal[True], *, srid: int | None = None, **kw) -> None: ...
