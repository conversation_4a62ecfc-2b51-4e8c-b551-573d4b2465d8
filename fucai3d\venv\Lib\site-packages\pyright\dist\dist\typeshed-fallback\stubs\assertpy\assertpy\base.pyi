from typing import Any
from typing_extensions import Self, TypeAlias

__tracebackhide__: bool

_IncludeIgnore: TypeAlias = str | list[str] | list[tuple[str, ...]] | None

class BaseMixin:
    description: str
    def described_as(self, description: str) -> Self: ...
    def is_equal_to(self, other: Any, *, include: _IncludeIgnore = None, ignore: _IncludeIgnore = None) -> Self: ...
    def is_not_equal_to(self, other: Any) -> Self: ...
    def is_same_as(self, other: Any) -> Self: ...
    def is_not_same_as(self, other: Any) -> Self: ...
    def is_true(self) -> Self: ...
    def is_false(self) -> Self: ...
    def is_none(self) -> Self: ...
    def is_not_none(self) -> Self: ...
    def is_type_of(self, some_type: type) -> Self: ...
    def is_instance_of(self, some_class: type) -> Self: ...
    def is_length(self, length: int) -> Self: ...
