from _typeshed import Incomplete
from collections.abc import Sequence

from networkx.utils.backends import _dispatchable

__all__ = ["combinatorial_embedding_to_pos"]

@_dispatchable
def combinatorial_embedding_to_pos(embedding, fully_triangulate: bool = False) -> dict[Incomplete, Incomplete]: ...
def set_position(parent, tree, remaining_nodes, delta_x, y_coordinate, pos): ...
def get_canonical_ordering(embedding, outer_face: Sequence[Incomplete]) -> list[Incomplete]: ...
def triangulate_face(embedding, v1, v2): ...
def triangulate_embedding(embedding, fully_triangulate: bool = True): ...
def make_bi_connected(
    embedding, starting_node, outgoing_node, edges_counted: set[tuple[Incomplete, Incomplete]]
) -> list[Incomplete]: ...
