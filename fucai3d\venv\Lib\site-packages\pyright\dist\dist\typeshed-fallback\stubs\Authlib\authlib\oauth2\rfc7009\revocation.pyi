from authlib.oauth2.rfc6749 import TokenEndpoint

class RevocationEndpoint(TokenEndpoint):
    ENDPOINT_NAME: str
    def authenticate_token(self, request, client): ...
    def check_params(self, request, client) -> None: ...
    def create_endpoint_response(self, request): ...
    def query_token(self, token_string, token_type_hint) -> None: ...
    def revoke_token(self, token, request) -> None: ...
