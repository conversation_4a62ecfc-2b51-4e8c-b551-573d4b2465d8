from _typeshed import FileD<PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>
from logging import Logger
from typing import Final

DOCKER_CONFIG_FILENAME: Final[str]
LEGACY_DOCKER_CONFIG_FILENAME: Final[str]
log: Logger

def find_config_file(config_path: FileDescriptorOrPath | None = None) -> FileDescriptorOrPath | None: ...
def config_path_from_environment() -> str | None: ...
def home_dir() -> str: ...
def load_general_config(config_path: FileDescriptorOrPath | None = None): ...
