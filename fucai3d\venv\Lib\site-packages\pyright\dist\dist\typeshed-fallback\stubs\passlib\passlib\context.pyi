from _typeshed import <PERSON>r<PERSON>r<PERSON><PERSON>sPath, SupportsItems
from typing import Any
from typing_extensions import Self

class CryptPolicy:
    @classmethod
    def from_path(cls, path, section: str = "passlib", encoding: str = "utf-8"): ...
    @classmethod
    def from_string(cls, source, section: str = "passlib", encoding: str = "utf-8"): ...
    @classmethod
    def from_source(cls, source, _warn: bool = True): ...
    @classmethod
    def from_sources(cls, sources, _warn: bool = True): ...
    def replace(self, *args, **kwds): ...
    def __init__(self, *args, **kwds) -> None: ...
    def has_schemes(self): ...
    def iter_handlers(self): ...
    def schemes(self, resolve: bool = False): ...
    def get_handler(self, name=None, category=None, required: bool = False): ...
    def get_min_verify_time(self, category=None): ...
    def get_options(self, name, category=None): ...
    def handler_is_deprecated(self, name, category=None): ...
    def iter_config(self, ini: bool = False, resolve: bool = False): ...
    def to_dict(self, resolve: bool = False): ...
    def to_file(self, stream, section: str = "passlib") -> None: ...
    def to_string(self, section: str = "passlib", encoding=None): ...

class CryptContext:
    @classmethod
    def from_string(cls, source: str | bytes, section: str = "passlib", encoding: str = "utf-8") -> Self: ...
    @classmethod
    def from_path(cls, path: StrOrBytesPath, section: str = "passlib", encoding: str = "utf-8") -> Self: ...
    def copy(self, **kwds: Any) -> CryptContext: ...
    def using(self, **kwds: Any) -> CryptContext: ...
    def replace(self, **kwds): ...
    def __init__(self, schemes=None, policy=..., _autoload: bool = True, **kwds) -> None: ...
    policy: CryptPolicy
    def load_path(
        self, path: StrOrBytesPath, update: bool = False, section: str = "passlib", encoding: str = "utf-8"
    ) -> None: ...
    def load(
        self,
        source: str | bytes | SupportsItems[str, Any] | CryptContext,
        update: bool = False,
        section: str = "passlib",
        encoding: str = "utf-8",
    ) -> None: ...
    def update(self, *args: Any, **kwds: Any) -> None: ...
    def schemes(self, resolve: bool = False, category=None, unconfigured: bool = False): ...
    def default_scheme(self, category=None, resolve: bool = False, unconfigured: bool = False): ...
    def handler(self, scheme=None, category=None, unconfigured: bool = False): ...
    @property
    def context_kwds(self): ...
    def to_dict(self, resolve: bool = False) -> dict[str, Any]: ...
    def to_string(self, section: str = "passlib") -> str: ...
    mvt_estimate_max_samples: int
    mvt_estimate_min_samples: int
    mvt_estimate_max_time: int
    mvt_estimate_resolution: float
    harden_verify: Any
    min_verify_time: int
    def reset_min_verify_time(self) -> None: ...
    def needs_update(
        self, hash: str | bytes, scheme: str | None = None, category: str | None = None, secret: str | bytes | None = None
    ) -> bool: ...
    def hash_needs_update(self, hash, scheme=None, category=None): ...
    def genconfig(self, scheme=None, category=None, **settings): ...
    def genhash(self, secret, config, scheme=None, category=None, **kwds): ...
    def identify(self, hash, category=None, resolve: bool = False, required: bool = False, unconfigured: bool = False): ...
    def hash(self, secret: str | bytes, scheme: str | None = None, category: str | None = None, **kwds: Any) -> str: ...
    def encrypt(self, *args, **kwds): ...
    def verify(
        self, secret: str | bytes, hash: str | bytes | None, scheme: str | None = None, category: str | None = None, **kwds: Any
    ) -> bool: ...
    def verify_and_update(
        self, secret: str | bytes, hash: str | bytes | None, scheme: str | None = None, category: str | None = None, **kwds: Any
    ) -> tuple[bool, str | None]: ...
    def dummy_verify(self, elapsed: int = 0): ...
    def is_enabled(self, hash: str | bytes) -> bool: ...
    def disable(self, hash: str | bytes | None = None) -> str: ...
    def enable(self, hash: str | bytes) -> str: ...

class LazyCryptContext(CryptContext):
    def __init__(self, schemes=None, **kwds) -> None: ...
    def __getattribute__(self, attr: str) -> Any: ...

__all__ = ["CryptContext", "LazyCryptContext", "CryptPolicy"]
