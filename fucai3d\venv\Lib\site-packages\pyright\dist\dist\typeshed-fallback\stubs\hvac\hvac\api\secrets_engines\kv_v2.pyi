from hvac.api.vault_api_base import VaultApiBase

DEFAULT_MOUNT_POINT: str

class KvV2(VaultApiBase):
    def configure(self, max_versions: int = 10, cas_required=None, delete_version_after: str = "0s", mount_point="secret"): ...
    def read_configuration(self, mount_point="secret"): ...
    def read_secret(self, path, mount_point="secret", raise_on_deleted_version=None): ...
    def read_secret_version(self, path, version=None, mount_point="secret", raise_on_deleted_version=None): ...
    def create_or_update_secret(self, path, secret, cas=None, mount_point="secret"): ...
    def patch(self, path, secret, mount_point="secret"): ...
    def delete_latest_version_of_secret(self, path, mount_point="secret"): ...
    def delete_secret_versions(self, path, versions, mount_point="secret"): ...
    def undelete_secret_versions(self, path, versions, mount_point="secret"): ...
    def destroy_secret_versions(self, path, versions, mount_point="secret"): ...
    def list_secrets(self, path, mount_point="secret"): ...
    def read_secret_metadata(self, path, mount_point="secret"): ...
    def update_metadata(
        self,
        path,
        max_versions=None,
        cas_required=None,
        delete_version_after: str = "0s",
        mount_point="secret",
        custom_metadata=None,
    ): ...
    def delete_metadata_and_all_versions(self, path, mount_point="secret"): ...
