def fingerprint128(a: str, /) -> tuple[int, int]: ...
def fingerprint32(a: str, /) -> int: ...
def fingerprint64(a: str, /) -> int: ...
def hash128(a: str, /) -> tuple[int, int]: ...
def hash128withseed(a: str, seed_low: int, seed_high: int, /) -> tuple[int, int]: ...
def hash32(a: str, /) -> int: ...
def hash32withseed(a: str, seed: int, /) -> int: ...
def hash64(a: str, /) -> int: ...
def hash64withseed(a: str, seed: int, /) -> int: ...
