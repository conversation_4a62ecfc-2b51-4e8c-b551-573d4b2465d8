from _typeshed import Incomplete
from typing import Any, ClassVar

class BaseClaims(dict[str, Any]):  # dict values are key-dependent
    REGISTERED_CLAIMS: ClassVar[list[str]]
    header: Incomplete
    options: Incomplete
    params: Incomplete
    def __init__(self, payload, header, options=None, params=None) -> None: ...
    # TODO: Adds an attribute for each key in REGISTERED_CLAIMS
    def __getattr__(self, key: str): ...
    def get_registered_claims(self) -> dict[str, Incomplete]: ...

class JWTClaims(BaseClaims):
    def validate(self, now=None, leeway: int = 0) -> None: ...
    def validate_iss(self) -> None: ...
    def validate_sub(self) -> None: ...
    def validate_aud(self) -> None: ...
    def validate_exp(self, now, leeway) -> None: ...
    def validate_nbf(self, now, leeway) -> None: ...
    def validate_iat(self, now, leeway) -> None: ...
    def validate_jti(self) -> None: ...
