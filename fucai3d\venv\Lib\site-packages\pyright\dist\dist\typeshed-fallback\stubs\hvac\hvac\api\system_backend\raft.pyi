from typing import Any

from hvac.api.system_backend.system_backend_mixin import SystemBackendMixin
from requests import Response

class Raft(SystemBackendMixin):
    def join_raft_cluster(
        self, leader_api_addr, retry: bool = False, leader_ca_cert=None, leader_client_cert=None, leader_client_key=None
    ): ...
    def read_raft_config(self): ...
    def remove_raft_node(self, server_id): ...
    def take_raft_snapshot(self): ...
    def restore_raft_snapshot(self, snapshot): ...
    def force_restore_raft_snapshot(self, snapshot): ...
    def read_raft_auto_snapshot_status(self, name: str) -> Response: ...
    def read_raft_auto_snapshot_config(self, name: str) -> Response: ...
    def list_raft_auto_snapshot_configs(self) -> Response: ...
    def create_or_update_raft_auto_snapshot_config(
        self, name: str, interval: str, storage_type: str, retain: int = 1, **kwargs: Any
    ) -> Response: ...
    def delete_raft_auto_snapshot_config(self, name: str) -> Response: ...
