from _typeshed import Incomplete

from antlr4.atn.ATN import ATN as ATN
from antlr4.atn.ATNConfig import ATNConfig as ATNConfig
from antlr4.atn.SemanticContext import SemanticContext as SemanticContext
from antlr4.error.Errors import (
    IllegalStateException as IllegalStateException,
    UnsupportedOperationException as UnsupportedOperationException,
)
from antlr4.PredictionContext import merge as merge
from antlr4.Utils import str_list as str_list

ATNSimulator: Incomplete

class ATNConfigSet:
    configLookup: Incomplete
    fullCtx: Incomplete
    readonly: bool
    configs: Incomplete
    uniqueAlt: int
    conflictingAlts: Incomplete
    hasSemanticContext: bool
    dipsIntoOuterContext: bool
    cachedHashCode: int
    def __init__(self, fullCtx: bool = True) -> None: ...
    def __iter__(self): ...
    def add(self, config: ATNConfig, mergeCache=None): ...
    def getOrAdd(self, config: ATNConfig): ...
    def getStates(self): ...
    def getPredicates(self): ...
    def get(self, i: int): ...
    def optimizeConfigs(self, interpreter: ATNSimulator): ...
    def addAll(self, coll: list[Incomplete]): ...
    def __eq__(self, other): ...
    def __hash__(self): ...
    def hashConfigs(self): ...
    def __len__(self) -> int: ...
    def isEmpty(self): ...
    def __contains__(self, config) -> bool: ...
    def clear(self) -> None: ...
    def setReadonly(self, readonly: bool): ...

class OrderedATNConfigSet(ATNConfigSet):
    def __init__(self) -> None: ...
