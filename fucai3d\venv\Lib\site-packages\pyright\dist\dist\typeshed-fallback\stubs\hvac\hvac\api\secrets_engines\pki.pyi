from hvac.api.vault_api_base import VaultApiBase

DEFAULT_MOUNT_POINT: str

class Pki(VaultApiBase):
    def read_ca_certificate(self, mount_point="pki"): ...
    def read_ca_certificate_chain(self, mount_point="pki"): ...
    def read_certificate(self, serial, mount_point="pki"): ...
    def list_certificates(self, mount_point="pki"): ...
    def submit_ca_information(self, pem_bundle, mount_point="pki"): ...
    def read_crl_configuration(self, mount_point="pki"): ...
    def set_crl_configuration(self, expiry=None, disable=None, extra_params=None, mount_point="pki"): ...
    def read_urls(self, mount_point="pki"): ...
    def set_urls(self, params, mount_point="pki"): ...
    def read_crl(self, mount_point="pki"): ...
    def rotate_crl(self, mount_point="pki"): ...
    def generate_intermediate(self, type, common_name, extra_params=None, mount_point="pki", wrap_ttl=None): ...
    def set_signed_intermediate(self, certificate, mount_point="pki"): ...
    def generate_certificate(self, name, common_name, extra_params=None, mount_point="pki", wrap_ttl=None): ...
    def revoke_certificate(self, serial_number, mount_point="pki"): ...
    def create_or_update_role(self, name, extra_params=None, mount_point="pki"): ...
    def read_role(self, name, mount_point="pki"): ...
    def list_roles(self, mount_point="pki"): ...
    def delete_role(self, name, mount_point="pki"): ...
    def generate_root(self, type, common_name, extra_params=None, mount_point="pki", wrap_ttl=None): ...
    def delete_root(self, mount_point="pki"): ...
    def sign_intermediate(self, csr, common_name, extra_params=None, mount_point="pki"): ...
    def sign_self_issued(self, certificate, mount_point="pki"): ...
    def sign_certificate(self, name, csr, common_name, extra_params=None, mount_point="pki"): ...
    def sign_verbatim(self, csr, name: bool = False, extra_params=None, mount_point="pki"): ...
    def tidy(self, extra_params=None, mount_point="pki"): ...
    def read_issuer(self, issuer_ref, mount_point="pki"): ...
    def list_issuers(self, mount_point="pki"): ...
    def update_issuer(self, issuer_ref, extra_params=None, mount_point="pki"): ...
    def revoke_issuer(self, issuer_ref, mount_point="pki"): ...
    def delete_issuer(self, issuer_ref, mount_point="pki"): ...
