import _win32typing
from win32.lib.pywintypes import error as error

def CreateTransaction(
    TransactionAttributes: _win32typing.PySECURITY_ATTRIBUTES | None = ...,
    UOW: _win32typing.PyIID | None = ...,
    CreateOptions: int = ...,
    IsolationLevel: int = ...,
    IsolationFlags: int = ...,
    Timeout: int = ...,
    Description: str | None = ...,
) -> int: ...
def RollbackTransaction(TransactionHandle: int) -> None: ...
def RollbackTransactionAsync(TransactionHandle: int) -> None: ...
def CommitTransaction(TransactionHandle: int) -> None: ...
def CommitTransactionAsync(TransactionHandle: int) -> None: ...
def GetTransactionId(TransactionHandle: int) -> _win32typing.PyIID: ...
def OpenTransaction(DesiredAccess, TransactionId: _win32typing.PyIID) -> int: ...
