SIGNATURE_HMAC_SHA1: str
SIGNATURE_RSA_SHA1: str
SIGNATURE_PLAINTEXT: str
SIGNATURE_TYPE_HEADER: str
SIGNATURE_TYPE_QUERY: str
SIGNATURE_TYPE_BODY: str

def construct_base_string(method, uri, params, host=None): ...
def normalize_base_string_uri(uri, host=None): ...
def normalize_parameters(params): ...
def generate_signature_base_string(request): ...
def hmac_sha1_signature(base_string, client_secret, token_secret): ...
def rsa_sha1_signature(base_string, rsa_private_key): ...
def plaintext_signature(client_secret, token_secret): ...
def sign_hmac_sha1(client, request): ...
def sign_rsa_sha1(client, request): ...
def sign_plaintext(client, request): ...
def verify_hmac_sha1(request): ...
def verify_rsa_sha1(request): ...
def verify_plaintext(request): ...
