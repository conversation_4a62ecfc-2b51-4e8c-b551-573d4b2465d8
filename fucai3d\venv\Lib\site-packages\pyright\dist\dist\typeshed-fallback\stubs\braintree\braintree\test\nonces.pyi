from typing import Final

class Nonces:
    AbstractTransactable: Final = "fake-abstract-transactable-nonce"
    AmexExpressCheckoutCard: Final = "fake-amex-express-checkout-nonce"
    AndroidPayCard: Final = "fake-android-pay-nonce"
    AndroidPayCardAmEx: Final = "fake-android-pay-amex-nonce"
    AndroidPayCardDiscover: Final = "fake-android-pay-discover-nonce"
    AndroidPayCardMasterCard: Final = "fake-android-pay-mastercard-nonce"
    AndroidPayCardVisa: Final = "fake-android-pay-visa-nonce"
    ApplePayAmEx: Final = "fake-apple-pay-amex-nonce"
    ApplePayMasterCard: Final = "fake-apple-pay-mastercard-nonce"
    ApplePayMpan: Final = "fake-apple-pay-mpan-nonce"
    ApplePayVisa: Final = "fake-apple-pay-visa-nonce"
    Consumed: Final = "fake-consumed-nonce"
    Europe: Final = "fake-europe-bank-account-nonce"
    GatewayRejectedFraud: Final = "fake-gateway-rejected-fraud-nonce"
    GatewayRejectedRiskThreshold: Final = "fake-gateway-rejected-risk-thresholds-nonce"
    LocalPayment: Final = "fake-local-payment-method-nonce"
    LuhnInvalid: Final = "fake-luhn-invalid-nonce"
    MasterpassAmEx: Final = "fake-masterpass-amex-nonce"
    MasterpassDiscover: Final = "fake-masterpass-discover-nonce"
    MasterpassMasterCard: Final = "fake-masterpass-mastercard-nonce"
    MasterpassVisa: Final = "fake-masterpass-visa-nonce"
    PayPalBillingAgreement: Final = "fake-paypal-billing-agreement-nonce"
    PayPalFuturePayment: Final = "fake-paypal-future-nonce"
    PayPalFuturePaymentRefreshToken: Final = "fake-paypal-future-refresh-token-nonce"
    PayPalOneTimePayment: Final = "fake-paypal-one-time-nonce"
    ProcessorDeclinedAmEx: Final = "fake-processor-declined-amex-nonce"
    ProcessorDeclinedDiscover: Final = "fake-processor-declined-discover-nonce"
    ProcessorDeclinedMasterCard: Final = "fake-processor-declined-mastercard-nonce"
    ProcessorDeclinedVisa: Final = "fake-processor-declined-visa-nonce"
    ProcessorFailureJCB: Final = "fake-processor-failure-jcb-nonce"
    SEPA: Final = "fake-sepa-bank-account-nonce"
    MetaCheckoutCard: Final = "fake-meta-checkout-card-nonce"
    MetaCheckoutToken: Final = "fake-meta-checkout-token-nonce"
    SamsungPayAmex: Final = "tokensam_fake_american_express"
    SamsungPayDiscover: Final = "tokensam_fake_american_express"
    SamsungPayMasterCard: Final = "tokensam_fake_mastercard"
    SamsungPayVisa: Final = "tokensam_fake_visa"
    SepaDirectDebit: Final = "fake-sepa-direct-debit-nonce"
    ThreeDSecureTwoVisaErrorOnLookup: Final = "fake-three-d-secure-two-visa-error-on-lookup-nonce"
    ThreeDSecureTwoVisaSuccessfulFrictionlessAuthentication: Final[str]
    ThreeDSecureTwoVisaSuccessfulStepUpAuthentication: Final[str]
    ThreeDSecureTwoVisaTimeoutOnLookup: Final[str]
    ThreeDSecureVisaAttemptsNonParticipating: Final[str]
    ThreeDSecureVisaAuthenticationUnavailable: Final[str]
    ThreeDSecureVisaBypassedAuthentication: Final[str]
    ThreeDSecureVisaFailedAuthentication: Final[str]
    ThreeDSecureVisaFailedSignature: Final = "fake-three-d-secure-visa-failed-signature-nonce"
    ThreeDSecureVisaFullAuthentication: Final = "fake-three-d-secure-visa-full-authentication-nonce"
    ThreeDSecureVisaLookupTimeout: Final = "fake-three-d-secure-visa-lookup-timeout-nonce"
    ThreeDSecureVisaMPIAuthenticateError: Final[str]
    ThreeDSecureVisaMPILookupError: Final = "fake-three-d-secure-visa-mpi-lookup-error-nonce"
    ThreeDSecureVisaNoteEnrolled: Final = "fake-three-d-secure-visa-not-enrolled-nonce"
    ThreeDSecureVisaUnavailable: Final = "fake-three-d-secure-visa-unavailable-nonce"
    Transactable: Final = "fake-valid-nonce"
    TransactableAmEx: Final = "fake-valid-amex-nonce"
    TransactableBusiness: Final = "fake-valid-business-nonce"
    TransactableCommercial: Final = "fake-valid-commercial-nonce"
    TransactableConsumer: Final = "fake-valid-consumer-nonce"
    TransactableCorporate: Final = "fake-valid-corporate-nonce"
    TransactableCountryOfIssuanceCAD: Final = "fake-valid-country-of-issuance-cad-nonce"
    TransactableCountryOfIssuanceUSA: Final = "fake-valid-country-of-issuance-usa-nonce"
    TransactableDebit: Final = "fake-valid-debit-nonce"
    TransactableDinersClub: Final = "fake-valid-dinersclub-nonce"
    TransactableDiscover: Final = "fake-valid-discover-nonce"
    TransactableDurbinRegulated: Final = "fake-valid-durbin-regulated-nonce"
    TransactableHealthcare: Final = "fake-valid-healthcare-nonce"
    TransactableIssuingBankNetworkOnly: Final = "fake-valid-issuing-bank-network-only-nonce"
    TransactableJCB: Final = "fake-valid-jcb-nonce"
    TransactableMaestro: Final = "fake-valid-maestro-nonce"
    TransactableMasterCard: Final = "fake-valid-mastercard-nonce"
    TransactableNoIndicators: Final = "fake-valid-no-indicators-nonce"
    TransactablePayroll: Final = "fake-valid-payroll-nonce"
    TransactablePinlessDebitVisa: Final = "fake-pinless-debit-visa-nonce"
    TransactablePrepaid: Final = "fake-valid-prepaid-nonce"
    TransactablePrepaidReloadable: Final = "fake-valid-prepaid-reloadable-nonce"
    TransactablePurchase: Final = "fake-valid-purchase-nonce"
    TransactableUnknownIndicators: Final = "fake-valid-unknown-indicators-nonce"
    TransactableVisa: Final = "fake-valid-visa-nonce"
    VenmoAccount: Final = "fake-venmo-account-nonce"
    VenmoAccountTokenIssuanceError: Final = "fake-token-issuance-error-venmo-account-nonce"
    VisaCheckoutAmEx: Final = "fake-visa-checkout-amex-nonce"
    VisaCheckoutDiscover: Final = "fake-visa-checkout-discover-nonce"
    VisaCheckoutMasterCard: Final = "fake-visa-checkout-mastercard-nonce"
    VisaCheckoutVisa: Final = "fake-visa-checkout-visa-nonce"
    UsBankAccount: Final = "fake-us-bank-account-nonce"
