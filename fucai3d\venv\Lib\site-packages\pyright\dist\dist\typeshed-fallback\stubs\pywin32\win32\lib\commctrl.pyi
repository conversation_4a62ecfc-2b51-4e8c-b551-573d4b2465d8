from _typeshed import Incomplete

WM_USER: int
ICC_LISTVIEW_CLASSES: int
ICC_TREEVIEW_CLASSES: int
ICC_BAR_CLASSES: int
ICC_TAB_CLASSES: int
ICC_UPDOWN_CLASS: int
ICC_PROGRESS_CLASS: int
ICC_HOTKEY_CLASS: int
ICC_ANIMATE_CLASS: int
ICC_WIN95_CLASSES: int
ICC_DATE_CLASSES: int
ICC_USEREX_CLASSES: int
ICC_COOL_CLASSES: int
ICC_INTERNET_CLASSES: int
ICC_PAGESCROLLER_CLASS: int
ICC_NATIVEFNTCTL_CLASS: int
ODT_HEADER: int
ODT_TAB: int
ODT_LISTVIEW: int
PY_0U: int
NM_FIRST: int
NM_LAST: Incomplete
LVN_FIRST: Incomplete
LVN_LAST: Incomplete
HDN_FIRST: Incomplete
HDN_LAST: Incomplete
TVN_FIRST: Incomplete
TVN_LAST: Incomplete
TTN_FIRST: Incomplete
TTN_LAST: Incomplete
TCN_FIRST: Incomplete
TCN_LAST: Incomplete
CDN_FIRST: Incomplete
CDN_LAST: Incomplete
TBN_FIRST: Incomplete
TBN_LAST: Incomplete
UDN_FIRST: Incomplete
UDN_LAST: Incomplete
MCN_FIRST: Incomplete
MCN_LAST: Incomplete
DTN_FIRST: Incomplete
DTN_LAST: Incomplete
CBEN_FIRST: Incomplete
CBEN_LAST: Incomplete
RBN_FIRST: Incomplete
RBN_LAST: Incomplete
IPN_FIRST: Incomplete
IPN_LAST: Incomplete
SBN_FIRST: Incomplete
SBN_LAST: Incomplete
PGN_FIRST: Incomplete
PGN_LAST: Incomplete
LVM_FIRST: int
TV_FIRST: int
HDM_FIRST: int
TCM_FIRST: int
PGM_FIRST: int
CCM_FIRST: int
CCM_SETBKCOLOR: Incomplete
CCM_SETCOLORSCHEME: Incomplete
CCM_GETCOLORSCHEME: Incomplete
CCM_GETDROPTARGET: Incomplete
CCM_SETUNICODEFORMAT: Incomplete
CCM_GETUNICODEFORMAT: Incomplete
INFOTIPSIZE: int
NM_OUTOFMEMORY: Incomplete
NM_CLICK: Incomplete
NM_DBLCLK: Incomplete
NM_RETURN: Incomplete
NM_RCLICK: Incomplete
NM_RDBLCLK: Incomplete
NM_SETFOCUS: Incomplete
NM_KILLFOCUS: Incomplete
NM_CUSTOMDRAW: Incomplete
NM_HOVER: Incomplete
NM_NCHITTEST: Incomplete
NM_KEYDOWN: Incomplete
NM_RELEASEDCAPTURE: Incomplete
NM_SETCURSOR: Incomplete
NM_CHAR: Incomplete
MSGF_COMMCTRL_BEGINDRAG: int
MSGF_COMMCTRL_SIZEHEADER: int
MSGF_COMMCTRL_DRAGSELECT: int
MSGF_COMMCTRL_TOOLBARCUST: int
CDRF_DODEFAULT: int
CDRF_NEWFONT: int
CDRF_SKIPDEFAULT: int
CDRF_NOTIFYPOSTPAINT: int
CDRF_NOTIFYITEMDRAW: int
CDRF_NOTIFYSUBITEMDRAW: int
CDRF_NOTIFYPOSTERASE: int
CDDS_PREPAINT: int
CDDS_POSTPAINT: int
CDDS_PREERASE: int
CDDS_POSTERASE: int
CDDS_ITEM: int
CDDS_ITEMPREPAINT: Incomplete
CDDS_ITEMPOSTPAINT: Incomplete
CDDS_ITEMPREERASE: Incomplete
CDDS_ITEMPOSTERASE: Incomplete
CDDS_SUBITEM: int
CDIS_SELECTED: int
CDIS_GRAYED: int
CDIS_DISABLED: int
CDIS_CHECKED: int
CDIS_FOCUS: int
CDIS_DEFAULT: int
CDIS_HOT: int
CDIS_MARKED: int
CDIS_INDETERMINATE: int
CLR_NONE: int
CLR_DEFAULT: int
ILC_MASK: int
ILC_COLOR: int
ILC_COLORDDB: int
ILC_COLOR4: int
ILC_COLOR8: int
ILC_COLOR16: int
ILC_COLOR24: int
ILC_COLOR32: int
ILC_PALETTE: int
ILD_NORMAL: int
ILD_TRANSPARENT: int
ILD_MASK: int
ILD_IMAGE: int
ILD_ROP: int
ILD_BLEND25: int
ILD_BLEND50: int
ILD_OVERLAYMASK: int
ILD_SELECTED: int
ILD_FOCUS: int
ILD_BLEND: int
CLR_HILIGHT: int
ILCF_MOVE: int
ILCF_SWAP: int
WC_HEADERA: str
WC_HEADER: str
HDS_HORZ: int
HDS_BUTTONS: int
HDS_HOTTRACK: int
HDS_HIDDEN: int
HDS_DRAGDROP: int
HDS_FULLDRAG: int
HDI_WIDTH: int
HDI_HEIGHT: int
HDI_TEXT: int
HDI_FORMAT: int
HDI_LPARAM: int
HDI_BITMAP: int
HDI_IMAGE: int
HDI_DI_SETITEM: int
HDI_ORDER: int
HDF_LEFT: int
HDF_RIGHT: int
HDF_CENTER: int
HDF_JUSTIFYMASK: int
HDF_RTLREADING: int
HDF_OWNERDRAW: int
HDF_STRING: int
HDF_BITMAP: int
HDF_BITMAP_ON_RIGHT: int
HDF_IMAGE: int
HDM_GETITEMCOUNT: Incomplete
HDM_INSERTITEMA: Incomplete
HDM_INSERTITEMW: Incomplete
HDM_INSERTITEM: Incomplete
HDM_DELETEITEM: Incomplete
HDM_GETITEMA: Incomplete
HDM_GETITEMW: Incomplete
HDM_GETITEM: Incomplete
HDM_SETITEMA: Incomplete
HDM_SETITEMW: Incomplete
HDM_SETITEM: Incomplete
HDM_LAYOUT: Incomplete
HHT_NOWHERE: int
HHT_ONHEADER: int
HHT_ONDIVIDER: int
HHT_ONDIVOPEN: int
HHT_ABOVE: int
HHT_BELOW: int
HHT_TORIGHT: int
HHT_TOLEFT: int
HDM_HITTEST: Incomplete
HDM_GETITEMRECT: Incomplete
HDM_SETIMAGELIST: Incomplete
HDM_GETIMAGELIST: Incomplete
HDM_ORDERTOINDEX: Incomplete
HDM_CREATEDRAGIMAGE: Incomplete
HDM_GETORDERARRAY: Incomplete
HDM_SETORDERARRAY: Incomplete
HDM_SETHOTDIVIDER: Incomplete
HDM_SETUNICODEFORMAT: Incomplete
HDM_GETUNICODEFORMAT: Incomplete
HDN_ITEMCHANGINGA: Incomplete
HDN_ITEMCHANGINGW: Incomplete
HDN_ITEMCHANGEDA: Incomplete
HDN_ITEMCHANGEDW: Incomplete
HDN_ITEMCLICKA: Incomplete
HDN_ITEMCLICKW: Incomplete
HDN_ITEMDBLCLICKA: Incomplete
HDN_ITEMDBLCLICKW: Incomplete
HDN_DIVIDERDBLCLICKA: Incomplete
HDN_DIVIDERDBLCLICKW: Incomplete
HDN_BEGINTRACKA: Incomplete
HDN_BEGINTRACKW: Incomplete
HDN_ENDTRACKA: Incomplete
HDN_ENDTRACKW: Incomplete
HDN_TRACKA: Incomplete
HDN_TRACKW: Incomplete
HDN_GETDISPINFOA: Incomplete
HDN_GETDISPINFOW: Incomplete
HDN_BEGINDRAG: Incomplete
HDN_ENDDRAG: Incomplete
HDN_ITEMCHANGING: Incomplete
HDN_ITEMCHANGED: Incomplete
HDN_ITEMCLICK: Incomplete
HDN_ITEMDBLCLICK: Incomplete
HDN_DIVIDERDBLCLICK: Incomplete
HDN_BEGINTRACK: Incomplete
HDN_ENDTRACK: Incomplete
HDN_TRACK: Incomplete
HDN_GETDISPINFO: Incomplete
TOOLBARCLASSNAMEA: str
TOOLBARCLASSNAME: str
CMB_MASKED: int
TBSTATE_CHECKED: int
TBSTATE_PRESSED: int
TBSTATE_ENABLED: int
TBSTATE_HIDDEN: int
TBSTATE_INDETERMINATE: int
TBSTATE_WRAP: int
TBSTATE_ELLIPSES: int
TBSTATE_MARKED: int
TBSTYLE_BUTTON: int
TBSTYLE_SEP: int
TBSTYLE_CHECK: int
TBSTYLE_GROUP: int
TBSTYLE_CHECKGROUP: Incomplete
TBSTYLE_DROPDOWN: int
TBSTYLE_AUTOSIZE: int
TBSTYLE_NOPREFIX: int
TBSTYLE_TOOLTIPS: int
TBSTYLE_WRAPABLE: int
TBSTYLE_ALTDRAG: int
TBSTYLE_FLAT: int
TBSTYLE_LIST: int
TBSTYLE_CUSTOMERASE: int
TBSTYLE_REGISTERDROP: int
TBSTYLE_TRANSPARENT: int
TBSTYLE_EX_DRAWDDARROWS: int
BTNS_BUTTON: int
BTNS_SEP: int
BTNS_CHECK: int
BTNS_GROUP: int
BTNS_CHECKGROUP: Incomplete
BTNS_DROPDOWN: int
BTNS_AUTOSIZE: int
BTNS_NOPREFIX: int
BTNS_SHOWTEXT: int
BTNS_WHOLEDROPDOWN: int
TBCDRF_NOEDGES: int
TBCDRF_HILITEHOTTRACK: int
TBCDRF_NOOFFSET: int
TBCDRF_NOMARK: int
TBCDRF_NOETCHEDEFFECT: int
TB_ENABLEBUTTON: Incomplete
TB_CHECKBUTTON: Incomplete
TB_PRESSBUTTON: Incomplete
TB_HIDEBUTTON: Incomplete
TB_INDETERMINATE: Incomplete
TB_MARKBUTTON: Incomplete
TB_ISBUTTONENABLED: Incomplete
TB_ISBUTTONCHECKED: Incomplete
TB_ISBUTTONPRESSED: Incomplete
TB_ISBUTTONHIDDEN: Incomplete
TB_ISBUTTONINDETERMINATE: Incomplete
TB_ISBUTTONHIGHLIGHTED: Incomplete
TB_SETSTATE: Incomplete
TB_GETSTATE: Incomplete
TB_ADDBITMAP: Incomplete
HINST_COMMCTRL: int
IDB_STD_SMALL_COLOR: int
IDB_STD_LARGE_COLOR: int
IDB_VIEW_SMALL_COLOR: int
IDB_VIEW_LARGE_COLOR: int
IDB_HIST_SMALL_COLOR: int
IDB_HIST_LARGE_COLOR: int
STD_CUT: int
STD_COPY: int
STD_PASTE: int
STD_UNDO: int
STD_REDOW: int
STD_DELETE: int
STD_FILENEW: int
STD_FILEOPEN: int
STD_FILESAVE: int
STD_PRINTPRE: int
STD_PROPERTIES: int
STD_HELP: int
STD_FIND: int
STD_REPLACE: int
STD_PRINT: int
VIEW_LARGEICONS: int
VIEW_SMALLICONS: int
VIEW_LIST: int
VIEW_DETAILS: int
VIEW_SORTNAME: int
VIEW_SORTSIZE: int
VIEW_SORTDATE: int
VIEW_SORTTYPE: int
VIEW_PARENTFOLDER: int
VIEW_NETCONNECT: int
VIEW_NETDISCONNECT: int
VIEW_NEWFOLDER: int
VIEW_VIEWMENU: int
HIST_BACK: int
HIST_FORWARD: int
HIST_FAVORITES: int
HIST_ADDTOFAVORITES: int
HIST_VIEWTREE: int
TB_ADDBUTTONSA: Incomplete
TB_INSERTBUTTONA: Incomplete
TB_ADDBUTTONS: Incomplete
TB_INSERTBUTTON: Incomplete
TB_DELETEBUTTON: Incomplete
TB_GETBUTTON: Incomplete
TB_BUTTONCOUNT: Incomplete
TB_COMMANDTOINDEX: Incomplete
TB_SAVERESTOREA: Incomplete
TB_SAVERESTOREW: Incomplete
TB_CUSTOMIZE: Incomplete
TB_ADDSTRINGA: Incomplete
TB_ADDSTRINGW: Incomplete
TB_GETITEMRECT: Incomplete
TB_BUTTONSTRUCTSIZE: Incomplete
TB_SETBUTTONSIZE: Incomplete
TB_SETBITMAPSIZE: Incomplete
TB_AUTOSIZE: Incomplete
TB_GETTOOLTIPS: Incomplete
TB_SETTOOLTIPS: Incomplete
TB_SETPARENT: Incomplete
TB_SETROWS: Incomplete
TB_GETROWS: Incomplete
TB_SETCMDID: Incomplete
TB_CHANGEBITMAP: Incomplete
TB_GETBITMAP: Incomplete
TB_GETBUTTONTEXTA: Incomplete
TB_GETBUTTONTEXTW: Incomplete
TB_REPLACEBITMAP: Incomplete
TB_SETINDENT: Incomplete
TB_SETIMAGELIST: Incomplete
TB_GETIMAGELIST: Incomplete
TB_LOADIMAGES: Incomplete
TB_GETRECT: Incomplete
TB_SETHOTIMAGELIST: Incomplete
TB_GETHOTIMAGELIST: Incomplete
TB_SETDISABLEDIMAGELIST: Incomplete
TB_GETDISABLEDIMAGELIST: Incomplete
TB_SETSTYLE: Incomplete
TB_GETSTYLE: Incomplete
TB_GETBUTTONSIZE: Incomplete
TB_SETBUTTONWIDTH: Incomplete
TB_SETMAXTEXTROWS: Incomplete
TB_GETTEXTROWS: Incomplete
TB_GETBUTTONTEXT: Incomplete
TB_SAVERESTORE: Incomplete
TB_ADDSTRING: Incomplete
TB_GETOBJECT: Incomplete
TB_GETHOTITEM: Incomplete
TB_SETHOTITEM: Incomplete
TB_SETANCHORHIGHLIGHT: Incomplete
TB_GETANCHORHIGHLIGHT: Incomplete
TB_MAPACCELERATORA: Incomplete
TBIMHT_AFTER: int
TBIMHT_BACKGROUND: int
TB_GETINSERTMARK: Incomplete
TB_SETINSERTMARK: Incomplete
TB_INSERTMARKHITTEST: Incomplete
TB_MOVEBUTTON: Incomplete
TB_GETMAXSIZE: Incomplete
TB_SETEXTENDEDSTYLE: Incomplete
TB_GETEXTENDEDSTYLE: Incomplete
TB_GETPADDING: Incomplete
TB_SETPADDING: Incomplete
TB_SETINSERTMARKCOLOR: Incomplete
TB_GETINSERTMARKCOLOR: Incomplete
TB_SETCOLORSCHEME: Incomplete
TB_GETCOLORSCHEME: Incomplete
TB_SETUNICODEFORMAT: Incomplete
TB_GETUNICODEFORMAT: Incomplete
TB_MAPACCELERATORW: Incomplete
TB_MAPACCELERATOR: Incomplete
TBBF_LARGE: int
TB_GETBITMAPFLAGS: Incomplete
TBIF_IMAGE: int
TBIF_TEXT: int
TBIF_STATE: int
TBIF_STYLE: int
TBIF_LPARAM: int
TBIF_COMMAND: int
TBIF_SIZE: int
TB_GETBUTTONINFOW: Incomplete
TB_SETBUTTONINFOW: Incomplete
TB_GETBUTTONINFOA: Incomplete
TB_SETBUTTONINFOA: Incomplete
TB_INSERTBUTTONW: Incomplete
TB_ADDBUTTONSW: Incomplete
TB_HITTEST: Incomplete
TB_SETDRAWTEXTFLAGS: Incomplete
TBN_GETBUTTONINFOA: Incomplete
TBN_GETBUTTONINFOW: Incomplete
TBN_BEGINDRAG: Incomplete
TBN_ENDDRAG: Incomplete
TBN_BEGINADJUST: Incomplete
TBN_ENDADJUST: Incomplete
TBN_RESET: Incomplete
TBN_QUERYINSERT: Incomplete
TBN_QUERYDELETE: Incomplete
TBN_TOOLBARCHANGE: Incomplete
TBN_CUSTHELP: Incomplete
TBN_DROPDOWN: Incomplete
TBN_GETOBJECT: Incomplete
HICF_OTHER: int
HICF_MOUSE: int
HICF_ARROWKEYS: int
HICF_ACCELERATOR: int
HICF_DUPACCEL: int
HICF_ENTERING: int
HICF_LEAVING: int
HICF_RESELECT: int
TBN_HOTITEMCHANGE: Incomplete
TBN_DRAGOUT: Incomplete
TBN_DELETINGBUTTON: Incomplete
TBN_GETDISPINFOA: Incomplete
TBN_GETDISPINFOW: Incomplete
TBN_GETINFOTIPA: Incomplete
TBN_GETINFOTIPW: Incomplete
TBN_GETINFOTIP: Incomplete
TBNF_IMAGE: int
TBNF_TEXT: int
TBNF_DI_SETITEM: int
TBN_GETDISPINFO: Incomplete
TBDDRET_DEFAULT: int
TBDDRET_NODEFAULT: int
TBDDRET_TREATPRESSED: int
TBN_GETBUTTONINFO: Incomplete
REBARCLASSNAMEA: str
REBARCLASSNAME: str
RBIM_IMAGELIST: int
RBS_TOOLTIPS: int
RBS_VARHEIGHT: int
RBS_BANDBORDERS: int
RBS_FIXEDORDER: int
RBS_REGISTERDROP: int
RBS_AUTOSIZE: int
RBS_VERTICALGRIPPER: int
RBS_DBLCLKTOGGLE: int
RBBS_BREAK: int
RBBS_FIXEDSIZE: int
RBBS_CHILDEDGE: int
RBBS_HIDDEN: int
RBBS_NOVERT: int
RBBS_FIXEDBMP: int
RBBS_VARIABLEHEIGHT: int
RBBS_GRIPPERALWAYS: int
RBBS_NOGRIPPER: int
RBBIM_STYLE: int
RBBIM_COLORS: int
RBBIM_TEXT: int
RBBIM_IMAGE: int
RBBIM_CHILD: int
RBBIM_CHILDSIZE: int
RBBIM_SIZE: int
RBBIM_BACKGROUND: int
RBBIM_ID: int
RBBIM_IDEALSIZE: int
RBBIM_LPARAM: int
RB_INSERTBANDA: Incomplete
RB_DELETEBAND: Incomplete
RB_GETBARINFO: Incomplete
RB_SETBARINFO: Incomplete
RB_SETBANDINFOA: Incomplete
RB_SETPARENT: Incomplete
RB_HITTEST: Incomplete
RB_GETRECT: Incomplete
RB_INSERTBANDW: Incomplete
RB_SETBANDINFOW: Incomplete
RB_GETBANDCOUNT: Incomplete
RB_GETROWCOUNT: Incomplete
RB_GETROWHEIGHT: Incomplete
RB_IDTOINDEX: Incomplete
RB_GETTOOLTIPS: Incomplete
RB_SETTOOLTIPS: Incomplete
RB_SETBKCOLOR: Incomplete
RB_GETBKCOLOR: Incomplete
RB_SETTEXTCOLOR: Incomplete
RB_GETTEXTCOLOR: Incomplete
RB_SIZETORECT: Incomplete
RB_SETCOLORSCHEME: Incomplete
RB_GETCOLORSCHEME: Incomplete
RB_INSERTBAND: Incomplete
RB_SETBANDINFO: Incomplete
RB_BEGINDRAG: Incomplete
RB_ENDDRAG: Incomplete
RB_DRAGMOVE: Incomplete
RB_GETBARHEIGHT: Incomplete
RB_GETBANDINFOW: Incomplete
RB_GETBANDINFOA: Incomplete
RB_GETBANDINFO: Incomplete
RB_MINIMIZEBAND: Incomplete
RB_MAXIMIZEBAND: Incomplete
RB_GETDROPTARGET: Incomplete
RB_GETBANDBORDERS: Incomplete
RB_SHOWBAND: Incomplete
RB_SETPALETTE: Incomplete
RB_GETPALETTE: Incomplete
RB_MOVEBAND: Incomplete
RB_SETUNICODEFORMAT: Incomplete
RB_GETUNICODEFORMAT: Incomplete
RBN_HEIGHTCHANGE: Incomplete
RBN_GETOBJECT: Incomplete
RBN_LAYOUTCHANGED: Incomplete
RBN_AUTOSIZE: Incomplete
RBN_BEGINDRAG: Incomplete
RBN_ENDDRAG: Incomplete
RBN_DELETINGBAND: Incomplete
RBN_DELETEDBAND: Incomplete
RBN_CHILDSIZE: Incomplete
RBNM_ID: int
RBNM_STYLE: int
RBNM_LPARAM: int
RBHT_NOWHERE: int
RBHT_CAPTION: int
RBHT_CLIENT: int
RBHT_GRABBER: int
TOOLTIPS_CLASSA: str
TOOLTIPS_CLASS: str
TTS_ALWAYSTIP: int
TTS_NOPREFIX: int
TTF_IDISHWND: int
TTF_CENTERTIP: int
TTF_RTLREADING: int
TTF_SUBCLASS: int
TTF_TRACK: int
TTF_ABSOLUTE: int
TTF_TRANSPARENT: int
TTF_DI_SETITEM: int
TTDT_AUTOMATIC: int
TTDT_RESHOW: int
TTDT_AUTOPOP: int
TTDT_INITIAL: int
TTM_ACTIVATE: Incomplete
TTM_SETDELAYTIME: Incomplete
TTM_ADDTOOLA: Incomplete
TTM_ADDTOOLW: Incomplete
TTM_DELTOOLA: Incomplete
TTM_DELTOOLW: Incomplete
TTM_NEWTOOLRECTA: Incomplete
TTM_NEWTOOLRECTW: Incomplete
TTM_RELAYEVENT: Incomplete
TTM_GETTOOLINFOA: Incomplete
TTM_GETTOOLINFOW: Incomplete
TTM_SETTOOLINFOA: Incomplete
TTM_SETTOOLINFOW: Incomplete
TTM_HITTESTA: Incomplete
TTM_HITTESTW: Incomplete
TTM_GETTEXTA: Incomplete
TTM_GETTEXTW: Incomplete
TTM_UPDATETIPTEXTA: Incomplete
TTM_UPDATETIPTEXTW: Incomplete
TTM_GETTOOLCOUNT: Incomplete
TTM_ENUMTOOLSA: Incomplete
TTM_ENUMTOOLSW: Incomplete
TTM_GETCURRENTTOOLA: Incomplete
TTM_GETCURRENTTOOLW: Incomplete
TTM_WINDOWFROMPOINT: Incomplete
TTM_TRACKACTIVATE: Incomplete
TTM_TRACKPOSITION: Incomplete
TTM_SETTIPBKCOLOR: Incomplete
TTM_SETTIPTEXTCOLOR: Incomplete
TTM_GETDELAYTIME: Incomplete
TTM_GETTIPBKCOLOR: Incomplete
TTM_GETTIPTEXTCOLOR: Incomplete
TTM_SETMAXTIPWIDTH: Incomplete
TTM_GETMAXTIPWIDTH: Incomplete
TTM_SETMARGIN: Incomplete
TTM_GETMARGIN: Incomplete
TTM_POP: Incomplete
TTM_UPDATE: Incomplete
TTM_ADDTOOL: Incomplete
TTM_DELTOOL: Incomplete
TTM_NEWTOOLRECT: Incomplete
TTM_GETTOOLINFO: Incomplete
TTM_SETTOOLINFO: Incomplete
TTM_HITTEST: Incomplete
TTM_GETTEXT: Incomplete
TTM_UPDATETIPTEXT: Incomplete
TTM_ENUMTOOLS: Incomplete
TTM_GETCURRENTTOOL: Incomplete
TTN_GETDISPINFOA: Incomplete
TTN_GETDISPINFOW: Incomplete
TTN_SHOW: Incomplete
TTN_POP: Incomplete
TTN_GETDISPINFO: Incomplete
TTN_NEEDTEXT: Incomplete
TTN_NEEDTEXTA: Incomplete
TTN_NEEDTEXTW: Incomplete
SBARS_SIZEGRIP: int
SBARS_TOOLTIPS: int
STATUSCLASSNAMEA: str
STATUSCLASSNAME: str
SB_SETTEXTA: Incomplete
SB_SETTEXTW: Incomplete
SB_GETTEXTA: Incomplete
SB_GETTEXTW: Incomplete
SB_GETTEXTLENGTHA: Incomplete
SB_GETTEXTLENGTHW: Incomplete
SB_GETTEXT: Incomplete
SB_SETTEXT: Incomplete
SB_GETTEXTLENGTH: Incomplete
SB_SETPARTS: Incomplete
SB_GETPARTS: Incomplete
SB_GETBORDERS: Incomplete
SB_SETMINHEIGHT: Incomplete
SB_SIMPLE: Incomplete
SB_GETRECT: Incomplete
SB_ISSIMPLE: Incomplete
SB_SETICON: Incomplete
SB_SETTIPTEXTA: Incomplete
SB_SETTIPTEXTW: Incomplete
SB_GETTIPTEXTA: Incomplete
SB_GETTIPTEXTW: Incomplete
SB_GETICON: Incomplete
SB_SETTIPTEXT: Incomplete
SB_GETTIPTEXT: Incomplete
SB_SETUNICODEFORMAT: Incomplete
SB_GETUNICODEFORMAT: Incomplete
SBT_OWNERDRAW: int
SBT_NOBORDERS: int
SBT_POPOUT: int
SBT_RTLREADING: int
SBT_NOTABPARSING: int
SBT_TOOLTIPS: int
SB_SETBKCOLOR: Incomplete
SBN_SIMPLEMODECHANGE: Incomplete
TRACKBAR_CLASSA: str
TRACKBAR_CLASS: str
TBS_AUTOTICKS: int
TBS_VERT: int
TBS_HORZ: int
TBS_TOP: int
TBS_BOTTOM: int
TBS_LEFT: int
TBS_RIGHT: int
TBS_BOTH: int
TBS_NOTICKS: int
TBS_ENABLESELRANGE: int
TBS_FIXEDLENGTH: int
TBS_NOTHUMB: int
TBS_TOOLTIPS: int
TBM_GETPOS: int
TBM_GETRANGEMIN: Incomplete
TBM_GETRANGEMAX: Incomplete
TBM_GETTIC: Incomplete
TBM_SETTIC: Incomplete
TBM_SETPOS: Incomplete
TBM_SETRANGE: Incomplete
TBM_SETRANGEMIN: Incomplete
TBM_SETRANGEMAX: Incomplete
TBM_CLEARTICS: Incomplete
TBM_SETSEL: Incomplete
TBM_SETSELSTART: Incomplete
TBM_SETSELEND: Incomplete
TBM_GETPTICS: Incomplete
TBM_GETTICPOS: Incomplete
TBM_GETNUMTICS: Incomplete
TBM_GETSELSTART: Incomplete
TBM_GETSELEND: Incomplete
TBM_CLEARSEL: Incomplete
TBM_SETTICFREQ: Incomplete
TBM_SETPAGESIZE: Incomplete
TBM_GETPAGESIZE: Incomplete
TBM_SETLINESIZE: Incomplete
TBM_GETLINESIZE: Incomplete
TBM_GETTHUMBRECT: Incomplete
TBM_GETCHANNELRECT: Incomplete
TBM_SETTHUMBLENGTH: Incomplete
TBM_GETTHUMBLENGTH: Incomplete
TBM_SETTOOLTIPS: Incomplete
TBM_GETTOOLTIPS: Incomplete
TBM_SETTIPSIDE: Incomplete
TBTS_TOP: int
TBTS_LEFT: int
TBTS_BOTTOM: int
TBTS_RIGHT: int
TBM_SETBUDDY: Incomplete
TBM_GETBUDDY: Incomplete
TBM_SETUNICODEFORMAT: Incomplete
TBM_GETUNICODEFORMAT: Incomplete
TB_LINEUP: int
TB_LINEDOWN: int
TB_PAGEUP: int
TB_PAGEDOWN: int
TB_THUMBPOSITION: int
TB_THUMBTRACK: int
TB_TOP: int
TB_BOTTOM: int
TB_ENDTRACK: int
TBCD_TICS: int
TBCD_THUMB: int
TBCD_CHANNEL: int
DL_BEGINDRAG: Incomplete
DL_DRAGGING: Incomplete
DL_DROPPED: Incomplete
DL_CANCELDRAG: Incomplete
DL_CURSORSET: int
DL_STOPCURSOR: int
DL_COPYCURSOR: int
DL_MOVECURSOR: int
DRAGLISTMSGSTRING: str
UPDOWN_CLASSA: str
UPDOWN_CLASS: str
UD_MAXVAL: int
UD_MINVAL: Incomplete
UDS_WRAP: int
UDS_SETBUDDYINT: int
UDS_ALIGNRIGHT: int
UDS_ALIGNLEFT: int
UDS_AUTOBUDDY: int
UDS_ARROWKEYS: int
UDS_HORZ: int
UDS_NOTHOUSANDS: int
UDS_HOTTRACK: int
UDM_SETRANGE: Incomplete
UDM_GETRANGE: Incomplete
UDM_SETPOS: Incomplete
UDM_GETPOS: Incomplete
UDM_SETBUDDY: Incomplete
UDM_GETBUDDY: Incomplete
UDM_SETACCEL: Incomplete
UDM_GETACCEL: Incomplete
UDM_SETBASE: Incomplete
UDM_GETBASE: Incomplete
UDM_SETRANGE32: Incomplete
UDM_GETRANGE32: Incomplete
UDM_SETUNICODEFORMAT: Incomplete
UDM_GETUNICODEFORMAT: Incomplete
UDN_DELTAPOS: Incomplete
PROGRESS_CLASSA: str
PROGRESS_CLASS: str
PBS_SMOOTH: int
PBS_VERTICAL: int
PBM_SETRANGE: Incomplete
PBM_SETPOS: Incomplete
PBM_DELTAPOS: Incomplete
PBM_SETSTEP: Incomplete
PBM_STEPIT: Incomplete
PBM_SETRANGE32: Incomplete
PBM_GETRANGE: Incomplete
PBM_GETPOS: Incomplete
PBM_SETBARCOLOR: Incomplete
PBM_SETBKCOLOR: Incomplete
HOTKEYF_SHIFT: int
HOTKEYF_CONTROL: int
HOTKEYF_ALT: int
HOTKEYF_EXT: int
HKCOMB_NONE: int
HKCOMB_S: int
HKCOMB_C: int
HKCOMB_A: int
HKCOMB_SC: int
HKCOMB_SA: int
HKCOMB_CA: int
HKCOMB_SCA: int
HKM_SETHOTKEY: Incomplete
HKM_GETHOTKEY: Incomplete
HKM_SETRULES: Incomplete
HOTKEY_CLASSA: str
HOTKEY_CLASS: str
CCS_TOP: int
CCS_NOMOVEY: int
CCS_BOTTOM: int
CCS_NORESIZE: int
CCS_NOPARENTALIGN: int
CCS_ADJUSTABLE: int
CCS_NODIVIDER: int
CCS_VERT: int
CCS_LEFT: Incomplete
CCS_RIGHT: Incomplete
CCS_NOMOVEX: Incomplete
WC_LISTVIEWA: str
WC_LISTVIEW: str
LVS_ICON: int
LVS_REPORT: int
LVS_SMALLICON: int
LVS_LIST: int
LVS_TYPEMASK: int
LVS_SINGLESEL: int
LVS_SHOWSELALWAYS: int
LVS_SORTASCENDING: int
LVS_SORTDESCENDING: int
LVS_SHAREIMAGELISTS: int
LVS_NOLABELWRAP: int
LVS_AUTOARRANGE: int
LVS_EDITLABELS: int
LVS_OWNERDATA: int
LVS_NOSCROLL: int
LVS_TYPESTYLEMASK: int
LVS_ALIGNTOP: int
LVS_ALIGNLEFT: int
LVS_ALIGNMASK: int
LVS_OWNERDRAWFIXED: int
LVS_NOCOLUMNHEADER: int
LVS_NOSORTHEADER: int
LVM_SETUNICODEFORMAT: Incomplete
LVM_GETUNICODEFORMAT: Incomplete
LVM_GETBKCOLOR: Incomplete
LVM_SETBKCOLOR: Incomplete
LVM_GETIMAGELIST: Incomplete
LVSIL_NORMAL: int
LVSIL_SMALL: int
LVSIL_STATE: int
LVM_SETIMAGELIST: Incomplete
LVM_GETITEMCOUNT: Incomplete
LVIF_TEXT: int
LVIF_IMAGE: int
LVIF_PARAM: int
LVIF_STATE: int
LVIF_INDENT: int
LVIF_NORECOMPUTE: int
LVIS_FOCUSED: int
LVIS_SELECTED: int
LVIS_CUT: int
LVIS_DROPHILITED: int
LVIS_ACTIVATING: int
LVIS_OVERLAYMASK: int
LVIS_STATEIMAGEMASK: int
I_INDENTCALLBACK: int
LPSTR_TEXTCALLBACKA: int
LPSTR_TEXTCALLBACK: int
I_IMAGECALLBACK: int
LVM_GETITEMA: Incomplete
LVM_GETITEMW: Incomplete
LVM_GETITEM: Incomplete
LVM_SETITEMA: Incomplete
LVM_SETITEMW: Incomplete
LVM_SETITEM: Incomplete
LVM_INSERTITEMA: Incomplete
LVM_INSERTITEMW: Incomplete
LVM_INSERTITEM: Incomplete
LVM_DELETEITEM: Incomplete
LVM_DELETEALLITEMS: Incomplete
LVM_GETCALLBACKMASK: Incomplete
LVM_SETCALLBACKMASK: Incomplete
LVNI_ALL: int
LVNI_FOCUSED: int
LVNI_SELECTED: int
LVNI_CUT: int
LVNI_DROPHILITED: int
LVNI_ABOVE: int
LVNI_BELOW: int
LVNI_TOLEFT: int
LVNI_TORIGHT: int
LVM_GETNEXTITEM: Incomplete
LVFI_PARAM: int
LVFI_STRING: int
LVFI_PARTIAL: int
LVFI_WRAP: int
LVFI_NEARESTXY: int
LVM_FINDITEMA: Incomplete
LVM_FINDITEMW: Incomplete
LVM_FINDITEM: Incomplete
LVIR_BOUNDS: int
LVIR_ICON: int
LVIR_LABEL: int
LVIR_SELECTBOUNDS: int
LVM_GETITEMRECT: Incomplete
LVM_SETITEMPOSITION: Incomplete
LVM_GETITEMPOSITION: Incomplete
LVM_GETSTRINGWIDTHA: Incomplete
LVM_GETSTRINGWIDTHW: Incomplete
LVM_GETSTRINGWIDTH: Incomplete
LVHT_NOWHERE: int
LVHT_ONITEMICON: int
LVHT_ONITEMLABEL: int
LVHT_ONITEMSTATEICON: int
LVHT_ONITEM: Incomplete
LVHT_ABOVE: int
LVHT_BELOW: int
LVHT_TORIGHT: int
LVHT_TOLEFT: int
LVM_HITTEST: Incomplete
LVM_ENSUREVISIBLE: Incomplete
LVM_SCROLL: Incomplete
LVM_REDRAWITEMS: Incomplete
LVA_DEFAULT: int
LVA_ALIGNLEFT: int
LVA_ALIGNTOP: int
LVA_SNAPTOGRID: int
LVM_ARRANGE: Incomplete
LVM_EDITLABELA: Incomplete
LVM_EDITLABELW: Incomplete
LVM_EDITLABEL: Incomplete
LVM_GETEDITCONTROL: Incomplete
LVCF_FMT: int
LVCF_WIDTH: int
LVCF_TEXT: int
LVCF_SUBITEM: int
LVCF_IMAGE: int
LVCF_ORDER: int
LVCFMT_LEFT: int
LVCFMT_RIGHT: int
LVCFMT_CENTER: int
LVCFMT_JUSTIFYMASK: int
LVCFMT_IMAGE: int
LVCFMT_BITMAP_ON_RIGHT: int
LVCFMT_COL_HAS_IMAGES: int
LVM_GETCOLUMNA: Incomplete
LVM_GETCOLUMNW: Incomplete
LVM_GETCOLUMN: Incomplete
LVM_SETCOLUMNA: Incomplete
LVM_SETCOLUMNW: Incomplete
LVM_SETCOLUMN: Incomplete
LVM_INSERTCOLUMNA: Incomplete
LVM_INSERTCOLUMNW: Incomplete
LVM_INSERTCOLUMN: Incomplete
LVM_DELETECOLUMN: Incomplete
LVM_GETCOLUMNWIDTH: Incomplete
LVSCW_AUTOSIZE: int
LVSCW_AUTOSIZE_USEHEADER: int
LVM_SETCOLUMNWIDTH: Incomplete
LVM_GETHEADER: Incomplete
LVM_CREATEDRAGIMAGE: Incomplete
LVM_GETVIEWRECT: Incomplete
LVM_GETTEXTCOLOR: Incomplete
LVM_SETTEXTCOLOR: Incomplete
LVM_GETTEXTBKCOLOR: Incomplete
LVM_SETTEXTBKCOLOR: Incomplete
LVM_GETTOPINDEX: Incomplete
LVM_GETCOUNTPERPAGE: Incomplete
LVM_GETORIGIN: Incomplete
LVM_UPDATE: Incomplete
LVM_SETITEMSTATE: Incomplete
LVM_GETITEMSTATE: Incomplete
LVM_GETITEMTEXTA: Incomplete
LVM_GETITEMTEXTW: Incomplete
LVM_GETITEMTEXT: Incomplete
LVM_SETITEMTEXTA: Incomplete
LVM_SETITEMTEXTW: Incomplete
LVM_SETITEMTEXT: Incomplete
LVSICF_NOINVALIDATEALL: int
LVSICF_NOSCROLL: int
LVM_SETITEMCOUNT: Incomplete
LVM_SORTITEMS: Incomplete
LVM_SETITEMPOSITION32: Incomplete
LVM_GETSELECTEDCOUNT: Incomplete
LVM_GETITEMSPACING: Incomplete
LVM_GETISEARCHSTRINGA: Incomplete
LVM_GETISEARCHSTRINGW: Incomplete
LVM_GETISEARCHSTRING: Incomplete
LVM_SETICONSPACING: Incomplete
LVM_SETEXTENDEDLISTVIEWSTYLE: Incomplete
LVM_GETEXTENDEDLISTVIEWSTYLE: Incomplete
LVS_EX_GRIDLINES: int
LVS_EX_SUBITEMIMAGES: int
LVS_EX_CHECKBOXES: int
LVS_EX_TRACKSELECT: int
LVS_EX_HEADERDRAGDROP: int
LVS_EX_FULLROWSELECT: int
LVS_EX_ONECLICKACTIVATE: int
LVS_EX_TWOCLICKACTIVATE: int
LVS_EX_FLATSB: int
LVS_EX_REGIONAL: int
LVS_EX_INFOTIP: int
LVS_EX_UNDERLINEHOT: int
LVS_EX_UNDERLINECOLD: int
LVS_EX_MULTIWORKAREAS: int
LVM_GETSUBITEMRECT: Incomplete
LVM_SUBITEMHITTEST: Incomplete
LVM_SETCOLUMNORDERARRAY: Incomplete
LVM_GETCOLUMNORDERARRAY: Incomplete
LVM_SETHOTITEM: Incomplete
LVM_GETHOTITEM: Incomplete
LVM_SETHOTCURSOR: Incomplete
LVM_GETHOTCURSOR: Incomplete
LVM_APPROXIMATEVIEWRECT: Incomplete
LV_MAX_WORKAREAS: int
LVM_SETWORKAREAS: Incomplete
LVM_GETWORKAREAS: Incomplete
LVM_GETNUMBEROFWORKAREAS: Incomplete
LVM_GETSELECTIONMARK: Incomplete
LVM_SETSELECTIONMARK: Incomplete
LVM_SETHOVERTIME: Incomplete
LVM_GETHOVERTIME: Incomplete
LVM_SETTOOLTIPS: Incomplete
LVM_GETTOOLTIPS: Incomplete
LVBKIF_SOURCE_NONE: int
LVBKIF_SOURCE_HBITMAP: int
LVBKIF_SOURCE_URL: int
LVBKIF_SOURCE_MASK: int
LVBKIF_STYLE_NORMAL: int
LVBKIF_STYLE_TILE: int
LVBKIF_STYLE_MASK: int
LVM_SETBKIMAGEA: Incomplete
LVM_SETBKIMAGEW: Incomplete
LVM_GETBKIMAGEA: Incomplete
LVM_GETBKIMAGEW: Incomplete
LVKF_ALT: int
LVKF_CONTROL: int
LVKF_SHIFT: int
LVN_ITEMCHANGING: Incomplete
LVN_ITEMCHANGED: Incomplete
LVN_INSERTITEM: Incomplete
LVN_DELETEITEM: Incomplete
LVN_DELETEALLITEMS: Incomplete
LVN_BEGINLABELEDITA: Incomplete
LVN_BEGINLABELEDITW: Incomplete
LVN_ENDLABELEDITA: Incomplete
LVN_ENDLABELEDITW: Incomplete
LVN_COLUMNCLICK: Incomplete
LVN_BEGINDRAG: Incomplete
LVN_BEGINRDRAG: Incomplete
LVN_ODCACHEHINT: Incomplete
LVN_ODFINDITEMA: Incomplete
LVN_ODFINDITEMW: Incomplete
LVN_ITEMACTIVATE: Incomplete
LVN_ODSTATECHANGED: Incomplete
LVN_ODFINDITEM: Incomplete
LVN_HOTTRACK: Incomplete
LVN_GETDISPINFOA: Incomplete
LVN_GETDISPINFOW: Incomplete
LVN_SETDISPINFOA: Incomplete
LVN_SETDISPINFOW: Incomplete
LVN_BEGINLABELEDIT: Incomplete
LVN_ENDLABELEDIT: Incomplete
LVN_GETDISPINFO: Incomplete
LVN_SETDISPINFO: Incomplete
LVIF_DI_SETITEM: int
LVN_KEYDOWN: Incomplete
LVN_MARQUEEBEGIN: Incomplete
LVGIT_UNFOLDED: int
LVN_GETINFOTIPA: Incomplete
LVN_GETINFOTIPW: Incomplete
LVN_GETINFOTIP: Incomplete
WC_TREEVIEWA: str
WC_TREEVIEW: str
TVS_HASBUTTONS: int
TVS_HASLINES: int
TVS_LINESATROOT: int
TVS_EDITLABELS: int
TVS_DISABLEDRAGDROP: int
TVS_SHOWSELALWAYS: int
TVS_RTLREADING: int
TVS_NOTOOLTIPS: int
TVS_CHECKBOXES: int
TVS_TRACKSELECT: int
TVS_SINGLEEXPAND: int
TVS_INFOTIP: int
TVS_FULLROWSELECT: int
TVS_NOSCROLL: int
TVS_NONEVENHEIGHT: int
TVIF_TEXT: int
TVIF_IMAGE: int
TVIF_PARAM: int
TVIF_STATE: int
TVIF_HANDLE: int
TVIF_SELECTEDIMAGE: int
TVIF_CHILDREN: int
TVIF_INTEGRAL: int
TVIS_SELECTED: int
TVIS_CUT: int
TVIS_DROPHILITED: int
TVIS_BOLD: int
TVIS_EXPANDED: int
TVIS_EXPANDEDONCE: int
TVIS_EXPANDPARTIAL: int
TVIS_OVERLAYMASK: int
TVIS_STATEIMAGEMASK: int
TVIS_USERMASK: int
I_CHILDRENCALLBACK: int
TVI_ROOT: int
TVI_FIRST: int
TVI_LAST: int
TVI_SORT: int
TVM_INSERTITEMA: Incomplete
TVM_INSERTITEMW: Incomplete
TVM_INSERTITEM: Incomplete
TVM_DELETEITEM: Incomplete
TVM_EXPAND: Incomplete
TVE_COLLAPSE: int
TVE_EXPAND: int
TVE_TOGGLE: int
TVE_EXPANDPARTIAL: int
TVE_COLLAPSERESET: int
TVM_GETITEMRECT: Incomplete
TVM_GETCOUNT: Incomplete
TVM_GETINDENT: Incomplete
TVM_SETINDENT: Incomplete
TVM_GETIMAGELIST: Incomplete
TVSIL_NORMAL: int
TVSIL_STATE: int
TVM_SETIMAGELIST: Incomplete
TVM_GETNEXTITEM: Incomplete
TVGN_ROOT: int
TVGN_NEXT: int
TVGN_PREVIOUS: int
TVGN_PARENT: int
TVGN_CHILD: int
TVGN_FIRSTVISIBLE: int
TVGN_NEXTVISIBLE: int
TVGN_PREVIOUSVISIBLE: int
TVGN_DROPHILITE: int
TVGN_CARET: int
TVGN_LASTVISIBLE: int
TVM_SELECTITEM: Incomplete
TVM_GETITEMA: Incomplete
TVM_GETITEMW: Incomplete
TVM_GETITEM: Incomplete
TVM_SETITEMA: Incomplete
TVM_SETITEMW: Incomplete
TVM_SETITEM: Incomplete
TVM_EDITLABELA: Incomplete
TVM_EDITLABELW: Incomplete
TVM_EDITLABEL: Incomplete
TVM_GETEDITCONTROL: Incomplete
TVM_GETVISIBLECOUNT: Incomplete
TVM_HITTEST: Incomplete
TVHT_NOWHERE: int
TVHT_ONITEMICON: int
TVHT_ONITEMLABEL: int
TVHT_ONITEMINDENT: int
TVHT_ONITEMBUTTON: int
TVHT_ONITEMRIGHT: int
TVHT_ONITEMSTATEICON: int
TVHT_ABOVE: int
TVHT_BELOW: int
TVHT_TORIGHT: int
TVHT_TOLEFT: int
TVHT_ONITEM: Incomplete
TVM_CREATEDRAGIMAGE: Incomplete
TVM_SORTCHILDREN: Incomplete
TVM_ENSUREVISIBLE: Incomplete
TVM_SORTCHILDRENCB: Incomplete
TVM_ENDEDITLABELNOW: Incomplete
TVM_GETISEARCHSTRINGA: Incomplete
TVM_GETISEARCHSTRINGW: Incomplete
TVM_GETISEARCHSTRING: Incomplete
TVM_SETTOOLTIPS: Incomplete
TVM_GETTOOLTIPS: Incomplete
TVM_SETINSERTMARK: Incomplete
TVM_SETUNICODEFORMAT: Incomplete
TVM_GETUNICODEFORMAT: Incomplete
TVM_SETITEMHEIGHT: Incomplete
TVM_GETITEMHEIGHT: Incomplete
TVM_SETBKCOLOR: Incomplete
TVM_SETTEXTCOLOR: Incomplete
TVM_GETBKCOLOR: Incomplete
TVM_GETTEXTCOLOR: Incomplete
TVM_SETSCROLLTIME: Incomplete
TVM_GETSCROLLTIME: Incomplete
TVM_SETINSERTMARKCOLOR: Incomplete
TVM_GETINSERTMARKCOLOR: Incomplete
TVN_SELCHANGINGA: Incomplete
TVN_SELCHANGINGW: Incomplete
TVN_SELCHANGEDA: Incomplete
TVN_SELCHANGEDW: Incomplete
TVC_UNKNOWN: int
TVC_BYMOUSE: int
TVC_BYKEYBOARD: int
TVN_GETDISPINFOA: Incomplete
TVN_GETDISPINFOW: Incomplete
TVN_SETDISPINFOA: Incomplete
TVN_SETDISPINFOW: Incomplete
TVIF_DI_SETITEM: int
TVN_ITEMEXPANDINGA: Incomplete
TVN_ITEMEXPANDINGW: Incomplete
TVN_ITEMEXPANDEDA: Incomplete
TVN_ITEMEXPANDEDW: Incomplete
TVN_BEGINDRAGA: Incomplete
TVN_BEGINDRAGW: Incomplete
TVN_BEGINRDRAGA: Incomplete
TVN_BEGINRDRAGW: Incomplete
TVN_DELETEITEMA: Incomplete
TVN_DELETEITEMW: Incomplete
TVN_BEGINLABELEDITA: Incomplete
TVN_BEGINLABELEDITW: Incomplete
TVN_ENDLABELEDITA: Incomplete
TVN_ENDLABELEDITW: Incomplete
TVN_KEYDOWN: Incomplete
TVN_GETINFOTIPA: Incomplete
TVN_GETINFOTIPW: Incomplete
TVN_SINGLEEXPAND: Incomplete
TVN_SELCHANGING: Incomplete
TVN_SELCHANGED: Incomplete
TVN_GETDISPINFO: Incomplete
TVN_SETDISPINFO: Incomplete
TVN_ITEMEXPANDING: Incomplete
TVN_ITEMEXPANDED: Incomplete
TVN_BEGINDRAG: Incomplete
TVN_BEGINRDRAG: Incomplete
TVN_DELETEITEM: Incomplete
TVN_BEGINLABELEDIT: Incomplete
TVN_ENDLABELEDIT: Incomplete
TVN_GETINFOTIP: Incomplete
TVCDRF_NOIMAGES: int
WC_COMBOBOXEXA: str
WC_COMBOBOXEX: str
CBEIF_TEXT: int
CBEIF_IMAGE: int
CBEIF_SELECTEDIMAGE: int
CBEIF_OVERLAY: int
CBEIF_INDENT: int
CBEIF_LPARAM: int
CBEIF_DI_SETITEM: int
CBEM_INSERTITEMA: Incomplete
CBEM_SETIMAGELIST: Incomplete
CBEM_GETIMAGELIST: Incomplete
CBEM_GETITEMA: Incomplete
CBEM_SETITEMA: Incomplete
CBEM_GETCOMBOCONTROL: Incomplete
CBEM_GETEDITCONTROL: Incomplete
CBEM_SETEXSTYLE: Incomplete
CBEM_SETEXTENDEDSTYLE: Incomplete
CBEM_GETEXSTYLE: Incomplete
CBEM_GETEXTENDEDSTYLE: Incomplete
CBEM_SETUNICODEFORMAT: Incomplete
CBEM_GETUNICODEFORMAT: Incomplete
CBEM_HASEDITCHANGED: Incomplete
CBEM_INSERTITEMW: Incomplete
CBEM_SETITEMW: Incomplete
CBEM_GETITEMW: Incomplete
CBEM_INSERTITEM: Incomplete
CBEM_SETITEM: Incomplete
CBEM_GETITEM: Incomplete
CBES_EX_NOEDITIMAGE: int
CBES_EX_NOEDITIMAGEINDENT: int
CBES_EX_PATHWORDBREAKPROC: int
CBES_EX_NOSIZELIMIT: int
CBES_EX_CASESENSITIVE: int
CBEN_GETDISPINFO: Incomplete
CBEN_GETDISPINFOA: Incomplete
CBEN_INSERTITEM: Incomplete
CBEN_DELETEITEM: Incomplete
CBEN_BEGINEDIT: Incomplete
CBEN_ENDEDITA: Incomplete
CBEN_ENDEDITW: Incomplete
CBEN_GETDISPINFOW: Incomplete
CBEN_DRAGBEGINA: Incomplete
CBEN_DRAGBEGINW: Incomplete
CBEN_DRAGBEGIN: Incomplete
CBEN_ENDEDIT: Incomplete
CBENF_KILLFOCUS: int
CBENF_RETURN: int
CBENF_ESCAPE: int
CBENF_DROPDOWN: int
CBEMAXSTRLEN: int
WC_TABCONTROLA: str
WC_TABCONTROL: str
TCS_SCROLLOPPOSITE: int
TCS_BOTTOM: int
TCS_RIGHT: int
TCS_MULTISELECT: int
TCS_FLATBUTTONS: int
TCS_FORCEICONLEFT: int
TCS_FORCELABELLEFT: int
TCS_HOTTRACK: int
TCS_VERTICAL: int
TCS_TABS: int
TCS_BUTTONS: int
TCS_SINGLELINE: int
TCS_MULTILINE: int
TCS_RIGHTJUSTIFY: int
TCS_FIXEDWIDTH: int
TCS_RAGGEDRIGHT: int
TCS_FOCUSONBUTTONDOWN: int
TCS_OWNERDRAWFIXED: int
TCS_TOOLTIPS: int
TCS_FOCUSNEVER: int
TCS_EX_FLATSEPARATORS: int
TCS_EX_REGISTERDROP: int
TCM_GETIMAGELIST: Incomplete
TCM_SETIMAGELIST: Incomplete
TCM_GETITEMCOUNT: Incomplete
TCIF_TEXT: int
TCIF_IMAGE: int
TCIF_RTLREADING: int
TCIF_PARAM: int
TCIF_STATE: int
TCIS_BUTTONPRESSED: int
TCIS_HIGHLIGHTED: int
TCM_GETITEMA: Incomplete
TCM_GETITEMW: Incomplete
TCM_GETITEM: Incomplete
TCM_SETITEMA: Incomplete
TCM_SETITEMW: Incomplete
TCM_SETITEM: Incomplete
TCM_INSERTITEMA: Incomplete
TCM_INSERTITEMW: Incomplete
TCM_INSERTITEM: Incomplete
TCM_DELETEITEM: Incomplete
TCM_DELETEALLITEMS: Incomplete
TCM_GETITEMRECT: Incomplete
TCM_GETCURSEL: Incomplete
TCM_SETCURSEL: Incomplete
TCHT_NOWHERE: int
TCHT_ONITEMICON: int
TCHT_ONITEMLABEL: int
TCHT_ONITEM: Incomplete
TCM_HITTEST: Incomplete
TCM_SETITEMEXTRA: Incomplete
TCM_ADJUSTRECT: Incomplete
TCM_SETITEMSIZE: Incomplete
TCM_REMOVEIMAGE: Incomplete
TCM_SETPADDING: Incomplete
TCM_GETROWCOUNT: Incomplete
TCM_GETTOOLTIPS: Incomplete
TCM_SETTOOLTIPS: Incomplete
TCM_GETCURFOCUS: Incomplete
TCM_SETCURFOCUS: Incomplete
TCM_SETMINTABWIDTH: Incomplete
TCM_DESELECTALL: Incomplete
TCM_HIGHLIGHTITEM: Incomplete
TCM_SETEXTENDEDSTYLE: Incomplete
TCM_GETEXTENDEDSTYLE: Incomplete
TCM_SETUNICODEFORMAT: Incomplete
TCM_GETUNICODEFORMAT: Incomplete
TCN_KEYDOWN: Incomplete
ANIMATE_CLASSA: str
ANIMATE_CLASS: str
ACS_CENTER: int
ACS_TRANSPARENT: int
ACS_AUTOPLAY: int
ACS_TIMER: int
ACM_OPENA: Incomplete
ACM_OPENW: Incomplete
ACM_OPEN: Incomplete
ACM_PLAY: Incomplete
ACM_STOP: Incomplete
ACN_START: int
ACN_STOP: int
MONTHCAL_CLASSA: str
MONTHCAL_CLASS: str
MCM_FIRST: int
MCM_GETCURSEL: Incomplete
MCM_SETCURSEL: Incomplete
MCM_GETMAXSELCOUNT: Incomplete
MCM_SETMAXSELCOUNT: Incomplete
MCM_GETSELRANGE: Incomplete
MCM_SETSELRANGE: Incomplete
MCM_GETMONTHRANGE: Incomplete
MCM_SETDAYSTATE: Incomplete
MCM_GETMINREQRECT: Incomplete
MCM_SETCOLOR: Incomplete
MCM_GETCOLOR: Incomplete
MCSC_BACKGROUND: int
MCSC_TEXT: int
MCSC_TITLEBK: int
MCSC_TITLETEXT: int
MCSC_MONTHBK: int
MCSC_TRAILINGTEXT: int
MCM_SETTODAY: Incomplete
MCM_GETTODAY: Incomplete
MCM_HITTEST: Incomplete
MCHT_TITLE: int
MCHT_CALENDAR: int
MCHT_TODAYLINK: int
MCHT_NEXT: int
MCHT_PREV: int
MCHT_NOWHERE: int
MCHT_TITLEBK: int
MCHT_TITLEMONTH: Incomplete
MCHT_TITLEYEAR: Incomplete
MCHT_TITLEBTNNEXT: Incomplete
MCHT_TITLEBTNPREV: Incomplete
MCHT_CALENDARBK: int
MCHT_CALENDARDATE: Incomplete
MCHT_CALENDARDATENEXT: Incomplete
MCHT_CALENDARDATEPREV: Incomplete
MCHT_CALENDARDAY: Incomplete
MCHT_CALENDARWEEKNUM: Incomplete
MCM_SETFIRSTDAYOFWEEK: Incomplete
MCM_GETFIRSTDAYOFWEEK: Incomplete
MCM_GETRANGE: Incomplete
MCM_SETRANGE: Incomplete
MCM_GETMONTHDELTA: Incomplete
MCM_SETMONTHDELTA: Incomplete
MCM_GETMAXTODAYWIDTH: Incomplete
MCM_SETUNICODEFORMAT: Incomplete
MCM_GETUNICODEFORMAT: Incomplete
MCN_SELCHANGE: Incomplete
MCN_GETDAYSTATE: Incomplete
MCN_SELECT: Incomplete
MCS_DAYSTATE: int
MCS_MULTISELECT: int
MCS_WEEKNUMBERS: int
MCS_NOTODAYCIRCLE: int
MCS_NOTODAY: int
GMR_VISIBLE: int
GMR_DAYSTATE: int
DATETIMEPICK_CLASSA: str
DATETIMEPICK_CLASS: str
DTM_FIRST: int
DTM_GETSYSTEMTIME: Incomplete
DTM_SETSYSTEMTIME: Incomplete
DTM_GETRANGE: Incomplete
DTM_SETRANGE: Incomplete
DTM_SETFORMATA: Incomplete
DTM_SETFORMATW: Incomplete
DTM_SETFORMAT: Incomplete
DTM_SETMCCOLOR: Incomplete
DTM_GETMCCOLOR: Incomplete
DTM_GETMONTHCAL: Incomplete
DTM_SETMCFONT: Incomplete
DTM_GETMCFONT: Incomplete
DTS_UPDOWN: int
DTS_SHOWNONE: int
DTS_SHORTDATEFORMAT: int
DTS_LONGDATEFORMAT: int
DTS_TIMEFORMAT: int
DTS_APPCANPARSE: int
DTS_RIGHTALIGN: int
DTN_DATETIMECHANGE: Incomplete
DTN_USERSTRINGA: Incomplete
DTN_USERSTRINGW: Incomplete
DTN_USERSTRING: Incomplete
DTN_WMKEYDOWNA: Incomplete
DTN_WMKEYDOWNW: Incomplete
DTN_WMKEYDOWN: Incomplete
DTN_FORMATA: Incomplete
DTN_FORMATW: Incomplete
DTN_FORMAT: Incomplete
DTN_FORMATQUERYA: Incomplete
DTN_FORMATQUERYW: Incomplete
DTN_FORMATQUERY: Incomplete
DTN_DROPDOWN: Incomplete
DTN_CLOSEUP: Incomplete
GDTR_MIN: int
GDTR_MAX: int
GDT_ERROR: int
GDT_VALID: int
GDT_NONE: int
IPM_CLEARADDRESS: Incomplete
IPM_SETADDRESS: Incomplete
IPM_GETADDRESS: Incomplete
IPM_SETRANGE: Incomplete
IPM_SETFOCUS: Incomplete
IPM_ISBLANK: Incomplete
WC_IPADDRESSA: str
WC_IPADDRESS: str
IPN_FIELDCHANGED: Incomplete
WC_PAGESCROLLERA: str
WC_PAGESCROLLER: str
PGS_VERT: int
PGS_HORZ: int
PGS_AUTOSCROLL: int
PGS_DRAGNDROP: int
PGF_INVISIBLE: int
PGF_NORMAL: int
PGF_GRAYED: int
PGF_DEPRESSED: int
PGF_HOT: int
PGB_TOPORLEFT: int
PGB_BOTTOMORRIGHT: int
PGM_SETCHILD: Incomplete
PGM_RECALCSIZE: Incomplete
PGM_FORWARDMOUSE: Incomplete
PGM_SETBKCOLOR: Incomplete
PGM_GETBKCOLOR: Incomplete
PGM_SETBORDER: Incomplete
PGM_GETBORDER: Incomplete
PGM_SETPOS: Incomplete
PGM_GETPOS: Incomplete
PGM_SETBUTTONSIZE: Incomplete
PGM_GETBUTTONSIZE: Incomplete
PGM_GETBUTTONSTATE: Incomplete
PGM_GETDROPTARGET: Incomplete
PGN_SCROLL: Incomplete
PGF_SCROLLUP: int
PGF_SCROLLDOWN: int
PGF_SCROLLLEFT: int
PGF_SCROLLRIGHT: int
PGK_SHIFT: int
PGK_CONTROL: int
PGK_MENU: int
PGN_CALCSIZE: Incomplete
PGF_CALCWIDTH: int
PGF_CALCHEIGHT: int
WC_NATIVEFONTCTLA: str
WC_NATIVEFONTCTL: str
NFS_EDIT: int
NFS_STATIC: int
NFS_LISTCOMBO: int
NFS_BUTTON: int
NFS_ALL: int
WM_MOUSEHOVER: int
WM_MOUSELEAVE: int
TME_HOVER: int
TME_LEAVE: int
TME_QUERY: int
TME_CANCEL: int
HOVER_DEFAULT: int
WSB_PROP_CYVSCROLL: int
WSB_PROP_CXHSCROLL: int
WSB_PROP_CYHSCROLL: int
WSB_PROP_CXVSCROLL: int
WSB_PROP_CXHTHUMB: int
WSB_PROP_CYVTHUMB: int
WSB_PROP_VBKGCOLOR: int
WSB_PROP_HBKGCOLOR: int
WSB_PROP_VSTYLE: int
WSB_PROP_HSTYLE: int
WSB_PROP_WINSTYLE: int
WSB_PROP_PALETTE: int
WSB_PROP_MASK: int
FSB_FLAT_MODE: int
FSB_ENCARTA_MODE: int
FSB_REGULAR_MODE: int

def INDEXTOOVERLAYMASK(i): ...
def INDEXTOSTATEIMAGEMASK(i): ...
