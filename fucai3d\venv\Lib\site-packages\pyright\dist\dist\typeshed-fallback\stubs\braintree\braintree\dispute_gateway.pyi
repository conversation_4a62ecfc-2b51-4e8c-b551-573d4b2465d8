from _typeshed import Incomplete

from braintree.dispute import Dispute
from braintree.error_result import ErrorResult
from braintree.successful_result import SuccessfulResult

class DisputeGateway:
    gateway: Incomplete
    config: Incomplete
    def __init__(self, gateway) -> None: ...
    def accept(self, dispute_id: str) -> SuccessfulResult | ErrorResult: ...
    def add_file_evidence(self, dispute_id: str, document_upload_id_or_request) -> SuccessfulResult | ErrorResult | None: ...
    def add_text_evidence(self, dispute_id: str, content_or_request) -> SuccessfulResult | ErrorResult | None: ...
    def finalize(self, dispute_id: str) -> SuccessfulResult | ErrorResult: ...
    def find(self, dispute_id: str) -> Dispute: ...
    def remove_evidence(self, dispute_id: str, evidence_id: int) -> SuccessfulResult | ErrorResult: ...
    search_criteria: dict[Incomplete, Incomplete]
    def search(self, *query) -> SuccessfulResult: ...
