from datetime import date
from typing_extensions import Self, TypeAlias

__tracebackhide__: bool

_Numeric: TypeAlias = date | int | float

class NumericMixin:
    def is_zero(self) -> Self: ...
    def is_not_zero(self) -> Self: ...
    def is_nan(self) -> Self: ...
    def is_not_nan(self) -> Self: ...
    def is_inf(self) -> Self: ...
    def is_not_inf(self) -> Self: ...
    def is_greater_than(self, other: _Numeric) -> Self: ...
    def is_greater_than_or_equal_to(self, other: _Numeric) -> Self: ...
    def is_less_than(self, other: _Numeric) -> Self: ...
    def is_less_than_or_equal_to(self, other: _Numeric) -> Self: ...
    def is_positive(self) -> Self: ...
    def is_negative(self) -> Self: ...
    def is_between(self, low: _Numeric, high: _Numeric) -> Self: ...
    def is_not_between(self, low: _Numeric, high: _Numeric) -> Self: ...
    def is_close_to(self, other: _Numeric, tolerance: _Numeric) -> Self: ...
    def is_not_close_to(self, other: _Numeric, tolerance: _Numeric) -> Self: ...
