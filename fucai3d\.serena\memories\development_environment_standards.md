# 福彩3D项目开发环境标准

## 关键发现：Augment工具链权限要求

### 必要环境配置
1. **Cursor IDE**: 必须以管理员模式启动
2. **Python**: 3.11.9 或更高版本
3. **权限**: 管理员权限确保Augment工具链正常
4. **数据库**: SQLite，包含8,359条真实历史数据

### 技术原理
- **MCP工具链**: Augment使用Model Context Protocol进行进程管理
- **Windows进程创建**: 使用subprocess.CREATE_NO_WINDOW标志
- **Job Objects**: 管理进程树，需要足够权限
- **权限继承**: 子进程继承父进程（Cursor）的权限级别

### 环境验证清单
- [ ] Cursor以管理员模式启动
- [ ] Python环境正常 (`python --version`)
- [ ] 数据库访问正常 (`sqlite3 data/lottery.db`)
- [ ] Augment工具链正常 (无^C中断)
- [ ] 编译测试通过 (`python -m py_compile`)

### 故障排除
- **^C中断问题**: 重新以管理员模式启动Cursor
- **权限验证**: `whoami /groups | findstr "Administrators"`
- **PowerShell策略**: `Get-ExecutionPolicy -List`
- **环境重置**: 关闭Cursor，以管理员模式重新启动

### 团队协作要求
- **标准配置**: 所有团队成员必须使用管理员模式
- **文档参考**: 终端问题解决方案_最终版.md
- **培训要求**: 新成员必须了解权限配置要求
- **质量保证**: 每次开发前验证环境配置