from consolemenu.console_menu import MenuItem
from consolemenu.format import MenuBorderStyleType
from consolemenu.format.menu_borders import MenuBorderStyle as MenuBorderStyle, MenuBorderStyleFactory as MenuBorderStyleFactory
from consolemenu.format.menu_style import <PERSON>u<PERSON>ty<PERSON> as <PERSON>uStyle
from consolemenu.menu_component import (
    Dimension as Dimen<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    MenuItemsSection as MenuItemsSection,
    MenuPrompt as MenuPrompt,
    MenuTextSection as MenuTextSection,
)

class MenuFormatBuilder:
    def __init__(self, max_dimension: Dimension | None = None) -> None: ...
    def set_border_style(self, border_style: MenuBorderStyle) -> MenuFormatBuilder: ...
    def set_border_style_type(self, border_style_type: MenuBorderStyleType) -> MenuFormatBuilder: ...
    def set_border_style_factory(self, border_style_factory: MenuBorderStyleFactory) -> MenuFormatBuilder: ...
    def set_bottom_margin(self, bottom_margin: int) -> MenuFormatBuilder: ...
    def set_left_margin(self, left_margin: int) -> MenuFormatBuilder: ...
    def set_right_margin(self, right_margin: int) -> MenuFormatBuilder: ...
    def set_top_margin(self, top_margin: int) -> MenuFormatBuilder: ...
    def set_title_align(self, align: str = "left") -> MenuFormatBuilder: ...
    def set_subtitle_align(self, align: str = "left") -> MenuFormatBuilder: ...
    def set_header_left_padding(self, x: int) -> MenuFormatBuilder: ...
    def set_header_right_padding(self, x: int) -> MenuFormatBuilder: ...
    def set_header_bottom_padding(self, x: int) -> MenuFormatBuilder: ...
    def set_header_top_padding(self, x: int) -> MenuFormatBuilder: ...
    def show_header_bottom_border(self, flag: bool) -> MenuFormatBuilder: ...
    def set_footer_left_padding(self, x: int) -> MenuFormatBuilder: ...
    def set_footer_right_padding(self, x: int) -> MenuFormatBuilder: ...
    def set_footer_bottom_padding(self, x: int) -> MenuFormatBuilder: ...
    def set_footer_top_padding(self, x: int) -> MenuFormatBuilder: ...
    def set_items_left_padding(self, x: int) -> MenuFormatBuilder: ...
    def set_items_right_padding(self, x: int) -> MenuFormatBuilder: ...
    def set_items_bottom_padding(self, x: int) -> MenuFormatBuilder: ...
    def set_items_top_padding(self, x: int) -> MenuFormatBuilder: ...
    def show_item_bottom_border(self, item_text: str, flag: bool) -> MenuFormatBuilder: ...
    def show_item_top_border(self, item_text: str, flag: bool) -> MenuFormatBuilder: ...
    def set_prologue_text_align(self, align: str = "left") -> MenuFormatBuilder: ...
    def show_prologue_top_border(self, flag: bool) -> MenuFormatBuilder: ...
    def show_prologue_bottom_border(self, flag: bool) -> MenuFormatBuilder: ...
    def set_epilogue_text_align(self, align: str = "left") -> MenuFormatBuilder: ...
    def show_epilogue_top_border(self, flag: bool) -> MenuFormatBuilder: ...
    def show_epilogue_bottom_border(self, flag: bool) -> MenuFormatBuilder: ...
    def set_prompt(self, prompt: MenuPrompt) -> MenuFormatBuilder: ...
    def clear_data(self) -> None: ...
    def format(
        self,
        title: str | None = None,
        subtitle: str | None = None,
        prologue_text: str | None = None,
        epilogue_text: str | None = None,
        items: list[MenuItem] | None = None,
    ) -> str: ...
