from typing import Final

DEFAULT_ROW_HEIGHT: Final[float]
BASE_COL_WIDTH: Final = 8
DEFAULT_COLUMN_WIDTH: Final = 13
DEFAULT_LEFT_MARGIN: Final[float]
DEFAULT_TOP_MARGIN: Final[float]
DEFAULT_HEADER: Final[float]

def inch_to_dxa(value): ...
def dxa_to_inch(value): ...
def dxa_to_cm(value): ...
def cm_to_dxa(value): ...
def pixels_to_EMU(value): ...
def EMU_to_pixels(value): ...
def cm_to_EMU(value): ...
def EMU_to_cm(value): ...
def inch_to_EMU(value): ...
def EMU_to_inch(value): ...
def pixels_to_points(value, dpi: int = 96): ...
def points_to_pixels(value, dpi: int = 96): ...
def degrees_to_angle(value): ...
def angle_to_degrees(value): ...
def short_color(color): ...
