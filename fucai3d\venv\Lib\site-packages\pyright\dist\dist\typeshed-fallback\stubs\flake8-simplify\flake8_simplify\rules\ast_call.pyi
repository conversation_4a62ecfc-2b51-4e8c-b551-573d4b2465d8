import ast
import logging

from flake8_simplify.utils import Call

logger: logging.Logger

def get_sim115(node: Call) -> list[tuple[int, int, str]]: ...
def get_sim901(node: ast.Call) -> list[tuple[int, int, str]]: ...
def get_sim905(node: ast.Call) -> list[tuple[int, int, str]]: ...
def get_sim906(node: ast.Call) -> list[tuple[int, int, str]]: ...
def get_sim910(node: Call) -> list[tuple[int, int, str]]: ...
def get_sim911(node: ast.AST) -> list[tuple[int, int, str]]: ...
