# Not available at runtime. Contains type definitions that are otherwise not exposed and not part of a specific module.
from _typeshed import Incomplete, Unused
from collections.abc import Iterable, Sequence
from typing import Literal, NoReturn, SupportsIndex, final, overload
from typing_extensions import Self, TypeAlias, deprecated

from win32.lib.pywintypes import TimeType

_TwoIntSequence: TypeAlias = Sequence[int]
_FourIntSequence: TypeAlias = Sequence[int]
# Is actually pywin.mfc.DocTemplate
DocTemplate: TypeAlias = Incomplete

class ArgNotFound: ...
class PyOleEmpty: ...
class PyOleMissing: ...
class PyOleNothing: ...

class PyDSCAPSType:
    @property
    def dwFlags(self): ...
    @property
    def dwFreeHw3DAllBuffers(self): ...
    @property
    def dwFreeHw3DStaticBuffers(self): ...
    @property
    def dwFreeHw3DStreamingBuffers(self): ...
    @property
    def dwFreeHwMemBytes(self): ...
    @property
    def dwFreeHwMixingAllBuffers(self): ...
    @property
    def dwFreeHwMixingStaticBuffers(self): ...
    @property
    def dwFreeHwMixingStreamingBuffers(self): ...
    @property
    def dwMaxContigFreeHwMemBytes(self): ...
    @property
    def dwMaxHw3DAllBuffers(self): ...
    @property
    def dwMaxHw3DStaticBuffers(self): ...
    @property
    def dwMaxHw3DStreamingBuffers(self): ...
    @property
    def dwMaxHwMixingAllBuffers(self): ...
    @property
    def dwMaxHwMixingStaticBuffers(self): ...
    @property
    def dwMaxHwMixingStreamingBuffers(self): ...
    @property
    def dwMaxSecondarySampleRate(self): ...
    @property
    def dwMinSecondarySampleRate(self): ...
    @property
    def dwPlayCpuOverheadSwBuffers(self): ...
    @property
    def dwPrimaryBuffers(self): ...
    @property
    def dwTotalHwMemBytes(self): ...
    @property
    def dwUnlockTransferRateHwBuffers(self): ...

class PyDSCBCAPSType:
    @property
    def dwBufferBytes(self): ...
    @property
    def dwFlags(self): ...

class PyDSCCAPSType:
    @property
    def dwChannels(self): ...
    @property
    def dwFlags(self): ...
    @property
    def dwFormats(self): ...

@final
class PyNCB:
    @property
    def Bufflen(self): ...
    @property
    def Callname(self) -> str: ...
    @Callname.setter
    def Callname(self, value: str | bytes) -> None: ...
    Cmd_cplt: int
    Command: int
    Event: int
    Lana_num: int
    Lsn: int
    @property
    def Name(self) -> str: ...
    @Name.setter
    def Name(self, value: str | bytes) -> None: ...
    Num: int
    Post: int
    def Reset(self) -> None: ...
    Retcode: int
    Rto: int
    Sto: int

class COMMTIMEOUTS: ...
class CopyProgressRoutine: ...

class DOCINFO:
    @property
    def DocName(self) -> str: ...
    @property
    def Output(self) -> str: ...
    @property
    def DataType(self) -> str: ...
    @property
    def Type(self): ...

class ExportCallback: ...

class FORM_INFO_1:
    @property
    def Flags(self): ...
    @property
    def Name(self) -> str: ...
    @property
    def Size(self): ...
    @property
    def ImageableArea(self): ...

class ImportCallback: ...

# Note: Don't use these, use `int` instead. Or an overload with a deprecation message on the tuple param.
# We're only keeping these here as a reminder when typing from source code.
# Deprecated: Support for passing 2 integers to create a 64bit value is deprecated - pass a long instead
LARGE_INTEGER: TypeAlias = int | tuple[int, int]
ULARGE_INTEGER: TypeAlias = int | tuple[int, int]

class NCB:
    @property
    def Command(self): ...
    @property
    def Retcode(self): ...
    @property
    def Lsn(self): ...
    @property
    def Num(self): ...
    @property
    def Bufflen(self): ...
    @property
    def Callname(self) -> str: ...
    @property
    def Name(self) -> str: ...
    @property
    def Rto(self) -> str: ...
    @property
    def Sto(self) -> str: ...
    @property
    def Lana_num(self): ...
    @property
    def Cmd_cplt(self): ...
    @property
    def Event(self): ...
    @property
    def Post(self): ...

class PRINTER_DEFAULTS:
    @property
    def pDatatype(self) -> str: ...
    @property
    def pDevMode(self) -> PyDEVMODE: ...
    @property
    def DesiredAccess(self): ...

class PyACL:
    def Initialize(self) -> None: ...
    def IsValid(self) -> bool: ...
    @overload
    @deprecated(
        """\
Early versions of this function supported only two arguments. \
This has been deprecated in preference of the three argument version, \
which reflects the win32 API and the new functions in this module."""
    )
    def AddAccessAllowedAce(self, access: int, sid: PySID, /) -> None: ...
    @overload
    def AddAccessAllowedAce(self, revision: int, access: int, sid: PySID, /) -> None: ...
    def AddAccessAllowedAceEx(self, revision: int, aceflags: int, access: int, sid: PySID, /) -> None: ...
    def AddAccessAllowedObjectAce(
        self, AceRevision, AceFlags, AccessMask, ObjectTypeGuid: PyIID, InheritedObjectTypeGuid: PyIID, sid: PySID, /
    ) -> None: ...
    @overload
    @deprecated(
        """\
Early versions of this function supported only two arguments. \
This has been deprecated in preference of the three argument version, \
which reflects the win32 API and the new functions in this module."""
    )
    def AddAccessDeniedAce(self, access: int, sid: PySID, /) -> None: ...
    @overload
    def AddAccessDeniedAce(self, revision: int, access: int, sid: PySID, /) -> None: ...
    def AddAccessDeniedAceEx(self, revision: int, aceflags: int, access: int, sid: PySID, /) -> None: ...
    def AddMandatoryAce(self, AceRevision, AceFlags, MandatoryPolicy, LabelSid: PySID, /) -> None: ...
    def AddAuditAccessAce(self, dwAceRevision, dwAccessMask, sid: PySID, bAuditSuccess, bAuditFailure, /) -> None: ...
    def AddAuditAccessAceEx(self, dwAceRevision, AceFlags, dwAccessMask, sid: PySID, bAuditSuccess, bAuditFailure, /) -> None: ...
    def AddAuditAccessObjectAce(
        self,
        dwAceRevision,
        AceFlags,
        dwAccessMask,
        ObjectTypeGuid: PyIID,
        InheritedObjectTypeGuid: PyIID,
        sid: PySID,
        bAuditSuccess,
        bAuditFailure,
        /,
    ) -> None: ...
    def GetAclSize(self): ...
    def GetAclRevision(self): ...
    def GetAceCount(self) -> int: ...
    def GetAce(self, index: int, /) -> tuple[tuple[int, int], int, PySID]: ...
    def DeleteAce(self, index: int, /) -> None: ...
    def GetEffectiveRightsFromAcl(self, trustee: PyTRUSTEE | dict[str, int | PySID], /) -> int: ...
    def GetAuditedPermissionsFromAcl(self, trustee: PyTRUSTEE, /) -> tuple[Incomplete, Incomplete]: ...
    def SetEntriesInAcl(self, obexpl_list: tuple[dict[str, int | dict[str, int | PySID]], ...], /) -> PyACL: ...
    def GetExplicitEntriesFromAcl(self) -> tuple[dict[str, int | dict[str, int | PySID]]] | None: ...

class PyBITMAP:
    @property
    def bmType(self) -> int: ...
    @property
    def bmWidth(self) -> int: ...
    @property
    def bmHeight(self) -> int: ...
    @property
    def bmWidthBytes(self) -> int: ...
    @property
    def bmPlanes(self) -> int: ...

class PyBLENDFUNCTION: ...
class PyCEHANDLE: ...

class PyCERTSTORE:
    @property
    def HCERTSTORE(self): ...
    @overload
    def CertCloseStore(self) -> None: ...
    @overload
    @deprecated(
        """\
`Flags` argument has been deprecated as it is likely to crash the process if \
`CERT_CLOSE_STORE_FORCE_FLAG` is specified. The underlying function is now \
always called with `CERT_CLOSE_STORE_CHECK_FLAG`, and support for this \
param will be dropped at some point in the future."""
    )
    def CertCloseStore(self, Flags: int) -> None: ...
    def CertControlStore(self, Flags, CtrlType, CtrlPara: int) -> None: ...
    def CertEnumCertificatesInStore(self) -> list[PyCERT_CONTEXT]: ...
    def CertEnumCTLsInStore(self) -> list[PyCTL_CONTEXT]: ...
    def CertSaveStore(self, MsgAndCertEncodingType, SaveAs, SaveTo, SaveToPara: str | int, Flags=...) -> None: ...
    def CertAddEncodedCertificateToStore(self, CertEncodingType, CertEncoded, AddDisposition) -> PyCERT_CONTEXT: ...
    def CertAddCertificateContextToStore(self, CertContext: PyCERT_CONTEXT, AddDisposition) -> PyCERT_CONTEXT: ...
    def CertAddCertificateLinkToStore(self, CertContext: PyCERT_CONTEXT, AddDisposition) -> PyCERT_CONTEXT: ...
    def CertAddCTLContextToStore(self, CtlContext: PyCTL_CONTEXT, AddDisposition) -> PyCTL_CONTEXT: ...
    def CertAddCTLLinkToStore(self, CtlContext: PyCTL_CONTEXT, AddDisposition) -> PyCTL_CONTEXT: ...
    def CertAddStoreToCollection(self, SiblingStore: PyCERTSTORE, UpdateFlag: int = ..., Priority: int = ...) -> None: ...
    def CertRemoveStoreFromCollection(self, SiblingStore: PyCERTSTORE) -> None: ...
    def PFXExportCertStoreEx(self, Password: Incomplete | None = ..., Flags=...): ...

class PyCERT_ALT_NAME_ENTRY: ...
class PyCERT_ALT_NAME_INFO: ...

class PyCERT_AUTHORITY_KEY_ID_INFO:
    @property
    def KeyId(self): ...
    @property
    def CertIssuer(self): ...
    @property
    def CertSerialNumber(self): ...

class PyCERT_BASIC_CONSTRAINTS2_INFO:
    @property
    def fCA(self): ...
    @property
    def fPathLenConstraint(self): ...
    @property
    def PathLenConstraint(self): ...

class PyCERT_BASIC_CONSTRAINTS_INFO:
    @property
    def SubjectType(self) -> PyCRYPT_BIT_BLOB: ...
    @property
    def fPathLenConstraint(self): ...
    @property
    def PathLenConstraint(self): ...
    @property
    def SubtreesConstraint(self): ...

class PyCERT_CONTEXT:
    @property
    def HANDLE(self): ...
    @property
    def CertStore(self) -> PyCERTSTORE: ...
    @property
    def CertEncoded(self): ...
    @property
    def CertEncodingType(self): ...
    @property
    def Version(self): ...
    @property
    def Subject(self) -> str: ...
    @property
    def Issuer(self) -> str: ...
    @property
    def NotBefore(self) -> TimeType: ...
    @property
    def NotAfter(self) -> TimeType: ...
    @property
    def SignatureAlgorithm(self): ...
    @property
    def Extension(self) -> tuple[PyCERT_EXTENSION, ...]: ...
    @property
    def SubjectPublicKeyInfo(self) -> PyCERT_PUBLIC_KEY_INFO: ...
    @property
    def SerialNumber(self): ...
    def CertFreeCertificateContext(self) -> None: ...
    def CertEnumCertificateContextProperties(self) -> list[Incomplete]: ...
    def CryptAcquireCertificatePrivateKey(self, Flags: int = ...) -> tuple[Incomplete, PyCRYPTPROV]: ...
    def CertGetIntendedKeyUsage(self): ...
    def CertGetEnhancedKeyUsage(self, Flags: int = ...): ...
    def CertSerializeCertificateStoreElement(self, Flags: int = ...) -> str: ...
    def CertVerifySubjectCertificateContext(self, Issuer: PyCERT_CONTEXT, Flags): ...
    def CertDeleteCertificateFromStore(self) -> None: ...
    def CertGetCertificateContextProperty(self, PropId): ...
    def CertSetCertificateContextProperty(self, PropId, Data, Flags: int = ...) -> None: ...

class PyCERT_EXTENSION:
    @property
    def ObjId(self): ...
    @property
    def Critical(self): ...
    @property
    def Value(self): ...

class PyCERT_KEY_ATTRIBUTES_INFO:
    @property
    def KeyId(self): ...
    @property
    def IntendedKeyUsage(self) -> PyCRYPT_BIT_BLOB: ...
    @property
    def PrivateKeyUsagePeriod(self): ...

class PyCERT_NAME_INFO: ...
class PyCERT_NAME_VALUE: ...
class PyCERT_OTHER_NAME: ...

class PyCERT_POLICY_INFO:
    @property
    def PolicyIdentifier(self): ...
    @property
    def PolicyQualifier(self): ...

class PyCERT_PUBLIC_KEY_INFO:
    @property
    def Algorithm(self) -> PyCRYPT_ALGORITHM_IDENTIFIER: ...
    @property
    def PublicKey(self) -> PyCRYPT_BIT_BLOB: ...

class PyCOMSTAT:
    @property
    def cbInQue(self) -> int: ...
    @property
    def cbOutQue(self) -> int: ...
    @property
    def fCtsHold(self) -> int: ...
    @property
    def fDsrHold(self) -> int: ...
    @property
    def fRlsdHold(self) -> int: ...
    @property
    def fXoffHold(self) -> int: ...
    @property
    def fXoffSent(self) -> int: ...
    @property
    def fEof(self) -> int: ...
    @property
    def fTxim(self) -> int: ...
    @property
    def fReserved(self) -> int: ...

@final
class PyCOORD:
    def __init__(self, X: int = ..., Y: int = ...) -> None: ...
    X: int
    Y: int

class PyCREDENTIAL:
    @property
    def Flags(self): ...
    @property
    def Type(self): ...
    @property
    def TargetName(self) -> str: ...
    @property
    def Comment(self) -> str: ...
    @property
    def LastWritten(self) -> TimeType: ...
    @property
    def CredentialBlob(self) -> str: ...
    @property
    def Persist(self): ...
    @property
    def Attributes(self): ...
    @property
    def TargetAlias(self) -> str: ...
    @property
    def UserName(self) -> str: ...

class PyCREDENTIAL_ATTRIBUTE:
    @property
    def Keyword(self) -> str: ...
    @property
    def Flags(self): ...
    @property
    def Value(self): ...

class PyCREDENTIAL_TARGET_INFORMATION:
    @property
    def TargetName(self) -> str: ...
    @property
    def NetbiosServerName(self) -> str: ...
    @property
    def DnsServerName(self) -> str: ...
    @property
    def NetbiosDomainName(self) -> str: ...
    @property
    def DnsDomainName(self) -> str: ...
    @property
    def DnsTreeName(self) -> str: ...
    @property
    def PackageName(self) -> str: ...
    @property
    def Flags(self): ...
    @property
    def CredTypes(self) -> tuple[Incomplete, ...]: ...

class PyCREDUI_INFO:
    @property
    def Parent(self) -> int: ...
    @property
    def MessageText(self) -> str: ...
    @property
    def CaptionText(self) -> str: ...
    @property
    def Banner(self) -> int: ...

class PyCRYPTHASH:
    def CryptDestroyHash(self) -> None: ...
    def CryptDuplicateHash(self, Flags: int = ...) -> PyCRYPTHASH: ...
    def CryptHashData(self, Data: str, Flags: int = ...) -> None: ...
    def CryptHashSessionKey(self, Key: PyCRYPTKEY, Flags: int = ...) -> None: ...
    def CryptSignHash(self, KeySpec, Flags: int = ...) -> str: ...
    def CryptVerifySignature(self, Signature: str, PubKey: PyCRYPTKEY, Flags: int = ...) -> None: ...
    def CryptGetHashParam(self, Param, Flags: int = ...): ...

class PyCRYPTKEY:
    @property
    def HCRYPTPROV(self): ...
    @property
    def HCRYPTKEY(self): ...
    def CryptDestroyKey(self) -> None: ...
    def CryptExportKey(self, ExpKey: PyCRYPTKEY, BlobType, Flags: int = ...): ...
    def CryptGetKeyParam(self, Param, Flags: int = ...): ...
    def CryptDuplicateKey(self, Reserved: int = ..., Flags: int = ...) -> PyCRYPTKEY: ...
    def CryptEncrypt(self, Final, Data, Hash: PyCRYPTHASH | None = ..., Flags: int = ...): ...
    def CryptDecrypt(self, Final, Data, Hash: PyCRYPTHASH | None = ..., Flags: int = ...): ...

class PyCRYPTMSG:
    @property
    def HCRYPTMSG(self): ...
    def CryptMsgClose(self) -> None: ...

class PyCRYPTPROTECT_PROMPTSTRUCT: ...

class PyCRYPTPROV:
    def CryptReleaseContext(self, Flags: int = ...) -> None: ...
    def CryptGenKey(self, Algid, Flags, KeyLen: int = ...) -> PyCRYPTKEY: ...
    def CryptGetProvParam(self, Param, Flags: int = ...) -> None: ...
    def CryptGetUserKey(self, KeySpec) -> PyCRYPTKEY: ...
    def CryptGenRandom(self, Len, SeedData: str | None = ...) -> str: ...
    def CryptCreateHash(self, Algid, Key: PyCRYPTKEY | None = ..., Flags: int = ...) -> PyCRYPTHASH: ...
    def CryptImportKey(self, Data, PubKey: PyCRYPTKEY | None = ..., Flags: int = ...) -> PyCRYPTKEY: ...
    def CryptExportPublicKeyInfo(self, KeySpec, CertEncodingType=...) -> PyCERT_PUBLIC_KEY_INFO: ...
    def CryptImportPublicKeyInfo(self, Info, CertEncodingType=...) -> PyCRYPTKEY: ...

class PyCRYPT_ALGORITHM_IDENTIFIER:
    @property
    def ObjId(self): ...
    @property
    def Parameters(self): ...

class PyCRYPT_ATTRIBUTE:
    @property
    def ObjId(self): ...
    @property
    def Value(self) -> tuple[Incomplete, ...]: ...

class PyCRYPT_BIT_BLOB:
    @property
    def Data(self): ...
    @property
    def UnusedBits(self): ...

class PyCRYPT_DECRYPT_MESSAGE_PARA:
    @property
    def CertStores(self) -> tuple[Incomplete, ...]: ...
    @property
    def MsgAndCertEncodingType(self): ...
    @property
    def Flags(self): ...

class PyCRYPT_ENCRYPT_MESSAGE_PARA:
    @property
    def ContentEncryptionAlgorithm(self) -> PyCRYPT_ALGORITHM_IDENTIFIER: ...
    @property
    def CryptProv(self) -> PyCRYPTPROV: ...
    @property
    def EncryptionAuxInfo(self): ...
    @property
    def Flags(self): ...
    @property
    def InnerContentType(self): ...
    @property
    def MsgEncodingType(self): ...

class PyCRYPT_SIGN_MESSAGE_PARA:
    @property
    def SigningCert(self) -> PyCERT_CONTEXT: ...
    @property
    def HashAlgorithm(self) -> PyCRYPT_ALGORITHM_IDENTIFIER: ...
    @property
    def HashAuxInfo(self): ...
    @property
    def MsgCert(self) -> tuple[PyCERT_CONTEXT, ...]: ...
    @property
    def MsgCrl(self) -> tuple[Incomplete, ...]: ...
    @property
    def AuthAttr(self) -> tuple[PyCRYPT_ATTRIBUTE, ...]: ...
    @property
    def UnauthAttr(self) -> tuple[PyCRYPT_ATTRIBUTE, ...]: ...
    @property
    def Flags(self): ...
    @property
    def InnerContentType(self): ...
    @property
    def MsgEncodingType(self): ...

class PyCRYPT_VERIFY_MESSAGE_PARA:
    @property
    def MsgAndCertEncodingType(self): ...
    @property
    def CryptProv(self) -> PyCRYPTPROV: ...
    @property
    def PyGetSignerCertificate(self): ...
    @property
    def GetArg(self): ...

class PyCTL_CONTEXT:
    @property
    def HCTL_CONTEXT(self): ...
    def CertFreeCTLContext(self) -> None: ...
    def CertEnumCTLContextProperties(self) -> tuple[Incomplete, ...]: ...
    def CertEnumSubjectInSortedCTL(self) -> tuple[tuple[Incomplete, Incomplete], ...]: ...
    def CertDeleteCTLFromStore(self) -> None: ...
    def CertSerializeCTLStoreElement(self, Flags: int = ...) -> str: ...

class PyCTL_USAGE: ...

@final
class PyConsoleScreenBuffer:
    def __init__(self, Handle) -> None: ...
    def SetConsoleActiveScreenBuffer(self) -> None: ...
    def GetConsoleCursorInfo(self) -> tuple[Incomplete, Incomplete]: ...
    def SetConsoleCursorInfo(self, Size, Visible) -> None: ...
    def GetConsoleMode(self): ...
    def SetConsoleMode(self, Mode) -> None: ...
    def ReadConsole(self, NumberOfCharsToRead): ...
    def WriteConsole(self, Buffer: str) -> int: ...
    def FlushConsoleInputBuffer(self) -> None: ...
    def SetConsoleTextAttribute(self, Attributes: int) -> None: ...
    def SetConsoleCursorPosition(self, CursorPosition: PyCOORD) -> None: ...
    def SetConsoleScreenBufferSize(self, Size: PyCOORD) -> None: ...
    def SetConsoleWindowInfo(self, Absolute, ConsoleWindow: PySMALL_RECT) -> None: ...
    def GetConsoleScreenBufferInfo(self): ...
    def GetLargestConsoleWindowSize(self) -> PyCOORD: ...
    def FillConsoleOutputAttribute(self, Attribute, Length, WriteCoord: PyCOORD): ...
    def FillConsoleOutputCharacter(self, Character, Length, WriteCoord: PyCOORD): ...
    def ReadConsoleOutputCharacter(self, Length, ReadCoord: PyCOORD) -> str: ...
    def ReadConsoleOutputAttribute(self, Length, ReadCoord: PyCOORD) -> tuple[Incomplete, ...]: ...
    def WriteConsoleOutputCharacter(self, Characters, WriteCoord: PyCOORD): ...
    def WriteConsoleOutputAttribute(self, Attributes: tuple[Incomplete, ...], WriteCoord: PyCOORD): ...
    def ScrollConsoleScreenBuffer(
        self, ScrollRectangle: PySMALL_RECT, ClipRectangle: PySMALL_RECT, DestinationOrigin: PyCOORD, FillCharacter, FillAttribute
    ) -> None: ...
    def GetCurrentConsoleFont(self, MaximumWindow: bool = ...) -> tuple[int, PyCOORD]: ...
    def GetConsoleFontSize(self, Font) -> PyCOORD: ...
    def SetConsoleFont(self, Font) -> None: ...
    def SetStdHandle(self, StdHandle) -> None: ...
    def SetConsoleDisplayMode(self, Flags, NewScreenBufferDimensions: PyCOORD) -> None: ...
    def WriteConsoleInput(self, Buffer: Iterable[PyINPUT_RECORD]): ...
    def ReadConsoleInput(self, Length) -> tuple[PyINPUT_RECORD, ...]: ...
    def PeekConsoleInput(self, Length) -> tuple[PyINPUT_RECORD, ...]: ...
    def GetNumberOfConsoleInputEvents(self): ...
    def Close(self, *args): ...  # incomplete
    def Detach(self, *args): ...  # incomplete

class PyCredHandle:
    def Detach(self): ...
    def FreeCredentialsHandle(self) -> None: ...
    def QueryCredentialsAttributes(self, Attribute: int, /) -> str: ...

class PyCtxtHandle:
    def Detach(self): ...
    def CompleteAuthToken(self, Token: PySecBufferDesc, /) -> None: ...
    def QueryContextAttributes(self, Attribute, /) -> None: ...
    def DeleteSecurityContext(self) -> None: ...
    def QuerySecurityContextToken(self): ...
    def MakeSignature(self, fqop, Message: PySecBufferDesc, MessageSeqNo, /) -> None: ...
    def VerifySignature(self, Message: PySecBufferDesc, MessageSeqNo, /) -> None: ...
    def EncryptMessage(self, fqop, Message: PySecBufferDesc, MessageSeqNo, /) -> None: ...
    def DecryptMessage(self, Message: PySecBufferDesc, MessageSeqNo, /) -> None: ...
    def ImpersonateSecurityContext(self) -> None: ...
    def RevertSecurityContext(self) -> None: ...

class PyDCB:
    @property
    def BaudRate(self) -> int: ...
    @property
    def wReserved(self) -> int: ...
    @property
    def XonLim(self) -> int: ...
    @property
    def XoffLim(self) -> int: ...
    @property
    def ByteSize(self) -> int: ...
    @property
    def Parity(self) -> int: ...
    @property
    def StopBits(self) -> int: ...
    @property
    def XonChar(self) -> str: ...
    @property
    def XoffChar(self) -> str: ...
    @property
    def ErrorChar(self) -> str: ...
    @property
    def EofChar(self) -> str: ...
    @property
    def EvtChar(self) -> str: ...
    @property
    def wReserved1(self) -> int: ...
    @property
    def fBinary(self) -> int: ...
    @property
    def fParity(self) -> int: ...
    @property
    def fOutxCtsFlow(self) -> int: ...
    @property
    def fOutxDsrFlow(self) -> int: ...
    @property
    def fDtrControl(self) -> int: ...
    @property
    def fDsrSensitivity(self) -> int: ...
    @property
    def fTXContinueOnXoff(self) -> int: ...
    @property
    def fOutX(self) -> int: ...
    @property
    def fInX(self) -> int: ...
    @property
    def fErrorChar(self) -> int: ...
    @property
    def fNull(self) -> int: ...
    @property
    def fRtsControl(self) -> int: ...
    @property
    def fAbortOnError(self) -> int: ...
    @property
    def fDummy2(self) -> int: ...

class PyDEVMODE:
    @property
    def SpecVersion(self) -> int: ...
    @property
    def DriverVersion(self) -> int: ...
    @property
    def Size(self) -> int: ...
    @property
    def DriverExtra(self) -> int: ...
    @property
    def Fields(self) -> int: ...
    @property
    def Orientation(self) -> int: ...
    @property
    def PaperSize(self) -> int: ...
    @property
    def PaperLength(self) -> int: ...
    @property
    def PaperWidth(self) -> int: ...
    @property
    def Position_x(self) -> int: ...
    @property
    def Position_y(self) -> int: ...
    @property
    def DisplayOrientation(self) -> int: ...
    @property
    def DisplayFixedOutput(self) -> int: ...
    @property
    def Scale(self) -> int: ...
    @property
    def Copies(self) -> int: ...
    @property
    def DefaultSource(self) -> int: ...
    @property
    def PrintQuality(self) -> int: ...
    @property
    def Color(self) -> int: ...
    @property
    def Duplex(self) -> int: ...
    @property
    def YResolution(self) -> int: ...
    @property
    def TTOption(self) -> int: ...
    @property
    def Collate(self) -> int: ...
    @property
    def LogPixels(self) -> int: ...
    @property
    def BitsPerPel(self) -> int: ...
    @property
    def PelsWidth(self) -> int: ...
    @property
    def PelsHeight(self) -> int: ...
    @property
    def DisplayFlags(self) -> int: ...
    @property
    def DisplayFrequency(self) -> int: ...
    @property
    def ICMMethod(self) -> int: ...
    @property
    def ICMIntent(self) -> int: ...
    @property
    def MediaType(self) -> int: ...
    @property
    def DitherType(self) -> int: ...
    @property
    def Reserved1(self) -> int: ...
    @property
    def Reserved2(self) -> int: ...
    @property
    def Nup(self) -> int: ...
    @property
    def PanningWidth(self) -> int: ...
    @property
    def PanningHeight(self) -> int: ...
    @property
    def DeviceName(self) -> str: ...
    @property
    def FormName(self) -> str: ...
    @property
    def DriverData(self) -> Incomplete | None: ...
    def Clear(self) -> None: ...

class PyDEVMODEW:
    def __init__(self, DriverExtra: int = ...) -> None: ...
    SpecVersion: int
    DriverVersion: int
    @property
    def Size(self) -> int: ...
    @property
    def DriverExtra(self) -> int: ...
    Fields: int
    Orientation: int
    PaperSize: int
    PaperLength: int
    PaperWidth: int
    Position_x: int
    Position_y: int
    DisplayOrientation: int
    DisplayFixedOutput: int
    Scale: int
    Copies: int
    DefaultSource: int
    PrintQuality: int
    Color: int
    Duplex: int
    YResolution: int
    TTOption: int
    Collate: int
    LogPixels: int
    BitsPerPel: int
    PelsWidth: int
    PelsHeight: int
    DisplayFlags: int
    DisplayFrequency: int
    ICMMethod: int
    ICMIntent: int
    MediaType: int
    DitherType: int
    Reserved1: int
    Reserved2: int
    Nup: int
    PanningWidth: int
    PanningHeight: int
    DeviceName: str
    FormName: str
    @property
    def DriverData(self) -> bytes | None: ...
    @DriverData.setter
    def DriverData(self, value: bytes) -> None: ...

class PyDISPLAY_DEVICE:
    @property
    def Size(self) -> int: ...
    @property
    def DeviceName(self) -> str: ...
    @property
    def DeviceString(self) -> str: ...
    @property
    def StateFlags(self) -> int: ...
    @property
    def DeviceID(self) -> str: ...
    @property
    def DeviceKey(self) -> str: ...
    def Clear(self) -> None: ...

class PyDLGITEMTEMPLATE: ...
class PyDLGTEMPLATE: ...
class PyDS_HANDLE: ...
class PyDS_NAME_RESULT_ITEM: ...
class PyDialogTemplate: ...
class PyEVTLOG_HANDLE: ...
class PyEVT_HANDLE: ...
class PyEVT_RPC_LOGIN: ...

class PyEventLogRecord:
    @property
    def Reserved(self) -> int: ...
    @property
    def RecordNumber(self) -> int: ...
    @property
    def TimeGenerated(self) -> TimeType: ...
    @property
    def TimeWritten(self) -> TimeType: ...
    @property
    def EventID(self) -> int: ...
    @property
    def EventType(self) -> int: ...
    @property
    def EventCategory(self) -> int: ...
    @property
    def ReservedFlags(self) -> int: ...
    @property
    def ClosingRecordNumber(self) -> int: ...
    @property
    def SourceName(self) -> str: ...
    @property
    def StringInserts(self) -> tuple[str, ...]: ...
    @property
    def Sid(self) -> PySID | None: ...
    @property
    def Data(self) -> str: ...
    @property
    def ComputerName(self) -> str: ...

class PyGROUP_INFO_0:
    @property
    def name(self) -> str: ...

class PyGROUP_INFO_1:
    @property
    def name(self) -> str: ...
    @property
    def comment(self) -> str: ...

class PyGROUP_INFO_1002:
    @property
    def comment(self) -> str: ...

class PyGROUP_INFO_1005:
    @property
    def attributes(self): ...

class PyGROUP_INFO_2:
    @property
    def name(self) -> str: ...
    @property
    def comment(self) -> str: ...
    @property
    def group_id(self): ...
    @property
    def attributes(self): ...

class PyGROUP_USERS_INFO_0:
    @property
    def name(self) -> str: ...

class PyGROUP_USERS_INFO_1:
    @property
    def name(self) -> str: ...
    @property
    def attributes(self): ...

class PyGdiHANDLE: ...
class PyGetSignerCertificate: ...

class PyHANDLE:
    @property
    def handle(self) -> int: ...
    def Close(self) -> None: ...
    def close(self) -> None: ...
    def Detach(self) -> Self: ...

@final
class PyHDESK:
    def __init__(self, handle) -> None: ...
    def SetThreadDesktop(self) -> None: ...
    def EnumDesktopWindows(self) -> tuple[int, ...]: ...
    def SwitchDesktop(self) -> None: ...
    def CloseDesktop(self) -> None: ...
    def Detach(self, *args): ...  # incomplete

class PyHDEVNOTIFY: ...

class PyHHNTRACK:
    @property
    def action(self): ...
    @property
    def hdr(self): ...
    @property
    def curUrl(self) -> str: ...
    @property
    def winType(self): ...

class PyHHN_NOTIFY:
    @property
    def hdr(self): ...
    @property
    def url(self) -> str: ...

class PyHH_AKLINK:
    @property
    def indexOnFail(self): ...
    @property
    def keywords(self) -> str: ...
    @property
    def url(self) -> str: ...
    @property
    def msgText(self) -> str: ...
    @property
    def msgTitle(self) -> str: ...
    @property
    def window(self) -> str: ...

class PyHH_FTS_QUERY:
    @property
    def uniCodeStrings(self): ...
    @property
    def proximity(self): ...
    @property
    def stemmedSearch(self): ...
    @property
    def titleOnly(self): ...
    @property
    def execute(self): ...
    @property
    def searchQuery(self) -> str: ...

class PyHH_POPUP:
    @property
    def hinst(self): ...
    @property
    def idString(self): ...
    @property
    def clrForeground(self): ...
    @property
    def clrBackground(self): ...
    @property
    def text(self) -> str: ...
    @property
    def font(self) -> str: ...
    @property
    def pt(self): ...
    @property
    def margins(self): ...

class PyHH_WINTYPE:
    @property
    def uniCodeStrings(self): ...
    @property
    def validMembers(self): ...
    @property
    def winProperties(self): ...
    @property
    def styles(self): ...
    @property
    def exStyles(self): ...
    @property
    def showState(self): ...
    @property
    def hwndHelp(self): ...
    @property
    def hwndCaller(self): ...
    @property
    def hwndToolBar(self): ...
    @property
    def hwndNavigation(self): ...
    @property
    def hwndHTML(self): ...
    @property
    def navWidth(self): ...
    @property
    def toolBarFlags(self): ...
    @property
    def notExpanded(self): ...
    @property
    def curNavType(self): ...
    @property
    def idNotify(self): ...
    @property
    def typeName(self) -> str: ...
    @property
    def caption(self) -> str: ...
    @property
    def windowPos(self): ...
    @property
    def HTMLPos(self): ...
    @property
    def toc(self) -> str: ...
    @property
    def index(self) -> str: ...
    @property
    def file(self) -> str: ...
    @property
    def home(self) -> str: ...
    @property
    def jump1(self) -> str: ...
    @property
    def jump2(self) -> str: ...
    @property
    def urlJump1(self) -> str: ...
    @property
    def urlJump2(self) -> str: ...

class PyHINTERNET: ...

class PyHKEY:
    def Close(self): ...

class PyHTHEME: ...

@final
class PyHWINSTA:
    def __init__(self, handle) -> None: ...
    def EnumDesktops(self) -> tuple[Incomplete, ...]: ...
    def SetProcessWindowStation(self) -> None: ...
    def CloseWindowStation(self) -> None: ...
    def Detach(self, *args): ...  # incomplete

class PyICONINFO: ...

@final
class PyIID: ...

@final
class PyINPUT_RECORD:
    def __init__(self, EventType: int) -> None: ...
    EventType: int
    KeyDown: int | bool
    RepeatCount: int
    VirtualKeyCode: int
    VirtualScanCode: Incomplete
    Char: str
    ControlKeyState: int
    ButtonState: int
    EventFlags: int
    MousePosition: PyCOORD
    Size: PyCOORD
    SetFocus: Incomplete
    CommandId: Incomplete

class PyLOCALGROUP_INFO_0:
    @property
    def name(self) -> str: ...

class PyLOCALGROUP_INFO_1:
    @property
    def name(self) -> str: ...
    @property
    def comment(self) -> str: ...

class PyLOCALGROUP_INFO_1002:
    @property
    def comment(self) -> str: ...

class PyLOCALGROUP_MEMBERS_INFO_0:
    @property
    def sid(self) -> PySID: ...

class PyLOCALGROUP_MEMBERS_INFO_1:
    @property
    def sid(self) -> PySID: ...
    @property
    def sidusage(self): ...
    @property
    def name(self) -> str: ...

class PyLOCALGROUP_MEMBERS_INFO_2:
    @property
    def sid(self) -> PySID: ...
    @property
    def sidusage(self): ...
    @property
    def domainandname(self) -> str: ...

class PyLOCALGROUP_MEMBERS_INFO_3:
    @property
    def domainandname(self) -> str: ...

class PyLOGBRUSH:
    @property
    def Style(self): ...
    @property
    def Color(self): ...
    @property
    def Hatch(self) -> int: ...

class PyLOGFONT:
    @property
    def lfHeight(self) -> int: ...
    @property
    def lfWidth(self) -> int: ...
    @property
    def lfEscapement(self) -> int: ...
    @property
    def lfOrientation(self) -> int: ...
    @property
    def lfWeight(self) -> int: ...
    @property
    def lfItalic(self) -> int: ...
    @property
    def lfUnderline(self) -> int: ...
    @property
    def lfStrikeOut(self) -> int: ...
    @property
    def lfCharSet(self) -> int: ...
    @property
    def lfOutPrecision(self) -> int: ...
    @property
    def lfClipPrecision(self) -> int: ...
    @property
    def lfQuality(self) -> int: ...
    @property
    def lfPitchAndFamily(self) -> int: ...
    @property
    def lfFaceName(self) -> str: ...

class PyLSA_HANDLE: ...
class PyLUID_AND_ATTRIBUTES: ...
class PyLsaLogon_HANDLE: ...
class PyMSG: ...

@final
class PyNETRESOURCE:
    dwScope: int
    dwType: int
    dwDisplayType: int
    dwUsage: int
    lpComment: str | None
    lpLocalName: str | None
    lpProvider: str | None
    lpRemoteName: str | None

class PyNET_VALIDATE_AUTHENTICATION_INPUT_ARG: ...
class PyNET_VALIDATE_PASSWORD_CHANGE_INPUT_ARG: ...
class PyNET_VALIDATE_PERSISTED_FIELDS: ...

class PyNMHDR:
    @property
    def hwndFrom(self): ...
    @property
    def idFrom(self): ...
    @property
    def code(self): ...

class PyNOTIFYICONDATA: ...

class PyOVERLAPPED:
    Offset: int
    OffsetHigh: int
    object: object
    dword: int
    hEvent: int
    Internal: int
    InternalHigh: int

class PyOVERLAPPEDReadBuffer: ...

class PyPERF_COUNTER_DEFINITION:
    @property
    def DefaultScale(self) -> int: ...
    @property
    def DetailLevel(self) -> int: ...
    @property
    def CounterType(self) -> int: ...
    @property
    def CounterNameTitleIndex(self) -> int: ...
    @property
    def CounterHelpTitleIndex(self) -> int: ...
    def Increment(self) -> None: ...
    def Decrement(self) -> None: ...
    def Set(self) -> None: ...
    def Get(self) -> None: ...

class PyPERF_OBJECT_TYPE:
    @property
    def ObjectNameTitleIndex(self) -> int: ...
    @property
    def ObjectHelpTitleIndex(self) -> int: ...
    @property
    def DefaultCounterIndex(self) -> int: ...
    def Close(self) -> None: ...

class PyPOINT: ...

class PyPROFILEINFO:
    @property
    def UserName(self) -> str: ...
    @property
    def Flags(self): ...
    @property
    def ProfilePath(self) -> str: ...
    @property
    def DefaultPath(self) -> str: ...
    @property
    def ServerName(self) -> str: ...
    @property
    def PolicyPath(self) -> str: ...
    @property
    def Profile(self) -> PyHKEY: ...

class PyPerfMonManager:
    def Close(self) -> None: ...

class PyPrinterHANDLE: ...
class PyRECT: ...
class PyResourceId: ...
class PySCROLLINFO: ...
class PySC_HANDLE: ...

class PySECURITY_ATTRIBUTES:
    bInheritHandle: int
    SECURITY_DESCRIPTOR: PySECURITY_DESCRIPTOR

class PySECURITY_DESCRIPTOR:
    def Initialize(self) -> None: ...
    def GetSecurityDescriptorOwner(self) -> PySID: ...
    def GetSecurityDescriptorDacl(self) -> PyACL: ...
    def GetSecurityDescriptorSacl(self) -> PyACL: ...
    def GetSecurityDescriptorControl(self) -> tuple[Incomplete, Incomplete]: ...
    def SetSecurityDescriptorOwner(self, sid: PySID, bOwnerDefaulted: int | bool, /) -> None: ...
    def SetSecurityDescriptorGroup(self, sid: PySID, bOwnerDefaulted, /): ...
    def SetSecurityDescriptorDacl(self, bSaclPresent: int | bool, SACL: PyACL, bSaclDefaulted: int | bool, /) -> None: ...
    def SetSecurityDescriptorSacl(self, bSaclPresent, SACL: PyACL, bSaclDefaulted, /) -> None: ...
    def SetSecurityDescriptorControl(self, ControlBitsOfInterest, ControlBitsToSet, /) -> None: ...
    def IsValid(self) -> bool: ...
    def GetLength(self) -> None: ...
    def IsSelfRelative(self) -> bool: ...

class PySERVER_INFO_100:
    @property
    def platform_id(self): ...
    @property
    def name(self) -> str: ...

class PySERVER_INFO_101:
    @property
    def platform_id(self): ...
    @property
    def name(self) -> str: ...
    @property
    def version_major(self): ...
    @property
    def version_minor(self): ...
    @property
    def type(self): ...
    @property
    def comment(self) -> str: ...

class PySERVER_INFO_102:
    @property
    def platform_id(self): ...
    @property
    def name(self) -> str: ...
    @property
    def version_major(self): ...
    @property
    def version_minor(self): ...
    @property
    def type(self): ...
    @property
    def comment(self) -> str: ...
    @property
    def users(self): ...
    @property
    def disc(self): ...
    @property
    def hidden(self): ...
    @property
    def announce(self): ...
    @property
    def anndelta(self): ...
    @property
    def userpath(self) -> str: ...

class PySERVER_INFO_402:
    @property
    def ulist_mtime(self): ...
    @property
    def glist_mtime(self): ...
    @property
    def alist_mtime(self): ...
    @property
    def security(self): ...
    @property
    def numadmin(self): ...
    @property
    def lanmask(self): ...
    @property
    def guestacct(self) -> str: ...
    @property
    def chdevs(self): ...
    @property
    def chdevq(self): ...
    @property
    def chdevjobs(self): ...
    @property
    def connections(self): ...
    @property
    def shares(self): ...
    @property
    def openfiles(self): ...
    @property
    def sessopens(self): ...
    @property
    def sessvcs(self): ...
    @property
    def sessreqs(self): ...
    @property
    def opensearch(self): ...
    @property
    def activelocks(self): ...
    @property
    def numreqbuf(self): ...
    @property
    def sizreqbuf(self): ...
    @property
    def numbigbuf(self): ...
    @property
    def numfiletasks(self): ...
    @property
    def alertsched(self): ...
    @property
    def erroralert(self): ...
    @property
    def logonalert(self): ...
    @property
    def accessalert(self): ...
    @property
    def diskalert(self): ...
    @property
    def netioalert(self): ...
    @property
    def maxauditsz(self): ...
    @property
    def srvheuristics(self) -> str: ...

class PySERVER_INFO_403:
    @property
    def ulist_mtime(self): ...
    @property
    def glist_mtime(self): ...
    @property
    def alist_mtime(self): ...
    @property
    def security(self): ...
    @property
    def numadmin(self): ...
    @property
    def lanmask(self): ...
    @property
    def guestacct(self) -> str: ...
    @property
    def chdevs(self): ...
    @property
    def chdevq(self): ...
    @property
    def chdevjobs(self): ...
    @property
    def connections(self): ...
    @property
    def shares(self): ...
    @property
    def openfiles(self): ...
    @property
    def sessopens(self): ...
    @property
    def sessvcs(self): ...
    @property
    def sessreqs(self): ...
    @property
    def opensearch(self): ...
    @property
    def activelocks(self): ...
    @property
    def numreqbuf(self): ...
    @property
    def sizreqbuf(self): ...
    @property
    def numbigbuf(self): ...
    @property
    def numfiletasks(self): ...
    @property
    def alertsched(self): ...
    @property
    def erroralert(self): ...
    @property
    def logonalert(self): ...
    @property
    def accessalert(self): ...
    @property
    def diskalert(self): ...
    @property
    def netioalert(self): ...
    @property
    def maxauditsz(self): ...
    @property
    def srvheuristics(self) -> str: ...
    @property
    def auditedevents(self): ...
    @property
    def autoprofile(self): ...
    @property
    def autopath(self) -> str: ...

class PySERVER_INFO_502:
    @property
    def sessopens(self): ...
    @property
    def sessvcs(self): ...
    @property
    def opensearch(self): ...
    @property
    def sizreqbuf(self): ...
    @property
    def initworkitems(self): ...
    @property
    def maxworkitems(self): ...
    @property
    def rawworkitems(self): ...
    @property
    def irpstacksize(self): ...
    @property
    def maxrawbuflen(self): ...
    @property
    def sessusers(self): ...
    @property
    def sessconns(self): ...
    @property
    def maxpagedmemoryusage(self): ...
    @property
    def maxnonpagedmemoryusage(self): ...
    @property
    def enableforcedlogoff(self): ...
    @property
    def timesource(self): ...
    @property
    def acceptdownlevelapis(self): ...
    @property
    def lmannounce(self): ...

class PySERVER_INFO_503:
    @property
    def sessopens(self): ...
    @property
    def sessvcs(self): ...
    @property
    def opensearch(self): ...
    @property
    def sizreqbuf(self): ...
    @property
    def initworkitems(self): ...
    @property
    def maxworkitems(self): ...
    @property
    def rawworkitems(self): ...
    @property
    def irpstacksize(self): ...
    @property
    def maxrawbuflen(self): ...
    @property
    def sessusers(self): ...
    @property
    def sessconns(self): ...
    @property
    def maxpagedmemoryusage(self): ...
    @property
    def maxnonpagedmemoryusage(self): ...
    @property
    def enableforcedlogoff(self): ...
    @property
    def timesource(self): ...
    @property
    def acceptdownlevelapis(self): ...
    @property
    def lmannounce(self): ...
    @property
    def domain(self) -> str: ...
    @property
    def maxkeepsearch(self): ...
    @property
    def scavtimeout(self): ...
    @property
    def minrcvqueue(self): ...
    @property
    def minfreeworkitems(self): ...
    @property
    def xactmemsize(self): ...
    @property
    def threadpriority(self): ...
    @property
    def maxmpxct(self): ...
    @property
    def oplockbreakwait(self): ...
    @property
    def oplockbreakresponsewait(self): ...
    @property
    def enableoplocks(self): ...
    @property
    def enablefcbopens(self): ...
    @property
    def enableraw(self): ...
    @property
    def enablesharednetdrives(self): ...
    @property
    def minfreeconnections(self): ...
    @property
    def maxfreeconnections(self): ...

class PySHARE_INFO_0:
    @property
    def netname(self) -> str: ...

class PySHARE_INFO_1:
    @property
    def netname(self) -> str: ...
    @property
    def type(self): ...
    @property
    def remark(self) -> str: ...

class PySHARE_INFO_2:
    @property
    def netname(self) -> str: ...
    @property
    def type(self): ...
    @property
    def remark(self) -> str: ...
    @property
    def permissions(self): ...
    @property
    def max_uses(self): ...
    @property
    def current_uses(self): ...
    @property
    def path(self) -> str: ...
    @property
    def passwd(self) -> str: ...

class PySHARE_INFO_501:
    @property
    def netname(self) -> str: ...
    @property
    def type(self): ...
    @property
    def remark(self) -> str: ...
    @property
    def flags(self): ...

class PySHARE_INFO_502:
    @property
    def netname(self) -> str: ...
    @property
    def type(self): ...
    @property
    def remark(self) -> str: ...
    @property
    def permissions(self): ...
    @property
    def max_uses(self): ...
    @property
    def current_uses(self): ...
    @property
    def path(self) -> str: ...
    @property
    def passwd(self) -> str: ...
    @property
    def reserved(self): ...
    @property
    def security_descriptor(self) -> PySECURITY_DESCRIPTOR: ...

class PySID:
    def Initialize(self, idAuthority, numSubauthorities, /) -> None: ...
    def IsValid(self) -> bool: ...
    def SetSubAuthority(self, index, val, /) -> None: ...
    def GetLength(self): ...
    def GetSubAuthorityCount(self): ...
    def GetSubAuthority(self): ...
    def GetSidIdentifierAuthority(self) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete, Incomplete, Incomplete]: ...

class PySID_AND_ATTRIBUTES: ...
class PySIZE: ...

@final
class PySMALL_RECT:
    def __init__(self, Left: int = ..., Top: int = ..., Right: int = ..., Bottom: int = ...) -> None: ...
    Left: int
    Top: int
    Right: int
    Bottom: int

class PySTARTUPINFO:
    dwX: int
    dwY: int
    dwXSize: int
    dwYSize: int
    dwXCountChars: int
    dwYCountChars: int
    dwFillAttribute: int
    dwFlags: int
    wShowWindow: int
    hStdInput: int
    hStdOutput: int
    hStdError: int
    lpDesktop: str
    lpTitle: str

class PySecBuffer:
    def __init__(self, BufferSize, BufferType) -> None: ...
    @property
    def BufferType(self): ...
    @property
    def Buffer(self) -> str: ...
    @property
    def BufferSize(self): ...
    @property
    def MaxBufferSize(self): ...
    def Clear(self) -> None: ...

class PySecBufferDesc:
    def __init__(self, Version=...) -> None: ...
    Version: Incomplete
    Buffer: Incomplete
    def append(self, buffer, /) -> None: ...
    def __getitem__(self, index: SupportsIndex) -> PySecBuffer: ...

class PyTOKEN_GROUPS: ...
class PyTOKEN_PRIVILEGES: ...

class PyTRIVERTEX:
    @property
    def x(self): ...
    @property
    def y(self): ...
    @property
    def Red(self): ...
    @property
    def Green(self): ...
    @property
    def Blue(self): ...
    @property
    def Alpha(self): ...

# Properties Multiple* are ignored
class PyTRUSTEE:
    @property
    def TrusteeForm(self) -> int: ...
    @property
    def TrusteeType(self) -> int: ...
    @property
    def Identifier(self) -> PySID: ...
    @property
    def MultipleTrustee(self) -> None: ...
    @property
    def MultipleTrusteeOperation(self) -> Literal[0]: ...

class PyTS_HANDLE: ...

class PyUSER_INFO_0:
    @property
    def name(self) -> str: ...

class PyUSER_INFO_1:
    @property
    def name(self) -> str: ...
    @property
    def password(self) -> str: ...
    @property
    def password_age(self): ...
    @property
    def priv(self): ...
    @property
    def home_dir(self) -> str: ...
    @property
    def comment(self) -> str: ...
    @property
    def flags(self): ...
    @property
    def script_path(self) -> str: ...

class PyUSER_INFO_10:
    @property
    def name(self) -> str: ...
    @property
    def comment(self) -> str: ...
    @property
    def usr_comment(self) -> str: ...
    @property
    def full_name(self) -> str: ...

class PyUSER_INFO_1003:
    @property
    def password(self) -> str: ...

class PyUSER_INFO_1005:
    @property
    def priv(self): ...

class PyUSER_INFO_1006:
    @property
    def home_dir(self) -> str: ...

class PyUSER_INFO_1007:
    @property
    def comment(self) -> str: ...

class PyUSER_INFO_1008:
    @property
    def flags(self): ...

class PyUSER_INFO_1009:
    @property
    def script_path(self) -> str: ...

class PyUSER_INFO_1010:
    @property
    def auth_flags(self): ...

class PyUSER_INFO_1011:
    @property
    def full_name(self) -> str: ...

class PyUSER_INFO_11:
    @property
    def name(self) -> str: ...
    @property
    def comment(self) -> str: ...
    @property
    def usr_comment(self) -> str: ...
    @property
    def full_name(self) -> str: ...
    @property
    def priv(self): ...
    @property
    def auth_flags(self): ...
    @property
    def password_age(self): ...
    @property
    def home_dir(self) -> str: ...
    @property
    def parms(self) -> str: ...
    @property
    def last_logon(self): ...
    @property
    def last_logoff(self): ...
    @property
    def bad_pw_count(self): ...
    @property
    def num_logons(self): ...
    @property
    def logon_server(self) -> str: ...
    @property
    def country_code(self): ...
    @property
    def workstations(self) -> str: ...
    @property
    def max_storage(self): ...
    @property
    def units_per_week(self): ...
    @property
    def logon_hours(self) -> str: ...
    @property
    def code_page(self): ...

class PyUSER_INFO_2:
    @property
    def name(self) -> str: ...
    @property
    def password(self) -> str: ...
    @property
    def password_age(self): ...
    @property
    def priv(self): ...
    @property
    def home_dir(self) -> str: ...
    @property
    def comment(self) -> str: ...
    @property
    def flags(self): ...
    @property
    def script_path(self) -> str: ...
    @property
    def auth_flags(self): ...
    @property
    def full_name(self) -> str: ...
    @property
    def usr_comment(self) -> str: ...
    @property
    def parms(self) -> str: ...
    @property
    def workstations(self) -> str: ...
    @property
    def last_logon(self): ...
    @property
    def last_logoff(self): ...
    @property
    def acct_expires(self): ...
    @property
    def max_storage(self): ...
    @property
    def units_per_week(self): ...
    @property
    def logon_hours(self) -> str: ...
    @property
    def bad_pw_count(self): ...
    @property
    def num_logons(self): ...
    @property
    def logon_server(self) -> str: ...
    @property
    def country_code(self): ...
    @property
    def code_page(self): ...

class PyUSER_INFO_20:
    @property
    def name(self) -> str: ...
    @property
    def full_name(self) -> str: ...
    @property
    def comment(self) -> str: ...
    @property
    def flags(self): ...
    @property
    def user_id(self): ...

class PyUSER_INFO_3:
    @property
    def name(self) -> str: ...
    @property
    def password(self) -> str: ...
    @property
    def password_age(self): ...
    @property
    def priv(self): ...
    @property
    def home_dir(self) -> str: ...
    @property
    def comment(self) -> str: ...
    @property
    def flags(self): ...
    @property
    def script_path(self) -> str: ...
    @property
    def auth_flags(self): ...
    @property
    def full_name(self) -> str: ...
    @property
    def usr_comment(self) -> str: ...
    @property
    def parms(self) -> str: ...
    @property
    def workstations(self) -> str: ...
    @property
    def last_logon(self): ...
    @property
    def last_logoff(self): ...
    @property
    def acct_expires(self): ...
    @property
    def max_storage(self): ...
    @property
    def units_per_week(self): ...
    @property
    def logon_hours(self) -> str: ...
    @property
    def bad_pw_count(self): ...
    @property
    def num_logons(self): ...
    @property
    def logon_server(self) -> str: ...
    @property
    def country_code(self): ...
    @property
    def code_page(self): ...
    @property
    def user_id(self): ...
    @property
    def primary_group_id(self): ...
    @property
    def profile(self) -> str: ...
    @property
    def home_dir_drive(self) -> str: ...
    @property
    def password_expired(self): ...

class PyUSER_INFO_4:
    @property
    def name(self) -> str: ...
    @property
    def password(self) -> str: ...
    @property
    def password_age(self): ...
    @property
    def priv(self): ...
    @property
    def home_dir(self) -> str: ...
    @property
    def comment(self) -> str: ...
    @property
    def flags(self): ...
    @property
    def script_path(self) -> str: ...
    @property
    def auth_flags(self): ...
    @property
    def full_name(self) -> str: ...
    @property
    def usr_comment(self) -> str: ...
    @property
    def parms(self) -> str: ...
    @property
    def workstations(self) -> str: ...
    @property
    def last_logon(self): ...
    @property
    def last_logoff(self): ...
    @property
    def acct_expires(self): ...
    @property
    def max_storage(self): ...
    @property
    def units_per_week(self): ...
    @property
    def logon_hours(self) -> str: ...
    @property
    def bad_pw_count(self): ...
    @property
    def num_logons(self): ...
    @property
    def logon_server(self) -> str: ...
    @property
    def country_code(self): ...
    @property
    def code_page(self): ...
    @property
    def user_sid(self) -> PySID: ...
    @property
    def primary_group_id(self): ...
    @property
    def profile(self) -> str: ...
    @property
    def home_dir_drive(self) -> str: ...
    @property
    def password_expired(self): ...

class PyUSER_MODALS_INFO_0:
    @property
    def min_passwd_len(self): ...
    @property
    def max_passwd_age(self): ...
    @property
    def min_passwd_age(self): ...
    @property
    def force_logoff(self): ...
    @property
    def password_hist_len(self): ...

class PyUSER_MODALS_INFO_1:
    @property
    def role(self): ...
    @property
    def primary(self) -> str: ...

class PyUSER_MODALS_INFO_2:
    @property
    def domain_name(self) -> str: ...
    @property
    def domain_id(self) -> PySID: ...

class PyUSER_MODALS_INFO_3:
    @property
    def lockout_duration(self): ...
    @property
    def lockout_observation_window(self): ...
    @property
    def usrmod3_lockout_threshold(self): ...

class PyUSE_INFO_0:
    @property
    def local(self) -> str: ...
    @property
    def remote(self) -> str: ...

class PyUSE_INFO_1:
    @property
    def local(self) -> str: ...
    @property
    def remote(self) -> str: ...
    @property
    def password(self) -> str: ...
    @property
    def status(self): ...
    @property
    def asg_type(self): ...
    @property
    def refcount(self): ...
    @property
    def usecount(self): ...

class PyUSE_INFO_2:
    @property
    def local(self) -> str: ...
    @property
    def remote(self) -> str: ...
    @property
    def password(self) -> str: ...
    @property
    def status(self): ...
    @property
    def asg_type(self): ...
    @property
    def refcount(self): ...
    @property
    def usecount(self): ...
    @property
    def username(self) -> str: ...
    @property
    def domainname(self) -> str: ...

class PyUSE_INFO_3:
    @property
    def local(self) -> str: ...
    @property
    def remote(self) -> str: ...
    @property
    def password(self) -> str: ...
    @property
    def status(self): ...
    @property
    def asg_type(self): ...
    @property
    def refcount(self): ...
    @property
    def usecount(self): ...
    @property
    def username(self) -> str: ...
    @property
    def domainname(self) -> str: ...
    @property
    def flags(self): ...

class PyUrlCacheHANDLE: ...

class PyWAVEFORMATEX:
    @property
    def wFormatTag(self) -> int: ...
    @property
    def nChannels(self) -> int: ...
    @property
    def nSamplesPerSec(self) -> int: ...
    @property
    def nAvgBytesPerSec(self) -> int: ...
    @property
    def nBlockAlign(self) -> int: ...
    @property
    def wBitsPerSample(self) -> int: ...

class PyWINHTTP_AUTOPROXY_OPTIONS: ...
class PyWINHTTP_PROXY_INFO: ...

class PyWKSTA_INFO_100:
    @property
    def platform_id(self): ...
    @property
    def computername(self) -> str: ...
    @property
    def langroup(self) -> str: ...
    @property
    def ver_major(self): ...
    @property
    def ver_minor(self): ...

class PyWKSTA_INFO_101:
    @property
    def platform_id(self): ...
    @property
    def computername(self) -> str: ...
    @property
    def langroup(self) -> str: ...
    @property
    def ver_major(self): ...
    @property
    def ver_minor(self): ...
    @property
    def lanroot(self) -> str: ...

class PyWKSTA_INFO_102:
    @property
    def platform_id(self): ...
    @property
    def computername(self) -> str: ...
    @property
    def langroup(self) -> str: ...
    @property
    def ver_major(self): ...
    @property
    def ver_minor(self): ...
    @property
    def lanroot(self) -> str: ...
    @property
    def logged_on_users(self): ...

class PyWKSTA_INFO_302:
    @property
    def char_wait(self): ...
    @property
    def collection_time(self): ...
    @property
    def maximum_collection_count(self): ...
    @property
    def keep_conn(self): ...
    @property
    def keep_search(self): ...
    @property
    def max_cmds(self): ...
    @property
    def num_work_buf(self): ...
    @property
    def siz_work_buf(self): ...
    @property
    def max_wrk_cache(self): ...
    @property
    def siz_error(self): ...
    @property
    def num_alerts(self): ...
    @property
    def num_services(self): ...
    @property
    def errlog_sz(self): ...
    @property
    def print_buf_time(self): ...
    @property
    def num_char_buf(self): ...
    @property
    def siz_char_buf(self): ...
    @property
    def wrk_heuristics(self) -> str: ...
    @property
    def mailslots(self): ...
    @property
    def num_dgram_buf(self): ...

class PyWKSTA_INFO_402:
    @property
    def char_wait(self): ...
    @property
    def collection_time(self): ...
    @property
    def maximum_collection_count(self) -> str: ...
    @property
    def keep_conn(self): ...
    @property
    def keep_search(self): ...
    @property
    def max_cmds(self): ...
    @property
    def num_work_buf(self): ...
    @property
    def siz_work_buf(self): ...
    @property
    def max_wrk_cache(self): ...
    @property
    def sess_timeout(self): ...
    @property
    def siz_error(self): ...
    @property
    def num_alerts(self): ...
    @property
    def num_services(self): ...
    @property
    def errlog_sz(self): ...
    @property
    def print_buf_time(self): ...
    @property
    def num_char_buf(self): ...
    @property
    def siz_char_buf(self): ...
    @property
    def mailslots(self): ...
    @property
    def num_dgram_buf(self): ...
    @property
    def max_threads(self): ...

class PyWKSTA_INFO_502:
    @property
    def char_wait(self): ...
    @property
    def collection_time(self): ...
    @property
    def maximum_collection_count(self): ...
    @property
    def keep_conn(self): ...
    @property
    def max_cmds(self): ...
    @property
    def max_wrk_cache(self): ...
    @property
    def siz_char_buf(self): ...
    @property
    def lock_quota(self): ...
    @property
    def lock_increment(self): ...
    @property
    def lock_maximum(self): ...
    @property
    def pipe_increment(self): ...
    @property
    def pipe_maximum(self): ...
    @property
    def cache_file_timeout(self): ...
    @property
    def dormant_file_limit(self): ...
    @property
    def read_ahead_throughput(self): ...
    @property
    def num_mailslot_buffers(self): ...
    @property
    def num_srv_announce_buffers(self): ...
    @property
    def max_illegal_datagram_events(self): ...
    @property
    def illegal_datagram_event_reset_frequency(self): ...
    @property
    def log_election_packets(self): ...
    @property
    def use_opportunistic_locking(self): ...
    @property
    def use_unlock_behind(self): ...
    @property
    def use_close_behind(self): ...
    @property
    def buf_named_pipes(self): ...
    @property
    def use_lock_read_unlock(self): ...
    @property
    def utilize_nt_caching(self): ...
    @property
    def use_raw_read(self): ...
    @property
    def use_raw_write(self): ...
    @property
    def use_write_raw_data(self): ...
    @property
    def use_encryption(self): ...
    @property
    def buf_files_deny_write(self): ...
    @property
    def buf_read_only_files(self): ...
    @property
    def force_core_create_mode(self): ...
    @property
    def use_512_byte_max_transfer(self): ...

class PyWKSTA_TRANSPORT_INFO_0:
    @property
    def quality_of_service(self): ...
    @property
    def number_of_vcs(self): ...
    @property
    def transport_name(self) -> str: ...
    @property
    def transport_address(self) -> str: ...
    @property
    def wan_ish(self): ...

class PyWKSTA_USER_INFO_0:
    @property
    def username(self) -> str: ...

class PyWKSTA_USER_INFO_1:
    @property
    def username(self) -> str: ...
    @property
    def logon_domain(self) -> str: ...
    @property
    def oth_domains(self) -> str: ...
    @property
    def logon_server(self) -> str: ...

class PyWNDCLASS:
    @property
    def style(self) -> int: ...
    @property
    def cbWndExtra(self) -> int: ...
    @property
    def hInstance(self) -> int: ...
    @property
    def hIcon(self) -> int: ...
    @property
    def hCursor(self) -> int: ...
    @property
    def hbrBackground(self) -> int: ...
    @property
    def lpszMenuName(self) -> str: ...
    @property
    def lpszClassName(self) -> str: ...
    @property
    def lpfnWndProc(self): ...
    def SetDialogProc(self) -> None: ...

class PyXFORM:
    @property
    def M11(self) -> float: ...
    @property
    def M12(self) -> float: ...
    @property
    def M21(self) -> float: ...
    @property
    def M22(self) -> float: ...
    @property
    def Dx(self) -> float: ...
    @property
    def Dy(self) -> float: ...

class Pymmapfile:
    def close(self) -> None: ...
    def find(self, needle, start, /): ...
    def flush(self, offset: int = ..., size: int = ..., /) -> None: ...
    def move(self, dest, src, count, /) -> None: ...
    def read(self, num_bytes, /): ...
    def read_byte(self): ...
    def read_line(self): ...
    def resize(self, MaximumSize, FileOffset: int = ..., NumberOfBytesToMap: int = ...) -> None: ...
    def seek(self, dist: int, how: int = ..., /) -> None: ...
    def size(self): ...
    def tell(self): ...
    def write(self, data, /) -> None: ...
    def write_byte(self, char, /) -> None: ...

class RASDIALEXTENSIONS:
    @property
    def dwfOptions(self) -> int: ...
    @property
    def hwndParent(self) -> int: ...
    @property
    def reserved(self) -> int: ...
    @property
    def reserved1(self) -> int: ...
    @property
    def RasEapInfo(self): ...

class RASDIALPARAMS: ...

class SC_ACTION:
    @property
    def Type(self): ...
    @property
    def Delay(self): ...

class SERVICE_FAILURE_ACTIONS:
    @property
    def ResetPeriod(self): ...
    @property
    def RebootMsg(self) -> str: ...
    @property
    def Command(self) -> str: ...
    @property
    def Actions(self): ...

class SERVICE_STATUS:
    def __getitem__(self, i: int, /) -> int: ...

class TRACKMOUSEEVENT: ...
class WIN32_FIND_DATA: ...

class connection:
    def setautocommit(self, c, /) -> None: ...
    def commit(self) -> None: ...
    def rollback(self) -> None: ...
    def cursor(self) -> None: ...
    def close(self) -> None: ...

class cursor:
    def close(self) -> None: ...
    def execute(self, sql: str, arg, /): ...
    def fetchone(self): ...
    def fetchmany(self) -> list[Incomplete]: ...
    def fetchall(self) -> list[Incomplete]: ...
    def setinputsizes(self) -> None: ...
    def setoutputsize(self) -> None: ...

class COMPONENT:
    @property
    def ID(self): ...
    @property
    def ComponentType(self): ...
    @property
    def Checked(self): ...
    @property
    def fDirty(self): ...
    @property
    def NoScroll(self): ...
    @property
    def Pos(self): ...
    @property
    def FriendlyName(self): ...
    @property
    def Source(self): ...
    @property
    def SubscribedURL(self): ...
    @property
    def CurItemState(self): ...
    @property
    def Original(self): ...
    @property
    def Restored(self): ...
    @property
    def Size(self): ...

class COMPONENTSOPT:
    @property
    def EnableComponents(self): ...
    @property
    def ActiveDesktop(self): ...
    @property
    def Size(self): ...

class COMPPOS:
    @property
    def Left(self): ...
    @property
    def Top(self): ...
    @property
    def Width(self): ...
    @property
    def Height(self): ...
    @property
    def Index(self): ...
    @property
    def CanResize(self): ...
    @property
    def CanResizeX(self): ...
    @property
    def CanResizeY(self): ...
    @property
    def PreferredLeftPercent(self): ...
    @property
    def PreferredTopPercent(self): ...
    @property
    def Size(self): ...

class COMPSTATEINFO:
    @property
    def Left(self): ...
    @property
    def Top(self): ...
    @property
    def Width(self): ...
    @property
    def Height(self): ...
    @property
    def dwItemState(self): ...
    @property
    def Size(self): ...

class DEFCONTENTMENU: ...
class ELEMDESC: ...

class EXP_DARWIN_LINK:
    @property
    def Signature(self): ...
    @property
    def DarwinID(self): ...
    @property
    def wDarwinID(self): ...
    @property
    def Size(self): ...

class EXP_SPECIAL_FOLDER:
    @property
    def Signature(self): ...
    @property
    def idSpecialFolder(self): ...
    @property
    def Offset(self): ...
    @property
    def Size(self): ...

class EXP_SZ_LINK:
    @property
    def Signature(self): ...
    @property
    def Target(self): ...
    @property
    def wTarget(self): ...
    @property
    def Size(self): ...

class FUNCDESC:
    @property
    def memid(self) -> int: ...
    @property
    def scodeArray(self) -> tuple[Incomplete, ...]: ...
    @property
    def args(self) -> tuple[ELEMDESC, ...]: ...
    @property
    def funckind(self): ...
    @property
    def invkind(self): ...
    @property
    def callconv(self): ...
    @property
    def cParamsOpt(self): ...
    @property
    def oVft(self): ...
    @property
    def rettype(self) -> ELEMDESC: ...
    @property
    def wFuncFlags(self): ...

class IDLDESC: ...
class MAPIINIT_0: ...

class NT_CONSOLE_PROPS:
    @property
    def Signature(self): ...
    @property
    def FillAttribute(self): ...
    @property
    def PopupFillAttribute(self): ...
    @property
    def ScreenBufferSize(self) -> tuple[Incomplete, Incomplete]: ...
    @property
    def WindowSize(self) -> tuple[Incomplete, Incomplete]: ...
    @property
    def WindowOrigin(self) -> tuple[Incomplete, Incomplete]: ...
    @property
    def nFont(self): ...
    @property
    def InputBufferSize(self): ...
    @property
    def FontSize(self) -> tuple[Incomplete, Incomplete]: ...
    @property
    def FontFamily(self): ...
    @property
    def FontWeight(self): ...
    @property
    def FaceName(self): ...
    @property
    def CursorSize(self): ...
    @property
    def FullScreen(self): ...
    @property
    def QuickEdit(self): ...
    @property
    def InsertMode(self): ...
    @property
    def AutoPosition(self): ...
    @property
    def HistoryBufferSize(self): ...
    @property
    def NumberOfHistoryBuffers(self): ...
    @property
    def HistoryNoDup(self): ...
    @property
    def ColorTable(self): ...
    @property
    def Size(self): ...

class NT_FE_CONSOLE_PROPS:
    @property
    def Signature(self): ...
    @property
    def CodePage(self): ...
    @property
    def Size(self): ...

class PROPSPEC: ...
class PyADSVALUE: ...

class PyADS_ATTR_INFO:
    @property
    def AttrName(self): ...
    @property
    def ControlCode(self) -> int: ...
    @property
    def ADsType(self) -> int: ...
    @property
    def Values(self) -> list[Incomplete]: ...

class PyADS_OBJECT_INFO:
    @property
    def RDN(self): ...
    @property
    def ObjectDN(self): ...
    @property
    def ParentDN(self): ...
    @property
    def ClassName(self): ...

class PyADS_SEARCHPREF_INFO: ...

class PyBIND_OPTS:
    @property
    def Flags(self): ...
    @property
    def Mode(self): ...
    @property
    def TickCountDeadline(self): ...
    @property
    def cbStruct(self): ...

class PyCMINVOKECOMMANDINFO: ...

class PyDSBCAPS:
    @property
    def dwFlags(self) -> int: ...
    @property
    def dwUnlockTransferRate(self) -> int: ...
    @property
    def dwBufferBytes(self): ...
    @property
    def dwPlayCpuOverhead(self): ...

class PyDSBUFFERDESC:
    @property
    def dwFlags(self) -> int: ...
    @property
    def dwBufferBytes(self) -> int: ...
    @property
    def lpwfxFormat(self): ...

class PyDSCAPS:
    @property
    def dwFlags(self) -> int: ...
    @property
    def dwMinSecondarySampleRate(self) -> int: ...
    @property
    def dwMaxSecondarySampleRate(self) -> int: ...
    @property
    def dwPrimaryBuffers(self) -> int: ...
    @property
    def dwMaxHwMixingAllBuffers(self) -> int: ...
    @property
    def dwMaxHwMixingStaticBuffers(self) -> int: ...
    @property
    def dwMaxHwMixingStreamingBuffers(self) -> int: ...
    @property
    def dwFreeHwMixingAllBuffers(self) -> int: ...
    @property
    def dwFreeHwMixingStaticBuffers(self) -> int: ...
    @property
    def dwFreeHwMixingStreamingBuffers(self) -> int: ...
    @property
    def dwMaxHw3DAllBuffers(self) -> int: ...
    @property
    def dwMaxHw3DStaticBuffers(self) -> int: ...
    @property
    def dwMaxHw3DStreamingBuffers(self) -> int: ...
    @property
    def dwFreeHw3DAllBuffers(self) -> int: ...
    @property
    def dwFreeHw3DStaticBuffers(self) -> int: ...
    @property
    def dwFreeHw3DStreamingBuffers(self) -> int: ...
    @property
    def dwTotalHwMemBytes(self) -> int: ...
    @property
    def dwFreeHwMemBytes(self) -> int: ...
    @property
    def dwMaxContigFreeHwMemBytes(self) -> int: ...
    @property
    def dwUnlockTransferRateHwBuffers(self) -> int: ...
    @property
    def dwPlayCpuOverheadSwBuffers(self) -> int: ...

class PyDSCBCAPS:
    @property
    def dwFlags(self) -> int: ...
    @property
    def dwBufferBytes(self) -> int: ...

class PyDSCBUFFERDESC:
    @property
    def dwFlags(self) -> int: ...
    @property
    def dwBufferBytes(self) -> int: ...
    @property
    def lpwfxFormat(self): ...

class PyDSCCAPS:
    @property
    def dwFlags(self) -> int: ...
    @property
    def dwFormats(self) -> int: ...
    @property
    def dwChannels(self) -> int: ...

class PyDSOP_FILTER_FLAGS:
    @property
    def uplevel(self) -> PyDSOP_UPLEVEL_FILTER_FLAGS: ...
    @property
    def downlevel(self): ...

class PyDSOP_SCOPE_INIT_INFO:
    @property
    def type(self): ...
    @property
    def scope(self): ...
    @property
    def hr(self): ...
    @property
    def dcName(self) -> str: ...
    @property
    def filterFlags(self) -> PyDSOP_FILTER_FLAGS: ...

class PyDSOP_SCOPE_INIT_INFOs:
    def __new__(cls, size, /): ...

class PyDSOP_UPLEVEL_FILTER_FLAGS:
    @property
    def bothModes(self): ...
    @property
    def mixedModeOnly(self): ...
    @property
    def nativeModeOnly(self): ...

class PyFORMATETC: ...

class PyGFileOperationProgressSink:
    def StartOperations(self) -> None: ...
    def FinishOperations(self, Result, /) -> None: ...
    def PreRenameItem(self, Flags, Item: PyIShellItem, NewName, /) -> None: ...
    def PostRenameItem(self, Flags, Item: PyIShellItem, NewName, hrRename, NewlyCreated: PyIShellItem, /) -> None: ...
    def PreMoveItem(self, Flags, Item: PyIShellItem, DestinationFolder: PyIShellItem, NewName, /) -> None: ...
    def PostMoveItem(
        self, Flags, Item: PyIShellItem, DestinationFolder: PyIShellItem, NewName, hrMove, NewlyCreated: PyIShellItem, /
    ) -> None: ...
    def PreCopyItem(self, Flags, Item: PyIShellItem, DestinationFolder: PyIShellItem, NewName, /) -> None: ...
    def PostCopyItem(
        self, Flags, Item: PyIShellItem, DestinationFolder: PyIShellItem, NewName, hrCopy, NewlyCreated: PyIShellItem, /
    ) -> None: ...
    def PreDeleteItem(self, Flags, Item: PyIShellItem, /) -> None: ...
    def PostDeleteItem(self, Flags, Item: PyIShellItem, hrDelete, NewlyCreated: PyIShellItem, /) -> None: ...
    def PreNewItem(self, Flags, DestinationFolder: PyIShellItem, NewName, /) -> None: ...
    def PostNewItem(
        self, Flags, DestinationFolder: PyIShellItem, NewName, TemplateName, FileAttributes, hrNew, NewItem: PyIShellItem, /
    ) -> None: ...
    def UpdateProgress(self, WorkTotal, WorkSoFar, /) -> None: ...
    def ResetTimer(self) -> None: ...
    def PauseTimer(self) -> None: ...
    def ResumeTimer(self) -> None: ...

class PyGSecurityInformation:
    def GetObjectInformation(self) -> SI_OBJECT_INFO: ...
    def GetSecurity(self, RequestedInformation, Default, /) -> PySECURITY_DESCRIPTOR: ...
    def SetSecurity(self, SecurityInformation, SecurityDescriptor: PySECURITY_DESCRIPTOR, /) -> None: ...
    def GetAccessRights(self, ObjectType: PyIID, Flags, /) -> tuple[SI_ACCESS, Incomplete]: ...
    def MapGeneric(self, ObjectType: PyIID, AceFlags, Mask, /): ...
    def GetInheritTypes(self) -> tuple[SI_INHERIT_TYPE, ...]: ...
    def PropertySheetPageCallback(self, hwnd: int, Msg, Page, /) -> None: ...

class PyIADesktopP2:
    def UpdateAllDesktopSubscriptions(self) -> None: ...

class PyIADs:
    @property
    def ADsPath(self) -> str: ...
    @property
    def AdsPath(self) -> str: ...
    @property
    def Class(self) -> str: ...
    @property
    def GUID(self) -> str: ...
    @property
    def Name(self) -> str: ...
    @property
    def Parent(self) -> str: ...
    @property
    def Schema(self) -> str: ...
    def GetInfo(self) -> None: ...
    def SetInfo(self) -> None: ...
    def Get(self, prop: str, /): ...
    def Put(self, _property: str, val, /) -> None: ...
    def get(self, prop: str, /): ...
    def put(self, _property: str, val, /) -> None: ...

class PyIADsContainer:
    def GetObject(self, _class: str, relativeName: str, /) -> PyIDispatch: ...
    def get_Count(self): ...
    def get_Filter(self): ...
    def put_Filter(self, val, /) -> None: ...
    def get_Hints(self): ...
    def put_Hints(self, val, /) -> None: ...

class PyIADsUser:
    def get_AccountDisabled(self): ...
    def put_AccountDisabled(self, val, /) -> None: ...
    def get_AccountExpirationDate(self): ...
    def put_AccountExpirationDate(self, val: TimeType, /) -> None: ...
    def get_BadLoginAddress(self): ...
    def get_BadLoginCount(self): ...
    def get_Department(self): ...
    def put_Department(self, val, /) -> None: ...
    def get_Description(self): ...
    def put_Description(self, val, /) -> None: ...
    def get_Division(self): ...
    def put_Division(self, val, /) -> None: ...
    def get_EmailAddress(self): ...
    def put_EmailAddress(self, val, /) -> None: ...
    def get_EmployeeID(self): ...
    def put_EmployeeID(self, val, /) -> None: ...
    def get_FirstName(self): ...
    def put_FirstName(self, val, /) -> None: ...
    def get_FullName(self): ...
    def put_FullName(self, val, /) -> None: ...
    def get_HomeDirectory(self): ...
    def put_HomeDirectory(self, val, /) -> None: ...
    def get_HomePage(self): ...
    def put_HomePage(self, val, /) -> None: ...
    def get_LoginScript(self): ...
    def put_LoginScript(self, val, /) -> None: ...
    def SetPassword(self, val, /) -> None: ...
    def ChangePassword(self, oldval, newval, /) -> None: ...

class PyIActiveDesktop:
    def ApplyChanges(self, Flags, /) -> None: ...
    def GetWallpaper(self, cchWallpaper, Reserved: int = ..., /): ...
    def SetWallpaper(self, Wallpaper, Reserved: int = ..., /) -> None: ...
    def GetWallpaperOptions(self, Reserved: int = ..., /): ...
    def SetWallpaperOptions(self, Style, Reserved: int = ..., /) -> None: ...
    def GetPattern(self, cchPattern: int = ..., Reserved: int = ..., /) -> None: ...
    def SetPattern(self, Pattern, Reserved: int = ..., /) -> None: ...
    def GetDesktopItemOptions(self): ...
    def SetDesktopItemOptions(self, comp, Reserved: int = ..., /) -> None: ...
    def AddDesktopItem(self, comp, Reserved: int = ..., /) -> None: ...
    def AddDesktopItemWithUI(self, hwnd: int, comp, Flags, /) -> None: ...
    def ModifyDesktopItem(self, comp, Flags, /) -> None: ...
    def RemoveDesktopItem(self, comp, Reserved: int = ..., /) -> None: ...
    def GetDesktopItemCount(self) -> None: ...
    def GetDesktopItem(self, Component, Reserved: int = ..., /): ...
    def GetDesktopItemByID(self, ID, reserved: int = ..., /): ...
    def GenerateDesktopItemHtml(self, FileName, comp, Reserved: int = ..., /) -> None: ...
    def AddUrl(self, hwnd: int, Source, comp, Flags, /) -> None: ...
    def GetDesktopItemBySource(self, Source, Reserved: int = ..., /): ...

class PyIActiveDesktopP:
    def SetSafeMode(self, Flags, /) -> None: ...

class PyIActiveScriptDebug:
    def GetScriptTextAttributes(self, pstrCode: str, pstrDelimiter: str, dwFlags, /) -> tuple[Incomplete, ...]: ...
    def GetScriptletTextAttributes(self, pstrCode: str, pstrDelimiter: str, dwFlags, /) -> None: ...
    def EnumCodeContextsOfPosition(self, dwSourceContext, uCharacterOffset, uNumChars, /) -> None: ...

class PyIActiveScriptError:
    def GetExceptionInfo(self) -> None: ...
    def GetSourcePosition(self) -> None: ...
    def GetSourceLineText(self) -> None: ...

class PyIActiveScriptErrorDebug:
    def GetDocumentContext(self) -> None: ...
    def GetStackFrame(self) -> None: ...

class PyIActiveScriptParseProcedure:
    def ParseProcedureText(
        self,
        pstrCode,
        pstrFormalParams,
        pstrProcedureName,
        pstrItemName,
        punkContext: PyIUnknown,
        pstrDelimiter,
        dwSourceContextCookie,
        ulStartingLineNumber,
        dwFlags,
        /,
    ) -> None: ...

class PyIActiveScriptSite:
    def GetLCID(self): ...
    def GetItemInfo(self): ...
    def GetDocVersionString(self): ...
    def OnStateChange(self): ...
    def OnEnterScript(self): ...
    def OnLeaveScript(self): ...
    def OnScriptError(self): ...
    def OnScriptTerminate(self): ...

class PyIActiveScriptSiteDebug:
    def GetDocumentContextFromPosition(self, dwSourceContext, uCharacterOffset, uNumChars, /) -> None: ...
    def GetApplication(self) -> None: ...
    def GetRootApplicationNode(self) -> None: ...
    def OnScriptErrorDebug(self) -> tuple[Incomplete, Incomplete]: ...

class PyIAddrBook:
    def ResolveName(self, uiParm, flags, entryTitle: str, ADRlist, /) -> None: ...
    def OpenEntry(self, entryId: str, iid: PyIID, flags, /): ...
    def CompareEntryIDs(self, entryId: str, entryId1: str, flags: int = ..., /): ...

class PyIApplicationDebugger:
    def QueryAlive(self) -> None: ...
    def CreateInstanceAtDebugger(self, rclsid: PyIID, pUnkOuter: PyIUnknown, dwClsContext, riid: PyIID, /) -> None: ...
    def onDebugOutput(self, pstr, /) -> None: ...
    def onHandleBreakPoint(self, prpt: PyIRemoteDebugApplicationThread, br, pError, /) -> None: ...
    def onClose(self) -> None: ...
    def onDebuggerEvent(self, guid: PyIID, uUnknown: PyIUnknown, /) -> None: ...

class PyIApplicationDestinations:
    def SetAppID(self, AppID, /) -> None: ...
    def RemoveDestination(self, punk: PyIUnknown, /) -> None: ...
    def RemoveAllDestinations(self) -> None: ...

class PyIApplicationDocumentlists:
    def SetAppID(self, AppID, /) -> None: ...
    def Getlist(self, listType, riid: PyIID, ItemsDesired: int = ..., /) -> PyIEnumObjects: ...

class PyIAsyncOperation:
    def SetAsyncMode(self, fDoOpAsync, /) -> None: ...
    def GetAsyncMode(self): ...
    def StartOperation(self, pbcReserved: PyIBindCtx, /) -> None: ...
    def InOperation(self) -> None: ...
    def EndOperation(self, hResult, pbcReserved: PyIBindCtx, dwEffects, /) -> None: ...

class PyIAttach:
    def GetLastError(self, hr, flags, /): ...

class PyIBindCtx:
    def GetRunningObjectTable(self) -> PyIRunningObjectTable: ...
    def GetBindOptions(self) -> PyBIND_OPTS: ...
    def SetBindOptions(self, bindopts, /) -> None: ...
    def RegisterObjectParam(self, Key: str, punk: PyIUnknown, /) -> None: ...
    def RevokeObjectParam(self, Key: str, /) -> None: ...
    def GetObjectParam(self, Key: str, /) -> PyIUnknown: ...
    def EnumObjectParam(self) -> PyIEnumString: ...

class PyIBrowserFrameOptions:
    def GetFrameOptions(self, dwMask, /) -> None: ...

class PyICancelMethodCalls:
    def Cancel(self, Seconds, /) -> None: ...
    def TestCancel(self): ...

class PyICatInformation:
    def EnumCategories(self, lcid: int = ..., /) -> PyIEnumCATEGORYINFO: ...
    def GetCategoryDesc(self, lcid: int = ..., /) -> str: ...
    def EnumClassesOfCategories(
        self, listIIdImplemented: list[PyIID] | None = ..., listIIdRequired: Incomplete | None = ..., /
    ) -> PyIEnumGUID: ...

class PyICatRegister:
    def RegisterCategories(self, arg: list[tuple[PyIID, Incomplete, str]], /) -> None: ...
    def UnRegisterCategories(self, arg: list[PyIID], /) -> None: ...
    def RegisterClassImplCategories(self, clsid: PyIID, arg: list[PyIID], /) -> None: ...
    def UnRegisterClassImplCategories(self, clsid: PyIID, arg: list[PyIID], /) -> None: ...
    def RegisterClassReqCategories(self, clsid: PyIID, arg: list[PyIID], /) -> None: ...
    def UnRegisterClassReqCategories(self, clsid: PyIID, arg: list[PyIID], /) -> None: ...

class PyICategoryProvider:
    def CanCategorizeOnSCID(self, pscid, /) -> None: ...
    def GetDefaultCategory(self) -> None: ...
    def GetCategoryForSCID(self, pscid, /) -> None: ...
    def EnumCategories(self) -> None: ...
    def GetCategoryName(self, guid: PyIID, /) -> None: ...
    def CreateCategory(self, guid: PyIID, riid: PyIID, /) -> None: ...

class PyIClassFactory:
    def CreateInstance(self, outerUnknown: PyIUnknown, iid: PyIID, /) -> PyIUnknown: ...
    def LockServer(self, bInc, /) -> None: ...

class PyIClientSecurity:
    def QueryBlanket(self, Proxy: PyIUnknown, /): ...
    def SetBlanket(
        self, Proxy: PyIUnknown, AuthnSvc, AuthzSvc, ServerPrincipalName: str, AuthnLevel, ImpLevel, AuthInfo, Capabilities, /
    ) -> None: ...
    def CopyProxy(self, Proxy: PyIUnknown, /) -> PyIUnknown: ...

class PyIColumnProvider:
    def Initialize(self, psci, /) -> None: ...
    def GetColumnInfo(self, dwIndex, /) -> None: ...
    def GetItemData(self, pscid, pscd, /) -> None: ...

class PyIConnectionPoint:
    def GetConnectionInterface(self) -> PyIID: ...
    def GetConnectionPointContainer(self) -> PyIConnectionPointContainer: ...
    def Advise(self, unk: PyIUnknown, /): ...
    def Unadvise(self, cookie, /) -> None: ...
    def EnumConnections(self) -> PyIEnumConnections: ...

class PyIConnectionPointContainer:
    def EnumConnectionPoints(self) -> PyIEnumConnectionPoints: ...
    def FindConnectionPoint(self, iid: PyIID, /) -> PyIConnectionPoint: ...

class PyIContext:
    def SetProperty(self, rpolicyId: PyIID, flags, pUnk: PyIUnknown, /) -> None: ...
    def RemoveProperty(self, rPolicyId: PyIID, /) -> None: ...
    def GetProperty(self, rGuid: PyIID, /) -> tuple[Incomplete, PyIUnknown]: ...
    def EnumContextProps(self) -> PyIEnumContextProps: ...

class PyIContextMenu:
    def QueryContextMenu(self, hmenu: int, indexMenu, idCmdFirst, idCmdLast, uFlags, /): ...
    def InvokeCommand(self, pici: PyCMINVOKECOMMANDINFO, /) -> None: ...
    def GetCommandString(self, idCmd, uType, cchMax: int = ..., /): ...

class PyICopyHookA:
    def CopyCallback(self, hwnd: int, wFunc, wFlags, srcFile: str, srcAttribs, destFile: str, destAttribs, /) -> None: ...

class PyICopyHookW:
    def CopyCallback(self, hwnd: int, wFunc, wFlags, srcFile: str, srcAttribs, destFile: str, destAttribs, /) -> None: ...

class PyICreateTypeInfo:
    def SetGuid(self, guid: PyIID, /) -> None: ...
    def SetTypeFlags(self, uTypeFlags, /) -> None: ...
    def SetDocString(self, pStrDoc, /) -> None: ...
    def SetHelpContext(self, dwHelpContext, /) -> None: ...
    def SetVersion(self, wMajorVerNum, wMinorVerNum, /) -> None: ...
    def AddRefTypeInfo(self, pTInfo: PyITypeInfo, /) -> None: ...
    def AddFuncDesc(self, index, /) -> None: ...
    def AddImplType(self, index, hRefType, /) -> None: ...
    def SetImplTypeFlags(self, index, implTypeFlags, /) -> None: ...
    def SetAlignment(self, cbAlignment, /) -> None: ...
    def SetSchema(self, pStrSchema, /) -> None: ...
    def AddVarDesc(self, index, /) -> None: ...
    def SetFuncAndParamNames(self, index, rgszNames: tuple[Incomplete, ...], /) -> None: ...
    def SetVarName(self, index, szName, /) -> None: ...
    def SetTypeDescAlias(self) -> None: ...
    def DefineFuncAsDllEntry(self, index, szDllName, szProcName, /) -> None: ...
    def SetFuncDocString(self, index, szDocString, /) -> None: ...
    def SetVarDocString(self, index, szDocString, /) -> None: ...
    def SetFuncHelpContext(self, index, dwHelpContext, /) -> None: ...
    def SetVarHelpContext(self, index, dwHelpContext, /) -> None: ...
    def SetMops(self, index, bstrMops, /) -> None: ...
    def LayOut(self) -> None: ...

class PyICreateTypeLib:
    def CreateTypeInfo(self, szName, /) -> None: ...
    def SetName(self, szName, /) -> None: ...
    def SetVersion(self, wMajorVerNum, wMinorVerNum, /) -> None: ...
    def SetGuid(self, guid: PyIID, /) -> None: ...
    def SetDocString(self, szDoc, /) -> None: ...
    def SetHelpFileName(self, szHelpFileName, /) -> None: ...
    def SetHelpContext(self, dwHelpContext, /) -> None: ...
    def SetLcid(self) -> None: ...
    def SetLibFlags(self, uLibFlags, /) -> None: ...
    def SaveAllChanges(self) -> None: ...

class PyICreateTypeLib2:
    def CreateTypeInfo(self, szName, /) -> None: ...
    def SetName(self, szName, /) -> None: ...
    def SetVersion(self, wMajorVerNum, wMinorVerNum, /) -> None: ...
    def SetGuid(self, guid: PyIID, /) -> None: ...
    def SetDocString(self, szDoc, /) -> None: ...
    def SetHelpFileName(self, szHelpFileName, /) -> None: ...
    def SetHelpContext(self, dwHelpContext, /) -> None: ...
    def SetLcid(self) -> None: ...
    def SetLibFlags(self, uLibFlags, /) -> None: ...
    def SaveAllChanges(self) -> None: ...

class PyICurrentItem: ...

class PyICustomDestinationlist:
    def SetAppID(self, AppID, /) -> None: ...
    def Beginlist(self, riid: PyIID, /) -> tuple[Incomplete, PyIObjectArray]: ...
    def AppendCategory(self, Category, Items: PyIObjectArray, /) -> None: ...
    def AppendKnownCategory(self, Category, /) -> None: ...
    def AddUserTasks(self, Items: PyIObjectArray, /) -> None: ...
    def Commitlist(self) -> None: ...
    def GetRemovedDestinations(self, riid: PyIID, /) -> PyIObjectArray: ...
    def Deletelist(self, AppID: Incomplete | None = ..., /) -> None: ...
    def Abortlist(self) -> None: ...

class PyIDL: ...

class PyIDataObject:
    def GetData(self, pformatetcIn: PyFORMATETC, /) -> PySTGMEDIUM: ...
    def GetDataHere(self, pformatetcIn: PyFORMATETC, /) -> PySTGMEDIUM: ...
    def QueryGetData(self, pformatetc: PyFORMATETC, /) -> None: ...
    def GetCanonicalFormatEtc(self, pformatectIn: PyFORMATETC, /) -> PyFORMATETC: ...
    def SetData(self, pformatetc: PyFORMATETC, pmedium: PySTGMEDIUM, fRelease, /) -> None: ...
    def EnumFormatEtc(self, dwDirection, /) -> PyIEnumFORMATETC: ...
    def DAdvise(self, pformatetc: PyFORMATETC, advf, pAdvSink, /): ...
    def DUnadvise(self, dwConnection, /) -> None: ...
    def EnumDAdvise(self): ...

class PyIDebugApplication:
    def SetName(self, pstrName, /) -> None: ...
    def StepOutComplete(self) -> None: ...
    def DebugOutput(self, pstr, /) -> None: ...
    def StartDebugSession(self) -> None: ...
    def HandleBreakPoint(self, br, /): ...
    def Close(self) -> None: ...
    def GetBreakFlags(self): ...
    def GetCurrentThread(self) -> PyIDebugApplicationThread: ...
    def CreateAsyncDebugOperation(self, psdo: PyIDebugSyncOperation, /) -> None: ...
    def AddStackFrameSniffer(self, pdsfs: PyIDebugStackFrameSniffer, /): ...
    def RemoveStackFrameSniffer(self, dwCookie, /) -> None: ...
    def QueryCurrentThreadIsDebuggerThread(self) -> None: ...
    def SynchronousCallInDebuggerThread(self, pptc, dwParam1, dwParam2, dwParam3, /) -> None: ...
    def CreateApplicationNode(self) -> PyIDebugApplicationNode: ...
    def FireDebuggerEvent(self, guid, unknown: PyIUnknown, /) -> None: ...
    def HandleRuntimeError(self, pErrorDebug: PyIActiveScriptErrorDebug, pScriptSite: PyIActiveScriptSite, /) -> None: ...
    def FCanJitDebug(self) -> None: ...
    def FIsAutoJitDebugEnabled(self) -> None: ...
    def AddGlobalExpressionContextProvider(self, pdsfs: PyIProvideExpressionContexts, /) -> None: ...
    def RemoveGlobalExpressionContextProvider(self, dwCookie, /) -> None: ...

class PyIDebugApplicationNode:
    def EnumChildren(self) -> None: ...
    def GetParent(self) -> PyIDebugApplicationNode: ...
    def SetDocumentProvider(self, pddp: PyIDebugDocumentProvider, /) -> None: ...
    def Close(self) -> None: ...
    def Attach(self, pdanParent: PyIDebugApplicationNode, /) -> None: ...
    def Detach(self) -> None: ...

class PyIDebugApplicationNodeEvents:
    def onAddChild(self, prddpChild: PyIDebugApplicationNode, /) -> None: ...
    def onRemoveChild(self, prddpChild: PyIDebugApplicationNode, /) -> None: ...
    def onDetach(self) -> None: ...
    def onAttach(self, prddpParent: PyIDebugApplicationNode, /) -> None: ...

class PyIDebugApplicationThread:
    def SynchronousCallIntoThread(self, pstcb, dwParam1, dwParam2, dwParam3, /) -> None: ...
    def QueryIsCurrentThread(self) -> None: ...
    def QueryIsDebuggerThread(self) -> None: ...

class PyIDebugCodeContext:
    def GetDocumentContext(self) -> None: ...
    def SetBreakPoint(self, bps, /) -> None: ...

class PyIDebugDocument: ...

class PyIDebugDocumentContext:
    def GetDocument(self) -> None: ...
    def EnumCodeContexts(self) -> None: ...

class PyIDebugDocumentHelper:
    def Init(self, pda: PyIDebugApplication, pszShortName, pszLongName, docAttr, /) -> None: ...
    def Attach(self, pddhParent: PyIDebugDocumentHelper, /) -> None: ...
    def Detach(self) -> None: ...
    def AddUnicodeText(self, pszText, /) -> None: ...
    def AddDBCSText(self) -> None: ...
    def SetDebugDocumentHost(self, pddh: PyIDebugDocumentHost, /) -> None: ...
    def AddDeferredText(self, cChars, dwTextStartCookie, /) -> None: ...
    def DefineScriptBlock(self, ulCharOffset, cChars, pas, fScriptlet, /) -> None: ...
    def SetDefaultTextAttr(self, staTextAttr, /) -> None: ...
    def SetTextAttributes(self, ulCharOffset, obAttr, /) -> None: ...
    def SetLongName(self, pszLongName, /) -> None: ...
    def SetShortName(self, pszShortName, /) -> None: ...
    def SetDocumentAttr(self, pszAttributes, /) -> None: ...
    def GetDebugApplicationNode(self) -> None: ...
    def GetScriptBlockInfo(self, dwSourceContext, /) -> None: ...
    def CreateDebugDocumentContext(self, iCharPos, cChars, /) -> None: ...
    def BringDocumentToTop(self) -> None: ...
    def BringDocumentContextToTop(self, pddc: PyIDebugDocumentContext, /) -> None: ...

class PyIDebugDocumentHost:
    def GetDeferredText(self, dwTextStartCookie, cMaxChars, /) -> None: ...
    def GetScriptTextAttributes(self, pstrCode, pstrDelimiter, dwFlags, /) -> None: ...
    def OnCreateDocumentContext(self) -> None: ...
    def GetPathName(self) -> None: ...
    def GetFileName(self) -> None: ...
    def NotifyChanged(self) -> None: ...

class PyIDebugDocumentInfo:
    def GetName(self) -> None: ...
    def GetDocumentClassId(self) -> PyIID: ...

class PyIDebugDocumentProvider:
    def GetDocument(self) -> PyIDebugDocument: ...

class PyIDebugDocumentText:
    def GetDocumentAttributes(self) -> None: ...
    def GetSize(self) -> None: ...
    def GetPositionOfLine(self, cLineNumber, /) -> None: ...
    def GetLineOfPosition(self, cCharacterPosition, /) -> None: ...
    def GetText(self, cCharacterPosition, cMaxChars, bWantAttr: int = ..., /) -> None: ...
    def GetPositionOfContext(self, psc: PyIDebugDocumentContext, /) -> None: ...
    def GetContextOfPosition(self, cCharacterPosition, cNumChars, /) -> None: ...

class PyIDebugDocumentTextAuthor:
    def InsertText(self, cCharacterPosition, cNumToInsert, pcharText, /) -> None: ...
    def RemoveText(self, cCharacterPosition, cNumToRemove, /) -> None: ...
    def ReplaceText(self, cCharacterPosition, cNumToReplace, pcharText, /) -> None: ...

class PyIDebugDocumentTextEvents:
    def onDestroy(self) -> None: ...
    def onInsertText(self, cCharacterPosition, cNumToInsert, /) -> None: ...
    def onRemoveText(self, cCharacterPosition, cNumToRemove, /) -> None: ...
    def onReplaceText(self, cCharacterPosition, cNumToReplace, /) -> None: ...
    def onUpdateTextAttributes(self, cCharacterPosition, cNumToUpdate, /) -> None: ...
    def onUpdateDocumentAttributes(self, textdocattr, /) -> None: ...

class PyIDebugDocumentTextExternalAuthor:
    def GetPathName(self) -> None: ...
    def GetFileName(self) -> None: ...
    def NotifyChanged(self) -> None: ...

class PyIDebugExpression:
    def Start(self, pdecb: PyIDebugExpressionCallBack, /) -> None: ...
    def Abort(self) -> None: ...
    def QueryIsComplete(self) -> None: ...
    def GetResultAsString(self) -> None: ...
    def GetResultAsDebugProperties(self) -> None: ...

class PyIDebugExpressionCallBack:
    def onComplete(self) -> None: ...

class PyIDebugExpressionContext:
    def ParseLanguageText(self, pstrCode, nRadix, pstrDelimiter, dwFlags, /) -> None: ...
    def GetLanguageInfo(self) -> None: ...

class PyIDebugProperty:
    def GetPropertyInfo(self, dwFieldSpec, nRadix, /) -> None: ...
    def GetExtendedInfo(self) -> None: ...
    def SetValueAsString(self, pszValue, nRadix, /) -> None: ...
    def EnumMembers(self, dwFieldSpec, nRadix, refiid: PyIID, /) -> None: ...
    def GetParent(self) -> None: ...

class PyIDebugSessionProvider:
    def StartDebugSession(self, pda: PyIRemoteDebugApplication, /) -> None: ...

class PyIDebugStackFrame:
    def GetCodeContext(self) -> None: ...
    def GetDescriptionString(self, fLong, /): ...
    def GetLanguageString(self, fLong, /): ...
    def GetThread(self) -> PyIDebugApplicationThread: ...

class PyIDebugStackFrameSniffer:
    def EnumStackFrames(self) -> None: ...

class PyIDebugStackFrameSnifferEx:
    def EnumStackFramesEx(self) -> None: ...

class PyIDebugSyncOperation:
    def GetTargetThread(self) -> None: ...
    def Execute(self) -> None: ...
    def InProgressAbort(self) -> None: ...

class PyIDefaultExtractIconInit:
    def SetFlags(self, uFlags, /) -> None: ...
    def SetKey(self, hkey: PyHKEY, /) -> None: ...
    def SetNormalIcon(self, pszFile, iIcon, /) -> None: ...
    def SetOpenIcon(self, pszFile, iIcon, /) -> None: ...
    def SetShortcutIcon(self, pszFile, iIcon, /) -> None: ...
    def SetDefaultIcon(self, pszFile, iIcon, /) -> None: ...

class PyIDirectSound:
    def Initialize(self, guid: PyIID, /) -> None: ...
    def SetCooperativeLevel(self, hwnd: int, level, /) -> None: ...
    def CreateSoundBuffer(self, lpDSCBufferDesc: PyDSCBUFFERDESC, unk: Incomplete | None = ..., /) -> None: ...
    def GetCaps(self) -> None: ...
    def Compact(self) -> None: ...

class PyIDirectSoundBuffer:
    def Initialize(self) -> None: ...
    def GetStatus(self) -> None: ...
    def GetCaps(self) -> None: ...
    def Restore(self) -> None: ...
    def GetCurrentPosition(self) -> None: ...
    def Play(self) -> None: ...
    def SetCurrentPosition(self) -> None: ...
    def Stop(self) -> None: ...
    def GetFrequency(self) -> None: ...
    def GetPan(self) -> None: ...
    def GetVolume(self) -> None: ...
    def SetFrequency(self) -> None: ...
    def SetPan(self) -> None: ...
    def SetVolume(self) -> None: ...

class PyIDirectSoundCapture:
    def Initialize(self) -> None: ...
    def GetCaps(self) -> None: ...

class PyIDirectSoundCaptureBuffer:
    def Initialize(self) -> None: ...
    def GetStatus(self) -> None: ...
    def GetCurrentPosition(self) -> None: ...
    def Stop(self) -> None: ...

class PyIDirectSoundNotify: ...

class PyIDirectoryObject:
    def GetObjectInformation(self) -> PyADS_OBJECT_INFO: ...
    def GetObjectAttributes(self, names: tuple[str, ...], /) -> tuple[PyADS_ATTR_INFO, ...]: ...
    def SetObjectAttributes(self, attrs: tuple[PyADS_ATTR_INFO, ...], /): ...
    def CreateDSObject(self, rdn: str, attrs: tuple[PyADS_ATTR_INFO, ...], /) -> PyIDispatch: ...
    def DeleteDSObject(self, rdn: str, /) -> None: ...

class PyIDirectorySearch:
    def SetSearchPreference(self, prefs, /) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def ExecuteSearch(self, _filter: str, attrNames: list[str], /): ...
    def GetNextRow(self, handle, /): ...
    def GetFirstRow(self, handle, /): ...
    def GetPreviousRow(self, handle, /): ...
    def CloseSearchHandle(self, handle, /) -> None: ...
    def AdandonSearch(self, handle, /) -> None: ...
    def GetColumn(self, handle, name: str, /) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def GetNextColumnName(self) -> None: ...

@final
class PyIDispatch:
    def Invoke(self, dispid, lcid, flags, bResultWanted, arg: tuple[Incomplete, ...], /): ...
    def InvokeTypes(
        self, dispid, lcid, wFlags, resultTypeDesc, typeDescs: tuple[Incomplete, ...], args: tuple[Incomplete, ...], /
    ): ...
    def GetIDsOfNames(self, name: str, arg, /) -> tuple[Incomplete, Incomplete]: ...
    def GetTypeInfo(self, locale, index: int = ..., /) -> PyITypeInfo: ...
    def GetTypeInfoCount(self): ...

class PyIDispatchEx:
    def GetDispID(self, name: str, fdex, /): ...
    def InvokeEx(
        self,
        dispid,
        lcid,
        flags,
        args: list[Incomplete],
        types: list[Incomplete] | None = ...,
        returnDesc: int = ...,
        serviceProvider: PyIServiceProvider | None = ...,
        /,
    ): ...
    def DeleteMemberByName(self, name: str, fdex, /) -> None: ...
    def DeleteMemberByDispID(self, dispid, /) -> None: ...
    def GetMemberProperties(self, dispid, fdex, /): ...
    def GetMemberName(self, dispid, /): ...
    def GetNextDispID(self, fdex, dispid, /): ...

class PyIDisplayItem: ...

class PyIDocHostUIHandler:
    def ShowContextMenu(
        self, dwID, pt: tuple[Incomplete, Incomplete], pcmdtReserved: PyIUnknown, pdispReserved: PyIDispatch, /
    ) -> None: ...
    def GetHostInfo(self) -> None: ...
    def ShowUI(
        self,
        dwID,
        pActiveObject: PyIOleInPlaceActiveObject,
        pCommandTarget: PyIOleCommandTarget,
        pFrame: PyIOleInPlaceFrame,
        pDoc: PyIOleInPlaceUIWindow,
        /,
    ) -> None: ...
    def HideUI(self) -> None: ...
    def UpdateUI(self) -> None: ...
    def EnableModeless(self, fEnable, /) -> None: ...
    def OnDocWindowActivate(self, fActivate, /) -> None: ...
    def OnFrameWindowActivate(self, fActivate, /) -> None: ...
    def ResizeBorder(
        self, prcBorder: tuple[Incomplete, Incomplete, Incomplete, Incomplete], pUIWindow: PyIOleInPlaceUIWindow, fRameWindow, /
    ) -> None: ...
    def TranslateAccelerator(self, lpMsg, pguidCmdGroup: PyIID, nCmdID, /) -> None: ...
    def GetOptionKeyPath(self, dw, /) -> None: ...
    def GetDropTarget(self, pDropTarget: PyIDropTarget, /) -> None: ...
    def GetExternal(self) -> None: ...
    def TranslateUrl(self, dwTranslate, pchURLIn, /) -> None: ...
    def FilterDataObject(self, pDO: PyIDataObject, /) -> None: ...

class PyIDropSource:
    def QueryContinueDrag(self, fEscapePressed, grfKeyState, /) -> None: ...
    def GiveFeedback(self, dwEffect, /) -> None: ...

class PyIDropTarget:
    def DragEnter(self, pDataObj: PyIDataObject, grfKeyState, pt: tuple[Incomplete, Incomplete], pdwEffect, /): ...
    def DragOver(self, grfKeyState, pt: tuple[Incomplete, Incomplete], pdwEffect, /): ...
    def DragLeave(self) -> None: ...
    def Drop(self, pDataObj: PyIDataObject, grfKeyState, pt: tuple[Incomplete, Incomplete], dwEffect, /): ...

class PyIDropTargetHelper:
    def DragEnter(self, hwnd: int, pDataObj: PyIDataObject, pt: tuple[Incomplete, Incomplete], dwEffect, /) -> None: ...
    def DragOver(self, hwnd: int, pt: tuple[Incomplete, Incomplete], pdwEffect, /) -> None: ...
    def DragLeave(self) -> None: ...
    def Drop(self, pDataObj: PyIDataObject, pt: tuple[Incomplete, Incomplete], dwEffect, /) -> None: ...

class PyIDsObjectPicker:
    def Initialize(
        self, targetComputer: str, scopeInfos: PyDSOP_SCOPE_INIT_INFOs, options: int = ..., attrNames: list[str] | None = ..., /
    ) -> None: ...
    def InvokeDialog(self, hwnd: int, /) -> PyIDataObject: ...

class PyIEmptyVolumeCache: ...
class PyIEmptyVolumeCache2: ...

class PyIEmptyVolumeCacheCallBack:
    def ScanProgress(self, dwlSpaceUsed, dwFlags, pcwszStatus, /) -> None: ...
    def PurgeProgress(self, dwlSpaceFreed, spaceFreed, spaceToFree, flags, status, /) -> None: ...

class PyIEnumCATEGORYINFO:
    def Next(self, num: int = ..., /) -> tuple[tuple[PyIID, Incomplete, str], ...]: ...
    def Skip(self, num, /) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumCATEGORYINFO: ...

class PyIEnumConnectionPoints:
    def Next(self, num: int = ..., /) -> tuple[PyIConnectionPoint, ...]: ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumConnectionPoints: ...

class PyIEnumConnections:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumConnections: ...

class PyIEnumContextProps:
    def Next(self, num: int = ..., /) -> tuple[tuple[PyIID, Incomplete, PyIUnknown], ...]: ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumContextProps: ...

class PyIEnumDebugApplicationNodes:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumDebugApplicationNodes: ...

class PyIEnumDebugCodeContexts:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumDebugCodeContexts: ...

class PyIEnumDebugExpressionContexts:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumDebugExpressionContexts: ...

class PyIEnumDebugPropertyInfo:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumDebugPropertyInfo: ...
    def GetCount(self): ...

class PyIEnumDebugStackFrames:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumDebugStackFrames: ...

class PyIEnumExplorerCommand:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumExplorerCommand: ...

class PyIEnumFORMATETC:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumFORMATETC: ...

class PyIEnumGUID:
    def Next(self, num: int = ..., /) -> tuple[PyIID, ...]: ...
    def Skip(self, num, /) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumGUID: ...

class PyIEnumIDlist:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumIDlist: ...

class PyIEnumMoniker:
    def Next(self, num: int = ..., /) -> PyIMoniker: ...
    def Skip(self, num, /) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumMoniker: ...

class PyIEnumObjects:
    def Next(self, riid: PyIID, num: int = ..., /) -> tuple[PyIUnknown, ...]: ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumObjects: ...

class PyIEnumRemoteDebugApplicationThreads:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumRemoteDebugApplicationThreads: ...

class PyIEnumRemoteDebugApplications:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumRemoteDebugApplications: ...

class PyIEnumResources:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumResources: ...

class PyIEnumSTATPROPSETSTG:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumSTATPROPSETSTG: ...

class PyIEnumSTATPROPSTG:
    def Next(self, num: int = ..., /): ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumSTATPROPSTG: ...

class PyIEnumSTATSTG:
    def Next(self, num: int = ..., /) -> tuple[STATSTG, ...]: ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumSTATSTG: ...

class PyIEnumShellItems:
    def Next(self, num: int = ..., /) -> tuple[PyIShellItem, ...]: ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumShellItems: ...

class PyIEnumString:
    def Next(self, num: int = ..., /) -> tuple[str, ...]: ...
    def Skip(self) -> None: ...
    def Reset(self) -> None: ...
    def Clone(self) -> PyIEnumString: ...

class PyIErrorLog:
    def AddError(self, propName: str, excepInfo: Incomplete | None = ..., /) -> None: ...

class PyIExplorerBrowser:
    def Initialize(self, hwndParent, prc: PyRECT, pfs, /) -> None: ...
    def Destroy(self) -> None: ...
    def SetRect(self, hdwp, rcBrowser: PyRECT, /) -> int: ...
    def SetPropertyBag(self, PropertyBag, /) -> None: ...
    def SetEmptyText(self, EmptyText, /) -> None: ...
    def SetFolderSettings(self, pfs, /) -> None: ...
    def Advise(self, psbe: PyIExplorerBrowserEvents, /): ...
    def Unadvise(self, dwCookie, /) -> None: ...
    def SetOptions(self, dwFlag, /) -> None: ...
    def GetOptions(self): ...
    def BrowseToIDlist(self, pidl, uFlags, /) -> None: ...
    def BrowseToObject(self, punk: PyIUnknown, uFlags, /) -> None: ...
    def FillFromObject(self, punk: PyIUnknown, dwFlags, /) -> None: ...
    def RemoveAll(self) -> None: ...
    def GetCurrentView(self, riid: PyIID, /) -> PyIUnknown: ...

class PyIExplorerBrowserEvents:
    def OnNavigationPending(self, pidlFolder, /) -> None: ...
    def OnViewCreated(self, psv: PyIShellView, /) -> None: ...
    def OnNavigationComplete(self, pidlFolder, /) -> None: ...
    def OnNavigationFailed(self, pidlFolder, /) -> None: ...

class PyIExplorerCommand:
    def GetTitle(self, psiItemArray: PyIShellItemArray, /): ...
    def GetIcon(self, psiItemArray: PyIShellItemArray, /): ...
    def GetToolTip(self, psiItemArray: PyIShellItemArray, /): ...
    def GetCanonicalName(self) -> PyIID: ...
    def GetState(self, psiItemArray: PyIShellItemArray, fOkToBeSlow, /): ...
    def Invoke(self, psiItemArray: PyIShellItemArray, pbc: PyIBindCtx, /) -> None: ...
    def GetFlags(self): ...
    def EnumSubCommands(self) -> PyIEnumExplorerCommand: ...

class PyIExplorerCommandProvider: ...
class PyIExplorerPaneVisibility: ...

class PyIExternalConnection:
    def AddConnection(self, extconn, reserved: int = ..., /): ...
    def ReleaseConnection(self, extconn, reserved, fLastReleaseCloses, /): ...

class PyIExtractIcon:
    def Extract(self, pszFile, nIconIndex, nIconSize, /) -> None: ...
    def GetIconLocation(self, uFlags, cchMax, /) -> None: ...

class PyIExtractIconW:
    def Extract(self, pszFile, nIconIndex, nIconSize, /) -> None: ...
    def GetIconLocation(self, uFlags, cchMax, /) -> None: ...

class PyIExtractImage:
    def GetLocation(self, dwPriority, size: tuple[Incomplete, Incomplete], dwRecClrDepth, pdwFlags, /) -> None: ...
    def Extract(self) -> None: ...

class PyIFileOperation:
    def Advise(self, Sink: PyGFileOperationProgressSink, /): ...
    def Unadvise(self, Cookie, /) -> None: ...
    def SetOperationFlags(self, OperationFlags, /) -> None: ...
    def SetProgressMessage(self, Message, /) -> None: ...
    def SetProgressDialog(self, popd, /) -> None: ...
    def SetProperties(self, proparray: PyIPropertyChangeArray, /) -> None: ...
    def SetOwnerWindow(self, Owner: int, /) -> None: ...
    def ApplyPropertiesToItem(self, Item: PyIShellItem, /) -> None: ...
    def ApplyPropertiesToItems(self, Items: PyIUnknown, /) -> None: ...
    def RenameItem(self, Item: PyIShellItem, NewName, Sink: PyGFileOperationProgressSink | None = ..., /) -> None: ...
    def RenameItems(self, pUnkItems: PyIUnknown, NewName, /) -> None: ...
    def MoveItem(
        self,
        Item: PyIShellItem,
        DestinationFolder: PyIShellItem,
        pszNewName: Incomplete | None = ...,
        Sink: PyGFileOperationProgressSink | None = ...,
        /,
    ) -> None: ...
    def MoveItems(self, Items: PyIUnknown, DestinationFolder: PyIShellItem, /) -> None: ...
    def CopyItem(
        self,
        Item: PyIShellItem,
        DestinationFolder: PyIShellItem,
        CopyName: Incomplete | None = ...,
        Sink: PyGFileOperationProgressSink | None = ...,
        /,
    ) -> None: ...
    def CopyItems(self, Items: PyIUnknown, DestinationFolder: PyIShellItem, /) -> None: ...
    def DeleteItem(self, Item: PyIShellItem, Sink: PyGFileOperationProgressSink | None = ..., /) -> None: ...
    def DeleteItems(self, Items: PyIUnknown, /) -> None: ...
    def NewItem(
        self,
        DestinationFolder: PyIShellItem,
        FileAttributes,
        Name,
        TemplateName: Incomplete | None = ...,
        Sink: PyGFileOperationProgressSink | None = ...,
        /,
    ) -> None: ...
    def PerformOperations(self) -> None: ...
    def GetAnyOperationsAborted(self): ...

class PyIFolderView:
    def GetCurrentViewMode(self): ...
    def SetCurrentViewMode(self, ViewMode: int, /): ...
    def GetFolder(self, riid: PyIID | None, /): ...
    def Item(self, iItemIndex: int, /): ...
    def ItemCount(self, uFlags: int, /): ...
    def Items(self) -> NoReturn: ...  # Not Implemented
    def GetSelectionMarkedItem(self): ...
    def GetFocusedItem(self): ...
    def GetItemPosition(self, pidl: PyIDL | None, /): ...
    def GetSpacing(self, pt_x: int, pt_y: int, /): ...
    def GetDefaultSpacing(self): ...
    def GetAutoArrange(self): ...
    def SelectItem(self, iItem: int, dwFlags: int, /): ...
    def SelectAndPositionItems(self) -> NoReturn: ...  # Not Implemented
    def SelectAndPositionItem(self, apidl: PyIDL | None, pt: tuple[int, int], dwFlags: int, /): ...

class PyIIdentityName: ...

class PyIInitializeWithFile:
    def Initialize(self, FilePath, Mode, /) -> None: ...

class PyIInitializeWithStream:
    def Initialize(self, Stream: PyIStream, Mode, /) -> None: ...

class PyIInputObject:
    def TranslateAccelerator(self, pmsg, /) -> None: ...
    def UIActivate(self, uState, /) -> None: ...
    def HasFocusIO(self) -> None: ...

class PyIInternetBindInfo:
    def GetBindInfo(self) -> None: ...
    def GetBindString(self) -> None: ...

class PyIInternetPriority:
    def SetPriority(self, nPriority, /) -> None: ...
    def GetPriority(self) -> None: ...

class PyIInternetProtocol:
    def Read(self, cb, /) -> None: ...
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def Seek(self, dlibMove: tuple[int, int], dwOrigin, /) -> None: ...
    @overload
    def Seek(self, dlibMove: int, dwOrigin, /) -> None: ...
    def LockRequest(self, dwOptions, /) -> None: ...
    def UnlockRequest(self) -> None: ...

class PyIInternetProtocolInfo:
    def ParseUrl(self, pwzUrl, ParseAction, dwParseFlags, cchResult, dwReserved, /) -> None: ...
    def CombineUrl(self, pwzBaseUrl, pwzRelativeUrl, dwCombineFlags, cchResult, dwReserved, /) -> None: ...
    def CompareUrl(self, pwzUrl1, pwzUrl2, dwCompareFlags, /) -> None: ...
    def QueryInfo(self, pwzUrl, OueryOption, dwQueryFlags, cbBuffer, dwReserved, /): ...

class PyIInternetProtocolRoot:
    def Start(
        self, szUrl, pOIProtSink: PyIInternetProtocolSink, pOIBindInfo: PyIInternetBindInfo, grfPI, dwReserved, /
    ) -> None: ...
    def Continue(self) -> None: ...
    def Abort(self, hrReason, dwOptions, /) -> None: ...
    def Terminate(self, dwOptions, /) -> None: ...
    def Suspend(self) -> None: ...
    def Resume(self) -> None: ...

class PyIInternetProtocolSink:
    def Switch(self) -> None: ...
    def ReportProgress(self, ulStatusCode, szStatusText, /) -> None: ...
    def ReportData(self, grfBSCF, ulProgress, ulProgressMax, /) -> None: ...
    def ReportResult(self, hrResult, dwError, szResult, /) -> None: ...

class PyIInternetSecurityManager:
    def SetSecuritySite(self, pSite, /) -> None: ...
    def GetSecuritySite(self) -> None: ...
    def MapUrlToZone(self, pwszUrl, dwFlags, /) -> None: ...
    def GetSecurityId(self, pwszUrl, pcbSecurityId, /) -> None: ...
    def ProcessUrlAction(self, pwszUrl, dwAction, context, dwFlags, /) -> None: ...
    def SetZoneMapping(self, dwZone, lpszPattern, dwFlags, /) -> None: ...
    def GetZoneMappings(self, dwZone, dwFlags, /) -> None: ...

class PyIKnownFolder:
    def GetId(self) -> PyIID: ...
    def GetCategory(self): ...
    def GetShellItem(self, riid: PyIID, Flags: int = ..., /) -> PyIShellItem: ...
    def GetPath(self, Flags: int = ..., /): ...
    def SetPath(self, Flags, Path, /) -> None: ...
    def GetIDlist(self, Flags, /) -> PyIDL: ...
    def GetFolderType(self) -> PyIID: ...
    def GetRedirectionCapabilities(self): ...
    def GetFolderDefinition(self): ...

class PyIKnownFolderManager:
    def FolderIdFromCsidl(self, Csidl, /) -> PyIID: ...
    def FolderIdToCsidl(self, _id: PyIID, /): ...
    def GetFolderIds(self) -> tuple[PyIID, ...]: ...
    def GetFolder(self, _id: PyIID, /) -> PyIKnownFolder: ...
    def GetFolderByName(self, Name, /) -> PyIKnownFolder: ...
    def RegisterFolder(self, _id: PyIID, Definition, /) -> None: ...
    def UnregisterFolder(self, _id: PyIID, /) -> None: ...
    def FindFolderFromPath(self, Path, Mode, /) -> PyIKnownFolder: ...
    def FindFolderFromIDlist(self, pidl: PyIDL, /) -> PyIKnownFolder: ...
    def Redirect(self, _id: PyIID, hwnd: int, flags, TargetPath, Exclusion: tuple[PyIID, ...], /) -> None: ...

class PyILockBytes:
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def ReadAt(self, ulOffset: tuple[int, int], cb, /) -> str: ...
    @overload
    def ReadAt(self, ulOffset: int, cb, /) -> str: ...
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def WriteAt(self, ulOffset: tuple[int, int], data: str, /): ...
    @overload
    def WriteAt(self, ulOffset: int, data: str, /): ...
    def Flush(self) -> None: ...
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def SetSize(self, cb: tuple[int, int], /) -> None: ...
    @overload
    def SetSize(self, cb: int, /) -> None: ...
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def LockRegion(self, libOffset: tuple[int, int], cb: tuple[int, int], dwLockType, /) -> None: ...
    @overload
    def LockRegion(self, libOffset: int, cb: int, dwLockType, /) -> None: ...
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def UnlockRegion(self, libOffset: tuple[int, int], cb: tuple[int, int], dwLockType, /) -> None: ...
    @overload
    def UnlockRegion(self, libOffset: int, cb: int, dwLockType, /) -> None: ...

class PyIMAPIContainer:
    def OpenEntry(self, entryId: str, iid: PyIID, flags, /): ...
    def GetContentsTable(self, flags, /) -> PyIMAPITable: ...
    def GetHierarchyTable(self, flags, /) -> PyIMAPITable: ...

class PyIMAPIFolder:
    def GetLastError(self, hr, flags, /): ...
    def CreateFolder(
        self, folderType, folderName: str, folderComment: str | None = ..., iid: PyIID | None = ..., flags=..., /
    ) -> PyIMAPIFolder: ...
    def CreateMessage(self, iid: PyIID, flags, /) -> PyIMessage: ...
    def CopyMessages(self, msgs: PySBinaryArray, iid: PyIID, folder: PyIMAPIFolder, ulUIParam, progress, flags, /): ...
    def DeleteFolder(self, entryId: str, uiParam, progress, /) -> None: ...
    def DeleteMessages(self, msgs: PySBinaryArray, uiParam, progress, flags, /): ...
    def EmptyFolder(self, uiParam, progress, flags, /): ...
    def SetReadFlags(self, msgs: PySBinaryArray, uiParam, progress, flag, /) -> None: ...

class PyIMAPIProp:
    def GetProps(self, proplist: PySPropTagArray, flags: int = ..., /) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def DeleteProps(
        self, proplist: PySPropTagArray, wantProblems: bool = ..., /
    ) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def SetProps(
        self, proplist: tuple[Incomplete, Incomplete], wantProblems: bool = ..., /
    ) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def CopyTo(
        self,
        IIDExcludelist: tuple[Incomplete, Incomplete],
        propTags: PySPropTagArray,
        uiParam,
        progress,
        resultIID: PyIID,
        dest: PyIMAPIProp,
        flags,
        wantProblems: bool = ...,
        /,
    ) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def CopyProps(
        self,
        propTags: PySPropTagArray,
        uiParam,
        progress,
        resultIID: PyIID,
        dest: PyIMAPIProp,
        flags,
        wantProblems: bool = ...,
        /,
    ) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def OpenProperty(self, propTag, iid: PyIID, interfaceOptions, flags, /) -> PyIUnknown: ...
    def GetIDsFromNames(self, nameIds: PyMAPINAMEIDArray, flags: int = ..., /) -> PySPropTagArray: ...
    def GetNamesFromIDs(
        self, propTags: PySPropTagArray, propSetGuid: PyIID | None = ..., flags=..., /
    ) -> tuple[Incomplete, PySPropTagArray, PyMAPINAMEIDArray]: ...
    def GetLastError(self, hr, flags, /): ...
    def SaveChanges(self, flags, /) -> None: ...
    def GetProplist(self, flags, /) -> PySPropTagArray: ...

class PyIMAPISession:
    def OpenEntry(self, entryId: str, iid: PyIID, flags, /): ...
    def OpenMsgStore(self, uiParam, entryId: str, iid: PyIID, flags, /) -> PyIUnknown: ...
    def QueryIdentity(self) -> str: ...
    def Advise(self, entryId: str, mask, sink, /): ...
    def Unadvise(self, connection, /) -> None: ...
    def CompareEntryIDs(self, entryId: str, entryId1: str, flags: int = ..., /): ...
    def GetLastError(self, hr, flags, /): ...
    def GetMsgStoresTable(self, flags, /) -> PyIMAPITable: ...
    def GetStatusTable(self, flags, /) -> PyIMAPITable: ...
    def Logoff(self, uiParm, flags, reserved, /) -> None: ...
    def OpenAddressBook(self, uiParm, iid: PyIID, flags, /) -> PyIAddrBook: ...
    def OpenProfileSection(self, iidSection: PyIID, iid: PyIID, flags, /): ...
    def AdminServices(self, flags: int = ..., /) -> PyIMsgServiceAdmin: ...

class PyIMAPIStatus:
    def ChangePassword(self, oldPassword, newPassword, ulFlags, /) -> None: ...
    def SettingsDialog(self, ulUIParam, ulFlags, /) -> None: ...
    def ValidateState(self, ulUIParam, ulFlags, /) -> None: ...
    def FlushQueues(self, ulUIParam, transport: str, ulFlags, /) -> None: ...

class PyIMAPITable:
    def GetLastError(self, hr, flags, /): ...
    def Advise(self, eventMask, adviseSink, /): ...
    def SeekRow(self, bookmark, rowCount, /): ...
    def SeekRowApprox(self, numerator, denominator, /) -> None: ...
    def GetRowCount(self, flags, /): ...
    def QueryRows(self, rowCount, flags, /): ...
    def SetColumns(self, propTags, flags, /) -> None: ...
    def GetStatus(self) -> None: ...
    def QueryPosition(self) -> None: ...
    def QueryColumns(self, flags, /): ...
    def Abort(self) -> None: ...
    def FreeBookmark(self, bookmark, /) -> None: ...
    def CreateBookmark(self): ...
    def Restrict(self, restriction: PySRestriction, flags, /) -> None: ...
    def FindRow(self, restriction: PySRestriction, bookmarkOrigin, flags, /) -> None: ...
    def SortTable(self, sortOrderSet: PySSortOrderSet, flags, /) -> None: ...
    def Unadvise(self, handle, /) -> None: ...

class PyIMachineDebugManager:
    def AddApplication(self, pda: PyIRemoteDebugApplication, /) -> None: ...
    def RemoveApplication(self, dwAppCookie, /) -> None: ...
    def EnumApplications(self) -> None: ...

class PyIMachineDebugManagerEvents:
    def onAddApplication(self, pda: PyIRemoteDebugApplication, dwAppCookie, /) -> None: ...
    def onRemoveApplication(self, pda: PyIRemoteDebugApplication, dwAppCookie, /) -> None: ...

class PyIMessage:
    def SetReadFlag(self, flag, /) -> None: ...
    def GetAttachmentTable(self, flags, /) -> PyIMAPITable: ...
    def OpenAttach(self, attachmentNum, interface: PyIID, flags, /) -> PyIAttach: ...
    def CreateAttach(self, interface: PyIID, flags, /) -> tuple[Incomplete, PyIAttach]: ...
    def DeleteAttach(self, attachmentNum, ulUIParam, interface, flags, /) -> None: ...
    def ModifyRecipients(self, flags, mods, /) -> None: ...
    def GetRecipientTable(self, flags, /) -> PyIMAPITable: ...
    def SubmitMessage(self, flags, /) -> None: ...

class PyIMoniker:
    def BindToObject(self, bindCtx: PyIBindCtx, moniker: PyIMoniker, iidResult, /) -> PyIUnknown: ...
    def BindToStorage(self, bindCtx: PyIBindCtx, moniker: PyIMoniker, iidResult, /) -> PyIUnknown: ...
    def GetDisplayName(self, bindCtx: PyIBindCtx, moniker: PyIMoniker, /) -> str: ...
    def ComposeWith(self, mkRight: PyIMoniker, fOnlyIfNotGeneric, /) -> PyIMoniker: ...
    def Enum(self, fForward: bool = ..., /) -> PyIEnumMoniker: ...
    def IsEqual(self, other: PyIMoniker, /) -> bool: ...
    def IsSystemMoniker(self) -> bool: ...
    def Hash(self): ...

class PyIMsgServiceAdmin:
    def GetLastError(self, hr, flags, /): ...
    def CreateMsgService(self, serviceName: str, displayName: str, flags, uiParam: int = ..., /) -> None: ...
    def ConfigureMsgService(self, iid: PyIID, ulUIParam, ulFlags, arg: list[Incomplete], /) -> None: ...
    def GetMsgServiceTable(self, flags, /) -> PyIMAPITable: ...
    def GetProviderTable(self, flags, /) -> PyIMAPITable: ...
    def DeleteMsgService(self, uuid: PyIID, /) -> None: ...
    @deprecated("This is deprecated, and there is no replacement referenced to use instead.")
    def RenameMsgService(self, uuid: PyIID, flags, newName: str, /) -> None: ...
    def OpenProfileSection(self, uuid: PyIID, iid: PyIID, flags, /): ...
    def AdminProviders(self, uuid: PyIID, flags, /): ...

class PyIMsgStore:
    def OpenEntry(self, entryId: str, iid: PyIID, flags, /): ...
    def StoreLogoff(self, flags: int, /): ...
    def GetReceiveFolder(self, messageClass: str | None = ..., flags: int = ..., /) -> tuple[PyIID, str]: ...
    def GetReceiveFolderTable(self, flags, /) -> PyIMAPITable: ...
    def CompareEntryIDs(self, entryId: str, entryId1: str, flags: int = ..., /): ...
    def GetLastError(self, hr, flags, /): ...
    def AbortSubmit(self, entryId: str, flags: int = ..., /): ...
    def Advise(self, entryId: str, eventMask, adviseSink, /) -> None: ...
    def Unadvise(self, connection, /) -> None: ...

class PyINameSpaceTreeControl:
    def Initialize(self, hwndParent, prc: tuple[Incomplete, Incomplete, Incomplete, Incomplete], nsctsFlags, /) -> None: ...
    def TreeAdvise(self, punk: PyIUnknown, /) -> None: ...
    def TreeUnadvise(self, dwCookie, /) -> None: ...
    def AppendRoot(self, psiRoot: PyIShellItem, grfEnumFlags, grfRootStyle, pif, /) -> None: ...
    def InsertRoot(self, iIndex, psiRoot: PyIShellItem, grfEnumFlags, grfRootStyle, pif, /) -> None: ...
    def RemoveRoot(self, psiRoot: PyIShellItem, /) -> None: ...
    def RemoveAllRoots(self) -> None: ...
    def GetRootItems(self) -> None: ...
    def SetItemState(self, psi: PyIShellItem, nstcisMask, nstcisFlags, /) -> None: ...
    def GetItemState(self, psi: PyIShellItem, nstcisMask, /) -> None: ...
    def GetSelectedItems(self) -> None: ...
    def GetItemCustomState(self, psi: PyIShellItem, /) -> None: ...
    def SetItemCustomState(self, psi: PyIShellItem, iStateNumber, /) -> None: ...
    def EnsureItemVisible(self, psi: PyIShellItem, /) -> None: ...
    def SetTheme(self, pszTheme, /) -> None: ...
    def GetNextItem(self, psi: PyIShellItem, nstcgi, /) -> None: ...
    def HitTest(self, pt: tuple[Incomplete, Incomplete], /) -> None: ...
    def GetItemRect(self) -> None: ...
    def CollapseAll(self) -> None: ...

class PyINamedPropertyStore:
    def GetNamedValue(self, Name, /) -> PyPROPVARIANT: ...
    def SetNamedValue(self, propvar, /) -> None: ...
    def GetNameCount(self): ...
    def GetNameAt(self, Index, /): ...

class PyIObjectArray:
    def GetCount(self): ...
    def GetAt(self, Index, riid: PyIID, /) -> PyIUnknown: ...

class PyIObjectCollection:
    def AddObject(self, punk: PyIUnknown, /) -> None: ...
    def AddFromArray(self, Source: PyIObjectArray, /) -> None: ...
    def RemoveObjectAt(self, Index, /) -> None: ...
    def Clear(self) -> None: ...

class PyIObjectWithPropertyKey:
    def SetPropertyKey(self, key: PyPROPERTYKEY, /) -> None: ...
    def GetPropertyKey(self) -> PyPROPERTYKEY: ...

class PyIObjectWithSite:
    def SetSite(self, pUnkSite, /) -> None: ...
    def GetSite(self, riid: PyIID, /) -> None: ...

class PyIOleClientSite:
    def SaveObject(self) -> None: ...
    def GetMoniker(self, dwAssign, dwWhichMoniker, /) -> None: ...
    def GetContainer(self) -> None: ...
    def ShowObject(self) -> None: ...
    def OnShowWindow(self, fShow, /) -> None: ...
    def RequestNewObjectLayout(self) -> None: ...

class PyIOleCommandTarget:
    def QueryStatus(self) -> None: ...
    def Exec(self) -> None: ...

class PyIOleControl:
    def GetControlInfo(self) -> None: ...
    def OnMnemonic(self, msg, /) -> None: ...
    def OnAmbientPropertyChange(self, dispID, /) -> None: ...
    def FreezeEvents(self, bFreeze, /) -> None: ...

class PyIOleControlSite:
    def OnControlInfoChanged(self) -> None: ...
    def LockInPlaceActive(self, fLock, /) -> None: ...
    def GetExtendedControl(self) -> None: ...
    def TransformCoords(
        self, PtlHimetric: tuple[Incomplete, Incomplete], pPtfContainer: tuple[float, float], dwFlags, /
    ) -> None: ...
    def TranslateAccelerator(self, pMsg: PyMSG, grfModifiers, /) -> None: ...
    def OnFocus(self, fGotFocus, /) -> None: ...
    def ShowPropertyFrame(self) -> None: ...

class PyIOleInPlaceActiveObject:
    def TranslateAccelerator(self, lpmsg: PyMSG, /) -> None: ...
    def OnFrameWindowActivate(self, fActivate, /) -> None: ...
    def OnDocWindowActivate(self, fActivate, /) -> None: ...
    def ResizeBorder(
        self, rcBorder: tuple[Incomplete, Incomplete, Incomplete, Incomplete], pUIWindow: PyIOleInPlaceUIWindow, fFrameWindow, /
    ) -> None: ...
    def EnableModeless(self, fEnable, /) -> None: ...

class PyIOleInPlaceFrame:
    def InsertMenus(self, hmenuShared, menuWidths: PyOLEMENUGROUPWIDTHS, /) -> None: ...
    def SetMenu(self, hmenuShared, holemenu, hwndActiveObject, /) -> None: ...
    def RemoveMenus(self, hmenuShared, /) -> None: ...
    def SetStatusText(self, pszStatusText, /) -> None: ...
    def EnableModeless(self, fEnable, /) -> None: ...
    def TranslateAccelerator(self, lpmsg: PyMSG, wID, /) -> None: ...

class PyIOleInPlaceObject:
    def InPlaceDeactivate(self) -> None: ...
    def UIDeactivate(self) -> None: ...
    def SetObjectRects(self) -> None: ...
    def ReactivateAndUndo(self) -> None: ...

class PyIOleInPlaceSite:
    def CanInPlaceActivate(self) -> None: ...
    def OnInPlaceActivate(self) -> None: ...
    def OnUIActivate(self) -> None: ...
    def GetWindowContext(self) -> None: ...
    def Scroll(self) -> None: ...
    def OnUIDeactivate(self, fUndoable, /) -> None: ...
    def OnInPlaceDeactivate(self) -> None: ...
    def DiscardUndoState(self) -> None: ...
    def DeactivateAndUndo(self) -> None: ...
    def OnPosRectChange(self) -> None: ...

class PyIOleInPlaceSiteEx:
    def OnInPlaceActivateEx(self, dwFlags, /) -> None: ...
    def OnInPlaceDeactivateEx(self, fNoRedraw, /) -> None: ...
    def RequestUIActivate(self) -> None: ...

class PyIOleInPlaceSiteWindowless:
    def CanWindowlessActivate(self) -> None: ...
    def GetCapture(self) -> None: ...
    def SetCapture(self, fCapture, /) -> None: ...
    def GetFocus(self) -> None: ...
    def SetFocus(self, fFocus, /) -> None: ...
    def GetDC(self, grfFlags, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], /) -> None: ...
    def ReleaseDC(self, hDC: PyCDC, /) -> None: ...
    def InvalidateRect(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], fErase, /) -> None: ...
    def InvalidateRgn(self, hRgn, fErase, /) -> None: ...
    def ScrollRect(self, dx, dy, /) -> None: ...
    def AdjustRect(self) -> None: ...
    def OnDefWindowMessage(self, msg, wParam, lParam, /) -> None: ...

class PyIOleInPlaceUIWindow:
    def GetBorder(self) -> None: ...
    def RequestBorderSpace(self, borderwidths: tuple[Incomplete, Incomplete, Incomplete, Incomplete], /) -> None: ...
    def SetBorderSpace(self, borderwidths: tuple[Incomplete, Incomplete, Incomplete, Incomplete], /) -> None: ...
    def SetActiveObject(self, pActiveObject: PyIOleInPlaceActiveObject, pszObjName, /) -> None: ...

class PyIOleObject:
    def SetClientSite(self, pClientSite: PyIOleClientSite, /) -> None: ...
    def GetClientSite(self) -> None: ...
    def SetHostNames(self, szContainerApp, szContainerObj, /) -> None: ...
    def Close(self, dwSaveOption, /) -> None: ...
    def SetMoniker(self, dwWhichMoniker, pmk: PyIMoniker, /) -> None: ...
    def GetMoniker(self, dwAssign, dwWhichMoniker, /) -> None: ...
    def InitFromData(self, pDataObject: PyIDataObject, fCreation, dwReserved, /) -> None: ...
    def GetClipboardData(self, dwReserved, /) -> None: ...
    def DoVerb(
        self,
        iVerb,
        msg: PyMSG,
        pActiveSite: PyIOleClientSite,
        lindex,
        hwndParent,
        rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        /,
    ) -> None: ...
    def EnumVerbs(self) -> None: ...
    def Update(self) -> None: ...
    def IsUpToDate(self) -> bool: ...
    def GetUserClassID(self) -> None: ...
    def GetUserType(self, dwFormOfType, /) -> None: ...
    def SetExtent(self, dwDrawAspect, size: tuple[Incomplete, Incomplete], /) -> None: ...
    def GetExtent(self, dwDrawAspect, size: tuple[Incomplete, Incomplete], /) -> None: ...
    def Advise(self, pAdvSink, /) -> None: ...
    def Unadvise(self, dwConnection, /) -> None: ...
    def EnumAdvise(self) -> None: ...
    def GetMiscStatus(self, dwAspect, /) -> None: ...
    def SetColorScheme(self) -> None: ...

class PyIOleWindow:
    def GetWindow(self) -> None: ...
    def ContextSensitiveHelp(self, fEnterMode, /) -> None: ...

class PyIPersist:
    def GetClassID(self) -> PyIID: ...

class PyIPersistFile:
    def IsDirty(self) -> bool: ...
    def Load(self, FileName, Mode, /) -> None: ...
    def Save(self, FileName, fRemember, /) -> None: ...
    def SaveCompleted(self, FileName, /) -> None: ...
    def GetCurFile(self): ...

class PyIPersistFolder:
    def Initialize(self, pidl: PyIDL, /) -> None: ...

class PyIPersistFolder2:
    def GetCurFolder(self) -> None: ...

class PyIPersistPropertyBag:
    def InitNew(self) -> None: ...
    def Load(self, bag: PyIPropertyBag, log: PyIErrorLog | None = ..., /) -> None: ...
    def Save(self, bag: PyIPropertyBag, clearDirty, saveProperties, /) -> None: ...

class PyIPersistSerializedPropStorage:
    def SetFlags(self, flags, /) -> None: ...
    def SetPropertyStorage(self, ps, /) -> None: ...
    def GetPropertyStorage(self): ...

class PyIPersistStorage:
    def IsDirty(self) -> bool: ...
    def InitNew(self, PyIStorage: PyIStorage, /) -> None: ...
    def Load(self, storage: PyIStorage, /) -> None: ...
    def Save(self, PyIStorage: PyIStorage, _int, /) -> None: ...
    def SaveCompleted(self, PyIStorage: PyIStorage, /) -> None: ...
    def HandsOffStorage(self) -> None: ...

class PyIPersistStream:
    def IsDirty(self) -> bool: ...
    def Load(self, stream: PyIStream, /) -> None: ...
    def Save(self, stream: PyIStream, bClearDirty, /) -> None: ...
    def GetSizeMax(self) -> int: ...

class PyIPersistStreamInit:
    def InitNew(self) -> None: ...

class PyIProcessDebugManager:
    def CreateApplication(self) -> None: ...
    def GetDefaultApplication(self) -> None: ...
    def AddApplication(self, pda: PyIDebugApplication, /) -> None: ...
    def RemoveApplication(self, dwAppCookie, /) -> None: ...
    def CreateDebugDocumentHelper(self, unkOuter, /) -> None: ...

class PyIProfAdmin:
    def GetLastError(self, hr, flags, /): ...
    def CreateProfile(self, oldProfileName: str, Password: str, uiParam: int = ..., flags: int = ..., /) -> None: ...
    def DeleteProfile(self, oldProfileName: str, flags: int = ..., /) -> None: ...
    def CopyProfile(self, oldProfileName: str, Password: str, newProfileName: str, uiParam: int = ..., flags=..., /) -> None: ...
    def RenameProfile(
        self, oldProfileName: str, Password: str, newProfileName: str, uiParam: int = ..., flags=..., /
    ) -> None: ...
    def SetDefaultProfile(self, profileName: str, flags: int = ..., /) -> None: ...
    def AdminServices(self, profileName: str, Password: str | None = ..., uiParam: int = ..., flags=..., /) -> PyIProfAdmin: ...

class PyIPropertyBag:
    def Read(self, propName, propType, errorLog: PyIErrorLog | None = ..., /): ...
    def Write(self, propName, value, /) -> None: ...

class PyIPropertyChange:
    def ApplyToPropVariant(self, OrigVal: PyPROPVARIANT, /) -> PyPROPVARIANT: ...

class PyIPropertyChangeArray:
    def GetCount(self): ...
    def GetAt(self, Index, riid: PyIID, /) -> PyIPropertyChange: ...
    def InsertAt(self, Index, PropChange: PyIPropertyChange, /) -> None: ...
    def Append(self, PropChange: PyIPropertyChange, /) -> None: ...
    def AppendOrReplace(self, PropChange: PyIPropertyChange, /) -> None: ...
    def RemoveAt(self, Index, /) -> None: ...
    def IsKeyInArray(self, key: PyPROPERTYKEY, /) -> bool: ...

class PyIPropertyDescription:
    def GetPropertyKey(self) -> PyPROPERTYKEY: ...
    def GetCanonicalName(self): ...
    def GetPropertyType(self): ...
    def GetDisplayName(self): ...
    def GetEditInvitation(self): ...
    def GetTypeFlags(self, mask, /): ...
    def GetViewFlags(self): ...
    def GetDefaultColumnWidth(self): ...
    def GetDisplayType(self): ...
    def GetColumnState(self): ...
    def GetGroupingRange(self): ...
    def GetRelativeDescriptionType(self): ...
    def GetRelativeDescription(self, var1: PyPROPVARIANT, var2: PyPROPVARIANT, /) -> tuple[Incomplete, Incomplete]: ...
    def GetSortDescription(self): ...
    def GetSortDescriptionLabel(self, Descending, /): ...
    def GetAggregationType(self): ...
    def GetConditionType(self) -> tuple[Incomplete, Incomplete]: ...
    def GetEnumTypelist(self, riid: PyIID, /) -> PyIPropertyEnumTypelist: ...
    def CoerceToCanonicalValue(self, Value: PyPROPVARIANT, /): ...
    def FormatForDisplay(self, Value: PyPROPVARIANT, Flags, /): ...
    def IsValueCanonical(self, Value, /) -> bool: ...

class PyIPropertyDescriptionAliasInfo:
    def GetSortByAlias(self, riid: PyIID, /) -> PyIPropertyDescription: ...
    def GetAdditionalSortByAliases(self, riid: PyIID, /) -> PyIPropertyDescriptionlist: ...

class PyIPropertyDescriptionlist:
    def GetCount(self): ...
    def GetAt(self, Elem, riid: PyIID, /) -> PyIPropertyDescription: ...

class PyIPropertyDescriptionSearchInfo:
    def GetSearchInfoFlags(self): ...
    def GetColumnIndexType(self): ...
    def GetProjectionString(self): ...
    def GetMaxSize(self): ...

class PyIPropertyEnumType:
    def GetEnumType(self): ...
    def GetValue(self) -> PyPROPVARIANT: ...
    def GetRangeMinValue(self) -> PyPROPVARIANT: ...
    def GetRangeSetValue(self) -> PyPROPVARIANT: ...
    def GetDisplayText(self) -> None: ...

class PyIPropertyEnumTypelist:
    def GetCount(self): ...
    def GetAt(self, itype, riid: PyIID, /) -> PyIPropertyEnumType: ...
    def FindMatchingIndex(self, Cmp: PyPROPVARIANT, /): ...

class PyIPropertySetStorage:
    def Create(self, fmtid: PyIID, clsid: PyIID, Flags, Mode, /) -> PyIPropertyStorage: ...
    def Open(self, fmtid: PyIID, Mode, /) -> PyIPropertyStorage: ...
    def Delete(self, fmtid: PyIID, /) -> None: ...
    def Enum(self) -> PyIEnumSTATPROPSETSTG: ...

class PyIPropertyStorage:
    def ReadMultiple(self, props: tuple[PROPSPEC, ...], /) -> tuple[Incomplete, ...]: ...
    def WriteMultiple(
        self, props: tuple[PROPSPEC, ...], values: tuple[Incomplete, ...], propidNameFirst: int = ..., /
    ) -> None: ...
    def DeleteMultiple(self, props: tuple[PROPSPEC, ...], /) -> None: ...
    def ReadPropertyNames(self, props: tuple[Incomplete, ...], /) -> tuple[Incomplete, ...]: ...
    def WritePropertyNames(self, props: tuple[Incomplete, ...], names: tuple[str, ...], /) -> None: ...
    def DeletePropertyNames(self, props: tuple[Incomplete, ...], /) -> None: ...
    def Commit(self, CommitFlags, /) -> None: ...
    def Revert(self) -> None: ...
    def Enum(self) -> PyIEnumSTATPROPSTG: ...
    def SetTimes(self, ctime: TimeType, atime: TimeType, mtime: TimeType, /) -> None: ...
    def SetClass(self, clsid: PyIID, /) -> None: ...
    def Stat(self): ...

class PyIPropertyStore:
    def GetCount(self): ...
    def GetAt(self, iProp, /) -> PyPROPERTYKEY: ...
    def GetValue(self, Key: PyPROPERTYKEY, /) -> PyPROPVARIANT: ...
    def SetValue(self, Key: PyPROPERTYKEY, Value: PyPROPVARIANT, /) -> None: ...
    def Commit(self) -> None: ...

class PyIPropertyStoreCache:
    def GetState(self, key: PyPROPERTYKEY, /): ...
    def GetValueAndState(self, key: PyPROPERTYKEY, /) -> tuple[PyPROPVARIANT, Incomplete]: ...
    def SetState(self, key: PyPROPERTYKEY, state, /) -> None: ...
    def SetValueAndState(self, key: PyPROPERTYKEY, value: PyPROPVARIANT, state, /) -> None: ...

class PyIPropertyStoreCapabilities:
    def IsPropertyWritable(self, key: PyPROPERTYKEY, /) -> bool: ...

class PyIPropertySystem:
    def GetPropertyDescription(self, Key: PyPROPERTYKEY, riid: PyIID, /) -> PyIPropertyDescription: ...
    def GetPropertyDescriptionByName(self, CanonicalName, riid: PyIID, /) -> PyIPropertyDescription: ...
    def GetPropertyDescriptionlistFromString(self, Proplist, riid: PyIID, /) -> PyIPropertyDescriptionlist: ...
    def EnumeratePropertyDescriptions(self, Filter, riid: PyIID, /) -> PyIPropertyDescriptionlist: ...
    def FormatForDisplay(self, Key: PyPROPERTYKEY, Value: PyPROPVARIANT, Flags, /): ...
    def RegisterPropertySchema(self, Path, /) -> None: ...
    def UnregisterPropertySchema(self, Path, /) -> None: ...
    def RefreshPropertySchema(self) -> None: ...

class PyIProvideClassInfo:
    def GetClassInfo(self) -> PyITypeInfo: ...

class PyIProvideClassInfo2:
    def GetGUID(self, flags, /) -> PyIID: ...

class PyIProvideExpressionContexts:
    def EnumExpressionContexts(self) -> None: ...

class PyIProvideTaskPage:
    def GetPage(self, tpType, PersistChanges, /) -> None: ...

class PyIQueryAssociations:
    def Init(self, flags, assoc: str, hkeyProgId: PyHKEY | None = ..., hwnd: int | None = ..., /) -> None: ...
    def GetKey(self, flags, assocKey, arg: str, /): ...
    def GetString(self, flags, assocStr, arg: str, /): ...

class PyIRelatedItem:
    def GetItemIDlist(self) -> PyIDL: ...
    def GetItem(self) -> PyIShellItem: ...

class PyIRemoteDebugApplication:
    def ResumeFromBreakPoint(self, prptFocus: PyIRemoteDebugApplicationThread, bra, era, /) -> None: ...
    def CauseBreak(self) -> None: ...
    def ConnectDebugger(self, pad: PyIApplicationDebugger, /) -> None: ...
    def DisconnectDebugger(self) -> None: ...
    def GetDebugger(self) -> PyIApplicationDebugger: ...
    def CreateInstanceAtApplication(self, rclsid: PyIID, pUnkOuter: PyIUnknown, dwClsContext, riid: PyIID, /) -> PyIUnknown: ...
    def QueryAlive(self) -> None: ...
    def EnumThreads(self) -> PyIEnumRemoteDebugApplicationThreads: ...
    def GetName(self) -> None: ...
    def GetRootNode(self) -> PyIDebugApplicationNode: ...
    def EnumGlobalExpressionContexts(self): ...

class PyIRemoteDebugApplicationEvents:
    def OnConnectDebugger(self, pad: PyIApplicationDebugger, /) -> None: ...
    def OnDisconnectDebugger(self) -> None: ...
    def OnSetName(self, pstrName, /) -> None: ...
    def OnDebugOutput(self, pstr, /) -> None: ...
    def OnClose(self) -> None: ...
    def OnEnterBreakPoint(self, prdat: PyIRemoteDebugApplicationThread, /) -> None: ...
    def OnLeaveBreakPoint(self, prdat: PyIRemoteDebugApplicationThread, /) -> None: ...
    def OnCreateThread(self, prdat: PyIRemoteDebugApplicationThread, /) -> None: ...
    def OnDestroyThread(self, prdat: PyIRemoteDebugApplicationThread, /) -> None: ...
    def OnBreakFlagChange(self, abf, prdatSteppingThread: PyIRemoteDebugApplicationThread, /) -> None: ...

class PyIRemoteDebugApplicationThread:
    def GetSystemThreadId(self) -> None: ...
    def GetApplication(self) -> None: ...
    def EnumStackFrames(self) -> None: ...
    def GetDescription(self) -> None: ...
    def SetNextStatement(self, pStackFrame: PyIDebugStackFrame, pCodeContext: PyIDebugCodeContext, /) -> None: ...
    def GetState(self) -> None: ...
    def Suspend(self) -> None: ...
    def Resume(self) -> None: ...
    def GetSuspendCount(self) -> None: ...

class PyIRunningObjectTable:
    def Register(self): ...
    def Revoke(self): ...
    def IsRunning(self, objectName: PyIMoniker, /) -> bool: ...
    def GetObject(self, objectName: PyIMoniker, /) -> PyIUnknown: ...
    def EnumRunning(self) -> PyIEnumMoniker: ...

class PyIScheduledWorkItem:
    def CreateTrigger(self) -> tuple[Incomplete, PyITaskTrigger]: ...
    def DeleteTrigger(self, Trigger, /) -> None: ...
    def GetTriggerCount(self): ...
    def GetTrigger(self, iTrigger, /) -> PyITaskTrigger: ...
    def GetTriggerString(self): ...
    def GetRunTimes(self, Count, Begin: TimeType, End: TimeType, /) -> tuple[TimeType, Incomplete, Incomplete, Incomplete]: ...
    def GetNextRunTime(self) -> TimeType: ...
    def SetIdleWait(self, wIdleMinutes, wDeadlineMinutes, /) -> None: ...
    def GetIdleWait(self) -> tuple[Incomplete, Incomplete]: ...
    def Run(self) -> None: ...
    def Terminate(self) -> None: ...
    def EditWorkItem(self, hParent: int, dwReserved, /) -> None: ...
    def GetMostRecentRunTime(self) -> TimeType: ...
    def GetStatus(self): ...
    def GetExitCode(self) -> tuple[Incomplete, Incomplete]: ...
    def SetComment(self, Comment, /) -> None: ...
    def GetComment(self) -> str: ...
    def SetCreator(self, Creator, /) -> None: ...
    def GetCreator(self) -> None: ...
    def SetWorkItemData(self, Data: str, /) -> None: ...
    def GetWorkItemData(self) -> str: ...
    def SetErrorRetryCount(self, wRetryCount, /) -> None: ...
    def GetErrorRetryCount(self) -> None: ...
    def SetErrorRetryInterval(self, RetryInterval, /) -> None: ...
    def GetErrorRetryInterval(self) -> None: ...
    def SetFlags(self, dwFlags, /) -> None: ...
    def GetFlags(self): ...
    def SetAccountInformation(self, AccountName, Password, /) -> None: ...
    def GetAccountInformation(self): ...

class PyIServerSecurity:
    def QueryBlanket(self, Capabilities: int = ..., /): ...
    def ImpersonateClient(self) -> None: ...
    def RevertToSelf(self) -> None: ...
    def IsImpersonating(self) -> bool: ...

class PyIServiceProvider:
    def QueryService(self, clsid: PyIID, iid: PyIID, /) -> PyIUnknown: ...

class PyIShellBrowser:
    def InsertMenusSB(self, hmenuShared: int, lpMenuWidths: PyOLEMENUGROUPWIDTHS, /) -> PyOLEMENUGROUPWIDTHS: ...
    def SetMenuSB(self, hmenuShared: int, holemenuRes: int, hwndActiveObject: int, /) -> None: ...
    def RemoveMenusSB(self, hmenuShared: int, /) -> None: ...
    def SetStatusTextSB(self, pszStatusText, /) -> None: ...
    def EnableModelessSB(self, fEnable, /) -> None: ...
    def TranslateAcceleratorSB(self, pmsg: PyMSG, wID, /) -> None: ...
    def BrowseObject(self, pidl: PyIDL, wFlags, /) -> None: ...
    def GetViewStateStream(self, grfMode, /) -> PyIStream: ...
    def GetControlWindow(self, _id, /) -> None: ...
    def SendControlMsg(self, _id, uMsg, wParam, lParam, /): ...
    def QueryActiveShellView(self) -> PyIShellView: ...
    def OnViewWindowActive(self, pshv: PyIShellView, /) -> None: ...
    def SetToolbarItems(self, lpButtons, uFlags, /) -> None: ...

class PyIShellExtInit:
    def Initialize(self, pFolder: PyIDL, pDataObject: PyIDataObject, hkey: int, /) -> None: ...

class PyIShellFolder:
    def ParseDisplayName(self, hwndOwner: int, pbc: PyIBindCtx, DisplayName, Attributes: int = ..., /): ...
    def EnumObjects(self, grfFlags, hwndOwner: int | None = ..., /) -> PyIEnumIDlist: ...
    def BindToObject(self, pidl: PyIDL, pbc: PyIBindCtx, riid: PyIID, /) -> PyIShellFolder: ...
    def BindToStorage(self, pidl: PyIDL, pbc: PyIBindCtx, riid: PyIID, /): ...
    def CompareIDs(self, lparam, pidl1: PyIDL, pidl2: PyIDL, /): ...
    def CreateViewObject(self, hwndOwner, riid: PyIID, /) -> PyIShellView: ...
    def GetAttributesOf(self, pidl: tuple[PyIDL, ...], rgfInOut, /): ...
    def GetUIObjectOf(
        self, hwndOwner: int, pidl: tuple[PyIDL, ...], riid: PyIID, iidout: PyIID, Reserved=..., /
    ) -> tuple[Incomplete, PyIUnknown]: ...
    def GetDisplayNameOf(self, pidl: PyIDL, uFlags, /): ...
    def SetNameOf(self, hwndOwner, pidl: PyIDL, Name, Flags, /) -> PyIDL: ...

class PyIShellFolder2:
    def GetDefaultSearchGUID(self, pguid: PyIID, /) -> PyIID: ...
    def EnumSearches(self): ...
    def GetDefaultColumn(self) -> tuple[Incomplete, Incomplete]: ...
    def GetDefaultColumnState(self, iColumn, /): ...
    def GetDetailsEx(self, pidl: PyIDL, pscid, /): ...
    def GetDetailsOf(self, pidl: PyIDL, iColumn, /) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def MapColumnToSCID(self, Column, /): ...

class PyIShellIcon:
    def GetIconOf(self, pidl: PyIDL, /) -> None: ...

class PyIShellIconOverlay:
    def GetOverlayIndex(self, pidl: PyIDL, /) -> None: ...
    def GetOverlayIconIndex(self, pidl: PyIDL, /) -> None: ...

class PyIShellIconOverlayIdentifier:
    def IsMemberOf(self, path: str, attrib, /) -> bool: ...
    def GetOverlayInfo(self) -> tuple[str, Incomplete, Incomplete]: ...
    def GetPriority(self): ...

class PyIShellIconOverlayManager:
    def GetFileOverlayInfo(self, path, attrib, flags, /): ...
    def GetReservedOverlayInfo(self, path, attrib, flags, ireservedID, /) -> None: ...
    def RefreshOverlayImages(self, flags, /) -> None: ...
    def LoadNonloadedOverlayIdentifiers(self) -> None: ...
    def OverlayIndexFromImageIndex(self, iImage, fAdd, /) -> None: ...

class PyIShellItem:
    def BindToHandler(self, pbc: PyIBindCtx, bhid: PyIID, riid: PyIID, /): ...
    def GetParent(self) -> PyIShellItem: ...
    def GetDisplayName(self, sigdnName, /): ...
    def GetAttributes(self, Mask, /): ...
    def Compare(self, psi: PyIShellItem, hint, /): ...

class PyIShellItem2:
    def GetPropertyStore(self, Flags, riid: PyIID, /) -> PyIPropertyStore: ...
    def GetPropertyStoreForKeys(self, Keys: tuple[Incomplete, ...], Flags, riid: PyIID, /) -> PyIPropertyStore: ...
    def GetPropertyStoreWithCreateObject(self, Flags, CreateObject: PyIUnknown, riid: PyIID, /) -> PyIPropertyStore: ...
    def GetPropertyDescriptionlist(self, Type: PyPROPERTYKEY, riid: PyIID, /) -> PyIPropertyDescriptionlist: ...
    def Update(self, BindCtx: Incomplete | None = ..., /) -> None: ...
    def GetProperty(self, key: PyPROPERTYKEY, /): ...
    def GetCLSID(self, key: PyPROPERTYKEY, /) -> PyIID: ...
    def GetFileTime(self, key: PyPROPERTYKEY, /) -> TimeType: ...
    def GetInt32(self, key: PyPROPERTYKEY, /): ...
    def GetString(self, key: PyPROPERTYKEY, /): ...
    def GetUInt32(self, key: PyPROPERTYKEY, /): ...
    def GetUInt64(self, key: PyPROPERTYKEY, /): ...
    def GetBool(self, key: PyPROPERTYKEY, /): ...

class PyIShellItemArray:
    def BindToHandler(self, pbc: PyIBindCtx, rbhid: PyIID, riid: PyIID, /): ...
    def GetPropertyStore(self, flags, riid: PyIID, /) -> PyIPropertyStore: ...
    def GetPropertyDescriptionlist(self, Type: PyPROPERTYKEY, riid: PyIID, /) -> PyIPropertyDescriptionlist: ...
    def GetAttributes(self, AttribFlags, Mask, /): ...
    def GetCount(self): ...
    def GetItemAt(self, dwIndex, /) -> PyIShellItem: ...
    def EnumItems(self) -> PyIEnumShellItems: ...

class PyIShellItemResources:
    def GetAttributes(self) -> None: ...
    def GetSize(self): ...
    def GetTimes(self) -> None: ...
    def SetTimes(self, pftCreation: TimeType, pftWrite: TimeType, pftAccess: TimeType, /) -> None: ...
    def GetResourceDescription(self, pcsir: PySHELL_ITEM_RESOURCE, /) -> None: ...
    def EnumResources(self) -> PyIEnumResources: ...
    def SupportsResource(self, pcsir: PySHELL_ITEM_RESOURCE, /): ...
    def OpenResource(self, pcsir: PySHELL_ITEM_RESOURCE, riid: PyIID, /) -> PyIUnknown: ...
    def CreateResource(self, sir: PySHELL_ITEM_RESOURCE, riid: PyIID, /): ...
    def MarkForDelete(self) -> None: ...

class PyIShellLibrary:
    def LoadLibraryFromItem(self, Library: PyIShellItem, Mode, /) -> None: ...
    def LoadLibraryFromKnownFolder(self, Library: PyIID, Mode, /) -> None: ...
    def AddFolder(self, Location: PyIShellItem, /) -> None: ...
    def RemoveFolder(self, Location: PyIShellItem, /) -> None: ...
    def GetFolders(self, Filter, riid: PyIID, /) -> PyIShellItemArray: ...
    def ResolveFolder(self, FolderToResolve: PyIShellItem, Timeout, riid: PyIID, /) -> PyIShellItem: ...
    def GetDefaultSaveFolder(self, Type, riid: PyIID, /) -> PyIShellItem: ...
    def SetDefaultSaveFolder(self, Type, SaveFolder: PyIShellItem, /) -> None: ...
    def GetOptions(self): ...
    def SetOptions(self, Mask, Options, /) -> None: ...
    def GetFolderType(self) -> PyIID: ...
    def SetFolderType(self, Type: PyIID, /) -> None: ...
    def GetIcon(self): ...
    def SetIcon(self, Icon, /) -> None: ...
    def Commit(self) -> None: ...
    def Save(self, FolderToSaveIn: PyIShellItem, LibraryName, Flags, /) -> PyIShellItem: ...
    def SaveInKnownFolder(self, FolderToSaveIn: PyIID, LibraryName, Flags, /) -> PyIShellItem: ...

class PyIShellLink:
    def GetPath(self, fFlags, cchMaxPath, /) -> tuple[Incomplete, WIN32_FIND_DATA]: ...
    def GetIDlist(self) -> PyIDL: ...
    def SetIDlist(self, pidl: PyIDL, /) -> None: ...
    def GetDescription(self, cchMaxName: int = ..., /): ...
    def SetDescription(self, Name, /) -> None: ...
    def GetWorkingDirectory(self, cchMaxName: int = ..., /): ...
    def SetWorkingDirectory(self, Dir, /) -> None: ...
    def GetArguments(self, cchMaxName: int = ..., /): ...
    def SetArguments(self, args, /) -> None: ...
    def GetHotkey(self): ...
    def SetHotkey(self, wHotkey, /) -> None: ...
    def GetShowCmd(self): ...
    def SetShowCmd(self, iShowCmd, /) -> None: ...
    def GetIconLocation(self, cchMaxPath, /): ...
    def SetIconLocation(self, iconPath: str, iIcon, /) -> None: ...
    def SetRelativePath(self, relPath: str, reserved: int = ..., /) -> None: ...
    def Resolve(self, hwnd: int, fFlags, /) -> None: ...
    def SetPath(self, path: str, /) -> None: ...

class PyIShellLinkDatalist:
    def AddDataBlock(self, DataBlock, /) -> None: ...
    def CopyDataBlock(self, Sig, /): ...
    def GetFlags(self): ...
    def RemoveDataBlock(self, Sig, /) -> None: ...
    def SetFlags(self, Flags, /) -> None: ...

class PyIShellView:
    def TranslateAccelerator(self, pmsg, /): ...
    def EnableModeless(self, fEnable, /) -> None: ...
    def UIActivate(self, uState, /) -> None: ...
    def Refresh(self) -> None: ...
    def CreateViewWindow(
        self,
        psvPrevious: PyIShellView,
        pfs: tuple[Incomplete, Incomplete],
        psb: PyIShellBrowser,
        prcView: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        /,
    ): ...
    def DestroyViewWindow(self) -> None: ...
    def GetCurrentInfo(self): ...
    def SaveViewState(self) -> None: ...
    def SelectItem(self, pidlItem: PyIDL, uFlags, /) -> None: ...
    def GetItemObject(self, uItem, riid: PyIID, /) -> PyIUnknown: ...

class PyISpecifyPropertyPages:
    def GetPages(self) -> None: ...

class PyIStorage:
    def CreateStream(self, Name, Mode, reserved1: int = ..., reserved2: int = ..., /) -> PyIStream: ...
    def OpenStream(self, Name, reserved1, Mode, reserved2: int = ..., /) -> PyIStream: ...
    def CreateStorage(self, Name, Mode, StgFmt, reserved2: int = ..., /) -> PyIStorage: ...
    def OpenStorage(self, Name, Priority: PyIStorage, Mode, snbExclude, reserved=..., /) -> PyIStorage: ...
    def CopyTo(self, rgiidExclude: tuple[Incomplete, Incomplete], snbExclude, stgDest: PyIStorage, /) -> None: ...
    def MoveElementTo(self, Name, stgDest: PyIStorage, NewName, Flags, /) -> None: ...
    def Commit(self, grfCommitFlags, /) -> None: ...
    def Revert(self) -> None: ...
    def EnumElements(
        self, reserved1: int = ..., reserved2: Incomplete | None = ..., reserved3: int = ..., /
    ) -> PyIEnumSTATSTG: ...
    def DestroyElement(self, name: str, /) -> None: ...
    def RenameElement(self, OldName, NewName, /) -> None: ...
    def SetElementTimes(self, name, ctime: TimeType, atime: TimeType, mtime: TimeType, /) -> None: ...
    def SetClass(self, clsid: PyIID, /) -> None: ...
    def SetStateBits(self, grfStateBits, grfMask, /) -> None: ...
    def Stat(self, grfStatFlag, /) -> STATSTG: ...

class PyIStream:
    def Read(self, numBytes, /) -> str: ...
    def read(self, numBytes, /) -> str: ...
    def Write(self, data: str, /) -> None: ...
    def write(self, data: str, /) -> None: ...
    @overload
    def Seek(self, offset: int, origin: int, /) -> int: ...
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def Seek(self, offset: tuple[int, int], origin: int, /) -> int: ...
    @overload
    def SetSize(self, newSize: int, /) -> None: ...
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def SetSize(self, newSize: tuple[int, int], /) -> None: ...
    @overload
    def CopyTo(self, stream: PyIStream, cb: int, /) -> int: ...
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def CopyTo(self, stream: PyIStream, cb: tuple[int, int], /) -> int: ...
    def Commit(self, flags, /) -> None: ...
    def Revert(self) -> None: ...
    @overload
    def LockRegion(self, offset: int, cb: int, lockType, /) -> None: ...
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def LockRegion(self, offset: tuple[int, int], cb: tuple[int, int], lockType, /) -> None: ...
    @overload
    def UnLockRegion(self, offset: int, cb: int, lockType, /) -> None: ...
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def UnLockRegion(self, offset: tuple[int, int], cb: tuple[int, int], lockType, /) -> None: ...
    def Clone(self) -> PyIStream: ...
    def Stat(self, grfStatFlag: int = ..., /) -> STATSTG: ...

class PyITask:
    def SetApplicationName(self, ApplicationName, /) -> None: ...
    def GetApplicationName(self): ...
    def SetParameters(self, Parameters, /) -> None: ...
    def GetParameters(self): ...
    def SetWorkingDirectory(self, WorkingDirectory, /) -> None: ...
    def GetWorkingDirectory(self): ...
    def SetPriority(self, Priority, /) -> None: ...
    def GetPriority(self): ...
    def SetTaskFlags(self, dwFlags, /) -> None: ...
    def GetTaskFlags(self): ...
    def SetMaxRunTime(self, MaxRunTimeMS, /) -> None: ...
    def GetMaxRunTime(self): ...

class PyITaskScheduler:
    def SetTargetComputer(self, Computer, /) -> None: ...
    def GetTargetComputer(self): ...
    def Enum(self) -> tuple[str, ...]: ...
    def Activate(self, Name, riid: PyIID, /) -> PyITask: ...
    def Delete(self, TaskName, /) -> None: ...
    def NewWorkItem(self, TaskName, rclsid: PyIID, riid: PyIID, /) -> PyITask: ...
    def AddWorkItem(self, TaskName, WorkItem: PyITask, /) -> None: ...
    def IsOfType(self, Name, riid: PyIID, /) -> bool: ...

class PyITaskTrigger:
    def SetTrigger(self, Trigger: PyTASK_TRIGGER, /) -> None: ...
    def GetTrigger(self) -> PyTASK_TRIGGER: ...
    def GetTriggerString(self) -> str: ...

class PyITaskbarlist:
    def HrInit(self) -> None: ...
    def AddTab(self, hwnd: int, /) -> None: ...
    def DeleteTab(self, hwnd: int, /) -> None: ...
    def ActivateTab(self, hwnd: int, /) -> None: ...
    def SetActiveAlt(self, hwnd: int, /) -> None: ...

class PyITransferAdviseSink:
    def UpdateProgress(self, SizeCurrent, SizeTotal, FilesCurrent, FilesTotal, FoldersCurrent, FoldersTotal, /) -> None: ...
    def UpdateTransferState(self, State, /) -> None: ...
    def ConfirmOverwrite(self, Source: PyIShellItem, DestParent: PyIShellItem, Name, /): ...
    def ConfirmEncryptionLoss(self, Source: PyIShellItem, /): ...
    def FileFailure(self, Item: PyIShellItem, ItemName, Error, /) -> tuple[Incomplete, Incomplete]: ...
    def SubStreamFailure(self, Item: PyIShellItem, StreamName, Error, /): ...
    def PropertyFailure(self, Item: PyIShellItem, key: PyPROPERTYKEY, Error, /): ...

class PyITransferDestination:
    def Advise(self, Sink: PyITransferAdviseSink, /): ...
    def Unadvise(self, Cookie, /) -> None: ...
    def CreateItem(
        self, Name, Attributes, Size, Flags, riidItem: PyIID, riidResources: PyIID, /
    ) -> tuple[Incomplete, Incomplete, Incomplete]: ...

class PyITransferMediumItem: ...

class PyITransferSource:
    def Advise(self, Sink: PyITransferAdviseSink, /): ...
    def Unadvise(self, Cookie, /) -> None: ...
    def SetProperties(self, proparray: PyIPropertyChangeArray, /) -> None: ...
    def OpenItem(self, Item: PyIShellItem, flags, riid: PyIID, /) -> tuple[Incomplete, PyIShellItemResources]: ...
    def MoveItem(self, Item: PyIShellItem, ParentDst: PyIShellItem, NameDst, flags, /) -> tuple[Incomplete, PyIShellItem]: ...
    def RecycleItem(self, Source: PyIShellItem, ParentDest: PyIShellItem, flags, /) -> tuple[Incomplete, PyIShellItem]: ...
    def RemoveItem(self, Source: PyIShellItem, flags, /): ...
    def RenameItem(self, Source: PyIShellItem, NewName, flags, /) -> tuple[Incomplete, PyIShellItem]: ...
    def LinkItem(self, Source: PyIShellItem, ParentDest: PyIShellItem, NewName, flags, /) -> tuple[Incomplete, PyIShellItem]: ...
    def ApplyPropertiesToItem(self, Source: PyIShellItem, /) -> PyIShellItem: ...
    def GetDefaultDestinationName(self, Source: PyIShellItem, ParentDest: PyIShellItem, /): ...
    def EnterFolder(self, ChildFolderDest: PyIShellItem, /): ...
    def LeaveFolder(self, ChildFolderDest: PyIShellItem, /): ...

class PyITypeComp:
    def Bind(self, szName: str, wflags: int = ..., /): ...
    def BindType(self, szName: str, /): ...

class PyITypeInfo:
    def GetContainingTypeLib(self) -> tuple[PyITypeLib, Incomplete]: ...
    def GetDocumentation(self, memberId, /) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def GetFuncDesc(self, memberId, /) -> FUNCDESC: ...
    def GetImplTypeFlags(self, index, /): ...
    def GetIDsOfNames(self): ...
    def GetNames(self, memberId, /): ...
    def GetTypeAttr(self) -> TYPEATTR: ...
    def GetRefTypeInfo(self, hRefType, /) -> PyITypeInfo: ...
    def GetRefTypeOfImplType(self, hRefType, /): ...
    def GetVarDesc(self, memberId, /) -> VARDESC: ...
    def GetTypeComp(self) -> PyITypeComp: ...

class PyITypeLib:
    def GetDocumentation(self, index, /): ...
    def GetLibAttr(self) -> TLIBATTR: ...
    def GetTypeComp(self) -> PyITypeComp: ...
    def GetTypeInfo(self, index, /) -> PyITypeInfo: ...
    def GetTypeInfoCount(self): ...
    def GetTypeInfoOfGuid(self, iid: PyIID, /) -> PyITypeInfo: ...
    def GetTypeInfoType(self, index, /): ...

class PyIUniformResourceLocator:
    def GetURL(self): ...
    def SetURL(self, URL, InFlags: int = ..., /) -> None: ...
    def InvokeCommand(self, Verb, Flags: int = ..., hwndParent: int = ..., /): ...

@final
class PyIUnknown:
    def QueryInterface(self, iid, useIID: Incomplete | None = ..., /) -> PyIUnknown: ...

class PyIViewObject:
    def Draw(
        self,
        dwDrawAspect,
        lindex,
        aspectFlags,
        hdcTargetDev,
        hdcDraw,
        arg: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        arg1: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        funcContinue,
        obContinue,
        /,
    ) -> None: ...
    def GetColorSet(self, dwDrawAspect, lindex, aspectFlags, hicTargetDev, /) -> None: ...
    def Freeze(self, dwDrawAspect, lindex, aspectFlags, /) -> None: ...
    def Unfreeze(self, dwFreeze, /) -> None: ...
    def SetAdvise(self, aspects, advf, pAdvSink, /) -> None: ...
    def GetAdvise(self) -> None: ...

class PyIViewObject2:
    def GetExtent(self, dwDrawAspect, lindex, targetDevice, /) -> None: ...

class PyMAPINAMEIDArray: ...
class PyOLEMENUGROUPWIDTHS: ...
class PyPROPERTYKEY: ...

@final
class PyPROPVARIANT:
    @overload
    @deprecated("Support for passing two ints to create a 64-bit value is deprecated; pass a single int instead")
    def __init__(self, Value: tuple[int, int], Type=...) -> None: ...
    @overload
    def __init__(self, Value, Type=...) -> None: ...
    @property
    def vt(self): ...
    def GetValue(self): ...
    def ToString(self): ...
    def ChangeType(self, Type, Flags: int = ..., /) -> PyPROPVARIANT: ...

class PySAndRestriction: ...
class PySBinaryArray: ...
class PySBitMaskRestriction: ...
class PySContentRestriction: ...
class PySExistRestriction: ...
class PySHELL_ITEM_RESOURCE: ...
class PySNotRestriction: ...
class PySOrRestriction: ...
class PySPropTagArray: ...
class PySPropValue: ...
class PySPropValueArray: ...
class PySPropertyRestriction: ...
class PySRestriction: ...
class PySRow: ...
class PySRowSet: ...
class PySSortOrderItem: ...
class PySSortOrderSet: ...

class PySTGMEDIUM:
    @property
    def tymed(self): ...
    @property
    def data(self): ...
    @property
    def data_handle(self): ...
    def set(self, tymed, data, /) -> None: ...

class PyTASK_TRIGGER: ...
class RTF_WCSINFO: ...
class SHFILEINFO: ...
class SHFILEOPSTRUCT: ...
class SI_ACCESS: ...
class SI_INHERIT_TYPE: ...
class SI_OBJECT_INFO: ...

STATSTG: TypeAlias = tuple[str | None, int, int, TimeType, TimeType, TimeType, int, int, PyIID, int, int]

class TLIBATTR: ...

class TYPEATTR:
    @property
    def iid(self) -> PyIID: ...
    @property
    def lcid(self): ...
    @property
    def memidConstructor(self): ...
    @property
    def memidDestructor(self): ...
    @property
    def cbSizeInstance(self): ...
    @property
    def typekind(self): ...
    @property
    def cFuncs(self): ...
    @property
    def cVars(self): ...
    @property
    def cImplTypes(self): ...
    @property
    def cbSizeVft(self): ...
    @property
    def cbAlignment(self): ...
    @property
    def wTypeFlags(self): ...
    @property
    def wMajorVerNum(self): ...
    @property
    def wMinorVerNum(self): ...
    @property
    def tdescAlias(self) -> TYPEDESC: ...
    @property
    def idldeskType(self) -> IDLDESC: ...

class TYPEDESC: ...

class VARDESC:
    @property
    def memid(self): ...
    @property
    def value(self): ...
    @property
    def elemdescVar(self) -> ELEMDESC: ...
    @property
    def varFlags(self): ...
    @property
    def varkind(self): ...

class CHARFORMAT: ...
class CREATESTRUCT: ...
class LV_COLUMN: ...
class LV_ITEM: ...
class PARAFORMAT: ...
class PyAssocCObject: ...

class PyAssocObject:
    def AttachObject(self) -> None: ...
    def GetAttachedObject(self): ...

class PyCBitmap:
    def CreateCompatibleBitmap(self, dc: PyCDC, width: int, height: int, /) -> None: ...
    def GetSize(self) -> tuple[Incomplete, Incomplete]: ...
    def GetHandle(self, *args: Unused) -> int: ...
    def LoadBitmap(self, idRes, obDLL: PyDLL | None = ..., /) -> None: ...
    def LoadBitmapFile(self, fileObject, /) -> None: ...
    def LoadPPMFile(self, fileObject, cols, rows, /) -> None: ...
    def Paint(
        self,
        dcObject: PyCDC,
        arg: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        arg1: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        /,
    ) -> None: ...
    def GetInfo(self): ...
    @overload
    def GetBitmapBits(self, asString: Literal[False] = False, /) -> tuple[int, ...]: ...
    @overload
    def GetBitmapBits(self, asString: Literal[True], /) -> bytes: ...
    @overload
    def GetBitmapBits(self, asString: bool, /) -> tuple[int, ...] | bytes: ...
    def SaveBitmapFile(self, dcObject: PyCDC, Filename: str, /): ...

class PyCBrush:
    def CreateSolidBrush(self, i: int) -> None: ...
    def GetSafeHandle(self): ...

class PyCButton:
    def CreateWindow(
        self, caption: str, style, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], parent: PyCWnd, _id, /
    ) -> None: ...
    def GetBitmap(self): ...
    def SetBitmap(self, hBitmap: int = ..., /): ...
    def GetCheck(self): ...
    def SetCheck(self, idCheck, /) -> None: ...
    def GetState(self): ...
    def SetState(self, bHighlight, /): ...
    def GetButtonStyle(self): ...
    def SetButtonStyle(self, style, bRedraw: int = ..., /): ...

class PyCCmdTarget:
    def BeginWaitCursor(self) -> None: ...
    def EndWaitCursor(self) -> None: ...
    def HookCommand(self, obHandler, _id, /): ...
    def HookCommandUpdate(self, obHandler, _id, /): ...
    def HookOleEvent(self): ...
    def HookNotify(self, obHandler, _id, /): ...
    def RestoreWaitCursor(self) -> None: ...

class PyCCmdUI:
    @property
    def m_nIndex(self): ...
    @property
    def m_nID(self): ...
    @property
    def m_pMenu(self) -> PyCMenu: ...
    @property
    def m_pSubMenu(self) -> PyCMenu: ...
    def Enable(self, bEnable: int = ..., /) -> None: ...
    def SetCheck(self, state: int = ..., /) -> None: ...
    def SetRadio(self, bOn: int = ..., /) -> None: ...
    def SetText(self, text: str, /) -> None: ...
    def ContinueRouting(self) -> None: ...

class PyCColorDialog:
    def GetColor(self): ...
    def DoModal(self): ...
    def GetSavedCustomColors(self): ...
    def SetCurrentColor(self, color, /) -> None: ...
    def SetCustomColors(self, colors: Sequence[int], /) -> None: ...
    def GetCustomColors(self) -> tuple[Incomplete, ...]: ...

class PyCComboBox:
    def AddString(self, _object, /): ...
    def DeleteString(self, pos, /): ...
    def Dir(self, attr, wild: str, /): ...
    def GetCount(self): ...
    def GetCurSel(self): ...
    def GetEditSel(self): ...
    def GetExtendedUI(self): ...
    def GetItemData(self, item, /): ...
    def GetItemValue(self, item, /): ...
    def GetLBText(self, index, /) -> str: ...
    def GetLBTextLen(self, index, /): ...
    def InsertString(self, pos, _object, /): ...
    def LimitText(self, _max, /): ...
    def ResetContent(self) -> None: ...
    def SelectString(self, after, string: str, /) -> None: ...
    def SetCurSel(self, index, /) -> None: ...
    def SetEditSel(self, start, end, /) -> None: ...
    def SetExtendedUI(self, bExtended: int = ..., /) -> None: ...
    def SetItemData(self, item, Data, /): ...
    def SetItemValue(self, item, data, /): ...
    def ShowDropDown(self, bShowIt: int = ..., /) -> None: ...

class PyCCommonDialog: ...
class PyCControl: ...

class PyCControlBar:
    @property
    def dockSite(self) -> PyCFrameWnd: ...
    @property
    def dockBar(self) -> PyCWnd: ...
    @property
    def dockContext(self) -> PyCDockContext: ...
    @property
    def dwStyle(self): ...
    @property
    def dwDockStyle(self): ...
    def CalcDynamicLayout(self, length, dwMode, /): ...
    def CalcFixedLayout(self, bStretch, bHorz, /): ...
    def EnableDocking(self, style, /) -> None: ...
    def EraseNonClient(self) -> None: ...
    def GetBarStyle(self): ...
    def GetCount(self): ...
    def GetDockingFrame(self) -> PyCFrameWnd: ...
    def IsFloating(self) -> bool: ...
    def SetBarStyle(self, style, /) -> None: ...
    def ShowWindow(self): ...

class PyCCtrlView:
    def OnCommand(self, wparam, lparam, /) -> None: ...

class PyCDC:
    def AbortDoc(self) -> None: ...
    def Arc(
        self,
        rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        pointStart: tuple[Incomplete, Incomplete],
        pointEnd: tuple[Incomplete, Incomplete],
        /,
    ) -> None: ...
    def BeginPath(self) -> None: ...
    def BitBlt(
        self, destPos: tuple[int, int], size: tuple[int, int], dc: PyCDC, srcPos: tuple[int, int], rop: int, /
    ) -> None: ...
    def Chord(
        self,
        rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        pointStart: tuple[Incomplete, Incomplete],
        pointEnd: tuple[Incomplete, Incomplete],
        /,
    ) -> None: ...
    def CreateCompatibleDC(self, dcFrom: PyCDC | None = ..., /) -> PyCDC: ...
    def CreatePrinterDC(self, printerName: str | None = ..., /) -> None: ...
    def DeleteDC(self) -> None: ...
    def DPtoLP(self, point: tuple[Incomplete, Incomplete], x, y, /) -> tuple[Incomplete, Incomplete]: ...
    def Draw3dRect(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], colorTopLeft, colorBotRight, /) -> None: ...
    def DrawFocusRect(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], /) -> None: ...
    def DrawFrameControl(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], typ, state, /) -> None: ...
    def DrawIcon(self, point: tuple[Incomplete, Incomplete], hIcon: int, /) -> None: ...
    def DrawText(
        self, s: str, _tuple: tuple[Incomplete, Incomplete, Incomplete, Incomplete], _format, /
    ) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def Ellipse(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], /) -> None: ...
    def EndDoc(self) -> None: ...
    def EndPage(self) -> None: ...
    def EndPath(self) -> None: ...
    def ExtTextOut(
        self,
        _int,
        _int1,
        _int2,
        rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        string,
        _tuple: tuple[tuple[Incomplete, Incomplete], ...],
        /,
    ) -> None: ...
    def FillPath(self) -> None: ...
    def FillRect(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], brush: PyCBrush, /) -> None: ...
    def FillSolidRect(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], color, /) -> None: ...
    def FrameRect(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], brush: PyCBrush, /) -> None: ...
    def GetBrushOrg(self) -> tuple[Incomplete, Incomplete]: ...
    def GetClipBox(self) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def GetCurrentPosition(self) -> tuple[Incomplete, Incomplete]: ...
    def GetDeviceCaps(self, index, /): ...
    def GetHandleAttrib(self): ...
    def GetHandleOutput(self): ...
    def GetMapMode(self): ...
    def GetNearestColor(self, color, /): ...
    def GetPixel(self, x, y, /) -> None: ...
    def GetSafeHdc(self) -> int: ...
    def GetTextExtent(self, text: str, /) -> tuple[Incomplete, Incomplete]: ...
    def GetTextExtentPoint(self, text: str, /) -> tuple[Incomplete, Incomplete]: ...
    def GetTextFace(self) -> str: ...
    def GetTextMetrics(self): ...
    def GetViewportExt(self) -> tuple[Incomplete, Incomplete]: ...
    def GetViewportOrg(self) -> tuple[Incomplete, Incomplete]: ...
    def GetWindowExt(self) -> tuple[Incomplete, Incomplete]: ...
    def GetWindowOrg(self) -> tuple[Incomplete, Incomplete]: ...
    def IntersectClipRect(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], /) -> None: ...
    def IsPrinting(self) -> bool: ...
    def LineTo(self, point: tuple[Incomplete, Incomplete], x, y, /) -> None: ...
    def LPtoDP(self, point: tuple[Incomplete, Incomplete], x, y, /) -> tuple[Incomplete, Incomplete]: ...
    def MoveTo(self, point: tuple[Incomplete, Incomplete], x, y, /) -> tuple[Incomplete, Incomplete]: ...
    def OffsetWindowOrg(self, arg: tuple[Incomplete, Incomplete], /) -> tuple[Incomplete, Incomplete]: ...
    def OffsetViewportOrg(self, arg: tuple[Incomplete, Incomplete], /) -> tuple[Incomplete, Incomplete]: ...
    def PatBlt(self, destPos: tuple[Incomplete, Incomplete], size: tuple[Incomplete, Incomplete], rop, /) -> None: ...
    def Pie(self, x1, y1, x2, y2, x3, y3, x4, y4, /) -> None: ...
    def PolyBezier(self) -> None: ...
    def Polygon(self) -> None: ...
    def Polyline(self, points: list[tuple[Incomplete, Incomplete]], /) -> None: ...
    def RealizePalette(self): ...
    def Rectangle(self): ...
    def RectVisible(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], /): ...
    def RestoreDC(self, saved, /) -> None: ...
    def SaveDC(self): ...
    def ScaleWindowExt(self) -> tuple[Incomplete, Incomplete]: ...
    def ScaleViewportExt(self) -> tuple[Incomplete, Incomplete]: ...
    def SelectClipRgn(self): ...
    def SelectObject(self, ob: PyCBitmap, /) -> PyCBitmap: ...
    def SetBkColor(self, color, /): ...
    def SetBkMode(self, mode, /): ...
    def SetBrushOrg(self, point: tuple[Incomplete, Incomplete], /) -> tuple[Incomplete, Incomplete]: ...
    def SetGraphicsMode(self, mode, /): ...
    def SetMapMode(self, newMode, /): ...
    def SetPixel(self, x, y, color, /) -> None: ...
    def SetPolyFillMode(self, point: tuple[Incomplete, Incomplete], /): ...
    def SetROP2(self, mode, /): ...
    def SetTextAlign(self, newFlags, /): ...
    def SetTextColor(self, color, /): ...
    def SetWindowExt(self, size: tuple[Incomplete, Incomplete], /) -> tuple[Incomplete, Incomplete]: ...
    def SetWindowOrg(self, arg: tuple[Incomplete, Incomplete], /) -> tuple[Incomplete, Incomplete]: ...
    def SetViewportExt(self, size: tuple[Incomplete, Incomplete], /) -> tuple[Incomplete, Incomplete]: ...
    def SetViewportOrg(self, arg: tuple[Incomplete, Incomplete], /) -> tuple[Incomplete, Incomplete]: ...
    def SetWorldTransform(self): ...
    def StartDoc(self, docName: str, outputFile: str, /) -> None: ...
    def StartPage(self) -> None: ...
    def StretchBlt(
        self,
        destPos: tuple[Incomplete, Incomplete],
        size: tuple[Incomplete, Incomplete],
        dc: PyCDC,
        srcPos: tuple[Incomplete, Incomplete],
        size1: tuple[Incomplete, Incomplete],
        rop,
        /,
    ) -> None: ...
    def StrokeAndFillPath(self) -> None: ...
    def StrokePath(self) -> None: ...
    def TextOut(self, _int, _int1, string, /) -> None: ...

class PyCDialog:
    def CreateWindow(self, obParent: PyCWnd | None = ..., /) -> None: ...
    def DoModal(self): ...
    def EndDialog(self, result, /) -> None: ...
    def GotoDlgCtrl(self, control: PyCWnd, /) -> None: ...
    def MapDialogRect(
        self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], /
    ) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def OnCancel(self) -> None: ...
    def OnOK(self) -> None: ...
    def OnInitDialog(self): ...

class PyCDialogBar:
    def CreateWindow(self, parent: PyCWnd, template: PyResourceId, style, _id, /) -> None: ...

class PyCDocTemplate:
    def DoCreateDoc(self, fileName: str | None = ..., /) -> PyCDocument: ...
    def FindOpenDocument(self, fileName: str, /) -> PyCDocument: ...
    def GetDocString(self, docIndex, /) -> str: ...
    def GetDocumentlist(self): ...
    def GetResourceID(self) -> None: ...
    def GetSharedMenu(self) -> PyCMenu: ...
    def InitialUpdateFrame(
        self, frame: PyCFrameWnd | None = ..., doc: PyCDocument | None = ..., bMakeVisible: int = ..., /
    ) -> None: ...
    def SetContainerInfo(self, _id, /) -> None: ...
    def SetDocStrings(self, docStrings: str, /) -> None: ...
    def OpenDocumentFile(self, filename: str, bMakeVisible: int = ..., /) -> None: ...

class PyCDockContext:
    @property
    def ptLast(self) -> tuple[Incomplete, Incomplete]: ...
    @property
    def rectLast(self) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    @property
    def sizeLast(self) -> tuple[Incomplete, Incomplete]: ...
    @property
    def bDitherLast(self): ...
    @property
    def rectDragHorz(self) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    @property
    def rectDragVert(self) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    @property
    def rectFrameDragHorz(self) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    @property
    def rectFrameDragVert(self) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    @property
    def dwDockStyle(self): ...
    @property
    def dwOverDockStyle(self): ...
    @property
    def dwStyle(self): ...
    @property
    def bFlip(self): ...
    @property
    def bForceFrame(self): ...
    @property
    def bDragging(self): ...
    @property
    def nHitTest(self): ...
    @property
    def uMRUDockID(self): ...
    @property
    def rectMRUDockPos(self) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    @property
    def dwMRUFloatStyle(self): ...
    @property
    def ptMRUFloatPos(self) -> tuple[Incomplete, Incomplete]: ...
    def EndDrag(self): ...
    def StartDrag(self, pt: tuple[Incomplete, Incomplete], /): ...
    def EndResize(self): ...
    def StartResize(self, hittest, pt: tuple[Incomplete, Incomplete], /): ...
    def ToggleDocking(self): ...

class PyCDocument:
    def DeleteContents(self) -> None: ...
    def DoSave(self, fileName: str, bReplace: int = ..., /) -> None: ...
    def DoFileSave(self) -> None: ...
    def GetDocTemplate(self) -> PyCDocTemplate: ...
    def GetAllViews(self) -> list[Incomplete]: ...
    def GetFirstView(self) -> PyCView: ...
    def GetPathName(self) -> str: ...
    def GetTitle(self) -> str: ...
    def IsModified(self) -> bool: ...
    def OnChangedViewlist(self) -> None: ...
    def OnCloseDocument(self) -> None: ...
    def OnNewDocument(self) -> None: ...
    def OnOpenDocument(self, pathName: str, /) -> None: ...
    def OnSaveDocument(self, pathName: str, /) -> None: ...
    def SetModifiedFlag(self, bModified: int = ..., /) -> None: ...
    def SaveModified(self): ...
    def SetPathName(self, path: str, /) -> None: ...
    def SetTitle(self, title: str, /) -> None: ...
    def UpdateAllViews(self, sender: PyCView, hint: Incomplete | None = ..., /) -> None: ...

class PyCEdit:
    def CreateWindow(
        self, style, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], parent: PyCWnd, _id, /
    ) -> None: ...
    def Clear(self): ...
    def Copy(self) -> None: ...
    def Cut(self) -> None: ...
    def FmtLines(self, bAddEOL, /): ...
    def GetFirstVisibleLine(self): ...
    def GetSel(self) -> tuple[Incomplete, Incomplete]: ...
    def GetLine(self, lineNo, /): ...
    def GetLineCount(self): ...
    def LimitText(self, nChars: int = ..., /) -> None: ...
    def LineFromChar(self, charNo: int = ..., /): ...
    def LineIndex(self, lineNo: int = ..., /): ...
    def LineScroll(self, nLines, nChars: int = ..., /): ...
    def Paste(self) -> None: ...
    def ReplaceSel(self, text: str, /) -> None: ...
    def SetReadOnly(self, bReadOnly: int = ..., /) -> None: ...
    def SetSel(self, start, end, arg, bNoScroll1, bNoScroll: int = ..., /) -> None: ...

class PyCEditView:
    def IsModified(self) -> bool: ...
    def LoadFile(self, fileName: str, /) -> None: ...
    def SetModifiedFlag(self, bModified: int = ..., /) -> None: ...
    def GetEditCtrl(self): ...
    def PreCreateWindow(self, createStruct, /): ...
    def SaveFile(self, fileName: str, /) -> None: ...
    def OnCommand(self, wparam, lparam, /) -> None: ...

class PyCFileDialog:
    def GetPathName(self) -> str: ...
    def GetFileName(self) -> str: ...
    def GetFileExt(self) -> str: ...
    def GetFileTitle(self) -> str: ...
    def GetPathNames(self) -> str: ...
    def GetReadOnlyPref(self): ...
    def SetOFNTitle(self, title: str, /) -> None: ...
    def SetOFNInitialDir(self, title: str, /) -> None: ...

class PyCFont:
    def GetSafeHandle(self): ...

class PyCFontDialog:
    def DoModal(self): ...
    def GetCurrentFont(self): ...
    def GetCharFormat(self): ...
    def GetColor(self): ...
    def GetFaceName(self) -> str: ...
    def GetStyleName(self) -> str: ...
    def GetSize(self): ...
    def GetWeight(self): ...
    def IsStrikeOut(self) -> bool: ...
    def IsUnderline(self) -> bool: ...
    def IsBold(self) -> bool: ...
    def IsItalic(self) -> bool: ...

class PyCFormView:
    def OnCommand(self, wparam, lparam, /) -> None: ...

class PyCFrameWnd:
    def BeginModalState(self) -> None: ...
    def CreateWindow(
        self,
        wndClass: str,
        title: str,
        style,
        PyCWnd,
        menuId,
        styleEx,
        rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete] | None = ...,
        createContext: Incomplete | None = ...,
        /,
    ): ...
    def EndModalState(self) -> None: ...
    def DockControlBar(
        self, controlBar: PyCControlBar, arg: tuple[Incomplete, Incomplete, Incomplete, Incomplete], dockBarId: int = ..., /
    ) -> None: ...
    def EnableDocking(self, style, /) -> None: ...
    def FloatControlBar(self, controlBar: PyCControlBar, arg: tuple[Incomplete, Incomplete], style, /) -> None: ...
    def GetActiveDocument(self) -> PyCDocument: ...
    def GetControlBar(self, _id, /) -> PyCControlBar: ...
    def GetMessageString(self, _id, /) -> str: ...
    def GetMessageBar(self) -> PyCWnd: ...
    def IsTracking(self) -> bool: ...
    def InModalState(self): ...
    def LoadAccelTable(self, _id: PyResourceId, /) -> None: ...
    def LoadFrame(
        self, idResource, style: int = ..., wndParent: PyCWnd | None = ..., context: Incomplete | None = ..., /
    ) -> None: ...
    def LoadBarState(self, profileName: str, /) -> None: ...
    def PreCreateWindow(self, createStruct, /): ...
    def SaveBarState(self, profileName: str, /) -> None: ...
    def ShowControlBar(self, controlBar: PyCControlBar, bShow, bDelay, /) -> None: ...
    def RecalcLayout(self, bNotify: int = ..., /) -> None: ...
    def GetActiveView(self) -> PyCView: ...
    def OnBarCheck(self, _id, /): ...
    def OnUpdateControlBarMenu(self, cmdUI: PyCCmdUI, /): ...
    def SetActiveView(self, view: PyCView, bNotify: int = ..., /) -> None: ...

class PyCGdiObject: ...

class PyCImagelist:
    def Add(self, arg: tuple[Incomplete, Incomplete], bitmap, color, hIcon, /): ...
    def Destroy(self) -> None: ...
    def DeleteImagelist(self) -> None: ...
    def GetBkColor(self): ...
    def GetSafeHandle(self): ...
    def GetImageCount(self): ...
    def GetImageInfo(self, index, /): ...
    def SetBkColor(self, color, /) -> None: ...

class PyClistBox:
    def AddString(self, _object, /): ...
    def DeleteString(self, pos, /): ...
    def Dir(self, attr, wild: str, /): ...
    def GetCaretIndex(self): ...
    def GetCount(self): ...
    def GetCurSel(self): ...
    def GetItemData(self, item, /): ...
    def GetItemValue(self, item, /): ...
    def GetSel(self, index, /): ...
    def GetSelCount(self): ...
    def GetSelItems(self): ...
    def GetSelTextItems(self): ...
    def GetTopIndex(self): ...
    def GetText(self, index, /) -> str: ...
    def GetTextLen(self, index, /): ...
    def InsertString(self, pos, _object, /): ...
    def ResetContent(self) -> None: ...
    def SetCaretIndex(self, index, bScroll: int = ..., /) -> None: ...
    def SelectString(self, after, string: str, /) -> None: ...
    def SelItemRange(self, bSel, start, end, /) -> None: ...
    def SetCurSel(self, index, /) -> None: ...
    def SetItemData(self, item, Data, /): ...
    def SetItemValue(self, item, data, /): ...
    def SetSel(self, index, bSel: int = ..., /) -> None: ...
    def SetTabStops(self, eachTabStop, tabStops, /) -> None: ...
    def SetTopIndex(self, index, /) -> None: ...

class PyClistCtrl:
    def Arrange(self, code, /) -> None: ...
    def CreateWindow(self, style, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], PyCWnd, _id, /) -> None: ...
    def DeleteAllItems(self) -> None: ...
    def DeleteItem(self, item, /) -> None: ...
    def GetTextColor(self): ...
    def SetTextColor(self, color, /) -> None: ...
    def GetBkColor(self): ...
    def SetBkColor(self, color, /) -> None: ...
    def GetItem(self, item, sub, /) -> LV_ITEM: ...
    def GetItemCount(self): ...
    def GetItemRect(self, item, bTextOnly, /) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def GetEditControl(self) -> PyCEdit: ...
    def EditLabel(self, item, /) -> PyCEdit: ...
    def EnsureVisible(self, item, bPartialOK, /): ...
    def CreateDragImage(self, item, /) -> tuple[PyCImagelist, Incomplete, Incomplete]: ...
    def GetImagelist(self, nImagelist, /) -> PyCImagelist: ...
    def GetNextItem(self, item, flags, /): ...
    def InsertColumn(self, colNo, item: LV_COLUMN, /): ...
    def InsertItem(self, item: LV_ITEM, item1, text, image, item2, text1, /): ...
    def SetImagelist(self, imagelist: PyCImagelist, imageType, /): ...
    def GetColumn(self, column, /) -> LV_COLUMN: ...
    def GetTextBkColor(self): ...
    def SetTextBkColor(self, color, /) -> None: ...
    def GetTopIndex(self): ...
    def GetCountPerPage(self): ...
    def GetSelectedCount(self): ...
    def SetItem(self, item: LV_ITEM, /): ...
    def SetItemState(self, item, state, mask, /): ...
    def GetItemState(self, item, mask, /): ...
    def SetItemData(self, item, Data, /): ...
    def GetItemData(self, item, /): ...
    def SetItemCount(self, count, /) -> None: ...
    def SetItemText(self, item, sub, text: str, /): ...
    def GetItemText(self, item, sub, /): ...
    def RedrawItems(self, first, first1, /): ...
    def Update(self, item, /) -> None: ...
    def SetColumn(self, colNo, item: LV_COLUMN, /): ...
    def DeleteColumn(self, first, /): ...
    def GetColumnWidth(self, first, /): ...
    def SetColumnWidth(self, first, first1, /): ...
    def GetStringWidth(self, first, /): ...
    def HitTest(self, arg, /) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def GetItemPosition(self, item, /) -> tuple[Incomplete, Incomplete]: ...

class PyClistView:
    def PreCreateWindow(self, createStruct, /): ...
    def GetlistCtrl(self) -> PyClistCtrl: ...
    def OnCommand(self, wparam, lparam, /) -> None: ...

class PyCMDIChildWnd:
    def ActivateFrame(self, cmdShow: int = ..., /) -> None: ...
    def CreateWindow(
        self,
        wndClass: str,
        title: str,
        style,
        PyCWnd,
        rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete] | None = ...,
        createContext: Incomplete | None = ...,
        /,
    ): ...
    def GetMDIFrame(self) -> None: ...
    def MDIActivate(self, cmdShow: int = ..., /) -> None: ...
    def PreCreateWindow(self, createStruct, /): ...
    def PreTranslateMessage(self) -> None: ...
    def OnCommand(self, wparam, lparam, /) -> None: ...
    def OnClose(self) -> None: ...

class PyCMDIFrameWnd:
    def GetMDIClient(self) -> PyCMDIFrameWnd: ...
    def MDIGetActive(self) -> tuple[PyCMDIChildWnd, Incomplete]: ...
    def MDIActivate(self, window: PyCWnd, /) -> PyCMDIFrameWnd: ...
    def MDINext(self, fNext: int = ..., /) -> None: ...
    def PreCreateWindow(self, createStruct, /): ...
    def PreTranslateMessage(self) -> None: ...
    def OnCommand(self, wparam, lparam, /) -> None: ...
    def OnContextHelp(self): ...
    def OnClose(self) -> None: ...

class PyCMenu:
    def AppendMenu(self, flags, _id: int = ..., value: str | None = ..., /) -> None: ...
    def DeleteMenu(self, _id, flags, /) -> str: ...
    def EnableMenuItem(self, _id, flags, /): ...
    def GetMenuItemCount(self): ...
    def GetMenuItemID(self, pos, /): ...
    def GetMenuString(self, _id, arg, /) -> str: ...
    def GetSubMenu(self, pos, /) -> PyCMenu: ...
    def InsertMenu(self, pos, flags, _id: PyCMenu | int = ..., value: str | None = ..., /) -> None: ...
    def ModifyMenu(self, pos, flags, _id: int = ..., value: str | None = ..., /) -> None: ...
    def TrackPopupMenu(self, x_y: _TwoIntSequence, flags: int = ..., owner: PyCWnd = ..., /) -> None: ...

class PyCOleClientItem:
    def CreateNewItem(self) -> None: ...
    def Close(self) -> None: ...
    def DoVerb(self) -> None: ...
    def Draw(self) -> None: ...
    def GetActiveView(self) -> PyCView: ...
    def GetDocument(self) -> PyCDocument: ...
    def GetInPlaceWindow(self) -> PyCWnd: ...
    def GetItemState(self) -> None: ...
    def GetObject(self) -> PyIUnknown: ...
    def GetStorage(self) -> None: ...
    def OnActivate(self) -> None: ...
    def OnChange(self) -> None: ...
    def OnChangeItemPosition(self): ...
    def OnDeactivateUI(self): ...
    def Run(self) -> None: ...
    def SetItemRects(self) -> None: ...

class PyCOleDialog: ...

class PyCOleDocument:
    def EnableCompoundFile(self, bEnable: int = ..., /) -> None: ...
    def GetStartPosition(self): ...
    def GetNextItem(self, pos, /) -> tuple[Incomplete, PyCOleClientItem]: ...
    def GetInPlaceActiveItem(self, wnd: PyCWnd, /) -> PyCOleClientItem: ...

class PyCOleInsertDialog:
    def GetClassID(self): ...
    def GetSelectionType(self): ...
    def GetPathName(self): ...

class PyCPrintDialog: ...

class PyCPrintInfo:
    def DocObject(self) -> None: ...
    def GetDwFlags(self) -> None: ...
    def SetDwFlags(self) -> None: ...
    def GetDocOffsetPage(self) -> None: ...
    def SetDocOffsetPage(self) -> None: ...
    def SetPrintDialog(self) -> None: ...
    def GetDirect(self) -> None: ...
    def SetDirect(self) -> None: ...
    def GetPreview(self) -> None: ...
    def SetPreview(self) -> None: ...
    def GetContinuePrinting(self) -> None: ...
    def SetContinuePrinting(self) -> None: ...
    def GetCurPage(self) -> None: ...
    def SetCurPage(self) -> None: ...
    def GetNumPreviewPages(self) -> None: ...
    def SetNumPreviewPages(self) -> None: ...
    def GetUserData(self) -> None: ...
    def SetUserData(self) -> None: ...
    def GetDraw(self) -> None: ...
    def SetDraw(self) -> None: ...
    def GetPageDesc(self) -> None: ...
    def SetPageDesc(self) -> None: ...
    def GetMinPage(self) -> None: ...
    def SetMinPage(self) -> None: ...
    def GetMaxPage(self) -> None: ...
    def SetMaxPage(self) -> None: ...
    def GetOffsetPage(self) -> None: ...
    def GetFromPage(self) -> None: ...
    def GetToPage(self) -> None: ...
    def SetHDC(self, hdc, /) -> None: ...
    def CreatePrinterDC(self) -> None: ...
    def DoModal(self) -> None: ...
    def GetCopies(self) -> None: ...
    def GetDefaults(self) -> None: ...
    def FreeDefaults(self) -> None: ...
    def GetDeviceName(self) -> None: ...
    def GetDriverName(self) -> None: ...
    def GetDlgFromPage(self) -> None: ...
    def GetDlgToPage(self) -> None: ...
    def GetPortName(self) -> None: ...
    def GetPrinterDC(self) -> None: ...
    def PrintAll(self) -> None: ...
    def PrintCollate(self) -> None: ...
    def PrintRange(self) -> None: ...
    def PrintSelection(self) -> None: ...
    def GetHDC(self) -> None: ...
    def GetFlags(self) -> None: ...
    def SetFlags(self) -> None: ...
    def SetFromPage(self) -> None: ...
    def SetToPage(self) -> None: ...
    def GetPRINTDLGMinPage(self) -> None: ...
    def SetPRINTDLGMinPage(self) -> None: ...
    def GetPRINTDLGCopies(self) -> None: ...
    def SetPRINTDLGCopies(self) -> None: ...

class PyCProgressCtrl:
    def CreateWindow(
        self, style, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], parent: PyCWnd, _id, /
    ) -> None: ...
    def SetRange(self, nLower: int = ..., nUpper: int = ..., /) -> None: ...
    def SetPos(self, nPos: int = ..., /): ...
    def OffsetPos(self, nPos: int = ..., /): ...
    def SetStep(self, nStep: int = ..., /): ...
    def StepIt(self): ...

class PyCPropertyPage:
    def CancelToClose(self) -> None: ...
    def OnCancel(self) -> None: ...
    def OnOK(self) -> None: ...
    def OnApply(self) -> None: ...
    def OnReset(self) -> None: ...
    def OnQueryCancel(self) -> None: ...
    def OnWizardBack(self) -> None: ...
    def OnWizardNext(self) -> None: ...
    def OnWizardFinish(self) -> None: ...
    def OnSetActive(self): ...
    def OnKillActive(self): ...
    def SetModified(self, bChanged: int = ..., /) -> None: ...
    def SetPSPBit(self, bitMask, bitValue, /) -> None: ...

class PyCPropertySheet:
    def AddPage(self, page: PyCPropertyPage, /) -> None: ...
    def CreateWindow(self, style, exStyle, parent: PyCWnd | None = ..., /) -> None: ...
    def DoModal(self): ...
    def EnableStackedTabs(self, stacked, /) -> PyCPropertyPage: ...
    def EndDialog(self, result, /) -> None: ...
    def GetActiveIndex(self): ...
    def GetActivePage(self) -> PyCPropertyPage: ...
    def GetPage(self, pageNo, /) -> PyCPropertyPage: ...
    def GetPageIndex(self, page: PyCPropertyPage, /): ...
    def GetPageCount(self): ...
    def GetTabCtrl(self) -> PyCTabCtrl: ...
    def OnInitDialog(self): ...
    def PressButton(self, button, /) -> None: ...
    def RemovePage(self, offset, page, /) -> None: ...
    def SetActivePage(self, page: PyCPropertyPage, /) -> None: ...
    def SetTitle(self, title: str, /) -> None: ...
    def SetFinishText(self, text: str, /) -> None: ...
    def SetWizardMode(self) -> None: ...
    def SetWizardButtons(self, flags, /) -> None: ...
    def SetPSHBit(self, bitMask, bitValue, /) -> None: ...

class PyCRect: ...
class PyCRgn: ...

class PyCRichEditCtrl:
    def Clear(self): ...
    def Copy(self) -> None: ...
    def CreateWindow(
        self, style, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], parent: PyCWnd, _id, /
    ) -> None: ...
    def Cut(self) -> None: ...
    def FindText(self, charPos, /) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def GetCharPos(self, charPos, /): ...
    def GetDefaultCharFormat(self): ...
    def GetEventMask(self): ...
    def GetSelectionCharFormat(self): ...
    def GetFirstVisibleLine(self): ...
    def GetParaFormat(self): ...
    def GetSel(self) -> tuple[Incomplete, Incomplete]: ...
    def GetSelText(self) -> str: ...
    def GetTextLength(self): ...
    def GetLine(self, lineNo, /): ...
    def GetModify(self): ...
    def GetLineCount(self): ...
    def LimitText(self, nChars: int = ..., /) -> None: ...
    def LineFromChar(self, charNo: int = ..., /): ...
    def LineIndex(self, lineNo: int = ..., /): ...
    def LineScroll(self, nLines, nChars: int = ..., /): ...
    def Paste(self) -> None: ...
    def ReplaceSel(self, text: str, /) -> None: ...
    def SetBackgroundColor(self, bSysColor, cr: int = ..., /): ...
    def SetDefaultCharFormat(self, charFormat, /) -> None: ...
    def SetEventMask(self, eventMask, /): ...
    def SetSelectionCharFormat(self, charFormat, /) -> None: ...
    def SetModify(self, modified: int = ..., /) -> None: ...
    def SetOptions(self, op, flags, /) -> None: ...
    def SetParaFormat(self, paraFormat, /): ...
    def SetReadOnly(self, bReadOnly: int = ..., /) -> None: ...
    def SetSel(self, start, end, arg, /) -> None: ...
    def SetSelAndCharFormat(self, charFormat, /) -> None: ...
    def SetTargetDevice(self, dc: PyCDC, lineWidth, /) -> None: ...
    def StreamIn(self, _format, method, /) -> tuple[Incomplete, Incomplete]: ...
    def StreamOut(self, _format, method, /) -> tuple[Incomplete, Incomplete]: ...

class PyCRichEditDoc:
    def OnCloseDocument(self) -> None: ...

class PyCRichEditDocTemplate:
    def DoCreateRichEditDoc(self, fileName: str | None = ..., /) -> PyCRichEditDoc: ...

class PyCRichEditView:
    def GetRichEditCtrl(self) -> PyCRichEditCtrl: ...
    def SetWordWrap(self, wordWrap, /): ...
    def WrapChanged(self): ...
    def SaveTextFile(self, FileName, /): ...

class PyCScrollView:
    def GetDeviceScrollPosition(self) -> tuple[Incomplete, Incomplete]: ...
    def GetDC(self) -> PyCDC: ...
    def GetScrollPosition(self) -> tuple[Incomplete, Incomplete]: ...
    def GetTotalSize(self) -> tuple[Incomplete, Incomplete]: ...
    def OnCommand(self, wparam, lparam, /) -> None: ...
    def ResizeParentToFit(self, bShrinkOnly: int = ..., /): ...
    def SetScaleToFitSize(self, size: tuple[Incomplete, Incomplete], /) -> None: ...
    def ScrollToPosition(self, position: tuple[Incomplete, Incomplete], /) -> None: ...
    def SetScrollSizes(
        self,
        mapMode,
        sizeTotal: tuple[Incomplete, Incomplete],
        arg: tuple[Incomplete, Incomplete],
        arg1: tuple[Incomplete, Incomplete],
        /,
    ) -> None: ...
    def UpdateBars(self) -> None: ...

class PyCSliderCtrl:
    def CreateWindow(
        self, style, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], parent: PyCWnd, _id, /
    ) -> None: ...
    def GetLineSize(self): ...
    def SetLineSize(self, nLineSize: int = ..., /): ...
    def GetPageSize(self): ...
    def SetPageSize(self, nPageSize: int = ..., /): ...
    def GetRangeMax(self): ...
    def GetRangeMin(self): ...
    def GetRange(self): ...
    def SetRange(self, nRangeMin: int = ..., nRangeMax: int = ..., bRedraw: int = ..., /): ...
    def GetSelection(self): ...
    def SetSelection(self, nRangeMin: int = ..., nRangeMax: int = ..., /): ...
    def GetChannelRect(self): ...
    def GetThumbRect(self): ...
    def GetPos(self): ...
    def SetPos(self, nPos: int = ..., /): ...
    def GetNumTics(self): ...
    def GetTicArray(self): ...
    def GetTic(self, nTic: int = ..., /): ...
    def GetTicPos(self, nTic: int = ..., /): ...
    def SetTic(self, nTic: int = ..., /): ...
    def SetTicFreq(self, nFreq: int = ..., /): ...
    def ClearSel(self, bRedraw: int = ..., /): ...
    def VerifyPos(self): ...
    def ClearTics(self, bRedraw: int = ..., /): ...

class PyCSpinButtonCtrl:
    def GetPos(self): ...
    def SetPos(self, pos, /): ...
    def SetRange(self): ...
    def SetRange32(self): ...

class PyCSplitterWnd:  # aka PyCSplitter
    def GetPane(self, row, col, /) -> PyCWnd: ...
    def CreateView(self, view: PyCView, row, col, arg: tuple[Incomplete, Incomplete], /) -> None: ...
    def CreateStatic(self, parent: PyCSplitterWnd, rows, cols, style=..., _id=..., /) -> None: ...
    def SetColumnInfo(self, column, ideal, _min, /) -> None: ...
    def SetRowInfo(self, row, ideal, _min, /) -> None: ...
    def IdFromRowCol(self, row, col, /) -> None: ...
    def DoKeyboardSplit(self): ...

class PyCStatusBar:
    def GetPaneInfo(self, index, /) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def GetStatusBarCtrl(self) -> PyCStatusBarCtrl: ...
    def SetIndicators(self, indicators, /) -> None: ...
    def SetPaneInfo(self, index, _id, style, width, /) -> None: ...

class PyCStatusBarCtrl:
    def CreateWindow(
        self, style, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], parent: PyCWnd, _id, /
    ) -> None: ...
    def GetBorders(self) -> tuple[Incomplete, Incomplete, Incomplete]: ...
    def GetParts(self, nParts, /): ...
    def GetRect(self, nPane, /) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def GetText(self, nPane, /): ...
    def GetTextAttr(self, nPane, /): ...
    def GetTextLength(self, nPane, /): ...
    def SetMinHeight(self, nHeight, /) -> None: ...
    def SetParts(self, coord, /) -> None: ...
    def SetText(self, text: str, nPane, nType, /) -> None: ...
    def SetTipText(self, nPane, text: str, /) -> None: ...

class PyCTabCtrl:
    def GetCurSel(self): ...
    def GetItemCountl(self): ...
    def SetCurSel(self, index, /): ...

class PyCToolBar:
    def GetButtonStyle(self, index, /) -> None: ...
    def GetButtonText(self, index, /) -> str: ...
    def GetItemID(self, index, /) -> None: ...
    def SetButtonInfo(self, index, ID, style, imageIx, /) -> None: ...
    def GetToolBarCtrl(self) -> PyCToolBarCtrl: ...
    def LoadBitmap(self, _id: PyResourceId, /) -> None: ...
    def LoadToolBar(self, _id: PyResourceId, /) -> None: ...
    def SetBarStyle(self, style, /) -> None: ...
    def SetBitmap(self, hBitmap, /) -> None: ...
    def SetButtons(self, buttons, numButtons, /) -> None: ...
    def SetButtonStyle(self, index, style, /) -> None: ...
    def SetHeight(self, height, /) -> None: ...
    def SetSizes(self, sizeButton: tuple[Incomplete, Incomplete], sizeButton1: tuple[Incomplete, Incomplete], /) -> None: ...

class PyCToolBarCtrl:
    def AddBitmap(self, numButtons, bitmap, /): ...
    def AddButtons(self): ...
    def AddStrings(self, strings, /): ...
    def AutoSize(self) -> None: ...
    def CheckButton(self, nID, bCheck: int = ..., /): ...
    def CommandToIndex(self, nID, /): ...
    def CreateWindow(
        self, style, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], parent: PyCWnd, _id, /
    ) -> None: ...
    def Customize(self) -> None: ...
    def DeleteButton(self, nID, /) -> None: ...
    def EnableButton(self, nID, bEnable: int = ..., /) -> None: ...
    def GetBitmapFlags(self): ...
    def GetButton(self, nID, /): ...
    def GetButtonCount(self): ...
    def GetItemRect(self, nID, /) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def GetRows(self) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def HideButton(self, nID, bEnable: int = ..., /) -> None: ...
    def Indeterminate(self, nID, bEnable: int = ..., /) -> None: ...
    def InsertButton(self, nID, button: PyCToolBarCtrl, /): ...
    def IsButtonChecked(self, nID, /) -> bool: ...
    def IsButtonEnabled(self, nID, /) -> bool: ...
    def IsButtonHidden(self, nID, /) -> bool: ...
    def IsButtonIndeterminate(self, nID, /) -> bool: ...
    def IsButtonPressed(self, nID, /) -> bool: ...
    def PressButton(self, nID, bEnable: int = ..., /) -> None: ...
    def SetBitmapSize(self, width1, height1, width: int = ..., height: int = ..., /) -> None: ...
    def SetButtonSize(self, width1, height1, width: int = ..., height: int = ..., /) -> None: ...
    def SetCmdID(self, nIndex, nID, /) -> None: ...
    def SetRows(self, nRows, bLarger, /) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...

class PyCToolTipCtrl:
    def CreateWindow(self, parent: PyCWnd, style, /) -> None: ...
    def UpdateTipText(self, text: str, wnd: PyCWnd, _id, /) -> None: ...
    def AddTool(
        self, wnd: PyCWnd, text: str, _id, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete] | None = ..., /
    ) -> None: ...
    def SetMaxTipWidth(self, width, /): ...

class PyCTreeCtrl:
    def CreateWindow(self, style, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], PyCWnd, _id, /) -> None: ...
    def GetCount(self): ...
    def GetIndent(self): ...
    def SetIndent(self, indent, /) -> None: ...
    def GetImagelist(self, nImagelist, /) -> PyCImagelist: ...
    def SetImagelist(self, imagelist: PyCImagelist, imageType, /): ...
    def GetNextItem(self, item, code, /): ...
    def ItemHasChildren(self, item, /): ...
    def GetChildItem(self, item, /): ...
    def GetNextSiblingItem(self, item, /): ...
    def GetPrevSiblingItem(self, item, /): ...
    def GetParentItem(self, item, /): ...
    def GetFirstVisibleItem(self): ...
    def GetNextVisibleItem(self, item, /): ...
    def GetSelectedItem(self): ...
    def GetDropHilightItem(self): ...
    def GetRootItem(self): ...
    def GetToolTips(self): ...
    def GetItem(self, item, arg, /) -> TV_ITEM: ...
    def SetItem(self, item: TV_ITEM, /): ...
    def GetItemState(self, item, stateMask, /) -> tuple[Incomplete, Incomplete]: ...
    def SetItemState(self, item, state, stateMask, /) -> None: ...
    def GetItemImage(self, item, /) -> tuple[Incomplete, Incomplete]: ...
    def SetItemImage(self, item, iImage, iSelectedImage, /) -> None: ...
    def SetItemText(self, item, text: str, /): ...
    def GetItemText(self, item, /): ...
    def GetItemData(self, item, /): ...
    def SetItemData(self, item, Data, /): ...
    def GetItemRect(self, item, bTextOnly, /) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def GetEditControl(self) -> PyCEdit: ...
    def GetVisibleCount(self): ...
    def InsertItem(
        self,
        hParent,
        hInsertAfter,
        item: TV_ITEM,
        mask,
        text,
        image,
        selectedImage,
        state,
        stateMask,
        lParam,
        parent,
        parent1,
        text1,
        image1,
        selectedImage1,
        parent2,
        insertAfter,
        text2,
        parent3,
        parent4,
        /,
    ): ...
    def DeleteItem(self, item, /) -> None: ...
    def DeleteAllItems(self): ...
    def Expand(self, item, code, /) -> None: ...
    def Select(self, item, code, /) -> None: ...
    def SelectItem(self, item, /) -> None: ...
    def SelectDropTarget(self, item, /) -> None: ...
    def SelectSetFirstVisible(self, item, /) -> None: ...
    def EditLabel(self, item, /) -> PyCEdit: ...
    def CreateDragImage(self, item, /) -> PyCImagelist: ...
    def SortChildren(self, item, /) -> None: ...
    def EnsureVisible(self, item, /): ...
    def HitTest(self, arg, /) -> tuple[Incomplete, Incomplete]: ...

class PyCTreeView:
    def PreCreateWindow(self, createStruct, /): ...
    def GetTreeCtrl(self) -> PyCTreeCtrl: ...
    def OnCommand(self, wparam, lparam, /) -> None: ...

class PyCView:
    def CreateWindow(self, parent: PyCWnd, arg, arg1, arg2: tuple[Incomplete, Incomplete, Incomplete, Incomplete], /) -> None: ...
    def GetDocument(self) -> PyCDocument: ...
    def OnActivateView(self, activate, activateView: PyCView, DeactivateView: PyCView, /): ...
    def OnInitialUpdate(self) -> None: ...
    def OnMouseActivate(self, wnd: PyCWnd, hittest, message, /): ...
    def PreCreateWindow(self, createStruct, /): ...
    def OnFilePrint(self) -> None: ...
    def DoPreparePrinting(self): ...
    def OnBeginPrinting(self) -> None: ...
    def OnEndPrinting(self) -> None: ...

class PyCWinApp:
    def AddDocTemplate(self, template: PyCDocTemplate | DocTemplate, /) -> None: ...
    def FindOpenDocument(self, fileName: str, /) -> PyCDocument: ...
    def GetDocTemplatelist(self) -> list[Incomplete]: ...
    def InitDlgInstance(self, dialog: PyCDialog, /) -> None: ...
    def LoadCursor(self, cursorId: PyResourceId, /): ...
    def LoadStandardCursor(self, cursorId: PyResourceId, /): ...
    def LoadOEMCursor(self, cursorId, /): ...
    def LoadIcon(self, idResource: int, /) -> int: ...
    def LoadStandardIcon(self, resourceName: PyResourceId, /): ...
    def OpenDocumentFile(self, fileName: str, /) -> None: ...
    def OnFileNew(self) -> None: ...
    def OnFileOpen(self) -> None: ...
    def RemoveDocTemplate(self, template: PyCDocTemplate | DocTemplate, /) -> None: ...
    def Run(self): ...
    def IsInproc(self) -> bool: ...

class PyCWinThread:
    def CreateThread(self) -> None: ...
    def PumpIdle(self) -> None: ...
    def PumpMessages(self) -> None: ...
    def Run(self): ...
    def SetMainFrame(self, mainFrame: PyCWnd, /) -> None: ...
    def SetThreadPriority(self, priority: PyCWnd, /) -> None: ...

class PyCWnd:
    def ActivateFrame(self, cmdShow, /) -> None: ...
    def BringWindowToTop(self) -> None: ...
    def BeginPaint(self) -> tuple[PyCDC, Incomplete]: ...
    def CalcWindowRect(
        self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], nAdjustType, /
    ) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def CenterWindow(self, altwin: PyCWnd | None = ..., /) -> None: ...
    def CheckRadioButton(self, idFirst, idLast, idCheck, /) -> None: ...
    def ChildWindowFromPoint(self, x, y, flag: int = ..., /) -> PyCWnd: ...
    def ClientToScreen(
        self, point: tuple[Incomplete, Incomplete], rect, /
    ) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def CreateWindow(
        self,
        classId: str,
        windowName: str,
        style,
        rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        parent: PyCWnd,
        _id,
        context: Incomplete | None = ...,
        /,
    ) -> None: ...
    def CreateWindowEx(
        self,
        styleEx,
        classId: str,
        windowName: str,
        style,
        rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete],
        parent: PyCWnd,
        _id,
        createStruct1,
        createStruct: CREATESTRUCT | None = ...,
        /,
    ) -> None: ...
    def DefWindowProc(self, message, idLast, idCheck, /): ...
    def DestroyWindow(self) -> None: ...
    def DlgDirlist(self, defPath: str, idlistbox, idStaticPath, fileType, /) -> None: ...
    def DlgDirlistComboBox(self) -> None: ...
    def DlgDirSelect(self, idlistbox, /) -> str: ...
    def DlgDirSelectComboBox(self, idlistbox, /) -> str: ...
    def DragAcceptFiles(self, bAccept: int = ..., /) -> None: ...
    def DrawMenuBar(self) -> None: ...
    def EnableWindow(self, bEnable: int = ..., /): ...
    def EndModalLoop(self, result, /) -> None: ...
    def EndPaint(self, paintStruct, /) -> None: ...
    def GetCheckedRadioButton(self, idFirst, idLast, /): ...
    def GetClientRect(self) -> tuple[Incomplete, Incomplete, Incomplete, Incomplete]: ...
    def GetDC(self) -> PyCDC: ...
    def GetDCEx(self) -> PyCDC: ...
    def GetDlgCtrlID(self): ...
    def GetDlgItem(self, idControl, /) -> PyCWnd: ...
    def GetDlgItemInt(self, idControl, bUnsigned: int = ..., /): ...
    def GetDlgItemText(self, idControl, /) -> str: ...
    def GetLastActivePopup(self) -> PyCWnd: ...
    def GetMenu(self) -> PyCMenu: ...
    def GetParent(self) -> PyCWnd: ...
    def GetParentFrame(self) -> PyCWnd: ...
    def GetSafeHwnd(self): ...
    def GetScrollInfo(self, nBar, mask, /): ...
    def GetScrollPos(self, nBar, /): ...
    def GetStyle(self): ...
    def GetExStyle(self): ...
    def GetSystemMenu(self) -> PyCMenu: ...
    def GetTopLevelFrame(self) -> PyCWnd: ...
    def GetTopLevelOwner(self) -> PyCWnd: ...
    def GetTopLevelParent(self) -> PyCWnd: ...
    def GetTopWindow(self) -> PyCWnd: ...
    def GetWindow(self, _type, /) -> PyCWnd: ...
    def GetWindowDC(self) -> PyCDC: ...
    def GetWindowPlacement(self): ...
    def GetWindowRect(self) -> tuple[int, int, int, int]: ...
    def GetWindowText(self) -> str: ...
    def HideCaret(self) -> None: ...
    def HookAllKeyStrokes(self, obHandler, /) -> None: ...
    def HookKeyStroke(self, obHandler, ch, /): ...
    def HookMessage(self, obHandler, message, /): ...
    def InvalidateRect(self, arg: tuple[Incomplete, Incomplete, Incomplete, Incomplete], bErase: int = ..., /) -> None: ...
    def InvalidateRgn(self, region: PyCRgn, bErase: int = ..., /) -> None: ...
    def IsChild(self, obWnd: PyCWnd, /) -> bool: ...
    def IsDlgButtonChecked(self, idCtl, /) -> bool: ...
    def IsIconic(self) -> bool: ...
    def IsZoomed(self) -> bool: ...
    def IsWindow(self) -> bool: ...
    def IsWindowVisible(self) -> bool: ...
    def KillTimer(self): ...
    def LockWindowUpdate(self) -> None: ...
    def MapWindowPoints(self, wnd: PyCWnd, points: list[tuple[Incomplete, Incomplete]], /) -> None: ...
    def MouseCaptured(self): ...
    def MessageBox(self, message: str, arg, title: str | None = ..., /) -> None: ...
    def ModifyStyle(self, remove, add, flags: int = ..., /): ...
    def ModifyStyleEx(self, remove, add, flags: int = ..., /): ...
    def MoveWindow(self, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete], bRepaint: int = ..., /) -> None: ...
    def OnClose(self): ...
    def OnCtlColor(self, dc: PyCDC, control, _type, /): ...
    def OnEraseBkgnd(self, dc: PyCDC, /): ...
    def OnNcHitTest(self, arg: tuple[Incomplete, Incomplete], /): ...
    def OnPaint(self): ...
    def OnQueryDragIcon(self): ...
    def OnQueryNewPalette(self): ...
    def OnSetCursor(self, wnd: PyCWnd, hittest, message, /): ...
    def OnMouseActivate(self, wnd: PyCWnd, hittest, message, /): ...
    def OnWndMsg(self, msg, wParam, lParam, /) -> tuple[Incomplete, Incomplete]: ...
    def PreCreateWindow(self, createStruct, /): ...
    def PumpWaitingMessages(self, firstMsg, lastMsg, /) -> None: ...
    def RedrawWindow(
        self, _object: PyCRgn, flags, rect: tuple[Incomplete, Incomplete, Incomplete, Incomplete] | None = ..., /
    ) -> None: ...
    def ReleaseCapture(self) -> None: ...
    def ReleaseDC(self, dc: PyCDC, /) -> None: ...
    def RepositionBars(self, idFirst, idLast, idLeftOver, /) -> None: ...
    def RunModalLoop(self, flags, /): ...
    def PostMessage(self, idMessage, wParam: int = ..., lParam: int = ..., /) -> None: ...
    def SendMessageToDescendants(self, idMessage, wParam: int = ..., lParam: int = ..., bDeep: int = ..., /) -> None: ...
    def SendMessage(self, idMessage, wParam: int = ..., lParam: int = ..., /) -> None: ...
    def SetActiveWindow(self) -> PyCWnd: ...
    def SetForegroundWindow(self) -> None: ...
    def SetWindowPos(
        self, hWndInsertAfter, position: tuple[Incomplete, Incomplete, Incomplete, Incomplete], flags, /
    ) -> None: ...
    @overload
    def ScreenToClient(self, rect: tuple[int, int], /) -> tuple[int, int]: ...
    @overload
    def ScreenToClient(self, rect: tuple[int, int, int, int], /) -> tuple[int, int, int, int]: ...
    @overload
    def ScreenToClient(self, rect: _TwoIntSequence | _FourIntSequence, /) -> tuple[int, int] | tuple[int, int, int, int]: ...
    def SetCapture(self) -> None: ...
    def SetDlgItemText(self, idControl, text: str, /) -> None: ...
    def SetFocus(self) -> None: ...
    def SetFont(self, font: PyCFont, bRedraw: int = ..., /) -> None: ...
    def SetIcon(self): ...
    def SetMenu(self, menuObj: PyCMenu, /) -> None: ...
    def SetRedraw(self, bState: int = ..., /) -> None: ...
    def SetScrollPos(self, nBar, nPos, redraw: int = ..., /): ...
    def SetScrollInfo(self, nBar, ScrollInfo, redraw: int = ..., /): ...
    def SetTimer(self, idEvent, elapse, /): ...
    def SetWindowPlacement(self, placement, /) -> None: ...
    def SetWindowText(self, text: str, /) -> None: ...
    def ShowCaret(self) -> None: ...
    def ShowScrollBar(self, nBar, bShow: int = ..., /) -> None: ...
    def ShowWindow(self, arg, /): ...
    def UnLockWindowUpdate(self) -> None: ...
    def UpdateData(self, bSaveAndValidate: int = ..., /): ...
    def UpdateDialogControls(self, pTarget: PyCCmdTarget, disableIfNoHandler, /): ...
    def UpdateWindow(self) -> None: ...

class PyDDEConv:
    def ConnectTo(self, service: str, topic: str, /) -> None: ...
    def Connected(self) -> None: ...
    def Exec(self, Cmd: str, /) -> None: ...
    def Request(self) -> None: ...
    def Poke(self) -> None: ...

class PyDDEServer:
    def AddTopic(self, topic: PyDDETopic, /) -> None: ...
    def Create(self, name: str, filterFlags: int = ..., /) -> None: ...
    def Destroy(self) -> None: ...
    def GetLastError(self): ...
    def Shutdown(self) -> None: ...

class PyDDEStringItem:
    def SetData(self, data: str, /) -> None: ...

class PyDDETopic:
    def AddItem(self, item, /) -> None: ...
    def Destroy(self) -> None: ...

class PyDLL:
    def GetFileName(self) -> str: ...
    def AttachToMFC(self) -> None: ...

class SCROLLINFO: ...
class TV_ITEM: ...

class EXTENSION_CONTROL_BLOCK:
    @property
    def Version(self) -> int: ...
    @property
    def TotalBytes(self): ...
    @property
    def AvailableBytes(self): ...
    @property
    def HttpStatusCode(self): ...
    @property
    def Method(self): ...
    @property
    def ConnID(self): ...
    @property
    def QueryString(self): ...
    @property
    def PathInfo(self): ...
    @property
    def PathTranslated(self): ...
    @property
    def AvailableData(self): ...
    @property
    def ContentType(self): ...
    @property
    def LogData(self): ...
    def WriteClient(self, data: str, reserved: int = ..., /): ...
    def GetServerVariable(self, variable: str, default, /) -> str: ...
    def ReadClient(self, nbytes, /) -> str: ...
    def SendResponseHeaders(self, reply: str, headers: str, keepAlive: bool = ..., /) -> None: ...
    def SetFlushFlag(self, flag, /) -> None: ...
    def TransmitFile(self, callback, param, hFile, statusCode: str, BytesToWrite, Offset, head: str, tail: str, flags, /): ...
    def MapURLToPath(self) -> None: ...
    def DoneWithSession(self, status, /) -> None: ...
    def Redirect(self, url: str, /) -> None: ...
    def IsKeepAlive(self) -> bool: ...
    def GetAnonymousToken(self, metabase_path: str, /): ...
    def GetImpersonationToken(self): ...
    def IsKeepConn(self) -> bool: ...
    def ExecURL(self, url: str, method: str, clientHeaders: str, info, entity, flags, /): ...
    def GetExecURLStatus(self): ...
    def IOCompletion(self, func, arg: Incomplete | None = ..., /): ...
    def ReportUnhealthy(self, reason: str | None = ..., /): ...
    def IOCallback(self, ecb: EXTENSION_CONTROL_BLOCK, arg, cbIO, dwError, /): ...

class HSE_VERSION_INFO:
    @property
    def ExtensionDesc(self) -> str: ...

class HTTP_FILTER_AUTHENT:
    @property
    def User(self) -> str: ...
    @property
    def Password(self) -> str: ...

class HTTP_FILTER_CONTEXT:
    @property
    def Revision(self): ...
    @property
    def fIsSecurePort(self): ...
    @property
    def NotificationType(self): ...
    @property
    def FilterContext(self): ...
    def GetData(self): ...
    def GetServerVariable(self, variable: str, default, /) -> str: ...
    def WriteClient(self, data: str, reserverd: int = ..., /) -> None: ...
    def AddResponseHeaders(self, data: str, reserverd: int = ..., /) -> None: ...
    def SendResponseHeader(self, status: str, header: str, /) -> None: ...
    def DisableNotifications(self, flags, /) -> None: ...

class HTTP_FILTER_LOG:
    @property
    def ClientHostName(self) -> str: ...
    @property
    def ClientUserName(self) -> str: ...
    @property
    def ServerName(self) -> str: ...
    @property
    def Operation(self) -> str: ...
    @property
    def Target(self) -> str: ...
    @property
    def Parameters(self) -> str: ...
    @property
    def HttpStatus(self): ...

class HTTP_FILTER_PREPROC_HEADERS:
    def GetHeader(self, header: str, default, /) -> str: ...
    def SetHeader(self, name: str, val: str, /) -> None: ...
    def AddHeader(self) -> None: ...

class HTTP_FILTER_RAW_DATA:
    @property
    def InData(self) -> str: ...

class HTTP_FILTER_URL_MAP:
    @property
    def URL(self) -> str: ...
    @property
    def PhysicalPath(self) -> str: ...

class HTTP_FILTER_VERSION:
    @property
    def ServerFilterVersion(self): ...
    @property
    def FilterVersion(self): ...
    @property
    def Flags(self): ...
    @property
    def FilterDesc(self) -> str: ...
