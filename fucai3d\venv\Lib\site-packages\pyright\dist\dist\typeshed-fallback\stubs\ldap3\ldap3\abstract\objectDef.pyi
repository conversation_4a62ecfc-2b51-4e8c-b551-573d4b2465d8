class ObjectDef:
    def __init__(self, object_class=None, schema=None, custom_validator=None, auxiliary_class=None) -> None: ...
    def __getitem__(self, item): ...
    def __getattr__(self, item: str): ...
    def __setattr__(self, key: str, value) -> None: ...
    def __iadd__(self, other): ...
    def __isub__(self, other): ...
    def __iter__(self): ...
    def __len__(self) -> int: ...
    def __bool__(self) -> bool: ...
    def __contains__(self, item): ...
    def add_from_schema(self, attribute_name, mandatory: bool = False) -> None: ...
    def add_attribute(self, definition=None) -> None: ...
    def remove_attribute(self, item) -> None: ...
    def clear_attributes(self) -> None: ...
