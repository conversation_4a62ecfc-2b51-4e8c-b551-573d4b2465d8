import numbers
import xml.etree.ElementTree as ET
from collections.abc import Iterable
from typing import ClassVar, Final, SupportsIndex, overload
from typing_extensions import Self

GLOBAL_ATTRIBUTES: Final[tuple[str, ...]]

class MathElement(ET.Element):
    nchildren: ClassVar[int | None]
    parent: MathElement | None
    def __init__(self, *children, **attributes: object) -> None: ...  # attributes is passed to self.a_str method
    @staticmethod
    def a_str(v: object) -> str: ...
    def set(self, key: str, value: object) -> None: ...  # value is passed to self.a_str method
    @overload  # type: ignore[override]
    def __setitem__(self, key: SupportsIndex, value: MathElement) -> None: ...
    @overload  # type: ignore[override]
    def __setitem__(self, key: slice, value: Iterable[MathElement]) -> None: ...
    def is_full(self) -> bool: ...
    def close(self) -> MathElement | None: ...
    def append(self, element: MathElement) -> Self: ...  # type: ignore[override]
    def extend(self, elements: Iterable[MathElement]) -> Self: ...  # type: ignore[override]
    def pop(self, index: int = -1): ...
    def in_block(self) -> bool: ...
    def indent_xml(self, space: str = "  ", level: int = 0) -> None: ...
    def unindent_xml(self) -> None: ...
    def toxml(self, encoding: str | None = None) -> str: ...

class MathRow(MathElement): ...

class MathSchema(MathElement):
    nchildren: ClassVar[int]
    switch: bool
    def __init__(self, *children, switch: bool = False, **kwargs) -> None: ...

class MathToken(MathElement):
    nchildren: ClassVar[int]
    text: str
    def __init__(self, text: str | numbers.Number, **attributes: object) -> None: ...

class math(MathRow): ...
class mtext(MathToken): ...
class mi(MathToken): ...
class mn(MathToken): ...
class mo(MathToken): ...

class mspace(MathElement):
    nchildren: ClassVar[int]

class mrow(MathRow):
    def transfer_attributes(self, other) -> None: ...

class mfrac(MathSchema): ...

class msqrt(MathRow):
    nchildren: ClassVar[int]

class mroot(MathSchema): ...
class mstyle(MathRow): ...
class merror(MathRow): ...

class menclose(MathRow):
    nchildren: ClassVar[int]

class mpadded(MathRow): ...

class mphantom(MathRow):
    nchildren: ClassVar[int]

class msub(MathSchema): ...
class msup(MathSchema): ...

class msubsup(MathSchema):
    nchildren: ClassVar[int]

class munder(msub): ...
class mover(msup): ...
class munderover(msubsup): ...
class mtable(MathElement): ...
class mtr(MathRow): ...
class mtd(MathRow): ...
