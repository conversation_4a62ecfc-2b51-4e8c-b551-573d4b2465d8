from _typeshed import Incomplete
from typing import Any

from google.cloud.ndb import model

class _BaseMetadata(model.Model):
    KIND_NAME: str
    def __new__(cls, *args, **kwargs): ...

class Namespace(_BaseMetadata):
    KIND_NAME: str
    EMPTY_NAMESPACE_ID: int
    @property
    def namespace_name(self): ...
    @classmethod
    def key_for_namespace(cls, namespace): ...
    @classmethod
    def key_to_namespace(cls, key): ...

class Kind(_BaseMetadata):
    KIND_NAME: str
    @property
    def kind_name(self): ...
    @classmethod
    def key_for_kind(cls, kind): ...
    @classmethod
    def key_to_kind(cls, key): ...

class Property(_BaseMetadata):
    KIND_NAME: str
    @property
    def property_name(self): ...
    @property
    def kind_name(self): ...
    property_representation: Any
    @classmethod
    def key_for_kind(cls, kind): ...
    @classmethod
    def key_for_property(cls, kind, property): ...
    @classmethod
    def key_to_kind(cls, key): ...
    @classmethod
    def key_to_property(cls, key): ...

class EntityGroup:
    def __new__(cls, *args, **kwargs): ...

def get_entity_group_version(*args, **kwargs) -> None: ...
def get_kinds(start: Incomplete | None = ..., end: Incomplete | None = ...): ...
def get_namespaces(start: Incomplete | None = ..., end: Incomplete | None = ...): ...
def get_properties_of_kind(kind, start: Incomplete | None = ..., end: Incomplete | None = ...): ...
def get_representations_of_kind(kind, start: Incomplete | None = ..., end: Incomplete | None = ...): ...
