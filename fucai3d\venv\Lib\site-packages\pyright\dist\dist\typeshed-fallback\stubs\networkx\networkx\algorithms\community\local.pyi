from collections.abc import Callable, <PERSON><PERSON><PERSON>
from typing import Literal
from typing_extensions import TypeAlias

from networkx.classes.graph import Graph, _Node

__all__ = ["greedy_source_expansion"]

_Algorithm: TypeAlias = Literal["clauset"]

ALGORITHMS: dict[_<PERSON>gorithm, Callable[[Graph[<PERSON><PERSON><PERSON>], <PERSON><PERSON><PERSON>, int | None], set[<PERSON><PERSON><PERSON>]]]

def greedy_source_expansion(
    G: Graph[_Node], *, source: _Node, cutoff: int | None = None, method: _Algorithm = "clauset"
) -> set[_Node | None]: ...
