from typing import Final

VS_FFI_SIGNATURE: Final = -17890115
VS_FFI_STRUCVERSION: Final = 0x00010000
VS_FFI_FILEFLAGSMASK: Final = 0x0000003F
VOS_NT_WINDOWS32: Final = 0x00040004
null_byte: Final = b"\0"

def file_flags(debug): ...
def file_type(is_dll): ...
def VS_FIXEDFILEINFO(maj, min, sub, build, debug: int = 0, is_dll: int = 1): ...
def nullterm(s): ...
def pad32(s, extra: int = 2): ...
def addlen(s): ...
def String(key, value): ...
def StringTable(key, data): ...
def StringFileInfo(data): ...
def Var(key, value): ...
def VarFileInfo(data): ...
def VS_VERSION_INFO(maj, min, sub, build, sdata, vdata, debug: int = 0, is_dll: int = 1): ...
def stamp(pathname, options) -> None: ...
