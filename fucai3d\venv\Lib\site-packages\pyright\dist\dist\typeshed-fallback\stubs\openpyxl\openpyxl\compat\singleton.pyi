from typing import Any, overload

class Singleton(type):
    @overload
    def __init__(self, o: object, /) -> None: ...
    @overload
    def __init__(self, name: str, bases: tuple[type, ...], dict: dict[str, Any], /, **kwds: Any) -> None: ...
    def __call__(self, *args: Any, **kwds: Any) -> Any: ...

class Cached(type):
    @overload
    def __init__(self, o: object, /) -> None: ...
    @overload
    def __init__(self, name: str, bases: tuple[type, ...], dict: dict[str, Any], /, **kwds: Any) -> None: ...
    def __call__(self, *args: Any) -> Any: ...
