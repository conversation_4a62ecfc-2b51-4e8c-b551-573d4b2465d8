from collections.abc import Generator
from typing import Any

from ..formatter import Formatter
from .bbcode import <PERSON>ode<PERSON><PERSON>atter as BBCodeFormatter
from .html import Html<PERSON><PERSON>att<PERSON> as HtmlFormatter
from .img import (
    BmpImageFormatter as Bmp<PERSON>mageFormatter,
    Gif<PERSON><PERSON><PERSON><PERSON>atter as <PERSON><PERSON><PERSON><PERSON>Formatter,
    Image<PERSON><PERSON>atter as ImageFormatter,
    JpgImageFormatter as Jpg<PERSON>mageFormatter,
)
from .irc import IRCFormatter as IRCFormatter
from .latex import LatexFormatter as LatexFormatter
from .other import NullFormatter as NullFormatter, RawTokenFormatter as RawTokenFormatter, TestcaseFormatter as TestcaseFormatter
from .pangomarkup import PangoMarkupFormatter as PangoMarkupFormatter
from .rtf import RtfFormatter as RtfFormatter
from .svg import SvgFormatter as SvgFormatter
from .terminal import <PERSON>Formatter as TerminalFormatter
from .terminal256 import Terminal256<PERSON><PERSON>atter as Terminal256<PERSON>ormatter, TerminalTrueColor<PERSON><PERSON>atter as TerminalTrueColorFormatter

def get_all_formatters() -> Generator[type[Formatter[Any]], None, None]: ...
def get_formatter_by_name(_alias, **options): ...
def load_formatter_from_file(filename, formattername: str = "CustomFormatter", **options): ...
def get_formatter_for_filename(fn, **options): ...
