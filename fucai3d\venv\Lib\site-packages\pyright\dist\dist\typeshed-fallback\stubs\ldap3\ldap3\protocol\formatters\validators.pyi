def check_backslash(value): ...
def check_type(input_value, value_type): ...
def always_valid(input_value): ...
def validate_generic_single_value(input_value): ...
def validate_zero_and_minus_one_and_positive_int(input_value): ...
def validate_integer(input_value): ...
def validate_bytes(input_value): ...
def validate_boolean(input_value): ...
def validate_time_with_0_year(input_value): ...
def validate_time(input_value): ...
def validate_ad_timestamp(input_value): ...
def validate_ad_timedelta(input_value): ...
def validate_guid(input_value): ...
def validate_uuid(input_value): ...
def validate_uuid_le(input_value): ...
def validate_sid(input_value): ...
