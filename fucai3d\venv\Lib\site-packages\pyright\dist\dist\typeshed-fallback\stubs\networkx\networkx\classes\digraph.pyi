from _typeshed import Incomplete
from collections.abc import Iterator
from functools import cached_property
from typing_extensions import Self

from networkx.classes.coreviews import AdjacencyView
from networkx.classes.graph import Graph, _Node
from networkx.classes.reportviews import InDegreeView, InMultiDegreeView, OutDegreeView, OutEdgeView, OutMultiDegreeView

__all__ = ["DiGraph"]

class DiGraph(Graph[_Node]):
    @cached_property
    def succ(self) -> AdjacencyView[_Node, _Node, dict[str, Incomplete]]: ...
    @cached_property
    def pred(self) -> AdjacencyView[_Node, _Node, dict[str, Incomplete]]: ...
    def has_successor(self, u: _Node, v: _Node) -> bool: ...
    def has_predecessor(self, u: _Node, v: _Node) -> bool: ...
    def successors(self, n: _Node) -> Iterator[_Node]: ...
    neighbors = successors
    def predecessors(self, n: _Node) -> Iterator[_Node]: ...
    @cached_property
    def out_edges(self) -> OutEdgeView[_Node]: ...
    @cached_property
    def in_edges(self) -> OutEdgeView[_Node]: ...
    @cached_property
    def in_degree(self) -> InDegreeView[_Node] | InMultiDegreeView[_Node]: ...  # Include subtypes' possible return types
    @cached_property
    def out_degree(self) -> OutDegreeView[_Node] | OutMultiDegreeView[_Node]: ...  # Include subtypes' possible return types
    def to_undirected(self, reciprocal: bool = False, as_view: bool = False) -> Graph[_Node]: ...  # type: ignore[override]  # Has an additional `reciprocal` keyword argument
    def reverse(self, copy: bool = True) -> Self: ...
    def copy(self, as_view: bool = False) -> DiGraph[_Node]: ...
