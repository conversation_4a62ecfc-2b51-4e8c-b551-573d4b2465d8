from _typeshed import Incomplete, Unused
from datetime import date, datetime
from typing import <PERSON><PERSON><PERSON><PERSON>

from braintree.credit_card import CreditCard
from braintree.error_result import ErrorResult
from braintree.resource_collection import ResourceCollection
from braintree.successful_result import SuccessfulResult

class CreditCardGateway:
    gateway: Incomplete
    config: Incomplete
    def __init__(self, gateway) -> None: ...
    def create(self, params: dict[str, Incomplete] | None = None) -> SuccessfulResult | ErrorResult | None: ...
    def delete(self, credit_card_token: str) -> SuccessfulResult: ...
    def expired(self) -> ResourceCollection: ...
    def expiring_between(self, start_date: date | datetime, end_date: date | datetime) -> ResourceCollection: ...
    def find(self, credit_card_token: str) -> CreditCard: ...
    def forward(self, credit_card_token: Unused, receiving_merchant_id: Unused) -> NoReturn: ...
    def from_nonce(self, nonce: str) -> CreditCard: ...
    def update(
        self, credit_card_token: str, params: dict[str, Incomplete] | None = None
    ) -> SuccessfulResult | ErrorResult | None: ...
