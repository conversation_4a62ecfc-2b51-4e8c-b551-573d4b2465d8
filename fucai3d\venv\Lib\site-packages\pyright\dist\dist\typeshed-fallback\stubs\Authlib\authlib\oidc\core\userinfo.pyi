from authlib.oauth2.rfc6749.authorization_server import AuthorizationServer
from authlib.oauth2.rfc6749.requests import OAuth2Request
from authlib.oauth2.rfc6749.resource_protector import ResourceProtector

from .claims import UserInfo

class UserInfoEndpoint:
    ENDPOINT_NAME: str
    server: AuthorizationServer | None
    resource_protector: ResourceProtector | None
    def __init__(
        self, server: AuthorizationServer | None = None, resource_protector: ResourceProtector | None = None
    ) -> None: ...
    def create_endpoint_request(self, request: OAuth2Request): ...
    def __call__(self, request: OAuth2Request) -> tuple[int, dict[str, str | None], list[tuple[str, str]]]: ...
    def generate_user_info(self, user, scope: str) -> UserInfo: ...
    def get_issuer(self) -> str: ...
    def resolve_private_key(self): ...
