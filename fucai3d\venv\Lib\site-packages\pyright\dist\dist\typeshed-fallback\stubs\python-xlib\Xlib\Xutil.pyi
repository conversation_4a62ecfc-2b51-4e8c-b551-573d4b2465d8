from typing import Final

NoValue: Final = 0x0000
XValue: Final = 0x0001
YValue: Final = 0x0002
WidthValue: Final = 0x0004
HeightValue: Final = 0x0008
AllValues: Final = 0x000F
XNegative: Final = 0x0010
YNegative: Final = 0x0020
USPosition: Final = 0x001
USSize: Final = 0x002
PPosition: Final = 0x004
PSize: Final = 0x008
PMinSize: Final = 0x010
PMaxSize: Final = 0x020
PResizeInc: Final = 0x040
PAspect: Final = 0x080
PBaseSize: Final = 0x100
PWinGravity: Final = 0x200
PAllHints: Final = 252
InputHint: Final = 0x001
StateHint: Final = 0x002
IconPixmapHint: Final = 0x004
IconWindowHint: Final = 0x008
IconPositionHint: Final = 0x010
IconMaskHint: Final = 0x020
WindowGroupHint: Final = 0x040
MessageHint: Final = 0x080
UrgencyHint: Final = 0x100
AllHints: Final = 511
WithdrawnState: Final = 0
NormalState: Final = 1
IconicState: Final = 3
DontCareState: Final = 0
ZoomState: Final = 2
InactiveState: Final = 4
RectangleOut: Final = 0
RectangleIn: Final = 1
RectanglePart: Final = 2
VisualNoMask: Final = 0x0
VisualIDMask: Final = 0x1
VisualScreenMask: Final = 0x2
VisualDepthMask: Final = 0x4
VisualClassMask: Final = 0x8
VisualRedMaskMask: Final = 0x10
VisualGreenMaskMask: Final = 0x20
VisualBlueMaskMask: Final = 0x40
VisualColormapSizeMask: Final = 0x80
VisualBitsPerRGBMask: Final = 0x100
VisualAllMask: Final = 0x1FF
ReleaseByFreeingColormap: Final = 1
BitmapSuccess: Final = 0
BitmapOpenFailed: Final = 1
BitmapFileInvalid: Final = 2
BitmapNoMemory: Final = 3
XCSUCCESS: Final = 0
XCNOMEM: Final = 1
XCNOENT: Final = 2
