from _typeshed import Incomplete
from collections.abc import Generator

WHEEL_NAME: Incomplete
NAMESPACE_PACKAGE_INIT: str

def unpack(src_dir, dst_dir) -> None: ...
def disable_info_traces() -> Generator[None]: ...

class Wheel:
    filename: Incomplete
    def __init__(self, filename) -> None: ...
    def tags(self): ...
    def is_compatible(self): ...
    def egg_name(self): ...
    def get_dist_info(self, zf): ...
    def install_as_egg(self, destination_eggdir) -> None: ...
