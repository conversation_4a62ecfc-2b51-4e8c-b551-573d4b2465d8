from oauthlib.common import Request
from oauthlib.oauth2.rfc6749.grant_types.base import GrantTypeBase
from oauthlib.oauth2.rfc6749.tokens import TokenBase

class DeviceCodeGrant(GrantTypeBase):
    def create_authorization_response(self, request: Request, token_handler: TokenBase) -> tuple[dict[str, str], str, int]: ...
    def validate_token_request(self, request: Request) -> None: ...
    def create_token_response(self, request: Request, token_handler: TokenBase) -> tuple[dict[str, str], str, int]: ...
