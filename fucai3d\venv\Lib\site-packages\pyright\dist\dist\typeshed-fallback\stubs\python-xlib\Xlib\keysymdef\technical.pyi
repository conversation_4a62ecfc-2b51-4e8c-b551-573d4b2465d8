from typing import Final

XK_leftradical: Final = 0x8A1
XK_topleftradical: Final = 0x8A2
XK_horizconnector: Final = 0x8A3
XK_topintegral: Final = 0x8A4
XK_botintegral: Final = 0x8A5
XK_vertconnector: Final = 0x8A6
XK_topleftsqbracket: Final = 0x8A7
XK_botleftsqbracket: Final = 0x8A8
XK_toprightsqbracket: Final = 0x8A9
XK_botrightsqbracket: Final = 0x8AA
XK_topleftparens: Final = 0x8AB
XK_botleftparens: Final = 0x8AC
XK_toprightparens: Final = 0x8AD
XK_botrightparens: Final = 0x8AE
XK_leftmiddlecurlybrace: Final = 0x8AF
XK_rightmiddlecurlybrace: Final = 0x8B0
XK_topleftsummation: Final = 0x8B1
XK_botleftsummation: Final = 0x8B2
XK_topvertsummationconnector: Final = 0x8B3
XK_botvertsummationconnector: Final = 0x8B4
XK_toprightsummation: Final = 0x8B5
XK_botrightsummation: Final = 0x8B6
XK_rightmiddlesummation: Final = 0x8B7
XK_lessthanequal: Final = 0x8BC
XK_notequal: Final = 0x8BD
XK_greaterthanequal: Final = 0x8BE
XK_integral: Final = 0x8BF
XK_therefore: Final = 0x8C0
XK_variation: Final = 0x8C1
XK_infinity: Final = 0x8C2
XK_nabla: Final = 0x8C5
XK_approximate: Final = 0x8C8
XK_similarequal: Final = 0x8C9
XK_ifonlyif: Final = 0x8CD
XK_implies: Final = 0x8CE
XK_identical: Final = 0x8CF
XK_radical: Final = 0x8D6
XK_includedin: Final = 0x8DA
XK_includes: Final = 0x8DB
XK_intersection: Final = 0x8DC
XK_union: Final = 0x8DD
XK_logicaland: Final = 0x8DE
XK_logicalor: Final = 0x8DF
XK_partialderivative: Final = 0x8EF
XK_function: Final = 0x8F6
XK_leftarrow: Final = 0x8FB
XK_uparrow: Final = 0x8FC
XK_rightarrow: Final = 0x8FD
XK_downarrow: Final = 0x8FE
