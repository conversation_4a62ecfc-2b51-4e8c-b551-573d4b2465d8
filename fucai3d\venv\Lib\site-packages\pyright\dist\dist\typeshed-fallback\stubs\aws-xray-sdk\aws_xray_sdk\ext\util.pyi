import re
from typing import Final, overload

from aws_xray_sdk.core.models.trace_header import TraceHeader

first_cap_re: Final[re.Pattern[str]]
all_cap_re: Final[re.Pattern[str]]
UNKNOWN_HOSTNAME: str = "UNKNOWN HOST"

def inject_trace_header(headers, entity) -> None: ...
def calculate_sampling_decision(trace_header, recorder, sampling_req): ...
def construct_xray_header(headers) -> TraceHeader: ...
def calculate_segment_name(host_name, recorder): ...
def prepare_response_header(origin_header, segment) -> str: ...
def to_snake_case(name: str) -> str: ...
def strip_url(url): ...
@overload
def get_hostname(url: str | None) -> str: ...
@overload
def get_hostname(url: bytes | bytearray | None) -> str | bytes: ...
def unwrap(obj: object, attr: str) -> None: ...
