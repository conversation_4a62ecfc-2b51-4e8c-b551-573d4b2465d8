import datetime
import zipimport
from _typeshed import Incomplete, SupportsItems
from collections.abc import Generator, Iterable
from os import Path<PERSON>ike
from types import TracebackType
from typing import AnyStr, Final, Literal, TypeVar, overload

from reportlab.lib.rltempfile import get_rl_tempdir as get_rl_tempdir, get_rl_tempfile as get_rl_tempfile

from .rl_safe_eval import (
    rl_extended_literal_eval as rl_extended_literal_eval,
    rl_safe_exec as rl_safe_exec,
    safer_globals as safer_globals,
)

_KT = TypeVar("_KT")
_VT = TypeVar("_VT")

__version__: Final[str]

class _UNSET_:
    @staticmethod
    def __bool__() -> Literal[False]: ...

__UNSET__: Final[_UNSET_]

isPyPy: bool

def isFunction(v: object) -> bool: ...
def isMethod(v: object, mt=...) -> bool: ...
def isModule(v: object) -> bool: ...
def isSeq(v: object, _st=...) -> bool: ...
def isNative(v: object) -> bool: ...

strTypes: tuple[type[str], type[bytes]]

def asBytes(v: str | bytes, enc: str = "utf8") -> bytes: ...
def asUnicode(v: str | bytes, enc: str = "utf8") -> str: ...
def asUnicodeEx(v: str | bytes, enc: str = "utf8") -> str: ...
def asNative(v: str | bytes, enc: str = "utf8") -> str: ...
def int2Byte(i: int) -> bytes: ...
def isStr(v: object) -> bool: ...
def isBytes(v: object) -> bool: ...
def isUnicode(v: object) -> bool: ...
def isClass(v: object) -> bool: ...
def isNonPrimitiveInstance(x: object) -> bool: ...
def instantiated(v: object) -> bool: ...
def bytestr(x: object, enc: str = "utf8") -> bytes: ...
def encode_label(args) -> str: ...
def decode_label(label: str): ...
def rawUnicode(s: str | bytes) -> str: ...
def rawBytes(s: str | bytes) -> bytes: ...

rl_exec = exec

def char2int(s: int | str | bytes) -> int: ...
def rl_reraise(t, v: BaseException, b: TracebackType | None = None) -> None: ...
def rl_add_builtins(**kwd) -> None: ...
def zipImported(ldr: zipimport.zipimporter | None = None) -> zipimport.zipimporter | None: ...

class CIDict(dict[_KT, _VT]):
    def __init__(self, *args, **kwds) -> None: ...
    def update(self, D: SupportsItems[_KT, _VT]) -> None: ...  # type:ignore[override]

def markfilename(filename, creatorcode=None, filetype=None): ...

__rl_loader__: Incomplete

def rl_glob(pattern: AnyStr, glob=...) -> list[AnyStr]: ...
def isFileSystemDistro() -> bool: ...
def isCompactDistro() -> bool: ...
def isSourceDistro() -> bool: ...
def normalize_path(p: PathLike[AnyStr]) -> PathLike[AnyStr]: ...
def recursiveImport(modulename, baseDir=None, noCWD: int = 0, debug: int = 0): ...

haveImages: Final[bool]

class ArgvDictValue:
    value: Incomplete
    func: Incomplete
    def __init__(self, value, func) -> None: ...

def getArgvDict(**kw): ...
def getHyphenater(hDict=None): ...
def open_for_read_by_name(name, mode: str = "b"): ...
def rlUrlRead(name): ...
def open_for_read(name, mode: str = "b"): ...
def open_and_read(name, mode: str = "b"): ...
def open_and_readlines(name, mode: str = "t"): ...
def rl_isfile(fn, os_path_isfile=...): ...
def rl_isdir(pn, os_path_isdir=..., os_path_normpath=...): ...
def rl_listdir(pn, os_path_isdir=..., os_path_normpath=..., os_listdir=...): ...
def rl_getmtime(pn, os_path_isfile=..., os_path_normpath=..., os_path_getmtime=..., time_mktime=...): ...
def __rl_get_module__(name, dir): ...
def rl_get_module(name, dir): ...

class ImageReader:
    fileName: Incomplete
    fp: Incomplete
    def __init__(self, fileName, ident=None) -> None: ...
    def identity(self) -> str: ...
    @classmethod
    def check_pil_image_size(cls, im) -> None: ...
    @classmethod
    def set_max_image_size(cls, max_image_size=None) -> None: ...
    def jpeg_fh(self) -> None: ...
    def getSize(self) -> tuple[int, int]: ...
    mode: Incomplete
    def getRGBData(self): ...
    def getImageData(self): ...
    def getTransparent(self): ...

class LazyImageReader(ImageReader): ...

def getImageData(imageFileName): ...

class DebugMemo:
    fn: Incomplete
    stdout: Incomplete
    store: Incomplete
    def __init__(
        self,
        fn: str = "rl_dbgmemo.dbg",
        mode: str = "w",
        getScript: int = 1,
        modules=(),
        capture_traceback: int = 1,
        stdout=None,
        **kw,
    ) -> None: ...
    def add(self, **kw) -> None: ...
    def dump(self) -> None: ...
    def dumps(self): ...
    def load(self) -> None: ...
    def loads(self, s) -> None: ...
    specials: Incomplete
    def show(self) -> None: ...
    def payload(self, name): ...
    def __setitem__(self, name, value) -> None: ...
    def __getitem__(self, name): ...

def flatten(L): ...
def find_locals(func, depth: int = 0): ...

class _FmtSelfDict:
    obj: Incomplete
    def __init__(self, obj, overrideArgs) -> None: ...
    def __getitem__(self, k): ...

class FmtSelfDict: ...

def simpleSplit(text: str | bytes, fontName: str | None, fontSize: float, maxWidth: float | None): ...
@overload
def escapeTextOnce(text: None) -> None: ...
@overload
def escapeTextOnce(text: str | bytes) -> str: ...
def fileName2FSEnc(fn): ...
def prev_this_next(items): ...
def commasplit(s: str | bytes) -> list[str]: ...
def commajoin(l: Iterable[str | bytes]) -> str: ...
def findInPaths(fn, paths, isfile: bool = True, fail: bool = False): ...
def annotateException(msg: str, enc: str = "utf8", postMsg: str = "", sep: str = " ") -> None: ...
def escapeOnce(data: str) -> str: ...

class IdentStr(str):
    def __new__(cls, value): ...

class RLString(str):
    def __new__(cls, v, **kwds): ...

def makeFileName(s): ...

class FixedOffsetTZ(datetime.tzinfo):
    def __init__(self, h, m, name) -> None: ...
    def utcoffset(self, dt): ...
    def tzname(self, dt): ...
    def dst(self, dt): ...

class TimeStamp:
    tzname: str
    t: Incomplete
    lt: Incomplete
    YMDhms: Incomplete
    dhh: Incomplete
    dmm: Incomplete
    def __init__(self, invariant=None) -> None: ...
    @property
    def datetime(self): ...
    @property
    def asctime(self): ...

def recursiveGetAttr(obj, name, g=None): ...
def recursiveSetAttr(obj, name, value) -> None: ...
def recursiveDelAttr(obj, name) -> None: ...
def yieldNoneSplits(L) -> Generator[Incomplete, None, None]: ...

class KlassStore:
    lim: int
    store: dict[str, type]
    def __init__(self, lim: int = 127) -> None: ...
    def add(self, k: str, v: type) -> None: ...
    def __contains__(self, k) -> bool: ...
    def __getitem__(self, k: str) -> type: ...
    def get(self, k, default=None): ...
