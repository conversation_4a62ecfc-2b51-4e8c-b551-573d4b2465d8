from _typeshed import ReadableBuffer
from collections.abc import Callable, Generator, Iterable, Sized
from html.parser import <PERSON>TMLP<PERSON><PERSON>
from re import Pattern
from typing import Literal, overload

def camel2under(camel_string: str) -> str: ...
def under2camel(under_string: str) -> str: ...
@overload
def slugify(text: str, delim: str = "_", lower: bool = True, *, ascii: Literal[True]) -> bytes: ...
@overload
def slugify(text: str, delim: str, lower: bool, ascii: Literal[True]) -> bytes: ...
@overload
def slugify(text: str, delim: str = "_", lower: bool = True, ascii: Literal[False] = False) -> str: ...
def split_punct_ws(text: str) -> list[str]: ...
def unit_len(sized_iterable: Sized, unit_noun: str = "item") -> str: ...
def ordinalize(number: int | str, ext_only: bool = False) -> str: ...
def cardinalize(unit_noun: str, count: int) -> str: ...
def singularize(word: str) -> str: ...
def pluralize(word: str) -> str: ...
def find_hashtags(string: str) -> list[str]: ...
def a10n(string: str) -> str: ...
def strip_ansi(text: str) -> str: ...
def asciify(text: str | bytes | bytearray, ignore: bool = False) -> bytes: ...
def is_ascii(text: str) -> bool: ...

class DeaccenterDict(dict[int, int]):
    def __missing__(self, key: int) -> int: ...

def bytes2human(nbytes: int, ndigits: int = 0) -> str: ...

class HTMLTextExtractor(HTMLParser):
    strict: bool
    convert_charrefs: bool
    result: list[str]
    def __init__(self) -> None: ...
    def handle_data(self, d: str) -> None: ...
    def handle_charref(self, number: str) -> None: ...
    def handle_entityref(self, name: str) -> None: ...
    def get_text(self) -> str: ...

def html2text(html: str) -> str: ...
def gunzip_bytes(bytestring: ReadableBuffer) -> bytes: ...
def gzip_bytes(bytestring: ReadableBuffer, level: int = 6) -> int: ...
def iter_splitlines(text: str) -> Generator[str, None, None]: ...
def indent(text: str, margin: str, newline: str = "\n", key: Callable[[str], bool] = ...) -> str: ...
def is_uuid(obj, version: int = 4) -> bool: ...
def escape_shell_args(args: Iterable[str], sep: str = " ", style: Literal["cmd", "sh"] | None = None) -> str: ...
def args2sh(args: Iterable[str], sep: str = " ") -> str: ...
def args2cmd(args: Iterable[str], sep: str = " ") -> str: ...
def parse_int_list(range_string: str, delim: str = ",", range_delim: str = "-") -> list[int]: ...
def format_int_list(int_list: list[int], delim: str = ",", range_delim: str = "-", delim_space: bool = False) -> str: ...
def complement_int_list(
    range_string: str, range_start: int = 0, range_end: int | None = None, delim: str = ",", range_delim: str = "-"
) -> str: ...
def int_ranges_from_int_list(range_string: str, delim: str = ",", range_delim: str = "-") -> tuple[int, int]: ...

class MultiReplace:
    group_map: dict[str, str]
    combined_pattern: Pattern[str]
    def __init__(self, sub_map: dict[str, str], **kwargs) -> None: ...
    def sub(self, text: str) -> str: ...

def multi_replace(text: str, sub_map: dict[str, str], **kwargs) -> str: ...
def unwrap_text(text: str, ending: str | None = "\n\n") -> str: ...
def removeprefix(text: str, prefix: str) -> str: ...

__all__ = [
    "camel2under",
    "under2camel",
    "slugify",
    "split_punct_ws",
    "unit_len",
    "ordinalize",
    "cardinalize",
    "pluralize",
    "singularize",
    "asciify",
    "is_ascii",
    "is_uuid",
    "html2text",
    "strip_ansi",
    "bytes2human",
    "find_hashtags",
    "a10n",
    "gzip_bytes",
    "gunzip_bytes",
    "iter_splitlines",
    "indent",
    "escape_shell_args",
    "args2cmd",
    "args2sh",
    "parse_int_list",
    "format_int_list",
    "complement_int_list",
    "int_ranges_from_int_list",
    "MultiReplace",
    "multi_replace",
    "unwrap_text",
    "removeprefix",
]
