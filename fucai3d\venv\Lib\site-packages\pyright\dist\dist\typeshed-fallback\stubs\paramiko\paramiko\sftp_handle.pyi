from _typeshed import ReadableBuffer

from paramiko.sftp_attr import SFTPAttributes
from paramiko.util import ClosingContextManager

class SFTPHandle(ClosingContextManager):
    def __init__(self, flags: int = 0) -> None: ...
    def close(self) -> None: ...
    def read(self, offset: int, length: int) -> bytes | int: ...
    def write(self, offset: int, data: ReadableBuffer) -> int: ...
    def stat(self) -> int | SFTPAttributes: ...
    def chattr(self, attr: SFTPAttributes) -> int: ...
