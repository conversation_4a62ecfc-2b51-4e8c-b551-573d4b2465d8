from _typeshed import Incomplete, ReadableBuffer
from collections.abc import Callable, Iterable
from typing import Literal, TypedDict, overload, type_check_only
from typing_extensions import deprecated

import _win32typing
from win32.lib.pywintypes import TimeType, error as error

@type_check_only
class _MonitorInfo(TypedDict):
    Monitor: tuple[int, int, int, int]
    Work: tuple[int, int, int, int]
    Flags: int
    Device: str

@type_check_only
class _FileVersionInfo(TypedDict):
    Signature: int
    StrucVersion: int
    FileVersionMS: int
    FileVersionLS: int
    ProductVersionMS: int
    ProductVersionLS: int
    FileFlagsMask: int
    FileFlags: int
    FileOS: int
    FileType: int
    FileSubtype: int
    FileDate: None | Incomplete

@type_check_only
class _PwrCapabilitiesBatteryScale(TypedDict):
    Granularity: int
    Capacity: int

@type_check_only
class _PwrCapabilities(TypedDict):
    PowerButtonPresent: bool
    SleepButtonPresent: bool
    LidPresent: bool
    SystemS1: bool
    SystemS2: bool
    SystemS3: bool
    SystemS4: bool
    SystemS5: bool
    HiberFilePresent: bool
    FullWake: bool
    VideoDimPresent: bool
    ApmPresent: bool
    UpsPresent: bool
    ThermalControl: bool
    ProcessorThrottle: bool
    ProcessorMinThrottle: int
    ProcessorMaxThrottle: int
    FastSystemS4: bool
    spare2: Incomplete | None
    DiskSpinDown: bool
    spare3: Incomplete | None
    SystemBatteriesPresent: bool
    BatteriesAreShortTerm: bool
    BatteryScale: tuple[_PwrCapabilitiesBatteryScale, ...]
    AcOnLineWake: int
    SoftLidWake: int
    RtcWake: int
    MinDeviceWakeState: int
    DefaultLowLatencyWake: int

def AbortSystemShutdown(computerName: str, /) -> None: ...
def InitiateSystemShutdown(computerName: str, message: str, timeOut, bForceClose, bRebootAfterShutdown, /) -> None: ...
def Apply(exceptionHandler, func, args, /): ...
def Beep(freq, dur, /) -> None: ...
def BeginUpdateResource(filename: str, delete, /) -> int: ...
def ChangeDisplaySettings(DevMode: _win32typing.PyDEVMODE, Flags, /): ...
def ChangeDisplaySettingsEx(DeviceName: str | None = ..., DevMode: _win32typing.PyDEVMODE | None = ..., Flags=...) -> int: ...
def ClipCursor(arg: tuple[Incomplete, Incomplete, Incomplete, Incomplete], /) -> None: ...
def CloseHandle(handle: int, /) -> None: ...
def CopyFile(src, dest: str, bFailOnExist: int = ..., /) -> None: ...
def DebugBreak() -> None: ...
def DeleteFile(fileName: str, /) -> None: ...
def DragQueryFile(hDrop, fileNum: int = ..., /) -> str: ...
def DragFinish(hDrop, /) -> None: ...
def DuplicateHandle(
    hSourceProcess: int, hSource: int, hTargetProcessHandle: int, desiredAccess: int, bInheritHandle: int, options: int, /
) -> int: ...
def EndUpdateResource(handle: int, discard, /) -> None: ...
def EnumDisplayDevices(Device: str | None = ..., DevNum: int = ..., Flags: int = ...) -> _win32typing.PyDISPLAY_DEVICE: ...
def EnumDisplayMonitors(
    hdc: int | None = ..., rcClip: _win32typing.PyRECT | None = ...
) -> list[tuple[_win32typing.PyHANDLE, _win32typing.PyHANDLE, tuple[int, int, int, int]]]: ...
def EnumDisplaySettings(DeviceName: str | None = ..., ModeNum: int = ...) -> _win32typing.PyDEVMODEW: ...
def EnumDisplaySettingsEx(DeviceName: str | None = ..., ModeNum=..., Flags=...) -> _win32typing.PyDEVMODEW: ...
def EnumResourceLanguages(
    hmodule: int, lpType: _win32typing.PyResourceId, lpName: _win32typing.PyResourceId, /
) -> list[Incomplete]: ...
def EnumResourceNames(hmodule: int, resType: _win32typing.PyResourceId, /) -> list[str]: ...
def EnumResourceTypes(hmodule: int, /) -> list[Incomplete]: ...
def ExpandEnvironmentStrings(_in: str, /) -> str: ...
def ExitWindows(reserved1: int = ..., reserved2: int = ..., /) -> None: ...
def ExitWindowsEx(flags, reserved: int = ..., /) -> None: ...
def FindFiles(fileSpec: str, /): ...
def FindFirstChangeNotification(pathName: str, bSubDirs, _filter, /): ...
def FindNextChangeNotification(handle: int, /) -> None: ...
def FindCloseChangeNotification(handle, /) -> None: ...
def FindExecutable(filename: str, _dir: str, /) -> tuple[Incomplete, str]: ...
def FormatMessage(
    flags: int, source: str | None = ..., messageId: int = ..., languageID: int = ..., inserts: Iterable[str] | None = ..., /
) -> str: ...
def FormatMessageW(
    flags: int, source: int | None = ..., messageId: int = ..., languageID: int = ..., inserts: Iterable[str] | None = ..., /
) -> str: ...
def FreeLibrary(hModule: int, /) -> None: ...
def GenerateConsoleCtrlEvent(controlEvent: int, processGroupId: int, /) -> None: ...
def GetAsyncKeyState(key: int, /) -> int: ...
def GetCommandLine() -> str: ...
def GetComputerName() -> str: ...
def GetComputerNameEx(NameType: int, /) -> str: ...
def GetComputerObjectName(NameFormat: int, /) -> str: ...
def GetMonitorInfo(hMonitor: int) -> _MonitorInfo: ...
def GetUserName() -> str: ...
def GetUserNameEx(NameFormat: int, /) -> str: ...
def GetCursorPos() -> tuple[int, int]: ...
def GetCurrentThread() -> int: ...
def GetCurrentThreadId() -> int: ...
def GetCurrentProcessId() -> int: ...
def GetCurrentProcess() -> int: ...
def GetConsoleTitle() -> str: ...
def GetDateFormat(locale: int, flags: int, time: TimeType | None, _format: str | None = None, /) -> str: ...
def GetDiskFreeSpace(rootPath: str | None = None, /) -> tuple[int, int, int, int]: ...
def GetDiskFreeSpaceEx(rootPath: str | None = None, /) -> tuple[int, int, int]: ...
def GetDllDirectory() -> str: ...
def GetDomainName() -> str: ...
def GetEnvironmentVariable(variable: str, /) -> str | None: ...
def GetEnvironmentVariableW(Name: str, /) -> str | None: ...
def GetFileAttributes(pathName: str, /) -> int: ...
def GetFileVersionInfo(Filename: str, SubBlock: str, /) -> _FileVersionInfo: ...
def GetFocus(): ...
def GetFullPathName(fileName: str, /) -> str: ...
def GetHandleInformation(Object: int, /): ...
def GetKeyboardLayout(threadId: int = ..., /) -> int: ...
def GetKeyboardLayoutName() -> str: ...
def GetKeyboardState() -> bytes: ...
def GetKeyState(key: int, /) -> int: ...
def GetLastError() -> int: ...
def GetLastInputInfo() -> int: ...
def GetLocalTime() -> tuple[int, int, int, int, int, int, int, int]: ...
def GetLongPathName(fileName: str, /) -> str: ...
def GetLongPathNameW(fileName: str, /) -> str: ...
def GetLogicalDrives() -> int: ...
def GetLogicalDriveStrings() -> str: ...
def GetModuleFileName(hModule: int | None, /) -> str: ...
def GetModuleFileNameW(hModule: int | None, /) -> str: ...
def GetModuleHandle(fileName: str | None = None, /) -> int: ...
def GetPwrCapabilities() -> _PwrCapabilities: ...
@deprecated("This function is obsolete, applications should use the registry instead.")
def GetProfileSection(section: str, iniName: str | None = ..., /) -> list[Incomplete]: ...
def GetProcAddress(hModule: int, functionName: _win32typing.PyResourceId, /): ...
@deprecated("This function is obsolete, applications should use the registry instead.")
def GetProfileVal(section: str, entry: str, defValue: str, iniName: str | None = ..., /) -> str: ...
def GetShortPathName(path: str, /) -> str: ...
def GetStdHandle(handle: int, /) -> int: ...
def GetSysColor(index: int, /) -> int: ...
def GetSystemDefaultLangID() -> int: ...
def GetSystemDefaultLCID() -> int: ...
def GetSystemDirectory() -> str: ...
def GetSystemFileCacheSize() -> tuple[int, int, int]: ...
def SetSystemFileCacheSize(MinimumFileCacheSize: int, MaximumFileCacheSize: int, Flags: int = ...) -> None: ...
def GetSystemInfo() -> tuple[int, int, int, int, int, int, int, int, tuple[int, int]]: ...
def GetNativeSystemInfo() -> tuple[int, int, int, int, int, int, int, int, tuple[int, int]]: ...
def GetSystemMetrics(index: int, /) -> int: ...
def GetSystemPowerStatus() -> dict[str, int]: ...
def GetSystemTime() -> tuple[int, int, int, int, int, int, int, int]: ...
def GetTempFileName(path: str, prefix: str, nUnique: int = ..., /) -> tuple[str, int]: ...
def GetTempPath() -> str: ...
def GetThreadLocale() -> int: ...
def GetTickCount() -> int: ...
def GetTimeFormat(locale: int, flags: int, time: TimeType | None, _format: str, /) -> str: ...
@overload
def GetTimeZoneInformation(
    times_as_tuples: Literal[True] = True, /
) -> tuple[
    int,
    tuple[int, str, tuple[int, int, int, int, int, int, int, int], int, str, tuple[int, int, int, int, int, int, int, int], int],
]: ...
@overload
def GetTimeZoneInformation(times_as_tuples: Literal[False], /): ...
def GetVersion() -> int: ...
def GetVersionEx(_format: int = ..., /) -> tuple[int, int, int, int, str]: ...
def GetVolumeInformation(path: str, /) -> tuple[str, int, int, int, str]: ...
def GetWindowsDirectory() -> str: ...
def GetWindowLong(hwnd: int | None, offset: int, /) -> int: ...
def GetUserDefaultLangID() -> int: ...
def GetUserDefaultLCID() -> int: ...
def GlobalMemoryStatus() -> dict[str, int]: ...
def GlobalMemoryStatusEx() -> dict[str, int]: ...
def keybd_event(bVk, bScan, dwFlags: int = ..., dwExtraInfo: int = ..., /) -> None: ...
def mouse_event(dx, dy, dwData, dwFlags: int = ..., dwExtraInfo=..., /) -> None: ...
def LoadCursor(hInstance: int, cursorid: _win32typing.PyResourceId, /) -> int: ...
def LoadKeyboardLayout(KLID: str, Flags: int = ..., /): ...
def LoadLibrary(fileName: str, /): ...
def LoadLibraryEx(fileName: str, handle: int, handle1, /) -> int: ...
def LoadResource(handle: int, _type: _win32typing.PyResourceId, name: _win32typing.PyResourceId, language, /) -> str: ...
def LoadString(handle: int, stringId, numChars: int = ..., /) -> str: ...
def MessageBeep(type: int = 0, /): ...
def MessageBox(hwnd: int | None, message: str, title: str | None = ..., style=..., language=..., /) -> int: ...
def MonitorFromPoint(pt: tuple[Incomplete, Incomplete], Flags: int = ...) -> int: ...
def MonitorFromRect(rc: _win32typing.PyRECT | tuple[int, int, int, int], Flags: int = ...) -> int: ...
def MonitorFromWindow(hwnd: int, Flags: int = ...) -> int: ...
def MoveFile(srcName: str, destName: str, /) -> None: ...
def MoveFileEx(srcName: str, destName: str, flag, /) -> None: ...
def OpenProcess(reqdAccess: int, bInherit: int | bool, pid: int, /) -> int: ...
def OutputDebugString(msg: str, /) -> None: ...
def PostMessage(hwnd: int, idMessage, wParam: Incomplete | None = ..., lParam: Incomplete | None = ..., /) -> None: ...
def PostQuitMessage(exitCode: int = ..., /) -> None: ...
def PostThreadMessage(tid, idMessage, wParam: Incomplete | None = ..., lParam: Incomplete | None = ..., /) -> None: ...
def RegCloseKey(key: _win32typing.PyHKEY, /) -> None: ...
def RegConnectRegistry(computerName: str, key, /): ...
def RegCopyTree(KeySrc: _win32typing.PyHKEY, SubKey: str, KeyDest: _win32typing.PyHKEY) -> None: ...
def RegCreateKey(key: _win32typing.PyHKEY | int, subKey: str, /) -> _win32typing.PyHKEY: ...
def RegCreateKeyEx(
    Key: _win32typing.PyHKEY,
    SubKey: str,
    samDesired,
    Class: str | None = ...,
    Options=...,
    SecurityAttributes: _win32typing.PySECURITY_ATTRIBUTES | None = ...,
    Transaction: int | None = ...,
) -> tuple[_win32typing.PyHKEY, Incomplete]: ...
def RegDeleteKey(key: _win32typing.PyHKEY, subKey: str, /) -> None: ...
def RegDeleteKeyEx(Key: _win32typing.PyHKEY, SubKey: str, samDesired: int = ..., Transaction: int | None = ...) -> None: ...
def RegDeleteTree(Key: _win32typing.PyHKEY, SubKey: str) -> None: ...
def RegDeleteValue(key: _win32typing.PyHKEY, value: str, /) -> None: ...
def RegEnumKey(key: _win32typing.PyHKEY, index, /) -> str: ...
def RegEnumKeyEx(Key: _win32typing.PyHKEY, /): ...
def RegEnumKeyExW(Key: _win32typing.PyHKEY, /): ...
def RegEnumValue(key: _win32typing.PyHKEY, index, /) -> tuple[str, Incomplete, Incomplete]: ...
def RegFlushKey(key: _win32typing.PyHKEY, /) -> None: ...
def RegGetKeySecurity(key: _win32typing.PyHKEY, security_info, /) -> _win32typing.PySECURITY_DESCRIPTOR: ...
def RegLoadKey(key: _win32typing.PyHKEY, subKey: str, filename: str, /) -> None: ...
def RegOpenCurrentUser(samDesired=..., /) -> _win32typing.PyHKEY: ...
def RegOpenKey(
    key: _win32typing.PyHKEY | int, subkey: str | None, reserved: bool = ..., sam: int = ..., /
) -> _win32typing.PyHKEY: ...
def RegOpenKeyEx(key: _win32typing.PyHKEY, subKey: str, sam: int, reserved: bool = ..., /) -> _win32typing.PyHKEY: ...
def RegOpenKeyTransacted(
    Key: _win32typing.PyHKEY, SubKey: str, samDesired, Transaction: int, Options: int = ...
) -> _win32typing.PyHKEY: ...
def RegOverridePredefKey(Key: _win32typing.PyHKEY, NewKey: _win32typing.PyHKEY) -> None: ...
def RegQueryValue(key: _win32typing.PyHKEY | int, subKey: str | None, /) -> str: ...
def RegQueryValueEx(key: _win32typing.PyHKEY | int, valueName: str | None, /) -> tuple[str, int]: ...
def RegQueryInfoKey(key: _win32typing.PyHKEY, /) -> tuple[Incomplete, Incomplete, Incomplete]: ...
def RegQueryInfoKeyW(Key: _win32typing.PyHKEY, /): ...
def RegRestoreKey(Key: _win32typing.PyHKEY, File: str, Flags: int = ...) -> None: ...
def RegSaveKey(key: _win32typing.PyHKEY, filename: str, sa: _win32typing.PySECURITY_ATTRIBUTES | None = ..., /) -> None: ...
def RegSaveKeyEx(
    Key: _win32typing.PyHKEY, File: str, SecurityAttributes: _win32typing.PySECURITY_ATTRIBUTES | None = ..., Flags=...
) -> None: ...
def RegSetKeySecurity(key: _win32typing.PyHKEY, security_info, sd: _win32typing.PySECURITY_DESCRIPTOR, /) -> None: ...
def RegSetValue(key: _win32typing.PyHKEY, subKey: str | None, _type, value: str, /) -> None: ...
def RegSetValueEx(key: _win32typing.PyHKEY, valueName: str, reserved, _type, value, /) -> None: ...
def RegUnLoadKey(key: _win32typing.PyHKEY, subKey: str, /) -> None: ...
def RegisterWindowMessage(msgString: str, /) -> None: ...
def RegNotifyChangeKeyValue(key: _win32typing.PyHKEY, bWatchSubTree, dwNotifyFilter, hKey: int, fAsynchronous, /) -> None: ...
def SearchPath(path: str, fileName: str, fileExt: str | None = ..., /): ...
def SendMessage(hwnd: int, idMessage, wParam: str | None = ..., lParam: str | None = ..., /) -> None: ...
def SetConsoleCtrlHandler(ctrlHandler: Callable[[int], bool], bAdd: bool, /) -> None: ...
def SetConsoleTitle(title: str, /) -> None: ...
def SetCursorPos(arg: tuple[Incomplete, Incomplete], /) -> None: ...
def SetDllDirectory(PathName: str, /) -> None: ...
def SetErrorMode(errorMode, /): ...
def SetFileAttributes(pathName: str, attrs, /): ...
def SetLastError(errVal: int, /): ...
def SetSysColors(Elements, RgbValues, /) -> None: ...
def SetLocalTime(SystemTime: TimeType, /) -> None: ...
def SetSystemTime(year, month, dayOfWeek, day, hour, minute, second, millseconds, /): ...
def SetClassLong(hwnd: int, offset, val, /): ...
@deprecated("This function is obsolete, use `win32api.SetClassLong` instead.")
def SetClassWord(hwnd: int, offset, val, /): ...
def SetCursor(hCursor: int, /) -> int: ...
def SetEnvironmentVariable(Name, Value, /) -> None: ...
def SetEnvironmentVariableW(Name, Value, /) -> None: ...
def SetHandleInformation(Object: int, Mask, Flags, /) -> None: ...
def SetStdHandle(handle, handle1: int, /) -> None: ...
def SetSystemPowerState(Suspend, Force, /) -> None: ...
def SetThreadLocale(lcid, /) -> None: ...
def SetTimeZoneInformation(tzi, /): ...
def SetWindowLong(hwnd: int | None, offset: int, value: float, /) -> int: ...
@deprecated("This function is obsolete, use `win32api.SetWindowLong` instead.")
def SetWindowWord(hwnd, offset: int, val: int) -> int: ...
def ShellExecute(hwnd: int, op: str, file: str, params: str, _dir: str, bShow, /): ...
def ShowCursor(show, /): ...
def Sleep(time, bAlterable: int = ..., /): ...
def TerminateProcess(handle: int, exitCode: int, /) -> None: ...
def ToAsciiEx(vk, scancode, keyboardstate, flags: int = ..., hlayout: Incomplete | None = ..., /): ...
def UpdateResource(
    handle: int,
    type: _win32typing.PyResourceId | int,
    name: _win32typing.PyResourceId | int,
    data: ReadableBuffer | None,
    language: int = ...,
    /,
) -> None: ...
def VkKeyScan(char: str | bytes, /): ...
def WinExec(cmdLine: str, arg, /) -> None: ...
def WinHelp(hwnd: int, hlpFile: str, cmd, data: str | int = ..., /) -> None: ...
@deprecated("This function is obsolete, applications should use the registry instead.")
def WriteProfileSection(section: str, data: str, iniName: str | None = ..., /): ...
@deprecated("This function is obsolete, applications should use the registry instead.")
def WriteProfileVal(section: str, entry: str, value: str, iniName: str | None = ..., /) -> None: ...
def HIBYTE(val: int, /) -> int: ...
def LOBYTE(val: int, /) -> int: ...
def HIWORD(val: int, /) -> int: ...
def LOWORD(val: int, /) -> int: ...
def RGB(red: int, green: int, blue: int, /) -> int: ...
def MAKELANGID(PrimaryLanguage, SubLanguage, /): ...
def MAKEWORD(low, high, /): ...
def MAKELONG(low, high, /): ...
def CommandLineToArgv(*args): ...  # incomplete
def GetKeyboardLayoutList() -> tuple[int, int]: ...
def MapVirtualKey(*args): ...  # incomplete
def MessageBoxEx(*args): ...  # incomplete
def OpenThread(*args): ...  # incomplete
def SleepEx(*args): ...  # incomplete
def VkKeyScanEx(*args): ...  # incomplete

NameCanonical: int
NameCanonicalEx: int
NameDisplay: int
NameFullyQualifiedDN: int
NameSamCompatible: int
NameServicePrincipal: int
NameUniqueId: int
NameUnknown: int
NameUserPrincipal: int
PyDISPLAY_DEVICEType = _win32typing.PyDISPLAY_DEVICE
REG_NOTIFY_CHANGE_ATTRIBUTES: int
REG_NOTIFY_CHANGE_LAST_SET: int
REG_NOTIFY_CHANGE_NAME: int
REG_NOTIFY_CHANGE_SECURITY: int
STD_ERROR_HANDLE: int
STD_INPUT_HANDLE: int
STD_OUTPUT_HANDLE: int
VFT_APP: int
VFT_DLL: int
VFT_DRV: int
VFT_FONT: int
VFT_STATIC_LIB: int
VFT_UNKNOWN: int
VFT_VXD: int
VOS_DOS: int
VOS_DOS_WINDOWS16: int
VOS_DOS_WINDOWS32: int
VOS_NT: int
VOS_NT_WINDOWS32: int
VOS_OS216: int
VOS_OS216_PM16: int
VOS_OS232: int
VOS_OS232_PM32: int
VOS_UNKNOWN: int
VOS__PM16: int
VOS__PM32: int
VOS__WINDOWS16: int
VOS__WINDOWS32: int
VS_FF_DEBUG: int
VS_FF_INFOINFERRED: int
VS_FF_PATCHED: int
VS_FF_PRERELEASE: int
VS_FF_PRIVATEBUILD: int
VS_FF_SPECIALBUILD: int
