from antlr4.Token import Token as Token

class IntervalSet:
    intervals: list[range] | None
    readonly: bool
    def __init__(self) -> None: ...
    def __iter__(self): ...
    def __getitem__(self, item): ...
    def addOne(self, v: int): ...
    def addRange(self, v: range): ...
    def addSet(self, other: IntervalSet): ...
    def reduce(self, k: int): ...
    def complement(self, start: int, stop: int): ...
    def __contains__(self, item) -> bool: ...
    def __len__(self) -> int: ...
    def removeRange(self, v) -> None: ...
    def removeOne(self, v) -> None: ...
    def toString(self, literalNames: list[str], symbolicNames: list[str]): ...
    def elementName(self, literalNames: list[str], symbolicNames: list[str], a: int): ...
