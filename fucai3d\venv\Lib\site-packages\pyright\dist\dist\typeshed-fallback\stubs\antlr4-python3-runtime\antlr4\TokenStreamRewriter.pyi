from _typeshed import Incomplete

from antlr4.CommonTokenStream import CommonTokenStream as CommonTokenStream
from antlr4.Token import Token as Token

class TokenStreamRewriter:
    DEFAULT_PROGRAM_NAME: str
    PROGRAM_INIT_SIZE: int
    MIN_TOKEN_INDEX: int
    tokens: Incomplete
    programs: Incomplete
    lastRewriteTokenIndexes: Incomplete
    def __init__(self, tokens) -> None: ...
    def getTokenStream(self): ...
    def rollback(self, instruction_index, program_name) -> None: ...
    def deleteProgram(self, program_name="default") -> None: ...
    def insertAfterToken(self, token, text, program_name="default") -> None: ...
    def insertAfter(self, index, text, program_name="default") -> None: ...
    def insertBeforeIndex(self, index, text) -> None: ...
    def insertBeforeToken(self, token, text, program_name="default") -> None: ...
    def insertBefore(self, program_name, index, text) -> None: ...
    def replaceIndex(self, index, text) -> None: ...
    def replaceRange(self, from_idx, to_idx, text) -> None: ...
    def replaceSingleToken(self, token, text) -> None: ...
    def replaceRangeTokens(self, from_token, to_token, text, program_name="default") -> None: ...
    def replace(self, program_name, from_idx, to_idx, text) -> None: ...
    def deleteToken(self, token) -> None: ...
    def deleteIndex(self, index) -> None: ...
    def delete(self, program_name, from_idx, to_idx) -> None: ...
    def lastRewriteTokenIndex(self, program_name="default"): ...
    def setLastRewriteTokenIndex(self, program_name, i) -> None: ...
    def getProgram(self, program_name): ...
    def getDefaultText(self): ...
    def getText(self, program_name, start: int, stop: int): ...

    class RewriteOperation:
        tokens: Incomplete
        index: Incomplete
        text: Incomplete
        instructionIndex: int
        def __init__(self, tokens, index, text: str = "") -> None: ...
        def execute(self, buf): ...

    class InsertBeforeOp(RewriteOperation):
        def __init__(self, tokens, index, text: str = "") -> None: ...
        def execute(self, buf): ...

    class InsertAfterOp(InsertBeforeOp): ...

    class ReplaceOp(RewriteOperation):
        last_index: Incomplete
        def __init__(self, from_idx, to_idx, tokens, text) -> None: ...
        def execute(self, buf): ...
