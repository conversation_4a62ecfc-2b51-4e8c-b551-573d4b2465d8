import functools
from _typeshed import Incomplete
from functools import total_ordering as total_ordering

NO_DEFAULT: Incomplete

def inspect_formatargspec(
    args,
    varargs=None,
    varkw=None,
    defaults=None,
    kwonlyargs=(),
    kwonlydefaults={},
    annotations={},
    formatarg=...,
    formatvarargs=...,
    formatvarkw=...,
    formatvalue=...,
    formatreturns=...,
    formatannotation=...,
): ...
def get_module_callables(mod, ignore=None): ...
def mro_items(type_obj): ...
def dir_dict(obj, raise_exc: bool = False): ...
def copy_function(orig, copy_dict: bool = True): ...
def partial_ordering(cls): ...

class InstancePartial(functools.partial[Incomplete]):
    def __get__(self, obj, obj_type): ...

class CachedInstancePartial(functools.partial[Incomplete]):
    __name__: Incomplete
    def __set_name__(self, obj_type, name) -> None: ...
    __doc__: Incomplete
    __module__: Incomplete
    def __get__(self, obj, obj_type): ...

partial = CachedInstancePartial

def format_invocation(name: str = "", args=(), kwargs=None, **kw): ...
def format_exp_repr(obj, pos_names, req_names=None, opt_names=None, opt_key=None): ...
def format_nonexp_repr(obj, req_names=None, opt_names=None, opt_key=None): ...
def wraps(func, injected=None, expected=None, **kw): ...
def update_wrapper(wrapper, func, injected=None, expected=None, build_from=None, **kw): ...

class FunctionBuilder:
    name: Incomplete
    def __init__(self, name, **kw) -> None: ...
    def get_sig_str(self, with_annotations: bool = True): ...
    def get_invocation_str(self): ...
    @classmethod
    def from_func(cls, func): ...
    def get_func(self, execdict=None, add_source: bool = True, with_dict: bool = True): ...
    def get_defaults_dict(self): ...
    def get_arg_names(self, only_required: bool = False): ...
    defaults: Incomplete
    def add_arg(self, arg_name, default=..., kwonly: bool = False) -> None: ...
    def remove_arg(self, arg_name) -> None: ...

class MissingArgument(ValueError): ...
class ExistingArgument(ValueError): ...

def noop(*args, **kwargs) -> None: ...
