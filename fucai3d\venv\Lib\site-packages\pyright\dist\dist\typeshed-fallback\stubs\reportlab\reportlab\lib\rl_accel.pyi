from typing import Literal

def fp_str(*a): ...
def unicode2T1(utext, fonts): ...
def instanceStringWidthT1(self, text: str, size: float, encoding: str = "utf8") -> float: ...
def instanceStringWidthTTF(self, text: str, size: float, encoding: str = "utf8") -> float: ...
def hex32(i) -> str: ...
def add32(x: int, y: int) -> int: ...
def calcChecksum(data: str | bytes) -> int: ...
def escapePDF(s): ...
def asciiBase85Encode(input: str) -> str: ...
def asciiBase85Decode(input): ...
def sameFrag(f, g) -> bool | Literal[0]: ...

__all__ = [
    "fp_str",
    "unicode2T1",
    "instanceStringWidthT1",
    "instanceStringWidthTTF",
    "asciiBase85Encode",
    "asciiBase85Decode",
    "escapePDF",
    "sameFrag",
    "calcChecksum",
    "add32",
    "hex32",
]
