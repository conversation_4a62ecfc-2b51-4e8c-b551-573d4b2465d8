import _win32typing

def CoInternetCreateSecurityManager(reserved, /) -> _win32typing.PyIInternetSecurityManager: ...
def CoInternetIsFeatureEnabled(flags, /): ...
def CoInternetSetFeatureEnabled(flags, enable, /): ...

FEATURE_ADDON_MANAGEMENT: int
FEATURE_BEHAVIORS: int
FEATURE_DISABLE_MK_PROTOCOL: int
FEATURE_ENTRY_COUNT: int
FEATURE_GET_URL_DOM_FILEPATH_UNENCODED: int
FEATURE_HTTP_USERNAME_PASSWORD_DISABLE: int
FEATURE_LOCALMACHINE_LOCKDOWN: int
FEATURE_MIME_HANDLING: int
FEATURE_MIME_SNIFFING: int
FEATURE_OBJECT_CACHING: int
FEATURE_PROTOCOL_LOCKDOWN: int
FEATURE_RESTRICT_ACTIVEXINSTALL: int
FEATURE_RESTRICT_FILEDOWNLOAD: int
FEATURE_SAFE_BINDTOOBJECT: int
FEATURE_SECURITYBAND: int
FEATURE_UNC_SAVEDFILECHECK: int
FEATURE_VALIDATE_NAVIGATE_URL: int
FEATURE_WEBOC_POPUPMANAGEMENT: int
FEATURE_WINDOW_RESTRICTIONS: int
FEATURE_ZONE_ELEVATION: int
GET_FEATURE_FROM_PROCESS: int
GET_FEATURE_FROM_REGISTRY: int
GET_FEATURE_FROM_THREAD: int
GET_FEATURE_FROM_THREAD_INTERNET: int
GET_FEATURE_FROM_THREAD_INTRANET: int
GET_FEATURE_FROM_THREAD_LOCALMACHINE: int
GET_FEATURE_FROM_THREAD_RESTRICTED: int
GET_FEATURE_FROM_THREAD_TRUSTED: int
IID_IDocHostUIHandler: _win32typing.PyIID
IID_IHTMLOMWindowServices: _win32typing.PyIID
IID_IInternetBindInfo: _win32typing.PyIID
IID_IInternetPriority: _win32typing.PyIID
IID_IInternetProtocol: _win32typing.PyIID
IID_IInternetProtocolInfo: _win32typing.PyIID
IID_IInternetProtocolRoot: _win32typing.PyIID
IID_IInternetProtocolSink: _win32typing.PyIID
IID_IInternetSecurityManager: _win32typing.PyIID
SET_FEATURE_IN_REGISTRY: int
SET_FEATURE_ON_PROCESS: int
SET_FEATURE_ON_THREAD: int
SET_FEATURE_ON_THREAD_INTERNET: int
SET_FEATURE_ON_THREAD_INTRANET: int
SET_FEATURE_ON_THREAD_LOCALMACHINE: int
SET_FEATURE_ON_THREAD_RESTRICTED: int
SET_FEATURE_ON_THREAD_TRUSTED: int
