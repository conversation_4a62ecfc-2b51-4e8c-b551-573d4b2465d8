from email.message import Message
from typing import Any, Final, overload
from urllib.error import HTTPError as _HTTPError

class HTTPError(Exception):
    status_code: int
    reason: str
    body: bytes
    headers: Message
    @overload
    def __init__(self, status_code: int, reason: str, body: bytes, headers: Message, /) -> None: ...
    @overload
    def __init__(self, http_error: _HTTPError, /) -> None: ...
    def __reduce__(self) -> tuple[type[HTTPError], tuple[int, str, bytes, Message]]: ...
    @property
    def to_dict(self) -> dict[str, Any]: ...  # dict of response error from the API

class BadRequestsError(HTTPError): ...
class UnauthorizedError(HTTPError): ...
class ForbiddenError(HTTPError): ...
class NotFoundError(HTTPError): ...
class MethodNotAllowedError(HTTPError): ...
class PayloadTooLargeError(HTTPError): ...
class UnsupportedMediaTypeError(HTTPError): ...
class TooManyRequestsError(HTTPError): ...
class InternalServerError(HTTPError): ...
class ServiceUnavailableError(HTTPError): ...
class GatewayTimeoutError(HTTPError): ...

err_dict: Final[dict[int, type[HTTPError]]]

def handle_error(error: _HTTPError) -> HTTPError: ...
