from _typeshed import Incomplete

PSGUID_STORAGE: Incomplete
PSGUID_SUMMARYINFORMATION: Incomplete
PSGUID_HTMLINFORMATION: Incomplete
PSGUID_HTML2_INFORMATION: Incomplete
IFILTER_INIT_CANON_PARAGRAPHS: int
IFILTER_INIT_HARD_LINE_BREAKS: int
IFILTER_INIT_CANON_HYPHENS: int
IFILTER_INIT_CANON_SPACES: int
IFILTER_INIT_APPLY_INDEX_ATTRIBUTES: int
IFILTER_INIT_APPLY_CRAWL_ATTRIBUTES: int
IFILTER_INIT_APPLY_OTHER_ATTRIBUTES: int
IFILTER_INIT_INDEXING_ONLY: int
IFILTER_INIT_SEARCH_LINKS: int
IFILTER_INIT_FILTER_OWNED_VALUE_OK: int
IFILTER_FLAGS_OLE_PROPERTIES: int
CHUNK_TEXT: int
CHUNK_VALUE: int
CHUNK_NO_BREAK: int
CHUNK_EOW: int
CHUNK_EOS: int
CHUNK_EOP: int
CHUNK_EOC: int
NOT_AN_ERROR: int
FILTER_E_END_OF_CHUNKS: int
FILTER_E_NO_MORE_TEXT: int
FILTER_E_NO_MORE_VALUES: int
FILTER_E_ACCESS: int
FILTER_W_MONIKER_CLIPPED: int
FILTER_E_NO_TEXT: int
FILTER_E_NO_VALUES: int
FILTER_E_EMBEDDING_UNAVAILABLE: int
FILTER_E_LINK_UNAVAILABLE: int
FILTER_S_LAST_TEXT: int
FILTER_S_LAST_VALUES: int
FILTER_E_PASSWORD: int
FILTER_E_UNKNOWNFORMAT: int
PROPSETFLAG_DEFAULT: int
PROPSETFLAG_NONSIMPLE: int
PROPSETFLAG_ANSI: int
PROPSETFLAG_UNBUFFERED: int
PROPSETFLAG_CASE_SENSITIVE: int
PROPSET_BEHAVIOR_CASE_SENSITIVE: int
PID_DICTIONARY: int
PID_CODEPAGE: int
PID_FIRST_USABLE: int
PID_FIRST_NAME_DEFAULT: int
PID_LOCALE: int
PID_MODIFY_TIME: int
PID_SECURITY: int
PID_BEHAVIOR: int
PID_ILLEGAL: int
PID_MIN_READONLY: int
PID_MAX_READONLY: int
PIDDI_THUMBNAIL: int
PIDSI_TITLE: int
PIDSI_SUBJECT: int
PIDSI_AUTHOR: int
PIDSI_KEYWORDS: int
PIDSI_COMMENTS: int
PIDSI_TEMPLATE: int
PIDSI_LASTAUTHOR: int
PIDSI_REVNUMBER: int
PIDSI_EDITTIME: int
PIDSI_LASTPRINTED: int
PIDSI_CREATE_DTM: int
PIDSI_LASTSAVE_DTM: int
PIDSI_PAGECOUNT: int
PIDSI_WORDCOUNT: int
PIDSI_CHARCOUNT: int
PIDSI_THUMBNAIL: int
PIDSI_APPNAME: int
PIDSI_DOC_SECURITY: int
PIDDSI_CATEGORY: int
PIDDSI_PRESFORMAT: int
PIDDSI_BYTECOUNT: int
PIDDSI_LINECOUNT: int
PIDDSI_PARCOUNT: int
PIDDSI_SLIDECOUNT: int
PIDDSI_NOTECOUNT: int
PIDDSI_HIDDENCOUNT: int
PIDDSI_MMCLIPCOUNT: int
PIDDSI_SCALE: int
PIDDSI_HEADINGPAIR: int
PIDDSI_DOCPARTS: int
PIDDSI_MANAGER: int
PIDDSI_COMPANY: int
PIDDSI_LINKSDIRTY: int
PIDMSI_EDITOR: int
PIDMSI_SUPPLIER: int
PIDMSI_SOURCE: int
PIDMSI_SEQUENCE_NO: int
PIDMSI_PROJECT: int
PIDMSI_STATUS: int
PIDMSI_OWNER: int
PIDMSI_RATING: int
PIDMSI_PRODUCTION: int
PIDMSI_COPYRIGHT: int
PRSPEC_INVALID: int
PRSPEC_LPWSTR: int
PRSPEC_PROPID: int
CCH_MAX_PROPSTG_NAME: int
