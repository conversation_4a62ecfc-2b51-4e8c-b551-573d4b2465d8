import ast
from _typeshed import Incomplete

class UnaryOp(ast.UnaryOp):
    parent: ast.Expr
    def __init__(self, orig: ast.UnaryOp) -> None: ...

class Call(ast.Call):
    parent: ast.Expr
    def __init__(self, orig: ast.Call) -> None: ...

class If(ast.If):
    parent: ast.Expr
    def __init__(self, orig: ast.If) -> None: ...

class For(ast.For):
    parent: ast.AST
    previous_sibling: Incomplete
    def __init__(self, orig: ast.For) -> None: ...

class Assign(ast.Assign):
    parent: ast.AST
    previous_sibling: Incomplete
    def __init__(self, orig: ast.Assign) -> None: ...

def to_source(node: ast.expr | ast.Expr | ast.withitem | ast.slice | ast.Assign | None) -> str: ...
def strip_parenthesis(string: str) -> str: ...
def strip_triple_quotes(string: str) -> str: ...
def use_double_quotes(string: str) -> str: ...
def is_body_same(body1: list[ast.stmt], body2: list[ast.stmt]) -> bool: ...
def is_stmt_equal(a: ast.stmt, b: ast.stmt) -> bool: ...
def get_if_body_pairs(node: ast.If) -> list[tuple[ast.expr, list[ast.stmt]]]: ...
def is_constant_increase(expr: ast.AugAssign) -> bool: ...
def is_exception_check(node: ast.If) -> bool: ...
def is_same_expression(a: ast.expr, b: ast.expr) -> bool: ...
def expression_uses_variable(expr: ast.expr, var: str) -> bool: ...
def body_contains_continue(stmts: list[ast.stmt]) -> bool: ...
