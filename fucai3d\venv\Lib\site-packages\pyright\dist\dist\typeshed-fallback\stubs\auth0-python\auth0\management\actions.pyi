from _typeshed import Incomplete

from ..rest import RestClientOptions
from ..types import TimeoutType

class Actions:
    domain: Incomplete
    protocol: Incomplete
    client: Incomplete
    def __init__(
        self,
        domain: str,
        token: str,
        telemetry: bool = True,
        timeout: TimeoutType = 5.0,
        protocol: str = "https",
        rest_options: RestClientOptions | None = None,
    ) -> None: ...
    def get_actions(
        self,
        trigger_id: str | None = None,
        action_name: str | None = None,
        deployed: bool | None = None,
        installed: bool = False,
        page: int | None = None,
        per_page: int | None = None,
    ): ...
    async def get_actions_async(
        self,
        trigger_id: str | None = None,
        action_name: str | None = None,
        deployed: bool | None = None,
        installed: bool = False,
        page: int | None = None,
        per_page: int | None = None,
    ): ...
    def create_action(self, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    async def create_action_async(self, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    def update_action(self, id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    async def update_action_async(self, id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    def get_action(self, id: str) -> dict[str, Incomplete]: ...
    async def get_action_async(self, id: str) -> dict[str, Incomplete]: ...
    def delete_action(self, id: str, force: bool = False): ...
    async def delete_action_async(self, id: str, force: bool = False): ...
    def get_triggers(self) -> dict[str, Incomplete]: ...
    async def get_triggers_async(self) -> dict[str, Incomplete]: ...
    def get_execution(self, id: str) -> dict[str, Incomplete]: ...
    async def get_execution_async(self, id: str) -> dict[str, Incomplete]: ...
    def get_action_versions(self, id: str, page: int | None = None, per_page: int | None = None) -> dict[str, Incomplete]: ...
    async def get_action_versions_async(
        self, id: str, page: int | None = None, per_page: int | None = None
    ) -> dict[str, Incomplete]: ...
    def get_trigger_bindings(self, id: str, page: int | None = None, per_page: int | None = None) -> dict[str, Incomplete]: ...
    async def get_trigger_bindings_async(
        self, id: str, page: int | None = None, per_page: int | None = None
    ) -> dict[str, Incomplete]: ...
    def get_action_version(self, action_id: str, version_id: str) -> dict[str, Incomplete]: ...
    async def get_action_version_async(self, action_id: str, version_id: str) -> dict[str, Incomplete]: ...
    def deploy_action(self, id: str) -> dict[str, Incomplete]: ...
    async def deploy_action_async(self, id: str) -> dict[str, Incomplete]: ...
    def rollback_action_version(self, action_id: str, version_id: str) -> dict[str, Incomplete]: ...
    async def rollback_action_version_async(self, action_id: str, version_id: str) -> dict[str, Incomplete]: ...
    def update_trigger_bindings(self, id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    async def update_trigger_bindings_async(self, id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
