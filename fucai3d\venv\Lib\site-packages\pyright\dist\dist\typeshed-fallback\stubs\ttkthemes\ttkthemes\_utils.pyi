from _typeshed import FileDescriptorOrPath, StrOrBytesPath
from contextlib import AbstractContextManager

def temporary_chdir(new_dir: FileDescriptorOrPath) -> AbstractContextManager[None]: ...
def get_file_directory() -> str: ...
def get_temp_directory() -> str: ...
def get_themes_directory(theme_name: str | None = None, png: bool = False) -> str: ...
def create_directory(directory: StrOrBytesPath) -> StrOrBytesPath: ...
