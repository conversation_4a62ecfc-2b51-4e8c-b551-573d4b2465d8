class ClientMixin:
    def get_default_redirect_uri(self) -> None: ...
    def get_client_secret(self) -> None: ...
    def get_rsa_public_key(self) -> None: ...

class TokenCredentialMixin:
    def get_oauth_token(self) -> None: ...
    def get_oauth_token_secret(self) -> None: ...

class TemporaryCredentialMixin(TokenCredentialMixin):
    def get_client_id(self) -> None: ...
    def get_redirect_uri(self) -> None: ...
    def check_verifier(self, verifier) -> None: ...

class TemporaryCredential(dict[str, object], TemporaryCredentialMixin):
    def get_client_id(self): ...
    def get_user_id(self): ...
    def get_redirect_uri(self): ...
    def check_verifier(self, verifier): ...
    def get_oauth_token(self): ...
    def get_oauth_token_secret(self): ...
