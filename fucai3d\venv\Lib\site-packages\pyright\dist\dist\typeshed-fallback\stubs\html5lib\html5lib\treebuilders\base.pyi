from _typeshed import Incomplete

Marker: Incomplete
listElementsMap: dict[str | None, tuple[frozenset[tuple[str, str]], bool]]

class Node:
    name: str
    parent: Incomplete
    value: Incomplete
    attributes: Incomplete
    childNodes: Incomplete
    def __init__(self, name: str) -> None: ...
    def appendChild(self, node) -> None: ...
    def insertText(self, data, insertBefore=None) -> None: ...
    def insertBefore(self, node, refNode) -> None: ...
    def removeChild(self, node) -> None: ...
    def reparentChildren(self, newParent) -> None: ...
    def cloneNode(self) -> None: ...
    def hasContent(self) -> None: ...

class ActiveFormattingElements(list[Incomplete]):
    def append(self, node) -> None: ...
    def nodesEqual(self, node1, node2) -> bool: ...

class TreeBuilder:
    documentClass: Incomplete
    elementClass: Incomplete
    commentClass: Incomplete
    doctypeClass: Incomplete
    fragmentClass: Incomplete
    defaultNamespace: str | None
    def __init__(self, namespaceHTMLElements: bool | None) -> None: ...
    openElements: Incomplete
    activeFormattingElements: Incomplete
    headPointer: Incomplete
    formPointer: Incomplete
    insertFromTable: bool
    document: Incomplete
    def reset(self) -> None: ...
    def elementInScope(self, target, variant=None): ...
    def reconstructActiveFormattingElements(self) -> None: ...
    def clearActiveFormattingElements(self) -> None: ...
    def elementInActiveFormattingElements(self, name): ...
    def insertRoot(self, token) -> None: ...
    def insertDoctype(self, token) -> None: ...
    def insertComment(self, token, parent=None) -> None: ...
    def createElement(self, token): ...
    def insertElementNormal(self, token): ...
    def insertElementTable(self, token): ...
    def insertText(self, data, parent=None) -> None: ...
    def getTableMisnestedNodePosition(self): ...
    def generateImpliedEndTags(self, exclude=None) -> None: ...
    def getDocument(self): ...
    def getFragment(self): ...
    def testSerializer(self, node): ...
