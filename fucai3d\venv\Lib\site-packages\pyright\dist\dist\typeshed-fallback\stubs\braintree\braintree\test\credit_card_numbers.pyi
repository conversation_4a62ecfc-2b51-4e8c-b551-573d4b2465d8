from typing import Final

class CreditCardNumbers:
    class CardTypeIndicators:
        Business: Final = "****************"
        Commercial: Final = "****************"
        Consumer: Final = "****************"
        Corporate: Final = "****************"
        DurbinRegulated: Final = "****************"
        Debit: Final = "****************"
        Healthcare: Final = "****************"
        Payroll: Final = "****************"
        Prepaid: Final = "****************"
        PrepaidReloadable: Final = "****************"
        Purchase: Final = "****************"
        IssuingBank: Final = "****************"
        CountryOfIssuance: Final = "****************"
        No: Final = "****************"
        Unknown: Final = "****************"

    Maestro: Final = "****************"
    MasterCard: Final = "****************"
    MasterCardInternational: Final = "****************"
    Visa: Final = "****************"
    VisaInternational: Final = "****************"
    VisaPrepaid: Final = "****************"
    Discover: Final = "****************"
    Elo: Final = "****************"
    Hiper: Final = "****************"
    Hipercard: Final = "****************"
    Amex: Final = "***************"

    class FailsSandboxVerification:
        AmEx: Final = "***************"
        Discover: Final = "****************"
        MasterCard: Final = "****************"
        Visa: Final = "****************"

    class AmexPayWithPoints:
        Success: Final = "***************"
        IneligibleCard: Final = "***************"
        InsufficientPoints: Final = "***************"

    class Disputes:
        Chargeback: Final = "****************"
