from _typeshed import Incomplete
from typing import Final

__version__: Final[str]

def replace(src, sep, rep): ...

keywordsList: Incomplete
commentPat: str
pat: str
quotePat: Incomplete
tripleQuotePat: Incomplete
nonKeyPat: str
keyPat: Incomplete
matchPat: Incomplete
matchRE: Incomplete
idKeyPat: str
idRE: Incomplete

def fontify(pytext, searchfrom: int = 0, searchto=None): ...
def test(path) -> None: ...
