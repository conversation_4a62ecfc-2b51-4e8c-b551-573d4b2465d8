from _typeshed import Incomplete

WM_USER: int
DROPEFFECT_NONE: int
DROPEFFECT_COPY: int
DROPEFFECT_MOVE: int
DROPEFFECT_LINK: int
DROPEFFECT_SCROLL: int
FO_MOVE: int
FO_COPY: int
FO_DELETE: int
FO_RENAME: int
FOF_MULTIDESTFILES: int
FOF_CONFIRMMOUSE: int
FOF_SILENT: int
FOF_RENAMEONCOLLISION: int
FOF_NOCONFIRMATION: int
FOF_WANTMAPPINGHANDLE: int
FOF_ALLOWUNDO: int
FOF_FILESONLY: int
FOF_SIMPLEPROGRESS: int
FOF_NOCONFIRMMKDIR: int
FOF_NOERRORUI: int
FOF_NOCOPYSECURITYATTRIBS: int
FOF_NORECURSION: int
FOF_NO_CONNECTED_ELEMENTS: int
FOF_WANTNUKEWARNING: int
FOF_NORECURSEREPARSE: int
FOF_NO_UI: Incomplete
FOFX_NOSKIPJUNCTIONS: int
FOFX_PREFERHARDLINK: int
FOFX_SHOWELEVATIONPROMPT: int
FOFX_EARLYFAILURE: int
FOFX_PRESERVEFILEEXTENSIONS: int
FOFX_KEEPNEWERFILE: int
FOFX_NOCOPYHOOKS: int
FOFX_NOMINIMIZEBOX: int
FOFX_MOVEACLSACROSSVOLUMES: int
FOFX_DONTDISPLAYSOURCEPATH: int
FOFX_DONTDISPLAYDESTPATH: int
FOFX_REQUIREELEVATION: int
FOFX_COPYASDOWNLOAD: int
FOFX_DONTDISPLAYLOCATIONS: int
PO_DELETE: int
PO_RENAME: int
PO_PORTCHANGE: int
PO_REN_PORT: int
SE_ERR_FNF: int
SE_ERR_PNF: int
SE_ERR_ACCESSDENIED: int
SE_ERR_OOM: int
SE_ERR_DLLNOTFOUND: int
SE_ERR_SHARE: int
SE_ERR_ASSOCINCOMPLETE: int
SE_ERR_DDETIMEOUT: int
SE_ERR_DDEFAIL: int
SE_ERR_DDEBUSY: int
SE_ERR_NOASSOC: int
SEE_MASK_CLASSNAME: int
SEE_MASK_CLASSKEY: int
SEE_MASK_IDLIST: int
SEE_MASK_INVOKEIDLIST: int
SEE_MASK_ICON: int
SEE_MASK_HOTKEY: int
SEE_MASK_NOCLOSEPROCESS: int
SEE_MASK_CONNECTNETDRV: int
SEE_MASK_FLAG_DDEWAIT: int
SEE_MASK_DOENVSUBST: int
SEE_MASK_FLAG_NO_UI: int
SEE_MASK_UNICODE: int
SEE_MASK_NO_CONSOLE: int
SEE_MASK_ASYNCOK: int
SEE_MASK_HMONITOR: int
SHERB_NOCONFIRMATION: int
SHERB_NOPROGRESSUI: int
SHERB_NOSOUND: int
NIM_ADD: int
NIM_MODIFY: int
NIM_DELETE: int
NIF_MESSAGE: int
NIF_ICON: int
NIF_TIP: int
SHGFI_ICON: int
SHGFI_DISPLAYNAME: int
SHGFI_TYPENAME: int
SHGFI_ATTRIBUTES: int
SHGFI_ICONLOCATION: int
SHGFI_EXETYPE: int
SHGFI_SYSICONINDEX: int
SHGFI_LINKOVERLAY: int
SHGFI_SELECTED: int
SHGFI_ATTR_SPECIFIED: int
SHGFI_LARGEICON: int
SHGFI_SMALLICON: int
SHGFI_OPENICON: int
SHGFI_SHELLICONSIZE: int
SHGFI_PIDL: int
SHGFI_USEFILEATTRIBUTES: int
SHGNLI_PIDL: int
SHGNLI_PREFIXNAME: int
SHGNLI_NOUNIQUE: int
PRINTACTION_OPEN: int
PRINTACTION_PROPERTIES: int
PRINTACTION_NETINSTALL: int
PRINTACTION_NETINSTALLLINK: int
PRINTACTION_TESTPAGE: int
PRINTACTION_OPENNETPRN: int
PRINTACTION_DOCUMENTDEFAULTS: int
PRINTACTION_SERVERPROPERTIES: int
CMF_NORMAL: int
CMF_DEFAULTONLY: int
CMF_VERBSONLY: int
CMF_EXPLORE: int
CMF_NOVERBS: int
CMF_CANRENAME: int
CMF_NODEFAULT: int
CMF_INCLUDESTATIC: int
CMF_ITEMMENU: int
CMF_EXTENDEDVERBS: int
CMF_DISABLEDVERBS: int
CMF_ASYNCVERBSTATE: int
CMF_OPTIMIZEFORINVOKE: int
CMF_SYNCCASCADEMENU: int
CMF_DONOTPICKDEFAULT: int
CMF_RESERVED: int
GCS_VERBA: int
GCS_HELPTEXTA: int
GCS_VALIDATEA: int
GCS_VERBW: int
GCS_HELPTEXTW: int
GCS_VALIDATEW: int
GCS_UNICODE: int
GCS_VERB: int
GCS_HELPTEXT: int
GCS_VALIDATE: int
CMDSTR_NEWFOLDERA: str
CMDSTR_VIEWLISTA: str
CMDSTR_VIEWDETAILSA: str
CMDSTR_NEWFOLDER: str
CMDSTR_VIEWLIST: str
CMDSTR_VIEWDETAILS: str
CMIC_MASK_HOTKEY: int
CMIC_MASK_ICON: int
CMIC_MASK_FLAG_NO_UI: int
CMIC_MASK_UNICODE: int
CMIC_MASK_NO_CONSOLE: int
CMIC_MASK_ASYNCOK: int
CMIC_MASK_PTINVOKE: int
GIL_OPENICON: int
GIL_FORSHELL: int
GIL_ASYNC: int
GIL_DEFAULTICON: int
GIL_FORSHORTCUT: int
GIL_CHECKSHIELD: int
GIL_SIMULATEDOC: int
GIL_PERINSTANCE: int
GIL_PERCLASS: int
GIL_NOTFILENAME: int
GIL_DONTCACHE: int
GIL_SHIELD: int
GIL_FORCENOSHIELD: int
ISIOI_ICONFILE: int
ISIOI_ICONINDEX: int
ISIOI_SYSIMAGELISTINDEX: int
FVSIF_RECT: int
FVSIF_PINNED: int
FVSIF_NEWFAILED: int
FVSIF_NEWFILE: int
FVSIF_CANVIEWIT: int
FCIDM_SHVIEWFIRST: int
FCIDM_SHVIEWLAST: int
FCIDM_BROWSERFIRST: int
FCIDM_BROWSERLAST: int
FCIDM_GLOBALFIRST: int
FCIDM_GLOBALLAST: int
FCIDM_MENU_FILE: Incomplete
FCIDM_MENU_EDIT: Incomplete
FCIDM_MENU_VIEW: Incomplete
FCIDM_MENU_VIEW_SEP_OPTIONS: Incomplete
FCIDM_MENU_TOOLS: Incomplete
FCIDM_MENU_TOOLS_SEP_GOTO: Incomplete
FCIDM_MENU_HELP: Incomplete
FCIDM_MENU_FIND: Incomplete
FCIDM_MENU_EXPLORE: Incomplete
FCIDM_MENU_FAVORITES: Incomplete
FCIDM_TOOLBAR: Incomplete
FCIDM_STATUS: Incomplete
IDC_OFFLINE_HAND: int
SBSP_DEFBROWSER: int
SBSP_SAMEBROWSER: int
SBSP_NEWBROWSER: int
SBSP_DEFMODE: int
SBSP_OPENMODE: int
SBSP_EXPLOREMODE: int
SBSP_ABSOLUTE: int
SBSP_RELATIVE: int
SBSP_PARENT: int
SBSP_NAVIGATEBACK: int
SBSP_NAVIGATEFORWARD: int
SBSP_ALLOW_AUTONAVIGATE: int
SBSP_INITIATEDBYHLINKFRAME: int
SBSP_REDIRECT: int
SBSP_WRITENOHISTORY: int
SBSP_NOAUTOSELECT: int
FCW_STATUS: int
FCW_TOOLBAR: int
FCW_TREE: int
FCW_INTERNETBAR: int
FCW_PROGRESS: int
FCT_MERGE: int
FCT_CONFIGABLE: int
FCT_ADDTOEND: int
CDBOSC_SETFOCUS: int
CDBOSC_KILLFOCUS: int
CDBOSC_SELCHANGE: int
CDBOSC_RENAME: int
SVSI_DESELECT: int
SVSI_SELECT: int
SVSI_EDIT: int
SVSI_DESELECTOTHERS: int
SVSI_ENSUREVISIBLE: int
SVSI_FOCUSED: int
SVSI_TRANSLATEPT: int
SVSI_SELECTIONMARK: int
SVSI_POSITIONITEM: int
SVSI_CHECK: int
SVSI_CHECK2: int
SVSI_KEYBOARDSELECT: int
SVSI_NOTAKEFOCUS: int
SVGIO_BACKGROUND: int
SVGIO_SELECTION: int
SVGIO_ALLVIEW: int
SVGIO_CHECKED: Incomplete
SVGIO_TYPE_MASK: Incomplete
SVGIO_FLAG_VIEWORDER: int
STRRET_WSTR: int
STRRET_OFFSET: int
STRRET_CSTR: int
CSIDL_DESKTOP: int
CSIDL_INTERNET: int
CSIDL_PROGRAMS: int
CSIDL_CONTROLS: int
CSIDL_PRINTERS: int
CSIDL_PERSONAL: int
CSIDL_FAVORITES: int
CSIDL_STARTUP: int
CSIDL_RECENT: int
CSIDL_SENDTO: int
CSIDL_BITBUCKET: int
CSIDL_STARTMENU: int
CSIDL_MYDOCUMENTS: int
CSIDL_MYMUSIC: int
CSIDL_MYVIDEO: int
CSIDL_DESKTOPDIRECTORY: int
CSIDL_DRIVES: int
CSIDL_NETWORK: int
CSIDL_NETHOOD: int
CSIDL_FONTS: int
CSIDL_TEMPLATES: int
CSIDL_COMMON_STARTMENU: int
CSIDL_COMMON_PROGRAMS: int
CSIDL_COMMON_STARTUP: int
CSIDL_COMMON_DESKTOPDIRECTORY: int
CSIDL_APPDATA: int
CSIDL_PRINTHOOD: int
CSIDL_LOCAL_APPDATA: int
CSIDL_ALTSTARTUP: int
CSIDL_COMMON_ALTSTARTUP: int
CSIDL_COMMON_FAVORITES: int
CSIDL_INTERNET_CACHE: int
CSIDL_COOKIES: int
CSIDL_HISTORY: int
CSIDL_COMMON_APPDATA: int
CSIDL_WINDOWS: int
CSIDL_SYSTEM: int
CSIDL_PROGRAM_FILES: int
CSIDL_MYPICTURES: int
CSIDL_PROFILE: int
CSIDL_SYSTEMX86: int
CSIDL_PROGRAM_FILESX86: int
CSIDL_PROGRAM_FILES_COMMON: int
CSIDL_PROGRAM_FILES_COMMONX86: int
CSIDL_COMMON_TEMPLATES: int
CSIDL_COMMON_DOCUMENTS: int
CSIDL_COMMON_ADMINTOOLS: int
CSIDL_ADMINTOOLS: int
CSIDL_CONNECTIONS: int
CSIDL_COMMON_MUSIC: int
CSIDL_COMMON_PICTURES: int
CSIDL_COMMON_VIDEO: int
CSIDL_RESOURCES: int
CSIDL_RESOURCES_LOCALIZED: int
CSIDL_COMMON_OEM_LINKS: int
CSIDL_CDBURN_AREA: int
CSIDL_COMPUTERSNEARME: int
BIF_RETURNONLYFSDIRS: int
BIF_DONTGOBELOWDOMAIN: int
BIF_STATUSTEXT: int
BIF_RETURNFSANCESTORS: int
BIF_EDITBOX: int
BIF_VALIDATE: int
BIF_BROWSEFORCOMPUTER: int
BIF_BROWSEFORPRINTER: int
BIF_BROWSEINCLUDEFILES: int
BFFM_INITIALIZED: int
BFFM_SELCHANGED: int
BFFM_VALIDATEFAILEDA: int
BFFM_VALIDATEFAILEDW: int
BFFM_SETSTATUSTEXTA: Incomplete
BFFM_ENABLEOK: Incomplete
BFFM_SETSELECTIONA: Incomplete
BFFM_SETSELECTIONW: Incomplete
BFFM_SETSTATUSTEXTW: Incomplete
BFFM_SETSTATUSTEXT: Incomplete
BFFM_SETSELECTION: Incomplete
BFFM_VALIDATEFAILED: int
SFGAO_CANCOPY: int
SFGAO_CANMOVE: int
SFGAO_CANLINK: int
SFGAO_CANRENAME: int
SFGAO_CANDELETE: int
SFGAO_HASPROPSHEET: int
SFGAO_DROPTARGET: int
SFGAO_CAPABILITYMASK: int
SFGAO_LINK: int
SFGAO_SHARE: int
SFGAO_READONLY: int
SFGAO_GHOSTED: int
SFGAO_HIDDEN: int
SFGAO_DISPLAYATTRMASK: int
SFGAO_FILESYSANCESTOR: int
SFGAO_FOLDER: int
SFGAO_FILESYSTEM: int
SFGAO_HASSUBFOLDER: int
SFGAO_CONTENTSMASK: int
SFGAO_VALIDATE: int
SFGAO_REMOVABLE: int
SFGAO_COMPRESSED: int
SFGAO_BROWSABLE: int
SFGAO_NONENUMERATED: int
SFGAO_NEWCONTENT: int
SFGAO_STORAGE: int
DWFRF_NORMAL: int
DWFRF_DELETECONFIGDATA: int
DWFAF_HIDDEN: int
DBIM_MINSIZE: int
DBIM_MAXSIZE: int
DBIM_INTEGRAL: int
DBIM_ACTUAL: int
DBIM_TITLE: int
DBIM_MODEFLAGS: int
DBIM_BKCOLOR: int
DBIMF_NORMAL: int
DBIMF_VARIABLEHEIGHT: int
DBIMF_DEBOSSED: int
DBIMF_BKCOLOR: int
DBIF_VIEWMODE_NORMAL: int
DBIF_VIEWMODE_VERTICAL: int
DBIF_VIEWMODE_FLOATING: int
DBIF_VIEWMODE_TRANSPARENT: int
COMPONENT_TOP: int
COMP_TYPE_HTMLDOC: int
COMP_TYPE_PICTURE: int
COMP_TYPE_WEBSITE: int
COMP_TYPE_CONTROL: int
COMP_TYPE_CFHTML: int
COMP_TYPE_MAX: int
AD_APPLY_SAVE: int
AD_APPLY_HTMLGEN: int
AD_APPLY_REFRESH: int
AD_APPLY_ALL: Incomplete
AD_APPLY_FORCE: int
AD_APPLY_BUFFERED_REFRESH: int
WPSTYLE_CENTER: int
WPSTYLE_TILE: int
WPSTYLE_STRETCH: int
WPSTYLE_MAX: int
COMP_ELEM_TYPE: int
COMP_ELEM_CHECKED: int
COMP_ELEM_DIRTY: int
COMP_ELEM_NOSCROLL: int
COMP_ELEM_POS_LEFT: int
COMP_ELEM_POS_TOP: int
COMP_ELEM_SIZE_WIDTH: int
COMP_ELEM_SIZE_HEIGHT: int
COMP_ELEM_POS_ZINDEX: int
COMP_ELEM_SOURCE: int
COMP_ELEM_FRIENDLYNAME: int
COMP_ELEM_SUBSCRIBEDURL: int
ADDURL_SILENT: int
CFSTR_SHELLIDLIST: str
CFSTR_SHELLIDLISTOFFSET: str
CFSTR_NETRESOURCES: str
CFSTR_FILEDESCRIPTORA: str
CFSTR_FILEDESCRIPTORW: str
CFSTR_FILECONTENTS: str
CFSTR_FILENAMEA: str
CFSTR_FILENAMEW: str
CFSTR_PRINTERGROUP: str
CFSTR_FILENAMEMAPA: str
CFSTR_FILENAMEMAPW: str
CFSTR_SHELLURL: str
CFSTR_INETURLA: str
CFSTR_INETURLW: str
CFSTR_PREFERREDDROPEFFECT: str
CFSTR_PERFORMEDDROPEFFECT: str
CFSTR_PASTESUCCEEDED: str
CFSTR_INDRAGLOOP: str
CFSTR_DRAGCONTEXT: str
CFSTR_MOUNTEDVOLUME: str
CFSTR_PERSISTEDDATAOBJECT: str
CFSTR_TARGETCLSID: str
CFSTR_LOGICALPERFORMEDDROPEFFECT: str
CFSTR_AUTOPLAY_SHELLIDLISTS: str
CFSTR_FILEDESCRIPTOR: str
CFSTR_FILENAME: str
CFSTR_FILENAMEMAP: str
DVASPECT_SHORTNAME: int
SHCNE_RENAMEITEM: int
SHCNE_CREATE: int
SHCNE_DELETE: int
SHCNE_MKDIR: int
SHCNE_RMDIR: int
SHCNE_MEDIAINSERTED: int
SHCNE_MEDIAREMOVED: int
SHCNE_DRIVEREMOVED: int
SHCNE_DRIVEADD: int
SHCNE_NETSHARE: int
SHCNE_NETUNSHARE: int
SHCNE_ATTRIBUTES: int
SHCNE_UPDATEDIR: int
SHCNE_UPDATEITEM: int
SHCNE_SERVERDISCONNECT: int
SHCNE_UPDATEIMAGE: int
SHCNE_DRIVEADDGUI: int
SHCNE_RENAMEFOLDER: int
SHCNE_FREESPACE: int
SHCNE_EXTENDED_EVENT: int
SHCNE_ASSOCCHANGED: int
SHCNE_DISKEVENTS: int
SHCNE_GLOBALEVENTS: int
SHCNE_ALLEVENTS: int
SHCNE_INTERRUPT: int
SHCNEE_ORDERCHANGED: int
SHCNF_IDLIST: int
SHCNF_PATHA: int
SHCNF_PRINTERA: int
SHCNF_DWORD: int
SHCNF_PATHW: int
SHCNF_PRINTERW: int
SHCNF_TYPE: int
SHCNF_FLUSH: int
SHCNF_FLUSHNOWAIT: int
SHCNF_PATH: int
SHCNF_PRINTER: int
QIF_CACHED: int
QIF_DONTEXPANDFOLDER: int
SWFO_NEEDDISPATCH: int
SWFO_INCLUDEPENDING: int
SWFO_COOKIEPASSED: int
SWC_EXPLORER: int
SWC_BROWSER: int
SWC_3RDPARTY: int
SWC_CALLBACK: int
SWC_DESKTOP: int
SHARD_PIDL: int
SHARD_PATHA: int
SHARD_PATHW: int
SHARD_APPIDINFO: int
SHARD_APPIDINFOIDLIST: int
SHARD_LINK: int
SHARD_APPIDINFOLINK: int
SHARD_SHELLITEM: int
SHARD_PATH: int
SHGDFIL_FINDDATA: int
SHGDFIL_NETRESOURCE: int
SHGDFIL_DESCRIPTIONID: int
SHDID_ROOT_REGITEM: int
SHDID_FS_FILE: int
SHDID_FS_DIRECTORY: int
SHDID_FS_OTHER: int
SHDID_COMPUTER_DRIVE35: int
SHDID_COMPUTER_DRIVE525: int
SHDID_COMPUTER_REMOVABLE: int
SHDID_COMPUTER_FIXED: int
SHDID_COMPUTER_NETDRIVE: int
SHDID_COMPUTER_CDROM: int
SHDID_COMPUTER_RAMDISK: int
SHDID_COMPUTER_OTHER: int
SHDID_NET_DOMAIN: int
SHDID_NET_SERVER: int
SHDID_NET_SHARE: int
SHDID_NET_RESTOFNET: int
SHDID_NET_OTHER: int
PID_IS_URL: int
PID_IS_NAME: int
PID_IS_WORKINGDIR: int
PID_IS_HOTKEY: int
PID_IS_SHOWCMD: int
PID_IS_ICONINDEX: int
PID_IS_ICONFILE: int
PID_IS_WHATSNEW: int
PID_IS_AUTHOR: int
PID_IS_DESCRIPTION: int
PID_IS_COMMENT: int
PID_INTSITE_WHATSNEW: int
PID_INTSITE_AUTHOR: int
PID_INTSITE_LASTVISIT: int
PID_INTSITE_LASTMOD: int
PID_INTSITE_VISITCOUNT: int
PID_INTSITE_DESCRIPTION: int
PID_INTSITE_COMMENT: int
PID_INTSITE_FLAGS: int
PID_INTSITE_CONTENTLEN: int
PID_INTSITE_CONTENTCODE: int
PID_INTSITE_RECURSE: int
PID_INTSITE_WATCH: int
PID_INTSITE_SUBSCRIPTION: int
PID_INTSITE_URL: int
PID_INTSITE_TITLE: int
PID_INTSITE_CODEPAGE: int
PID_INTSITE_TRACKING: int
PIDISF_RECENTLYCHANGED: int
PIDISF_CACHEDSTICKY: int
PIDISF_CACHEIMAGES: int
PIDISF_FOLLOWALLLINKS: int
PIDISM_GLOBAL: int
PIDISM_WATCH: int
PIDISM_DONTWATCH: int
SSF_SHOWALLOBJECTS: int
SSF_SHOWEXTENSIONS: int
SSF_SHOWCOMPCOLOR: int
SSF_SHOWSYSFILES: int
SSF_DOUBLECLICKINWEBVIEW: int
SSF_SHOWATTRIBCOL: int
SSF_DESKTOPHTML: int
SSF_WIN95CLASSIC: int
SSF_DONTPRETTYPATH: int
SSF_SHOWINFOTIP: int
SSF_MAPNETDRVBUTTON: int
SSF_NOCONFIRMRECYCLE: int
SSF_HIDEICONS: int
ABM_NEW: int
ABM_REMOVE: int
ABM_QUERYPOS: int
ABM_SETPOS: int
ABM_GETSTATE: int
ABM_GETTASKBARPOS: int
ABM_ACTIVATE: int
ABM_GETAUTOHIDEBAR: int
ABM_SETAUTOHIDEBAR: int
ABM_WINDOWPOSCHANGED: int
ABN_STATECHANGE: int
ABN_POSCHANGED: int
ABN_FULLSCREENAPP: int
ABN_WINDOWARRANGE: int
ABS_AUTOHIDE: int
ABS_ALWAYSONTOP: int
ABE_LEFT: int
ABE_TOP: int
ABE_RIGHT: int
ABE_BOTTOM: int

def EIRESID(x): ...

SHCONTF_FOLDERS: int
SHCONTF_NONFOLDERS: int
SHCONTF_INCLUDEHIDDEN: int
SHCONTF_INIT_ON_FIRST_NEXT: int
SHCONTF_NETPRINTERSRCH: int
SHCONTF_SHAREABLE: int
SHCONTF_STORAGE: int
SHGDN_NORMAL: int
SHGDN_INFOLDER: int
SHGDN_FOREDITING: int
SHGDN_INCLUDE_NONFILESYS: int
SHGDN_FORADDRESSBAR: int
SHGDN_FORPARSING: int
BFO_NONE: int
BFO_BROWSER_PERSIST_SETTINGS: int
BFO_RENAME_FOLDER_OPTIONS_TOINTERNET: int
BFO_BOTH_OPTIONS: int
BIF_PREFER_INTERNET_SHORTCUT: int
BFO_BROWSE_NO_IN_NEW_PROCESS: int
BFO_ENABLE_HYPERLINK_TRACKING: int
BFO_USE_IE_OFFLINE_SUPPORT: int
BFO_SUBSTITUE_INTERNET_START_PAGE: int
BFO_USE_IE_LOGOBANDING: int
BFO_ADD_IE_TOCAPTIONBAR: int
BFO_USE_DIALUP_REF: int
BFO_USE_IE_TOOLBAR: int
BFO_NO_PARENT_FOLDER_SUPPORT: int
BFO_NO_REOPEN_NEXT_RESTART: int
BFO_GO_HOME_PAGE: int
BFO_PREFER_IEPROCESS: int
BFO_SHOW_NAVIGATION_CANCELLED: int
BFO_QUERY_ALL: int
PID_FINDDATA: int
PID_NETRESOURCE: int
PID_DESCRIPTIONID: int
PID_WHICHFOLDER: int
PID_NETWORKLOCATION: int
PID_COMPUTERNAME: int
PID_DISPLACED_FROM: int
PID_DISPLACED_DATE: int
PID_SYNC_COPY_IN: int
PID_MISC_STATUS: int
PID_MISC_ACCESSCOUNT: int
PID_MISC_OWNER: int
PID_HTMLINFOTIPFILE: int
PID_MISC_PICS: int
PID_DISPLAY_PROPERTIES: int
PID_INTROTEXT: int
PIDSI_ARTIST: int
PIDSI_SONGTITLE: int
PIDSI_ALBUM: int
PIDSI_YEAR: int
PIDSI_COMMENT: int
PIDSI_TRACK: int
PIDSI_GENRE: int
PIDSI_LYRICS: int
PIDDRSI_PROTECTED: int
PIDDRSI_DESCRIPTION: int
PIDDRSI_PLAYCOUNT: int
PIDDRSI_PLAYSTARTS: int
PIDDRSI_PLAYEXPIRES: int
PIDVSI_STREAM_NAME: int
PIDVSI_FRAME_WIDTH: int
PIDVSI_FRAME_HEIGHT: int
PIDVSI_TIMELENGTH: int
PIDVSI_FRAME_COUNT: int
PIDVSI_FRAME_RATE: int
PIDVSI_DATA_RATE: int
PIDVSI_SAMPLE_SIZE: int
PIDVSI_COMPRESSION: int
PIDVSI_STREAM_NUMBER: int
PIDASI_FORMAT: int
PIDASI_TIMELENGTH: int
PIDASI_AVG_DATA_RATE: int
PIDASI_SAMPLE_RATE: int
PIDASI_SAMPLE_SIZE: int
PIDASI_CHANNEL_COUNT: int
PIDASI_STREAM_NUMBER: int
PIDASI_STREAM_NAME: int
PIDASI_COMPRESSION: int
PID_CONTROLPANEL_CATEGORY: int
PID_VOLUME_FREE: int
PID_VOLUME_CAPACITY: int
PID_VOLUME_FILESYSTEM: int
PID_SHARE_CSC_STATUS: int
PID_LINK_TARGET: int
PID_QUERY_RANK: int
PROPSETFLAG_DEFAULT: int
PROPSETFLAG_NONSIMPLE: int
PROPSETFLAG_ANSI: int
PROPSETFLAG_UNBUFFERED: int
PROPSETFLAG_CASE_SENSITIVE: int
PROPSET_BEHAVIOR_CASE_SENSITIVE: int
PID_DICTIONARY: int
PID_CODEPAGE: int
PID_FIRST_USABLE: int
PID_FIRST_NAME_DEFAULT: int
PID_LOCALE: int
PID_MODIFY_TIME: int
PID_SECURITY: int
PID_BEHAVIOR: int
PID_ILLEGAL: int
PID_MIN_READONLY: int
PID_MAX_READONLY: int
PIDDI_THUMBNAIL: int
PIDSI_TITLE: int
PIDSI_SUBJECT: int
PIDSI_AUTHOR: int
PIDSI_KEYWORDS: int
PIDSI_COMMENTS: int
PIDSI_TEMPLATE: int
PIDSI_LASTAUTHOR: int
PIDSI_REVNUMBER: int
PIDSI_EDITTIME: int
PIDSI_LASTPRINTED: int
PIDSI_CREATE_DTM: int
PIDSI_LASTSAVE_DTM: int
PIDSI_PAGECOUNT: int
PIDSI_WORDCOUNT: int
PIDSI_CHARCOUNT: int
PIDSI_THUMBNAIL: int
PIDSI_APPNAME: int
PIDSI_DOC_SECURITY: int
PIDDSI_CATEGORY: int
PIDDSI_PRESFORMAT: int
PIDDSI_BYTECOUNT: int
PIDDSI_LINECOUNT: int
PIDDSI_PARCOUNT: int
PIDDSI_SLIDECOUNT: int
PIDDSI_NOTECOUNT: int
PIDDSI_HIDDENCOUNT: int
PIDDSI_MMCLIPCOUNT: int
PIDDSI_SCALE: int
PIDDSI_HEADINGPAIR: int
PIDDSI_DOCPARTS: int
PIDDSI_MANAGER: int
PIDDSI_COMPANY: int
PIDDSI_LINKSDIRTY: int
PIDMSI_EDITOR: int
PIDMSI_SUPPLIER: int
PIDMSI_SOURCE: int
PIDMSI_SEQUENCE_NO: int
PIDMSI_PROJECT: int
PIDMSI_STATUS: int
PIDMSI_OWNER: int
PIDMSI_RATING: int
PIDMSI_PRODUCTION: int
PIDMSI_COPYRIGHT: int
PRSPEC_INVALID: int
PRSPEC_LPWSTR: int
PRSPEC_PROPID: int
SHCIDS_ALLFIELDS: int
SHCIDS_CANONICALONLY: int
SHCIDS_BITMASK: int
SHCIDS_COLUMNMASK: int
SFGAO_CANMONIKER: int
SFGAO_HASSTORAGE: int
SFGAO_STREAM: int
SFGAO_STORAGEANCESTOR: int
SFGAO_STORAGECAPMASK: int
MAXPROPPAGES: int
PSP_DEFAULT: int
PSP_DLGINDIRECT: int
PSP_USEHICON: int
PSP_USEICONID: int
PSP_USETITLE: int
PSP_RTLREADING: int
PSP_HASHELP: int
PSP_USEREFPARENT: int
PSP_USECALLBACK: int
PSP_PREMATURE: int
PSP_HIDEHEADER: int
PSP_USEHEADERTITLE: int
PSP_USEHEADERSUBTITLE: int
PSP_USEFUSIONCONTEXT: int
PSPCB_ADDREF: int
PSPCB_RELEASE: int
PSPCB_CREATE: int
PSH_DEFAULT: int
PSH_PROPTITLE: int
PSH_USEHICON: int
PSH_USEICONID: int
PSH_PROPSHEETPAGE: int
PSH_WIZARDHASFINISH: int
PSH_WIZARD: int
PSH_USEPSTARTPAGE: int
PSH_NOAPPLYNOW: int
PSH_USECALLBACK: int
PSH_HASHELP: int
PSH_MODELESS: int
PSH_RTLREADING: int
PSH_WIZARDCONTEXTHELP: int
PSH_WIZARD97: int
PSH_WATERMARK: int
PSH_USEHBMWATERMARK: int
PSH_USEHPLWATERMARK: int
PSH_STRETCHWATERMARK: int
PSH_HEADER: int
PSH_USEHBMHEADER: int
PSH_USEPAGELANG: int
PSH_WIZARD_LITE: int
PSH_NOCONTEXTHELP: int
PSCB_INITIALIZED: int
PSCB_PRECREATE: int
PSCB_BUTTONPRESSED: int
PSNRET_NOERROR: int
PSNRET_INVALID: int
PSNRET_INVALID_NOCHANGEPAGE: int
PSNRET_MESSAGEHANDLED: int
PSWIZB_BACK: int
PSWIZB_NEXT: int
PSWIZB_FINISH: int
PSWIZB_DISABLEDFINISH: int
PSBTN_BACK: int
PSBTN_NEXT: int
PSBTN_FINISH: int
PSBTN_OK: int
PSBTN_APPLYNOW: int
PSBTN_CANCEL: int
PSBTN_HELP: int
PSBTN_MAX: int
ID_PSRESTARTWINDOWS: int
ID_PSREBOOTSYSTEM: Incomplete
WIZ_CXDLG: int
WIZ_CYDLG: int
WIZ_CXBMP: int
WIZ_BODYX: int
WIZ_BODYCX: int
PROP_SM_CXDLG: int
PROP_SM_CYDLG: int
PROP_MED_CXDLG: int
PROP_MED_CYDLG: int
PROP_LG_CXDLG: int
PROP_LG_CYDLG: int
ISOLATION_AWARE_USE_STATIC_LIBRARY: int
ISOLATION_AWARE_BUILD_STATIC_LIBRARY: int
SHCOLSTATE_TYPE_STR: int
SHCOLSTATE_TYPE_INT: int
SHCOLSTATE_TYPE_DATE: int
SHCOLSTATE_TYPEMASK: int
SHCOLSTATE_ONBYDEFAULT: int
SHCOLSTATE_SLOW: int
SHCOLSTATE_EXTENDED: int
SHCOLSTATE_SECONDARYUI: int
SHCOLSTATE_HIDDEN: int
SHCOLSTATE_PREFER_VARCMP: int
FWF_AUTOARRANGE: int
FWF_ABBREVIATEDNAMES: int
FWF_SNAPTOGRID: int
FWF_OWNERDATA: int
FWF_BESTFITWINDOW: int
FWF_DESKTOP: int
FWF_SINGLESEL: int
FWF_NOSUBFOLDERS: int
FWF_TRANSPARENT: int
FWF_NOCLIENTEDGE: int
FWF_NOSCROLL: int
FWF_ALIGNLEFT: int
FWF_NOICONS: int
FWF_SHOWSELALWAYS: int
FWF_NOVISIBLE: int
FWF_SINGLECLICKACTIVATE: int
FWF_NOWEBVIEW: int
FWF_HIDEFILENAMES: int
FWF_CHECKSELECT: int
FVM_FIRST: int
FVM_ICON: int
FVM_SMALLICON: int
FVM_LIST: int
FVM_DETAILS: int
FVM_THUMBNAIL: int
FVM_TILE: int
FVM_THUMBSTRIP: int
SVUIA_DEACTIVATE: int
SVUIA_ACTIVATE_NOFOCUS: int
SVUIA_ACTIVATE_FOCUS: int
SVUIA_INPLACEACTIVATE: int
SHCNRF_InterruptLevel: int
SHCNRF_ShellLevel: int
SHCNRF_RecursiveInterrupt: int
SHCNRF_NewDelivery: int
FD_CLSID: int
FD_SIZEPOINT: int
FD_ATTRIBUTES: int
FD_CREATETIME: int
FD_ACCESSTIME: int
FD_WRITESTIME: int
FD_FILESIZE: int
FD_PROGRESSUI: int
FD_LINKUI: int
ASSOCF_INIT_NOREMAPCLSID: int
ASSOCF_INIT_BYEXENAME: int
ASSOCF_OPEN_BYEXENAME: int
ASSOCF_INIT_DEFAULTTOSTAR: int
ASSOCF_INIT_DEFAULTTOFOLDER: int
ASSOCF_NOUSERSETTINGS: int
ASSOCF_NOTRUNCATE: int
ASSOCF_VERIFY: int
ASSOCF_REMAPRUNDLL: int
ASSOCF_NOFIXUPS: int
ASSOCF_IGNOREBASECLASS: int
ASSOCSTR_COMMAND: int
ASSOCSTR_EXECUTABLE: int
ASSOCSTR_FRIENDLYDOCNAME: int
ASSOCSTR_FRIENDLYAPPNAME: int
ASSOCSTR_NOOPEN: int
ASSOCSTR_SHELLNEWVALUE: int
ASSOCSTR_DDECOMMAND: int
ASSOCSTR_DDEIFEXEC: int
ASSOCSTR_DDEAPPLICATION: int
ASSOCSTR_DDETOPIC: int
ASSOCSTR_INFOTIP: int
ASSOCSTR_QUICKTIP: int
ASSOCSTR_TILEINFO: int
ASSOCSTR_CONTENTTYPE: int
ASSOCSTR_DEFAULTICON: int
ASSOCSTR_SHELLEXTENSION: int
ASSOCKEY_SHELLEXECCLASS: int
ASSOCKEY_APP: int
ASSOCKEY_CLASS: int
ASSOCKEY_BASECLASS: int
ASSOCDATA_MSIDESCRIPTOR: int
ASSOCDATA_NOACTIVATEHANDLER: int
ASSOCDATA_QUERYCLASSSTORE: int
ASSOCDATA_HASPERUSERASSOC: int
ASSOCDATA_EDITFLAGS: int
ASSOCDATA_VALUE: int
SHGVSPB_PERUSER: int
SHGVSPB_ALLUSERS: int
SHGVSPB_PERFOLDER: int
SHGVSPB_ALLFOLDERS: int
SHGVSPB_INHERIT: int
SHGVSPB_ROAM: int
SHGVSPB_NOAUTODEFAULTS: int
SHGVSPB_FOLDER: Incomplete
SHGVSPB_FOLDERNODEFAULTS: Incomplete
SHGVSPB_USERDEFAULTS: Incomplete
SHGVSPB_GLOBALDEAFAULTS: Incomplete
SFVM_REARRANGE: int
SFVM_ADDOBJECT: int
SFVM_REMOVEOBJECT: int
SFVM_UPDATEOBJECT: int
SFVM_GETSELECTEDOBJECTS: int
SFVM_SETITEMPOS: int
SFVM_SETCLIPBOARD: int
SFVM_SETPOINTS: int
SLDF_HAS_ID_LIST: int
SLDF_HAS_LINK_INFO: int
SLDF_HAS_NAME: int
SLDF_HAS_RELPATH: int
SLDF_HAS_WORKINGDIR: int
SLDF_HAS_ARGS: int
SLDF_HAS_ICONLOCATION: int
SLDF_UNICODE: int
SLDF_FORCE_NO_LINKINFO: int
SLDF_HAS_EXP_SZ: int
SLDF_RUN_IN_SEPARATE: int
SLDF_HAS_LOGO3ID: int
SLDF_HAS_DARWINID: int
SLDF_RUNAS_USER: int
SLDF_NO_PIDL_ALIAS: int
SLDF_FORCE_UNCNAME: int
SLDF_HAS_EXP_ICON_SZ: int
SLDF_RUN_WITH_SHIMLAYER: int
SLDF_RESERVED: int
EXP_SPECIAL_FOLDER_SIG: int
NT_CONSOLE_PROPS_SIG: int
NT_FE_CONSOLE_PROPS_SIG: int
EXP_DARWIN_ID_SIG: int
EXP_LOGO3_ID_SIG: int
EXP_SZ_ICON_SIG: int
EXP_SZ_LINK_SIG: int
IURL_SETURL_FL_GUESS_PROTOCOL: int
IURL_SETURL_FL_USE_DEFAULT_PROTOCOL: int
IURL_INVOKECOMMAND_FL_ALLOW_UI: int
IURL_INVOKECOMMAND_FL_USE_DEFAULT_VERB: int
IURL_INVOKECOMMAND_FL_DDEWAIT: int
IS_NORMAL: int
IS_FULLSCREEN: int
IS_SPLIT: int
IS_VALIDSIZESTATEBITS: Incomplete
IS_VALIDSTATEBITS: Incomplete
AD_APPLY_DYNAMICREFRESH: int
COMP_ELEM_ORIGINAL_CSI: int
COMP_ELEM_RESTORED_CSI: int
COMP_ELEM_CURITEMSTATE: int
COMP_ELEM_ALL: Incomplete
DTI_ADDUI_DEFAULT: int
DTI_ADDUI_DISPSUBWIZARD: int
DTI_ADDUI_POSITIONITEM: int
COMPONENT_DEFAULT_LEFT: int
COMPONENT_DEFAULT_TOP: int
SSM_CLEAR: int
SSM_SET: int
SSM_REFRESH: int
SSM_UPDATE: int
SCHEME_DISPLAY: int
SCHEME_EDIT: int
SCHEME_LOCAL: int
SCHEME_GLOBAL: int
SCHEME_REFRESH: int
SCHEME_UPDATE: int
SCHEME_DONOTUSE: int
SCHEME_CREATE: int
GADOF_DIRTY: int
EVCF_HASSETTINGS: int
EVCF_ENABLEBYDEFAULT: int
EVCF_REMOVEFROMLIST: int
EVCF_ENABLEBYDEFAULT_AUTO: int
EVCF_DONTSHOWIFZERO: int
EVCF_SETTINGSMODE: int
EVCF_OUTOFDISKSPACE: int
EVCCBF_LASTNOTIFICATION: int
EBO_NONE: int
EBO_NAVIGATEONCE: int
EBO_SHOWFRAMES: int
EBO_ALWAYSNAVIGATE: int
EBO_NOTRAVELLOG: int
EBO_NOWRAPPERWINDOW: int
EBF_NONE: int
EBF_SELECTFROMDATAOBJECT: int
EBF_NODROPTARGET: int
ECS_ENABLED: int
ECS_DISABLED: int
ECS_HIDDEN: int
ECS_CHECKBOX: int
ECS_CHECKED: int
ECF_HASSUBCOMMANDS: int
ECF_HASSPLITBUTTON: int
ECF_HIDELABEL: int
ECF_ISSEPARATOR: int
ECF_HASLUASHIELD: int
SIATTRIBFLAGS_AND: int
SIATTRIBFLAGS_OR: int
SIATTRIBFLAGS_APPCOMPAT: int
SIATTRIBFLAGS_MASK: int
SIGDN_NORMALDISPLAY: int
SIGDN_PARENTRELATIVEPARSING: int
SIGDN_DESKTOPABSOLUTEPARSING: int
SIGDN_PARENTRELATIVEEDITING: int
SIGDN_DESKTOPABSOLUTEEDITING: int
SIGDN_FILESYSPATH: int
SIGDN_URL: int
SIGDN_PARENTRELATIVEFORADDRESSBAR: int
SIGDN_PARENTRELATIVE: int
SICHINT_DISPLAY: Incomplete
SICHINT_ALLFIELDS: int
SICHINT_CANONICAL: int
ASSOCCLASS_SHELL_KEY: int
ASSOCCLASS_PROGID_KEY: int
ASSOCCLASS_PROGID_STR: int
ASSOCCLASS_CLSID_KEY: int
ASSOCCLASS_CLSID_STR: int
ASSOCCLASS_APP_KEY: int
ASSOCCLASS_APP_STR: int
ASSOCCLASS_SYSTEM_STR: int
ASSOCCLASS_FOLDER: int
ASSOCCLASS_STAR: int
NSTCS_HASEXPANDOS: int
NSTCS_HASLINES: int
NSTCS_SINGLECLICKEXPAND: int
NSTCS_FULLROWSELECT: int
NSTCS_SPRINGEXPAND: int
NSTCS_HORIZONTALSCROLL: int
NSTCS_ROOTHASEXPANDO: int
NSTCS_SHOWSELECTIONALWAYS: int
NSTCS_NOINFOTIP: int
NSTCS_EVENHEIGHT: int
NSTCS_NOREPLACEOPEN: int
NSTCS_DISABLEDRAGDROP: int
NSTCS_NOORDERSTREAM: int
NSTCS_RICHTOOLTIP: int
NSTCS_BORDER: int
NSTCS_NOEDITLABELS: int
NSTCS_TABSTOP: int
NSTCS_FAVORITESMODE: int
NSTCS_AUTOHSCROLL: int
NSTCS_FADEINOUTEXPANDOS: int
NSTCS_EMPTYTEXT: int
NSTCS_CHECKBOXES: int
NSTCS_PARTIALCHECKBOXES: int
NSTCS_EXCLUSIONCHECKBOXES: int
NSTCS_DIMMEDCHECKBOXES: int
NSTCS_NOINDENTCHECKS: int
NSTCS_ALLOWJUNCTIONS: int
NSTCS_SHOWTABSBUTTON: int
NSTCS_SHOWDELETEBUTTON: int
NSTCS_SHOWREFRESHBUTTON: int
NSTCRS_VISIBLE: int
NSTCRS_HIDDEN: int
NSTCRS_EXPANDED: int
NSTCIS_NONE: int
NSTCIS_SELECTED: int
NSTCIS_EXPANDED: int
NSTCIS_BOLD: int
NSTCIS_DISABLED: int
NSTCGNI_NEXT: int
NSTCGNI_NEXTVISIBLE: int
NSTCGNI_PREV: int
NSTCGNI_PREVVISIBLE: int
NSTCGNI_PARENT: int
NSTCGNI_CHILD: int
NSTCGNI_FIRSTVISIBLE: int
NSTCGNI_LASTVISIBLE: int
CLSID_ExplorerBrowser: str
IBrowserFrame_Methods: Incomplete
ICategorizer_Methods: Incomplete
ICategoryProvider_Methods: Incomplete
IContextMenu_Methods: Incomplete
IExplorerCommand_Methods: Incomplete
IExplorerCommandProvider_Methods: Incomplete
IOleWindow_Methods: Incomplete
IPersist_Methods: Incomplete
IPersistFolder_Methods: Incomplete
IPersistFolder2_Methods: Incomplete
IShellExtInit_Methods: Incomplete
IShellView_Methods: Incomplete
IShellFolder_Methods: Incomplete
IShellFolder2_Methods: Incomplete
GPS_DEFAULT: int
GPS_HANDLERPROPERTIESONLY: int
GPS_READWRITE: int
GPS_TEMPORARY: int
GPS_FASTPROPERTIESONLY: int
GPS_OPENSLOWITEM: int
GPS_DELAYCREATION: int
GPS_BESTEFFORT: int
GPS_MASK_VALID: int
STR_AVOID_DRIVE_RESTRICTION_POLICY: str
STR_BIND_DELEGATE_CREATE_OBJECT: str
STR_BIND_FOLDERS_READ_ONLY: str
STR_BIND_FOLDER_ENUM_MODE: str
STR_BIND_FORCE_FOLDER_SHORTCUT_RESOLVE: str
STR_DONT_PARSE_RELATIVE: str
STR_DONT_RESOLVE_LINK: str
STR_FILE_SYS_BIND_DATA: str
STR_GET_ASYNC_HANDLER: str
STR_GPS_BESTEFFORT: str
STR_GPS_DELAYCREATION: str
STR_GPS_FASTPROPERTIESONLY: str
STR_GPS_HANDLERPROPERTIESONLY: str
STR_GPS_NO_OPLOCK: str
STR_GPS_OPENSLOWITEM: str
STR_IFILTER_FORCE_TEXT_FILTER_FALLBACK: str
STR_IFILTER_LOAD_DEFINED_FILTER: str
STR_INTERNAL_NAVIGATE: str
STR_INTERNETFOLDER_PARSE_ONLY_URLMON_BINDABLE: str
STR_ITEM_CACHE_CONTEXT: str
STR_NO_VALIDATE_FILENAME_CHARS: str
STR_PARSE_ALLOW_INTERNET_SHELL_FOLDERS: str
STR_PARSE_AND_CREATE_ITEM: str
STR_PARSE_DONT_REQUIRE_VALIDATED_URLS: str
STR_PARSE_EXPLICIT_ASSOCIATION_SUCCESSFUL: str
STR_PARSE_PARTIAL_IDLIST: str
STR_PARSE_PREFER_FOLDER_BROWSING: str
STR_PARSE_PREFER_WEB_BROWSING: str
STR_PARSE_PROPERTYSTORE: str
STR_PARSE_SHELL_PROTOCOL_TO_FILE_OBJECTS: str
STR_PARSE_SHOW_NET_DIAGNOSTICS_UI: str
STR_PARSE_SKIP_NET_CACHE: str
STR_PARSE_TRANSLATE_ALIASES: str
STR_PARSE_WITH_EXPLICIT_ASSOCAPP: str
STR_PARSE_WITH_EXPLICIT_PROGID: str
STR_PARSE_WITH_PROPERTIES: str
STR_SKIP_BINDING_CLSID: str
STR_TRACK_CLSID: str
KF_REDIRECTION_CAPABILITIES_ALLOW_ALL: int
KF_REDIRECTION_CAPABILITIES_REDIRECTABLE: int
KF_REDIRECTION_CAPABILITIES_DENY_ALL: int
KF_REDIRECTION_CAPABILITIES_DENY_POLICY_REDIRECTED: int
KF_REDIRECTION_CAPABILITIES_DENY_POLICY: int
KF_REDIRECTION_CAPABILITIES_DENY_PERMISSIONS: int
KF_REDIRECT_USER_EXCLUSIVE: int
KF_REDIRECT_COPY_SOURCE_DACL: int
KF_REDIRECT_OWNER_USER: int
KF_REDIRECT_SET_OWNER_EXPLICIT: int
KF_REDIRECT_CHECK_ONLY: int
KF_REDIRECT_WITH_UI: int
KF_REDIRECT_UNPIN: int
KF_REDIRECT_PIN: int
KF_REDIRECT_COPY_CONTENTS: int
KF_REDIRECT_DEL_SOURCE_CONTENTS: int
KF_REDIRECT_EXCLUDE_ALL_KNOWN_SUBFOLDERS: int
KF_CATEGORY_VIRTUAL: int
KF_CATEGORY_FIXED: int
KF_CATEGORY_COMMON: int
KF_CATEGORY_PERUSER: int
FFFP_EXACTMATCH: int
FFFP_NEARESTPARENTMATCH: int
KF_FLAG_CREATE: int
KF_FLAG_DONT_VERIFY: int
KF_FLAG_DONT_UNEXPAND: int
KF_FLAG_NO_ALIAS: int
KF_FLAG_INIT: int
KF_FLAG_DEFAULT_PATH: int
KF_FLAG_NOT_PARENT_RELATIVE: int
KF_FLAG_SIMPLE_IDLIST: int
ADLT_RECENT: int
ADLT_FREQUENT: int
KDC_FREQUENT: int
KDC_RECENT: int
LFF_FORCEFILESYSTEM: int
LFF_STORAGEITEMS: int
LFF_ALLITEMS: int
DSFT_DETECT: int
DSFT_PRIVATE: int
DSFT_PUBLIC: int
LOF_DEFAULT: int
LOF_PINNEDTONAVPANE: int
LOF_MASK_ALL: int
LSF_FAILIFTHERE: int
LSF_OVERRIDEEXISTING: int
LSF_MAKEUNIQUENAME: int
TSF_NORMAL: int
TSF_FAIL_EXIST: int
TSF_RENAME_EXIST: int
TSF_OVERWRITE_EXIST: int
TSF_ALLOW_DECRYPTION: int
TSF_NO_SECURITY: int
TSF_COPY_CREATION_TIME: int
TSF_COPY_WRITE_TIME: int
TSF_USE_FULL_ACCESS: int
TSF_DELETE_RECYCLE_IF_POSSIBLE: int
TSF_COPY_HARD_LINK: int
TSF_COPY_LOCALIZED_NAME: int
TSF_MOVE_AS_COPY_DELETE: int
TSF_SUSPEND_SHELLEVENTS: int
TS_NONE: int
TS_PERFORMING: int
TS_PREPARING: int
TS_INDETERMINATE: int
COPYENGINE_S_YES: int
COPYENGINE_S_NOT_HANDLED: int
COPYENGINE_S_USER_RETRY: int
COPYENGINE_S_USER_IGNORED: int
COPYENGINE_S_MERGE: int
COPYENGINE_S_DONT_PROCESS_CHILDREN: int
COPYENGINE_S_ALREADY_DONE: int
COPYENGINE_S_PENDING: int
COPYENGINE_S_KEEP_BOTH: int
COPYENGINE_S_CLOSE_PROGRAM: int
COPYENGINE_S_COLLISIONRESOLVED: int
COPYENGINE_E_USER_CANCELLED: int
COPYENGINE_E_CANCELLED: int
COPYENGINE_E_REQUIRES_ELEVATION: int
COPYENGINE_E_SAME_FILE: int
COPYENGINE_E_DIFF_DIR: int
COPYENGINE_E_MANY_SRC_1_DEST: int
COPYENGINE_E_DEST_SUBTREE: int
COPYENGINE_E_DEST_SAME_TREE: int
COPYENGINE_E_FLD_IS_FILE_DEST: int
COPYENGINE_E_FILE_IS_FLD_DEST: int
COPYENGINE_E_FILE_TOO_LARGE: int
COPYENGINE_E_REMOVABLE_FULL: int
COPYENGINE_E_DEST_IS_RO_CD: int
COPYENGINE_E_DEST_IS_RW_CD: int
COPYENGINE_E_DEST_IS_R_CD: int
COPYENGINE_E_DEST_IS_RO_DVD: int
COPYENGINE_E_DEST_IS_RW_DVD: int
COPYENGINE_E_DEST_IS_R_DVD: int
COPYENGINE_E_SRC_IS_RO_CD: int
COPYENGINE_E_SRC_IS_RW_CD: int
COPYENGINE_E_SRC_IS_R_CD: int
COPYENGINE_E_SRC_IS_RO_DVD: int
COPYENGINE_E_SRC_IS_RW_DVD: int
COPYENGINE_E_SRC_IS_R_DVD: int
COPYENGINE_E_INVALID_FILES_SRC: int
COPYENGINE_E_INVALID_FILES_DEST: int
COPYENGINE_E_PATH_TOO_DEEP_SRC: int
COPYENGINE_E_PATH_TOO_DEEP_DEST: int
COPYENGINE_E_ROOT_DIR_SRC: int
COPYENGINE_E_ROOT_DIR_DEST: int
COPYENGINE_E_ACCESS_DENIED_SRC: int
COPYENGINE_E_ACCESS_DENIED_DEST: int
COPYENGINE_E_PATH_NOT_FOUND_SRC: int
COPYENGINE_E_PATH_NOT_FOUND_DEST: int
COPYENGINE_E_NET_DISCONNECT_SRC: int
COPYENGINE_E_NET_DISCONNECT_DEST: int
COPYENGINE_E_SHARING_VIOLATION_SRC: int
COPYENGINE_E_SHARING_VIOLATION_DEST: int
COPYENGINE_E_ALREADY_EXISTS_NORMAL: int
COPYENGINE_E_ALREADY_EXISTS_READONLY: int
COPYENGINE_E_ALREADY_EXISTS_SYSTEM: int
COPYENGINE_E_ALREADY_EXISTS_FOLDER: int
COPYENGINE_E_STREAM_LOSS: int
COPYENGINE_E_EA_LOSS: int
COPYENGINE_E_PROPERTY_LOSS: int
COPYENGINE_E_PROPERTIES_LOSS: int
COPYENGINE_E_ENCRYPTION_LOSS: int
COPYENGINE_E_DISK_FULL: int
COPYENGINE_E_DISK_FULL_CLEAN: int
COPYENGINE_E_EA_NOT_SUPPORTED: int
COPYENGINE_E_CANT_REACH_SOURCE: int
COPYENGINE_E_RECYCLE_UNKNOWN_ERROR: int
COPYENGINE_E_RECYCLE_FORCE_NUKE: int
COPYENGINE_E_RECYCLE_SIZE_TOO_BIG: int
COPYENGINE_E_RECYCLE_PATH_TOO_LONG: int
COPYENGINE_E_RECYCLE_BIN_NOT_FOUND: int
COPYENGINE_E_NEWFILE_NAME_TOO_LONG: int
COPYENGINE_E_NEWFOLDER_NAME_TOO_LONG: int
COPYENGINE_E_DIR_NOT_EMPTY: int
COPYENGINE_E_FAT_MAX_IN_ROOT: int
COPYENGINE_E_ACCESSDENIED_READONLY: int
COPYENGINE_E_REDIRECTED_TO_WEBPAGE: int
COPYENGINE_E_SERVER_BAD_FILE_TYPE: int
FOLDERID_NetworkFolder: str
FOLDERID_ComputerFolder: str
FOLDERID_InternetFolder: str
FOLDERID_ControlPanelFolder: str
FOLDERID_PrintersFolder: str
FOLDERID_SyncManagerFolder: str
FOLDERID_SyncSetupFolder: str
FOLDERID_ConflictFolder: str
FOLDERID_SyncResultsFolder: str
FOLDERID_RecycleBinFolder: str
FOLDERID_ConnectionsFolder: str
FOLDERID_Fonts: str
FOLDERID_Desktop: str
FOLDERID_Startup: str
FOLDERID_Programs: str
FOLDERID_StartMenu: str
FOLDERID_Recent: str
FOLDERID_SendTo: str
FOLDERID_Documents: str
FOLDERID_Favorites: str
FOLDERID_NetHood: str
FOLDERID_PrintHood: str
FOLDERID_Templates: str
FOLDERID_CommonStartup: str
FOLDERID_CommonPrograms: str
FOLDERID_CommonStartMenu: str
FOLDERID_PublicDesktop: str
FOLDERID_ProgramData: str
FOLDERID_CommonTemplates: str
FOLDERID_PublicDocuments: str
FOLDERID_RoamingAppData: str
FOLDERID_LocalAppData: str
FOLDERID_LocalAppDataLow: str
FOLDERID_InternetCache: str
FOLDERID_Cookies: str
FOLDERID_History: str
FOLDERID_System: str
FOLDERID_SystemX86: str
FOLDERID_Windows: str
FOLDERID_Profile: str
FOLDERID_Pictures: str
FOLDERID_ProgramFilesX86: str
FOLDERID_ProgramFilesCommonX86: str
FOLDERID_ProgramFilesX64: str
FOLDERID_ProgramFilesCommonX64: str
FOLDERID_ProgramFiles: str
FOLDERID_ProgramFilesCommon: str
FOLDERID_UserProgramFiles: str
FOLDERID_UserProgramFilesCommon: str
FOLDERID_AdminTools: str
FOLDERID_CommonAdminTools: str
FOLDERID_Music: str
FOLDERID_Videos: str
FOLDERID_Ringtones: str
FOLDERID_PublicPictures: str
FOLDERID_PublicMusic: str
FOLDERID_PublicVideos: str
FOLDERID_PublicRingtones: str
FOLDERID_ResourceDir: str
FOLDERID_LocalizedResourcesDir: str
FOLDERID_CommonOEMLinks: str
FOLDERID_CDBurning: str
FOLDERID_UserProfiles: str
FOLDERID_Playlists: str
FOLDERID_SamplePlaylists: str
FOLDERID_SampleMusic: str
FOLDERID_SamplePictures: str
FOLDERID_SampleVideos: str
FOLDERID_PhotoAlbums: str
FOLDERID_Public: str
FOLDERID_ChangeRemovePrograms: str
FOLDERID_AppUpdates: str
FOLDERID_AddNewPrograms: str
FOLDERID_Downloads: str
FOLDERID_PublicDownloads: str
FOLDERID_SavedSearches: str
FOLDERID_QuickLaunch: str
FOLDERID_Contacts: str
FOLDERID_SidebarParts: str
FOLDERID_SidebarDefaultParts: str
FOLDERID_PublicGameTasks: str
FOLDERID_GameTasks: str
FOLDERID_SavedGames: str
FOLDERID_Games: str
FOLDERID_SEARCH_MAPI: str
FOLDERID_SEARCH_CSC: str
FOLDERID_Links: str
FOLDERID_UsersFiles: str
FOLDERID_UsersLibraries: str
FOLDERID_SearchHome: str
FOLDERID_OriginalImages: str
FOLDERID_DocumentsLibrary: str
FOLDERID_MusicLibrary: str
FOLDERID_PicturesLibrary: str
FOLDERID_VideosLibrary: str
FOLDERID_RecordedTVLibrary: str
FOLDERID_HomeGroup: str
FOLDERID_HomeGroupCurrentUser: str
FOLDERID_DeviceMetadataStore: str
FOLDERID_Libraries: str
FOLDERID_PublicLibraries: str
FOLDERID_UserPinned: str
FOLDERID_ImplicitAppShortcuts: str
FOLDERID_AccountPictures: str
FOLDERID_PublicUserTiles: str
FOLDERID_AppsFolder: str
FOLDERID_StartMenuAllPrograms: str
FOLDERID_CommonStartMenuPlaces: str
FOLDERID_ApplicationShortcuts: str
FOLDERID_RoamingTiles: str
FOLDERID_RoamedTileImages: str
FOLDERID_Screenshots: str
FOLDERID_CameraRoll: str
FOLDERID_SkyDrive: str
FOLDERID_OneDrive: str
FOLDERID_SkyDriveDocuments: str
FOLDERID_SkyDrivePictures: str
FOLDERID_SkyDriveMusic: str
FOLDERID_SkyDriveCameraRoll: str
FOLDERID_SearchHistory: str
FOLDERID_SearchTemplates: str
FOLDERID_CameraRollLibrary: str
FOLDERID_SavedPictures: str
FOLDERID_SavedPicturesLibrary: str
FOLDERID_RetailDemo: str
FOLDERID_Device: str
FOLDERID_DevelopmentFiles: str
FOLDERID_Objects3D: str
FOLDERID_AppCaptures: str
FOLDERID_LocalDocuments: str
FOLDERID_LocalPictures: str
FOLDERID_LocalVideos: str
FOLDERID_LocalMusic: str
FOLDERID_LocalDownloads: str
FOLDERID_RecordedCalls: str
KF_FLAG_DEFAULT: int
KF_FLAG_FORCE_APP_DATA_REDIRECTION: int
KF_FLAG_RETURN_FILTER_REDIRECTION_TARGET: int
KF_FLAG_FORCE_PACKAGE_REDIRECTION: int
KF_FLAG_NO_PACKAGE_REDIRECTION: int
KF_FLAG_FORCE_APPCONTAINER_REDIRECTION: int
KF_FLAG_NO_APPCONTAINER_REDIRECTION: int
KF_FLAG_ALIAS_ONLY: int
