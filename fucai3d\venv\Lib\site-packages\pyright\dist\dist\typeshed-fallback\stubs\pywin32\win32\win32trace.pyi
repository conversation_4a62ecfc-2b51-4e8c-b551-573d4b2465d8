from win32.lib.pywintypes import error as error

def GetHandle(*args): ...  # incomplete
def GetTracer(): ...
def InitRead(*args): ...  # incomplete
def InitWrite(*args): ...  # incomplete
def TermRead(*args): ...  # incomplete
def TermWrite(*args): ...  # incomplete
def blockingread(*args): ...  # incomplete
def flush(*args): ...  # incomplete
def read(*args): ...  # incomplete
def setprint(*args): ...  # incomplete
def write(*args): ...  # incomplete
