import argparse
from _typeshed import Incomplete

from ..statistics import Statistics
from ..violation import Violation as Violation

class BaseFormatter:
    options: Incomplete
    filename: Incomplete
    output_fd: Incomplete
    newline: str
    color: Incomplete
    def __init__(self, options: argparse.Namespace) -> None: ...
    def after_init(self) -> None: ...
    def beginning(self, filename: str) -> None: ...
    def finished(self, filename: str) -> None: ...
    def start(self) -> None: ...
    def handle(self, error: Violation) -> None: ...
    def format(self, error: Violation) -> str | None: ...
    def show_statistics(self, statistics: Statistics) -> None: ...
    def show_benchmarks(self, benchmarks: list[tuple[str, float]]) -> None: ...
    def show_source(self, error: Violation) -> str | None: ...
    def write(self, line: str | None, source: str | None) -> None: ...
    def _write(self, output: str) -> None: ...
    def stop(self) -> None: ...
