import datetime
from typing import overload

@overload
def list_to_scope(scope: None) -> None: ...
@overload
def list_to_scope(scope: str | set[object] | tuple[object] | list[object]) -> str: ...
@overload
def scope_to_list(scope: None) -> None: ...
@overload
def scope_to_list(scope: str | set[object] | tuple[object] | list[object]) -> list[str]: ...
def params_from_uri(uri: str) -> dict[str, str | list[str]]: ...
def host_from_uri(uri: str) -> tuple[str, str | None]: ...
def escape(u: str) -> str: ...
def generate_age(issue_time: datetime.datetime | datetime.timedelta) -> str: ...
def is_secure_transport(uri: str) -> bool: ...
