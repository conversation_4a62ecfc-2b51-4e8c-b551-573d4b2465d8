#!/usr/bin/env python3
"""
P2核心模块简化验证脚本
快速验证P2系统的核心功能是否正常

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_core_modules():
    """测试核心模块导入和基本功能"""
    print("🔧 测试P2核心模块...")
    
    results = {}
    
    # 1. 测试AdvancedFeatureEngineer
    try:
        from src.data.advanced_feature_engineer import AdvancedFeatureEngineer
        engineer = AdvancedFeatureEngineer("data/lottery.db")
        feature_types = engineer.get_available_feature_types()
        results['AdvancedFeatureEngineer'] = f"✅ 成功 - 支持{len(feature_types)}种特征类型"
    except Exception as e:
        results['AdvancedFeatureEngineer'] = f"❌ 失败 - {str(e)[:50]}..."
    
    # 2. 测试CacheOptimizer
    try:
        from src.data.cache_optimizer import CacheOptimizer, CacheConfig
        config = CacheConfig(memory_size=10)
        optimizer = CacheOptimizer(config)
        optimizer.cache_features("test", {"data": 1})
        cached = optimizer.get_cached_features("test")
        results['CacheOptimizer'] = "✅ 成功 - 缓存读写正常"
    except Exception as e:
        results['CacheOptimizer'] = f"❌ 失败 - {str(e)[:50]}..."
    
    # 3. 测试FeatureImportanceAnalyzer
    try:
        from src.data.feature_importance import FeatureImportanceAnalyzer
        analyzer = FeatureImportanceAnalyzer()
        results['FeatureImportanceAnalyzer'] = "✅ 成功 - 初始化正常"
    except Exception as e:
        results['FeatureImportanceAnalyzer'] = f"❌ 失败 - {str(e)[:50]}..."
    
    # 4. 测试PredictorFeatureInterface
    try:
        from src.interfaces.predictor_feature_interface import PredictorFeatureInterface
        interface = PredictorFeatureInterface("data/lottery.db")
        cache_stats = interface.get_cache_stats()
        results['PredictorFeatureInterface'] = "✅ 成功 - 接口初始化正常"
    except Exception as e:
        results['PredictorFeatureInterface'] = f"❌ 失败 - {str(e)[:50]}..."
    
    # 5. 测试专用特征生成器
    try:
        from src.data.predictor_features.hundreds_features import generate_hundreds_features
        from src.data.predictor_features.tens_features import generate_tens_features
        from src.data.predictor_features.units_features import generate_units_features
        from src.data.predictor_features.sum_features import generate_sum_features
        from src.data.predictor_features.span_features import generate_span_features
        from src.data.predictor_features.common_features import generate_common_features
        results['专用特征生成器'] = "✅ 成功 - 6个生成器全部可用"
    except Exception as e:
        results['专用特征生成器'] = f"❌ 失败 - {str(e)[:50]}..."
    
    # 6. 测试API集成
    try:
        from src.api.v2.advanced_features import init_advanced_features_api
        init_advanced_features_api("data/lottery.db")
        results['API集成'] = "✅ 成功 - API初始化正常"
    except Exception as e:
        results['API集成'] = f"❌ 失败 - {str(e)[:50]}..."
    
    return results

def test_data_access():
    """测试数据访问"""
    print("🔧 测试数据访问...")
    
    try:
        import sqlite3
        db_path = "data/lottery.db"
        
        if not os.path.exists(db_path):
            return "❌ 数据库文件不存在"
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查数据表
        cursor.execute("SELECT COUNT(*) FROM lottery_data")
        count = cursor.fetchone()[0]
        
        # 检查最新数据
        cursor.execute("SELECT issue, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 1")
        latest = cursor.fetchone()
        
        conn.close()
        
        return f"✅ 数据库正常 - {count}条记录，最新期号: {latest[0]}"
        
    except Exception as e:
        return f"❌ 数据访问失败 - {str(e)[:50]}..."

def test_feature_generation():
    """测试特征生成"""
    print("🔧 测试特征生成...")
    
    try:
        from src.data.advanced_feature_engineer import AdvancedFeatureEngineer
        
        engineer = AdvancedFeatureEngineer("data/lottery.db")
        
        # 测试获取单期特征
        start_time = time.time()
        features = engineer.get_features_with_cache("2025001", "hundreds")
        end_time = time.time()
        
        if features and len(features) > 0:
            return f"✅ 特征生成正常 - {len(features)}个特征，耗时{end_time-start_time:.3f}s"
        else:
            return "⚠️ 特征生成返回空结果"
            
    except Exception as e:
        return f"❌ 特征生成失败 - {str(e)[:50]}..."

def main():
    """主函数"""
    print("🚀 P2核心模块简化验证")
    print("=" * 50)
    
    # 测试核心模块
    print("\n📦 核心模块测试:")
    module_results = test_core_modules()
    for module, result in module_results.items():
        print(f"   {module}: {result}")
    
    # 测试数据访问
    print("\n📊 数据访问测试:")
    data_result = test_data_access()
    print(f"   数据库: {data_result}")
    
    # 测试特征生成
    print("\n🔧 特征生成测试:")
    feature_result = test_feature_generation()
    print(f"   特征生成: {feature_result}")
    
    # 统计结果
    success_count = sum(1 for result in module_results.values() if result.startswith("✅"))
    total_count = len(module_results)
    
    print("\n" + "=" * 50)
    print("📊 验证结果总结:")
    print(f"   核心模块: {success_count}/{total_count} 成功")
    print(f"   数据访问: {'✅' if data_result.startswith('✅') else '❌'}")
    print(f"   特征生成: {'✅' if feature_result.startswith('✅') else '❌'}")
    
    overall_success = (success_count == total_count and 
                      data_result.startswith('✅') and 
                      feature_result.startswith('✅'))
    
    if overall_success:
        print("\n🎉 P2核心模块验证全部通过！")
        print("✅ P2高级特征工程系统完全可用")
        return True
    else:
        print("\n⚠️ 部分验证失败，但核心功能基本可用")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
