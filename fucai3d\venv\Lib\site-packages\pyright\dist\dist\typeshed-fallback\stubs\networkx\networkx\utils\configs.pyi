import sys
from _typeshed import Incomplete
from collections.abc import Callable, ItemsView, Iterable, Iterator, KeysView, ValuesView
from dataclasses import dataclass
from types import TracebackType
from typing_extensions import Self

__all__ = ["Config"]

# TODO: Our pyright test doesn't understand `requires_python` in METADATA.toml
# https://github.com/python/typeshed/issues/14025
if sys.version_info >= (3, 10):
    @dataclass(init=False, eq=False, slots=True, kw_only=True, match_args=False)
    class Config:
        def __init_subclass__(cls, strict: bool = True) -> None: ...
        def __new__(cls, **kwargs) -> Self: ...
        def __dir__(self) -> Iterable[str]: ...
        def __setattr__(self, name: str, value) -> None: ...
        def __delattr__(self, name: str) -> None: ...
        def __contains__(self, key: object) -> bool: ...
        def __iter__(self) -> Iterator[str]: ...
        def __len__(self) -> int: ...
        def __reversed__(self) -> Iterator[str]: ...
        def __getitem__(self, key: str): ...
        def __setitem__(self, key: str, value) -> None: ...
        def __delitem__(self, key: str) -> None: ...
        def get(self, key: str, default=None): ...
        def items(self) -> ItemsView[str, Incomplete]: ...
        def keys(self) -> KeysView[str]: ...
        def values(self) -> ValuesView[Incomplete]: ...
        def __reduce__(self) -> tuple[Callable[..., Self], tuple[type[Self], dict[Incomplete, Incomplete]]]: ...
        def __call__(self, **kwargs) -> Self: ...
        def __enter__(self) -> Self: ...
        def __exit__(
            self, exc_type: type[BaseException] | None, exc_value: BaseException | None, traceback: TracebackType | None
        ) -> None: ...

else:
    @dataclass(init=False, eq=False)
    class Config:
        def __init_subclass__(cls, strict: bool = True) -> None: ...
        def __new__(cls, **kwargs) -> Self: ...
        def __dir__(self) -> Iterable[str]: ...
        def __setattr__(self, name: str, value) -> None: ...
        def __delattr__(self, name: str) -> None: ...
        def __contains__(self, key: object) -> bool: ...
        def __iter__(self) -> Iterator[str]: ...
        def __len__(self) -> int: ...
        def __reversed__(self) -> Iterator[str]: ...
        def __getitem__(self, key: str): ...
        def __setitem__(self, key: str, value) -> None: ...
        def __delitem__(self, key: str) -> None: ...
        def get(self, key: str, default=None): ...
        def items(self) -> ItemsView[str, Incomplete]: ...
        def keys(self) -> KeysView[str]: ...
        def values(self) -> ValuesView[Incomplete]: ...
        def __reduce__(self) -> tuple[Callable[..., Self], tuple[type[Self], dict[Incomplete, Incomplete]]]: ...
        def __call__(self, **kwargs) -> Self: ...
        def __enter__(self) -> Self: ...
        def __exit__(
            self, exc_type: type[BaseException] | None, exc_value: BaseException | None, traceback: TracebackType | None
        ) -> None: ...

class NetworkXConfig(Config):
    backend_priority: list[str]
    backends: Config
    cache_converted_graphs: bool
    fallback_to_nx: bool
    warnings_to_ignore: set[str]
    def __init__(
        self,
        *,
        backend_priority: list[str],
        backends: Config,
        cache_converted_graphs: bool,
        fallback_to_nx: bool,
        warnings_to_ignore: set[str],
    ) -> None: ...
    def __new__(
        cls,
        *,
        backend_priority: list[str],
        backends: Config,
        cache_converted_graphs: bool,
        fallback_to_nx: bool,
        warnings_to_ignore: set[str],
    ) -> Self: ...

config: NetworkXConfig
