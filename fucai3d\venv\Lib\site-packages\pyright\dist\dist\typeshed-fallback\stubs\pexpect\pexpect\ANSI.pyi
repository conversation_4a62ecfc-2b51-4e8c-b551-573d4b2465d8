from _typeshed import Incomplete

from . import screen

def DoEmit(fsm) -> None: ...
def DoStartNumber(fsm) -> None: ...
def DoBuildNumber(fsm) -> None: ...
def DoBackOne(fsm) -> None: ...
def DoBack(fsm) -> None: ...
def DoDownOne(fsm) -> None: ...
def DoDown(fsm) -> None: ...
def DoForwardOne(fsm) -> None: ...
def DoForward(fsm) -> None: ...
def DoUpReverse(fsm) -> None: ...
def DoUpOne(fsm) -> None: ...
def DoUp(fsm) -> None: ...
def DoHome(fsm) -> None: ...
def DoHomeOrigin(fsm) -> None: ...
def DoEraseDown(fsm) -> None: ...
def DoErase(fsm) -> None: ...
def DoEraseEndOfLine(fsm) -> None: ...
def DoEraseLine(fsm) -> None: ...
def DoEnableScroll(fsm) -> None: ...
def DoCursorSave(fsm) -> None: ...
def DoCursorRestore(fsm) -> None: ...
def DoScrollRegion(fsm) -> None: ...
def DoMode(fsm) -> None: ...
def DoLog(fsm) -> None: ...

class term(screen.screen):
    def __init__(self, r: int = 24, c: int = 80, *args, **kwargs) -> None: ...

class ANSI(term):
    state: Incomplete
    def __init__(self, r: int = 24, c: int = 80, *args, **kwargs) -> None: ...
    def process(self, c) -> None: ...
    def process_list(self, l) -> None: ...
    def write(self, s) -> None: ...
    def flush(self) -> None: ...
    def write_ch(self, ch) -> None: ...
    def do_sgr(self, fsm) -> None: ...
    def do_decsca(self, fsm) -> None: ...
    def do_modecrap(self, fsm) -> None: ...
