from typing import Any

RESULT_SUCCESS: int
RESULT_OPERATIONS_ERROR: int
RESULT_PROTOCOL_ERROR: int
RESULT_TIME_LIMIT_EXCEEDED: int
RESULT_SIZE_LIMIT_EXCEEDED: int
RESULT_COMPARE_FALSE: int
RESULT_COMPARE_TRUE: int
RESULT_AUTH_METHOD_NOT_SUPPORTED: int
RESULT_STRONGER_AUTH_REQUIRED: int
RESULT_RESERVED: int
RESULT_REFERRAL: int
RESULT_ADMIN_LIMIT_EXCEEDED: int
RESULT_UNAVAILABLE_CRITICAL_EXTENSION: int
RESULT_CONFIDENTIALITY_REQUIRED: int
RESULT_SASL_BIND_IN_PROGRESS: int
RESULT_NO_SUCH_ATTRIBUTE: int
RESULT_UNDEFINED_ATTRIBUTE_TYPE: int
RESULT_INAPPROPRIATE_MATCHING: int
RESULT_CONSTRAINT_VIOLATION: int
RESULT_ATTRIBUTE_OR_VALUE_EXISTS: int
RESULT_INVALID_ATTRIBUTE_SYNTAX: int
RESULT_NO_SUCH_OBJECT: int
RESULT_ALIAS_PROBLEM: int
RESULT_INVALID_DN_SYNTAX: int
RESULT_ALIAS_DEREFERENCING_PROBLEM: int
RESULT_INAPPROPRIATE_AUTHENTICATION: int
RESULT_INVALID_CREDENTIALS: int
RESULT_INSUFFICIENT_ACCESS_RIGHTS: int
RESULT_BUSY: int
RESULT_UNAVAILABLE: int
RESULT_UNWILLING_TO_PERFORM: int
RESULT_LOOP_DETECTED: int
RESULT_NAMING_VIOLATION: int
RESULT_OBJECT_CLASS_VIOLATION: int
RESULT_NOT_ALLOWED_ON_NON_LEAF: int
RESULT_NOT_ALLOWED_ON_RDN: int
RESULT_ENTRY_ALREADY_EXISTS: int
RESULT_OBJECT_CLASS_MODS_PROHIBITED: int
RESULT_AFFECT_MULTIPLE_DSAS: int
RESULT_OTHER: int
RESULT_LCUP_RESOURCES_EXHAUSTED: int
RESULT_LCUP_SECURITY_VIOLATION: int
RESULT_LCUP_INVALID_DATA: int
RESULT_LCUP_UNSUPPORTED_SCHEME: int
RESULT_LCUP_RELOAD_REQUIRED: int
RESULT_CANCELED: int
RESULT_NO_SUCH_OPERATION: int
RESULT_TOO_LATE: int
RESULT_CANNOT_CANCEL: int
RESULT_ASSERTION_FAILED: int
RESULT_AUTHORIZATION_DENIED: int
RESULT_E_SYNC_REFRESH_REQUIRED: int
RESULT_CODES: Any
DO_NOT_RAISE_EXCEPTIONS: Any
