from _typeshed import Incomplete
from threading import Thread

__all__ = ["TMonitor", "TqdmSynchronisationWarning"]

class TqdmSynchronisationWarning(RuntimeWarning): ...

class TMonitor(Thread):
    daemon: bool
    woken: int
    tqdm_cls: type[Incomplete]
    sleep_interval: float
    was_killed: Incomplete
    def __init__(self, tqdm_cls: type[Incomplete], sleep_interval: float) -> None: ...
    def exit(self): ...
    def get_instances(self): ...
    def run(self) -> None: ...
    def report(self): ...
