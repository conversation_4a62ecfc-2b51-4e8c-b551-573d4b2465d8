from _typeshed import Incomplete
from logging import Logger

from pika.adapters.base_connection import BaseConnection
from pika.adapters.utils.nbio_interface import AbstractIOReference
from pika.adapters.utils.selector_ioloop_adapter import AbstractSelectorIOLoop, SelectorIOServicesAdapter

LOGGER: Logger

class GeventConnection(BaseConnection):
    def __init__(
        self,
        parameters: Incomplete | None = ...,
        on_open_callback: Incomplete | None = ...,
        on_open_error_callback: Incomplete | None = ...,
        on_close_callback: Incomplete | None = ...,
        custom_ioloop: Incomplete | None = ...,
        internal_connection_workflow: bool = ...,
    ) -> None: ...
    @classmethod
    def create_connection(
        cls, connection_configs, on_done, custom_ioloop: Incomplete | None = ..., workflow: Incomplete | None = ...
    ): ...

class _TSafeCallbackQueue:
    def __init__(self) -> None: ...
    @property
    def fd(self): ...
    def add_callback_threadsafe(self, callback) -> None: ...
    def run_next_callback(self) -> None: ...

class _GeventSelectorIOLoop(AbstractSelectorIOLoop):
    READ: int
    WRITE: int
    ERROR: int
    def __init__(self, gevent_hub: Incomplete | None = ...) -> None: ...
    def close(self) -> None: ...
    def start(self) -> None: ...
    def stop(self) -> None: ...
    def add_callback(self, callback) -> None: ...
    def call_later(self, delay, callback): ...
    def remove_timeout(self, timeout_handle) -> None: ...
    def add_handler(self, fd, handler, events) -> None: ...
    def update_handler(self, fd, events) -> None: ...
    def remove_handler(self, fd) -> None: ...

class _GeventSelectorIOServicesAdapter(SelectorIOServicesAdapter):
    def getaddrinfo(self, host, port, on_done, family: int = ..., socktype: int = ..., proto: int = ..., flags: int = ...): ...

class _GeventIOLoopIOHandle(AbstractIOReference):
    def __init__(self, subject) -> None: ...
    def cancel(self): ...

class _GeventAddressResolver:
    def __init__(self, native_loop, host, port, family, socktype, proto, flags, on_done) -> None: ...
    def start(self) -> None: ...
    def cancel(self): ...
