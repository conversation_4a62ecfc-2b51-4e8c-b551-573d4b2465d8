def nullTransform(): ...
def translate(dx: float, dy: float = 0): ...
def scale(sx: float, sy: float = 1): ...
def rotate(angle: float, cx: float = 0, cy: float = 0): ...
def skewX(angle: float): ...
def skewY(angle: float): ...
def mmult(A, B): ...
def inverse(A): ...
def zTransformPoint(A, v): ...
def transformPoint(A, v): ...
def transformPoints(matrix, V): ...
def zTransformPoints(matrix, V): ...

__all__ = (
    "nullTransform",
    "translate",
    "scale",
    "rotate",
    "skewX",
    "skewY",
    "mmult",
    "inverse",
    "zTransformPoint",
    "transformPoint",
    "transformPoints",
    "zTransformPoints",
)
