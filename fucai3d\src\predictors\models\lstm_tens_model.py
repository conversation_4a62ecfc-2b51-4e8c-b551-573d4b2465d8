#!/usr/bin/env python3
"""
LSTM百位预测模型

基于LSTM实现的百位数字预测模型，专注于独立位置预测。

特点：
- 深度学习序列模型
- 时间序列特征学习
- 支持多分类概率输出
- 早停和学习率调整
- 序列长度可配置

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
from pathlib import Path
import numpy as np
import logging
import pickle
import time
from typing import Dict, Tuple, Optional, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, callbacks
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
    from sklearn.preprocessing import LabelEncoder, StandardScaler
except ImportError as e:
    print(f"警告: 缺少必要的深度学习库: {e}")
    print("请安装: pip install tensorflow scikit-learn")

# 导入基类
try:
    from src.predictors.base_independent_predictor import BaseIndependentPredictor
    from config.config_loader import get_config
except ImportError as e:
    print(f"警告: 无法导入基类或配置: {e}")

class LSTMTensModel(BaseIndependentPredictor):
    """LSTM十位预测模型"""
    
    def __init__(self, db_path: str):
        """
        初始化LSTM十位预测模型
        
        Args:
            db_path: 数据库路径
        """
        super().__init__("tens", db_path)
        
        # 加载LSTM配置
        self._load_lstm_config()
        
        # 初始化模型相关属性
        self.lstm_model = None
        self.label_encoder = LabelEncoder()
        self.feature_scaler = StandardScaler()
        self.training_metrics = {}
        self.sequence_length = self.lstm_config.get('sequence_length', 10)
        
        self.logger.info("LSTM十位预测模型初始化完成")
    
    def _load_lstm_config(self):
        """加载LSTM配置"""
        try:
            config_loader = get_config()
            self.lstm_config = config_loader.get_model_config('lstm')
        except Exception as e:
            self.logger.warning(f"配置加载失败，使用默认LSTM配置: {e}")
            self.lstm_config = {
                'sequence_length': 10,
                'hidden_units': [64, 32],
                'dropout_rate': 0.2,
                'activation': 'relu',
                'output_activation': 'softmax',
                'optimizer': 'adam',
                'learning_rate': 0.001,
                'batch_size': 32,
                'epochs': 100,
                'validation_split': 0.2,
                'early_stopping_patience': 10,
                'reduce_lr_patience': 5,
                'reduce_lr_factor': 0.5
            }
    
    def build_model(self) -> keras.Model:
        """
        构建LSTM模型
        
        Returns:
            Keras模型
        """
        try:
            # 获取配置参数
            hidden_units = self.lstm_config['hidden_units']
            dropout_rate = self.lstm_config['dropout_rate']
            activation = self.lstm_config['activation']
            output_activation = self.lstm_config['output_activation']
            learning_rate = self.lstm_config['learning_rate']
            
            # 构建模型架构
            model = keras.Sequential([
                # 输入层
                layers.Input(shape=(self.sequence_length, len(self.feature_names) if self.feature_names else 50)),
                
                # 第一个LSTM层
                layers.LSTM(hidden_units[0], return_sequences=True, activation=activation),
                layers.Dropout(dropout_rate),
                
                # 第二个LSTM层
                layers.LSTM(hidden_units[1], activation=activation),
                layers.Dropout(dropout_rate),
                
                # 全连接层
                layers.Dense(32, activation=activation),
                layers.Dropout(dropout_rate),
                
                # 输出层（10个数字的概率）
                layers.Dense(10, activation=output_activation)
            ])
            
            # 编译模型
            optimizer = keras.optimizers.Adam(learning_rate=learning_rate)
            model.compile(
                optimizer=optimizer,
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )
            
            self.lstm_model = model
            self.logger.info("LSTM模型构建完成")
            
            return model
            
        except Exception as e:
            self.logger.error(f"LSTM模型构建失败: {e}")
            raise
    
    def train(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练LSTM模型
        
        Args:
            X: 特征矩阵
            y: 目标向量
            
        Returns:
            训练结果字典
        """
        try:
            start_time = time.time()
            self.logger.info(f"开始训练LSTM模型，数据形状: X{X.shape}, y{y.shape}")
            
            # 构建模型
            if self.lstm_model is None:
                self.build_model()
            
            # 数据预处理
            X_processed, y_processed = self._preprocess_data(X, y)
            
            # 准备序列数据
            X_sequences, y_sequences = self._prepare_sequence_data(X_processed, y_processed)
            
            # 分割训练和验证集
            X_train, X_val, y_train, y_val = train_test_split(
                X_sequences, y_sequences,
                test_size=self.data_config.get('validation_split', 0.2),
                random_state=42,
                stratify=y_sequences
            )
            
            # 设置回调函数
            callback_list = [
                callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=self.lstm_config.get('early_stopping_patience', 10),
                    restore_best_weights=True
                ),
                callbacks.ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=self.lstm_config.get('reduce_lr_factor', 0.5),
                    patience=self.lstm_config.get('reduce_lr_patience', 5),
                    min_lr=1e-7
                )
            ]
            
            # 训练模型
            history = self.lstm_model.fit(
                X_train, y_train,
                batch_size=self.lstm_config.get('batch_size', 32),
                epochs=self.lstm_config.get('epochs', 100),
                validation_data=(X_val, y_val),
                callbacks=callback_list,
                verbose=0
            )
            
            # 计算训练指标
            train_pred = np.argmax(self.lstm_model.predict(X_train, verbose=0), axis=1)
            val_pred = np.argmax(self.lstm_model.predict(X_val, verbose=0), axis=1)
            
            train_accuracy = accuracy_score(y_train, train_pred)
            val_accuracy = accuracy_score(y_val, val_pred)
            
            training_time = time.time() - start_time
            
            # 保存训练指标
            self.training_metrics = {
                'train_accuracy': train_accuracy,
                'val_accuracy': val_accuracy,
                'training_time': training_time,
                'epochs_trained': len(history.history['loss']),
                'final_loss': history.history['loss'][-1],
                'final_val_loss': history.history['val_loss'][-1],
                'sequence_length': self.sequence_length
            }
            
            self.is_trained = True
            self.training_history['lstm'] = self.training_metrics
            
            self.logger.info(f"LSTM训练完成: 训练准确率={train_accuracy:.4f}, 验证准确率={val_accuracy:.4f}, 耗时={training_time:.2f}s")
            
            return self.training_metrics
            
        except Exception as e:
            self.logger.error(f"LSTM训练失败: {e}")
            raise
    
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """
        预测概率分布
        
        Args:
            X: 特征矩阵
            
        Returns:
            概率分布数组，shape: (n_samples, 10)
        """
        if not self.is_trained or self.lstm_model is None:
            raise ValueError("模型尚未训练")
        
        try:
            # 数据预处理
            X_processed = self._preprocess_features(X)
            
            # 准备序列数据
            X_sequences = self._prepare_prediction_sequences(X_processed)
            
            # 预测概率
            probabilities = self.lstm_model.predict(X_sequences, verbose=0)
            
            return probabilities
            
        except Exception as e:
            self.logger.error(f"LSTM预测失败: {e}")
            raise
    
    def _preprocess_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        数据预处理
        
        Args:
            X: 特征矩阵
            y: 目标向量
            
        Returns:
            预处理后的特征矩阵和目标向量
        """
        # 处理缺失值
        X_processed = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
        
        # 特征标准化
        X_processed = self.feature_scaler.fit_transform(X_processed)
        
        # 标签编码（确保标签是0-9的整数）
        y_processed = self.label_encoder.fit_transform(y)
        
        return X_processed, y_processed
    
    def _preprocess_features(self, X: np.ndarray) -> np.ndarray:
        """
        特征预处理（仅特征）
        
        Args:
            X: 特征矩阵
            
        Returns:
            预处理后的特征矩阵
        """
        X_processed = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
        return self.feature_scaler.transform(X_processed)
    
    def _prepare_sequence_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备序列数据
        
        Args:
            X: 特征矩阵
            y: 目标向量
            
        Returns:
            序列特征矩阵和目标向量
        """
        sequences = []
        targets = []
        
        for i in range(self.sequence_length, len(X)):
            sequences.append(X[i-self.sequence_length:i])
            targets.append(y[i])
        
        return np.array(sequences), np.array(targets)
    
    def _prepare_prediction_sequences(self, X: np.ndarray) -> np.ndarray:
        """
        准备预测序列数据
        
        Args:
            X: 特征矩阵
            
        Returns:
            序列特征矩阵
        """
        if len(X) < self.sequence_length:
            # 如果数据不足，用最后一行填充
            padding = np.tile(X[-1], (self.sequence_length - len(X), 1))
            X_padded = np.vstack([padding, X])
        else:
            X_padded = X
        
        # 取最后sequence_length行作为序列
        sequence = X_padded[-self.sequence_length:]
        return np.array([sequence])
    
    def save_model(self, filepath: Optional[str] = None) -> str:
        """
        保存LSTM模型
        
        Args:
            filepath: 保存路径
            
        Returns:
            实际保存路径
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，无法保存")
        
        if filepath is None:
            model_dir = Path(self.training_config.get('model_save_path', 'models/tens/'))
            model_dir.mkdir(parents=True, exist_ok=True)
            filepath = model_dir / "lstm_tens_model"
        
        try:
            # 保存Keras模型
            self.lstm_model.save(f"{filepath}.h5")
            
            # 保存其他组件
            model_data = {
                'label_encoder': self.label_encoder,
                'feature_scaler': self.feature_scaler,
                'feature_names': self.feature_names,
                'training_metrics': self.training_metrics,
                'config': self.lstm_config,
                'sequence_length': self.sequence_length
            }
            
            with open(f"{filepath}_components.pkl", 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"LSTM模型保存成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"LSTM模型保存失败: {e}")
            raise
    
    def load_model(self, filepath: str) -> bool:
        """
        加载LSTM模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            是否加载成功
        """
        try:
            # 加载Keras模型
            self.lstm_model = keras.models.load_model(f"{filepath}.h5")
            
            # 加载其他组件
            with open(f"{filepath}_components.pkl", 'rb') as f:
                model_data = pickle.load(f)
            
            self.label_encoder = model_data['label_encoder']
            self.feature_scaler = model_data['feature_scaler']
            self.feature_names = model_data['feature_names']
            self.training_metrics = model_data['training_metrics']
            self.sequence_length = model_data['sequence_length']
            
            self.is_trained = True
            
            self.logger.info(f"LSTM模型加载成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"LSTM模型加载失败: {e}")
            return False
