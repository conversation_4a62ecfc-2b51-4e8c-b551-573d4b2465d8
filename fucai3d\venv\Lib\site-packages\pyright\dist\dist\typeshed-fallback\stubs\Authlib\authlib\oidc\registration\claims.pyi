from _typeshed import Incomplete
from collections.abc import Mapping

from authlib.jose import BaseClaims

class ClientMetadataClaims(BaseClaims):
    def validate(self) -> None: ...
    # The "cls" argument is called "self" in the actual implementation,
    # but stubtest will not allow that.
    @classmethod
    def get_claims_options(cls, metadata: Mapping[str, Incomplete]) -> dict[str, Incomplete]: ...
    def validate_token_endpoint_auth_signing_alg(self) -> None: ...
    def validate_application_type(self) -> None: ...
    def validate_sector_identifier_uri(self) -> None: ...
    def validate_subject_type(self) -> None: ...
    def validate_id_token_signed_response_alg(self) -> None: ...
    def validate_id_token_encrypted_response_alg(self) -> None: ...
    def validate_id_token_encrypted_response_enc(self) -> None: ...
    def validate_userinfo_signed_response_alg(self) -> None: ...
    def validate_userinfo_encrypted_response_alg(self) -> None: ...
    def validate_userinfo_encrypted_response_enc(self) -> None: ...
    def validate_default_max_age(self) -> None: ...
    def validate_require_auth_time(self) -> None: ...
    def validate_default_acr_values(self) -> None: ...
    def validate_initiate_login_uri(self) -> None: ...
    def validate_request_object_signing_alg(self) -> None: ...
    def validate_request_object_encryption_alg(self) -> None: ...
    def validate_request_object_encryption_enc(self) -> None: ...
    def validate_request_uris(self) -> None: ...
