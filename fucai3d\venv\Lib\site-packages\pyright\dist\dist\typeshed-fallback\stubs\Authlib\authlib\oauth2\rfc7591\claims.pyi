from _typeshed import Incomplete
from collections.abc import Mapping
from typing import Any

from authlib.jose import BaseClaims

class ClientMetadataClaims(BaseClaims):
    def validate(self) -> None: ...
    def validate_redirect_uris(self) -> None: ...
    def validate_token_endpoint_auth_method(self) -> None: ...
    def validate_grant_types(self) -> None: ...
    def validate_response_types(self) -> None: ...
    def validate_client_name(self) -> None: ...
    def validate_client_uri(self) -> None: ...
    def validate_logo_uri(self) -> None: ...
    def validate_scope(self) -> None: ...
    def validate_contacts(self) -> None: ...
    def validate_tos_uri(self) -> None: ...
    def validate_policy_uri(self) -> None: ...
    def validate_jwks_uri(self) -> None: ...
    def validate_jwks(self) -> None: ...
    def validate_software_id(self) -> None: ...
    def validate_software_version(self) -> None: ...
    @classmethod
    def get_claims_options(cls, metadata: Mapping[str, Incomplete]) -> dict[str, Any]: ...  # dict values are key-dependent
