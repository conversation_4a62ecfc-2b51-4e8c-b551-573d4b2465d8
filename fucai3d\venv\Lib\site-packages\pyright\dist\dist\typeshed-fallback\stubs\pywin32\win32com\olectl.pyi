FACILITY_CONTROL: int

def MAKE_SCODE(sev: int, fac: int, code: int) -> int: ...
def STD_CTL_SCODE(n: int) -> int: ...

CTL_E_ILLEGALFUNCTIONCALL: int
CTL_E_OVERFLOW: int
CTL_E_OUTOFMEMORY: int
CTL_E_DIVISIONBYZERO: int
CTL_E_OUTOFSTRINGSPACE: int
CTL_E_OUTOFSTACKSPACE: int
CTL_E_BADFILENAMEORNUMBER: int
CTL_E_FILENOTFOUND: int
CTL_E_BADFILEMODE: int
CTL_E_FILEALREADYOPEN: int
CTL_E_DEVICEIOERROR: int
CTL_E_FILEALREADYEXISTS: int
CTL_E_BADRECORDLENGTH: int
CTL_E_DISKFULL: int
CTL_E_BADRECORDNUMBER: int
CTL_E_BADFILENAME: int
CTL_E_TOOMANYFILES: int
CTL_E_DEVICEUNAVAILABLE: int
CTL_E_PERMISSIONDENIED: int
CTL_E_DISKNOTREADY: int
CTL_E_PATHFILEACCESSERROR: int
CTL_E_PATHNOTFOUND: int
CTL_E_INVALIDPATTERNSTRING: int
CTL_E_INVALIDUSEOFNULL: int
CTL_E_INVALIDFILEFORMAT: int
CTL_E_INVALIDPROPERTYVALUE: int
CTL_E_INVALIDPROPERTYARRAYINDEX: int
CTL_E_SETNOTSUPPORTEDATRUNTIME: int
CTL_E_SETNOTSUPPORTED: int
CTL_E_NEEDPROPERTYARRAYINDEX: int
CTL_E_SETNOTPERMITTED: int
CTL_E_GETNOTSUPPORTEDATRUNTIME: int
CTL_E_GETNOTSUPPORTED: int
CTL_E_PROPERTYNOTFOUND: int
CTL_E_INVALIDCLIPBOARDFORMAT: int
CTL_E_INVALIDPICTURE: int
CTL_E_PRINTERERROR: int
CTL_E_CANTSAVEFILETOTEMP: int
CTL_E_SEARCHTEXTNOTFOUND: int
CTL_E_REPLACEMENTSTOOLONG: int
CONNECT_E_FIRST: int
CONNECT_E_LAST: int
CONNECT_S_FIRST: int
CONNECT_S_LAST: int
CONNECT_E_NOCONNECTION: int
CONNECT_E_ADVISELIMIT: int
CONNECT_E_CANNOTCONNECT: int
CONNECT_E_OVERRIDDEN: int
CLASS_E_NOTLICENSED: int
