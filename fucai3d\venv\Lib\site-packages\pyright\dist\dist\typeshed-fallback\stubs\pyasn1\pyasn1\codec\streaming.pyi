import io
from _typeshed import Incomplete
from collections.abc import Generator

from pyasn1 import error as error
from pyasn1.type import univ as univ

class CachingStreamWrapper(io.IOBase):
    def __init__(self, raw) -> None: ...
    def peek(self, n): ...
    def seekable(self): ...
    def seek(self, n: int = -1, whence=0): ...
    def read(self, n: int = -1): ...
    @property
    def markedPosition(self): ...
    def tell(self): ...

def asSeekableStream(substrate): ...
def isEndOfStream(substrate) -> Generator[Incomplete, None, None]: ...
def peekIntoStream(substrate, size: int = -1) -> Generator[Incomplete, None, None]: ...
def readFromStream(substrate, size: int = -1, context=None) -> Generator[Incomplete, None, None]: ...
