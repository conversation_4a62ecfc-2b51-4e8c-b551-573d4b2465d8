MV_FLAG: int
PT_UNSPECIFIED: int
PT_NULL: int
PT_I2: int
PT_LONG: int
PT_R4: int
PT_DOUBLE: int
PT_CURRENCY: int
PT_APPTIME: int
PT_ERROR: int
PT_BOOLEAN: int
PT_OBJECT: int
PT_I8: int
PT_STRING8: int
PT_UNICODE: int
PT_SYSTIME: int
PT_CLSID: int
PT_BINARY: int
PT_SHORT: int
PT_I4: int
PT_FLOAT: int
PT_R8: int
PT_LONGLONG: int
PT_MV_I2: int
PT_MV_LONG: int
PT_MV_R4: int
PT_MV_DOUBLE: int
PT_MV_CURRENCY: int
PT_MV_APPTIME: int
PT_MV_SYSTIME: int
PT_MV_STRING8: int
PT_MV_BINARY: int
PT_MV_UNICODE: int
PT_MV_CLSID: int
PT_MV_I8: int
PT_MV_SHORT: int
PT_MV_I4: int
PT_MV_FLOAT: int
PT_MV_R8: int
PT_MV_LONGLONG: int
PT_TSTRING: int
PT_MV_TSTRING: int
PROP_TYPE_MASK: int

def PROP_TYPE(ulPropTag: int) -> int: ...
def PROP_ID(ulPropTag: int) -> int: ...
def PROP_TAG(ulPropType: int, ulPropID: int) -> int: ...

PROP_ID_NULL: int
PROP_ID_INVALID: int
PR_NULL: int
PR_ACKNOWLEDGEMENT_MODE: int
PR_ALTERNATE_RECIPIENT_ALLOWED: int
PR_AUTHORIZING_USERS: int
PR_AUTO_FORWARD_COMMENT: int
PR_AUTO_FORWARD_COMMENT_W: int
PR_AUTO_FORWARD_COMMENT_A: int
PR_AUTO_FORWARDED: int
PR_CONTENT_CONFIDENTIALITY_ALGORITHM_ID: int
PR_CONTENT_CORRELATOR: int
PR_CONTENT_IDENTIFIER: int
PR_CONTENT_IDENTIFIER_W: int
PR_CONTENT_IDENTIFIER_A: int
PR_CONTENT_LENGTH: int
PR_CONTENT_RETURN_REQUESTED: int
PR_CONVERSATION_KEY: int
PR_CONVERSION_EITS: int
PR_CONVERSION_WITH_LOSS_PROHIBITED: int
PR_CONVERTED_EITS: int
PR_DEFERRED_DELIVERY_TIME: int
PR_DELIVER_TIME: int
PR_DISCARD_REASON: int
PR_DISCLOSURE_OF_RECIPIENTS: int
PR_DL_EXPANSION_HISTORY: int
PR_DL_EXPANSION_PROHIBITED: int
PR_EXPIRY_TIME: int
PR_IMPLICIT_CONVERSION_PROHIBITED: int
PR_IMPORTANCE: int
PR_IPM_ID: int
PR_LATEST_DELIVERY_TIME: int
PR_MESSAGE_CLASS: int
PR_MESSAGE_CLASS_W: int
PR_MESSAGE_CLASS_A: int
PR_MESSAGE_DELIVERY_ID: int
PR_MESSAGE_SECURITY_LABEL: int
PR_OBSOLETED_IPMS: int
PR_ORIGINALLY_INTENDED_RECIPIENT_NAME: int
PR_ORIGINAL_EITS: int
PR_ORIGINATOR_CERTIFICATE: int
PR_ORIGINATOR_DELIVERY_REPORT_REQUESTED: int
PR_ORIGINATOR_RETURN_ADDRESS: int
PR_PARENT_KEY: int
PR_PRIORITY: int
PR_ORIGIN_CHECK: int
PR_PROOF_OF_SUBMISSION_REQUESTED: int
PR_READ_RECEIPT_REQUESTED: int
PR_RECEIPT_TIME: int
PR_RECIPIENT_REASSIGNMENT_PROHIBITED: int
PR_REDIRECTION_HISTORY: int
PR_RELATED_IPMS: int
PR_ORIGINAL_SENSITIVITY: int
PR_LANGUAGES: int
PR_LANGUAGES_W: int
PR_LANGUAGES_A: int
PR_REPLY_TIME: int
PR_REPORT_TAG: int
PR_REPORT_TIME: int
PR_RETURNED_IPM: int
PR_SECURITY: int
PR_INCOMPLETE_COPY: int
PR_SENSITIVITY: int
PR_SUBJECT: int
PR_SUBJECT_W: int
PR_SUBJECT_A: int
PR_SUBJECT_IPM: int
PR_CLIENT_SUBMIT_TIME: int
PR_REPORT_NAME: int
PR_REPORT_NAME_W: int
PR_REPORT_NAME_A: int
PR_SENT_REPRESENTING_SEARCH_KEY: int
PR_X400_CONTENT_TYPE: int
PR_SUBJECT_PREFIX: int
PR_SUBJECT_PREFIX_W: int
PR_SUBJECT_PREFIX_A: int
PR_NON_RECEIPT_REASON: int
PR_RECEIVED_BY_ENTRYID: int
PR_RECEIVED_BY_NAME: int
PR_RECEIVED_BY_NAME_W: int
PR_RECEIVED_BY_NAME_A: int
PR_SENT_REPRESENTING_ENTRYID: int
PR_SENT_REPRESENTING_NAME: int
PR_SENT_REPRESENTING_NAME_W: int
PR_SENT_REPRESENTING_NAME_A: int
PR_RCVD_REPRESENTING_ENTRYID: int
PR_RCVD_REPRESENTING_NAME: int
PR_RCVD_REPRESENTING_NAME_W: int
PR_RCVD_REPRESENTING_NAME_A: int
PR_REPORT_ENTRYID: int
PR_READ_RECEIPT_ENTRYID: int
PR_MESSAGE_SUBMISSION_ID: int
PR_PROVIDER_SUBMIT_TIME: int
PR_ORIGINAL_SUBJECT: int
PR_ORIGINAL_SUBJECT_W: int
PR_ORIGINAL_SUBJECT_A: int
PR_DISC_VAL: int
PR_ORIG_MESSAGE_CLASS: int
PR_ORIG_MESSAGE_CLASS_W: int
PR_ORIG_MESSAGE_CLASS_A: int
PR_ORIGINAL_AUTHOR_ENTRYID: int
PR_ORIGINAL_AUTHOR_NAME: int
PR_ORIGINAL_AUTHOR_NAME_W: int
PR_ORIGINAL_AUTHOR_NAME_A: int
PR_ORIGINAL_SUBMIT_TIME: int
PR_REPLY_RECIPIENT_ENTRIES: int
PR_REPLY_RECIPIENT_NAMES: int
PR_REPLY_RECIPIENT_NAMES_W: int
PR_REPLY_RECIPIENT_NAMES_A: int
PR_RECEIVED_BY_SEARCH_KEY: int
PR_RCVD_REPRESENTING_SEARCH_KEY: int
PR_READ_RECEIPT_SEARCH_KEY: int
PR_REPORT_SEARCH_KEY: int
PR_ORIGINAL_DELIVERY_TIME: int
PR_ORIGINAL_AUTHOR_SEARCH_KEY: int
PR_MESSAGE_TO_ME: int
PR_MESSAGE_CC_ME: int
PR_MESSAGE_RECIP_ME: int
PR_ORIGINAL_SENDER_NAME: int
PR_ORIGINAL_SENDER_NAME_W: int
PR_ORIGINAL_SENDER_NAME_A: int
PR_ORIGINAL_SENDER_ENTRYID: int
PR_ORIGINAL_SENDER_SEARCH_KEY: int
PR_ORIGINAL_SENT_REPRESENTING_NAME: int
PR_ORIGINAL_SENT_REPRESENTING_NAME_W: int
PR_ORIGINAL_SENT_REPRESENTING_NAME_A: int
PR_ORIGINAL_SENT_REPRESENTING_ENTRYID: int
PR_ORIGINAL_SENT_REPRESENTING_SEARCH_KEY: int
PR_START_DATE: int
PR_END_DATE: int
PR_OWNER_APPT_ID: int
PR_RESPONSE_REQUESTED: int
PR_SENT_REPRESENTING_ADDRTYPE: int
PR_SENT_REPRESENTING_ADDRTYPE_W: int
PR_SENT_REPRESENTING_ADDRTYPE_A: int
PR_SENT_REPRESENTING_EMAIL_ADDRESS: int
PR_SENT_REPRESENTING_EMAIL_ADDRESS_W: int
PR_SENT_REPRESENTING_EMAIL_ADDRESS_A: int
PR_ORIGINAL_SENDER_ADDRTYPE: int
PR_ORIGINAL_SENDER_ADDRTYPE_W: int
PR_ORIGINAL_SENDER_ADDRTYPE_A: int
PR_ORIGINAL_SENDER_EMAIL_ADDRESS: int
PR_ORIGINAL_SENDER_EMAIL_ADDRESS_W: int
PR_ORIGINAL_SENDER_EMAIL_ADDRESS_A: int
PR_ORIGINAL_SENT_REPRESENTING_ADDRTYPE: int
PR_ORIGINAL_SENT_REPRESENTING_ADDRTYPE_W: int
PR_ORIGINAL_SENT_REPRESENTING_ADDRTYPE_A: int
PR_ORIGINAL_SENT_REPRESENTING_EMAIL_ADDRESS: int
PR_ORIGINAL_SENT_REPRESENTING_EMAIL_ADDRESS_W: int
PR_ORIGINAL_SENT_REPRESENTING_EMAIL_ADDRESS_A: int
PR_CONVERSATION_TOPIC: int
PR_CONVERSATION_TOPIC_W: int
PR_CONVERSATION_TOPIC_A: int
PR_CONVERSATION_INDEX: int
PR_ORIGINAL_DISPLAY_BCC: int
PR_ORIGINAL_DISPLAY_BCC_W: int
PR_ORIGINAL_DISPLAY_BCC_A: int
PR_ORIGINAL_DISPLAY_CC: int
PR_ORIGINAL_DISPLAY_CC_W: int
PR_ORIGINAL_DISPLAY_CC_A: int
PR_ORIGINAL_DISPLAY_TO: int
PR_ORIGINAL_DISPLAY_TO_W: int
PR_ORIGINAL_DISPLAY_TO_A: int
PR_RECEIVED_BY_ADDRTYPE: int
PR_RECEIVED_BY_ADDRTYPE_W: int
PR_RECEIVED_BY_ADDRTYPE_A: int
PR_RECEIVED_BY_EMAIL_ADDRESS: int
PR_RECEIVED_BY_EMAIL_ADDRESS_W: int
PR_RECEIVED_BY_EMAIL_ADDRESS_A: int
PR_RCVD_REPRESENTING_ADDRTYPE: int
PR_RCVD_REPRESENTING_ADDRTYPE_W: int
PR_RCVD_REPRESENTING_ADDRTYPE_A: int
PR_RCVD_REPRESENTING_EMAIL_ADDRESS: int
PR_RCVD_REPRESENTING_EMAIL_ADDRESS_W: int
PR_RCVD_REPRESENTING_EMAIL_ADDRESS_A: int
PR_ORIGINAL_AUTHOR_ADDRTYPE: int
PR_ORIGINAL_AUTHOR_ADDRTYPE_W: int
PR_ORIGINAL_AUTHOR_ADDRTYPE_A: int
PR_ORIGINAL_AUTHOR_EMAIL_ADDRESS: int
PR_ORIGINAL_AUTHOR_EMAIL_ADDRESS_W: int
PR_ORIGINAL_AUTHOR_EMAIL_ADDRESS_A: int
PR_ORIGINALLY_INTENDED_RECIP_ADDRTYPE: int
PR_ORIGINALLY_INTENDED_RECIP_ADDRTYPE_W: int
PR_ORIGINALLY_INTENDED_RECIP_ADDRTYPE_A: int
PR_ORIGINALLY_INTENDED_RECIP_EMAIL_ADDRESS: int
PR_ORIGINALLY_INTENDED_RECIP_EMAIL_ADDRESS_W: int
PR_ORIGINALLY_INTENDED_RECIP_EMAIL_ADDRESS_A: int
PR_TRANSPORT_MESSAGE_HEADERS: int
PR_TRANSPORT_MESSAGE_HEADERS_W: int
PR_TRANSPORT_MESSAGE_HEADERS_A: int
PR_DELEGATION: int
PR_TNEF_CORRELATION_KEY: int
PR_BODY: int
PR_BODY_W: int
PR_BODY_A: int
PR_BODY_HTML: int
PR_BODY_HTML_W: int
PR_BODY_HTML_A: int
PR_REPORT_TEXT: int
PR_REPORT_TEXT_W: int
PR_REPORT_TEXT_A: int
PR_ORIGINATOR_AND_DL_EXPANSION_HISTORY: int
PR_REPORTING_DL_NAME: int
PR_REPORTING_MTA_CERTIFICATE: int
PR_RTF_SYNC_BODY_CRC: int
PR_RTF_SYNC_BODY_COUNT: int
PR_RTF_SYNC_BODY_TAG: int
PR_RTF_SYNC_BODY_TAG_W: int
PR_RTF_SYNC_BODY_TAG_A: int
PR_RTF_COMPRESSED: int
PR_RTF_SYNC_PREFIX_COUNT: int
PR_RTF_SYNC_TRAILING_COUNT: int
PR_ORIGINALLY_INTENDED_RECIP_ENTRYID: int
PR_CONTENT_INTEGRITY_CHECK: int
PR_EXPLICIT_CONVERSION: int
PR_IPM_RETURN_REQUESTED: int
PR_MESSAGE_TOKEN: int
PR_NDR_REASON_CODE: int
PR_NDR_DIAG_CODE: int
PR_NON_RECEIPT_NOTIFICATION_REQUESTED: int
PR_DELIVERY_POINT: int
PR_ORIGINATOR_NON_DELIVERY_REPORT_REQUESTED: int
PR_ORIGINATOR_REQUESTED_ALTERNATE_RECIPIENT: int
PR_PHYSICAL_DELIVERY_BUREAU_FAX_DELIVERY: int
PR_PHYSICAL_DELIVERY_MODE: int
PR_PHYSICAL_DELIVERY_REPORT_REQUEST: int
PR_PHYSICAL_FORWARDING_ADDRESS: int
PR_PHYSICAL_FORWARDING_ADDRESS_REQUESTED: int
PR_PHYSICAL_FORWARDING_PROHIBITED: int
PR_PHYSICAL_RENDITION_ATTRIBUTES: int
PR_PROOF_OF_DELIVERY: int
PR_PROOF_OF_DELIVERY_REQUESTED: int
PR_RECIPIENT_CERTIFICATE: int
PR_RECIPIENT_NUMBER_FOR_ADVICE: int
PR_RECIPIENT_NUMBER_FOR_ADVICE_W: int
PR_RECIPIENT_NUMBER_FOR_ADVICE_A: int
PR_RECIPIENT_TYPE: int
PR_REGISTERED_MAIL_TYPE: int
PR_REPLY_REQUESTED: int
PR_REQUESTED_DELIVERY_METHOD: int
PR_SENDER_ENTRYID: int
PR_SENDER_NAME: int
PR_SENDER_NAME_W: int
PR_SENDER_NAME_A: int
PR_SUPPLEMENTARY_INFO: int
PR_SUPPLEMENTARY_INFO_W: int
PR_SUPPLEMENTARY_INFO_A: int
PR_TYPE_OF_MTS_USER: int
PR_SENDER_SEARCH_KEY: int
PR_SENDER_ADDRTYPE: int
PR_SENDER_ADDRTYPE_W: int
PR_SENDER_ADDRTYPE_A: int
PR_SENDER_EMAIL_ADDRESS: int
PR_SENDER_EMAIL_ADDRESS_W: int
PR_SENDER_EMAIL_ADDRESS_A: int
PR_CURRENT_VERSION: int
PR_DELETE_AFTER_SUBMIT: int
PR_DISPLAY_BCC: int
PR_DISPLAY_BCC_W: int
PR_DISPLAY_BCC_A: int
PR_DISPLAY_CC: int
PR_DISPLAY_CC_W: int
PR_DISPLAY_CC_A: int
PR_DISPLAY_TO: int
PR_DISPLAY_TO_W: int
PR_DISPLAY_TO_A: int
PR_PARENT_DISPLAY: int
PR_PARENT_DISPLAY_W: int
PR_PARENT_DISPLAY_A: int
PR_MESSAGE_DELIVERY_TIME: int
PR_MESSAGE_FLAGS: int
PR_MESSAGE_SIZE: int
PR_PARENT_ENTRYID: int
PR_SENTMAIL_ENTRYID: int
PR_CORRELATE: int
PR_CORRELATE_MTSID: int
PR_DISCRETE_VALUES: int
PR_RESPONSIBILITY: int
PR_SPOOLER_STATUS: int
PR_TRANSPORT_STATUS: int
PR_MESSAGE_RECIPIENTS: int
PR_MESSAGE_ATTACHMENTS: int
PR_SUBMIT_FLAGS: int
PR_RECIPIENT_STATUS: int
PR_TRANSPORT_KEY: int
PR_MSG_STATUS: int
PR_MESSAGE_DOWNLOAD_TIME: int
PR_CREATION_VERSION: int
PR_MODIFY_VERSION: int
PR_HASATTACH: int
PR_BODY_CRC: int
PR_NORMALIZED_SUBJECT: int
PR_NORMALIZED_SUBJECT_W: int
PR_NORMALIZED_SUBJECT_A: int
PR_RTF_IN_SYNC: int
PR_ATTACH_SIZE: int
PR_ATTACH_NUM: int
PR_PREPROCESS: int
PR_ORIGINATING_MTA_CERTIFICATE: int
PR_PROOF_OF_SUBMISSION: int
PR_ENTRYID: int
PR_OBJECT_TYPE: int
PR_ICON: int
PR_MINI_ICON: int
PR_STORE_ENTRYID: int
PR_STORE_RECORD_KEY: int
PR_RECORD_KEY: int
PR_MAPPING_SIGNATURE: int
PR_ACCESS_LEVEL: int
PR_INSTANCE_KEY: int
PR_ROW_TYPE: int
PR_ACCESS: int
PR_ROWID: int
PR_DISPLAY_NAME: int
PR_DISPLAY_NAME_W: int
PR_DISPLAY_NAME_A: int
PR_ADDRTYPE: int
PR_ADDRTYPE_W: int
PR_ADDRTYPE_A: int
PR_EMAIL_ADDRESS: int
PR_EMAIL_ADDRESS_W: int
PR_EMAIL_ADDRESS_A: int
PR_COMMENT: int
PR_COMMENT_W: int
PR_COMMENT_A: int
PR_DEPTH: int
PR_PROVIDER_DISPLAY: int
PR_PROVIDER_DISPLAY_W: int
PR_PROVIDER_DISPLAY_A: int
PR_CREATION_TIME: int
PR_LAST_MODIFICATION_TIME: int
PR_RESOURCE_FLAGS: int
PR_PROVIDER_DLL_NAME: int
PR_PROVIDER_DLL_NAME_W: int
PR_PROVIDER_DLL_NAME_A: int
PR_SEARCH_KEY: int
PR_PROVIDER_UID: int
PR_PROVIDER_ORDINAL: int
PR_FORM_VERSION: int
PR_FORM_VERSION_W: int
PR_FORM_VERSION_A: int
PR_FORM_CLSID: int
PR_FORM_CONTACT_NAME: int
PR_FORM_CONTACT_NAME_W: int
PR_FORM_CONTACT_NAME_A: int
PR_FORM_CATEGORY: int
PR_FORM_CATEGORY_W: int
PR_FORM_CATEGORY_A: int
PR_FORM_CATEGORY_SUB: int
PR_FORM_CATEGORY_SUB_W: int
PR_FORM_CATEGORY_SUB_A: int
PR_FORM_HOST_MAP: int
PR_FORM_HIDDEN: int
PR_FORM_DESIGNER_NAME: int
PR_FORM_DESIGNER_NAME_W: int
PR_FORM_DESIGNER_NAME_A: int
PR_FORM_DESIGNER_GUID: int
PR_FORM_MESSAGE_BEHAVIOR: int
PR_DEFAULT_STORE: int
PR_STORE_SUPPORT_MASK: int
PR_STORE_STATE: int
PR_IPM_SUBTREE_SEARCH_KEY: int
PR_IPM_OUTBOX_SEARCH_KEY: int
PR_IPM_WASTEBASKET_SEARCH_KEY: int
PR_IPM_SENTMAIL_SEARCH_KEY: int
PR_MDB_PROVIDER: int
PR_RECEIVE_FOLDER_SETTINGS: int
PR_VALID_FOLDER_MASK: int
PR_IPM_SUBTREE_ENTRYID: int
PR_IPM_OUTBOX_ENTRYID: int
PR_IPM_WASTEBASKET_ENTRYID: int
PR_IPM_SENTMAIL_ENTRYID: int
PR_VIEWS_ENTRYID: int
PR_COMMON_VIEWS_ENTRYID: int
PR_FINDER_ENTRYID: int
PR_CONTAINER_FLAGS: int
PR_FOLDER_TYPE: int
PR_CONTENT_COUNT: int
PR_CONTENT_UNREAD: int
PR_CREATE_TEMPLATES: int
PR_DETAILS_TABLE: int
PR_SEARCH: int
PR_SELECTABLE: int
PR_SUBFOLDERS: int
PR_STATUS: int
PR_ANR: int
PR_ANR_W: int
PR_ANR_A: int
PR_CONTENTS_SORT_ORDER: int
PR_CONTAINER_HIERARCHY: int
PR_CONTAINER_CONTENTS: int
PR_FOLDER_ASSOCIATED_CONTENTS: int
PR_DEF_CREATE_DL: int
PR_DEF_CREATE_MAILUSER: int
PR_CONTAINER_CLASS: int
PR_CONTAINER_CLASS_W: int
PR_CONTAINER_CLASS_A: int
PR_CONTAINER_MODIFY_VERSION: int
PR_AB_PROVIDER_ID: int
PR_DEFAULT_VIEW_ENTRYID: int
PR_ASSOC_CONTENT_COUNT: int
PR_ATTACHMENT_X400_PARAMETERS: int
PR_ATTACH_DATA_OBJ: int
PR_ATTACH_DATA_BIN: int
PR_ATTACH_ENCODING: int
PR_ATTACH_EXTENSION: int
PR_ATTACH_EXTENSION_W: int
PR_ATTACH_EXTENSION_A: int
PR_ATTACH_FILENAME: int
PR_ATTACH_FILENAME_W: int
PR_ATTACH_FILENAME_A: int
PR_ATTACH_METHOD: int
PR_ATTACH_LONG_FILENAME: int
PR_ATTACH_LONG_FILENAME_W: int
PR_ATTACH_LONG_FILENAME_A: int
PR_ATTACH_PATHNAME: int
PR_ATTACH_PATHNAME_W: int
PR_ATTACH_PATHNAME_A: int
PR_ATTACH_RENDERING: int
PR_ATTACH_TAG: int
PR_RENDERING_POSITION: int
PR_ATTACH_TRANSPORT_NAME: int
PR_ATTACH_TRANSPORT_NAME_W: int
PR_ATTACH_TRANSPORT_NAME_A: int
PR_ATTACH_LONG_PATHNAME: int
PR_ATTACH_LONG_PATHNAME_W: int
PR_ATTACH_LONG_PATHNAME_A: int
PR_ATTACH_MIME_TAG: int
PR_ATTACH_MIME_TAG_W: int
PR_ATTACH_MIME_TAG_A: int
PR_ATTACH_ADDITIONAL_INFO: int
PR_DISPLAY_TYPE: int
PR_TEMPLATEID: int
PR_PRIMARY_CAPABILITY: int
PR_7BIT_DISPLAY_NAME: int
PR_ACCOUNT: int
PR_ACCOUNT_W: int
PR_ACCOUNT_A: int
PR_ALTERNATE_RECIPIENT: int
PR_CALLBACK_TELEPHONE_NUMBER: int
PR_CALLBACK_TELEPHONE_NUMBER_W: int
PR_CALLBACK_TELEPHONE_NUMBER_A: int
PR_CONVERSION_PROHIBITED: int
PR_DISCLOSE_RECIPIENTS: int
PR_GENERATION: int
PR_GENERATION_W: int
PR_GENERATION_A: int
PR_GIVEN_NAME: int
PR_GIVEN_NAME_W: int
PR_GIVEN_NAME_A: int
PR_GOVERNMENT_ID_NUMBER: int
PR_GOVERNMENT_ID_NUMBER_W: int
PR_GOVERNMENT_ID_NUMBER_A: int
PR_BUSINESS_TELEPHONE_NUMBER: int
PR_BUSINESS_TELEPHONE_NUMBER_W: int
PR_BUSINESS_TELEPHONE_NUMBER_A: int
PR_OFFICE_TELEPHONE_NUMBER: int
PR_OFFICE_TELEPHONE_NUMBER_W: int
PR_OFFICE_TELEPHONE_NUMBER_A: int
PR_HOME_TELEPHONE_NUMBER: int
PR_HOME_TELEPHONE_NUMBER_W: int
PR_HOME_TELEPHONE_NUMBER_A: int
PR_INITIALS: int
PR_INITIALS_W: int
PR_INITIALS_A: int
PR_KEYWORD: int
PR_KEYWORD_W: int
PR_KEYWORD_A: int
PR_LANGUAGE: int
PR_LANGUAGE_W: int
PR_LANGUAGE_A: int
PR_LOCATION: int
PR_LOCATION_W: int
PR_LOCATION_A: int
PR_MAIL_PERMISSION: int
PR_MHS_COMMON_NAME: int
PR_MHS_COMMON_NAME_W: int
PR_MHS_COMMON_NAME_A: int
PR_ORGANIZATIONAL_ID_NUMBER: int
PR_ORGANIZATIONAL_ID_NUMBER_W: int
PR_ORGANIZATIONAL_ID_NUMBER_A: int
PR_SURNAME: int
PR_SURNAME_W: int
PR_SURNAME_A: int
PR_ORIGINAL_ENTRYID: int
PR_ORIGINAL_DISPLAY_NAME: int
PR_ORIGINAL_DISPLAY_NAME_W: int
PR_ORIGINAL_DISPLAY_NAME_A: int
PR_ORIGINAL_SEARCH_KEY: int
PR_POSTAL_ADDRESS: int
PR_POSTAL_ADDRESS_W: int
PR_POSTAL_ADDRESS_A: int
PR_COMPANY_NAME: int
PR_COMPANY_NAME_W: int
PR_COMPANY_NAME_A: int
PR_TITLE: int
PR_TITLE_W: int
PR_TITLE_A: int
PR_DEPARTMENT_NAME: int
PR_DEPARTMENT_NAME_W: int
PR_DEPARTMENT_NAME_A: int
PR_OFFICE_LOCATION: int
PR_OFFICE_LOCATION_W: int
PR_OFFICE_LOCATION_A: int
PR_PRIMARY_TELEPHONE_NUMBER: int
PR_PRIMARY_TELEPHONE_NUMBER_W: int
PR_PRIMARY_TELEPHONE_NUMBER_A: int
PR_BUSINESS2_TELEPHONE_NUMBER: int
PR_BUSINESS2_TELEPHONE_NUMBER_W: int
PR_BUSINESS2_TELEPHONE_NUMBER_A: int
PR_OFFICE2_TELEPHONE_NUMBER: int
PR_OFFICE2_TELEPHONE_NUMBER_W: int
PR_OFFICE2_TELEPHONE_NUMBER_A: int
PR_MOBILE_TELEPHONE_NUMBER: int
PR_MOBILE_TELEPHONE_NUMBER_W: int
PR_MOBILE_TELEPHONE_NUMBER_A: int
PR_CELLULAR_TELEPHONE_NUMBER: int
PR_CELLULAR_TELEPHONE_NUMBER_W: int
PR_CELLULAR_TELEPHONE_NUMBER_A: int
PR_RADIO_TELEPHONE_NUMBER: int
PR_RADIO_TELEPHONE_NUMBER_W: int
PR_RADIO_TELEPHONE_NUMBER_A: int
PR_CAR_TELEPHONE_NUMBER: int
PR_CAR_TELEPHONE_NUMBER_W: int
PR_CAR_TELEPHONE_NUMBER_A: int
PR_OTHER_TELEPHONE_NUMBER: int
PR_OTHER_TELEPHONE_NUMBER_W: int
PR_OTHER_TELEPHONE_NUMBER_A: int
PR_TRANSMITABLE_DISPLAY_NAME: int
PR_TRANSMITABLE_DISPLAY_NAME_W: int
PR_TRANSMITABLE_DISPLAY_NAME_A: int
PR_PAGER_TELEPHONE_NUMBER: int
PR_PAGER_TELEPHONE_NUMBER_W: int
PR_PAGER_TELEPHONE_NUMBER_A: int
PR_BEEPER_TELEPHONE_NUMBER: int
PR_BEEPER_TELEPHONE_NUMBER_W: int
PR_BEEPER_TELEPHONE_NUMBER_A: int
PR_USER_CERTIFICATE: int
PR_PRIMARY_FAX_NUMBER: int
PR_PRIMARY_FAX_NUMBER_W: int
PR_PRIMARY_FAX_NUMBER_A: int
PR_BUSINESS_FAX_NUMBER: int
PR_BUSINESS_FAX_NUMBER_W: int
PR_BUSINESS_FAX_NUMBER_A: int
PR_HOME_FAX_NUMBER: int
PR_HOME_FAX_NUMBER_W: int
PR_HOME_FAX_NUMBER_A: int
PR_COUNTRY: int
PR_COUNTRY_W: int
PR_COUNTRY_A: int
PR_BUSINESS_ADDRESS_COUNTRY: int
PR_BUSINESS_ADDRESS_COUNTRY_W: int
PR_BUSINESS_ADDRESS_COUNTRY_A: int
PR_LOCALITY: int
PR_LOCALITY_W: int
PR_LOCALITY_A: int
PR_BUSINESS_ADDRESS_CITY: int
PR_BUSINESS_ADDRESS_CITY_W: int
PR_BUSINESS_ADDRESS_CITY_A: int
PR_STATE_OR_PROVINCE: int
PR_STATE_OR_PROVINCE_W: int
PR_STATE_OR_PROVINCE_A: int
PR_BUSINESS_ADDRESS_STATE_OR_PROVINCE: int
PR_BUSINESS_ADDRESS_STATE_OR_PROVINCE_W: int
PR_BUSINESS_ADDRESS_STATE_OR_PROVINCE_A: int
PR_STREET_ADDRESS: int
PR_STREET_ADDRESS_W: int
PR_STREET_ADDRESS_A: int
PR_BUSINESS_ADDRESS_STREET: int
PR_BUSINESS_ADDRESS_STREET_W: int
PR_BUSINESS_ADDRESS_STREET_A: int
PR_POSTAL_CODE: int
PR_POSTAL_CODE_W: int
PR_POSTAL_CODE_A: int
PR_BUSINESS_ADDRESS_POSTAL_CODE: int
PR_BUSINESS_ADDRESS_POSTAL_CODE_W: int
PR_BUSINESS_ADDRESS_POSTAL_CODE_A: int
PR_POST_OFFICE_BOX: int
PR_POST_OFFICE_BOX_W: int
PR_POST_OFFICE_BOX_A: int
PR_BUSINESS_ADDRESS_POST_OFFICE_BOX: int
PR_BUSINESS_ADDRESS_POST_OFFICE_BOX_W: int
PR_BUSINESS_ADDRESS_POST_OFFICE_BOX_A: int
PR_TELEX_NUMBER: int
PR_TELEX_NUMBER_W: int
PR_TELEX_NUMBER_A: int
PR_ISDN_NUMBER: int
PR_ISDN_NUMBER_W: int
PR_ISDN_NUMBER_A: int
PR_ASSISTANT_TELEPHONE_NUMBER: int
PR_ASSISTANT_TELEPHONE_NUMBER_W: int
PR_ASSISTANT_TELEPHONE_NUMBER_A: int
PR_HOME2_TELEPHONE_NUMBER: int
PR_HOME2_TELEPHONE_NUMBER_W: int
PR_HOME2_TELEPHONE_NUMBER_A: int
PR_ASSISTANT: int
PR_ASSISTANT_W: int
PR_ASSISTANT_A: int
PR_SEND_RICH_INFO: int
PR_WEDDING_ANNIVERSARY: int
PR_BIRTHDAY: int
PR_HOBBIES: int
PR_HOBBIES_W: int
PR_HOBBIES_A: int
PR_MIDDLE_NAME: int
PR_MIDDLE_NAME_W: int
PR_MIDDLE_NAME_A: int
PR_DISPLAY_NAME_PREFIX: int
PR_DISPLAY_NAME_PREFIX_W: int
PR_DISPLAY_NAME_PREFIX_A: int
PR_PROFESSION: int
PR_PROFESSION_W: int
PR_PROFESSION_A: int
PR_PREFERRED_BY_NAME: int
PR_PREFERRED_BY_NAME_W: int
PR_PREFERRED_BY_NAME_A: int
PR_SPOUSE_NAME: int
PR_SPOUSE_NAME_W: int
PR_SPOUSE_NAME_A: int
PR_COMPUTER_NETWORK_NAME: int
PR_COMPUTER_NETWORK_NAME_W: int
PR_COMPUTER_NETWORK_NAME_A: int
PR_CUSTOMER_ID: int
PR_CUSTOMER_ID_W: int
PR_CUSTOMER_ID_A: int
PR_TTYTDD_PHONE_NUMBER: int
PR_TTYTDD_PHONE_NUMBER_W: int
PR_TTYTDD_PHONE_NUMBER_A: int
PR_FTP_SITE: int
PR_FTP_SITE_W: int
PR_FTP_SITE_A: int
PR_GENDER: int
PR_MANAGER_NAME: int
PR_MANAGER_NAME_W: int
PR_MANAGER_NAME_A: int
PR_NICKNAME: int
PR_NICKNAME_W: int
PR_NICKNAME_A: int
PR_PERSONAL_HOME_PAGE: int
PR_PERSONAL_HOME_PAGE_W: int
PR_PERSONAL_HOME_PAGE_A: int
PR_BUSINESS_HOME_PAGE: int
PR_BUSINESS_HOME_PAGE_W: int
PR_BUSINESS_HOME_PAGE_A: int
PR_CONTACT_VERSION: int
PR_CONTACT_ENTRYIDS: int
PR_CONTACT_ADDRTYPES: int
PR_CONTACT_ADDRTYPES_W: int
PR_CONTACT_ADDRTYPES_A: int
PR_CONTACT_DEFAULT_ADDRESS_INDEX: int
PR_CONTACT_EMAIL_ADDRESSES: int
PR_CONTACT_EMAIL_ADDRESSES_W: int
PR_CONTACT_EMAIL_ADDRESSES_A: int
PR_COMPANY_MAIN_PHONE_NUMBER: int
PR_COMPANY_MAIN_PHONE_NUMBER_W: int
PR_COMPANY_MAIN_PHONE_NUMBER_A: int
PR_CHILDRENS_NAMES: int
PR_CHILDRENS_NAMES_W: int
PR_CHILDRENS_NAMES_A: int
PR_HOME_ADDRESS_CITY: int
PR_HOME_ADDRESS_CITY_W: int
PR_HOME_ADDRESS_CITY_A: int
PR_HOME_ADDRESS_COUNTRY: int
PR_HOME_ADDRESS_COUNTRY_W: int
PR_HOME_ADDRESS_COUNTRY_A: int
PR_HOME_ADDRESS_POSTAL_CODE: int
PR_HOME_ADDRESS_POSTAL_CODE_W: int
PR_HOME_ADDRESS_POSTAL_CODE_A: int
PR_HOME_ADDRESS_STATE_OR_PROVINCE: int
PR_HOME_ADDRESS_STATE_OR_PROVINCE_W: int
PR_HOME_ADDRESS_STATE_OR_PROVINCE_A: int
PR_HOME_ADDRESS_STREET: int
PR_HOME_ADDRESS_STREET_W: int
PR_HOME_ADDRESS_STREET_A: int
PR_HOME_ADDRESS_POST_OFFICE_BOX: int
PR_HOME_ADDRESS_POST_OFFICE_BOX_W: int
PR_HOME_ADDRESS_POST_OFFICE_BOX_A: int
PR_OTHER_ADDRESS_CITY: int
PR_OTHER_ADDRESS_CITY_W: int
PR_OTHER_ADDRESS_CITY_A: int
PR_OTHER_ADDRESS_COUNTRY: int
PR_OTHER_ADDRESS_COUNTRY_W: int
PR_OTHER_ADDRESS_COUNTRY_A: int
PR_OTHER_ADDRESS_POSTAL_CODE: int
PR_OTHER_ADDRESS_POSTAL_CODE_W: int
PR_OTHER_ADDRESS_POSTAL_CODE_A: int
PR_OTHER_ADDRESS_STATE_OR_PROVINCE: int
PR_OTHER_ADDRESS_STATE_OR_PROVINCE_W: int
PR_OTHER_ADDRESS_STATE_OR_PROVINCE_A: int
PR_OTHER_ADDRESS_STREET: int
PR_OTHER_ADDRESS_STREET_W: int
PR_OTHER_ADDRESS_STREET_A: int
PR_OTHER_ADDRESS_POST_OFFICE_BOX: int
PR_OTHER_ADDRESS_POST_OFFICE_BOX_W: int
PR_OTHER_ADDRESS_POST_OFFICE_BOX_A: int
PR_STORE_PROVIDERS: int
PR_AB_PROVIDERS: int
PR_TRANSPORT_PROVIDERS: int
PR_DEFAULT_PROFILE: int
PR_AB_SEARCH_PATH: int
PR_AB_DEFAULT_DIR: int
PR_AB_DEFAULT_PAB: int
PR_FILTERING_HOOKS: int
PR_SERVICE_NAME: int
PR_SERVICE_NAME_W: int
PR_SERVICE_NAME_A: int
PR_SERVICE_DLL_NAME: int
PR_SERVICE_DLL_NAME_W: int
PR_SERVICE_DLL_NAME_A: int
PR_SERVICE_ENTRY_NAME: int
PR_SERVICE_UID: int
PR_SERVICE_EXTRA_UIDS: int
PR_SERVICES: int
PR_SERVICE_SUPPORT_FILES: int
PR_SERVICE_SUPPORT_FILES_W: int
PR_SERVICE_SUPPORT_FILES_A: int
PR_SERVICE_DELETE_FILES: int
PR_SERVICE_DELETE_FILES_W: int
PR_SERVICE_DELETE_FILES_A: int
PR_AB_SEARCH_PATH_UPDATE: int
PR_PROFILE_NAME: int
PR_PROFILE_NAME_A: int
PR_PROFILE_NAME_W: int
PR_IDENTITY_DISPLAY: int
PR_IDENTITY_DISPLAY_W: int
PR_IDENTITY_DISPLAY_A: int
PR_IDENTITY_ENTRYID: int
PR_RESOURCE_METHODS: int
PR_RESOURCE_TYPE: int
PR_STATUS_CODE: int
PR_IDENTITY_SEARCH_KEY: int
PR_OWN_STORE_ENTRYID: int
PR_RESOURCE_PATH: int
PR_RESOURCE_PATH_W: int
PR_RESOURCE_PATH_A: int
PR_STATUS_STRING: int
PR_STATUS_STRING_W: int
PR_STATUS_STRING_A: int
PR_X400_DEFERRED_DELIVERY_CANCEL: int
PR_HEADER_FOLDER_ENTRYID: int
PR_REMOTE_PROGRESS: int
PR_REMOTE_PROGRESS_TEXT: int
PR_REMOTE_PROGRESS_TEXT_W: int
PR_REMOTE_PROGRESS_TEXT_A: int
PR_REMOTE_VALIDATE_OK: int
PR_CONTROL_FLAGS: int
PR_CONTROL_STRUCTURE: int
PR_CONTROL_TYPE: int
PR_DELTAX: int
PR_DELTAY: int
PR_XPOS: int
PR_YPOS: int
PR_CONTROL_ID: int
PR_INITIAL_DETAILS_PANE: int
PROP_ID_SECURE_MIN: int
PROP_ID_SECURE_MAX: int
pidExchangeXmitReservedMin: int
pidExchangeNonXmitReservedMin: int
pidProfileMin: int
pidStoreMin: int
pidFolderMin: int
pidMessageReadOnlyMin: int
pidMessageWriteableMin: int
pidAttachReadOnlyMin: int
pidSpecialMin: int
pidAdminMin: int
pidSecureProfileMin: int
PR_PROFILE_VERSION: int
PR_PROFILE_CONFIG_FLAGS: int
PR_PROFILE_HOME_SERVER: int
PR_PROFILE_HOME_SERVER_DN: int
PR_PROFILE_HOME_SERVER_ADDRS: int
PR_PROFILE_USER: int
PR_PROFILE_CONNECT_FLAGS: int
PR_PROFILE_TRANSPORT_FLAGS: int
PR_PROFILE_UI_STATE: int
PR_PROFILE_UNRESOLVED_NAME: int
PR_PROFILE_UNRESOLVED_SERVER: int
PR_PROFILE_BINDING_ORDER: int
PR_PROFILE_MAX_RESTRICT: int
PR_PROFILE_AB_FILES_PATH: int
PR_PROFILE_OFFLINE_STORE_PATH: int
PR_PROFILE_OFFLINE_INFO: int
PR_PROFILE_ADDR_INFO: int
PR_PROFILE_OPTIONS_DATA: int
PR_PROFILE_SECURE_MAILBOX: int
PR_DISABLE_WINSOCK: int
PR_OST_ENCRYPTION: int
PR_PROFILE_OPEN_FLAGS: int
PR_PROFILE_TYPE: int
PR_PROFILE_MAILBOX: int
PR_PROFILE_SERVER: int
PR_PROFILE_SERVER_DN: int
PR_PROFILE_FAVFLD_DISPLAY_NAME: int
PR_PROFILE_FAVFLD_COMMENT: int
PR_PROFILE_ALLPUB_DISPLAY_NAME: int
PR_PROFILE_ALLPUB_COMMENT: int
OSTF_NO_ENCRYPTION: int
OSTF_COMPRESSABLE_ENCRYPTION: int
OSTF_BEST_ENCRYPTION: int
PR_NON_IPM_SUBTREE_ENTRYID: int
PR_EFORMS_REGISTRY_ENTRYID: int
PR_SPLUS_FREE_BUSY_ENTRYID: int
PR_OFFLINE_ADDRBOOK_ENTRYID: int
PR_EFORMS_FOR_LOCALE_ENTRYID: int
PR_FREE_BUSY_FOR_LOCAL_SITE_ENTRYID: int
PR_ADDRBOOK_FOR_LOCAL_SITE_ENTRYID: int
PR_OFFLINE_MESSAGE_ENTRYID: int
PR_IPM_FAVORITES_ENTRYID: int
PR_IPM_PUBLIC_FOLDERS_ENTRYID: int
PR_GW_MTSIN_ENTRYID: int
PR_GW_MTSOUT_ENTRYID: int
PR_TRANSFER_ENABLED: int
PR_TEST_LINE_SPEED: int
PR_HIERARCHY_SYNCHRONIZER: int
PR_CONTENTS_SYNCHRONIZER: int
PR_COLLECTOR: int
PR_FAST_TRANSFER: int
PR_STORE_OFFLINE: int
PR_IN_TRANSIT: int
PR_REPLICATION_STYLE: int
PR_REPLICATION_SCHEDULE: int
PR_REPLICATION_MESSAGE_PRIORITY: int
PR_OVERALL_MSG_AGE_LIMIT: int
PR_REPLICATION_ALWAYS_INTERVAL: int
PR_REPLICATION_MSG_SIZE: int
STYLE_ALWAYS_INTERVAL_DEFAULT: int
REPLICATION_MESSAGE_SIZE_LIMIT_DEFAULT: int
STYLE_NEVER: int
STYLE_NORMAL: int
STYLE_ALWAYS: int
STYLE_DEFAULT: int
PR_SOURCE_KEY: int
PR_PARENT_SOURCE_KEY: int
PR_CHANGE_KEY: int
PR_PREDECESSOR_CHANGE_LIST: int
PR_FOLDER_CHILD_COUNT: int
PR_RIGHTS: int
PR_ACL_TABLE: int
PR_RULES_TABLE: int
PR_HAS_RULES: int
PR_ADDRESS_BOOK_ENTRYID: int
PR_ACL_DATA: int
PR_RULES_DATA: int
PR_FOLDER_DESIGN_FLAGS: int
PR_DESIGN_IN_PROGRESS: int
PR_SECURE_ORIGINATION: int
PR_PUBLISH_IN_ADDRESS_BOOK: int
PR_RESOLVE_METHOD: int
PR_ADDRESS_BOOK_DISPLAY_NAME: int
PR_EFORMS_LOCALE_ID: int
PR_REPLICA_LIST: int
PR_OVERALL_AGE_LIMIT: int
RESOLVE_METHOD_DEFAULT: int
RESOLVE_METHOD_LAST_WRITER_WINS: int
RESOLVE_METHOD_NO_CONFLICT_NOTIFICATION: int
PR_PUBLIC_FOLDER_ENTRYID: int
PR_HAS_NAMED_PROPERTIES: int
PR_CREATOR_NAME: int
PR_CREATOR_ENTRYID: int
PR_LAST_MODIFIER_NAME: int
PR_LAST_MODIFIER_ENTRYID: int
PR_HAS_DAMS: int
PR_RULE_TRIGGER_HISTORY: int
PR_MOVE_TO_STORE_ENTRYID: int
PR_MOVE_TO_FOLDER_ENTRYID: int
PR_REPLICA_SERVER: int
PR_DEFERRED_SEND_NUMBER: int
PR_DEFERRED_SEND_UNITS: int
PR_EXPIRY_NUMBER: int
PR_EXPIRY_UNITS: int
PR_DEFERRED_SEND_TIME: int
PR_GW_ADMIN_OPERATIONS: int
PR_P1_CONTENT: int
PR_P1_CONTENT_TYPE: int
PR_CLIENT_ACTIONS: int
PR_DAM_ORIGINAL_ENTRYID: int
PR_DAM_BACK_PATCHED: int
PR_RULE_ERROR: int
PR_RULE_ACTION_TYPE: int
PR_RULE_ACTION_NUMBER: int
PR_RULE_FOLDER_ENTRYID: int
PR_CONFLICT_ENTRYID: int
PR_MESSAGE_LOCALE_ID: int
PR_STORAGE_QUOTA_LIMIT: int
PR_EXCESS_STORAGE_USED: int
PR_SVR_GENERATING_QUOTA_MSG: int
PR_DELEGATED_BY_RULE: int
MSGSTATUS_IN_CONFLICT: int
PR_IN_CONFLICT: int
PR_LONGTERM_ENTRYID_FROM_TABLE: int
PR_ORIGINATOR_NAME: int
PR_ORIGINATOR_ADDR: int
PR_ORIGINATOR_ADDRTYPE: int
PR_ORIGINATOR_ENTRYID: int
PR_ARRIVAL_TIME: int
PR_TRACE_INFO: int
PR_INTERNAL_TRACE_INFO: int
PR_SUBJECT_TRACE_INFO: int
PR_RECIPIENT_NUMBER: int
PR_MTS_SUBJECT_ID: int
PR_REPORT_DESTINATION_NAME: int
PR_REPORT_DESTINATION_ENTRYID: int
PR_CONTENT_SEARCH_KEY: int
PR_FOREIGN_ID: int
PR_FOREIGN_REPORT_ID: int
PR_FOREIGN_SUBJECT_ID: int
PR_MTS_ID: int
PR_MTS_REPORT_ID: int
PR_FOLDER_FLAGS: int
PR_LAST_ACCESS_TIME: int
PR_RESTRICTION_COUNT: int
PR_CATEG_COUNT: int
PR_CACHED_COLUMN_COUNT: int
PR_NORMAL_MSG_W_ATTACH_COUNT: int
PR_ASSOC_MSG_W_ATTACH_COUNT: int
PR_RECIPIENT_ON_NORMAL_MSG_COUNT: int
PR_RECIPIENT_ON_ASSOC_MSG_COUNT: int
PR_ATTACH_ON_NORMAL_MSG_COUNT: int
PR_ATTACH_ON_ASSOC_MSG_COUNT: int
PR_NORMAL_MESSAGE_SIZE: int
PR_NORMAL_MESSAGE_SIZE_EXTENDED: int
PR_ASSOC_MESSAGE_SIZE: int
PR_ASSOC_MESSAGE_SIZE_EXTENDED: int
PR_FOLDER_PATHNAME: int
PR_OWNER_COUNT: int
PR_CONTACT_COUNT: int
PR_MESSAGE_SIZE_EXTENDED: int
PR_USERFIELDS: int
PR_FORCE_USE_ENTRYID_SERVER: int
PR_PROFILE_MDB_DN: int
PST_EXTERN_PROPID_BASE: int
PR_PST_PATH: int
PR_PST_PATH_W: int
PR_PST_PATH_A: int
PR_PST_REMEMBER_PW: int
PR_PST_ENCRYPTION: int
PR_PST_PW_SZ_OLD: int
PR_PST_PW_SZ_OLD_W: int
PR_PST_PW_SZ_OLD_A: int
PR_PST_PW_SZ_NEW: int
PR_PST_PW_SZ_NEW_W: int
PR_PST_PW_SZ_NEW_A: int
