from _typeshed import Incomplete

lock: Incomplete
CDEF_SOURCE_STRING: str

class Parser:
    def __init__(self) -> None: ...
    def convert_pycparser_error(self, e, csource) -> None: ...
    def parse(self, csource, override: bool = False, packed: bool = False, pack=None, dllexport: bool = False) -> None: ...
    def parse_type(self, cdecl): ...
    def parse_type_and_quals(self, cdecl): ...
    def include(self, other) -> None: ...
