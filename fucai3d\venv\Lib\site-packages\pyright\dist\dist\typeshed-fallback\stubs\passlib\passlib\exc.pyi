from typing import Any

class UnknownBackendError(ValueError):
    hasher: Any
    backend: Any
    def __init__(self, hasher, backend) -> None: ...

class MissingBackendError(RuntimeError): ...
class InternalBackendError(RuntimeError): ...
class PasswordValueError(ValueError): ...

class PasswordSizeError(PasswordValueError):
    max_size: Any
    def __init__(self, max_size, msg=None) -> None: ...

class PasswordTruncateError(PasswordSizeError):
    def __init__(self, cls, msg=None) -> None: ...

class PasslibSecurityError(RuntimeError): ...

class TokenError(ValueError):
    def __init__(self, msg=None, *args, **kwds) -> None: ...

class MalformedTokenError(TokenError): ...
class InvalidTokenError(TokenError): ...

class UsedTokenError(TokenError):
    expire_time: Any
    def __init__(self, *args, **kwds) -> None: ...

class UnknownHashError(ValueError):
    value: Any
    message: Any
    def __init__(self, message=None, value=None) -> None: ...

class PasslibWarning(UserWarning): ...
class PasslibConfigWarning(PasslibWarning): ...
class PasslibHashWarning(PasslibWarning): ...
class PasslibRuntimeWarning(PasslibWarning): ...
class PasslibSecurityWarning(PasslibWarning): ...

def type_name(value): ...
def ExpectedTypeError(value, expected, param): ...
def ExpectedStringError(value, param): ...
def MissingDigestError(handler=None): ...
def NullPasswordError(handler=None): ...
def InvalidHashError(handler=None): ...
def MalformedHashError(handler=None, reason=None): ...
def ZeroPaddedRoundsError(handler=None): ...
def ChecksumSizeError(handler, raw: bool = False): ...

ENABLE_DEBUG_ONLY_REPR: bool

def debug_only_repr(value, param: str = "hash"): ...
def CryptBackendError(handler, config, hash, source: str = "crypt.crypt()") -> None: ...
