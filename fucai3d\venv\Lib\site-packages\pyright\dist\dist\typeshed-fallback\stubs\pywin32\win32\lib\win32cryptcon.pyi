def GET_ALG_CLASS(x: int) -> int: ...
def GET_ALG_TYPE(x: int) -> int: ...
def GET_ALG_SID(x: int) -> int: ...

ALG_CLASS_ANY: int
ALG_CLASS_SIGNATURE: int
ALG_CLASS_MSG_ENCRYPT: int
ALG_CLASS_DATA_ENCRYPT: int
ALG_CLASS_HASH: int
ALG_CLASS_KEY_EXCHANGE: int
ALG_CLASS_ALL: int
ALG_TYPE_ANY: int
ALG_TYPE_DSS: int
ALG_TYPE_RSA: int
ALG_TYPE_BLOCK: int
ALG_TYPE_STREAM: int
ALG_TYPE_DH: int
ALG_TYPE_SECURECHANNEL: int
ALG_SID_ANY: int
ALG_SID_RSA_ANY: int
ALG_SID_RSA_PKCS: int
ALG_SID_RSA_MSATWORK: int
ALG_SID_RSA_ENTRUST: int
ALG_SID_RSA_PGP: int
ALG_SID_DSS_ANY: int
ALG_SID_DSS_PKCS: int
ALG_SID_DSS_DMS: int
ALG_SID_DES: int
ALG_SID_3DES: int
ALG_SID_DESX: int
ALG_SID_IDEA: int
ALG_SID_CAST: int
ALG_SID_SAFERSK64: int
ALG_SID_SAFERSK128: int
ALG_SID_3DES_112: int
ALG_SID_CYLINK_MEK: int
ALG_SID_RC5: int
ALG_SID_AES_128: int
ALG_SID_AES_192: int
ALG_SID_AES_256: int
ALG_SID_AES: int
ALG_SID_SKIPJACK: int
ALG_SID_TEK: int
CRYPT_MODE_CBCI: int
CRYPT_MODE_CFBP: int
CRYPT_MODE_OFBP: int
CRYPT_MODE_CBCOFM: int
CRYPT_MODE_CBCOFMI: int
ALG_SID_RC2: int
ALG_SID_RC4: int
ALG_SID_SEAL: int
ALG_SID_DH_SANDF: int
ALG_SID_DH_EPHEM: int
ALG_SID_AGREED_KEY_ANY: int
ALG_SID_KEA: int
ALG_SID_MD2: int
ALG_SID_MD4: int
ALG_SID_MD5: int
ALG_SID_SHA: int
ALG_SID_SHA1: int
ALG_SID_MAC: int
ALG_SID_RIPEMD: int
ALG_SID_RIPEMD160: int
ALG_SID_SSL3SHAMD5: int
ALG_SID_HMAC: int
ALG_SID_TLS1PRF: int
ALG_SID_HASH_REPLACE_OWF: int
ALG_SID_SHA_256: int
ALG_SID_SHA_384: int
ALG_SID_SHA_512: int
ALG_SID_SSL3_MASTER: int
ALG_SID_SCHANNEL_MASTER_HASH: int
ALG_SID_SCHANNEL_MAC_KEY: int
ALG_SID_PCT1_MASTER: int
ALG_SID_SSL2_MASTER: int
ALG_SID_TLS1_MASTER: int
ALG_SID_SCHANNEL_ENC_KEY: int
ALG_SID_EXAMPLE: int
CALG_MD2: int
CALG_MD4: int
CALG_MD5: int
CALG_SHA: int
CALG_SHA1: int
CALG_MAC: int
CALG_RSA_SIGN: int
CALG_DSS_SIGN: int
CALG_NO_SIGN: int
CALG_RSA_KEYX: int
CALG_DES: int
CALG_3DES_112: int
CALG_3DES: int
CALG_DESX: int
CALG_RC2: int
CALG_RC4: int
CALG_SEAL: int
CALG_DH_SF: int
CALG_DH_EPHEM: int
CALG_AGREEDKEY_ANY: int
CALG_KEA_KEYX: int
CALG_HUGHES_MD5: int
CALG_SKIPJACK: int
CALG_TEK: int
CALG_CYLINK_MEK: int
CALG_SSL3_SHAMD5: int
CALG_SSL3_MASTER: int
CALG_SCHANNEL_MASTER_HASH: int
CALG_SCHANNEL_MAC_KEY: int
CALG_SCHANNEL_ENC_KEY: int
CALG_PCT1_MASTER: int
CALG_SSL2_MASTER: int
CALG_TLS1_MASTER: int
CALG_RC5: int
CALG_HMAC: int
CALG_TLS1PRF: int
CALG_HASH_REPLACE_OWF: int
CALG_AES_128: int
CALG_AES_192: int
CALG_AES_256: int
CALG_AES: int
CALG_SHA_256: int
CALG_SHA_384: int
CALG_SHA_512: int
CRYPT_VERIFYCONTEXT: int
CRYPT_NEWKEYSET: int
CRYPT_DELETEKEYSET: int
CRYPT_MACHINE_KEYSET: int
CRYPT_SILENT: int
CRYPT_EXPORTABLE: int
CRYPT_USER_PROTECTED: int
CRYPT_CREATE_SALT: int
CRYPT_UPDATE_KEY: int
CRYPT_NO_SALT: int
CRYPT_PREGEN: int
CRYPT_RECIPIENT: int
CRYPT_INITIATOR: int
CRYPT_ONLINE: int
CRYPT_SF: int
CRYPT_CREATE_IV: int
CRYPT_KEK: int
CRYPT_DATA_KEY: int
CRYPT_VOLATILE: int
CRYPT_SGCKEY: int
CRYPT_ARCHIVABLE: int
RSA1024BIT_KEY: int
CRYPT_SERVER: int
KEY_LENGTH_MASK: int
CRYPT_Y_ONLY: int
CRYPT_SSL2_FALLBACK: int
CRYPT_DESTROYKEY: int
CRYPT_OAEP: int
CRYPT_BLOB_VER3: int
CRYPT_IPSEC_HMAC_KEY: int
CRYPT_DECRYPT_RSA_NO_PADDING_CHECK: int
CRYPT_SECRETDIGEST: int
CRYPT_OWF_REPL_LM_HASH: int
CRYPT_LITTLE_ENDIAN: int
CRYPT_NOHASHOID: int
CRYPT_TYPE2_FORMAT: int
CRYPT_X931_FORMAT: int
CRYPT_MACHINE_DEFAULT: int
CRYPT_USER_DEFAULT: int
CRYPT_DELETE_DEFAULT: int
SIMPLEBLOB: int
PUBLICKEYBLOB: int
PRIVATEKEYBLOB: int
PLAINTEXTKEYBLOB: int
OPAQUEKEYBLOB: int
PUBLICKEYBLOBEX: int
SYMMETRICWRAPKEYBLOB: int
AT_KEYEXCHANGE: int
AT_SIGNATURE: int
CRYPT_USERDATA: int
KP_IV: int
KP_SALT: int
KP_PADDING: int
KP_MODE: int
KP_MODE_BITS: int
KP_PERMISSIONS: int
KP_ALGID: int
KP_BLOCKLEN: int
KP_KEYLEN: int
KP_SALT_EX: int
KP_P: int
KP_G: int
KP_Q: int
KP_X: int
KP_Y: int
KP_RA: int
KP_RB: int
KP_INFO: int
KP_EFFECTIVE_KEYLEN: int
KP_SCHANNEL_ALG: int
KP_CLIENT_RANDOM: int
KP_SERVER_RANDOM: int
KP_RP: int
KP_PRECOMP_MD5: int
KP_PRECOMP_SHA: int
KP_CERTIFICATE: int
KP_CLEAR_KEY: int
KP_PUB_EX_LEN: int
KP_PUB_EX_VAL: int
KP_KEYVAL: int
KP_ADMIN_PIN: int
KP_KEYEXCHANGE_PIN: int
KP_SIGNATURE_PIN: int
KP_PREHASH: int
KP_ROUNDS: int
KP_OAEP_PARAMS: int
KP_CMS_KEY_INFO: int
KP_CMS_DH_KEY_INFO: int
KP_PUB_PARAMS: int
KP_VERIFY_PARAMS: int
KP_HIGHEST_VERSION: int
KP_GET_USE_COUNT: int
PKCS5_PADDING: int
RANDOM_PADDING: int
ZERO_PADDING: int
CRYPT_MODE_CBC: int
CRYPT_MODE_ECB: int
CRYPT_MODE_OFB: int
CRYPT_MODE_CFB: int
CRYPT_MODE_CTS: int
CRYPT_ENCRYPT: int
CRYPT_DECRYPT: int
CRYPT_EXPORT: int
CRYPT_READ: int
CRYPT_WRITE: int
CRYPT_MAC: int
CRYPT_EXPORT_KEY: int
CRYPT_IMPORT_KEY: int
CRYPT_ARCHIVE: int
HP_ALGID: int
HP_HASHVAL: int
HP_HASHSIZE: int
HP_HMAC_INFO: int
HP_TLS1PRF_LABEL: int
HP_TLS1PRF_SEED: int
CRYPT_FAILED: int
CRYPT_SUCCEED: int

def RCRYPT_SUCCEEDED(rt: int) -> bool: ...
def RCRYPT_FAILED(rt: int) -> bool: ...

PP_ENUMALGS: int
PP_ENUMCONTAINERS: int
PP_IMPTYPE: int
PP_NAME: int
PP_VERSION: int
PP_CONTAINER: int
PP_CHANGE_PASSWORD: int
PP_KEYSET_SEC_DESCR: int
PP_CERTCHAIN: int
PP_KEY_TYPE_SUBTYPE: int
PP_PROVTYPE: int
PP_KEYSTORAGE: int
PP_APPLI_CERT: int
PP_SYM_KEYSIZE: int
PP_SESSION_KEYSIZE: int
PP_UI_PROMPT: int
PP_ENUMALGS_EX: int
PP_ENUMMANDROOTS: int
PP_ENUMELECTROOTS: int
PP_KEYSET_TYPE: int
PP_ADMIN_PIN: int
PP_KEYEXCHANGE_PIN: int
PP_SIGNATURE_PIN: int
PP_SIG_KEYSIZE_INC: int
PP_KEYX_KEYSIZE_INC: int
PP_UNIQUE_CONTAINER: int
PP_SGC_INFO: int
PP_USE_HARDWARE_RNG: int
PP_KEYSPEC: int
PP_ENUMEX_SIGNING_PROT: int
PP_CRYPT_COUNT_KEY_USE: int
CRYPT_FIRST: int
CRYPT_NEXT: int
CRYPT_SGC_ENUM: int
CRYPT_IMPL_HARDWARE: int
CRYPT_IMPL_SOFTWARE: int
CRYPT_IMPL_MIXED: int
CRYPT_IMPL_UNKNOWN: int
CRYPT_IMPL_REMOVABLE: int
CRYPT_SEC_DESCR: int
CRYPT_PSTORE: int
CRYPT_UI_PROMPT: int
CRYPT_FLAG_PCT1: int
CRYPT_FLAG_SSL2: int
CRYPT_FLAG_SSL3: int
CRYPT_FLAG_TLS1: int
CRYPT_FLAG_IPSEC: int
CRYPT_FLAG_SIGNING: int
CRYPT_SGC: int
CRYPT_FASTSGC: int
PP_CLIENT_HWND: int
PP_CONTEXT_INFO: int
PP_KEYEXCHANGE_KEYSIZE: int
PP_SIGNATURE_KEYSIZE: int
PP_KEYEXCHANGE_ALG: int
PP_SIGNATURE_ALG: int
PP_DELETEKEY: int
PROV_RSA_FULL: int
PROV_RSA_SIG: int
PROV_DSS: int
PROV_FORTEZZA: int
PROV_MS_EXCHANGE: int
PROV_SSL: int
PROV_RSA_SCHANNEL: int
PROV_DSS_DH: int
PROV_EC_ECDSA_SIG: int
PROV_EC_ECNRA_SIG: int
PROV_EC_ECDSA_FULL: int
PROV_EC_ECNRA_FULL: int
PROV_DH_SCHANNEL: int
PROV_SPYRUS_LYNKS: int
PROV_RNG: int
PROV_INTEL_SEC: int
PROV_REPLACE_OWF: int
PROV_RSA_AES: int
MS_DEF_PROV_A: str
MS_DEF_PROV: str
MS_ENHANCED_PROV_A: str
MS_ENHANCED_PROV: str
MS_STRONG_PROV_A: str
MS_STRONG_PROV: str
MS_DEF_RSA_SIG_PROV_A: str
MS_DEF_RSA_SIG_PROV: str
MS_DEF_RSA_SCHANNEL_PROV_A: str
MS_DEF_RSA_SCHANNEL_PROV: str
MS_DEF_DSS_PROV_A: str
MS_DEF_DSS_PROV: str
MS_DEF_DSS_DH_PROV_A: str
MS_DEF_DSS_DH_PROV: str
MS_ENH_DSS_DH_PROV_A: str
MS_ENH_DSS_DH_PROV: str
MS_DEF_DH_SCHANNEL_PROV_A: str
MS_DEF_DH_SCHANNEL_PROV: str
MS_SCARD_PROV_A: str
MS_SCARD_PROV: str
MS_ENH_RSA_AES_PROV_A: str
MS_ENH_RSA_AES_PROV: str
MAXUIDLEN: int
EXPO_OFFLOAD_REG_VALUE: str
EXPO_OFFLOAD_FUNC_NAME: str
szKEY_CRYPTOAPI_PRIVATE_KEY_OPTIONS: str
szFORCE_KEY_PROTECTION: str
dwFORCE_KEY_PROTECTION_DISABLED: int
dwFORCE_KEY_PROTECTION_USER_SELECT: int
dwFORCE_KEY_PROTECTION_HIGH: int
szKEY_CACHE_ENABLED: str
szKEY_CACHE_SECONDS: str
CUR_BLOB_VERSION: int
SCHANNEL_MAC_KEY: int
SCHANNEL_ENC_KEY: int
INTERNATIONAL_USAGE: int
szOID_RSA: str
szOID_PKCS: str
szOID_RSA_HASH: str
szOID_RSA_ENCRYPT: str
szOID_PKCS_1: str
szOID_PKCS_2: str
szOID_PKCS_3: str
szOID_PKCS_4: str
szOID_PKCS_5: str
szOID_PKCS_6: str
szOID_PKCS_7: str
szOID_PKCS_8: str
szOID_PKCS_9: str
szOID_PKCS_10: str
szOID_PKCS_12: str
szOID_RSA_RSA: str
szOID_RSA_MD2RSA: str
szOID_RSA_MD4RSA: str
szOID_RSA_MD5RSA: str
szOID_RSA_SHA1RSA: str
szOID_RSA_SETOAEP_RSA: str
szOID_RSA_DH: str
szOID_RSA_data: str
szOID_RSA_signedData: str
szOID_RSA_envelopedData: str
szOID_RSA_signEnvData: str
szOID_RSA_digestedData: str
szOID_RSA_hashedData: str
szOID_RSA_encryptedData: str
szOID_RSA_emailAddr: str
szOID_RSA_unstructName: str
szOID_RSA_contentType: str
szOID_RSA_messageDigest: str
szOID_RSA_signingTime: str
szOID_RSA_counterSign: str
szOID_RSA_challengePwd: str
szOID_RSA_unstructAddr: str
szOID_RSA_extCertAttrs: str
szOID_RSA_certExtensions: str
szOID_RSA_SMIMECapabilities: str
szOID_RSA_preferSignedData: str
szOID_RSA_SMIMEalg: str
szOID_RSA_SMIMEalgESDH: str
szOID_RSA_SMIMEalgCMS3DESwrap: str
szOID_RSA_SMIMEalgCMSRC2wrap: str
szOID_RSA_MD2: str
szOID_RSA_MD4: str
szOID_RSA_MD5: str
szOID_RSA_RC2CBC: str
szOID_RSA_RC4: str
szOID_RSA_DES_EDE3_CBC: str
szOID_RSA_RC5_CBCPad: str
szOID_ANSI_X942: str
szOID_ANSI_X942_DH: str
szOID_X957: str
szOID_X957_DSA: str
szOID_X957_SHA1DSA: str
szOID_DS: str
szOID_DSALG: str
szOID_DSALG_CRPT: str
szOID_DSALG_HASH: str
szOID_DSALG_SIGN: str
szOID_DSALG_RSA: str
szOID_OIW: str
szOID_OIWSEC: str
szOID_OIWSEC_md4RSA: str
szOID_OIWSEC_md5RSA: str
szOID_OIWSEC_md4RSA2: str
szOID_OIWSEC_desECB: str
szOID_OIWSEC_desCBC: str
szOID_OIWSEC_desOFB: str
szOID_OIWSEC_desCFB: str
szOID_OIWSEC_desMAC: str
szOID_OIWSEC_rsaSign: str
szOID_OIWSEC_dsa: str
szOID_OIWSEC_shaDSA: str
szOID_OIWSEC_mdc2RSA: str
szOID_OIWSEC_shaRSA: str
szOID_OIWSEC_dhCommMod: str
szOID_OIWSEC_desEDE: str
szOID_OIWSEC_sha: str
szOID_OIWSEC_mdc2: str
szOID_OIWSEC_dsaComm: str
szOID_OIWSEC_dsaCommSHA: str
szOID_OIWSEC_rsaXchg: str
szOID_OIWSEC_keyHashSeal: str
szOID_OIWSEC_md2RSASign: str
szOID_OIWSEC_md5RSASign: str
szOID_OIWSEC_sha1: str
szOID_OIWSEC_dsaSHA1: str
szOID_OIWSEC_dsaCommSHA1: str
szOID_OIWSEC_sha1RSASign: str
szOID_OIWDIR: str
szOID_OIWDIR_CRPT: str
szOID_OIWDIR_HASH: str
szOID_OIWDIR_SIGN: str
szOID_OIWDIR_md2: str
szOID_OIWDIR_md2RSA: str
szOID_INFOSEC: str
szOID_INFOSEC_sdnsSignature: str
szOID_INFOSEC_mosaicSignature: str
szOID_INFOSEC_sdnsConfidentiality: str
szOID_INFOSEC_mosaicConfidentiality: str
szOID_INFOSEC_sdnsIntegrity: str
szOID_INFOSEC_mosaicIntegrity: str
szOID_INFOSEC_sdnsTokenProtection: str
szOID_INFOSEC_mosaicTokenProtection: str
szOID_INFOSEC_sdnsKeyManagement: str
szOID_INFOSEC_mosaicKeyManagement: str
szOID_INFOSEC_sdnsKMandSig: str
szOID_INFOSEC_mosaicKMandSig: str
szOID_INFOSEC_SuiteASignature: str
szOID_INFOSEC_SuiteAConfidentiality: str
szOID_INFOSEC_SuiteAIntegrity: str
szOID_INFOSEC_SuiteATokenProtection: str
szOID_INFOSEC_SuiteAKeyManagement: str
szOID_INFOSEC_SuiteAKMandSig: str
szOID_INFOSEC_mosaicUpdatedSig: str
szOID_INFOSEC_mosaicKMandUpdSig: str
szOID_INFOSEC_mosaicUpdatedInteg: str
szOID_COMMON_NAME: str
szOID_SUR_NAME: str
szOID_DEVICE_SERIAL_NUMBER: str
szOID_COUNTRY_NAME: str
szOID_LOCALITY_NAME: str
szOID_STATE_OR_PROVINCE_NAME: str
szOID_STREET_ADDRESS: str
szOID_ORGANIZATION_NAME: str
szOID_ORGANIZATIONAL_UNIT_NAME: str
szOID_TITLE: str
szOID_DESCRIPTION: str
szOID_SEARCH_GUIDE: str
szOID_BUSINESS_CATEGORY: str
szOID_POSTAL_ADDRESS: str
szOID_POSTAL_CODE: str
szOID_POST_OFFICE_BOX: str
szOID_PHYSICAL_DELIVERY_OFFICE_NAME: str
szOID_TELEPHONE_NUMBER: str
szOID_TELEX_NUMBER: str
szOID_TELETEXT_TERMINAL_IDENTIFIER: str
szOID_FACSIMILE_TELEPHONE_NUMBER: str
szOID_X21_ADDRESS: str
szOID_INTERNATIONAL_ISDN_NUMBER: str
szOID_REGISTERED_ADDRESS: str
szOID_DESTINATION_INDICATOR: str
szOID_PREFERRED_DELIVERY_METHOD: str
szOID_PRESENTATION_ADDRESS: str
szOID_SUPPORTED_APPLICATION_CONTEXT: str
szOID_MEMBER: str
szOID_OWNER: str
szOID_ROLE_OCCUPANT: str
szOID_SEE_ALSO: str
szOID_USER_PASSWORD: str
szOID_USER_CERTIFICATE: str
szOID_CA_CERTIFICATE: str
szOID_CROSS_CERTIFICATE_PAIR: str
szOID_GIVEN_NAME: str
szOID_INITIALS: str
szOID_DN_QUALIFIER: str
szOID_DOMAIN_COMPONENT: str
szOID_PKCS_12_FRIENDLY_NAME_ATTR: str
szOID_PKCS_12_LOCAL_KEY_ID: str
szOID_PKCS_12_KEY_PROVIDER_NAME_ATTR: str
szOID_LOCAL_MACHINE_KEYSET: str
szOID_KEYID_RDN: str
CERT_RDN_ANY_TYPE: int
CERT_RDN_ENCODED_BLOB: int
CERT_RDN_OCTET_STRING: int
CERT_RDN_NUMERIC_STRING: int
CERT_RDN_PRINTABLE_STRING: int
CERT_RDN_TELETEX_STRING: int
CERT_RDN_T61_STRING: int
CERT_RDN_VIDEOTEX_STRING: int
CERT_RDN_IA5_STRING: int
CERT_RDN_GRAPHIC_STRING: int
CERT_RDN_VISIBLE_STRING: int
CERT_RDN_ISO646_STRING: int
CERT_RDN_GENERAL_STRING: int
CERT_RDN_UNIVERSAL_STRING: int
CERT_RDN_INT4_STRING: int
CERT_RDN_BMP_STRING: int
CERT_RDN_UNICODE_STRING: int
CERT_RDN_UTF8_STRING: int
CERT_RDN_TYPE_MASK: int
CERT_RDN_FLAGS_MASK: int
CERT_RDN_ENABLE_T61_UNICODE_FLAG: int
CERT_RDN_ENABLE_UTF8_UNICODE_FLAG: int
CERT_RDN_DISABLE_CHECK_TYPE_FLAG: int
CERT_RDN_DISABLE_IE4_UTF8_FLAG: int
CERT_RSA_PUBLIC_KEY_OBJID: str
CERT_DEFAULT_OID_PUBLIC_KEY_SIGN: str
CERT_DEFAULT_OID_PUBLIC_KEY_XCHG: str
CERT_V1: int
CERT_V2: int
CERT_V3: int
CERT_INFO_VERSION_FLAG: int
CERT_INFO_SERIAL_NUMBER_FLAG: int
CERT_INFO_SIGNATURE_ALGORITHM_FLAG: int
CERT_INFO_ISSUER_FLAG: int
CERT_INFO_NOT_BEFORE_FLAG: int
CERT_INFO_NOT_AFTER_FLAG: int
CERT_INFO_SUBJECT_FLAG: int
CERT_INFO_SUBJECT_PUBLIC_KEY_INFO_FLAG: int
CERT_INFO_ISSUER_UNIQUE_ID_FLAG: int
CERT_INFO_SUBJECT_UNIQUE_ID_FLAG: int
CERT_INFO_EXTENSION_FLAG: int
CRL_V1: int
CRL_V2: int
CERT_REQUEST_V1: int
CERT_KEYGEN_REQUEST_V1: int
CTL_V1: int
CERT_ENCODING_TYPE_MASK: int
CMSG_ENCODING_TYPE_MASK: int

def GET_CERT_ENCODING_TYPE(X: int) -> int: ...
def GET_CMSG_ENCODING_TYPE(X: int) -> int: ...

CRYPT_ASN_ENCODING: int
CRYPT_NDR_ENCODING: int
X509_ASN_ENCODING: int
X509_NDR_ENCODING: int
PKCS_7_ASN_ENCODING: int
PKCS_7_NDR_ENCODING: int
CRYPT_FORMAT_STR_MULTI_LINE: int
CRYPT_FORMAT_STR_NO_HEX: int
CRYPT_FORMAT_SIMPLE: int
CRYPT_FORMAT_X509: int
CRYPT_FORMAT_OID: int
CRYPT_FORMAT_RDN_SEMICOLON: int
CRYPT_FORMAT_RDN_CRLF: int
CRYPT_FORMAT_RDN_UNQUOTE: int
CRYPT_FORMAT_RDN_REVERSE: int
CRYPT_FORMAT_COMMA: int
CRYPT_FORMAT_SEMICOLON: int
CRYPT_FORMAT_CRLF: int
CRYPT_ENCODE_NO_SIGNATURE_BYTE_REVERSAL_FLAG: int
CRYPT_ENCODE_ALLOC_FLAG: int
CRYPT_UNICODE_NAME_ENCODE_ENABLE_T61_UNICODE_FLAG: int
CRYPT_UNICODE_NAME_ENCODE_ENABLE_UTF8_UNICODE_FLAG: int
CRYPT_UNICODE_NAME_ENCODE_DISABLE_CHECK_TYPE_FLAG: int
CRYPT_SORTED_CTL_ENCODE_HASHED_SUBJECT_IDENTIFIER_FLAG: int
CRYPT_DECODE_NOCOPY_FLAG: int
CRYPT_DECODE_TO_BE_SIGNED_FLAG: int
CRYPT_DECODE_SHARE_OID_STRING_FLAG: int
CRYPT_DECODE_NO_SIGNATURE_BYTE_REVERSAL_FLAG: int
CRYPT_DECODE_ALLOC_FLAG: int
CRYPT_UNICODE_NAME_DECODE_DISABLE_IE4_UTF8_FLAG: int
CRYPT_ENCODE_DECODE_NONE: int
X509_CERT: int
X509_CERT_TO_BE_SIGNED: int
X509_CERT_CRL_TO_BE_SIGNED: int
X509_CERT_REQUEST_TO_BE_SIGNED: int
X509_EXTENSIONS: int
X509_NAME_VALUE: int
X509_NAME: int
X509_PUBLIC_KEY_INFO: int
X509_AUTHORITY_KEY_ID: int
X509_KEY_ATTRIBUTES: int
X509_KEY_USAGE_RESTRICTION: int
X509_ALTERNATE_NAME: int
X509_BASIC_CONSTRAINTS: int
X509_KEY_USAGE: int
X509_BASIC_CONSTRAINTS2: int
X509_CERT_POLICIES: int
PKCS_UTC_TIME: int
PKCS_TIME_REQUEST: int
RSA_CSP_PUBLICKEYBLOB: int
X509_UNICODE_NAME: int
X509_KEYGEN_REQUEST_TO_BE_SIGNED: int
PKCS_ATTRIBUTE: int
PKCS_CONTENT_INFO_SEQUENCE_OF_ANY: int
X509_UNICODE_NAME_VALUE: int
X509_ANY_STRING: int
X509_UNICODE_ANY_STRING: int
X509_OCTET_STRING: int
X509_BITS: int
X509_INTEGER: int
X509_MULTI_BYTE_INTEGER: int
X509_ENUMERATED: int
X509_CHOICE_OF_TIME: int
X509_AUTHORITY_KEY_ID2: int
X509_AUTHORITY_INFO_ACCESS: int
X509_SUBJECT_INFO_ACCESS: int
X509_CRL_REASON_CODE: int
PKCS_CONTENT_INFO: int
X509_SEQUENCE_OF_ANY: int
X509_CRL_DIST_POINTS: int
X509_ENHANCED_KEY_USAGE: int
PKCS_CTL: int
X509_MULTI_BYTE_UINT: int
X509_DSS_PUBLICKEY: int
X509_DSS_PARAMETERS: int
X509_DSS_SIGNATURE: int
PKCS_RC2_CBC_PARAMETERS: int
PKCS_SMIME_CAPABILITIES: int
X509_QC_STATEMENTS_EXT: int
PKCS_RSA_PRIVATE_KEY: int
PKCS_PRIVATE_KEY_INFO: int
PKCS_ENCRYPTED_PRIVATE_KEY_INFO: int
X509_PKIX_POLICY_QUALIFIER_USERNOTICE: int
X509_DH_PUBLICKEY: int
X509_DH_PARAMETERS: int
PKCS_ATTRIBUTES: int
PKCS_SORTED_CTL: int
X509_ECC_SIGNATURE: int
X942_DH_PARAMETERS: int
X509_BITS_WITHOUT_TRAILING_ZEROES: int
X942_OTHER_INFO: int
X509_CERT_PAIR: int
X509_ISSUING_DIST_POINT: int
X509_NAME_CONSTRAINTS: int
X509_POLICY_MAPPINGS: int
X509_POLICY_CONSTRAINTS: int
X509_CROSS_CERT_DIST_POINTS: int
CMC_DATA: int
CMC_RESPONSE: int
CMC_STATUS: int
CMC_ADD_EXTENSIONS: int
CMC_ADD_ATTRIBUTES: int
X509_CERTIFICATE_TEMPLATE: int
OCSP_SIGNED_REQUEST: int
OCSP_REQUEST: int
OCSP_RESPONSE: int
OCSP_BASIC_SIGNED_RESPONSE: int
OCSP_BASIC_RESPONSE: int
X509_LOGOTYPE_EXT: int
X509_BIOMETRIC_EXT: int
CNG_RSA_PUBLIC_KEY_BLOB: int
X509_OBJECT_IDENTIFIER: int
X509_ALGORITHM_IDENTIFIER: int
PKCS_RSA_SSA_PSS_PARAMETERS: int
PKCS_RSAES_OAEP_PARAMETERS: int
ECC_CMS_SHARED_INFO: int
TIMESTAMP_REQUEST: int
TIMESTAMP_RESPONSE: int
TIMESTAMP_INFO: int
X509_CERT_BUNDLE: int
PKCS7_SIGNER_INFO: int
CMS_SIGNER_INFO: int
szOID_AUTHORITY_KEY_IDENTIFIER: str
szOID_KEY_ATTRIBUTES: str
szOID_CERT_POLICIES_95: str
szOID_KEY_USAGE_RESTRICTION: str
szOID_SUBJECT_ALT_NAME: str
szOID_ISSUER_ALT_NAME: str
szOID_BASIC_CONSTRAINTS: str
szOID_KEY_USAGE: str
szOID_PRIVATEKEY_USAGE_PERIOD: str
szOID_BASIC_CONSTRAINTS2: str
szOID_CERT_POLICIES: str
szOID_ANY_CERT_POLICY: str
szOID_AUTHORITY_KEY_IDENTIFIER2: str
szOID_SUBJECT_KEY_IDENTIFIER: str
szOID_SUBJECT_ALT_NAME2: str
szOID_ISSUER_ALT_NAME2: str
szOID_CRL_REASON_CODE: str
szOID_REASON_CODE_HOLD: str
szOID_CRL_DIST_POINTS: str
szOID_ENHANCED_KEY_USAGE: str
szOID_CRL_NUMBER: str
szOID_DELTA_CRL_INDICATOR: str
szOID_ISSUING_DIST_POINT: str
szOID_FRESHEST_CRL: str
szOID_NAME_CONSTRAINTS: str
szOID_POLICY_MAPPINGS: str
szOID_LEGACY_POLICY_MAPPINGS: str
szOID_POLICY_CONSTRAINTS: str
szOID_RENEWAL_CERTIFICATE: str
szOID_ENROLLMENT_NAME_VALUE_PAIR: str
szOID_ENROLLMENT_CSP_PROVIDER: str
szOID_OS_VERSION: str
szOID_ENROLLMENT_AGENT: str
szOID_PKIX: str
szOID_PKIX_PE: str
szOID_AUTHORITY_INFO_ACCESS: str
szOID_CERT_EXTENSIONS: str
szOID_NEXT_UPDATE_LOCATION: str
szOID_REMOVE_CERTIFICATE: str
szOID_CROSS_CERT_DIST_POINTS: str
szOID_CTL: str
szOID_SORTED_CTL: str
szOID_SERIALIZED: str
szOID_NT_PRINCIPAL_NAME: str
szOID_PRODUCT_UPDATE: str
szOID_ANY_APPLICATION_POLICY: str
szOID_AUTO_ENROLL_CTL_USAGE: str
szOID_ENROLL_CERTTYPE_EXTENSION: str
szOID_CERT_MANIFOLD: str
szOID_CERTSRV_CA_VERSION: str
szOID_CERTSRV_PREVIOUS_CERT_HASH: str
szOID_CRL_VIRTUAL_BASE: str
szOID_CRL_NEXT_PUBLISH: str
szOID_KP_CA_EXCHANGE: str
szOID_KP_KEY_RECOVERY_AGENT: str
szOID_CERTIFICATE_TEMPLATE: str
szOID_ENTERPRISE_OID_ROOT: str
szOID_RDN_DUMMY_SIGNER: str
szOID_APPLICATION_CERT_POLICIES: str
szOID_APPLICATION_POLICY_MAPPINGS: str
szOID_APPLICATION_POLICY_CONSTRAINTS: str
szOID_ARCHIVED_KEY_ATTR: str
szOID_CRL_SELF_CDP: str
szOID_REQUIRE_CERT_CHAIN_POLICY: str
szOID_ARCHIVED_KEY_CERT_HASH: str
szOID_ISSUED_CERT_HASH: str
szOID_DS_EMAIL_REPLICATION: str
szOID_REQUEST_CLIENT_INFO: str
szOID_ENCRYPTED_KEY_HASH: str
szOID_CERTSRV_CROSSCA_VERSION: str
szOID_NTDS_REPLICATION: str
szOID_SUBJECT_DIR_ATTRS: str
szOID_PKIX_KP: str
szOID_PKIX_KP_SERVER_AUTH: str
szOID_PKIX_KP_CLIENT_AUTH: str
szOID_PKIX_KP_CODE_SIGNING: str
szOID_PKIX_KP_EMAIL_PROTECTION: str
szOID_PKIX_KP_IPSEC_END_SYSTEM: str
szOID_PKIX_KP_IPSEC_TUNNEL: str
szOID_PKIX_KP_IPSEC_USER: str
szOID_PKIX_KP_TIMESTAMP_SIGNING: str
szOID_IPSEC_KP_IKE_INTERMEDIATE: str
szOID_KP_CTL_USAGE_SIGNING: str
szOID_KP_TIME_STAMP_SIGNING: str
szOID_SERVER_GATED_CRYPTO: str
szOID_SGC_NETSCAPE: str
szOID_KP_EFS: str
szOID_EFS_RECOVERY: str
szOID_WHQL_CRYPTO: str
szOID_NT5_CRYPTO: str
szOID_OEM_WHQL_CRYPTO: str
szOID_EMBEDDED_NT_CRYPTO: str
szOID_KP_QUALIFIED_SUBORDINATION: str
szOID_KP_KEY_RECOVERY: str
szOID_KP_DOCUMENT_SIGNING: str
szOID_KP_LIFETIME_SIGNING: str
szOID_KP_MOBILE_DEVICE_SOFTWARE: str
szOID_DRM: str
szOID_DRM_INDIVIDUALIZATION: str
szOID_LICENSES: str
szOID_LICENSE_SERVER: str
szOID_KP_SMARTCARD_LOGON: str
szOID_YESNO_TRUST_ATTR: str
szOID_PKIX_POLICY_QUALIFIER_CPS: str
szOID_PKIX_POLICY_QUALIFIER_USERNOTICE: str
szOID_CERT_POLICIES_95_QUALIFIER1: str
CERT_UNICODE_RDN_ERR_INDEX_MASK: int
CERT_UNICODE_RDN_ERR_INDEX_SHIFT: int
CERT_UNICODE_ATTR_ERR_INDEX_MASK: int
CERT_UNICODE_ATTR_ERR_INDEX_SHIFT: int
CERT_UNICODE_VALUE_ERR_INDEX_MASK: int
CERT_UNICODE_VALUE_ERR_INDEX_SHIFT: int
CERT_DIGITAL_SIGNATURE_KEY_USAGE: int
CERT_NON_REPUDIATION_KEY_USAGE: int
CERT_KEY_ENCIPHERMENT_KEY_USAGE: int
CERT_DATA_ENCIPHERMENT_KEY_USAGE: int
CERT_KEY_AGREEMENT_KEY_USAGE: int
CERT_KEY_CERT_SIGN_KEY_USAGE: int
CERT_OFFLINE_CRL_SIGN_KEY_USAGE: int
CERT_CRL_SIGN_KEY_USAGE: int
CERT_ENCIPHER_ONLY_KEY_USAGE: int
CERT_DECIPHER_ONLY_KEY_USAGE: int
CERT_ALT_NAME_OTHER_NAME: int
CERT_ALT_NAME_RFC822_NAME: int
CERT_ALT_NAME_DNS_NAME: int
CERT_ALT_NAME_X400_ADDRESS: int
CERT_ALT_NAME_DIRECTORY_NAME: int
CERT_ALT_NAME_EDI_PARTY_NAME: int
CERT_ALT_NAME_URL: int
CERT_ALT_NAME_IP_ADDRESS: int
CERT_ALT_NAME_REGISTERED_ID: int
CERT_ALT_NAME_ENTRY_ERR_INDEX_MASK: int
CERT_ALT_NAME_ENTRY_ERR_INDEX_SHIFT: int
CERT_ALT_NAME_VALUE_ERR_INDEX_MASK: int
CERT_ALT_NAME_VALUE_ERR_INDEX_SHIFT: int
CERT_CA_SUBJECT_FLAG: int
CERT_END_ENTITY_SUBJECT_FLAG: int
szOID_PKIX_ACC_DESCR: str
szOID_PKIX_OCSP: str
szOID_PKIX_CA_ISSUERS: str
CRL_REASON_UNSPECIFIED: int
CRL_REASON_KEY_COMPROMISE: int
CRL_REASON_CA_COMPROMISE: int
CRL_REASON_AFFILIATION_CHANGED: int
CRL_REASON_SUPERSEDED: int
CRL_REASON_CESSATION_OF_OPERATION: int
CRL_REASON_CERTIFICATE_HOLD: int
CRL_REASON_REMOVE_FROM_CRL: int
CRL_DIST_POINT_NO_NAME: int
CRL_DIST_POINT_FULL_NAME: int
CRL_DIST_POINT_ISSUER_RDN_NAME: int
CRL_REASON_UNUSED_FLAG: int
CRL_REASON_KEY_COMPROMISE_FLAG: int
CRL_REASON_CA_COMPROMISE_FLAG: int
CRL_REASON_AFFILIATION_CHANGED_FLAG: int
CRL_REASON_SUPERSEDED_FLAG: int
CRL_REASON_CESSATION_OF_OPERATION_FLAG: int
CRL_REASON_CERTIFICATE_HOLD_FLAG: int
CRL_DIST_POINT_ERR_INDEX_MASK: int
CRL_DIST_POINT_ERR_INDEX_SHIFT: int
CRL_DIST_POINT_ERR_CRL_ISSUER_BIT: int
CROSS_CERT_DIST_POINT_ERR_INDEX_MASK: int
CROSS_CERT_DIST_POINT_ERR_INDEX_SHIFT: int
CERT_EXCLUDED_SUBTREE_BIT: int
SORTED_CTL_EXT_FLAGS_OFFSET: int
SORTED_CTL_EXT_COUNT_OFFSET: int
SORTED_CTL_EXT_MAX_COLLISION_OFFSET: int
SORTED_CTL_EXT_HASH_BUCKET_OFFSET: int
SORTED_CTL_EXT_HASHED_SUBJECT_IDENTIFIER_FLAG: int
CERT_DSS_R_LEN: int
CERT_DSS_S_LEN: int
CERT_DSS_SIGNATURE_LEN: int
CERT_MAX_ASN_ENCODED_DSS_SIGNATURE_LEN: int
CRYPT_X942_COUNTER_BYTE_LENGTH: int
CRYPT_X942_KEY_LENGTH_BYTE_LENGTH: int
CRYPT_X942_PUB_INFO_BYTE_LENGTH: float
CRYPT_RC2_40BIT_VERSION: int
CRYPT_RC2_56BIT_VERSION: int
CRYPT_RC2_64BIT_VERSION: int
CRYPT_RC2_128BIT_VERSION: int
szOID_VERISIGN_PRIVATE_6_9: str
szOID_VERISIGN_ONSITE_JURISDICTION_HASH: str
szOID_VERISIGN_BITSTRING_6_13: str
szOID_VERISIGN_ISS_STRONG_CRYPTO: str
szOID_NETSCAPE: str
szOID_NETSCAPE_CERT_EXTENSION: str
szOID_NETSCAPE_CERT_TYPE: str
szOID_NETSCAPE_BASE_URL: str
szOID_NETSCAPE_REVOCATION_URL: str
szOID_NETSCAPE_CA_REVOCATION_URL: str
szOID_NETSCAPE_CERT_RENEWAL_URL: str
szOID_NETSCAPE_CA_POLICY_URL: str
szOID_NETSCAPE_SSL_SERVER_NAME: str
szOID_NETSCAPE_COMMENT: str
szOID_NETSCAPE_DATA_TYPE: str
szOID_NETSCAPE_CERT_SEQUENCE: str
NETSCAPE_SSL_CLIENT_AUTH_CERT_TYPE: int
NETSCAPE_SSL_SERVER_AUTH_CERT_TYPE: int
NETSCAPE_SMIME_CERT_TYPE: int
NETSCAPE_SIGN_CERT_TYPE: int
NETSCAPE_SSL_CA_CERT_TYPE: int
NETSCAPE_SMIME_CA_CERT_TYPE: int
NETSCAPE_SIGN_CA_CERT_TYPE: int
szOID_CT_PKI_DATA: str
szOID_CT_PKI_RESPONSE: str
szOID_PKIX_NO_SIGNATURE: str
szOID_CMC: str
szOID_CMC_STATUS_INFO: str
szOID_CMC_IDENTIFICATION: str
szOID_CMC_IDENTITY_PROOF: str
szOID_CMC_DATA_RETURN: str
szOID_CMC_TRANSACTION_ID: str
szOID_CMC_SENDER_NONCE: str
szOID_CMC_RECIPIENT_NONCE: str
szOID_CMC_ADD_EXTENSIONS: str
szOID_CMC_ENCRYPTED_POP: str
szOID_CMC_DECRYPTED_POP: str
szOID_CMC_LRA_POP_WITNESS: str
szOID_CMC_GET_CERT: str
szOID_CMC_GET_CRL: str
szOID_CMC_REVOKE_REQUEST: str
szOID_CMC_REG_INFO: str
szOID_CMC_RESPONSE_INFO: str
szOID_CMC_QUERY_PENDING: str
szOID_CMC_ID_POP_LINK_RANDOM: str
szOID_CMC_ID_POP_LINK_WITNESS: str
szOID_CMC_ID_CONFIRM_CERT_ACCEPTANCE: str
szOID_CMC_ADD_ATTRIBUTES: str
CMC_TAGGED_CERT_REQUEST_CHOICE: int
CMC_OTHER_INFO_NO_CHOICE: int
CMC_OTHER_INFO_FAIL_CHOICE: int
CMC_OTHER_INFO_PEND_CHOICE: int
CMC_STATUS_SUCCESS: int
CMC_STATUS_FAILED: int
CMC_STATUS_PENDING: int
CMC_STATUS_NO_SUPPORT: int
CMC_STATUS_CONFIRM_REQUIRED: int
CMC_FAIL_BAD_ALG: int
CMC_FAIL_BAD_MESSAGE_CHECK: int
CMC_FAIL_BAD_REQUEST: int
CMC_FAIL_BAD_TIME: int
CMC_FAIL_BAD_CERT_ID: int
CMC_FAIL_UNSUPORTED_EXT: int
CMC_FAIL_MUST_ARCHIVE_KEYS: int
CMC_FAIL_BAD_IDENTITY: int
CMC_FAIL_POP_REQUIRED: int
CMC_FAIL_POP_FAILED: int
CMC_FAIL_NO_KEY_REUSE: int
CMC_FAIL_INTERNAL_CA_ERROR: int
CMC_FAIL_TRY_LATER: int
CRYPT_OID_ENCODE_OBJECT_FUNC: str
CRYPT_OID_DECODE_OBJECT_FUNC: str
CRYPT_OID_ENCODE_OBJECT_EX_FUNC: str
CRYPT_OID_DECODE_OBJECT_EX_FUNC: str
CRYPT_OID_CREATE_COM_OBJECT_FUNC: str
CRYPT_OID_VERIFY_REVOCATION_FUNC: str
CRYPT_OID_VERIFY_CTL_USAGE_FUNC: str
CRYPT_OID_FORMAT_OBJECT_FUNC: str
CRYPT_OID_FIND_OID_INFO_FUNC: str
CRYPT_OID_FIND_LOCALIZED_NAME_FUNC: str
CRYPT_OID_REGPATH: str
CRYPT_OID_REG_ENCODING_TYPE_PREFIX: str
CRYPT_OID_REG_DLL_VALUE_NAME: str
CRYPT_OID_REG_FUNC_NAME_VALUE_NAME: str
CRYPT_OID_REG_FUNC_NAME_VALUE_NAME_A: str
CRYPT_OID_REG_FLAGS_VALUE_NAME: str
CRYPT_DEFAULT_OID: str
CRYPT_INSTALL_OID_FUNC_BEFORE_FLAG: int
CRYPT_GET_INSTALLED_OID_FUNC_FLAG: int
CRYPT_REGISTER_FIRST_INDEX: int
CRYPT_REGISTER_LAST_INDEX: int
CRYPT_MATCH_ANY_ENCODING_TYPE: int
CRYPT_HASH_ALG_OID_GROUP_ID: int
CRYPT_ENCRYPT_ALG_OID_GROUP_ID: int
CRYPT_PUBKEY_ALG_OID_GROUP_ID: int
CRYPT_SIGN_ALG_OID_GROUP_ID: int
CRYPT_RDN_ATTR_OID_GROUP_ID: int
CRYPT_EXT_OR_ATTR_OID_GROUP_ID: int
CRYPT_ENHKEY_USAGE_OID_GROUP_ID: int
CRYPT_POLICY_OID_GROUP_ID: int
CRYPT_TEMPLATE_OID_GROUP_ID: int
CRYPT_LAST_OID_GROUP_ID: int
CRYPT_FIRST_ALG_OID_GROUP_ID: int
CRYPT_LAST_ALG_OID_GROUP_ID: int
CRYPT_OID_INHIBIT_SIGNATURE_FORMAT_FLAG: int
CRYPT_OID_USE_PUBKEY_PARA_FOR_PKCS7_FLAG: int
CRYPT_OID_NO_NULL_ALGORITHM_PARA_FLAG: int
CRYPT_OID_INFO_OID_KEY: int
CRYPT_OID_INFO_NAME_KEY: int
CRYPT_OID_INFO_ALGID_KEY: int
CRYPT_OID_INFO_SIGN_KEY: int
CRYPT_INSTALL_OID_INFO_BEFORE_FLAG: int
CRYPT_LOCALIZED_NAME_ENCODING_TYPE: int
CRYPT_LOCALIZED_NAME_OID: str
szOID_PKCS_7_DATA: str
szOID_PKCS_7_SIGNED: str
szOID_PKCS_7_ENVELOPED: str
szOID_PKCS_7_SIGNEDANDENVELOPED: str
szOID_PKCS_7_DIGESTED: str
szOID_PKCS_7_ENCRYPTED: str
szOID_PKCS_9_CONTENT_TYPE: str
szOID_PKCS_9_MESSAGE_DIGEST: str
CMSG_DATA: int
CMSG_SIGNED: int
CMSG_ENVELOPED: int
CMSG_SIGNED_AND_ENVELOPED: int
CMSG_HASHED: int
CMSG_ENCRYPTED: int
CMSG_ALL_FLAGS: int
CMSG_DATA_FLAG: int
CMSG_SIGNED_FLAG: int
CMSG_ENVELOPED_FLAG: int
CMSG_SIGNED_AND_ENVELOPED_FLAG: int
CMSG_HASHED_FLAG: int
CMSG_ENCRYPTED_FLAG: int
CERT_ID_ISSUER_SERIAL_NUMBER: int
CERT_ID_KEY_IDENTIFIER: int
CERT_ID_SHA1_HASH: int
CMSG_KEY_AGREE_EPHEMERAL_KEY_CHOICE: int
CMSG_KEY_AGREE_STATIC_KEY_CHOICE: int
CMSG_KEY_TRANS_RECIPIENT: int
CMSG_KEY_AGREE_RECIPIENT: int
CMSG_SP3_COMPATIBLE_ENCRYPT_FLAG: int
CMSG_RC4_NO_SALT_FLAG: int
CMSG_INDEFINITE_LENGTH: int
CMSG_BARE_CONTENT_FLAG: int
CMSG_LENGTH_ONLY_FLAG: int
CMSG_DETACHED_FLAG: int
CMSG_AUTHENTICATED_ATTRIBUTES_FLAG: int
CMSG_CONTENTS_OCTETS_FLAG: int
CMSG_MAX_LENGTH_FLAG: int
CMSG_CMS_ENCAPSULATED_CONTENT_FLAG: int
CMSG_CRYPT_RELEASE_CONTEXT_FLAG: int
CMSG_TYPE_PARAM: int
CMSG_CONTENT_PARAM: int
CMSG_BARE_CONTENT_PARAM: int
CMSG_INNER_CONTENT_TYPE_PARAM: int
CMSG_SIGNER_COUNT_PARAM: int
CMSG_SIGNER_INFO_PARAM: int
CMSG_SIGNER_CERT_INFO_PARAM: int
CMSG_SIGNER_HASH_ALGORITHM_PARAM: int
CMSG_SIGNER_AUTH_ATTR_PARAM: int
CMSG_SIGNER_UNAUTH_ATTR_PARAM: int
CMSG_CERT_COUNT_PARAM: int
CMSG_CERT_PARAM: int
CMSG_CRL_COUNT_PARAM: int
CMSG_CRL_PARAM: int
CMSG_ENVELOPE_ALGORITHM_PARAM: int
CMSG_RECIPIENT_COUNT_PARAM: int
CMSG_RECIPIENT_INDEX_PARAM: int
CMSG_RECIPIENT_INFO_PARAM: int
CMSG_HASH_ALGORITHM_PARAM: int
CMSG_HASH_DATA_PARAM: int
CMSG_COMPUTED_HASH_PARAM: int
CMSG_ENCRYPT_PARAM: int
CMSG_ENCRYPTED_DIGEST: int
CMSG_ENCODED_SIGNER: int
CMSG_ENCODED_MESSAGE: int
CMSG_VERSION_PARAM: int
CMSG_ATTR_CERT_COUNT_PARAM: int
CMSG_ATTR_CERT_PARAM: int
CMSG_CMS_RECIPIENT_COUNT_PARAM: int
CMSG_CMS_RECIPIENT_INDEX_PARAM: int
CMSG_CMS_RECIPIENT_ENCRYPTED_KEY_INDEX_PARAM: int
CMSG_CMS_RECIPIENT_INFO_PARAM: int
CMSG_UNPROTECTED_ATTR_PARAM: int
CMSG_SIGNER_CERT_ID_PARAM: int
CMSG_CMS_SIGNER_INFO_PARAM: int
CMSG_SIGNED_DATA_V1: int
CMSG_SIGNED_DATA_V3: int
CMSG_SIGNED_DATA_PKCS_1_5_VERSION: int
CMSG_SIGNED_DATA_CMS_VERSION: int
CMSG_SIGNER_INFO_V1: int
CMSG_SIGNER_INFO_V3: int
CMSG_SIGNER_INFO_PKCS_1_5_VERSION: int
CMSG_SIGNER_INFO_CMS_VERSION: int
CMSG_HASHED_DATA_V0: int
CMSG_HASHED_DATA_V2: int
CMSG_HASHED_DATA_PKCS_1_5_VERSION: int
CMSG_HASHED_DATA_CMS_VERSION: int
CMSG_ENVELOPED_DATA_V0: int
CMSG_ENVELOPED_DATA_V2: int
CMSG_ENVELOPED_DATA_PKCS_1_5_VERSION: int
CMSG_ENVELOPED_DATA_CMS_VERSION: int
CMSG_KEY_AGREE_ORIGINATOR_CERT: int
CMSG_KEY_AGREE_ORIGINATOR_PUBLIC_KEY: int
CMSG_ENVELOPED_RECIPIENT_V0: int
CMSG_ENVELOPED_RECIPIENT_V2: int
CMSG_ENVELOPED_RECIPIENT_V3: int
CMSG_ENVELOPED_RECIPIENT_V4: int
CMSG_KEY_TRANS_PKCS_1_5_VERSION: int
CMSG_KEY_TRANS_CMS_VERSION: int
CMSG_KEY_AGREE_VERSION: int
CMSG_CTRL_VERIFY_SIGNATURE: int
CMSG_CTRL_DECRYPT: int
CMSG_CTRL_VERIFY_HASH: int
CMSG_CTRL_ADD_SIGNER: int
CMSG_CTRL_DEL_SIGNER: int
CMSG_CTRL_ADD_SIGNER_UNAUTH_ATTR: int
CMSG_CTRL_DEL_SIGNER_UNAUTH_ATTR: int
CMSG_CTRL_ADD_CERT: int
CMSG_CTRL_DEL_CERT: int
CMSG_CTRL_ADD_CRL: int
CMSG_CTRL_DEL_CRL: int
CMSG_CTRL_ADD_ATTR_CERT: int
CMSG_CTRL_DEL_ATTR_CERT: int
CMSG_CTRL_KEY_TRANS_DECRYPT: int
CMSG_CTRL_KEY_AGREE_DECRYPT: int
CMSG_CTRL_VERIFY_SIGNATURE_EX: int
CMSG_CTRL_ADD_CMS_SIGNER_INFO: int
CMSG_VERIFY_SIGNER_PUBKEY: int
CMSG_VERIFY_SIGNER_CERT: int
CMSG_VERIFY_SIGNER_CHAIN: int
CMSG_VERIFY_SIGNER_NULL: int
CMSG_OID_GEN_ENCRYPT_KEY_FUNC: str
CMSG_OID_EXPORT_ENCRYPT_KEY_FUNC: str
CMSG_OID_IMPORT_ENCRYPT_KEY_FUNC: str
CMSG_CONTENT_ENCRYPT_PAD_ENCODED_LEN_FLAG: int
CMSG_DEFAULT_INSTALLABLE_FUNC_OID: int
CMSG_CONTENT_ENCRYPT_FREE_PARA_FLAG: int
CMSG_CONTENT_ENCRYPT_RELEASE_CONTEXT_FLAG: int
CMSG_OID_GEN_CONTENT_ENCRYPT_KEY_FUNC: str
CMSG_KEY_TRANS_ENCRYPT_FREE_PARA_FLAG: int
CMSG_OID_EXPORT_KEY_TRANS_FUNC: str
CMSG_KEY_AGREE_ENCRYPT_FREE_PARA_FLAG: int
CMSG_KEY_AGREE_ENCRYPT_FREE_MATERIAL_FLAG: int
CMSG_KEY_AGREE_ENCRYPT_FREE_PUBKEY_ALG_FLAG: int
CMSG_KEY_AGREE_ENCRYPT_FREE_PUBKEY_PARA_FLAG: int
CMSG_KEY_AGREE_ENCRYPT_FREE_PUBKEY_BITS_FLAG: int
CMSG_OID_EXPORT_KEY_AGREE_FUNC: str
CMSG_OID_IMPORT_KEY_TRANS_FUNC: str
CMSG_OID_IMPORT_KEY_AGREE_FUNC: str
CERT_KEY_PROV_HANDLE_PROP_ID: int
CERT_KEY_PROV_INFO_PROP_ID: int
CERT_SHA1_HASH_PROP_ID: int
CERT_MD5_HASH_PROP_ID: int
CERT_HASH_PROP_ID: int
CERT_KEY_CONTEXT_PROP_ID: int
CERT_KEY_SPEC_PROP_ID: int
CERT_IE30_RESERVED_PROP_ID: int
CERT_PUBKEY_HASH_RESERVED_PROP_ID: int
CERT_ENHKEY_USAGE_PROP_ID: int
CERT_CTL_USAGE_PROP_ID: int
CERT_NEXT_UPDATE_LOCATION_PROP_ID: int
CERT_FRIENDLY_NAME_PROP_ID: int
CERT_PVK_FILE_PROP_ID: int
CERT_DESCRIPTION_PROP_ID: int
CERT_ACCESS_STATE_PROP_ID: int
CERT_SIGNATURE_HASH_PROP_ID: int
CERT_SMART_CARD_DATA_PROP_ID: int
CERT_EFS_PROP_ID: int
CERT_FORTEZZA_DATA_PROP_ID: int
CERT_ARCHIVED_PROP_ID: int
CERT_KEY_IDENTIFIER_PROP_ID: int
CERT_AUTO_ENROLL_PROP_ID: int
CERT_PUBKEY_ALG_PARA_PROP_ID: int
CERT_CROSS_CERT_DIST_POINTS_PROP_ID: int
CERT_ISSUER_PUBLIC_KEY_MD5_HASH_PROP_ID: int
CERT_SUBJECT_PUBLIC_KEY_MD5_HASH_PROP_ID: int
CERT_ENROLLMENT_PROP_ID: int
CERT_DATE_STAMP_PROP_ID: int
CERT_ISSUER_SERIAL_NUMBER_MD5_HASH_PROP_ID: int
CERT_SUBJECT_NAME_MD5_HASH_PROP_ID: int
CERT_EXTENDED_ERROR_INFO_PROP_ID: int
CERT_RENEWAL_PROP_ID: int
CERT_ARCHIVED_KEY_HASH_PROP_ID: int
CERT_AUTO_ENROLL_RETRY_PROP_ID: int
CERT_AIA_URL_RETRIEVED_PROP_ID: int
CERT_AUTHORITY_INFO_ACCESS_PROP_ID: int
CERT_BACKED_UP_PROP_ID: int
CERT_OCSP_RESPONSE_PROP_ID: int
CERT_REQUEST_ORIGINATOR_PROP_ID: int
CERT_SOURCE_LOCATION_PROP_ID: int
CERT_SOURCE_URL_PROP_ID: int
CERT_NEW_KEY_PROP_ID: int
CERT_OCSP_CACHE_PREFIX_PROP_ID: int
CERT_SMART_CARD_ROOT_INFO_PROP_ID: int
CERT_NO_AUTO_EXPIRE_CHECK_PROP_ID: int
CERT_NCRYPT_KEY_HANDLE_PROP_ID: int
CERT_HCRYPTPROV_OR_NCRYPT_KEY_HANDLE_PROP_ID: int
CERT_SUBJECT_INFO_ACCESS_PROP_ID: int
CERT_CA_OCSP_AUTHORITY_INFO_ACCESS_PROP_ID: int
CERT_CA_DISABLE_CRL_PROP_ID: int
CERT_ROOT_PROGRAM_CERT_POLICIES_PROP_ID: int
CERT_ROOT_PROGRAM_NAME_CONSTRAINTS_PROP_ID: int
CERT_SUBJECT_OCSP_AUTHORITY_INFO_ACCESS_PROP_ID: int
CERT_SUBJECT_DISABLE_CRL_PROP_ID: int
CERT_CEP_PROP_ID: int
CERT_SIGN_HASH_CNG_ALG_PROP_ID: int
CERT_SCARD_PIN_ID_PROP_ID: int
CERT_SCARD_PIN_INFO_PROP_ID: int
CERT_FIRST_RESERVED_PROP_ID: int
CERT_LAST_RESERVED_PROP_ID: int
CERT_FIRST_USER_PROP_ID: int
CERT_LAST_USER_PROP_ID: int
szOID_CERT_PROP_ID_PREFIX: str
szOID_CERT_KEY_IDENTIFIER_PROP_ID: str
szOID_CERT_ISSUER_SERIAL_NUMBER_MD5_HASH_PROP_ID: str
szOID_CERT_SUBJECT_NAME_MD5_HASH_PROP_ID: str
CERT_ACCESS_STATE_WRITE_PERSIST_FLAG: int
CERT_ACCESS_STATE_SYSTEM_STORE_FLAG: int
CERT_ACCESS_STATE_LM_SYSTEM_STORE_FLAG: int
CERT_SET_KEY_PROV_HANDLE_PROP_ID: int
CERT_SET_KEY_CONTEXT_PROP_ID: int
sz_CERT_STORE_PROV_MEMORY: str
sz_CERT_STORE_PROV_FILENAME_W: str
sz_CERT_STORE_PROV_FILENAME: str
sz_CERT_STORE_PROV_SYSTEM_W: str
sz_CERT_STORE_PROV_SYSTEM: str
sz_CERT_STORE_PROV_PKCS7: str
sz_CERT_STORE_PROV_SERIALIZED: str
sz_CERT_STORE_PROV_COLLECTION: str
sz_CERT_STORE_PROV_SYSTEM_REGISTRY_W: str
sz_CERT_STORE_PROV_SYSTEM_REGISTRY: str
sz_CERT_STORE_PROV_PHYSICAL_W: str
sz_CERT_STORE_PROV_PHYSICAL: str
sz_CERT_STORE_PROV_SMART_CARD_W: str
sz_CERT_STORE_PROV_SMART_CARD: str
sz_CERT_STORE_PROV_LDAP_W: str
sz_CERT_STORE_PROV_LDAP: str
CERT_STORE_SIGNATURE_FLAG: int
CERT_STORE_TIME_VALIDITY_FLAG: int
CERT_STORE_REVOCATION_FLAG: int
CERT_STORE_NO_CRL_FLAG: int
CERT_STORE_NO_ISSUER_FLAG: int
CERT_STORE_BASE_CRL_FLAG: int
CERT_STORE_DELTA_CRL_FLAG: int
CERT_STORE_NO_CRYPT_RELEASE_FLAG: int
CERT_STORE_SET_LOCALIZED_NAME_FLAG: int
CERT_STORE_DEFER_CLOSE_UNTIL_LAST_FREE_FLAG: int
CERT_STORE_DELETE_FLAG: int
CERT_STORE_UNSAFE_PHYSICAL_FLAG: int
CERT_STORE_SHARE_STORE_FLAG: int
CERT_STORE_SHARE_CONTEXT_FLAG: int
CERT_STORE_MANIFOLD_FLAG: int
CERT_STORE_ENUM_ARCHIVED_FLAG: int
CERT_STORE_UPDATE_KEYID_FLAG: int
CERT_STORE_BACKUP_RESTORE_FLAG: int
CERT_STORE_READONLY_FLAG: int
CERT_STORE_OPEN_EXISTING_FLAG: int
CERT_STORE_CREATE_NEW_FLAG: int
CERT_STORE_MAXIMUM_ALLOWED_FLAG: int
CERT_SYSTEM_STORE_MASK: int
CERT_SYSTEM_STORE_RELOCATE_FLAG: int
CERT_SYSTEM_STORE_UNPROTECTED_FLAG: int
CERT_SYSTEM_STORE_LOCATION_MASK: int
CERT_SYSTEM_STORE_LOCATION_SHIFT: int
CERT_SYSTEM_STORE_CURRENT_USER_ID: int
CERT_SYSTEM_STORE_LOCAL_MACHINE_ID: int
CERT_SYSTEM_STORE_CURRENT_SERVICE_ID: int
CERT_SYSTEM_STORE_SERVICES_ID: int
CERT_SYSTEM_STORE_USERS_ID: int
CERT_SYSTEM_STORE_CURRENT_USER_GROUP_POLICY_ID: int
CERT_SYSTEM_STORE_LOCAL_MACHINE_GROUP_POLICY_ID: int
CERT_SYSTEM_STORE_LOCAL_MACHINE_ENTERPRISE_ID: int
CERT_SYSTEM_STORE_CURRENT_USER: int
CERT_SYSTEM_STORE_LOCAL_MACHINE: int
CERT_SYSTEM_STORE_CURRENT_SERVICE: int
CERT_SYSTEM_STORE_SERVICES: int
CERT_SYSTEM_STORE_USERS: int
CERT_SYSTEM_STORE_CURRENT_USER_GROUP_POLICY: int
CERT_SYSTEM_STORE_LOCAL_MACHINE_GROUP_POLICY: int
CERT_SYSTEM_STORE_LOCAL_MACHINE_ENTERPRISE: int
CERT_PROT_ROOT_DISABLE_CURRENT_USER_FLAG: int
CERT_PROT_ROOT_INHIBIT_ADD_AT_INIT_FLAG: int
CERT_PROT_ROOT_INHIBIT_PURGE_LM_FLAG: int
CERT_PROT_ROOT_DISABLE_LM_AUTH_FLAG: int
CERT_PROT_ROOT_ONLY_LM_GPT_FLAG: int
CERT_PROT_ROOT_DISABLE_NT_AUTH_REQUIRED_FLAG: int
CERT_PROT_ROOT_DISABLE_NOT_DEFINED_NAME_CONSTRAINT_FLAG: int
CERT_TRUST_PUB_ALLOW_TRUST_MASK: int
CERT_TRUST_PUB_ALLOW_END_USER_TRUST: int
CERT_TRUST_PUB_ALLOW_MACHINE_ADMIN_TRUST: int
CERT_TRUST_PUB_ALLOW_ENTERPRISE_ADMIN_TRUST: int
CERT_TRUST_PUB_CHECK_PUBLISHER_REV_FLAG: int
CERT_TRUST_PUB_CHECK_TIMESTAMP_REV_FLAG: int
CERT_AUTH_ROOT_AUTO_UPDATE_LOCAL_MACHINE_REGPATH: str
CERT_AUTH_ROOT_AUTO_UPDATE_DISABLE_UNTRUSTED_ROOT_LOGGING_FLAG: int
CERT_AUTH_ROOT_AUTO_UPDATE_DISABLE_PARTIAL_CHAIN_LOGGING_FLAG: int
CERT_AUTH_ROOT_AUTO_UPDATE_ROOT_DIR_URL_VALUE_NAME: str
CERT_AUTH_ROOT_AUTO_UPDATE_SYNC_DELTA_TIME_VALUE_NAME: str
CERT_AUTH_ROOT_AUTO_UPDATE_FLAGS_VALUE_NAME: str
CERT_AUTH_ROOT_CTL_FILENAME: str
CERT_AUTH_ROOT_CTL_FILENAME_A: str
CERT_AUTH_ROOT_CAB_FILENAME: str
CERT_AUTH_ROOT_SEQ_FILENAME: str
CERT_AUTH_ROOT_CERT_EXT: str
CERT_GROUP_POLICY_SYSTEM_STORE_REGPATH: str
CERT_EFSBLOB_REGPATH: str
CERT_EFSBLOB_VALUE_NAME: str
CERT_PROT_ROOT_FLAGS_REGPATH: str
CERT_PROT_ROOT_FLAGS_VALUE_NAME: str
CERT_TRUST_PUB_SAFER_GROUP_POLICY_REGPATH: str
CERT_LOCAL_MACHINE_SYSTEM_STORE_REGPATH: str
CERT_TRUST_PUB_SAFER_LOCAL_MACHINE_REGPATH: str
CERT_TRUST_PUB_AUTHENTICODE_FLAGS_VALUE_NAME: str
CERT_OCM_SUBCOMPONENTS_LOCAL_MACHINE_REGPATH: str
CERT_OCM_SUBCOMPONENTS_ROOT_AUTO_UPDATE_VALUE_NAME: str
CERT_DISABLE_ROOT_AUTO_UPDATE_REGPATH: str
CERT_DISABLE_ROOT_AUTO_UPDATE_VALUE_NAME: str
CERT_REGISTRY_STORE_REMOTE_FLAG: int
CERT_REGISTRY_STORE_SERIALIZED_FLAG: int
CERT_REGISTRY_STORE_CLIENT_GPT_FLAG: int
CERT_REGISTRY_STORE_LM_GPT_FLAG: int
CERT_REGISTRY_STORE_ROAMING_FLAG: int
CERT_REGISTRY_STORE_MY_IE_DIRTY_FLAG: int
CERT_IE_DIRTY_FLAGS_REGPATH: str
CERT_FILE_STORE_COMMIT_ENABLE_FLAG: int
CERT_LDAP_STORE_SIGN_FLAG: int
CERT_LDAP_STORE_AREC_EXCLUSIVE_FLAG: int
CERT_LDAP_STORE_OPENED_FLAG: int
CERT_LDAP_STORE_UNBIND_FLAG: int
CRYPT_OID_OPEN_STORE_PROV_FUNC: str
CERT_STORE_PROV_EXTERNAL_FLAG: int
CERT_STORE_PROV_DELETED_FLAG: int
CERT_STORE_PROV_NO_PERSIST_FLAG: int
CERT_STORE_PROV_SYSTEM_STORE_FLAG: int
CERT_STORE_PROV_LM_SYSTEM_STORE_FLAG: int
CERT_STORE_PROV_CLOSE_FUNC: int
CERT_STORE_PROV_READ_CERT_FUNC: int
CERT_STORE_PROV_WRITE_CERT_FUNC: int
CERT_STORE_PROV_DELETE_CERT_FUNC: int
CERT_STORE_PROV_SET_CERT_PROPERTY_FUNC: int
CERT_STORE_PROV_READ_CRL_FUNC: int
CERT_STORE_PROV_WRITE_CRL_FUNC: int
CERT_STORE_PROV_DELETE_CRL_FUNC: int
CERT_STORE_PROV_SET_CRL_PROPERTY_FUNC: int
CERT_STORE_PROV_READ_CTL_FUNC: int
CERT_STORE_PROV_WRITE_CTL_FUNC: int
CERT_STORE_PROV_DELETE_CTL_FUNC: int
CERT_STORE_PROV_SET_CTL_PROPERTY_FUNC: int
CERT_STORE_PROV_CONTROL_FUNC: int
CERT_STORE_PROV_FIND_CERT_FUNC: int
CERT_STORE_PROV_FREE_FIND_CERT_FUNC: int
CERT_STORE_PROV_GET_CERT_PROPERTY_FUNC: int
CERT_STORE_PROV_FIND_CRL_FUNC: int
CERT_STORE_PROV_FREE_FIND_CRL_FUNC: int
CERT_STORE_PROV_GET_CRL_PROPERTY_FUNC: int
CERT_STORE_PROV_FIND_CTL_FUNC: int
CERT_STORE_PROV_FREE_FIND_CTL_FUNC: int
CERT_STORE_PROV_GET_CTL_PROPERTY_FUNC: int
CERT_STORE_PROV_WRITE_ADD_FLAG: int
CERT_STORE_SAVE_AS_STORE: int
CERT_STORE_SAVE_AS_PKCS7: int
CERT_STORE_SAVE_TO_FILE: int
CERT_STORE_SAVE_TO_MEMORY: int
CERT_STORE_SAVE_TO_FILENAME_A: int
CERT_STORE_SAVE_TO_FILENAME_W: int
CERT_STORE_SAVE_TO_FILENAME: int
CERT_CLOSE_STORE_FORCE_FLAG: int
CERT_CLOSE_STORE_CHECK_FLAG: int
CERT_COMPARE_MASK: int
CERT_COMPARE_SHIFT: int
CERT_COMPARE_ANY: int
CERT_COMPARE_SHA1_HASH: int
CERT_COMPARE_NAME: int
CERT_COMPARE_ATTR: int
CERT_COMPARE_MD5_HASH: int
CERT_COMPARE_PROPERTY: int
CERT_COMPARE_PUBLIC_KEY: int
CERT_COMPARE_HASH: int
CERT_COMPARE_NAME_STR_A: int
CERT_COMPARE_NAME_STR_W: int
CERT_COMPARE_KEY_SPEC: int
CERT_COMPARE_ENHKEY_USAGE: int
CERT_COMPARE_CTL_USAGE: int
CERT_COMPARE_SUBJECT_CERT: int
CERT_COMPARE_ISSUER_OF: int
CERT_COMPARE_EXISTING: int
CERT_COMPARE_SIGNATURE_HASH: int
CERT_COMPARE_KEY_IDENTIFIER: int
CERT_COMPARE_CERT_ID: int
CERT_COMPARE_CROSS_CERT_DIST_POINTS: int
CERT_COMPARE_PUBKEY_MD5_HASH: int
CERT_FIND_ANY: int
CERT_FIND_SHA1_HASH: int
CERT_FIND_MD5_HASH: int
CERT_FIND_SIGNATURE_HASH: int
CERT_FIND_KEY_IDENTIFIER: int
CERT_FIND_HASH: int
CERT_FIND_PROPERTY: int
CERT_FIND_PUBLIC_KEY: int
CERT_FIND_SUBJECT_NAME: int
CERT_FIND_SUBJECT_ATTR: int
CERT_FIND_ISSUER_NAME: int
CERT_FIND_ISSUER_ATTR: int
CERT_FIND_SUBJECT_STR_A: int
CERT_FIND_SUBJECT_STR_W: int
CERT_FIND_SUBJECT_STR: int
CERT_FIND_ISSUER_STR_A: int
CERT_FIND_ISSUER_STR_W: int
CERT_FIND_ISSUER_STR: int
CERT_FIND_KEY_SPEC: int
CERT_FIND_ENHKEY_USAGE: int
CERT_FIND_CTL_USAGE: int
CERT_FIND_SUBJECT_CERT: int
CERT_FIND_ISSUER_OF: int
CERT_FIND_EXISTING: int
CERT_FIND_CERT_ID: int
CERT_FIND_CROSS_CERT_DIST_POINTS: int
CERT_FIND_PUBKEY_MD5_HASH: int
CERT_FIND_OPTIONAL_ENHKEY_USAGE_FLAG: int
CERT_FIND_EXT_ONLY_ENHKEY_USAGE_FLAG: int
CERT_FIND_PROP_ONLY_ENHKEY_USAGE_FLAG: int
CERT_FIND_NO_ENHKEY_USAGE_FLAG: int
CERT_FIND_OR_ENHKEY_USAGE_FLAG: int
CERT_FIND_VALID_ENHKEY_USAGE_FLAG: int
CERT_FIND_OPTIONAL_CTL_USAGE_FLAG: int
CERT_FIND_EXT_ONLY_CTL_USAGE_FLAG: int
CERT_FIND_PROP_ONLY_CTL_USAGE_FLAG: int
CERT_FIND_NO_CTL_USAGE_FLAG: int
CERT_FIND_OR_CTL_USAGE_FLAG: int
CERT_FIND_VALID_CTL_USAGE_FLAG: int
CERT_SET_PROPERTY_IGNORE_PERSIST_ERROR_FLAG: int
CERT_SET_PROPERTY_INHIBIT_PERSIST_FLAG: int
CTL_ENTRY_FROM_PROP_CHAIN_FLAG: int
CRL_FIND_ANY: int
CRL_FIND_ISSUED_BY: int
CRL_FIND_EXISTING: int
CRL_FIND_ISSUED_FOR: int
CRL_FIND_ISSUED_BY_AKI_FLAG: int
CRL_FIND_ISSUED_BY_SIGNATURE_FLAG: int
CRL_FIND_ISSUED_BY_DELTA_FLAG: int
CRL_FIND_ISSUED_BY_BASE_FLAG: int
CERT_STORE_ADD_NEW: int
CERT_STORE_ADD_USE_EXISTING: int
CERT_STORE_ADD_REPLACE_EXISTING: int
CERT_STORE_ADD_ALWAYS: int
CERT_STORE_ADD_REPLACE_EXISTING_INHERIT_PROPERTIES: int
CERT_STORE_ADD_NEWER: int
CERT_STORE_ADD_NEWER_INHERIT_PROPERTIES: int
CERT_STORE_CERTIFICATE_CONTEXT: int
CERT_STORE_CRL_CONTEXT: int
CERT_STORE_CTL_CONTEXT: int
CERT_STORE_ALL_CONTEXT_FLAG: int
CERT_STORE_CERTIFICATE_CONTEXT_FLAG: int
CERT_STORE_CRL_CONTEXT_FLAG: int
CERT_STORE_CTL_CONTEXT_FLAG: int
CTL_ANY_SUBJECT_TYPE: int
CTL_CERT_SUBJECT_TYPE: int
CTL_FIND_ANY: int
CTL_FIND_SHA1_HASH: int
CTL_FIND_MD5_HASH: int
CTL_FIND_USAGE: int
CTL_FIND_SUBJECT: int
CTL_FIND_EXISTING: int
CTL_FIND_SAME_USAGE_FLAG: int
CERT_STORE_CTRL_RESYNC: int
CERT_STORE_CTRL_NOTIFY_CHANGE: int
CERT_STORE_CTRL_COMMIT: int
CERT_STORE_CTRL_AUTO_RESYNC: int
CERT_STORE_CTRL_CANCEL_NOTIFY: int
CERT_STORE_CTRL_INHIBIT_DUPLICATE_HANDLE_FLAG: int
CERT_STORE_CTRL_COMMIT_FORCE_FLAG: int
CERT_STORE_CTRL_COMMIT_CLEAR_FLAG: int
CERT_STORE_LOCALIZED_NAME_PROP_ID: int
CERT_CREATE_CONTEXT_NOCOPY_FLAG: int
CERT_CREATE_CONTEXT_SORTED_FLAG: int
CERT_CREATE_CONTEXT_NO_HCRYPTMSG_FLAG: int
CERT_CREATE_CONTEXT_NO_ENTRY_FLAG: int
CERT_PHYSICAL_STORE_ADD_ENABLE_FLAG: int
CERT_PHYSICAL_STORE_OPEN_DISABLE_FLAG: int
CERT_PHYSICAL_STORE_REMOTE_OPEN_DISABLE_FLAG: int
CERT_PHYSICAL_STORE_INSERT_COMPUTER_NAME_ENABLE_FLAG: int
CERT_PHYSICAL_STORE_PREDEFINED_ENUM_FLAG: int
CERT_PHYSICAL_STORE_DEFAULT_NAME: str
CERT_PHYSICAL_STORE_GROUP_POLICY_NAME: str
CERT_PHYSICAL_STORE_LOCAL_MACHINE_NAME: str
CERT_PHYSICAL_STORE_DS_USER_CERTIFICATE_NAME: str
CERT_PHYSICAL_STORE_LOCAL_MACHINE_GROUP_POLICY_NAME: str
CERT_PHYSICAL_STORE_ENTERPRISE_NAME: str
CERT_PHYSICAL_STORE_AUTH_ROOT_NAME: str
CERT_PHYSICAL_STORE_SMART_CARD_NAME: str
CRYPT_OID_OPEN_SYSTEM_STORE_PROV_FUNC: str
CRYPT_OID_REGISTER_SYSTEM_STORE_FUNC: str
CRYPT_OID_UNREGISTER_SYSTEM_STORE_FUNC: str
CRYPT_OID_ENUM_SYSTEM_STORE_FUNC: str
CRYPT_OID_REGISTER_PHYSICAL_STORE_FUNC: str
CRYPT_OID_UNREGISTER_PHYSICAL_STORE_FUNC: str
CRYPT_OID_ENUM_PHYSICAL_STORE_FUNC: str
CRYPT_OID_SYSTEM_STORE_LOCATION_VALUE_NAME: str
CMSG_TRUSTED_SIGNER_FLAG: int
CMSG_SIGNER_ONLY_FLAG: int
CMSG_USE_SIGNER_INDEX_FLAG: int
CMSG_CMS_ENCAPSULATED_CTL_FLAG: int
CMSG_ENCODE_SORTED_CTL_FLAG: int
CMSG_ENCODE_HASHED_SUBJECT_IDENTIFIER_FLAG: int
CERT_VERIFY_INHIBIT_CTL_UPDATE_FLAG: int
CERT_VERIFY_TRUSTED_SIGNERS_FLAG: int
CERT_VERIFY_NO_TIME_CHECK_FLAG: int
CERT_VERIFY_ALLOW_MORE_USAGE_FLAG: int
CERT_VERIFY_UPDATED_CTL_FLAG: int
CERT_CONTEXT_REVOCATION_TYPE: int
CERT_VERIFY_REV_CHAIN_FLAG: int
CERT_VERIFY_CACHE_ONLY_BASED_REVOCATION: int
CERT_VERIFY_REV_ACCUMULATIVE_TIMEOUT_FLAG: int
CERT_UNICODE_IS_RDN_ATTRS_FLAG: int
CERT_CASE_INSENSITIVE_IS_RDN_ATTRS_FLAG: int
CRYPT_VERIFY_CERT_SIGN_SUBJECT_BLOB: int
CRYPT_VERIFY_CERT_SIGN_SUBJECT_CERT: int
CRYPT_VERIFY_CERT_SIGN_SUBJECT_CRL: int
CRYPT_VERIFY_CERT_SIGN_ISSUER_PUBKEY: int
CRYPT_VERIFY_CERT_SIGN_ISSUER_CERT: int
CRYPT_VERIFY_CERT_SIGN_ISSUER_CHAIN: int
CRYPT_VERIFY_CERT_SIGN_ISSUER_NULL: int
CRYPT_DEFAULT_CONTEXT_AUTO_RELEASE_FLAG: int
CRYPT_DEFAULT_CONTEXT_PROCESS_FLAG: int
CRYPT_DEFAULT_CONTEXT_CERT_SIGN_OID: int
CRYPT_DEFAULT_CONTEXT_MULTI_CERT_SIGN_OID: int
CRYPT_OID_EXPORT_PUBLIC_KEY_INFO_FUNC: str
CRYPT_OID_IMPORT_PUBLIC_KEY_INFO_FUNC: str
CRYPT_ACQUIRE_CACHE_FLAG: int
CRYPT_ACQUIRE_USE_PROV_INFO_FLAG: int
CRYPT_ACQUIRE_COMPARE_KEY_FLAG: int
CRYPT_ACQUIRE_SILENT_FLAG: int
CRYPT_FIND_USER_KEYSET_FLAG: int
CRYPT_FIND_MACHINE_KEYSET_FLAG: int
CRYPT_FIND_SILENT_KEYSET_FLAG: int
CRYPT_OID_IMPORT_PRIVATE_KEY_INFO_FUNC: str
CRYPT_OID_EXPORT_PRIVATE_KEY_INFO_FUNC: str
CRYPT_DELETE_KEYSET: int
CERT_SIMPLE_NAME_STR: int
CERT_OID_NAME_STR: int
CERT_X500_NAME_STR: int
CERT_NAME_STR_SEMICOLON_FLAG: int
CERT_NAME_STR_NO_PLUS_FLAG: int
CERT_NAME_STR_NO_QUOTING_FLAG: int
CERT_NAME_STR_CRLF_FLAG: int
CERT_NAME_STR_COMMA_FLAG: int
CERT_NAME_STR_REVERSE_FLAG: int
CERT_NAME_STR_DISABLE_IE4_UTF8_FLAG: int
CERT_NAME_STR_ENABLE_T61_UNICODE_FLAG: int
CERT_NAME_STR_ENABLE_UTF8_UNICODE_FLAG: int
CERT_NAME_EMAIL_TYPE: int
CERT_NAME_RDN_TYPE: int
CERT_NAME_ATTR_TYPE: int
CERT_NAME_SIMPLE_DISPLAY_TYPE: int
CERT_NAME_FRIENDLY_DISPLAY_TYPE: int
CERT_NAME_DNS_TYPE: int
CERT_NAME_URL_TYPE: int
CERT_NAME_UPN_TYPE: int
CERT_NAME_ISSUER_FLAG: int
CERT_NAME_DISABLE_IE4_UTF8_FLAG: int
CRYPT_MESSAGE_BARE_CONTENT_OUT_FLAG: int
CRYPT_MESSAGE_ENCAPSULATED_CONTENT_OUT_FLAG: int
CRYPT_MESSAGE_KEYID_SIGNER_FLAG: int
CRYPT_MESSAGE_SILENT_KEYSET_FLAG: int
CRYPT_MESSAGE_KEYID_RECIPIENT_FLAG: int
CERT_QUERY_OBJECT_FILE: int
CERT_QUERY_OBJECT_BLOB: int
CERT_QUERY_CONTENT_CERT: int
CERT_QUERY_CONTENT_CTL: int
CERT_QUERY_CONTENT_CRL: int
CERT_QUERY_CONTENT_SERIALIZED_STORE: int
CERT_QUERY_CONTENT_SERIALIZED_CERT: int
CERT_QUERY_CONTENT_SERIALIZED_CTL: int
CERT_QUERY_CONTENT_SERIALIZED_CRL: int
CERT_QUERY_CONTENT_PKCS7_SIGNED: int
CERT_QUERY_CONTENT_PKCS7_UNSIGNED: int
CERT_QUERY_CONTENT_PKCS7_SIGNED_EMBED: int
CERT_QUERY_CONTENT_PKCS10: int
CERT_QUERY_CONTENT_PFX: int
CERT_QUERY_CONTENT_CERT_PAIR: int
CERT_QUERY_CONTENT_FLAG_CERT: int
CERT_QUERY_CONTENT_FLAG_CTL: int
CERT_QUERY_CONTENT_FLAG_CRL: int
CERT_QUERY_CONTENT_FLAG_SERIALIZED_STORE: int
CERT_QUERY_CONTENT_FLAG_SERIALIZED_CERT: int
CERT_QUERY_CONTENT_FLAG_SERIALIZED_CTL: int
CERT_QUERY_CONTENT_FLAG_SERIALIZED_CRL: int
CERT_QUERY_CONTENT_FLAG_PKCS7_SIGNED: int
CERT_QUERY_CONTENT_FLAG_PKCS7_UNSIGNED: int
CERT_QUERY_CONTENT_FLAG_PKCS7_SIGNED_EMBED: int
CERT_QUERY_CONTENT_FLAG_PKCS10: int
CERT_QUERY_CONTENT_FLAG_PFX: int
CERT_QUERY_CONTENT_FLAG_CERT_PAIR: int
CERT_QUERY_CONTENT_FLAG_ALL: int
CERT_QUERY_FORMAT_BINARY: int
CERT_QUERY_FORMAT_BASE64_ENCODED: int
CERT_QUERY_FORMAT_ASN_ASCII_HEX_ENCODED: int
CERT_QUERY_FORMAT_FLAG_BINARY: int
CERT_QUERY_FORMAT_FLAG_BASE64_ENCODED: int
CERT_QUERY_FORMAT_FLAG_ASN_ASCII_HEX_ENCODED: int
CERT_QUERY_FORMAT_FLAG_ALL: int
CREDENTIAL_OID_PASSWORD_CREDENTIALS_A: int
CREDENTIAL_OID_PASSWORD_CREDENTIALS_W: int
CREDENTIAL_OID_PASSWORD_CREDENTIALS: int
SCHEME_OID_RETRIEVE_ENCODED_OBJECT_FUNC: str
SCHEME_OID_RETRIEVE_ENCODED_OBJECTW_FUNC: str
CONTEXT_OID_CREATE_OBJECT_CONTEXT_FUNC: str
CONTEXT_OID_CERTIFICATE: int
CONTEXT_OID_CRL: int
CONTEXT_OID_CTL: int
CONTEXT_OID_PKCS7: int
CONTEXT_OID_CAPI2_ANY: int
CONTEXT_OID_OCSP_RESP: int
CRYPT_RETRIEVE_MULTIPLE_OBJECTS: int
CRYPT_CACHE_ONLY_RETRIEVAL: int
CRYPT_WIRE_ONLY_RETRIEVAL: int
CRYPT_DONT_CACHE_RESULT: int
CRYPT_ASYNC_RETRIEVAL: int
CRYPT_STICKY_CACHE_RETRIEVAL: int
CRYPT_LDAP_SCOPE_BASE_ONLY_RETRIEVAL: int
CRYPT_OFFLINE_CHECK_RETRIEVAL: int
CRYPT_LDAP_INSERT_ENTRY_ATTRIBUTE: int
CRYPT_LDAP_SIGN_RETRIEVAL: int
CRYPT_NO_AUTH_RETRIEVAL: int
CRYPT_LDAP_AREC_EXCLUSIVE_RETRIEVAL: int
CRYPT_AIA_RETRIEVAL: int
CRYPT_VERIFY_CONTEXT_SIGNATURE: int
CRYPT_VERIFY_DATA_HASH: int
CRYPT_KEEP_TIME_VALID: int
CRYPT_DONT_VERIFY_SIGNATURE: int
CRYPT_DONT_CHECK_TIME_VALIDITY: int
CRYPT_CHECK_FRESHNESS_TIME_VALIDITY: int
CRYPT_ACCUMULATIVE_TIMEOUT: int
CRYPT_PARAM_ASYNC_RETRIEVAL_COMPLETION: int
CRYPT_PARAM_CANCEL_ASYNC_RETRIEVAL: int
CRYPT_GET_URL_FROM_PROPERTY: int
CRYPT_GET_URL_FROM_EXTENSION: int
CRYPT_GET_URL_FROM_UNAUTH_ATTRIBUTE: int
CRYPT_GET_URL_FROM_AUTH_ATTRIBUTE: int
URL_OID_GET_OBJECT_URL_FUNC: str
TIME_VALID_OID_GET_OBJECT_FUNC: str
TIME_VALID_OID_FLUSH_OBJECT_FUNC: str
TIME_VALID_OID_GET_CTL: int
TIME_VALID_OID_GET_CRL: int
TIME_VALID_OID_GET_CRL_FROM_CERT: int
TIME_VALID_OID_GET_FRESHEST_CRL_FROM_CERT: int
TIME_VALID_OID_GET_FRESHEST_CRL_FROM_CRL: int
TIME_VALID_OID_FLUSH_CTL: int
TIME_VALID_OID_FLUSH_CRL: int
TIME_VALID_OID_FLUSH_CRL_FROM_CERT: int
TIME_VALID_OID_FLUSH_FRESHEST_CRL_FROM_CERT: int
TIME_VALID_OID_FLUSH_FRESHEST_CRL_FROM_CRL: int
CRYPTPROTECT_PROMPT_ON_UNPROTECT: int
CRYPTPROTECT_PROMPT_ON_PROTECT: int
CRYPTPROTECT_PROMPT_RESERVED: int
CRYPTPROTECT_PROMPT_STRONG: int
CRYPTPROTECT_PROMPT_REQUIRE_STRONG: int
CRYPTPROTECT_UI_FORBIDDEN: int
CRYPTPROTECT_LOCAL_MACHINE: int
CRYPTPROTECT_CRED_SYNC: int
CRYPTPROTECT_AUDIT: int
CRYPTPROTECT_NO_RECOVERY: int
CRYPTPROTECT_VERIFY_PROTECTION: int
CRYPTPROTECT_CRED_REGENERATE: int
CRYPTPROTECT_FIRST_RESERVED_FLAGVAL: int
CRYPTPROTECT_LAST_RESERVED_FLAGVAL: int
CRYPTPROTECTMEMORY_BLOCK_SIZE: int
CRYPTPROTECTMEMORY_SAME_PROCESS: int
CRYPTPROTECTMEMORY_CROSS_PROCESS: int
CRYPTPROTECTMEMORY_SAME_LOGON: int
CERT_CREATE_SELFSIGN_NO_SIGN: int
CERT_CREATE_SELFSIGN_NO_KEY_INFO: int
CRYPT_KEYID_MACHINE_FLAG: int
CRYPT_KEYID_ALLOC_FLAG: int
CRYPT_KEYID_DELETE_FLAG: int
CRYPT_KEYID_SET_NEW_FLAG: int
CERT_CHAIN_MAX_AIA_URL_COUNT_IN_CERT_DEFAULT: int
CERT_CHAIN_MAX_AIA_URL_RETRIEVAL_COUNT_PER_CHAIN_DEFAULT: int
CERT_CHAIN_MAX_AIA_URL_RETRIEVAL_BYTE_COUNT_DEFAULT: int
CERT_CHAIN_MAX_AIA_URL_RETRIEVAL_CERT_COUNT_DEFAULT: int
CERT_CHAIN_CACHE_END_CERT: int
CERT_CHAIN_THREAD_STORE_SYNC: int
CERT_CHAIN_CACHE_ONLY_URL_RETRIEVAL: int
CERT_CHAIN_USE_LOCAL_MACHINE_STORE: int
CERT_CHAIN_ENABLE_CACHE_AUTO_UPDATE: int
CERT_CHAIN_ENABLE_SHARE_STORE: int
CERT_TRUST_NO_ERROR: int
CERT_TRUST_IS_NOT_TIME_VALID: int
CERT_TRUST_IS_NOT_TIME_NESTED: int
CERT_TRUST_IS_REVOKED: int
CERT_TRUST_IS_NOT_SIGNATURE_VALID: int
CERT_TRUST_IS_NOT_VALID_FOR_USAGE: int
CERT_TRUST_IS_UNTRUSTED_ROOT: int
CERT_TRUST_REVOCATION_STATUS_UNKNOWN: int
CERT_TRUST_IS_CYCLIC: int
CERT_TRUST_INVALID_EXTENSION: int
CERT_TRUST_INVALID_POLICY_CONSTRAINTS: int
CERT_TRUST_INVALID_BASIC_CONSTRAINTS: int
CERT_TRUST_INVALID_NAME_CONSTRAINTS: int
CERT_TRUST_HAS_NOT_SUPPORTED_NAME_CONSTRAINT: int
CERT_TRUST_HAS_NOT_DEFINED_NAME_CONSTRAINT: int
CERT_TRUST_HAS_NOT_PERMITTED_NAME_CONSTRAINT: int
CERT_TRUST_HAS_EXCLUDED_NAME_CONSTRAINT: int
CERT_TRUST_IS_OFFLINE_REVOCATION: int
CERT_TRUST_NO_ISSUANCE_CHAIN_POLICY: int
CERT_TRUST_IS_PARTIAL_CHAIN: int
CERT_TRUST_CTL_IS_NOT_TIME_VALID: int
CERT_TRUST_CTL_IS_NOT_SIGNATURE_VALID: int
CERT_TRUST_CTL_IS_NOT_VALID_FOR_USAGE: int
CERT_TRUST_HAS_EXACT_MATCH_ISSUER: int
CERT_TRUST_HAS_KEY_MATCH_ISSUER: int
CERT_TRUST_HAS_NAME_MATCH_ISSUER: int
CERT_TRUST_IS_SELF_SIGNED: int
CERT_TRUST_HAS_PREFERRED_ISSUER: int
CERT_TRUST_HAS_ISSUANCE_CHAIN_POLICY: int
CERT_TRUST_HAS_VALID_NAME_CONSTRAINTS: int
CERT_TRUST_IS_COMPLEX_CHAIN: int
USAGE_MATCH_TYPE_AND: int
USAGE_MATCH_TYPE_OR: int
CERT_CHAIN_REVOCATION_CHECK_END_CERT: int
CERT_CHAIN_REVOCATION_CHECK_CHAIN: int
CERT_CHAIN_REVOCATION_CHECK_CHAIN_EXCLUDE_ROOT: int
CERT_CHAIN_REVOCATION_CHECK_CACHE_ONLY: int
CERT_CHAIN_REVOCATION_ACCUMULATIVE_TIMEOUT: int
CERT_CHAIN_DISABLE_PASS1_QUALITY_FILTERING: int
CERT_CHAIN_RETURN_LOWER_QUALITY_CONTEXTS: int
CERT_CHAIN_DISABLE_AUTH_ROOT_AUTO_UPDATE: int
CERT_CHAIN_TIMESTAMP_TIME: int
REVOCATION_OID_CRL_REVOCATION: int
CERT_CHAIN_FIND_BY_ISSUER: int
CERT_CHAIN_FIND_BY_ISSUER_COMPARE_KEY_FLAG: int
CERT_CHAIN_FIND_BY_ISSUER_COMPLEX_CHAIN_FLAG: int
CERT_CHAIN_FIND_BY_ISSUER_CACHE_ONLY_URL_FLAG: int
CERT_CHAIN_FIND_BY_ISSUER_LOCAL_MACHINE_FLAG: int
CERT_CHAIN_FIND_BY_ISSUER_NO_KEY_FLAG: int
CERT_CHAIN_FIND_BY_ISSUER_CACHE_ONLY_FLAG: int
CERT_CHAIN_POLICY_IGNORE_NOT_TIME_VALID_FLAG: int
CERT_CHAIN_POLICY_IGNORE_CTL_NOT_TIME_VALID_FLAG: int
CERT_CHAIN_POLICY_IGNORE_NOT_TIME_NESTED_FLAG: int
CERT_CHAIN_POLICY_IGNORE_INVALID_BASIC_CONSTRAINTS_FLAG: int
CERT_CHAIN_POLICY_IGNORE_ALL_NOT_TIME_VALID_FLAGS: int
CERT_CHAIN_POLICY_ALLOW_UNKNOWN_CA_FLAG: int
CERT_CHAIN_POLICY_IGNORE_WRONG_USAGE_FLAG: int
CERT_CHAIN_POLICY_IGNORE_INVALID_NAME_FLAG: int
CERT_CHAIN_POLICY_IGNORE_INVALID_POLICY_FLAG: int
CERT_CHAIN_POLICY_IGNORE_END_REV_UNKNOWN_FLAG: int
CERT_CHAIN_POLICY_IGNORE_CTL_SIGNER_REV_UNKNOWN_FLAG: int
CERT_CHAIN_POLICY_IGNORE_CA_REV_UNKNOWN_FLAG: int
CERT_CHAIN_POLICY_IGNORE_ROOT_REV_UNKNOWN_FLAG: int
CERT_CHAIN_POLICY_IGNORE_ALL_REV_UNKNOWN_FLAGS: int
CERT_CHAIN_POLICY_ALLOW_TESTROOT_FLAG: int
CERT_CHAIN_POLICY_TRUST_TESTROOT_FLAG: int
CRYPT_OID_VERIFY_CERTIFICATE_CHAIN_POLICY_FUNC: str
AUTHTYPE_CLIENT: int
AUTHTYPE_SERVER: int
BASIC_CONSTRAINTS_CERT_CHAIN_POLICY_CA_FLAG: int
BASIC_CONSTRAINTS_CERT_CHAIN_POLICY_END_ENTITY_FLAG: int
MICROSOFT_ROOT_CERT_CHAIN_POLICY_ENABLE_TEST_ROOT_FLAG: int
CRYPT_STRING_BASE64HEADER: int
CRYPT_STRING_BASE64: int
CRYPT_STRING_BINARY: int
CRYPT_STRING_BASE64REQUESTHEADER: int
CRYPT_STRING_HEX: int
CRYPT_STRING_HEXASCII: int
CRYPT_STRING_BASE64_ANY: int
CRYPT_STRING_ANY: int
CRYPT_STRING_HEX_ANY: int
CRYPT_STRING_BASE64X509CRLHEADER: int
CRYPT_STRING_HEXADDR: int
CRYPT_STRING_HEXASCIIADDR: int
CRYPT_STRING_NOCR: int
CRYPT_USER_KEYSET: int
PKCS12_IMPORT_RESERVED_MASK: int
REPORT_NO_PRIVATE_KEY: int
REPORT_NOT_ABLE_TO_EXPORT_PRIVATE_KEY: int
EXPORT_PRIVATE_KEYS: int
PKCS12_EXPORT_RESERVED_MASK: int
CERT_STORE_PROV_MSG: int
CERT_STORE_PROV_MEMORY: int
CERT_STORE_PROV_FILE: int
CERT_STORE_PROV_REG: int
CERT_STORE_PROV_PKCS7: int
CERT_STORE_PROV_SERIALIZED: int
CERT_STORE_PROV_FILENAME: int
CERT_STORE_PROV_SYSTEM: int
CERT_STORE_PROV_COLLECTION: int
CERT_STORE_PROV_SYSTEM_REGISTRY: int
CERT_STORE_PROV_PHYSICAL: int
CERT_STORE_PROV_SMART_CARD: int
CERT_STORE_PROV_LDAP: int
URL_OID_CERTIFICATE_ISSUER: int
URL_OID_CERTIFICATE_CRL_DIST_POINT: int
URL_OID_CTL_ISSUER: int
URL_OID_CTL_NEXT_UPDATE: int
URL_OID_CRL_ISSUER: int
URL_OID_CERTIFICATE_FRESHEST_CRL: int
URL_OID_CRL_FRESHEST_CRL: int
URL_OID_CROSS_CERT_DIST_POINT: int
URL_OID_CERTIFICATE_OCSP: int
URL_OID_CERTIFICATE_OCSP_AND_CRL_DIST_POINT: int
URL_OID_CERTIFICATE_CRL_DIST_POINT_AND_OCSP: int
URL_OID_CROSS_CERT_SUBJECT_INFO_ACCESS: int
URL_OID_CERTIFICATE_ONLY_OCSP: int
CMSG_CTRL_MAIL_LIST_DECRYPT: int
CMSG_MAIL_LIST_ENCRYPT_FREE_PARA_FLAG: int
CMSG_MAIL_LIST_HANDLE_KEY_CHOICE: int
CMSG_MAIL_LIST_RECIPIENT: int
CMSG_MAIL_LIST_VERSION: int
CMSG_OID_EXPORT_MAIL_LIST_FUNC: str
CMSG_OID_IMPORT_MAIL_LIST_FUNC: str
CTL_FIND_NO_LIST_ID_CBDATA: int
szOID_AUTHORITY_REVOCATION_LIST: str
szOID_CERTIFICATE_REVOCATION_LIST: str
szOID_ROOT_LIST_SIGNER: str
