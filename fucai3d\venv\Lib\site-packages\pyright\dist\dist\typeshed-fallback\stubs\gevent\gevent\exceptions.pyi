from gevent.hub import Hub
from greenlet import GreenletExit

class LoopExit(Exception):
    @property
    def hub(self) -> Hub | None: ...

class BlockingSwitchOutError(AssertionError): ...
class InvalidSwitchError(AssertionError): ...
class ConcurrentObjectUseError(AssertionError): ...
class InvalidThreadUseError(RuntimeError): ...

class HubDestroyed(GreenletExit):
    destroy_loop: bool
    def __init__(self, destroy_loop: bool) -> None: ...

__all__ = ["LoopExit"]
