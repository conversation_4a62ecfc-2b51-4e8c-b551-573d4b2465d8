from Xlib._typing import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from Xlib.protocol.structs import _RGB3IntIterable
from Xlib.xobject import resource

class Cursor(resource.Resource):
    __cursor__ = resource.Resource.__resource__
    def free(self, onerror: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>[object] | None = None) -> None: ...
    def recolor(
        self, foreground: _RGB3IntIterable, background: _RGB3IntIterable, onerror: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>[object] | None = None
    ) -> None: ...
