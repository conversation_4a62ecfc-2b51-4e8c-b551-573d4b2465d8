from types import TracebackType
from typing import Any
from typing_extensions import Self

from .tracer import Tracer

class SpanContext:
    EMPTY_BAGGAGE: dict[str, str]
    @property
    def baggage(self) -> dict[str, str]: ...

class Span:
    def __init__(self, tracer: Tracer, context: SpanContext) -> None: ...
    @property
    def context(self) -> SpanContext: ...
    @property
    def tracer(self) -> Tracer: ...
    def set_operation_name(self, operation_name: str) -> Self: ...
    def finish(self, finish_time: float | None = None) -> None: ...
    def set_tag(self, key: str, value: str | bool | float) -> Self: ...
    def log_kv(self, key_values: dict[str, Any], timestamp: float | None = None) -> Self: ...
    def set_baggage_item(self, key: str, value: str) -> Self: ...
    def get_baggage_item(self, key: str) -> str | None: ...
    def __enter__(self) -> Self: ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...
    def log_event(self, event: Any, payload=None) -> Self: ...
    def log(self, **kwargs: Any) -> Self: ...
