import _win32typing

CLSID_CTask: _win32typing.PyIID
CLSID_CTaskScheduler: _win32typing.PyIID
HIGH_PRIORITY_CLASS: int
IDLE_PRIORITY_CLASS: int
IID_IProvideTaskPage: _win32typing.PyIID
IID_IScheduledWorkItem: _win32typing.PyIID
IID_ITask: _win32typing.PyIID
IID_ITaskScheduler: _win32typing.PyIID
IID_ITaskTrigger: _win32typing.PyIID
NORMAL_PRIORITY_CLASS: int
REALTIME_PRIORITY_CLASS: int
SCHED_E_ACCOUNT_DBASE_CORRUPT: int
SCHED_E_ACCOUNT_INFORMATION_NOT_SET: int
SCHED_E_ACCOUNT_NAME_NOT_FOUND: int
SCHED_E_CANNOT_OPEN_TASK: int
SCHED_E_INVALID_TASK: int
SCHED_E_SERVICE_NOT_INSTALLED: int
SCHED_E_TASK_NOT_READY: int
SCHED_E_TASK_NOT_RUNNING: int
SCHED_E_TRIGGER_NOT_FOUND: int
SCHED_E_UNKNOWN_OBJECT_VERSION: int
SCHED_S_EVENT_TRIGGER: int
SCHED_S_TASK_DISABLED: int
SCHED_S_TASK_HAS_NOT_RUN: int
SCHED_S_TASK_NOT_SCHEDULED: int
SCHED_S_TASK_NO_MORE_RUNS: int
SCHED_S_TASK_NO_VALID_TRIGGERS: int
SCHED_S_TASK_READY: int
SCHED_S_TASK_RUNNING: int
SCHED_S_TASK_TERMINATED: int
TASKPAGE_SCHEDULE: int
TASKPAGE_SETTINGS: int
TASKPAGE_TASK: int
TASK_APRIL: int
TASK_AUGUST: int
TASK_DECEMBER: int
TASK_EVENT_TRIGGER_AT_LOGON: int
TASK_EVENT_TRIGGER_AT_SYSTEMSTART: int
TASK_EVENT_TRIGGER_ON_IDLE: int
TASK_FEBRUARY: int
TASK_FIRST_WEEK: int
TASK_FLAG_DELETE_WHEN_DONE: int
TASK_FLAG_DISABLED: int
TASK_FLAG_DONT_START_IF_ON_BATTERIES: int
TASK_FLAG_HIDDEN: int
TASK_FLAG_INTERACTIVE: int
TASK_FLAG_KILL_IF_GOING_ON_BATTERIES: int
TASK_FLAG_KILL_ON_IDLE_END: int
TASK_FLAG_RESTART_ON_IDLE_RESUME: int
TASK_FLAG_RUN_IF_CONNECTED_TO_INTERNET: int
TASK_FLAG_RUN_ONLY_IF_DOCKED: int
TASK_FLAG_RUN_ONLY_IF_LOGGED_ON: int
TASK_FLAG_START_ONLY_IF_IDLE: int
TASK_FLAG_SYSTEM_REQUIRED: int
TASK_FOURTH_WEEK: int
TASK_FRIDAY: int
TASK_JANUARY: int
TASK_JULY: int
TASK_JUNE: int
TASK_LAST_WEEK: int
TASK_MARCH: int
TASK_MAY: int
TASK_MONDAY: int
TASK_NOVEMBER: int
TASK_OCTOBER: int
TASK_SATURDAY: int
TASK_SECOND_WEEK: int
TASK_SEPTEMBER: int
TASK_SUNDAY: int
TASK_THIRD_WEEK: int
TASK_THURSDAY: int
TASK_TIME_TRIGGER_DAILY: int
TASK_TIME_TRIGGER_MONTHLYDATE: int
TASK_TIME_TRIGGER_MONTHLYDOW: int
TASK_TIME_TRIGGER_ONCE: int
TASK_TIME_TRIGGER_WEEKLY: int
TASK_TRIGGER_FLAG_DISABLED: int
TASK_TRIGGER_FLAG_HAS_END_DATE: int
TASK_TRIGGER_FLAG_KILL_AT_DURATION_END: int
TASK_TUESDAY: int
TASK_WEDNESDAY: int
