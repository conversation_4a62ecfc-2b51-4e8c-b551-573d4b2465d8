version = "15.0.*"
# This is the official web portal for the GDB Git repo,
# see https://sourceware.org/gdb/current/ for other ways of obtaining the source code.
upstream_repository = "https://sourceware.org/git/gitweb.cgi?p=binutils-gdb.git;a=tree"
extra_description = """\
  Type hints for GDB's \
  [Python API](https://sourceware.org/gdb/onlinedocs/gdb/Python-API.html). \
  Note that this API is available only when running Python scripts under GDB: \
  it is not possible to install the `gdb` package separately, for instance \
  using `pip`.\
"""

[tool.stubtest]
platforms = ["linux"]
apt_dependencies = ["gdb"]
