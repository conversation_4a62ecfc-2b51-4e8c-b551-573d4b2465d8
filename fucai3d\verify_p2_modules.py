#!/usr/bin/env python3
"""
P2核心模块验证脚本
验证所有P2高级特征工程系统的核心模块是否可以正常导入和使用

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import traceback
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_module_import(module_name: str, class_name: str = None) -> Dict[str, Any]:
    """
    测试模块导入
    
    Args:
        module_name: 模块名称
        class_name: 类名称（可选）
    
    Returns:
        测试结果字典
    """
    result = {
        'module': module_name,
        'class': class_name,
        'import_success': False,
        'class_exists': False,
        'error': None,
        'attributes': []
    }
    
    try:
        # 导入模块
        module = __import__(module_name, fromlist=[class_name] if class_name else [])
        result['import_success'] = True
        
        if class_name:
            # 检查类是否存在
            if hasattr(module, class_name):
                result['class_exists'] = True
                cls = getattr(module, class_name)
                # 获取类的主要方法
                result['attributes'] = [attr for attr in dir(cls) 
                                      if not attr.startswith('_') and callable(getattr(cls, attr, None))]
            else:
                result['error'] = f"类 {class_name} 不存在于模块 {module_name} 中"
        else:
            # 获取模块的主要属性
            result['attributes'] = [attr for attr in dir(module) 
                                  if not attr.startswith('_')]
            
    except Exception as e:
        result['error'] = str(e)
        result['traceback'] = traceback.format_exc()
    
    return result

def test_basic_functionality():
    """测试基本功能"""
    print("🔧 测试基本功能...")
    
    # 测试数据库连接
    try:
        import sqlite3
        db_path = "data/lottery.db"
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM lottery_data")
            count = cursor.fetchone()[0]
            conn.close()
            print(f"✅ 数据库连接成功，包含 {count} 条记录")
        else:
            print(f"⚠️ 数据库文件不存在: {db_path}")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

def main():
    """主函数"""
    print("🚀 开始验证P2核心模块...")
    print("=" * 60)
    
    # 定义要测试的模块和类
    test_cases = [
        # P2核心模块
        ("src.data.advanced_feature_engineer", "AdvancedFeatureEngineer"),
        ("src.data.cache_optimizer", "CacheOptimizer"),
        ("src.data.feature_importance", "FeatureImportanceAnalyzer"),
        ("src.data.pipeline_manager", "FeaturePipelineManager"),
        ("src.interfaces.predictor_feature_interface", "PredictorFeatureInterface"),
        
        # 专用特征生成器
        ("src.data.predictor_features.hundreds_features", None),
        ("src.data.predictor_features.tens_features", None),
        ("src.data.predictor_features.units_features", None),
        ("src.data.predictor_features.sum_features", None),
        ("src.data.predictor_features.span_features", None),
        ("src.data.predictor_features.common_features", None),
        
        # P1基础模块（依赖）
        ("src.data.feature_service", "FeatureService"),
        ("src.data.feature_calculator", "LotteryFeatureCalculator"),
        
        # API模块
        ("src.api.v2.advanced_features", None),
    ]
    
    results = []
    success_count = 0
    total_count = len(test_cases)
    
    for module_name, class_name in test_cases:
        print(f"\n📦 测试模块: {module_name}")
        if class_name:
            print(f"   类: {class_name}")
        
        result = test_module_import(module_name, class_name)
        results.append(result)
        
        if result['import_success']:
            if class_name:
                if result['class_exists']:
                    print(f"✅ 导入成功，类存在")
                    print(f"   主要方法: {', '.join(result['attributes'][:5])}")
                    success_count += 1
                else:
                    print(f"❌ 导入成功但类不存在: {result['error']}")
            else:
                print(f"✅ 模块导入成功")
                print(f"   主要属性: {', '.join(result['attributes'][:5])}")
                success_count += 1
        else:
            print(f"❌ 导入失败: {result['error']}")
    
    # 测试基本功能
    print("\n" + "=" * 60)
    test_basic_functionality()
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 验证结果总结")
    print(f"总计测试: {total_count} 个模块/类")
    print(f"成功导入: {success_count} 个")
    print(f"失败导入: {total_count - success_count} 个")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("\n🎉 所有P2核心模块验证通过！")
        return True
    else:
        print("\n⚠️ 部分模块验证失败，请检查错误信息")
        
        # 输出失败详情
        print("\n❌ 失败详情:")
        for result in results:
            if not result['import_success'] or (result['class'] and not result['class_exists']):
                print(f"   {result['module']}" + (f".{result['class']}" if result['class'] else ""))
                print(f"   错误: {result['error']}")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
