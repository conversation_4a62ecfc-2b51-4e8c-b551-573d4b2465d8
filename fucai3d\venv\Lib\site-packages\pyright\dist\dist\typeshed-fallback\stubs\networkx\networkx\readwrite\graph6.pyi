from _typeshed import Incomplete
from collections.abc import Iterable

from networkx.classes.graph import Graph, _Node
from networkx.utils.backends import _dispatchable

__all__ = ["from_graph6_bytes", "read_graph6", "to_graph6_bytes", "write_graph6"]

@_dispatchable
def from_graph6_bytes(bytes_in) -> Graph[Incomplete]: ...
def to_graph6_bytes(G, nodes=None, header: bool = True): ...
@_dispatchable
def read_graph6(path): ...
def write_graph6(G, path, nodes=None, header: bool = True): ...
def write_graph6_file(G: Graph[_Node], f, nodes: Iterable[Incomplete] | None = None, header: bool = True): ...
def data_to_n(data): ...
def n_to_data(n): ...
