from _typeshed import Incomplete
from typing import NamedTuple

from psutil._common import (
    AF_INET6 as AF_INET6,
    AccessDenied as AccessDenied,
    NoSuchProcess as NoSuchProcess,
    ZombieProcess as ZombieProcess,
    debug as debug,
    get_procfs_path as get_procfs_path,
    isfile_strict as isfile_strict,
    memoize_when_activated as memoize_when_activated,
    sockfam_to_enum as sockfam_to_enum,
    socktype_to_enum as socktype_to_enum,
    usage_percent as usage_percent,
)

__extra__all__: Incomplete
PAGE_SIZE: Incomplete
AF_LINK: Incomplete
IS_64_BIT: Incomplete
CONN_IDLE: str
CONN_BOUND: str
PROC_STATUSES: Incomplete
TCP_STATUSES: Incomplete
proc_info_map: Incomplete

class scputimes(NamedTuple):
    user: Incomplete
    system: Incomplete
    idle: Incomplete
    iowait: Incomplete

class pcputimes(NamedTuple):
    user: Incomplete
    system: Incomplete
    children_user: Incomplete
    children_system: Incomplete

class svmem(NamedTuple):
    total: Incomplete
    available: Incomplete
    percent: Incomplete
    used: Incomplete
    free: Incomplete

class pmem(NamedTuple):
    rss: Incomplete
    vms: Incomplete

pfullmem = pmem

class pmmap_grouped(NamedTuple):
    path: Incomplete
    rss: Incomplete
    anonymous: Incomplete
    locked: Incomplete

pmmap_ext: Incomplete

def virtual_memory(): ...
def swap_memory(): ...
def cpu_times(): ...
def per_cpu_times(): ...
def cpu_count_logical(): ...
def cpu_count_cores(): ...
def cpu_stats(): ...

disk_io_counters: Incomplete
disk_usage: Incomplete

def disk_partitions(all: bool = ...): ...

net_io_counters: Incomplete
net_if_addrs: Incomplete

def net_connections(kind, _pid: int = ...): ...
def net_if_stats(): ...
def boot_time(): ...
def users(): ...
def pids(): ...
def pid_exists(pid): ...
def wrap_exceptions(fun): ...

class Process:
    pid: Incomplete
    def __init__(self, pid) -> None: ...
    def oneshot_enter(self) -> None: ...
    def oneshot_exit(self) -> None: ...
    def name(self): ...
    def exe(self): ...
    def cmdline(self): ...
    def environ(self): ...
    def create_time(self): ...
    def num_threads(self): ...
    def nice_get(self): ...
    def nice_set(self, value): ...
    def ppid(self): ...
    def uids(self): ...
    def gids(self): ...
    def cpu_times(self): ...
    def cpu_num(self): ...
    def terminal(self): ...
    def cwd(self): ...
    def memory_info(self): ...
    memory_full_info: Incomplete
    def status(self): ...
    def threads(self): ...
    def open_files(self): ...
    def net_connections(self, kind: str = ...): ...

    class nt_mmap_grouped(NamedTuple):
        path: Incomplete
        rss: Incomplete
        anon: Incomplete
        locked: Incomplete

    class nt_mmap_ext(NamedTuple):
        addr: Incomplete
        perms: Incomplete
        path: Incomplete
        rss: Incomplete
        anon: Incomplete
        locked: Incomplete

    def memory_maps(self): ...
    def num_fds(self): ...
    def num_ctx_switches(self): ...
    def wait(self, timeout: Incomplete | None = ...): ...
