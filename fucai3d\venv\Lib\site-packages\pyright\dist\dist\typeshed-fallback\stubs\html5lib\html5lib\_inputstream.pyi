from _typeshed import Incomplete, SupportsRead
from codecs import CodecInfo
from typing import Protocol, overload
from typing_extensions import TypeAlias

# Is actually webencodings.Encoding
class _Encoding(Protocol):
    name: str
    codec_info: CodecInfo
    def __init__(self, name: str, codec_info: CodecInfo) -> None: ...

_UnicodeInputStream: TypeAlias = str | SupportsRead[str]
_BinaryInputStream: TypeAlias = bytes | SupportsRead[bytes]
_InputStream: TypeAlias = _UnicodeInputStream | _BinaryInputStream  # noqa: Y047  # used in other files

spaceCharactersBytes: Incomplete
asciiLettersBytes: Incomplete
asciiUppercaseBytes: Incomplete
spacesAngleBrackets: Incomplete
invalid_unicode_no_surrogate: str
invalid_unicode_re: Incomplete
non_bmp_invalid_codepoints: Incomplete
ascii_punctuation_re: Incomplete
charsUntilRegEx: Incomplete

class BufferedStream:
    stream: Incomplete
    buffer: Incomplete
    position: Incomplete
    def __init__(self, stream) -> None: ...
    def tell(self): ...
    def seek(self, pos) -> None: ...
    def read(self, bytes): ...

@overload
def HTMLInputStream(source: _UnicodeInputStream) -> HTMLUnicodeInputStream: ...
@overload
def HTMLInputStream(
    source: _BinaryInputStream,
    *,
    override_encoding: str | bytes | None = None,
    transport_encoding: str | bytes | None = None,
    same_origin_parent_encoding: str | bytes | None = None,
    likely_encoding: str | bytes | None = None,
    default_encoding: str = "windows-1252",
    useChardet: bool = True,
) -> HTMLBinaryInputStream: ...

class HTMLUnicodeInputStream:
    reportCharacterErrors: Incomplete
    newLines: Incomplete
    charEncoding: tuple[_Encoding, str]
    dataStream: Incomplete
    def __init__(self, source: _UnicodeInputStream) -> None: ...
    chunk: str
    chunkSize: int
    chunkOffset: int
    errors: list[str]
    prevNumLines: int
    prevNumCols: int
    def reset(self) -> None: ...
    def openStream(self, source): ...
    def position(self) -> tuple[int, int]: ...
    def char(self): ...
    def readChunk(self, chunkSize=None): ...
    def characterErrorsUCS4(self, data) -> None: ...
    def characterErrorsUCS2(self, data) -> None: ...
    def charsUntil(self, characters, opposite: bool = False): ...
    def unget(self, char) -> None: ...

class HTMLBinaryInputStream(HTMLUnicodeInputStream):
    rawStream: Incomplete
    numBytesMeta: int
    numBytesChardet: int
    override_encoding: Incomplete
    transport_encoding: Incomplete
    same_origin_parent_encoding: Incomplete
    likely_encoding: Incomplete
    default_encoding: Incomplete
    charEncoding: tuple[_Encoding, str]
    def __init__(
        self,
        source: _BinaryInputStream,
        override_encoding: str | bytes | None = None,
        transport_encoding: str | bytes | None = None,
        same_origin_parent_encoding: str | bytes | None = None,
        likely_encoding: str | bytes | None = None,
        default_encoding: str = "windows-1252",
        useChardet: bool = True,
    ) -> None: ...
    dataStream: Incomplete
    def reset(self) -> None: ...
    def openStream(self, source): ...
    def determineEncoding(self, chardet: bool = True): ...
    def changeEncoding(self, newEncoding: str | bytes | None) -> None: ...
    def detectBOM(self): ...
    def detectEncodingMeta(self): ...

class EncodingBytes(bytes):
    def __new__(self, value): ...
    def __init__(self, value) -> None: ...
    def __iter__(self): ...
    def __next__(self): ...
    def next(self): ...
    def previous(self): ...
    def setPosition(self, position) -> None: ...
    def getPosition(self): ...
    position: Incomplete
    def getCurrentByte(self): ...
    @property
    def currentByte(self): ...
    def skip(self, chars=...): ...
    def skipUntil(self, chars): ...
    def matchBytes(self, bytes): ...
    def jumpTo(self, bytes): ...

class EncodingParser:
    data: Incomplete
    encoding: Incomplete
    def __init__(self, data) -> None: ...
    def getEncoding(self): ...
    def handleComment(self): ...
    def handleMeta(self): ...
    def handlePossibleStartTag(self): ...
    def handlePossibleEndTag(self): ...
    def handlePossibleTag(self, endTag): ...
    def handleOther(self): ...
    def getAttribute(self): ...

class ContentAttrParser:
    data: Incomplete
    def __init__(self, data) -> None: ...
    def parse(self): ...

def lookupEncoding(encoding: str | bytes | None) -> str | None: ...
