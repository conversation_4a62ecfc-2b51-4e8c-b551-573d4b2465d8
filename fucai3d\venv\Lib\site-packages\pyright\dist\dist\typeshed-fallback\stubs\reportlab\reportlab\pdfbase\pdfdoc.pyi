from _typeshed import Incomplete
from collections.abc import Mapping
from typing import Final

__version__: Final[str]

class PDFError(Exception): ...

__InternalName__: Final[str]
__RefOnly__: Final[str]
__Comment__: Final[str]
BasicFonts: Final[str]
Pages: Final[str]
PDF_VERSION_DEFAULT: Final[tuple[int, int]]
PDF_SUPPORT_VERSION: Final[Mapping[str, tuple[int, int]]]

def pdfdocEnc(x): ...
def format(element, document, toplevel: int = 0): ...
def xObjectName(externalname): ...

formName = xObjectName

class NoEncryption:
    def encode(self, t): ...
    def prepare(self, document) -> None: ...
    def register(self, objnum, version) -> None: ...
    def info(self) -> None: ...

class PDFObject: ...

class DummyDoc(PDFObject):
    encrypt: Incomplete

class PDFDocument(PDFObject):
    defaultStreamFilters: Incomplete
    encrypt: Incomplete
    objectcounter: int
    shadingCounter: int
    inObject: Incomplete
    pageCounter: int
    invariant: Incomplete
    idToObjectNumberAndVersion: Incomplete
    idToObject: Incomplete
    idToOffset: Incomplete
    numberToId: Incomplete
    outline: Incomplete
    info: Incomplete
    fontMapping: Incomplete
    delayedFonts: Incomplete
    def __init__(
        self, dummyoutline: int = 0, compression=1, invariant=0, filename=None, pdfVersion=(1, 3), lang=None
    ) -> None: ...
    compression: Incomplete
    def setCompression(self, onoff) -> None: ...
    def ensureMinPdfVersion(self, *keys) -> None: ...
    def updateSignature(self, thing) -> None: ...
    def ID(self): ...
    def SaveToFile(self, filename, canvas) -> None: ...
    def GetPDFData(self, canvas): ...
    def inPage(self) -> None: ...
    def inForm(self) -> None: ...
    def getInternalFontName(self, psfontname): ...
    def thisPageName(self): ...
    def thisPageRef(self): ...
    def addPage(self, page) -> None: ...
    def addForm(self, name, form) -> None: ...
    def annotationName(self, externalname): ...
    def addAnnotation(self, name, annotation) -> None: ...
    def refAnnotation(self, name): ...
    def addShading(self, shading): ...
    def addColor(self, cmyk): ...
    def setTitle(self, title) -> None: ...
    def setAuthor(self, author) -> None: ...
    def setSubject(self, subject) -> None: ...
    def setCreator(self, creator) -> None: ...
    def setProducer(self, producer) -> None: ...
    def setKeywords(self, keywords) -> None: ...
    def setDateFormatter(self, dateFormatter) -> None: ...
    def getAvailableFonts(self): ...
    __accum__: Incomplete
    def format(self): ...
    def hasForm(self, name): ...
    def getFormBBox(self, name, boxType: str = "MediaBox"): ...
    def getXObjectName(self, name): ...
    def xobjDict(self, formnames): ...
    def Reference(self, obj, name=None): ...

PDFtrue: str
PDFfalse: str
PDFnull: str

class PDFText(PDFObject):
    t: Incomplete
    enc: Incomplete
    def __init__(self, t, enc: str = "utf-8") -> None: ...
    def format(self, document): ...

def PDFnumber(n): ...

class PDFString(PDFObject):
    unicodeEncValid: bool
    s: Incomplete
    escape: Incomplete
    enc: Incomplete
    def __init__(self, s, escape: int = 1, enc: str = "auto") -> None: ...
    def format(self, document): ...

def PDFName(data, lo="!", hi="~"): ...

class PDFDictionary(PDFObject):
    multiline: bool
    dict: Incomplete
    def __init__(self, dict=None) -> None: ...
    def __setitem__(self, name, value) -> None: ...
    def __getitem__(self, a): ...
    def __contains__(self, a) -> bool: ...
    def Reference(self, name, document) -> None: ...
    def format(self, document, IND: bytes = b"\n "): ...
    def copy(self): ...
    def normalize(self) -> None: ...

class checkPDFNames:
    names: Incomplete
    def __init__(self, *names) -> None: ...
    def __call__(self, value): ...

def checkPDFBoolean(value): ...

class CheckedPDFDictionary(PDFDictionary):
    validate: Incomplete
    def __init__(self, dict=None, validate=None) -> None: ...
    def __setitem__(self, name, value) -> None: ...

class ViewerPreferencesPDFDictionary(CheckedPDFDictionary):
    validate: Incomplete

class PDFStreamFilterZCompress:
    pdfname: str
    def encode(self, text): ...
    def decode(self, encoded): ...

PDFZCompress: Incomplete

class PDFStreamFilterBase85Encode:
    pdfname: str
    def encode(self, text): ...
    def decode(self, text): ...

PDFBase85Encode: Incomplete

class PDFStream(PDFObject):
    __RefOnly__: int
    dictionary: Incomplete
    content: Incomplete
    filters: Incomplete
    def __init__(self, dictionary=None, content=None, filters=None) -> None: ...
    def format(self, document): ...

def teststream(content=None): ...

teststreamcontent: str

class PDFArray(PDFObject):
    multiline: bool
    sequence: Incomplete
    def __init__(self, sequence) -> None: ...
    def References(self, document) -> None: ...
    def format(self, document, IND: bytes = b"\n "): ...

class PDFArrayCompact(PDFArray):
    multiline: bool

class PDFIndirectObject(PDFObject):
    __RefOnly__: int
    name: Incomplete
    content: Incomplete
    def __init__(self, name, content) -> None: ...
    def format(self, document): ...

class PDFObjectReference(PDFObject):
    name: Incomplete
    def __init__(self, name) -> None: ...
    def format(self, document): ...

class PDFFile(PDFObject):
    strings: Incomplete
    write: Incomplete
    offset: int
    def __init__(self, pdfVersion=(1, 3)) -> None: ...
    def closeOrReset(self) -> None: ...
    def add(self, s): ...
    def format(self, document): ...

class PDFCrossReferenceSubsection(PDFObject):
    firstentrynumber: Incomplete
    idsequence: Incomplete
    def __init__(self, firstentrynumber, idsequence) -> None: ...
    def format(self, document): ...

class PDFCrossReferenceTable(PDFObject):
    sections: Incomplete
    def __init__(self) -> None: ...
    def addsection(self, firstentry, ids) -> None: ...
    def format(self, document): ...

class PDFTrailer(PDFObject):
    startxref: Incomplete
    def __init__(self, startxref, Size=None, Prev=None, Root=None, Info=None, ID=None, Encrypt=None) -> None: ...
    def format(self, document): ...

class PDFCatalog(PDFObject):
    __Comment__: str
    __RefOnly__: int
    __Defaults__: Incomplete
    __NoDefault__: Incomplete
    __Refs__ = __NoDefault__
    def format(self, document): ...
    def showOutline(self) -> None: ...
    def showFullScreen(self) -> None: ...
    PageLayout: Incomplete
    def setPageLayout(self, layout) -> None: ...
    PageMode: Incomplete
    def setPageMode(self, mode) -> None: ...
    def check_format(self, document) -> None: ...

class PDFPages(PDFCatalog):
    __Comment__: str
    __RefOnly__: int
    __Defaults__: Incomplete
    __NoDefault__: Incomplete
    __Refs__: Incomplete
    pages: Incomplete
    def __init__(self) -> None: ...
    def __getitem__(self, item): ...
    def addPage(self, page) -> None: ...
    Kids: Incomplete
    Count: Incomplete
    def check_format(self, document) -> None: ...

class PDFPage(PDFCatalog):
    __Comment__: str
    Override_default_compilation: int
    __RefOnly__: int
    __Defaults__: Incomplete
    __NoDefault__: Incomplete
    __Refs__: Incomplete
    pagewidth: int
    pageheight: int
    stream: Incomplete
    hasImages: int
    compression: int
    XObjects: Incomplete
    Trans: Incomplete
    def __init__(self) -> None: ...
    def setCompression(self, onoff) -> None: ...
    def setStream(self, code) -> None: ...
    def setPageTransition(self, tranDict) -> None: ...
    MediaBox: Incomplete
    Annots: Incomplete
    Contents: Incomplete
    Resources: Incomplete
    Parent: Incomplete
    def check_format(self, document) -> None: ...

class DuplicatePageLabelPage(Exception): ...

class PDFPageLabels(PDFCatalog):
    __comment__: Incomplete
    __RefOnly__: int
    __Defaults__: Incomplete
    __NoDefault__: Incomplete
    __Refs__: Incomplete
    labels: Incomplete
    def __init__(self) -> None: ...
    def addPageLabel(self, page, label) -> None: ...
    Nums: Incomplete
    def format(self, document): ...

class PDFPageLabel(PDFCatalog):
    __Comment__: Incomplete
    __RefOnly__: int
    __Defaults__: Incomplete
    __NoDefault__: Incomplete
    __convertible__: str
    ARABIC: str
    ROMAN_UPPER: str
    ROMAN_LOWER: str
    LETTERS_UPPER: str
    LETTERS_LOWER: str
    S: Incomplete
    St: Incomplete
    P: Incomplete
    def __init__(self, style=None, start=None, prefix=None) -> None: ...
    def __lt__(self, oth): ...

def testpage(document) -> None: ...

DUMMYOUTLINE: str

class PDFOutlines0(PDFObject):
    __Comment__: str
    text: Incomplete
    __RefOnly__: int
    def format(self, document): ...

class OutlineEntryObject(PDFObject):
    Title: Incomplete
    Dest: Incomplete
    Parent: Incomplete
    Prev: Incomplete
    Next: Incomplete
    First: Incomplete
    Last: Incomplete
    Count: Incomplete
    def format(self, document): ...

class PDFOutlines(PDFObject):
    mydestinations: Incomplete
    ready: Incomplete
    counter: int
    currentlevel: int
    destinationnamestotitles: Incomplete
    destinationstotitles: Incomplete
    levelstack: Incomplete
    buildtree: Incomplete
    closedict: Incomplete
    def __init__(self) -> None: ...
    def addOutlineEntry(self, destinationname, level: int = 0, title=None, closed=None) -> None: ...
    def setDestinations(self, destinationtree) -> None: ...
    def format(self, document): ...
    def setNames(self, canvas, *nametree) -> None: ...
    def setNameList(self, canvas, nametree) -> None: ...
    def translateNames(self, canvas, object): ...
    first: Incomplete
    count: int
    def prepare(self, document, canvas) -> None: ...
    def maketree(self, document, destinationtree, Parent=None, toplevel: int = 0): ...

def count(tree, closedict=None): ...

class PDFInfo(PDFObject):
    producer: Incomplete
    creator: str
    title: str
    author: str
    subject: str
    keywords: str
    trapped: str
    def __init__(self) -> None: ...
    def digest(self, md5object) -> None: ...
    def format(self, document): ...
    def copy(self): ...

class Annotation(PDFObject):
    defaults: Incomplete
    required: Incomplete
    permitted: Incomplete
    def cvtdict(self, d, escape: int = 1): ...
    def AnnotationDict(self, **kw): ...
    def Dict(self) -> None: ...
    def format(self, document): ...

class FreeTextAnnotation(Annotation):
    permitted: Incomplete
    Rect: Incomplete
    Contents: Incomplete
    DA: Incomplete
    otherkw: Incomplete
    def __init__(self, Rect, Contents, DA, **kw) -> None: ...
    def Dict(self): ...

class LinkAnnotation(Annotation):
    permitted: Incomplete
    Border: Incomplete
    Rect: Incomplete
    Contents: Incomplete
    Destination: Incomplete
    otherkw: Incomplete
    def __init__(self, Rect, Contents, Destination, Border: str = "[0 0 1]", **kw) -> None: ...
    def dummyDictString(self): ...
    def Dict(self): ...

class HighlightAnnotation(Annotation):
    permitted: Incomplete
    Rect: Incomplete
    Contents: Incomplete
    otherkw: Incomplete
    QuadPoints: Incomplete
    Color: Incomplete
    def __init__(self, Rect, Contents, QuadPoints, Color=[0.83, 0.89, 0.95], **kw) -> None: ...
    def cvtdict(self, d, escape: int = 1): ...
    def Dict(self): ...

class TextAnnotation(HighlightAnnotation):
    permitted: Incomplete
    def __init__(self, Rect, Contents, **kw) -> None: ...
    def Dict(self): ...

def rect_to_quad(Rect): ...

class PDFRectangle(PDFObject):
    def __init__(self, llx, lly, urx, ury) -> None: ...
    def format(self, document): ...

class PDFDate(PDFObject):
    dateFormatter: Incomplete
    def __init__(self, ts=None, dateFormatter=None) -> None: ...
    def format(self, doc): ...

class Destination(PDFObject):
    representation: None
    page: None
    name: Incomplete
    fmt: Incomplete
    def __init__(self, name) -> None: ...
    def format(self, document): ...
    def xyz(self, left, top, zoom) -> None: ...
    def fit(self) -> None: ...
    def fitb(self) -> None: ...
    def fith(self, top) -> None: ...
    def fitv(self, left) -> None: ...
    def fitbh(self, top) -> None: ...
    def fitbv(self, left) -> None: ...
    def fitr(self, left, bottom, right, top) -> None: ...
    def setPage(self, page) -> None: ...

class PDFDestinationXYZ(PDFObject):
    typename: str
    page: Incomplete
    top: Incomplete
    zoom: Incomplete
    left: Incomplete
    def __init__(self, page, left, top, zoom) -> None: ...
    def format(self, document): ...

class PDFDestinationFit(PDFObject):
    typename: str
    page: Incomplete
    def __init__(self, page) -> None: ...
    def format(self, document): ...

class PDFDestinationFitB(PDFDestinationFit):
    typename: str

class PDFDestinationFitH(PDFObject):
    typename: str
    page: Incomplete
    top: Incomplete
    def __init__(self, page, top) -> None: ...
    def format(self, document): ...

class PDFDestinationFitBH(PDFDestinationFitH):
    typename: str

class PDFDestinationFitV(PDFObject):
    typename: str
    page: Incomplete
    left: Incomplete
    def __init__(self, page, left) -> None: ...
    def format(self, document): ...

class PDFDestinationFitBV(PDFDestinationFitV):
    typename: str

class PDFDestinationFitR(PDFObject):
    typename: str
    page: Incomplete
    left: Incomplete
    bottom: Incomplete
    right: Incomplete
    top: Incomplete
    def __init__(self, page, left, bottom, right, top) -> None: ...
    def format(self, document): ...

class PDFResourceDictionary(PDFObject):
    ProcSet: Incomplete
    def __init__(self, **kwds) -> None: ...
    stdprocs: Incomplete
    dict_attributes: Incomplete
    def allProcs(self) -> None: ...
    def basicProcs(self) -> None: ...
    Font: Incomplete
    def basicFonts(self) -> None: ...
    def setColorSpace(self, colorsUsed) -> None: ...
    def setShading(self, shadingUsed) -> None: ...
    def format(self, document): ...

class PDFType1Font(PDFObject):
    __RefOnly__: int
    name_attributes: Incomplete
    Type: str
    Subtype: str
    local_attributes: Incomplete
    def format(self, document): ...

class PDFTrueTypeFont(PDFType1Font):
    Subtype: str

class PDFFormXObject(PDFObject):
    XObjects: Incomplete
    Annots: Incomplete
    BBox: Incomplete
    Matrix: Incomplete
    Contents: Incomplete
    stream: Incomplete
    Resources: Incomplete
    hasImages: int
    compression: int
    lowerx: Incomplete
    lowery: Incomplete
    upperx: Incomplete
    uppery: Incomplete
    def __init__(self, lowerx, lowery, upperx, uppery) -> None: ...
    def setStreamList(self, data) -> None: ...
    def BBoxList(self): ...
    def format(self, document): ...

class PDFPostScriptXObject(PDFObject):
    content: Incomplete
    def __init__(self, content=None) -> None: ...
    def format(self, document): ...

class PDFImageXObject(PDFObject):
    name: Incomplete
    width: int
    height: int
    bitsPerComponent: int
    colorSpace: str
    streamContent: str
    mask: Incomplete
    def __init__(self, name, source=None, mask=None) -> None: ...
    def loadImageFromA85(self, source): ...
    def loadImageFromJPEG(self, imageFile): ...
    def loadImageFromRaw(self, source): ...
    def loadImageFromSRC(self, im) -> None: ...
    def format(self, document): ...

class PDFSeparationCMYKColor:
    cmyk: Incomplete
    def __init__(self, cmyk) -> None: ...
    def value(self): ...

class PDFFunction(PDFObject):
    defaults: Incomplete
    required: Incomplete
    permitted: Incomplete
    def FunctionDict(self, **kw): ...
    def Dict(self, document) -> None: ...
    def format(self, document): ...

class PDFExponentialFunction(PDFFunction):
    defaults: Incomplete
    required: Incomplete
    permitted: Incomplete
    C0: Incomplete
    C1: Incomplete
    N: Incomplete
    otherkw: Incomplete
    def __init__(self, C0, C1, N, **kw) -> None: ...
    def Dict(self, document): ...

class PDFStitchingFunction(PDFFunction):
    required: Incomplete
    permitted: Incomplete
    Functions: Incomplete
    Bounds: Incomplete
    Encode: Incomplete
    otherkw: Incomplete
    def __init__(self, Functions, Bounds, Encode, **kw) -> None: ...
    def Dict(self, document): ...

class PDFShading(PDFObject):
    required: Incomplete
    permitted: Incomplete
    def ShadingDict(self, **kw): ...
    def Dict(self, document) -> None: ...
    def format(self, document): ...

class PDFFunctionShading(PDFShading):
    required: Incomplete
    permitted: Incomplete
    Function: Incomplete
    ColorSpace: Incomplete
    otherkw: Incomplete
    def __init__(self, Function, ColorSpace, **kw) -> None: ...
    def Dict(self, document): ...

class PDFAxialShading(PDFShading):
    required: Incomplete
    permitted: Incomplete
    Coords: Incomplete
    Function: Incomplete
    ColorSpace: Incomplete
    otherkw: Incomplete
    def __init__(self, x0, y0, x1, y1, Function, ColorSpace, **kw) -> None: ...
    def Dict(self, document): ...

class PDFRadialShading(PDFShading):
    required: Incomplete
    permitted: Incomplete
    Coords: Incomplete
    Function: Incomplete
    ColorSpace: Incomplete
    otherkw: Incomplete
    def __init__(self, x0, y0, r0, x1, y1, r1, Function, ColorSpace, **kw) -> None: ...
    def Dict(self, document): ...

class XMP(PDFStream):
    def __init__(self, path=None, creator=None) -> None: ...
    def makeContent(self, doc): ...
    # Param name is changed from the base class:
    def format(self, doc): ...
