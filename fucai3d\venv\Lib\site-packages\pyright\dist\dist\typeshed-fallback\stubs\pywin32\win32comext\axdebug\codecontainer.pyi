from _typeshed import Incomplete

name: str

class SourceCodeContainer:
    sourceContext: Incomplete
    text: Incomplete
    nextLineNo: int
    fileName: Incomplete
    codeContexts: Incomplete
    site: Incomplete
    startLineNumber: Incomplete
    debugDocument: Incomplete | None
    def __init__(
        self,
        text,
        fileName: str = ...,
        sourceContext: int = ...,
        startLineNumber: int = ...,
        site: Incomplete | None = ...,
        debugDocument: Incomplete | None = ...,
    ) -> None: ...
    def GetText(self): ...
    def GetName(self, dnt) -> None: ...
    def GetFileName(self): ...
    def GetPositionOfLine(self, cLineNumber): ...
    def GetLineOfPosition(self, charPos): ...
    def GetNextLine(self): ...
    def GetLine(self, num): ...
    def GetNumChars(self): ...
    def GetNumLines(self): ...
    lastPos: int
    attrs: Incomplete
    def GetSyntaxColorAttributes(self): ...
    def GetCodeContextAtPosition(self, charPos): ...

class SourceModuleContainer(SourceCodeContainer):
    module: Incomplete
    def __init__(self, module) -> None: ...
    text: Incomplete
    def GetText(self): ...
    def GetName(self, dnt): ...
