from _typeshed import Incomplete

class TransactionGateway:
    gateway: Incomplete
    config: Incomplete
    def __init__(self, gateway) -> None: ...
    def adjust_authorization(self, transaction_id, amount): ...
    def clone_transaction(self, transaction_id, params): ...
    def cancel_release(self, transaction_id): ...
    def create(self, params): ...
    def credit(self, params): ...
    def find(self, transaction_id): ...
    def refund(self, transaction_id, amount_or_options=None): ...
    def sale(self, params): ...
    def search(self, *query): ...
    def submit_for_settlement(self, transaction_id, amount=None, params=None): ...
    def update_details(self, transaction_id, params=None): ...
    def submit_for_partial_settlement(self, transaction_id, amount, params=None): ...
    def package_tracking(self, transaction_id, params=None): ...
    def void(self, transaction_id): ...
