import datetime
from typing import Any
from typing_extensions import Self

ZERO: datetime.timedelta

class FixedOffsetTimezone(datetime.tzinfo):
    def __init__(self, offset: datetime.timedelta | float | None = None, name: str | None = None) -> None: ...
    def __new__(cls, offset: datetime.timedelta | float | None = None, name: str | None = None) -> Self: ...
    def __eq__(self, other: object) -> bool: ...
    def __ne__(self, other: object) -> bool: ...
    def __getinitargs__(self) -> tuple[Any, ...]: ...
    def utcoffset(self, dt: datetime.datetime | None) -> datetime.timedelta: ...
    def tzname(self, dt: datetime.datetime | None) -> str: ...
    def dst(self, dt: datetime.datetime | None) -> datetime.timedelta: ...

STDOFFSET: datetime.timedelta
DSTOFFSET: datetime.timedelta
DSTDIFF: datetime.timedelta

class LocalTimezone(datetime.tzinfo):
    def utcoffset(self, dt: datetime.datetime) -> datetime.timedelta: ...  # type: ignore[override]
    def dst(self, dt: datetime.datetime) -> datetime.timedelta: ...  # type: ignore[override]
    def tzname(self, dt: datetime.datetime) -> str: ...  # type: ignore[override]

LOCAL: LocalTimezone
