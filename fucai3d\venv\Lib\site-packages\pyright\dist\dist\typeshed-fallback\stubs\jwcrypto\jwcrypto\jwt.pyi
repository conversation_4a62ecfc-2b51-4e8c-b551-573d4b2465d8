from collections.abc import Mapping
from typing import Any, SupportsInt
from typing_extensions import deprecated

from jwcrypto.common import JWException, JWKeyNotFound
from jwcrypto.jwk import JWK, JWKSet

JWTClaimsRegistry: Mapping[str, str]
JWT_expect_type: bool

class JWTExpired(JWException):
    def __init__(self, message: str | None = None, exception: BaseException | None = None) -> None: ...

class JWTNotYetValid(JWException):
    def __init__(self, message: str | None = None, exception: BaseException | None = None) -> None: ...

class JWTMissingClaim(JWException):
    def __init__(self, message: str | None = None, exception: BaseException | None = None) -> None: ...

class JWTInvalidClaimValue(JWException):
    def __init__(self, message: str | None = None, exception: BaseException | None = None) -> None: ...

class JWTInvalidClaimFormat(JWException):
    def __init__(self, message: str | None = None, exception: BaseException | None = None) -> None: ...

@deprecated("")
class JWTMissingKeyID(JWException):
    def __init__(self, message: str | None = None, exception: BaseException | None = None) -> None: ...

class JWTMissingKey(JWKeyNotFound):
    def __init__(self, message: str | None = None, exception: BaseException | None = None) -> None: ...

class JWT:
    deserializelog: list[str] | None
    def __init__(
        self,
        header: dict[str, Any] | str | None = None,
        claims: dict[str, Any] | str | None = None,
        jwt=None,
        key: JWK | JWKSet | None = None,
        algs=None,
        default_claims=None,
        check_claims=None,
        expected_type=None,
    ) -> None: ...
    @property
    def header(self) -> str: ...
    @header.setter
    def header(self, h: dict[str, Any] | str) -> None: ...
    @property
    def claims(self) -> str: ...
    @claims.setter
    def claims(self, data: str) -> None: ...
    @property
    def token(self): ...
    @token.setter
    def token(self, t) -> None: ...
    @property
    def leeway(self) -> int: ...
    @leeway.setter
    def leeway(self, lwy: SupportsInt) -> None: ...
    @property
    def validity(self) -> int: ...
    @validity.setter
    def validity(self, v: SupportsInt) -> None: ...
    @property
    def expected_type(self): ...
    @expected_type.setter
    def expected_type(self, v) -> None: ...
    def norm_typ(self, val): ...
    def make_signed_token(self, key: JWK) -> None: ...
    def make_encrypted_token(self, key: JWK) -> None: ...
    def validate(self, key: JWK | JWKSet) -> None: ...
    def deserialize(self, jwt, key=None) -> None: ...
    def serialize(self, compact: bool = True) -> str: ...
    @classmethod
    def from_jose_token(cls, token): ...
    def __eq__(self, other: object) -> bool: ...
