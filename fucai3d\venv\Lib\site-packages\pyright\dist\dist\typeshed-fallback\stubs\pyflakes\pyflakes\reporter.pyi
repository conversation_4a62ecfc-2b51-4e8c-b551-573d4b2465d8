from _typeshed import SupportsWrite

from .messages import Message

class Reporter:
    def __init__(self, warningStream: SupportsWrite[str], errorStream: SupportsWrite[str]) -> None: ...
    def unexpectedError(self, filename: str, msg: str) -> None: ...
    def syntaxError(self, filename: str, msg: str, lineno: int, offset: int | None, text: str | None) -> None: ...
    def flake(self, message: Message) -> None: ...
