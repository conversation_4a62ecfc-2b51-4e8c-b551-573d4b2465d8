from _typeshed import Incomplete

from antlr4.Token import Token as Token

INVALID_INTERVAL: Incomplete

class Tree: ...
class SyntaxTree(Tree): ...
class ParseTree(SyntaxTree): ...
class RuleNode(ParseTree): ...
class TerminalNode(ParseTree): ...
class ErrorNode(TerminalNode): ...

class ParseTreeVisitor:
    def visit(self, tree): ...
    def visitChildren(self, node): ...
    def visitTerminal(self, node): ...
    def visitErrorNode(self, node): ...
    def defaultResult(self) -> None: ...
    def aggregateResult(self, aggregate, nextResult): ...
    def shouldVisitNextChild(self, node, currentResult): ...

class ParseTreeListener:
    def visitTerminal(self, node: TerminalNode): ...
    def visitErrorNode(self, node: ErrorNode): ...
    def enterEveryRule(self, ctx): ...
    def exitEveryRule(self, ctx): ...

class TerminalNodeImpl(TerminalNode):
    parentCtx: Incomplete
    symbol: Incomplete
    def __init__(self, symbol: Token) -> None: ...
    def __setattr__(self, key, value) -> None: ...
    def getChild(self, i: int): ...
    def getSymbol(self): ...
    def getParent(self): ...
    def getPayload(self): ...
    def getSourceInterval(self): ...
    def getChildCount(self): ...
    def accept(self, visitor: ParseTreeVisitor): ...
    def getText(self): ...

class ErrorNodeImpl(TerminalNodeImpl, ErrorNode):
    def __init__(self, token: Token) -> None: ...
    def accept(self, visitor: ParseTreeVisitor): ...

class ParseTreeWalker:
    DEFAULT: Incomplete
    def walk(self, listener: ParseTreeListener, t: ParseTree): ...
    def enterRule(self, listener: ParseTreeListener, r: RuleNode): ...
    def exitRule(self, listener: ParseTreeListener, r: RuleNode): ...
