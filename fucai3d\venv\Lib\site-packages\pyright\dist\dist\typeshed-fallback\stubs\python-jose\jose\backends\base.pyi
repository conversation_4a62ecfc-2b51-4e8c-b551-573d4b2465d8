from typing import Any
from typing_extensions import Self

class Key:
    # Enable when we can use stubs from installed dependencies,
    # as `key` can be of type cryptography.x509.base.Certificate:
    # from cryptography.x509 import Certificate
    def __init__(self, key, algorithm) -> None: ...
    def sign(self, msg: bytes) -> bytes: ...
    def verify(self, msg: bytes, sig: bytes) -> bool: ...
    def public_key(self) -> Self: ...
    def to_pem(self) -> bytes: ...
    def to_dict(self) -> dict[str, Any]: ...
    def encrypt(self, plain_text: str | bytes, aad: bytes | None = None) -> tuple[bytes, bytes, bytes | None]: ...
    def decrypt(
        self, cipher_text: str | bytes, iv: str | bytes | None = None, aad: bytes | None = None, tag: bytes | None = None
    ) -> bytes: ...
    def wrap_key(self, key_data: bytes) -> bytes: ...
    def unwrap_key(self, wrapped_key: bytes) -> bytes: ...

class DIRKey(Key):
    def __init__(self, key_data: str | bytes, algorithm: str) -> None: ...
    def to_dict(self) -> dict[str, Any]: ...
