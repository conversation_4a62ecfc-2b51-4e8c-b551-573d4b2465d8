from hvac.api.vault_api_base import VaultApiBase

class AppRole(VaultApiBase):
    def create_or_update_approle(
        self,
        role_name,
        bind_secret_id=None,
        secret_id_bound_cidrs=None,
        secret_id_num_uses=None,
        secret_id_ttl=None,
        enable_local_secret_ids=None,
        token_ttl=None,
        token_max_ttl=None,
        token_policies=None,
        token_bound_cidrs=None,
        token_explicit_max_ttl=None,
        token_no_default_policy=None,
        token_num_uses=None,
        token_period=None,
        token_type=None,
        mount_point="approle",
    ): ...
    def list_roles(self, mount_point="approle"): ...
    def read_role(self, role_name, mount_point="approle"): ...
    def delete_role(self, role_name, mount_point="approle"): ...
    def read_role_id(self, role_name, mount_point="approle"): ...
    def update_role_id(self, role_name, role_id, mount_point="approle"): ...
    def generate_secret_id(
        self, role_name, metadata=None, cidr_list=None, token_bound_cidrs=None, mount_point="approle", wrap_ttl=None
    ): ...
    def create_custom_secret_id(
        self, role_name, secret_id, metadata=None, cidr_list=None, token_bound_cidrs=None, mount_point="approle", wrap_ttl=None
    ): ...
    def read_secret_id(self, role_name, secret_id, mount_point="approle"): ...
    def destroy_secret_id(self, role_name, secret_id, mount_point="approle"): ...
    def list_secret_id_accessors(self, role_name, mount_point="approle"): ...
    def read_secret_id_accessor(self, role_name, secret_id_accessor, mount_point="approle"): ...
    def destroy_secret_id_accessor(self, role_name, secret_id_accessor, mount_point="approle"): ...
    def login(self, role_id, secret_id=None, use_token: bool = True, mount_point="approle"): ...
