import re
from _typeshed import Incomplete
from typing import Final

PORT_SPEC: Final[re.Pattern[str]]

def add_port_mapping(port_bindings, internal_port, external) -> None: ...
def add_port(port_bindings, internal_port_range, external_range) -> None: ...
def build_port_bindings(ports) -> dict[Incomplete, Incomplete]: ...
def port_range(start, end, proto, randomly_available_port: bool = False): ...
def split_port(port: object) -> tuple[Incomplete, Incomplete]: ...
