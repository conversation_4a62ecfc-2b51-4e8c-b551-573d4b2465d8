from _typeshed import Incomplete
from typing import SupportsIndex, overload

from .base import NonRecursiveTreeWalker

@overload
def ensure_str(s: None) -> None: ...
@overload
def ensure_str(s: str | bytes | bytearray) -> str: ...

class Root:
    elementtree: Incomplete
    children: list[Incomplete]
    text: str | None
    tail: str | None
    def __init__(self, et) -> None: ...
    def __getitem__(self, key: SupportsIndex): ...
    def getnext(self) -> None: ...
    def __len__(self) -> int: ...

class Doctype:
    root_node: Incomplete
    name: Incomplete
    public_id: Incomplete
    system_id: Incomplete
    text: Incomplete
    tail: Incomplete
    def __init__(self, root_node, name, public_id, system_id) -> None: ...
    def getnext(self): ...

class FragmentRoot(Root):
    children: Incomplete
    text: Incomplete
    def __init__(self, children) -> None: ...
    def getnext(self) -> None: ...

class FragmentWrapper:
    root_node: Incomplete
    obj: Incomplete
    text: Incomplete
    tail: Incomplete
    def __init__(self, fragment_root, obj) -> None: ...
    def __getattr__(self, name: str): ...
    def getnext(self): ...
    def __getitem__(self, key): ...
    def __bool__(self) -> bool: ...
    def getparent(self) -> None: ...
    def __unicode__(self) -> str: ...
    def __len__(self) -> int: ...

class TreeWalker(NonRecursiveTreeWalker):
    fragmentChildren: Incomplete
    filter: Incomplete
    def __init__(self, tree) -> None: ...
    def getNodeDetails(self, node): ...
    def getFirstChild(self, node): ...
    def getNextSibling(self, node): ...
    def getParentNode(self, node): ...
