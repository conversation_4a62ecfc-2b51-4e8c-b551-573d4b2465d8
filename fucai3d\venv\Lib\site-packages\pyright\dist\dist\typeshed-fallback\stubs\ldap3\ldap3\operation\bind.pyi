def bind_operation(
    version, authentication, name: str = "", password=None, sasl_mechanism=None, sasl_credentials=None, auto_encode: bool = False
): ...
def bind_request_to_dict(request): ...
def bind_response_operation(
    result_code, matched_dn: str = "", diagnostic_message: str = "", referral=None, server_sasl_credentials=None
): ...
def bind_response_to_dict(response): ...
def sicily_bind_response_to_dict(response): ...
def bind_response_to_dict_fast(response): ...
def sicily_bind_response_to_dict_fast(response): ...
