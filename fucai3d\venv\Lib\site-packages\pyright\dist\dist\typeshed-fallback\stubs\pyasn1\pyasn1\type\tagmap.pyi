from collections.abc import Container, Mapping

from pyasn1.type.base import Asn1Type

__all__ = ["TagMap"]

class TagMap:
    def __init__(
        self,
        presentTypes: Mapping[TagMap, Asn1Type] | None = None,
        skipTypes: Container[TagMap] | None = None,
        defaultType: Asn1Type | None = None,
    ) -> None: ...
    def __contains__(self, tagSet) -> bool: ...
    def __getitem__(self, tagSet): ...
    def __iter__(self): ...
    @property
    def presentTypes(self): ...
    @property
    def skipTypes(self): ...
    @property
    def defaultType(self): ...
    def getPosMap(self): ...
    def getNegMap(self): ...
    def getDef(self): ...
