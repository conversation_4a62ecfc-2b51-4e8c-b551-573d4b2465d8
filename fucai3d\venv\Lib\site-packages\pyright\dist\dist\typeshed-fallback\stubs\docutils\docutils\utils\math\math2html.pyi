from _typeshed import Incomplete
from collections.abc import Generator
from typing import ClassVar, Final, TextIO, TypeVar

_T = TypeVar("_T")

__version__: Final[str]

class Trace:
    debugmode: ClassVar[bool]
    quietmode: ClassVar[bool]
    showlinesmode: ClassVar[bool]
    prefix: ClassVar[str | None]
    @classmethod
    def debug(cls, message: str) -> None: ...
    @classmethod
    def message(cls, message: str) -> None: ...
    @classmethod
    def error(cls, message: str) -> None: ...
    @classmethod
    def show(cls, message: str, channel: TextIO) -> None: ...

class ContainerConfig:
    extracttext: ClassVar[dict[str, list[str]]]

class EscapeConfig:
    chars: ClassVar[dict[str, str]]
    entities: ClassVar[dict[str, str]]

class FormulaConfig:
    alphacommands: ClassVar[dict[str, str]]
    array: ClassVar[dict[str, str]]
    bigbrackets: ClassVar[dict[str, list[str]]]
    bracketcommands: ClassVar[dict[str, str]]
    combiningfunctions: ClassVar[dict[str, str]]
    commands: ClassVar[dict[str, str]]
    cmddict: ClassVar[dict[str, str]]
    oversetfunctions: ClassVar[dict[str, str]]
    undersetfunctions: ClassVar[dict[str, str]]
    endings: ClassVar[dict[str, str]]
    environments: ClassVar[dict[str, list[str]]]
    fontfunctions: ClassVar[dict[str, str]]
    hybridfunctions: ClassVar[dict[str, list[str]]]
    hybridsizes: ClassVar[dict[str, str]]
    labelfunctions: ClassVar[dict[str, str]]
    limitcommands: ClassVar[dict[str, str]]
    modified: ClassVar[dict[str, str]]
    onefunctions: ClassVar[dict[str, str]]
    spacedcommands: ClassVar[dict[str, str]]
    starts: ClassVar[dict[str, str]]
    symbolfunctions: ClassVar[dict[str, str]]
    textfunctions: ClassVar[dict[str, str]]
    unmodified: ClassVar[dict[str, list[str]]]

class CommandLineParser:
    options: Incomplete
    def __init__(self, options) -> None: ...
    def parseoptions(self, args): ...
    def readoption(self, args): ...
    def readquoted(self, args, initial): ...
    def readequalskey(self, arg, args): ...

class Options:
    location: Incomplete
    debug: bool
    quiet: bool
    version: bool
    help: bool
    simplemath: bool
    showlines: bool
    branches: Incomplete
    def parseoptions(self, args) -> None: ...
    def processoptions(self) -> None: ...
    def usage(self) -> None: ...
    def showoptions(self) -> None: ...
    def showversion(self) -> None: ...

class Cloner:
    @classmethod
    def clone(cls, original: _T) -> _T: ...
    @classmethod
    def create(cls, type: type[_T]) -> _T: ...

class ContainerExtractor:
    allowed: Incomplete
    extracted: Incomplete
    def __init__(self, config) -> None: ...
    def extract(self, container): ...
    def process(self, container, list) -> None: ...
    def safeclone(self, container): ...

class Parser:
    begin: int
    parameters: Incomplete
    def __init__(self) -> None: ...
    def parseheader(self, reader): ...
    def parseparameter(self, reader) -> None: ...
    def parseending(self, reader, process) -> None: ...
    def parsecontainer(self, reader, contents) -> None: ...

class LoneCommand(Parser):
    def parse(self, reader): ...

class TextParser(Parser):
    stack: Incomplete
    ending: Incomplete
    endings: Incomplete
    def __init__(self, container) -> None: ...
    def parse(self, reader): ...
    def isending(self, reader): ...

class ExcludingParser(Parser):
    def parse(self, reader): ...

class BoundedParser(ExcludingParser):
    def parse(self, reader): ...

class BoundedDummy(Parser):
    def parse(self, reader): ...

class StringParser(Parser):
    begin: Incomplete
    def parseheader(self, reader): ...
    def parse(self, reader): ...

class ContainerOutput:
    def gethtml(self, container) -> None: ...
    def isempty(self): ...

class EmptyOutput(ContainerOutput):
    def gethtml(self, container): ...
    def isempty(self): ...

class FixedOutput(ContainerOutput):
    def gethtml(self, container): ...

class ContentsOutput(ContainerOutput):
    def gethtml(self, container): ...

class TaggedOutput(ContentsOutput):
    tag: Incomplete
    breaklines: bool
    empty: bool
    def settag(self, tag, breaklines: bool = False, empty: bool = False): ...
    def setbreaklines(self, breaklines): ...
    def gethtml(self, container): ...
    def open(self, container): ...
    def close(self, container): ...
    def selfclosing(self, container): ...
    def checktag(self, container): ...

class FilteredOutput(ContentsOutput):
    filters: Incomplete
    def __init__(self) -> None: ...
    def addfilter(self, original, replacement) -> None: ...
    def gethtml(self, container): ...
    def filter(self, line): ...

class StringOutput(ContainerOutput):
    def gethtml(self, container): ...

class Globable:
    leavepending: bool
    endinglist: Incomplete
    def __init__(self) -> None: ...
    def checkbytemark(self) -> None: ...
    def isout(self): ...
    def current(self): ...
    def checkfor(self, string): ...
    def finished(self): ...
    def skipcurrent(self): ...
    def glob(self, currentcheck): ...
    def globalpha(self): ...
    def globnumber(self): ...
    def isidentifier(self): ...
    def globidentifier(self): ...
    def isvalue(self): ...
    def globvalue(self): ...
    def skipspace(self): ...
    def globincluding(self, magicchar): ...
    def globexcluding(self, excluded): ...
    def pushending(self, ending, optional: bool = False) -> None: ...
    def popending(self, expected=None): ...
    def nextending(self): ...

class EndingList:
    endings: Incomplete
    def __init__(self) -> None: ...
    def add(self, ending, optional: bool = False) -> None: ...
    def pickpending(self, pos) -> None: ...
    def checkin(self, pos): ...
    def pop(self, pos): ...
    def findending(self, pos): ...
    def checkpending(self) -> None: ...

class PositionEnding:
    ending: Incomplete
    optional: Incomplete
    def __init__(self, ending, optional) -> None: ...
    def checkin(self, pos): ...

class Position(Globable):
    def __init__(self) -> None: ...
    def skip(self, string) -> None: ...
    def identifier(self): ...
    def extract(self, length) -> None: ...
    def checkfor(self, string): ...
    def checkforlower(self, string): ...
    def skipcurrent(self): ...
    def __next__(self): ...
    def checkskip(self, string): ...
    def error(self, message) -> None: ...

class TextPosition(Position):
    pos: int
    text: Incomplete
    def __init__(self, text) -> None: ...
    def skip(self, string) -> None: ...
    def identifier(self): ...
    def isout(self): ...
    def current(self): ...
    def extract(self, length): ...

class Container:
    partkey: Incomplete
    parent: Incomplete
    begin: Incomplete
    contents: Incomplete
    def __init__(self) -> None: ...
    def process(self) -> None: ...
    def gethtml(self): ...
    def escape(self, line, replacements={"&": "&amp;", "<": "&lt;", ">": "&gt;"}): ...
    def escapeentities(self, line): ...
    def searchall(self, type): ...
    def searchremove(self, type): ...
    def searchprocess(self, type, process): ...
    def locateprocess(self, locate, process) -> None: ...
    def recursivesearch(self, locate, recursive, process) -> None: ...
    def extracttext(self): ...
    def group(self, index, group, isingroup) -> None: ...
    def remove(self, index) -> None: ...
    def tree(self, level: int = 0) -> None: ...
    def getparameter(self, name): ...
    def getparameterlist(self, name): ...
    def hasemptyoutput(self): ...

class BlackBox(Container):
    parser: Incomplete
    output: Incomplete
    contents: Incomplete
    def __init__(self) -> None: ...

class StringContainer(Container):
    parsed: Incomplete
    parser: Incomplete
    output: Incomplete
    string: str
    def __init__(self) -> None: ...
    def process(self) -> None: ...
    def replacespecial(self, line): ...
    def changeline(self, line): ...
    def extracttext(self): ...

class Constant(StringContainer):
    contents: Incomplete
    string: Incomplete
    output: Incomplete
    def __init__(self, text) -> None: ...

class DocumentParameters:
    displaymode: bool

class FormulaParser(Parser):
    begin: Incomplete
    def parseheader(self, reader): ...
    def parsetype(self, reader): ...
    def parse(self, reader): ...
    def parseformula(self, reader): ...
    def parsesingleliner(self, reader, start, ending): ...
    def parsemultiliner(self, reader, start, ending): ...

class FormulaBit(Container):
    type: str | None
    size: int
    original: str
    contents: Incomplete
    output: Incomplete
    def __init__(self) -> None: ...
    factory: Incomplete
    def setfactory(self, factory): ...
    def add(self, bit) -> None: ...
    def skiporiginal(self, string, pos) -> None: ...
    def computesize(self): ...
    def clone(self): ...

class TaggedBit(FormulaBit):
    output: Incomplete
    def constant(self, constant, tag): ...
    contents: Incomplete
    def complete(self, contents, tag, breaklines: bool = False): ...
    def selfcomplete(self, tag): ...

class FormulaConstant(Constant):
    original: Incomplete
    size: int
    type: str | None
    def __init__(self, string) -> None: ...
    def computesize(self): ...
    def clone(self): ...

class RawText(FormulaBit):
    def detect(self, pos): ...
    def parsebit(self, pos) -> None: ...

class FormulaSymbol(FormulaBit):
    modified: Incomplete
    unmodified: Incomplete
    def detect(self, pos): ...
    def parsebit(self, pos) -> None: ...
    def addsymbol(self, symbol, pos) -> None: ...

class FormulaNumber(FormulaBit):
    def detect(self, pos): ...
    def parsebit(self, pos): ...

class Comment(FormulaBit):
    start: Incomplete
    def detect(self, pos): ...
    def parsebit(self, pos) -> None: ...

class WhiteSpace(FormulaBit):
    def detect(self, pos): ...
    def parsebit(self, pos) -> None: ...

class Bracket(FormulaBit):
    start: Incomplete
    ending: Incomplete
    inner: Incomplete
    def __init__(self) -> None: ...
    def detect(self, pos): ...
    def parsebit(self, pos): ...
    def parsetext(self, pos): ...
    def parseliteral(self, pos): ...
    def parsecomplete(self, pos, innerparser) -> None: ...
    def innerformula(self, pos) -> None: ...
    def innertext(self, pos) -> None: ...
    literal: str
    def innerliteral(self, pos) -> None: ...

class SquareBracket(Bracket):
    start: Incomplete
    ending: Incomplete
    def clone(self): ...

class MathsProcessor:
    def process(self, contents, index) -> None: ...

class FormulaProcessor:
    processors: Incomplete
    def process(self, bit) -> None: ...
    def processcontents(self, bit) -> None: ...
    def processinsides(self, bit) -> None: ...
    def traversewhole(self, formula) -> None: ...
    def traverse(self, bit) -> Generator[Incomplete, Incomplete]: ...
    def italicize(self, bit, contents) -> None: ...

class Formula(Container):
    parser: Incomplete
    output: Incomplete
    def __init__(self) -> None: ...
    def process(self) -> None: ...
    contents: Incomplete
    def classic(self) -> None: ...
    def parse(self, pos): ...
    header: Incomplete
    def parsedollarinline(self, pos) -> None: ...
    def parsedollarblock(self, pos) -> None: ...
    parsed: Incomplete
    def parsedollar(self, pos) -> None: ...
    def parseinlineto(self, pos, limit) -> None: ...
    def parseblockto(self, pos, limit) -> None: ...
    def parseupto(self, pos, limit): ...

class WholeFormula(FormulaBit):
    def detect(self, pos): ...
    def parsebit(self, pos) -> None: ...

class FormulaFactory:
    types: Incomplete
    skippedtypes: Incomplete
    defining: bool
    instances: Incomplete
    def __init__(self) -> None: ...
    def detecttype(self, type, pos): ...
    def instance(self, type): ...
    def create(self, type): ...
    def clearskipped(self, pos) -> None: ...
    def skipany(self, pos): ...
    def parseany(self, pos): ...
    def parsetype(self, type, pos): ...
    def parseformula(self, formula): ...

class FormulaCommand(FormulaBit):
    types: Incomplete
    start: Incomplete
    commandmap: ClassVar[dict[str, str] | dict[str, list[str]] | None]
    def detect(self, pos): ...
    output: Incomplete
    def parsebit(self, pos): ...
    def parsewithcommand(self, command, pos): ...
    def parsecommandtype(self, command, type, pos): ...
    def extractcommand(self, pos): ...
    def emptycommand(self, pos): ...
    def parseupgreek(self, command, pos): ...

class CommandBit(FormulaCommand):
    command: Incomplete
    translated: Incomplete
    def setcommand(self, command) -> None: ...
    def parseparameter(self, pos): ...
    def parsesquare(self, pos): ...
    def parseliteral(self, pos): ...
    def parsesquareliteral(self, pos): ...
    def parsetext(self, pos): ...

class EmptyCommand(CommandBit):
    commandmap: ClassVar[dict[str, str]]
    contents: Incomplete
    def parsebit(self, pos) -> None: ...

class SpacedCommand(CommandBit):
    commandmap: ClassVar[dict[str, str]]
    contents: Incomplete
    def parsebit(self, pos) -> None: ...

class AlphaCommand(EmptyCommand):
    commandmap: ClassVar[dict[str, str]]
    greek_capitals: Incomplete
    def parsebit(self, pos) -> None: ...

class OneParamFunction(CommandBit):
    commandmap: ClassVar[dict[str, str]]
    simplified: bool
    output: Incomplete
    def parsebit(self, pos) -> None: ...
    html: Incomplete
    def simplifyifpossible(self) -> None: ...

class SymbolFunction(CommandBit):
    commandmap: ClassVar[dict[str, str]]
    def detect(self, pos): ...
    output: Incomplete
    def parsebit(self, pos) -> None: ...

class TextFunction(CommandBit):
    commandmap: ClassVar[dict[str, str]]
    output: Incomplete
    def parsebit(self, pos) -> None: ...
    def process(self) -> None: ...

class FontFunction(OneParamFunction):
    commandmap: ClassVar[dict[str, str]]
    def process(self) -> None: ...

class BigBracket:
    size: Incomplete
    original: Incomplete
    alignment: Incomplete
    pieces: Incomplete
    def __init__(self, size, bracket, alignment: str = "l") -> None: ...
    def getpiece(self, index): ...
    def getpiece1(self, index): ...
    def getpiece3(self, index): ...
    def getpiece4(self, index): ...
    def getcell(self, index): ...
    def getcontents(self): ...
    def getsinglebracket(self): ...

class FormulaEquation(CommandBit):
    piece: str
    output: Incomplete
    def parsebit(self, pos) -> None: ...

class FormulaCell(FormulaCommand):
    alignment: Incomplete
    output: Incomplete
    def setalignment(self, alignment): ...
    def parsebit(self, pos) -> None: ...

class FormulaRow(FormulaCommand):
    cellseparator: Incomplete
    alignments: Incomplete
    output: Incomplete
    def setalignments(self, alignments): ...
    def parsebit(self, pos) -> None: ...
    def createcell(self, index): ...

class MultiRowFormula(CommandBit):
    rows: Incomplete
    size: Incomplete
    def parserows(self, pos) -> None: ...
    def iteraterows(self, pos) -> Generator[Incomplete]: ...
    def addempty(self) -> None: ...
    def addrow(self, row) -> None: ...

class FormulaArray(MultiRowFormula):
    piece: str
    output: Incomplete
    def parsebit(self, pos) -> None: ...
    valign: str
    alignments: Incomplete
    def parsealignments(self, pos) -> None: ...

class FormulaMatrix(MultiRowFormula):
    piece: str
    output: Incomplete
    valign: str
    alignments: Incomplete
    def parsebit(self, pos) -> None: ...

class FormulaCases(MultiRowFormula):
    piece: str
    output: Incomplete
    alignments: Incomplete
    contents: Incomplete
    def parsebit(self, pos) -> None: ...

class EquationEnvironment(MultiRowFormula):
    output: Incomplete
    alignments: Incomplete
    def parsebit(self, pos) -> None: ...

class BeginCommand(CommandBit):
    commandmap: ClassVar[dict[str, str]]
    types: Incomplete
    size: Incomplete
    def parsebit(self, pos) -> None: ...
    def findbit(self, piece): ...

class CombiningFunction(OneParamFunction):
    commandmap: ClassVar[dict[str, str]]
    def parsebit(self, pos) -> None: ...
    def parsesingleparameter(self, pos): ...

class OversetFunction(OneParamFunction):
    commandmap: ClassVar[dict[str, str]]
    symbol: Incomplete
    parameter: Incomplete
    output: Incomplete
    def parsebit(self, pos) -> None: ...

class UndersetFunction(OneParamFunction):
    commandmap: ClassVar[dict[str, str]]
    symbol: Incomplete
    parameter: Incomplete
    output: Incomplete
    def parsebit(self, pos) -> None: ...

class LimitCommand(EmptyCommand):
    commandmap: ClassVar[dict[str, str]]
    output: Incomplete
    def parsebit(self, pos) -> None: ...

class LimitPreviousCommand(LimitCommand):
    commandmap: ClassVar[None]  # type: ignore[assignment]
    output: Incomplete
    def parsebit(self, pos) -> None: ...

class LimitsProcessor(MathsProcessor):
    def process(self, contents, index) -> None: ...
    def checklimits(self, contents, index): ...
    def limitsahead(self, contents, index) -> None: ...
    def modifylimits(self, contents, index) -> None: ...
    def getlimit(self, contents, index): ...
    def modifyscripts(self, contents, index) -> None: ...
    def checkscript(self, contents, index): ...
    def checkcommand(self, contents, index, type): ...
    def getscript(self, contents, index): ...

class BracketCommand(OneParamFunction):
    commandmap: ClassVar[dict[str, str]]
    def parsebit(self, pos) -> None: ...
    original: Incomplete
    command: Incomplete
    contents: Incomplete
    def create(self, direction, character): ...

class BracketProcessor(MathsProcessor):
    def process(self, contents, index): ...
    def processleft(self, contents, index) -> None: ...
    def checkleft(self, contents, index): ...
    def checkright(self, contents, index): ...
    def checkdirection(self, bit, command): ...
    def findright(self, contents, index): ...
    def findmax(self, contents, leftindex, rightindex): ...
    def resize(self, command, size) -> None: ...

class ParameterDefinition:
    parambrackets: ClassVar[list[tuple[str, str]]]
    name: str
    literal: bool
    optional: bool
    value: Incomplete
    literalvalue: Incomplete
    def __init__(self) -> None: ...
    def parse(self, pos): ...
    def read(self, pos, function) -> None: ...

class ParameterFunction(CommandBit):
    params: Incomplete
    def readparams(self, readtemplate, pos) -> None: ...
    def paramdefs(self, readtemplate) -> Generator[Incomplete]: ...
    def getparam(self, name): ...
    def getvalue(self, name): ...
    def getliteralvalue(self, name): ...

class HybridFunction(ParameterFunction):
    commandmap: ClassVar[dict[str, list[str]]]
    contents: Incomplete
    def parsebit(self, pos) -> None: ...
    def writeparams(self, writetemplate): ...
    def writepos(self, pos): ...
    def writeparam(self, pos): ...
    def writefunction(self, pos): ...
    def readtag(self, pos): ...
    def writebracket(self, direction, character): ...
    size: Incomplete
    def computehybridsize(self) -> None: ...

class HybridSize:
    configsizes: ClassVar[dict[str, str]]
    def getsize(self, function): ...

def math2html(formula): ...
def main() -> None: ...
