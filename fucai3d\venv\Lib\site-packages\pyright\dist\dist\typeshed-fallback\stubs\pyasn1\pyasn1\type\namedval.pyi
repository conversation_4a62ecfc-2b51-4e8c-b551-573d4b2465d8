from _typeshed import Incomplete
from collections.abc import Generator

__all__ = ["NamedValues"]

class NamedValues:
    def __init__(self, *args, **kwargs) -> None: ...
    def __eq__(self, other): ...
    def __ne__(self, other): ...
    def __lt__(self, other): ...
    def __le__(self, other): ...
    def __gt__(self, other): ...
    def __ge__(self, other): ...
    def __hash__(self): ...
    def __getitem__(self, key): ...
    def __len__(self) -> int: ...
    def __contains__(self, key) -> bool: ...
    def __iter__(self): ...
    def values(self): ...
    def keys(self): ...
    def items(self) -> Generator[Incomplete, None, None]: ...
    def __add__(self, namedValues): ...
    def clone(self, *args, **kwargs): ...
    def getName(self, value): ...
    def getValue(self, name): ...
    def getValues(self, *names): ...
