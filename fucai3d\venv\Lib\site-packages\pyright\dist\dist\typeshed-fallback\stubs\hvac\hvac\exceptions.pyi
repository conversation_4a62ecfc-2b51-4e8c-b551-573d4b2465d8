from collections.abc import Iterable
from typing_extensions import Self

class VaultError(Exception):
    errors: Iterable[str] | str | None
    method: str | None
    url: str | None
    text: str | None
    json: object
    def __init__(
        self,
        message: str | None = None,
        errors: Iterable[str] | str | None = None,
        method: str | None = None,
        url: str | None = None,
        text: str | None = None,
        json: object | None = None,
    ) -> None: ...
    @classmethod
    def from_status(
        cls,
        status_code: int,
        message: str | None = ...,
        errors: Iterable[str] | str | None = ...,
        method: str | None = ...,
        url: str | None = ...,
        text: str | None = ...,
        json: object | None = ...,
    ) -> Self: ...

class InvalidRequest(VaultError): ...
class Unauthorized(VaultError): ...
class Forbidden(VaultError): ...
class InvalidPath(VaultError): ...
class UnsupportedOperation(VaultError): ...
class PreconditionFailed(VaultError): ...
class RateLimitExceeded(VaultError): ...
class InternalServerError(VaultError): ...
class VaultNotInitialized(VaultError): ...
class VaultDown(VaultError): ...
class UnexpectedError(VaultError): ...
class BadGateway(VaultError): ...
class ParamValidationError(VaultError): ...
