#!/usr/bin/env python3
"""
P5-个位预测器预测脚本

执行个位数字预测，支持单期和批量预测。

功能：
- 单期预测和批量预测
- 多模型支持
- 预测结果保存
- 历史预测查询
- 性能统计分析

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import argparse
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.predictors.units_predictor import UnitsPredictor
    from config.config_loader import setup_logging
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='P5-个位预测器预测脚本')
    
    parser.add_argument(
        '--issue', '-i',
        type=str,
        help='要预测的期号 (例如: 2025206)'
    )
    
    parser.add_argument(
        '--issues', '-I',
        type=str,
        nargs='+',
        help='要预测的期号列表 (例如: 2025206 2025207 2025208)'
    )
    
    parser.add_argument(
        '--model', '-m',
        type=str,
        choices=['xgb', 'lgb', 'lstm', 'ensemble'],
        default='ensemble',
        help='使用的模型类型 (默认: ensemble)'
    )
    
    parser.add_argument(
        '--db-path', '-d',
        type=str,
        default='data/lottery.db',
        help='数据库文件路径 (默认: data/lottery.db)'
    )
    
    parser.add_argument(
        '--history', '-H',
        type=int,
        help='查看预测历史 (指定显示的记录数)'
    )
    
    parser.add_argument(
        '--stats', '-s',
        action='store_true',
        help='显示模型准确率统计'
    )
    
    parser.add_argument(
        '--all-models', '-a',
        action='store_true',
        help='使用所有模型进行预测对比'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    return parser.parse_args()

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🎯 P5-个位预测器预测系统")
    print("基于独立位置预测理念 | 专注个位数字预测")
    print("=" * 60)

def predict_single_issue(predictor: UnitsPredictor, issue: str, 
                        model_type: str, verbose: bool = False) -> dict:
    """预测单期"""
    try:
        result = predictor.predict(issue, model_type)
        
        print(f"\n🎯 期号 {issue} 个位预测结果 ({model_type.upper()}):")
        print(f"   预测数字: {result['predicted_digit']}")
        print(f"   置信度: {result['confidence']:.4f}")
        
        # 显示Top3预测
        if 'top3_predictions' in result:
            print(f"   Top3预测:")
            for i, (digit, prob) in enumerate(result['top3_predictions'], 1):
                print(f"     {i}. 数字{digit}: {prob:.4f}")
        
        # 显示概率分布
        if verbose and 'probabilities' in result:
            print(f"   概率分布:")
            for digit, prob in enumerate(result['probabilities']):
                print(f"     数字{digit}: {prob:.4f}")
        
        return result
        
    except Exception as e:
        print(f"❌ 期号 {issue} 预测失败: {e}")
        return {'error': str(e)}

def predict_batch_issues(predictor: UnitsPredictor, issues: list, 
                        model_type: str, verbose: bool = False) -> list:
    """批量预测"""
    print(f"\n🎯 批量预测 {len(issues)} 期 ({model_type.upper()}):")
    
    try:
        results = predictor.predict_batch(issues, model_type)
        
        # 显示结果
        for result in results:
            if 'error' not in result:
                print(f"   {result['issue']}: 数字{result['predicted_digit']} (置信度: {result['confidence']:.4f})")
            else:
                print(f"   {result['issue']}: 预测失败 - {result['error']}")
        
        return results
        
    except Exception as e:
        print(f"❌ 批量预测失败: {e}")
        return []

def predict_all_models(predictor: UnitsPredictor, issue: str, 
                      verbose: bool = False):
    """使用所有模型预测对比"""
    print(f"\n🎯 期号 {issue} 多模型预测对比:")
    
    models = ['xgb', 'lgb', 'lstm', 'ensemble']
    results = {}
    
    for model_type in models:
        try:
            result = predictor.predict(issue, model_type)
            results[model_type] = result
            
            print(f"   {model_type.upper()}: 数字{result['predicted_digit']} (置信度: {result['confidence']:.4f})")
            
        except Exception as e:
            print(f"   {model_type.upper()}: 预测失败 - {e}")
            results[model_type] = {'error': str(e)}
    
    # 分析一致性
    predictions = [r['predicted_digit'] for r in results.values() if 'predicted_digit' in r]
    if predictions:
        from collections import Counter
        counter = Counter(predictions)
        most_common = counter.most_common(1)[0]
        
        print(f"\n📊 预测分析:")
        print(f"   最频繁预测: 数字{most_common[0]} ({most_common[1]}/{len(predictions)} 个模型)")
        
        if len(set(predictions)) == 1:
            print(f"   ✅ 所有模型预测一致")
        else:
            print(f"   ⚠️ 模型预测存在分歧")

def show_prediction_history(predictor: UnitsPredictor, limit: int, 
                           model_type: str = None):
    """显示预测历史"""
    print(f"\n📚 预测历史 (最近{limit}期):")
    
    try:
        history = predictor.get_prediction_history(model_type, limit)
        
        if not history:
            print("   暂无预测历史")
            return
        
        print(f"   {'期号':<12} {'模型':<10} {'预测':<6} {'置信度':<8} {'时间'}")
        print("   " + "-" * 50)
        
        for record in history:
            issue = record['issue']
            model = record['model_type']
            digit = record['predicted_digit']
            confidence = record['confidence']
            created_at = record['created_at'][:16]  # 只显示日期和时间
            
            print(f"   {issue:<12} {model:<10} {digit:<6} {confidence:<8.4f} {created_at}")
            
    except Exception as e:
        print(f"❌ 获取预测历史失败: {e}")

def show_accuracy_statistics(predictor: UnitsPredictor, model_type: str = None):
    """显示准确率统计"""
    print(f"\n📊 准确率统计:")
    
    models = ['xgb', 'lgb', 'lstm', 'ensemble'] if model_type is None else [model_type]
    
    for model in models:
        try:
            stats = predictor.get_accuracy_statistics(model, recent_periods=100)
            
            if stats['total_predictions'] > 0:
                print(f"   {model.upper()}:")
                print(f"     准确率: {stats['accuracy']:.4f}")
                print(f"     平均置信度: {stats['avg_confidence']:.4f}")
                print(f"     预测总数: {stats['total_predictions']}")
                print(f"     正确预测: {stats['correct_predictions']}")
            else:
                print(f"   {model.upper()}: 暂无预测数据")
                
        except Exception as e:
            print(f"   {model.upper()}: 统计失败 - {e}")

def show_model_status(predictor: UnitsPredictor):
    """显示模型状态"""
    print(f"\n🔧 模型状态:")
    
    try:
        status = predictor.get_model_status()
        
        for model_name, info in status.items():
            if 'error' not in info:
                trained = "✅ 已训练" if info.get('is_trained', False) else "❌ 未训练"
                feature_count = info.get('feature_count', 0)
                print(f"   {model_name.upper()}: {trained} (特征数: {feature_count})")
            else:
                print(f"   {model_name.upper()}: ❌ 错误 - {info['error']}")
                
    except Exception as e:
        print(f"❌ 获取模型状态失败: {e}")

def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()
    
    # 设置日志
    try:
        setup_logging()
    except:
        pass
    
    # 打印横幅
    print_banner()
    
    # 验证数据库文件
    if not os.path.exists(args.db_path):
        print(f"❌ 数据库文件不存在: {args.db_path}")
        sys.exit(1)
    
    print(f"📂 数据库路径: {args.db_path}")
    print(f"🎯 使用模型: {args.model}")
    
    try:
        # 初始化预测器
        print(f"\n🔧 初始化个位预测器...")
        predictor = UnitsPredictor(args.db_path)
        print(f"✅ 预测器初始化完成")
        
        # 显示模型状态
        show_model_status(predictor)
        
        # 执行相应操作
        if args.history:
            # 显示预测历史
            show_prediction_history(predictor, args.history, args.model if args.model != 'ensemble' else None)
            
        elif args.stats:
            # 显示统计信息
            show_accuracy_statistics(predictor, args.model if args.model != 'ensemble' else None)
            
        elif args.issue:
            # 单期预测
            if args.all_models:
                predict_all_models(predictor, args.issue, args.verbose)
            else:
                predict_single_issue(predictor, args.issue, args.model, args.verbose)
                
        elif args.issues:
            # 批量预测
            predict_batch_issues(predictor, args.issues, args.model, args.verbose)
            
        else:
            # 显示帮助信息
            print(f"\n💡 使用提示:")
            print(f"   预测单期: python predict_units.py -i 2025206")
            print(f"   批量预测: python predict_units.py -I 2025206 2025207 2025208")
            print(f"   查看历史: python predict_units.py --history 10")
            print(f"   查看统计: python predict_units.py --stats")
            print(f"   多模型对比: python predict_units.py -i 2025206 --all-models")
        
        print(f"\n" + "=" * 60)
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 操作过程发生错误: {e}")
        import traceback
        if args.verbose:
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
