from _typeshed import Incomplete

class FrameworkIntegration:
    expires_in: int
    name: Incomplete
    cache: Incomplete
    def __init__(self, name, cache=None) -> None: ...
    def get_state_data(self, session, state): ...
    def set_state_data(self, session, state, data): ...
    def clear_state_data(self, session, state): ...
    def update_token(self, token, refresh_token=None, access_token=None) -> None: ...
    @staticmethod
    def load_config(oauth, name, params) -> None: ...
