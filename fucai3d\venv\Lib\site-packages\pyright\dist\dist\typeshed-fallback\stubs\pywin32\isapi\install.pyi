from _typeshed import Incomplete, StrOrBytesPath, StrPath, SupportsGetItem, Unused
from collections.abc import Callable, Iterable, Mapping
from optparse import OptionParser
from typing import Final, Literal

this_dir: str

class FilterParameters:
    Name: Incomplete
    Description: Incomplete
    Path: Incomplete
    Server: Incomplete
    AddExtensionFile: bool
    AddExtensionFile_Enabled: bool
    AddExtensionFile_GroupID: Incomplete
    AddExtensionFile_CanDelete: bool
    AddExtensionFile_Description: Incomplete
    def __init__(self, **kw) -> None: ...

class VirtualDirParameters:
    Name: Incomplete
    Description: Incomplete
    AppProtection: Incomplete
    Headers: Incomplete
    Path: Incomplete
    Type: Incomplete
    AccessExecute: Incomplete
    AccessRead: Incomplete
    AccessWrite: Incomplete
    AccessScript: Incomplete
    ContentIndexed: Incomplete
    EnableDirBrowsing: Incomplete
    EnableDefaultDoc: Incomplete
    DefaultDoc: Incomplete
    ScriptMaps: list[ScriptMapParams]
    ScriptMapUpdate: str
    Server: Incomplete
    def __init__(self, **kw) -> None: ...
    def is_root(self) -> bool: ...
    def split_path(self) -> list[str]: ...

class ScriptMapParams:
    Extension: Incomplete
    Module: Incomplete
    Flags: int
    Verbs: str
    AddExtensionFile: bool
    AddExtensionFile_Enabled: bool
    AddExtensionFile_GroupID: Incomplete
    AddExtensionFile_CanDelete: bool
    AddExtensionFile_Description: Incomplete
    def __init__(self, **kw) -> None: ...

class ISAPIParameters:
    ServerName: Incomplete
    Filters: list[FilterParameters]
    VirtualDirs: list[VirtualDirParameters]
    def __init__(self, **kw) -> None: ...

verbose: int

def log(level: int, what: object) -> None: ...

class InstallationError(Exception): ...
class ItemNotFound(InstallationError): ...
class ConfigurationError(InstallationError): ...

def FindPath(options, server: str | bytes | bytearray, name: str) -> str: ...
def LocateWebServerPath(description: str): ...
def GetWebServer(description: str | None = None): ...
def LoadWebServer(path): ...
def FindWebServer(options, server_desc: str | bytes | bytearray | None) -> str: ...
def split_path(path: str) -> list[str]: ...
def CreateDirectory(params, options): ...
def AssignScriptMaps(script_maps: Iterable[ScriptMapParams], target, update: str = "replace") -> None: ...
def get_unique_items(sequence, reference): ...
def CreateISAPIFilter(filterParams, options): ...
def DeleteISAPIFilter(filterParams, options) -> None: ...
def AddExtensionFiles(params, options) -> None: ...
def DeleteExtensionFileRecords(params, options) -> None: ...
def CheckLoaderModule(dll_name: StrOrBytesPath) -> None: ...
def Install(params, options) -> None: ...
def RemoveDirectory(params, options) -> None: ...
def RemoveScriptMaps(vd_params, options) -> None: ...
def Uninstall(params, options) -> None: ...
def GetLoaderModuleName(mod_name: StrPath, check_module: bool | None = None) -> str: ...
def InstallModule(conf_module_name: StrPath, params, options, log: Callable[[int, str], Unused] = ...) -> None: ...
def UninstallModule(conf_module_name: StrPath, params, options, log: Callable[[int, str], Unused] = ...) -> None: ...

standard_arguments: Final[dict[Literal["install", "remove"], Callable[..., Incomplete]]]

def build_usage(handler_map: Mapping[str, object]) -> str: ...
def MergeStandardOptions(options, params) -> None: ...
def HandleCommandLine(
    params,
    argv: SupportsGetItem[int, str] | None = None,
    conf_module_name: str | None = None,
    default_arg: str = "install",
    opt_parser: OptionParser | None = None,
    custom_arg_handlers: Mapping[str, object] = {},
) -> None: ...
