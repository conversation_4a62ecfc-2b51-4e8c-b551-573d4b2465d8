from _typeshed import <PERSON>ric<PERSON><PERSON>, StrPath, Unused
from collections.abc import Callable, Iterable, Mapping
from typing import AnyStr, Literal
from typing_extensions import TypeVarTuple, Unpack

_Ts = TypeVarTuple("_Ts")

def get_host_platform() -> str: ...
def get_platform() -> str: ...
def get_macosx_target_ver_from_syscfg(): ...
def get_macosx_target_ver(): ...
def split_version(s: str) -> list[int]: ...
def convert_path(pathname: StrPath) -> str: ...
def change_root(new_root: GenericPath[AnyStr], pathname: GenericPath[AnyStr]) -> AnyStr: ...
def check_environ() -> None: ...
def subst_vars(s: str, local_vars: Mapping[str, object]) -> str: ...
def grok_environment_error(exc: object, prefix: str = "error: ") -> str: ...
def split_quoted(s: str) -> list[str]: ...
def execute(
    func: Callable[[Unpack[_Ts]], Unused],
    args: tuple[Unpack[_Ts]],
    msg: str | None = None,
    verbose: bool = False,
    dry_run: bool = False,
) -> None: ...
def strtobool(val: str) -> Literal[0, 1]: ...
def byte_compile(
    py_files: Iterable[str],
    optimize: int = 0,
    force: bool = False,
    prefix: str | None = None,
    base_dir: str | None = None,
    verbose: bool = True,
    dry_run: bool = False,
    direct: bool | None = None,
) -> None: ...
def rfc822_escape(header: str) -> str: ...
def is_mingw() -> bool: ...
