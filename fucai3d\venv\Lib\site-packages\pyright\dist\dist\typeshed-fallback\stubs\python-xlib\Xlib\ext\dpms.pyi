from _typeshed import Unused
from typing import Final, Literal

from Xlib.display import Display
from Xlib.protocol import rq
from Xlib.xobject import resource

extname: Final = "DPMS"
DPMSModeOn: Final = 0
DPMSModeStandby: Final = 1
DPMSModeSuspend: Final = 2
DPMSModeOff: Final = 3
DPMSPowerLevel: Final[tuple[Literal[0], Literal[1], Literal[2], Literal[3]]]

class DPMSGetVersion(rq.ReplyRequest): ...

def get_version(self: Display | resource.Resource) -> DPMSGetVersion: ...

class DPMSCapable(rq.ReplyRequest): ...

def capable(self: Display | resource.Resource) -> DPMSCapable: ...

class DPMSGetTimeouts(rq.ReplyRequest): ...

def get_timeouts(self: Display | resource.Resource) -> DPMSGetTimeouts: ...

class DPMSSetTimeouts(rq.Request): ...

def set_timeouts(
    self: Display | resource.Resource, standby_timeout: int, suspend_timeout: int, off_timeout: int
) -> DPMSSetTimeouts: ...

class DPMSEnable(rq.Request): ...

def enable(self: Display | resource.Resource) -> DPMSEnable: ...

class DPMSDisable(rq.Request): ...

def disable(self: Display | resource.Resource) -> DPMSDisable: ...

class DPMSForceLevel(rq.Request): ...

def force_level(self: Display | resource.Resource, power_level: int) -> DPMSForceLevel: ...

class DPMSInfo(rq.ReplyRequest): ...

def info(self: Display | resource.Resource) -> DPMSInfo: ...
def init(disp: Display, _info: Unused) -> None: ...
