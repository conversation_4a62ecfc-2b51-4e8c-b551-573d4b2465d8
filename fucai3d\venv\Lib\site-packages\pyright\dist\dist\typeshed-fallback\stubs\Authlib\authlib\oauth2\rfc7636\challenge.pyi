import re
from _typeshed import Incomplete
from collections.abc import Callable
from typing import Final

CODE_VERIFIER_PATTERN: Final[re.Pattern[str]]
CODE_CHALLENGE_PATTERN: Final[re.Pattern[str]]

def create_s256_code_challenge(code_verifier): ...
def compare_plain_code_challenge(code_verifier, code_challenge): ...
def compare_s256_code_challenge(code_verifier, code_challenge): ...

class CodeChallenge:
    DEFAULT_CODE_CHALLENGE_METHOD: str
    SUPPORTED_CODE_CHALLENGE_METHOD: list[str]
    CODE_CHALLENGE_METHODS: dict[str, Callable[[Incomplete, Incomplete], Incomplete]]
    required: bool
    def __init__(self, required: bool = True) -> None: ...
    def __call__(self, grant) -> None: ...
    def validate_code_challenge(self, grant, redirect_uri) -> None: ...
    def validate_code_verifier(self, grant, result) -> None: ...
    def get_authorization_code_challenge(self, authorization_code): ...
    def get_authorization_code_challenge_method(self, authorization_code): ...
