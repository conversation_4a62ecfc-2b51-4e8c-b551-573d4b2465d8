from typing import Final

XK_emspace: Final = 0xAA1
XK_enspace: Final = 0xAA2
XK_em3space: Final = 0xAA3
XK_em4space: Final = 0xAA4
XK_digitspace: Final = 0xAA5
XK_punctspace: Final = 0xAA6
XK_thinspace: Final = 0xAA7
XK_hairspace: Final = 0xAA8
XK_emdash: Final = 0xAA9
XK_endash: Final = 0xAAA
XK_signifblank: Final = 0xAAC
XK_ellipsis: Final = 0xAAE
XK_doubbaselinedot: Final = 0xAAF
XK_onethird: Final = 0xAB0
XK_twothirds: Final = 0xAB1
XK_onefifth: Final = 0xAB2
XK_twofifths: Final = 0xAB3
XK_threefifths: Final = 0xAB4
XK_fourfifths: Final = 0xAB5
XK_onesixth: Final = 0xAB6
XK_fivesixths: Final = 0xAB7
XK_careof: Final = 0xAB8
XK_figdash: Final = 0xABB
XK_leftanglebracket: Final = 0xABC
XK_decimalpoint: Final = 0xABD
XK_rightanglebracket: Final = 0xABE
XK_marker: Final = 0xABF
XK_oneeighth: Final = 0xAC3
XK_threeeighths: Final = 0xAC4
XK_fiveeighths: Final = 0xAC5
XK_seveneighths: Final = 0xAC6
XK_trademark: Final = 0xAC9
XK_signaturemark: Final = 0xACA
XK_trademarkincircle: Final = 0xACB
XK_leftopentriangle: Final = 0xACC
XK_rightopentriangle: Final = 0xACD
XK_emopencircle: Final = 0xACE
XK_emopenrectangle: Final = 0xACF
XK_leftsinglequotemark: Final = 0xAD0
XK_rightsinglequotemark: Final = 0xAD1
XK_leftdoublequotemark: Final = 0xAD2
XK_rightdoublequotemark: Final = 0xAD3
XK_prescription: Final = 0xAD4
XK_minutes: Final = 0xAD6
XK_seconds: Final = 0xAD7
XK_latincross: Final = 0xAD9
XK_hexagram: Final = 0xADA
XK_filledrectbullet: Final = 0xADB
XK_filledlefttribullet: Final = 0xADC
XK_filledrighttribullet: Final = 0xADD
XK_emfilledcircle: Final = 0xADE
XK_emfilledrect: Final = 0xADF
XK_enopencircbullet: Final = 0xAE0
XK_enopensquarebullet: Final = 0xAE1
XK_openrectbullet: Final = 0xAE2
XK_opentribulletup: Final = 0xAE3
XK_opentribulletdown: Final = 0xAE4
XK_openstar: Final = 0xAE5
XK_enfilledcircbullet: Final = 0xAE6
XK_enfilledsqbullet: Final = 0xAE7
XK_filledtribulletup: Final = 0xAE8
XK_filledtribulletdown: Final = 0xAE9
XK_leftpointer: Final = 0xAEA
XK_rightpointer: Final = 0xAEB
XK_club: Final = 0xAEC
XK_diamond: Final = 0xAED
XK_heart: Final = 0xAEE
XK_maltesecross: Final = 0xAF0
XK_dagger: Final = 0xAF1
XK_doubledagger: Final = 0xAF2
XK_checkmark: Final = 0xAF3
XK_ballotcross: Final = 0xAF4
XK_musicalsharp: Final = 0xAF5
XK_musicalflat: Final = 0xAF6
XK_malesymbol: Final = 0xAF7
XK_femalesymbol: Final = 0xAF8
XK_telephone: Final = 0xAF9
XK_telephonerecorder: Final = 0xAFA
XK_phonographcopyright: Final = 0xAFB
XK_caret: Final = 0xAFC
XK_singlelowquotemark: Final = 0xAFD
XK_doublelowquotemark: Final = 0xAFE
XK_cursor: Final = 0xAFF
