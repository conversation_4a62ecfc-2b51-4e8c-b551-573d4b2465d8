# Many definitions are not included in http.client.__all__
from http.client import *
from http.client import (
    ACCEPTED as ACCEPTED,
    BAD_GATEWAY as BAD_GATEWAY,
    BAD_REQUEST as BAD_REQUEST,
    CONFLICT as CONFLICT,
    CONTINUE as CONTINUE,
    CREATED as CREATED,
    EXPECTATION_FAILED as EXPECTATION_FAILED,
    FAILED_DEPENDENCY as FAILED_DEPENDENCY,
    FOR<PERSON>DDE<PERSON> as FORBIDDEN,
    FOUND as FOUND,
    GATEWAY_TIMEOUT as GATEWAY_TIMEOUT,
    GONE as GONE,
    HTTP_PORT as HTTP_PORT,
    HTTP_VERSION_NOT_SUPPORTED as HTTP_VERSION_NOT_SUPPORTED,
    HTTPS_PORT as HTTPS_PORT,
    IM_USED as IM_USED,
    INSUFFICIENT_STORAGE as INSUFFICIENT_STORAGE,
    INTERNAL_SERVER_ERROR as INTERNAL_SERVER_ERROR,
    LENGTH_REQUIRED as LENGTH_REQUIRED,
    LOCKED as LOCKED,
    METHOD_NOT_ALLOWED as METHOD_NOT_ALLOWED,
    MOVED_PERMANENTLY as MOVED_PERMANENTLY,
    MULTI_STATUS as MULTI_STATUS,
    MULTIPLE_CHOICES as MULTIPLE_CHOICES,
    NETWORK_AUTHENTICATION_REQUIRED as NETWORK_AUTHENTICATION_REQUIRED,
    NO_CONTENT as NO_CONTENT,
    NON_AUTHORITATIVE_INFORMATION as NON_AUTHORITATIVE_INFORMATION,
    NOT_ACCEPTABLE as NOT_ACCEPTABLE,
    NOT_EXTENDED as NOT_EXTENDED,
    NOT_FOUND as NOT_FOUND,
    NOT_IMPLEMENTED as NOT_IMPLEMENTED,
    NOT_MODIFIED as NOT_MODIFIED,
    OK as OK,
    PARTIAL_CONTENT as PARTIAL_CONTENT,
    PAYMENT_REQUIRED as PAYMENT_REQUIRED,
    PRECONDITION_FAILED as PRECONDITION_FAILED,
    PRECONDITION_REQUIRED as PRECONDITION_REQUIRED,
    PROCESSING as PROCESSING,
    PROXY_AUTHENTICATION_REQUIRED as PROXY_AUTHENTICATION_REQUIRED,
    REQUEST_ENTITY_TOO_LARGE as REQUEST_ENTITY_TOO_LARGE,
    REQUEST_HEADER_FIELDS_TOO_LARGE as REQUEST_HEADER_FIELDS_TOO_LARGE,
    REQUEST_TIMEOUT as REQUEST_TIMEOUT,
    REQUEST_URI_TOO_LONG as REQUEST_URI_TOO_LONG,
    REQUESTED_RANGE_NOT_SATISFIABLE as REQUESTED_RANGE_NOT_SATISFIABLE,
    RESET_CONTENT as RESET_CONTENT,
    SEE_OTHER as SEE_OTHER,
    SERVICE_UNAVAILABLE as SERVICE_UNAVAILABLE,
    SWITCHING_PROTOCOLS as SWITCHING_PROTOCOLS,
    TEMPORARY_REDIRECT as TEMPORARY_REDIRECT,
    TOO_MANY_REQUESTS as TOO_MANY_REQUESTS,
    UNAUTHORIZED as UNAUTHORIZED,
    UNPROCESSABLE_ENTITY as UNPROCESSABLE_ENTITY,
    UNSUPPORTED_MEDIA_TYPE as UNSUPPORTED_MEDIA_TYPE,
    UPGRADE_REQUIRED as UPGRADE_REQUIRED,
    USE_PROXY as USE_PROXY,
    HTTPMessage as HTTPMessage,
    parse_headers as parse_headers,
)
