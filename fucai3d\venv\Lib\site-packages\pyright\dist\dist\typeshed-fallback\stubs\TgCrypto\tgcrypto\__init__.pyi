from typing_extensions import <PERSON><PERSON><PERSON>

def ige256_encrypt(data: <PERSON><PERSON><PERSON>, key: <PERSON><PERSON><PERSON>, iv: <PERSON><PERSON><PERSON>, /) -> bytes: ...
def ige256_decrypt(data: <PERSON><PERSON><PERSON>, key: <PERSON><PERSON><PERSON>, iv: <PERSON><PERSON><PERSON>, /) -> bytes: ...
def ctr256_encrypt(data: <PERSON><PERSON><PERSON>, key: <PERSON><PERSON><PERSON>, iv: <PERSON><PERSON><PERSON>, state: <PERSON><PERSON><PERSON>, /) -> bytes: ...
def ctr256_decrypt(data: <PERSON><PERSON><PERSON>, key: <PERSON><PERSON><PERSON>, iv: <PERSON><PERSON><PERSON>, state: <PERSON><PERSON><PERSON>, /) -> bytes: ...
def cbc256_encrypt(data: <PERSON><PERSON><PERSON>, key: <PERSON><PERSON><PERSON>, iv: <PERSON><PERSON><PERSON>, /) -> bytes: ...
def cbc256_decrypt(data: <PERSON><PERSON><PERSON>, key: <PERSON><PERSON><PERSON>, iv: <PERSON><PERSON><PERSON>, /) -> bytes: ...
