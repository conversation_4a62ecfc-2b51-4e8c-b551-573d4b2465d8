from _typeshed import Incomplete

from win32com.server.util import ListEnumeratorGateway

class EnumDebugCodeContexts(ListEnumeratorGateway): ...
class EnumDebugStackFrames(ListEnumeratorGateway): ...
class EnumDebugApplicationNodes(ListEnumeratorGateway): ...
class EnumRemoteDebugApplications(ListEnumeratorGateway): ...
class EnumRemoteDebugApplicationThreads(ListEnumeratorGateway): ...

class DebugDocumentInfo:
    def GetName(self, dnt) -> None: ...
    def GetDocumentClassId(self) -> None: ...

class DebugDocumentProvider(DebugDocumentInfo):
    def GetDocument(self) -> None: ...

class DebugApplicationNode(DebugDocumentProvider):
    def EnumChildren(self) -> None: ...
    def GetParent(self) -> None: ...
    def SetDocumentProvider(self, pddp) -> None: ...
    def Close(self) -> None: ...
    def Attach(self, parent) -> None: ...
    def Detach(self) -> None: ...

class DebugApplicationNodeEvents:
    def onAddChild(self, child) -> None: ...
    def onRemoveChild(self, child) -> None: ...
    def onDetach(self) -> None: ...
    def onAttach(self, parent) -> None: ...

class DebugDocument(DebugDocumentInfo): ...

class DebugDocumentText(DebugDocument):
    def GetDocumentAttributes(self) -> None: ...
    def GetSize(self) -> None: ...
    def GetPositionOfLine(self, cLineNumber) -> None: ...
    def GetLineOfPosition(self, charPos) -> None: ...
    def GetText(self, charPos, maxChars, wantAttr) -> None: ...
    def GetPositionOfContext(self, debugDocumentContext) -> None: ...
    def GetContextOfPosition(self, charPos, maxChars) -> None: ...

class DebugDocumentTextExternalAuthor:
    def GetPathName(self) -> None: ...
    def GetFileName(self) -> None: ...
    def NotifyChanged(self) -> None: ...

class DebugDocumentTextEvents:
    def onDestroy(self) -> None: ...
    def onInsertText(self, cCharacterPosition, cNumToInsert) -> None: ...
    def onRemoveText(self, cCharacterPosition, cNumToRemove) -> None: ...
    def onReplaceText(self, cCharacterPosition, cNumToReplace) -> None: ...
    def onUpdateTextAttributes(self, cCharacterPosition, cNumToUpdate) -> None: ...
    def onUpdateDocumentAttributes(self, textdocattr) -> None: ...

class DebugDocumentContext:
    def GetDocument(self) -> None: ...
    def EnumCodeContexts(self) -> None: ...

class DebugCodeContext:
    def GetDocumentContext(self) -> None: ...
    def SetBreakPoint(self, bps) -> None: ...

class DebugStackFrame:
    def GetCodeContext(self) -> None: ...
    def GetDescriptionString(self, fLong) -> None: ...
    def GetLanguageString(self) -> None: ...
    def GetThread(self) -> None: ...
    def GetDebugProperty(self) -> None: ...

class DebugDocumentHost:
    def GetDeferredText(self, dwTextStartCookie, maxChars, bWantAttr) -> None: ...
    def GetScriptTextAttributes(self, codeText, delimterText, flags) -> None: ...
    def OnCreateDocumentContext(self) -> None: ...
    def GetPathName(self) -> None: ...
    def GetFileName(self) -> None: ...
    def NotifyChanged(self) -> None: ...

class DebugDocumentTextConnectServer:
    cookieNo: int
    connections: Incomplete
    def EnumConnections(self) -> None: ...
    def GetConnectionInterface(self) -> None: ...
    def GetConnectionPointContainer(self): ...
    def Advise(self, pUnk): ...
    def Unadvise(self, cookie): ...
    def EnumConnectionPoints(self) -> None: ...
    def FindConnectionPoint(self, iid): ...

class RemoteDebugApplicationEvents:
    def OnConnectDebugger(self, appDebugger) -> None: ...
    def OnDisconnectDebugger(self) -> None: ...
    def OnSetName(self, name) -> None: ...
    def OnDebugOutput(self, string) -> None: ...
    def OnClose(self) -> None: ...
    def OnEnterBreakPoint(self, rdat) -> None: ...
    def OnLeaveBreakPoint(self, rdat) -> None: ...
    def OnCreateThread(self, rdat) -> None: ...
    def OnDestroyThread(self, rdat) -> None: ...
    def OnBreakFlagChange(self, abf, rdat) -> None: ...

class DebugExpressionContext:
    def ParseLanguageText(self, code, radix, delim, flags) -> None: ...
    def GetLanguageInfo(self) -> None: ...

class DebugExpression:
    def Start(self, callback) -> None: ...
    def Abort(self) -> None: ...
    def QueryIsComplete(self) -> None: ...
    def GetResultAsString(self) -> None: ...
    def GetResultAsDebugProperty(self) -> None: ...

class ProvideExpressionContexts:
    def EnumExpressionContexts(self) -> None: ...
