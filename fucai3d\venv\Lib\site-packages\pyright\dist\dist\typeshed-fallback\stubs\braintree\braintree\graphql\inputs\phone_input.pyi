from _typeshed import Incomplete
from typing_extensions import Self

class PhoneInput:
    def __init__(
        self, country_phone_code: str | None = None, phone_number: str | None = None, extension_number: str | None = None
    ) -> None: ...
    def to_graphql_variables(self) -> dict[str, Incomplete]: ...
    @staticmethod
    def builder() -> Builder: ...

    class Builder:
        def __init__(self) -> None: ...
        def country_phone_code(self, country_phone_code: str) -> Self: ...
        def phone_number(self, phone_number: str) -> Self: ...
        def extension_number(self, extension_number: str) -> Self: ...
        def build(self) -> PhoneInput: ...
