from collections.abc import Callable, Sequence
from typing import Final, Literal

BOARD: Final = 10
BCM: Final = 11
TEGRA_SOC: Final = 1000
CVM: Final = 1001

PUD_OFF: Final = 20
PUD_DOWN: Final = 21
PUD_UP: Final = 22

HIGH: Final = 1
LOW: Final = 0

RISING: Final = 31
FALLING: Final = 32
BOTH: Final = 33

UNKNOWN: Final = -1
OUT: Final = 0
IN: Final = 1
HARD_PWM: Final = 43

model = ...
JETSON_INFO = ...
RPI_INFO = ...

def setwarnings(state: bool) -> None: ...
def setmode(mode: Literal[10, 11, 1000, 1001]) -> None: ...
def getmode() -> Literal[10, 11, 1000, 1001]: ...
def setup(
    channels: int | Sequence[int],
    direction: Literal[0, 1],
    pull_up_down: Literal[20, 21, 22] = ...,
    initial: Literal[0, 1] = ...,
    consumer: str = ...,
) -> None: ...
def cleanup(channel: int | Sequence[int] | None = ...) -> None: ...
def input(channel: int) -> Literal[0, 1]: ...
def output(channels: int | Sequence[int], values: Literal[0, 1]) -> None: ...
def add_event_detect(
    channel: int,
    edge: Literal[31, 32, 33],
    callback: Callable[[int], None] | None = ...,
    bouncetime: int | None = ...,
    polltime: float = ...,
) -> None: ...
def remove_event_detect(channel: int, timeout: float = ...) -> None: ...
def event_detected(channel: int) -> bool: ...
def add_event_callback(channel: int, callback: Callable[[int], None]) -> None: ...
def wait_for_edge(
    channel: int, edge: Literal[31, 32, 33], bouncetime: int | None = ..., timeout: float | None = ...
) -> int | None: ...
def gpio_function(channel: int) -> Literal[-1, 0, 1]: ...

class PWM:
    def __init__(self, channel: int, frequency_hz: float) -> None: ...
    def __del__(self) -> None: ...
    def start(self, duty_cycle_percent: float) -> None: ...
    def ChangeFrequency(self, frequency_hz: float) -> None: ...
    def ChangeDutyCycle(self, duty_cycle_percent: float) -> None: ...
    def stop(self) -> None: ...
