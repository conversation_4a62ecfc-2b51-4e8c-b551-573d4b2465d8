from types import TracebackType

from .models.segment import SegmentContextManager
from .models.subsegment import SubsegmentContextManager
from .recorder import AWSXRayRecorder

class AsyncSegmentContextManager(SegmentContextManager):
    async def __aenter__(self): ...
    async def __aexit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...

class AsyncSubsegmentContextManager(SubsegmentContextManager):
    async def __call__(self, wrapped, instance, args, kwargs): ...
    async def __aenter__(self): ...
    async def __aexit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...

class AsyncAWSXRayRecorder(AWSXRayRecorder):
    def capture_async(self, name=None): ...
    def in_segment_async(self, name=None, **segment_kwargs): ...
    def in_subsegment_async(self, name=None, **subsegment_kwargs): ...
    async def record_subsegment_async(self, wrapped, instance, args, kwargs, name, namespace, meta_processor): ...
