from typing import Final

XK_ISO_Lock: Final = 0xFE01
XK_ISO_Level2_Latch: Final = 0xFE02
XK_ISO_Level3_Shift: Final = 0xFE03
XK_ISO_Level3_Latch: Final = 0xFE04
XK_ISO_Level3_Lock: Final = 0xFE05
XK_ISO_Group_Shift: Final = 0xFF7E
XK_ISO_Group_Latch: Final = 0xFE06
XK_ISO_Group_Lock: Final = 0xFE07
XK_ISO_Next_Group: Final = 0xFE08
XK_ISO_Next_Group_Lock: Final = 0xFE09
XK_ISO_Prev_Group: Final = 0xFE0A
XK_ISO_Prev_Group_Lock: Final = 0xFE0B
XK_ISO_First_Group: Final = 0xFE0C
XK_ISO_First_Group_Lock: Final = 0xFE0D
XK_ISO_Last_Group: Final = 0xFE0E
XK_ISO_Last_Group_Lock: Final = 0xFE0F
XK_ISO_Left_Tab: Final = 0xFE20
XK_ISO_Move_Line_Up: Final = 0xFE21
XK_ISO_Move_Line_Down: Final = 0xFE22
XK_ISO_Partial_Line_Up: Final = 0xFE23
XK_ISO_Partial_Line_Down: Final = 0xFE24
XK_ISO_Partial_Space_Left: Final = 0xFE25
XK_ISO_Partial_Space_Right: Final = 0xFE26
XK_ISO_Set_Margin_Left: Final = 0xFE27
XK_ISO_Set_Margin_Right: Final = 0xFE28
XK_ISO_Release_Margin_Left: Final = 0xFE29
XK_ISO_Release_Margin_Right: Final = 0xFE2A
XK_ISO_Release_Both_Margins: Final = 0xFE2B
XK_ISO_Fast_Cursor_Left: Final = 0xFE2C
XK_ISO_Fast_Cursor_Right: Final = 0xFE2D
XK_ISO_Fast_Cursor_Up: Final = 0xFE2E
XK_ISO_Fast_Cursor_Down: Final = 0xFE2F
XK_ISO_Continuous_Underline: Final = 0xFE30
XK_ISO_Discontinuous_Underline: Final = 0xFE31
XK_ISO_Emphasize: Final = 0xFE32
XK_ISO_Center_Object: Final = 0xFE33
XK_ISO_Enter: Final = 0xFE34
XK_dead_grave: Final = 0xFE50
XK_dead_acute: Final = 0xFE51
XK_dead_circumflex: Final = 0xFE52
XK_dead_tilde: Final = 0xFE53
XK_dead_macron: Final = 0xFE54
XK_dead_breve: Final = 0xFE55
XK_dead_abovedot: Final = 0xFE56
XK_dead_diaeresis: Final = 0xFE57
XK_dead_abovering: Final = 0xFE58
XK_dead_doubleacute: Final = 0xFE59
XK_dead_caron: Final = 0xFE5A
XK_dead_cedilla: Final = 0xFE5B
XK_dead_ogonek: Final = 0xFE5C
XK_dead_iota: Final = 0xFE5D
XK_dead_voiced_sound: Final = 0xFE5E
XK_dead_semivoiced_sound: Final = 0xFE5F
XK_dead_belowdot: Final = 0xFE60
XK_First_Virtual_Screen: Final = 0xFED0
XK_Prev_Virtual_Screen: Final = 0xFED1
XK_Next_Virtual_Screen: Final = 0xFED2
XK_Last_Virtual_Screen: Final = 0xFED4
XK_Terminate_Server: Final = 0xFED5
XK_AccessX_Enable: Final = 0xFE70
XK_AccessX_Feedback_Enable: Final = 0xFE71
XK_RepeatKeys_Enable: Final = 0xFE72
XK_SlowKeys_Enable: Final = 0xFE73
XK_BounceKeys_Enable: Final = 0xFE74
XK_StickyKeys_Enable: Final = 0xFE75
XK_MouseKeys_Enable: Final = 0xFE76
XK_MouseKeys_Accel_Enable: Final = 0xFE77
XK_Overlay1_Enable: Final = 0xFE78
XK_Overlay2_Enable: Final = 0xFE79
XK_AudibleBell_Enable: Final = 0xFE7A
XK_Pointer_Left: Final = 0xFEE0
XK_Pointer_Right: Final = 0xFEE1
XK_Pointer_Up: Final = 0xFEE2
XK_Pointer_Down: Final = 0xFEE3
XK_Pointer_UpLeft: Final = 0xFEE4
XK_Pointer_UpRight: Final = 0xFEE5
XK_Pointer_DownLeft: Final = 0xFEE6
XK_Pointer_DownRight: Final = 0xFEE7
XK_Pointer_Button_Dflt: Final = 0xFEE8
XK_Pointer_Button1: Final = 0xFEE9
XK_Pointer_Button2: Final = 0xFEEA
XK_Pointer_Button3: Final = 0xFEEB
XK_Pointer_Button4: Final = 0xFEEC
XK_Pointer_Button5: Final = 0xFEED
XK_Pointer_DblClick_Dflt: Final = 0xFEEE
XK_Pointer_DblClick1: Final = 0xFEEF
XK_Pointer_DblClick2: Final = 0xFEF0
XK_Pointer_DblClick3: Final = 0xFEF1
XK_Pointer_DblClick4: Final = 0xFEF2
XK_Pointer_DblClick5: Final = 0xFEF3
XK_Pointer_Drag_Dflt: Final = 0xFEF4
XK_Pointer_Drag1: Final = 0xFEF5
XK_Pointer_Drag2: Final = 0xFEF6
XK_Pointer_Drag3: Final = 0xFEF7
XK_Pointer_Drag4: Final = 0xFEF8
XK_Pointer_Drag5: Final = 0xFEFD
XK_Pointer_EnableKeys: Final = 0xFEF9
XK_Pointer_Accelerate: Final = 0xFEFA
XK_Pointer_DfltBtnNext: Final = 0xFEFB
XK_Pointer_DfltBtnPrev: Final = 0xFEFC
