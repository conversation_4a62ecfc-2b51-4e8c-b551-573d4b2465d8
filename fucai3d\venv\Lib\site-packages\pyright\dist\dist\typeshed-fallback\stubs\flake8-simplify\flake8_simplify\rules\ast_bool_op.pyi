import ast

def get_sim101(node: ast.<PERSON><PERSON><PERSON><PERSON>) -> list[tuple[int, int, str]]: ...
def get_sim109(node: ast.<PERSON><PERSON><PERSON><PERSON>) -> list[tuple[int, int, str]]: ...
def get_sim220(node: ast.BoolOp) -> list[tuple[int, int, str]]: ...
def get_sim221(node: ast.BoolOp) -> list[tuple[int, int, str]]: ...
def get_sim222(node: ast.<PERSON><PERSON>O<PERSON>) -> list[tuple[int, int, str]]: ...
def get_sim223(node: ast.BoolOp) -> list[tuple[int, int, str]]: ...
