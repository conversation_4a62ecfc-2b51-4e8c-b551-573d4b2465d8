import optparse
from collections.abc import Iterable
from typing import Literal

from .base import Component

def getSortKey(component: Component) -> str: ...
def sortByUID(components: Iterable[Component]) -> list[Component]: ...
def deleteExtraneous(component: Component, ignore_dtstamp: bool = False) -> None: ...
def diff(left, right): ...
def prettyDiff(leftObj, rightObj) -> None: ...
def main() -> None: ...
def getOptions() -> tuple[Literal[False], Literal[False]] | tuple[optparse.Values, list[str]]: ...
