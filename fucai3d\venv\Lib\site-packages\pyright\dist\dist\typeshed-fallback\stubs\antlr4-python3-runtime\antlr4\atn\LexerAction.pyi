from _typeshed import Incomplete
from enum import IntEnum

Lexer: Incomplete

class LexerActionType(IntEnum):
    CHANNEL = 0
    CUSTOM = 1
    MODE = 2
    MORE = 3
    POP_MODE = 4
    PUSH_MODE = 5
    SKIP = 6
    TYPE = 7

class LexerAction:
    actionType: Incomplete
    isPositionDependent: bool
    def __init__(self, action: LexerActionType) -> None: ...
    def __hash__(self): ...
    def __eq__(self, other): ...

class LexerSkipAction(LexerAction):
    INSTANCE: Incomplete
    def __init__(self) -> None: ...
    def execute(self, lexer: Lexer): ...

class LexerTypeAction(LexerAction):
    type: Incomplete
    def __init__(self, type: int) -> None: ...
    def execute(self, lexer: Lexer): ...
    def __hash__(self): ...
    def __eq__(self, other): ...

class LexerPushModeAction(LexerAction):
    mode: Incomplete
    def __init__(self, mode: int) -> None: ...
    def execute(self, lexer: Lexer): ...
    def __hash__(self): ...
    def __eq__(self, other): ...

class LexerPopModeAction(LexerAction):
    INSTANCE: Incomplete
    def __init__(self) -> None: ...
    def execute(self, lexer: Lexer): ...

class LexerMoreAction(LexerAction):
    INSTANCE: Incomplete
    def __init__(self) -> None: ...
    def execute(self, lexer: Lexer): ...

class LexerModeAction(LexerAction):
    mode: Incomplete
    def __init__(self, mode: int) -> None: ...
    def execute(self, lexer: Lexer): ...
    def __hash__(self): ...
    def __eq__(self, other): ...

class LexerCustomAction(LexerAction):
    ruleIndex: Incomplete
    actionIndex: Incomplete
    isPositionDependent: bool
    def __init__(self, ruleIndex: int, actionIndex: int) -> None: ...
    def execute(self, lexer: Lexer): ...
    def __hash__(self): ...
    def __eq__(self, other): ...

class LexerChannelAction(LexerAction):
    channel: Incomplete
    def __init__(self, channel: int) -> None: ...
    def execute(self, lexer: Lexer): ...
    def __hash__(self): ...
    def __eq__(self, other): ...

class LexerIndexedCustomAction(LexerAction):
    offset: Incomplete
    action: Incomplete
    isPositionDependent: bool
    def __init__(self, offset: int, action: LexerAction) -> None: ...
    def execute(self, lexer: Lexer): ...
    def __hash__(self): ...
    def __eq__(self, other): ...
