from typing import Any

from crontab import CronTab

class UserSpool(list[CronTab]):
    def __init__(self, loc: str, tabs: CronTabs | None = ...) -> None: ...
    def listdir(self, loc: str) -> list[str]: ...
    def get_owner(self, path: str) -> str: ...
    def generate(self, loc: str, username: str) -> CronTab: ...

class SystemTab(list[CronTab]):
    def __init__(self, loc: str, tabs: CronTabs | None = ...) -> None: ...

class AnaCronTab(list[CronTab]):
    def __init__(self, loc: str, tabs: CronTabs | None = ...) -> None: ...
    def add(self, loc: str, item: str, anajob: CronTab) -> CronTab: ...

KNOWN_LOCATIONS: list[tuple[UserSpool | SystemTab | AnaCronTab, str]]

class CronTabs(list[UserSpool | SystemTab | AnaCronTab]):
    def __init__(self) -> None: ...
    def add(self, cls: type[UserSpool | SystemTab | AnaCronTab], *args: Any) -> None: ...
    @property
    def all(self) -> CronTab: ...
