from _typeshed import Read<PERSON><PERSON>uffer
from logging import Logger
from typing import Any, Final

log: Logger

BASE64_CHARS: Final[str]
AB64_CHARS: Final[str]
HASH64_CHARS: Final[str]
BCRYPT_CHARS: Final[str]
PADDED_BASE64_CHARS: Final[str]
HEX_CHARS: Final[str]
UPPER_HEX_CHARS: Final[str]
LOWER_HEX_CHARS: Final[str]
ALL_BYTE_VALUES: Final[bytes]

def compile_byte_translation(mapping: dict[str | bytes | int, str | bytes], source: bytes | None = None) -> bytes: ...
def b64s_encode(data: ReadableBuffer) -> bytes: ...
def b64s_decode(data: str | ReadableBuffer) -> bytes: ...
def ab64_encode(data: ReadableBuffer) -> bytes: ...
def ab64_decode(data: str | ReadableBuffer) -> bytes: ...
def b32encode(source: ReadableBuffer) -> str: ...
def b32decode(source: str | bytes) -> bytes: ...

class Base64Engine:
    bytemap: Any
    big: Any
    def __init__(self, charmap, big: bool = False) -> None: ...
    @property
    def charmap(self): ...
    def encode_bytes(self, source): ...
    def decode_bytes(self, source): ...
    def check_repair_unused(self, source): ...
    def repair_unused(self, source): ...
    def encode_transposed_bytes(self, source, offsets): ...
    def decode_transposed_bytes(self, source, offsets): ...
    def decode_int6(self, source): ...
    def decode_int12(self, source): ...
    def decode_int24(self, source): ...
    def decode_int30(self, source): ...
    def decode_int64(self, source): ...
    def encode_int6(self, value): ...
    def encode_int12(self, value): ...
    def encode_int24(self, value): ...
    def encode_int30(self, value): ...
    def encode_int64(self, value): ...

class LazyBase64Engine(Base64Engine):
    def __init__(self, *args, **kwds) -> None: ...
    def __getattribute__(self, attr: str): ...

h64: Base64Engine
h64big: Base64Engine
bcrypt64: Base64Engine

__all__ = [
    # constants
    "BASE64_CHARS",
    "PADDED_BASE64_CHARS",
    "AB64_CHARS",
    "HASH64_CHARS",
    "BCRYPT_CHARS",
    "HEX_CHARS",
    "LOWER_HEX_CHARS",
    "UPPER_HEX_CHARS",
    "ALL_BYTE_VALUES",
    # misc
    "compile_byte_translation",
    # base64
    "ab64_encode",
    "ab64_decode",
    "b64s_encode",
    "b64s_decode",
    # base32
    "b32encode",
    "b32decode",
    # custom encodings
    "Base64Engine",
    "LazyBase64Engine",
    "h64",
    "h64big",
    "bcrypt64",
]
