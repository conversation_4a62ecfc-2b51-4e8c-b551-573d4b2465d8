from types import ModuleType
from typing import ClassVar

from docutils import nodes
from docutils.transforms import Transform

class pending_xref(nodes.Inline, nodes.Element): ...

sphinx: ModuleType

def is_literal(node: nodes.Node) -> bool: ...

class _CommonMarkParser:
    default_config: ClassVar[dict[str, None]]
    def __init__(self) -> None: ...
    def convert_ast(self, ast): ...
    def default_visit(self, mdnode): ...
    def default_depart(self, mdnode): ...
    def visit_heading(self, mdnode): ...
    def depart_heading(self, _): ...
    def visit_text(self, mdnode): ...
    def visit_softbreak(self, _): ...
    def visit_linebreak(self, _): ...
    def visit_paragraph(self, mdnode): ...
    def visit_emph(self, _): ...
    def visit_strong(self, _): ...
    def visit_code(self, mdnode): ...
    def visit_link(self, mdnode): ...
    def depart_link(self, mdnode): ...
    def visit_image(self, mdnode): ...
    def visit_list(self, mdnode): ...
    def visit_item(self, mdnode): ...
    def visit_code_block(self, mdnode): ...
    def visit_block_quote(self, mdnode): ...
    def visit_html(self, mdnode): ...
    def visit_html_inline(self, mdnode): ...
    def visit_html_block(self, mdnode): ...
    def visit_thematic_break(self, _): ...
    def setup_sections(self): ...
    def add_section(self, section, level): ...
    def is_section_level(self, level, section): ...

class Parser(_CommonMarkParser):
    supported: ClassVar[tuple[str, ...]]
    config_section: ClassVar[str]
    config_section_dependencies: ClassVar[tuple[str, ...]]
    def get_transforms(self) -> list[type[Transform]]: ...
    def parse(self, inputstring: str, document: nodes.document) -> None: ...
    def visit_document(self, node) -> None: ...
    def visit_text(self, mdnode) -> None: ...
