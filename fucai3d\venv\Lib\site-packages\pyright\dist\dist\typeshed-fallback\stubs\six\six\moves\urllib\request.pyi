import sys

# Stubs for six.moves.urllib.request
#
# Note: Commented out items means they weren't implemented at the time.
# Uncomment them when the modules have been added to the typeshed.
# from urllib.request import proxy_bypass as proxy_bypass
from urllib.request import (
    AbstractBasicAuthHand<PERSON> as AbstractBasicAuthHandler,
    AbstractDigestAuthHandler as AbstractDigestAuthHandler,
    <PERSON><PERSON><PERSON><PERSON> as BaseHandler,
    <PERSON>acheFTP<PERSON><PERSON>ler as <PERSON><PERSON><PERSON><PERSON>H<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON> as <PERSON>Hand<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    HTTP<PERSON>asic<PERSON>uth<PERSON><PERSON><PERSON> as HTTPBasicAuthHandler,
    HTTPCookieProcessor as HTTPCookieProcessor,
    HTTPDefaultError<PERSON>andler as H<PERSON>PDefaultErrorHandler,
    HTTPDigestAuthHandler as HTTPDigestAuthHandler,
    HTTPErrorProcessor as HTTPErrorProcessor,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as HTTPHand<PERSON>,
    HTTPPasswordMgr as HTTPPasswordMgr,
    HTTPPasswordMgrWithDefaultRealm as HTTPPasswordMgrWithDefaultRealm,
    HTTPRedirect<PERSON><PERSON><PERSON> as HTTPRedirectHandler,
    HTTPSHand<PERSON> as <PERSON><PERSON>PSHandler,
    OpenerDirector as OpenerDirector,
    ProxyBasicAuthHandler as ProxyBasicAuthHandler,
    ProxyDigestAuthHandler as ProxyDigestAuthHandler,
    ProxyHandler as ProxyHandler,
    Request as Request,
    UnknownHandler as UnknownHandler,
    build_opener as build_opener,
    getproxies as getproxies,
    install_opener as install_opener,
    parse_http_list as parse_http_list,
    parse_keqv_list as parse_keqv_list,
    pathname2url as pathname2url,
    url2pathname as url2pathname,
    urlcleanup as urlcleanup,
    urlopen as urlopen,
    urlretrieve as urlretrieve,
)

if sys.version_info < (3, 14):
    from urllib.request import FancyURLopener as FancyURLopener, URLopener as URLopener
