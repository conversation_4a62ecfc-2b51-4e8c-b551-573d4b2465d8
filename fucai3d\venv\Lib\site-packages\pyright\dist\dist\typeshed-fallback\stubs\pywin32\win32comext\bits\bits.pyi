import _win32typing

BG_AUTH_SCHEME_BASIC: int
BG_AUTH_SCHEME_DIGEST: int
BG_AUTH_SCHEME_NEGOTIATE: int
BG_AUTH_SCHEME_NTLM: int
BG_AUTH_SCHEME_PASSPORT: int
BG_AUTH_TARGET_PROXY: int
BG_AUTH_TARGET_SERVER: int
BG_CERT_STORE_LOCATION_CURRENT_SERVICE: int
BG_CERT_STORE_LOCATION_CURRENT_USER: int
BG_CERT_STORE_LOCATION_CURRENT_USER_GROUP_POLICY: int
BG_CERT_STORE_LOCATION_LOCAL_MACHINE: int
BG_CERT_STORE_LOCATION_LOCAL_MACHINE_ENTERPRISE: int
BG_CERT_STORE_LOCATION_LOCAL_MACHINE_GROUP_POLICY: int
BG_CERT_STORE_LOCATION_SERVICES: int
BG_CERT_STORE_LOCATION_USERS: int
BG_ERROR_CONTEXT_GENERAL_QUEUE_MANAGER: int
BG_ERROR_CONTEXT_GENERAL_TRANSPORT: int
BG_ERROR_CONTEXT_LOCAL_FILE: int
BG_ERROR_CONTEXT_NONE: int
BG_ERROR_CONTEXT_QUEUE_MANAGER_NOTIFICATION: int
BG_ERROR_CONTEXT_REMOTE_APPLICATION: int
BG_ERROR_CONTEXT_REMOTE_FILE: int
BG_ERROR_CONTEXT_UNKNOWN: int
BG_JOB_ENUM_ALL_USERS: int
BG_JOB_PRIORITY_FOREGROUND: int
BG_JOB_PRIORITY_HIGH: int
BG_JOB_PRIORITY_LOW: int
BG_JOB_PRIORITY_NORMAL: int
BG_JOB_PROXY_USAGE_AUTODETECT: int
BG_JOB_PROXY_USAGE_NO_PROXY: int
BG_JOB_PROXY_USAGE_OVERRIDE: int
BG_JOB_PROXY_USAGE_PRECONFIG: int
BG_JOB_STATE_ACKNOWLEDGED: int
BG_JOB_STATE_CANCELLED: int
BG_JOB_STATE_CONNECTING: int
BG_JOB_STATE_ERROR: int
BG_JOB_STATE_QUEUED: int
BG_JOB_STATE_SUSPENDED: int
BG_JOB_STATE_TRANSFERRED: int
BG_JOB_STATE_TRANSFERRING: int
BG_JOB_STATE_TRANSIENT_ERROR: int
BG_JOB_TYPE_DOWNLOAD: int
BG_JOB_TYPE_UPLOAD: int
BG_JOB_TYPE_UPLOAD_REPLY: int
BG_NOTIFY_DISABLE: int
BG_NOTIFY_JOB_ERROR: int
BG_NOTIFY_JOB_MODIFICATION: int
BG_NOTIFY_JOB_TRANSFERRED: int
CLSID_BackgroundCopyManager: _win32typing.PyIID
IID_IBackgroundCopyCallback: _win32typing.PyIID
IID_IBackgroundCopyError: _win32typing.PyIID
IID_IBackgroundCopyFile: _win32typing.PyIID
IID_IBackgroundCopyFile2: _win32typing.PyIID
IID_IBackgroundCopyJob: _win32typing.PyIID
IID_IBackgroundCopyJob2: _win32typing.PyIID
IID_IBackgroundCopyJob3: _win32typing.PyIID
IID_IBackgroundCopyManager: _win32typing.PyIID
IID_IEnumBackgroundCopyFiles: _win32typing.PyIID
IID_IEnumBackgroundCopyJobs: _win32typing.PyIID
