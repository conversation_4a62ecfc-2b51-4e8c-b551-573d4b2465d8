import datetime
from typing_extensions import TypeAlias

from .cal import Alarm, Event, Todo
from .error import (
    ComponentEndMissing as ComponentEndMissing,
    ComponentStartMissing as ComponentStartMissing,
    IncompleteAlarmInformation as IncompleteAlarmInformation,
    LocalTimezoneMissing as LocalTimezoneMissing,
)

__all__ = ["Alarms", "AlarmTime", "IncompleteAlarmInformation", "ComponentEndMissing", "ComponentStartMissing"]

Parent: TypeAlias = Event | Todo

class AlarmTime:
    def __init__(
        self,
        alarm: Alarm,
        trigger: datetime.datetime,
        acknowledged_until: datetime.datetime | None = None,
        snoozed_until: datetime.datetime | None = None,
        parent: Parent | None = None,
    ): ...
    @property
    def acknowledged(self) -> datetime.datetime | None: ...
    @property
    def alarm(self) -> Alarm: ...
    @property
    def parent(self) -> Parent | None: ...
    def is_active(self) -> bool: ...
    @property
    def trigger(self) -> datetime.date: ...

class Alarms:
    def __init__(self, component: Alarm | Event | Todo | None = None): ...
    def add_component(self, component: Alarm | Parent) -> None: ...
    def set_parent(self, parent: Parent) -> None: ...
    def add_alarm(self, alarm: Alarm) -> None: ...
    def set_start(self, dt: datetime.date | None) -> None: ...
    def set_end(self, dt: datetime.date | None) -> None: ...
    def acknowledge_until(self, dt: datetime.date | None) -> None: ...
    def snooze_until(self, dt: datetime.date | None) -> None: ...
    def set_local_timezone(self, tzinfo: datetime.tzinfo | str | None) -> None: ...
    @property
    def times(self) -> list[AlarmTime]: ...
    @property
    def active(self) -> list[AlarmTime]: ...
