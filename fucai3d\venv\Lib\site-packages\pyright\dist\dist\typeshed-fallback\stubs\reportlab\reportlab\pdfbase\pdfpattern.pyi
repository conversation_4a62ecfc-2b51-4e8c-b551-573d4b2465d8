from _typeshed import Incomplete
from collections.abc import Generator

from reportlab.pdfbase.pdfdoc import PDFObject

class PDFPattern(PDFObject):
    __RefOnly__: int
    pattern: Incomplete
    arguments: Incomplete
    def __init__(self, pattern_sequence, **keywordargs) -> None: ...
    def __setitem__(self, item, value) -> None: ...
    def __getitem__(self, item): ...
    def eval(self, L) -> Generator[Incomplete, None, None]: ...
    def format(self, document): ...
    def clone(self): ...

class PDFPatternIf:
    cond: Incomplete
    thenPart: Incomplete
    elsePart: Incomplete
    def __init__(self, cond, thenPart=[], elsePart=[]) -> None: ...
