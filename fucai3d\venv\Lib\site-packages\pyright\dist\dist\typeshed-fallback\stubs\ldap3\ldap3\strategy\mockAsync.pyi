from typing import Any

from .asynchronous import AsyncStrategy
from .mockBase import MockBaseStrategy

class MockAsyncStrategy(MockBaseStrategy, AsyncStrategy):
    def __init__(self, ldap_connection) -> None: ...
    def post_send_search(self, payload): ...
    bound: Any
    def post_send_single_response(self, payload): ...
    def get_response(self, message_id, timeout=None, get_request: bool = False): ...
