from _typeshed import Incomplete, SupportsGetItem
from collections.abc import Mapping
from typing import SupportsIndex

from networkx.classes.graph import Graph, _Node
from networkx.utils.backends import _dispatchable

__all__ = ["equitable_color"]

@_dispatchable
def is_coloring(G: Graph[_Node], coloring: SupportsGetItem[Incomplete, Incomplete]) -> bool: ...
@_dispatchable
def is_equitable(G: Graph[_Node], coloring: Mapping[Incomplete, Incomplete], num_colors: SupportsIndex | None = None) -> bool: ...
def make_C_from_F(F): ...
def make_N_from_L_C(L, C): ...
def make_H_from_C_N(C, N): ...
def change_color(u, X, Y, N, H, F, C, L): ...
def move_witnesses(src_color, dst_color, N, H, F, C, T_cal, L): ...
@_dispatchable
def pad_graph(G: Graph[_Node], num_colors): ...
def procedure_P(V_minus, V_plus, N, H, F, C, L, excluded_colors=None): ...
@_dispatchable
def equitable_color(G: Graph[_Node], num_colors): ...
