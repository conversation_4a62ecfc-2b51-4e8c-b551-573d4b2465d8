from typing import Final

XK_Aogonek: Final = 0x1A1
XK_breve: Final = 0x1A2
XK_Lstroke: Final = 0x1A3
XK_Lcaron: Final = 0x1A5
XK_Sacute: Final = 0x1A6
XK_Scaron: Final = 0x1A9
XK_Scedilla: Final = 0x1AA
XK_Tcaron: Final = 0x1AB
XK_Zacute: Final = 0x1AC
XK_Zcaron: Final = 0x1AE
XK_Zabovedot: Final = 0x1AF
XK_aogonek: Final = 0x1B1
XK_ogonek: Final = 0x1B2
XK_lstroke: Final = 0x1B3
XK_lcaron: Final = 0x1B5
XK_sacute: Final = 0x1B6
XK_caron: Final = 0x1B7
XK_scaron: Final = 0x1B9
XK_scedilla: Final = 0x1BA
XK_tcaron: Final = 0x1BB
XK_zacute: Final = 0x1BC
XK_doubleacute: Final = 0x1BD
XK_zcaron: Final = 0x1BE
XK_zabovedot: Final = 0x1BF
XK_Racute: Final = 0x1C0
XK_Abreve: Final = 0x1C3
XK_Lacute: Final = 0x1C5
XK_Cacute: Final = 0x1C6
XK_Ccaron: Final = 0x1C8
XK_Eogonek: Final = 0x1CA
XK_Ecaron: Final = 0x1CC
XK_Dcaron: Final = 0x1CF
XK_Dstroke: Final = 0x1D0
XK_Nacute: Final = 0x1D1
XK_Ncaron: Final = 0x1D2
XK_Odoubleacute: Final = 0x1D5
XK_Rcaron: Final = 0x1D8
XK_Uring: Final = 0x1D9
XK_Udoubleacute: Final = 0x1DB
XK_Tcedilla: Final = 0x1DE
XK_racute: Final = 0x1E0
XK_abreve: Final = 0x1E3
XK_lacute: Final = 0x1E5
XK_cacute: Final = 0x1E6
XK_ccaron: Final = 0x1E8
XK_eogonek: Final = 0x1EA
XK_ecaron: Final = 0x1EC
XK_dcaron: Final = 0x1EF
XK_dstroke: Final = 0x1F0
XK_nacute: Final = 0x1F1
XK_ncaron: Final = 0x1F2
XK_odoubleacute: Final = 0x1F5
XK_udoubleacute: Final = 0x1FB
XK_rcaron: Final = 0x1F8
XK_uring: Final = 0x1F9
XK_tcedilla: Final = 0x1FE
XK_abovedot: Final = 0x1FF
