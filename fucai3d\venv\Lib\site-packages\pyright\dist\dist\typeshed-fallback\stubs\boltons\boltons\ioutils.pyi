import abc
from _typeshed import Incomplete
from abc import abstractmethod

READ_CHUNK_SIZE: int
EINVAL: Incomplete

class SpooledIOBase(metaclass=abc.ABCMeta):
    __metaclass__: Incomplete
    def __init__(self, max_size: int = 5000000, dir=None) -> None: ...
    @abstractmethod
    def read(self, n: int = -1): ...
    @abstractmethod
    def write(self, s): ...
    @abstractmethod
    def seek(self, pos, mode: int = 0): ...
    @abstractmethod
    def readline(self, length=None): ...
    @abstractmethod
    def readlines(self, sizehint: int = 0): ...
    def writelines(self, lines) -> None: ...
    @abstractmethod
    def rollover(self): ...
    @abstractmethod
    def tell(self): ...
    @property
    @abc.abstractmethod
    def buffer(self): ...
    @property
    @abc.abstractmethod
    def len(self): ...
    softspace: Incomplete
    def close(self): ...
    def flush(self): ...
    def isatty(self): ...
    def next(self): ...
    @property
    def closed(self): ...
    @property
    def pos(self): ...
    @property
    def buf(self): ...
    def fileno(self): ...
    def truncate(self, size=None): ...
    def getvalue(self): ...
    def seekable(self): ...
    def readable(self): ...
    def writable(self): ...
    __next__: Incomplete
    def __len__(self): ...
    def __iter__(self): ...
    def __enter__(self): ...
    def __exit__(self, *args) -> None: ...
    def __eq__(self, other): ...
    def __ne__(self, other): ...
    def __bool__(self): ...
    def __del__(self) -> None: ...
    __nonzero__: Incomplete

class SpooledBytesIO(SpooledIOBase):
    def read(self, n: int = -1): ...
    def write(self, s) -> None: ...
    def seek(self, pos, mode: int = 0): ...
    def readline(self, length=None): ...
    def readlines(self, sizehint: int = 0): ...
    def rollover(self) -> None: ...
    @property
    def buffer(self): ...
    @property
    def len(self): ...
    def tell(self): ...

class SpooledStringIO(SpooledIOBase):
    def __init__(self, *args, **kwargs) -> None: ...
    def read(self, n: int = -1): ...
    def write(self, s) -> None: ...
    def seek(self, pos, mode: int = 0): ...
    def readline(self, length=None): ...
    def readlines(self, sizehint: int = 0): ...
    @property
    def buffer(self): ...
    def rollover(self) -> None: ...
    def tell(self): ...
    @property
    def len(self): ...

def is_text_fileobj(fileobj) -> bool: ...

class MultiFileReader:
    def __init__(self, *fileobjs) -> None: ...
    def read(self, amt=None): ...
    def seek(self, offset, whence=0) -> None: ...
