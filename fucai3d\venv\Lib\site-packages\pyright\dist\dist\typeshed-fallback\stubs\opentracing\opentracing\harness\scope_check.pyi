from collections.abc import Callable

from ..scope_manager import ScopeManager

class ScopeCompatibilityCheckMixin:
    def scope_manager(self) -> ScopeManager: ...
    def run_test(self, test_fn: Callable[[], object]) -> None: ...
    def test_missing_active_external(self) -> None: ...
    def test_missing_active(self) -> None: ...
    def test_activate(self) -> None: ...
    def test_activate_external(self) -> None: ...
    def test_activate_finish_on_close(self) -> None: ...
    def test_activate_nested(self) -> None: ...
    def test_activate_finish_on_close_nested(self) -> None: ...
    def test_close_wrong_order(self) -> None: ...
