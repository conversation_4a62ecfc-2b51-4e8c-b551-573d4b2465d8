from typing import Any
from typing_extensions import Self

__tracebackhide__: bool

class ContainsMixin:
    def contains(self, *items: Any) -> Self: ...
    def does_not_contain(self, *items: Any) -> Self: ...
    def contains_only(self, *items: Any) -> Self: ...
    def contains_sequence(self, *items: Any) -> Self: ...
    def contains_duplicates(self) -> Self: ...
    def does_not_contain_duplicates(self) -> Self: ...
    def is_empty(self) -> Self: ...
    def is_not_empty(self) -> Self: ...
    def is_in(self, *items: Any) -> Self: ...
    def is_not_in(self, *items: Any) -> Self: ...
