from collections.abc import Iterable, Sequence
from typing import Any, NamedTuple

from consolemenu.screen import Screen
from consolemenu.validators.base import BaseValidator

class InputResult(NamedTuple):
    input_string: str
    validation_result: bool

class PromptFormatter:
    @staticmethod
    def format_prompt(
        prompt: str | None = None,
        default: str | None = None,
        enable_quit: bool = False,
        quit_string: str = "q",
        quit_message: str = "(enter q to Quit)",
    ) -> str: ...

class PromptUtils:
    def __init__(self, screen: Screen, prompt_formatter: PromptFormatter | None = None) -> None: ...
    @property
    def screen(self) -> Screen: ...
    def clear(self) -> None: ...
    def confirm_answer(self, answer: str, message: str | None = None) -> bool: ...
    def enter_to_continue(self, message: str | None = None) -> None: ...
    def input(
        self,
        prompt: str | None = None,
        default: str | None = None,
        validators: Iterable[BaseValidator] | None = None,
        enable_quit: bool = False,
        quit_string: str = "q",
        quit_message: str = "(enter q to Quit)",
    ) -> InputResult: ...
    def input_password(self, message: str | None = None) -> str: ...
    def printf(self, *args: Any) -> None: ...
    def println(self, *args: Any) -> None: ...
    def prompt_and_confirm_password(self, message: str) -> str: ...
    def prompt_for_bilateral_choice(self, prompt: str, option1: str, option2: str) -> str: ...
    def prompt_for_trilateral_choice(self, prompt: str, option1: str, option2: str, option3: str) -> str: ...
    def prompt_for_yes_or_no(self, prompt: str) -> bool: ...
    def prompt_for_numbered_choice(self, choices: Sequence[str], title: str | None = None, prompt: str = ">") -> int: ...
    def validate_input(self, input_string: str, validators: BaseValidator) -> bool: ...

class UserQuit(Exception): ...
