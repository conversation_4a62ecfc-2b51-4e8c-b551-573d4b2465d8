# P2高级特征工程系统 - 用户手册

## 📖 欢迎使用P2系统

P2高级特征工程系统是福彩3D智能预测系统的第二代特征工程解决方案，在P1基础系统的基础上，集成了先进的特征工程库、智能缓存优化和科学的特征分析功能，为用户提供更强大、更智能的特征数据服务。

## 🎯 系统特色

### ✨ 核心优势
- **高级特征工程**: 集成Feature-engine库，提供50+维高级特征
- **智能缓存系统**: LRU内存缓存 + 数据库持久化，响应速度提升10倍
- **科学特征分析**: 基于SHAP的特征重要性分析，提供科学决策依据
- **标准化接口**: 为机器学习模型提供ML就绪的数据接口
- **高性能API**: RESTful API v2，支持批量处理和并发访问

### 🔧 技术特性
- **多层缓存**: 内存 + 数据库双层缓存策略
- **批量处理**: 支持1000期数据的批量特征生成
- **实时分析**: 特征重要性实时分析和排序
- **性能监控**: 完整的性能指标和监控体系
- **错误恢复**: 完善的错误处理和自动恢复机制

## 🚀 快速入门

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Linux
- **Python版本**: 3.8 或更高版本
- **内存**: 建议 4GB 或以上
- **存储空间**: 至少 1GB 可用空间

### 安装步骤

#### 1. 环境准备
```bash
# 确认Python版本
python --version

# 创建虚拟环境（推荐）
python -m venv p2_env
source p2_env/bin/activate  # Linux/Mac
# 或
p2_env\Scripts\activate     # Windows
```

#### 2. 安装依赖
```bash
# 进入项目目录
cd fucai3d

# 安装核心依赖
pip install feature-engine>=1.6.0
pip install shap>=0.42.0
pip install scikit-learn>=1.3.0
pip install flask
pip install pandas numpy matplotlib seaborn

# 安装可选依赖
pip install pytest psutil  # 测试和监控
```

#### 3. 验证安装
```bash
# 运行测试验证安装
python test_cache_simple.py
python test_feature_importance.py
```

### 首次使用

#### 1. 启动API服务
```bash
# 启动P2 API服务
python src/api/app.py

# 看到以下输出表示启动成功：
# 启动P2高级特征工程API服务
# 服务地址: http://127.0.0.1:5000
# API文档: http://127.0.0.1:5000/docs
```

#### 2. 验证服务
```bash
# 在浏览器中访问
http://127.0.0.1:5000/health

# 或使用命令行
curl http://127.0.0.1:5000/health
```

#### 3. 查看API文档
```bash
# 访问完整API文档
http://127.0.0.1:5000/docs
```

## 📚 功能使用指南

### 1. 基础特征获取

#### 使用Python代码
```python
from src.data.advanced_feature_engineer import AdvancedFeatureEngineer

# 创建特征工程器
engineer = AdvancedFeatureEngineer("data/lottery.db", cache_enabled=True)

# 获取单期百位特征
features = engineer.get_features_with_cache("2025001", "hundreds")
print(f"百位特征: {features}")

# 获取所有类型特征
all_features = engineer.get_features_with_cache("2025001", "all")
print(f"特征总数: {len(all_features)}")
```

#### 使用API接口
```python
import requests

# 获取单期特征
response = requests.get("http://127.0.0.1:5000/api/v2/features/advanced/hundreds/2025001")
data = response.json()

if data['status'] == 'success':
    features = data['data']['features']
    print(f"获取到 {len(features)} 个特征")
else:
    print(f"错误: {data['message']}")
```

### 2. 批量特征处理

#### 批量获取特征
```python
# 使用Python代码
issues = ["2025001", "2025002", "2025003"]
batch_features = engineer.batch_generate_features(issues, "hundreds")

for issue, features in batch_features.items():
    print(f"{issue}: {len(features)}个特征")
```

#### 使用API批量接口
```python
import requests

# 批量API请求
batch_data = {
    "issues": ["2025001", "2025002", "2025003"],
    "feature_type": "hundreds",
    "include_cache_stats": True
}

response = requests.post(
    "http://127.0.0.1:5000/api/v2/features/batch", 
    json=batch_data
)

data = response.json()
if data['status'] == 'success':
    results = data['data']['results']
    summary = data['data']['summary']
    print(f"成功处理: {summary['success_count']}/{summary['total_requested']}")
```

### 3. 特征重要性分析

#### 基础分析
```python
from src.data.feature_importance import FeatureImportanceAnalyzer
import pandas as pd

# 创建分析器
analyzer = FeatureImportanceAnalyzer()

# 准备数据（示例）
# X: 特征数据 DataFrame
# y: 目标变量 Series

# 执行分析
result = analyzer.analyze_feature_importance(X, y)

# 查看Top特征
top_features = result.feature_ranking[:10]
for feature, score in top_features:
    print(f"{feature}: {score:.4f}")

# 生成详细报告
report = analyzer.generate_report(result)
print("分析建议:")
for recommendation in report['recommendations']:
    print(f"- {recommendation}")
```

#### 使用API进行分析
```python
# 特征重要性分析API
analysis_data = {
    "issues": [f"2025{str(i).zfill(3)}" for i in range(1, 51)],  # 50个期号
    "target_variable": "hundreds",
    "analysis_config": {
        "top_k_features": 15,
        "model_type": "auto"
    }
}

response = requests.post(
    "http://127.0.0.1:5000/api/v2/features/importance/hundreds",
    json=analysis_data
)

data = response.json()
if data['status'] == 'success':
    top_features = data['data']['top_features']
    print("Top 5 重要特征:")
    for i, (feature, score) in enumerate(top_features[:5], 1):
        print(f"{i}. {feature}: {score:.4f}")
```

### 4. 预测模型接口使用

#### 创建ML数据集
```python
from src.interfaces.predictor_feature_interface import create_predictor_interface
from src.interfaces.predictor_feature_interface import FeatureConfig

# 创建百位预测器接口
interface = create_predictor_interface("hundreds", "data/lottery.db")

# 配置特征参数
config = FeatureConfig(
    feature_types=['hundreds', 'common'],
    window_size=10,
    lag_features=[1, 2, 3],
    feature_selection=True,
    normalization="standard"
)

# 准备期号数据
issues = [f"2025{str(i).zfill(3)}" for i in range(1, 101)]  # 100个期号

# 创建ML数据集
dataset = interface.create_ml_pipeline(issues, config)

print(f"数据集信息:")
print(f"- 样本数量: {len(dataset.X)}")
print(f"- 特征数量: {len(dataset.feature_names)}")
print(f"- 目标变量范围: [{dataset.y.min()}, {dataset.y.max()}]")
```

#### 训练测试分割
```python
# 分割训练测试集
train_dataset, test_dataset = dataset.train_test_split(test_size=0.2, random_state=42)

print(f"训练集: {len(train_dataset.X)} 样本")
print(f"测试集: {len(test_dataset.X)} 样本")

# 获取特征重要性
importance_result = interface.get_feature_importance(train_dataset)
if importance_result:
    print("Top 5 重要特征:")
    for feature, score in importance_result['top_features'][:5]:
        print(f"- {feature}: {score:.4f}")
```

### 5. 缓存管理

#### 查看缓存状态
```python
# 获取缓存统计
cache_stats = engineer.get_cache_stats()
print(f"缓存命中率: {cache_stats['hit_rate']:.2%}")
print(f"总请求数: {cache_stats['total_requests']}")
print(f"内存命中: {cache_stats['memory_hits']}")
```

#### 缓存维护
```python
# 清理缓存
engineer.clear_cache()
print("缓存已清理")

# 使用API清理缓存
clear_data = {"cache_type": "memory"}
response = requests.post(
    "http://127.0.0.1:5000/api/v2/features/cache/clear",
    json=clear_data
)
```

## 🔧 高级功能

### 1. 自定义特征生成器

#### 创建专用生成器
```python
from src.data.specialized_generators.base_generator import BaseSpecializedGenerator

class CustomGenerator(BaseSpecializedGenerator):
    def __init__(self, db_path):
        super().__init__(db_path, "custom")
    
    def generate_features(self, issue):
        # 实现自定义特征逻辑
        features = {}
        
        # 获取基础数据
        lottery_data = self.get_lottery_data(issue)
        if not lottery_data:
            return features
        
        # 计算自定义特征
        features['custom_feature_1'] = lottery_data['hundreds'] * 2
        features['custom_feature_2'] = lottery_data['tens'] + lottery_data['units']
        
        return features

# 使用自定义生成器
custom_gen = CustomGenerator("data/lottery.db")
custom_features = custom_gen.generate_features("2025001")
```

### 2. 性能监控

#### 系统性能监控
```python
import psutil
import time

def monitor_performance():
    """监控系统性能"""
    # 内存使用
    memory = psutil.Process().memory_info()
    memory_mb = memory.rss / 1024 / 1024
    
    # CPU使用
    cpu_percent = psutil.cpu_percent(interval=1)
    
    print(f"内存使用: {memory_mb:.1f} MB")
    print(f"CPU使用: {cpu_percent:.1f}%")

# 性能测试
start_time = time.time()
features = engineer.get_features_with_cache("2025001", "all")
end_time = time.time()

print(f"特征生成耗时: {end_time - start_time:.3f} 秒")
monitor_performance()
```

### 3. 数据导出

#### 导出特征数据
```python
# 导出数据集
export_path = interface.export_dataset(
    dataset, 
    format="csv", 
    output_dir="exports"
)
print(f"数据集已导出到: {export_path}")

# 使用API导出
export_data = {
    "issues": ["2025001", "2025002"],
    "feature_types": ["hundreds", "tens"],
    "format": "json",
    "include_metadata": True
}

response = requests.post(
    "http://127.0.0.1:5000/api/v2/features/export",
    json=export_data
)
```

## 🛠️ 故障排除

### 常见问题

#### 1. 导入错误
**问题**: `ImportError: No module named 'feature_engine'`
**解决**: 
```bash
pip install feature-engine>=1.6.0
```

#### 2. 数据库连接错误
**问题**: `sqlite3.OperationalError: no such table`
**解决**: 确认数据库文件路径正确，检查数据库结构

#### 3. 内存不足
**问题**: `MemoryError` 或系统变慢
**解决**: 
- 减少批量处理的数据量
- 清理缓存: `engineer.clear_cache()`
- 重启服务

#### 4. API连接失败
**问题**: `requests.exceptions.ConnectionError`
**解决**:
- 确认API服务已启动
- 检查端口是否被占用
- 验证URL地址正确

### 性能优化建议

#### 1. 缓存优化
```python
# 配置更大的缓存
from src.data.cache_optimizer import CacheConfig

config = CacheConfig(
    memory_size=2000,  # 增加内存缓存大小
    db_cache_enabled=True,
    cache_ttl=7200     # 延长缓存时间
)
```

#### 2. 批量处理
```python
# 使用批量接口而不是循环单次请求
# ❌ 低效方式
for issue in issues:
    features = engineer.get_features_with_cache(issue, "hundreds")

# ✅ 高效方式
batch_features = engineer.batch_generate_features(issues, "hundreds")
```

#### 3. 特征选择
```python
# 只获取需要的特征类型
features = engineer.get_features_with_cache("2025001", "hundreds")  # 具体类型
# 而不是
features = engineer.get_features_with_cache("2025001", "all")       # 所有类型
```

## 📞 技术支持

### 日志查看
```python
import logging

# 启用详细日志
logging.basicConfig(level=logging.DEBUG)

# 查看特定组件日志
logger = logging.getLogger('AdvancedFeatureEngineer')
logger.setLevel(logging.DEBUG)
```

### 系统诊断
```python
# 运行系统诊断
python test_p2_performance.py

# 检查API健康状态
curl http://127.0.0.1:5000/api/v2/features/health
```

### 获取帮助
- **项目文档**: 查看 `P2项目最终交付文档.md`
- **API文档**: 访问 `http://127.0.0.1:5000/docs`
- **技术文档**: 查看 `API_v2_文档.md`

## 🎓 最佳实践

### 1. 开发建议
- 使用虚拟环境隔离依赖
- 定期备份重要数据
- 监控系统性能指标
- 及时清理过期缓存

### 2. 生产部署
- 配置适当的缓存大小
- 设置日志轮转
- 监控内存和CPU使用
- 建立数据备份策略

### 3. 性能调优
- 根据使用模式调整缓存配置
- 使用批量接口处理大量数据
- 定期进行性能测试
- 优化特征选择策略

---

**用户手册版本**: v2.0  
**适用系统**: P2高级特征工程系统  
**最后更新**: 2025-01-14

希望这份用户手册能帮助您快速上手并充分利用P2系统的强大功能！
