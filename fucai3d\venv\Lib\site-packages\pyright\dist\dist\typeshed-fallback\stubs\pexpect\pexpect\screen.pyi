from _typeshed import Incomplete

NUL: int
ENQ: int
BEL: int
BS: int
HT: int
LF: int
VT: int
FF: int
CR: int
SO: int
SI: int
XON: int
XOFF: int
CAN: int
SUB: int
ESC: int
DEL: int
SPACE: str
PY3: Incomplete
unicode = str

def constrain(n, min, max): ...

class screen:
    rows: Incomplete
    cols: Incomplete
    encoding: Incomplete
    encoding_errors: Incomplete
    decoder: Incomplete
    cur_r: int
    cur_c: int
    cur_saved_r: int
    cur_saved_c: int
    scroll_row_start: int
    scroll_row_end: Incomplete
    w: Incomplete
    def __init__(self, r: int = 24, c: int = 80, encoding: str = "latin-1", encoding_errors: str = "replace") -> None: ...
    def dump(self): ...
    def pretty(self): ...
    def fill(self, ch=" ") -> None: ...
    def fill_region(self, rs, cs, re, ce, ch=" ") -> None: ...
    def cr(self) -> None: ...
    def lf(self) -> None: ...
    def crlf(self) -> None: ...
    def newline(self) -> None: ...
    def put_abs(self, r, c, ch) -> None: ...
    def put(self, ch) -> None: ...
    def insert_abs(self, r, c, ch) -> None: ...
    def insert(self, ch) -> None: ...
    def get_abs(self, r, c): ...
    def get(self) -> None: ...
    def get_region(self, rs, cs, re, ce): ...
    def cursor_constrain(self) -> None: ...
    def cursor_home(self, r: int = 1, c: int = 1) -> None: ...
    def cursor_back(self, count: int = 1) -> None: ...
    def cursor_down(self, count: int = 1) -> None: ...
    def cursor_forward(self, count: int = 1) -> None: ...
    def cursor_up(self, count: int = 1) -> None: ...
    def cursor_up_reverse(self) -> None: ...
    def cursor_force_position(self, r, c) -> None: ...
    def cursor_save(self) -> None: ...
    def cursor_unsave(self) -> None: ...
    def cursor_save_attrs(self) -> None: ...
    def cursor_restore_attrs(self) -> None: ...
    def scroll_constrain(self) -> None: ...
    def scroll_screen(self) -> None: ...
    def scroll_screen_rows(self, rs, re) -> None: ...
    def scroll_down(self) -> None: ...
    def scroll_up(self) -> None: ...
    def erase_end_of_line(self) -> None: ...
    def erase_start_of_line(self) -> None: ...
    def erase_line(self) -> None: ...
    def erase_down(self) -> None: ...
    def erase_up(self) -> None: ...
    def erase_screen(self) -> None: ...
    def set_tab(self) -> None: ...
    def clear_tab(self) -> None: ...
    def clear_all_tabs(self) -> None: ...
