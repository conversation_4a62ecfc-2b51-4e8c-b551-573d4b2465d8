from typing import Final

XK_BackSpace: Final = 0xFF08
XK_Tab: Final = 0xFF09
XK_Linefeed: Final = 0xFF0A
XK_Clear: Final = 0xFF0B
XK_Return: Final = 0xFF0D
XK_Pause: Final = 0xFF13
XK_Scroll_Lock: Final = 0xFF14
XK_Sys_Req: Final = 0xFF15
XK_Escape: Final = 0xFF1B
XK_Delete: Final = 0xFFFF
XK_Multi_key: Final = 0xFF20
XK_SingleCandidate: Final = 0xFF3C
XK_MultipleCandidate: Final = 0xFF3D
XK_PreviousCandidate: Final = 0xFF3E
XK_Kanji: Final = 0xFF21
XK_Muhenkan: Final = 0xFF22
XK_Henkan_Mode: Final = 0xFF23
XK_Henkan: Final = 0xFF23
XK_Romaji: Final = 0xFF24
XK_Hiragana: Final = 0xFF25
XK_Katakana: Final = 0xFF26
XK_Hiragana_Katakana: Final = 0xFF27
XK_Zenkaku: Final = 0xFF28
XK_Hankaku: Final = 0xFF29
XK_Zenkaku_Hankaku: Final = 0xFF2A
XK_Touroku: Final = 0xFF2B
XK_Massyo: Final = 0xFF2C
XK_Kana_Lock: Final = 0xFF2D
XK_Kana_Shift: Final = 0xFF2E
XK_Eisu_Shift: Final = 0xFF2F
XK_Eisu_toggle: Final = 0xFF30
XK_Zen_Koho: Final = 0xFF3D
XK_Mae_Koho: Final = 0xFF3E
XK_Home: Final = 0xFF50
XK_Left: Final = 0xFF51
XK_Up: Final = 0xFF52
XK_Right: Final = 0xFF53
XK_Down: Final = 0xFF54
XK_Prior: Final = 0xFF55
XK_Page_Up: Final = 0xFF55
XK_Next: Final = 0xFF56
XK_Page_Down: Final = 0xFF56
XK_End: Final = 0xFF57
XK_Begin: Final = 0xFF58
XK_Select: Final = 0xFF60
XK_Print: Final = 0xFF61
XK_Execute: Final = 0xFF62
XK_Insert: Final = 0xFF63
XK_Undo: Final = 0xFF65
XK_Redo: Final = 0xFF66
XK_Menu: Final = 0xFF67
XK_Find: Final = 0xFF68
XK_Cancel: Final = 0xFF69
XK_Help: Final = 0xFF6A
XK_Break: Final = 0xFF6B
XK_Mode_switch: Final = 0xFF7E
XK_script_switch: Final = 0xFF7E
XK_Num_Lock: Final = 0xFF7F
XK_KP_Space: Final = 0xFF80
XK_KP_Tab: Final = 0xFF89
XK_KP_Enter: Final = 0xFF8D
XK_KP_F1: Final = 0xFF91
XK_KP_F2: Final = 0xFF92
XK_KP_F3: Final = 0xFF93
XK_KP_F4: Final = 0xFF94
XK_KP_Home: Final = 0xFF95
XK_KP_Left: Final = 0xFF96
XK_KP_Up: Final = 0xFF97
XK_KP_Right: Final = 0xFF98
XK_KP_Down: Final = 0xFF99
XK_KP_Prior: Final = 0xFF9A
XK_KP_Page_Up: Final = 0xFF9A
XK_KP_Next: Final = 0xFF9B
XK_KP_Page_Down: Final = 0xFF9B
XK_KP_End: Final = 0xFF9C
XK_KP_Begin: Final = 0xFF9D
XK_KP_Insert: Final = 0xFF9E
XK_KP_Delete: Final = 0xFF9F
XK_KP_Equal: Final = 0xFFBD
XK_KP_Multiply: Final = 0xFFAA
XK_KP_Add: Final = 0xFFAB
XK_KP_Separator: Final = 0xFFAC
XK_KP_Subtract: Final = 0xFFAD
XK_KP_Decimal: Final = 0xFFAE
XK_KP_Divide: Final = 0xFFAF
XK_KP_0: Final = 0xFFB0
XK_KP_1: Final = 0xFFB1
XK_KP_2: Final = 0xFFB2
XK_KP_3: Final = 0xFFB3
XK_KP_4: Final = 0xFFB4
XK_KP_5: Final = 0xFFB5
XK_KP_6: Final = 0xFFB6
XK_KP_7: Final = 0xFFB7
XK_KP_8: Final = 0xFFB8
XK_KP_9: Final = 0xFFB9
XK_F1: Final = 0xFFBE
XK_F2: Final = 0xFFBF
XK_F3: Final = 0xFFC0
XK_F4: Final = 0xFFC1
XK_F5: Final = 0xFFC2
XK_F6: Final = 0xFFC3
XK_F7: Final = 0xFFC4
XK_F8: Final = 0xFFC5
XK_F9: Final = 0xFFC6
XK_F10: Final = 0xFFC7
XK_F11: Final = 0xFFC8
XK_L1: Final = 0xFFC8
XK_F12: Final = 0xFFC9
XK_L2: Final = 0xFFC9
XK_F13: Final = 0xFFCA
XK_L3: Final = 0xFFCA
XK_F14: Final = 0xFFCB
XK_L4: Final = 0xFFCB
XK_F15: Final = 0xFFCC
XK_L5: Final = 0xFFCC
XK_F16: Final = 0xFFCD
XK_L6: Final = 0xFFCD
XK_F17: Final = 0xFFCE
XK_L7: Final = 0xFFCE
XK_F18: Final = 0xFFCF
XK_L8: Final = 0xFFCF
XK_F19: Final = 0xFFD0
XK_L9: Final = 0xFFD0
XK_F20: Final = 0xFFD1
XK_L10: Final = 0xFFD1
XK_F21: Final = 0xFFD2
XK_R1: Final = 0xFFD2
XK_F22: Final = 0xFFD3
XK_R2: Final = 0xFFD3
XK_F23: Final = 0xFFD4
XK_R3: Final = 0xFFD4
XK_F24: Final = 0xFFD5
XK_R4: Final = 0xFFD5
XK_F25: Final = 0xFFD6
XK_R5: Final = 0xFFD6
XK_F26: Final = 0xFFD7
XK_R6: Final = 0xFFD7
XK_F27: Final = 0xFFD8
XK_R7: Final = 0xFFD8
XK_F28: Final = 0xFFD9
XK_R8: Final = 0xFFD9
XK_F29: Final = 0xFFDA
XK_R9: Final = 0xFFDA
XK_F30: Final = 0xFFDB
XK_R10: Final = 0xFFDB
XK_F31: Final = 0xFFDC
XK_R11: Final = 0xFFDC
XK_F32: Final = 0xFFDD
XK_R12: Final = 0xFFDD
XK_F33: Final = 0xFFDE
XK_R13: Final = 0xFFDE
XK_F34: Final = 0xFFDF
XK_R14: Final = 0xFFDF
XK_F35: Final = 0xFFE0
XK_R15: Final = 0xFFE0
XK_Shift_L: Final = 0xFFE1
XK_Shift_R: Final = 0xFFE2
XK_Control_L: Final = 0xFFE3
XK_Control_R: Final = 0xFFE4
XK_Caps_Lock: Final = 0xFFE5
XK_Shift_Lock: Final = 0xFFE6
XK_Meta_L: Final = 0xFFE7
XK_Meta_R: Final = 0xFFE8
XK_Alt_L: Final = 0xFFE9
XK_Alt_R: Final = 0xFFEA
XK_Super_L: Final = 0xFFEB
XK_Super_R: Final = 0xFFEC
XK_Hyper_L: Final = 0xFFED
XK_Hyper_R: Final = 0xFFEE
