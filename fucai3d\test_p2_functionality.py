#!/usr/bin/env python3
"""
P2核心模块功能测试脚本
测试P2高级特征工程系统的实际功能和性能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import time
import traceback
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_advanced_feature_engineer():
    """测试高级特征工程器"""
    print("🔧 测试 AdvancedFeatureEngineer...")
    
    try:
        from src.data.advanced_feature_engineer import AdvancedFeatureEngineer
        
        # 初始化
        engineer = AdvancedFeatureEngineer("data/lottery.db")
        print("✅ AdvancedFeatureEngineer 初始化成功")
        
        # 测试获取特征类型
        feature_types = engineer.get_available_feature_types()
        print(f"✅ 可用特征类型: {feature_types}")
        
        # 测试获取单期特征
        start_time = time.time()
        features = engineer.get_features_with_cache("2025001", "hundreds")
        end_time = time.time()
        print(f"✅ 获取百位特征成功，特征数量: {len(features)}, 耗时: {end_time-start_time:.3f}s")
        
        # 测试缓存统计
        cache_stats = engineer.get_cache_stats()
        print(f"✅ 缓存统计: {cache_stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ AdvancedFeatureEngineer 测试失败: {e}")
        traceback.print_exc()
        return False

def test_cache_optimizer():
    """测试缓存优化器"""
    print("\n🔧 测试 CacheOptimizer...")
    
    try:
        from src.data.cache_optimizer import CacheOptimizer, CacheConfig
        
        # 创建配置
        config = CacheConfig(memory_size=100, db_cache_enabled=True)
        optimizer = CacheOptimizer(config)
        print("✅ CacheOptimizer 初始化成功")
        
        # 测试缓存功能
        test_key = "test_key"
        test_data = {"feature1": 1.0, "feature2": 2.0}
        
        # 缓存数据
        optimizer.cache_features(test_key, test_data)
        print("✅ 数据缓存成功")
        
        # 获取缓存数据
        cached_data = optimizer.get_cached_features(test_key)
        if cached_data:
            print(f"✅ 缓存数据获取成功: {cached_data}")
        else:
            print("⚠️ 缓存数据未找到")
        
        # 获取统计信息
        stats = optimizer.get_cache_stats()
        print(f"✅ 缓存统计: 总请求={stats.total_requests}, 命中率={stats.hit_rate:.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ CacheOptimizer 测试失败: {e}")
        traceback.print_exc()
        return False

def test_feature_importance():
    """测试特征重要性分析器"""
    print("\n🔧 测试 FeatureImportanceAnalyzer...")
    
    try:
        from src.data.feature_importance import FeatureImportanceAnalyzer
        import pandas as pd
        import numpy as np
        
        # 创建分析器
        analyzer = FeatureImportanceAnalyzer()
        print("✅ FeatureImportanceAnalyzer 初始化成功")
        
        # 创建测试数据
        np.random.seed(42)
        X = pd.DataFrame({
            'feature1': np.random.randn(100),
            'feature2': np.random.randn(100),
            'feature3': np.random.randn(100)
        })
        y = pd.Series(np.random.randint(0, 10, 100))
        
        # 测试特征重要性分析
        result = analyzer.analyze_feature_importance(X, y)
        print(f"✅ 特征重要性分析完成，分析了 {len(result.feature_ranking)} 个特征")
        
        # 生成报告
        report = analyzer.generate_report(result)
        print(f"✅ 分析报告生成成功，包含 {len(report['recommendations'])} 条建议")
        
        return True
        
    except Exception as e:
        print(f"❌ FeatureImportanceAnalyzer 测试失败: {e}")
        traceback.print_exc()
        return False

def test_predictor_interface():
    """测试预测器特征接口"""
    print("\n🔧 测试 PredictorFeatureInterface...")

    try:
        from src.interfaces.predictor_feature_interface import PredictorFeatureInterface, FeatureConfig

        # 初始化接口
        interface = PredictorFeatureInterface("data/lottery.db")
        print("✅ PredictorFeatureInterface 初始化成功")

        # 测试创建ML pipeline
        start_time = time.time()
        test_issues = ["2025001", "2025002", "2025003", "2025004", "2025005"]
        config = FeatureConfig()
        dataset = interface.create_ml_pipeline(test_issues, config)
        end_time = time.time()
        print(f"✅ 创建ML pipeline成功: X.shape={dataset.X.shape}, y.shape={dataset.y.shape}, 耗时: {end_time-start_time:.3f}s")

        # 测试缓存统计
        cache_stats = interface.get_cache_stats()
        print(f"✅ 缓存统计获取成功: {cache_stats['predictor_type']}")

        return True

    except Exception as e:
        print(f"❌ PredictorFeatureInterface 测试失败: {e}")
        traceback.print_exc()
        return False

def test_specialized_generators():
    """测试专用特征生成器"""
    print("\n🔧 测试专用特征生成器...")
    
    generators = [
        ("hundreds_features", "generate_hundreds_features"),
        ("tens_features", "generate_tens_features"),
        ("units_features", "generate_units_features"),
        ("sum_features", "generate_sum_features"),
        ("span_features", "generate_span_features"),
        ("common_features", "generate_common_features")
    ]
    
    success_count = 0
    
    for module_name, func_name in generators:
        try:
            module = __import__(f"src.data.predictor_features.{module_name}", fromlist=[func_name])
            if hasattr(module, func_name):
                print(f"✅ {module_name}.{func_name} 存在")
                success_count += 1
            else:
                print(f"⚠️ {module_name}.{func_name} 不存在")
        except Exception as e:
            print(f"❌ {module_name} 导入失败: {e}")
    
    print(f"✅ 专用特征生成器测试完成: {success_count}/{len(generators)} 成功")
    return success_count == len(generators)

def test_api_integration():
    """测试API集成"""
    print("\n🔧 测试API集成...")
    
    try:
        from src.api.v2.advanced_features import init_advanced_features_api
        
        # 初始化API
        init_advanced_features_api("data/lottery.db")
        print("✅ API初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ API集成测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始P2核心模块功能测试...")
    print("=" * 60)
    
    # 执行各项测试
    tests = [
        ("AdvancedFeatureEngineer", test_advanced_feature_engineer),
        ("CacheOptimizer", test_cache_optimizer),
        ("FeatureImportanceAnalyzer", test_feature_importance),
        ("PredictorFeatureInterface", test_predictor_interface),
        ("专用特征生成器", test_specialized_generators),
        ("API集成", test_api_integration)
    ]
    
    results = []
    success_count = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                success_count += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 功能测试结果总结")
    print(f"总计测试: {len(tests)} 个模块")
    print(f"成功测试: {success_count} 个")
    print(f"失败测试: {len(tests) - success_count} 个")
    print(f"成功率: {success_count/len(tests)*100:.1f}%")
    
    # 详细结果
    print("\n📋 详细结果:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if success_count == len(tests):
        print("\n🎉 所有P2核心模块功能测试通过！")
        print("✅ P2高级特征工程系统完全可用")
        return True
    else:
        print("\n⚠️ 部分功能测试失败，请检查相关模块")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
