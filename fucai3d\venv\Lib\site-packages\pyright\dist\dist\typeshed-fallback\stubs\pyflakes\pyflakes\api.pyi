from _typeshed import GenericPath
from collections.abc import Iterable, Iterator, Sequence
from re import Pattern
from typing import Final, NoReturn

from pyflakes.reporter import Reporter

__all__ = ["check", "checkPath", "checkRecursive", "iterSourceCode", "main"]

PYTHON_SHEBANG_REGEX: Final[Pattern[bytes]]

def check(codeString: str, filename: str, reporter: Reporter | None = None) -> int: ...
def checkPath(filename: str, reporter: Reporter | None = None) -> int: ...
def isPythonFile(filename: str) -> bool: ...
def iterSourceCode(paths: Iterable[GenericPath[str]]) -> Iterator[GenericPath[str]]: ...
def checkRecursive(paths: Iterable[GenericPath[str]], reporter: Reporter) -> int: ...
def main(prog: str | None = None, args: Sequence[str] | None = None) -> NoReturn: ...
