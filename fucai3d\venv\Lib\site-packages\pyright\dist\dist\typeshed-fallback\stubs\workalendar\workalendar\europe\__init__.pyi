from .austria import Austria as Austria
from .belarus import Belarus as Belarus
from .belgium import Belgium as Belgium
from .bulgaria import Bulgaria as Bulgaria
from .cayman_islands import CaymanIslands as CaymanIslands
from .croatia import Croatia as Croatia
from .cyprus import Cyprus as Cyprus
from .czech_republic import CzechRepublic as CzechRepublic
from .denmark import Denmark as Denmark
from .estonia import Estonia as Estonia
from .european_central_bank import EuropeanCentralBank as EuropeanCentralBank
from .finland import Finland as Finland
from .france import France as France, FranceAlsaceMoselle as FranceAlsaceMoselle
from .georgia import Georgia as Georgia
from .germany import (
    BadenWurttemberg as BadenWurttemberg,
    Bavaria as Bavaria,
    Berlin as Berlin,
    Brandenburg as Brandenburg,
    Bremen as Bremen,
    Germany as Germany,
    Hamburg as Hamburg,
    Hesse as Hesse,
    LowerSaxony as LowerSaxony,
    MecklenburgVorpommern as MecklenburgVorpommern,
    NorthRhineWestphalia as NorthRhineWestphalia,
    RhinelandPalatinate as RhinelandPalatinate,
    Saarland as Saarland,
    Saxony as Saxony,
    SaxonyAnhalt as SaxonyAnhalt,
    SchleswigHolstein as SchleswigHolstein,
    Thuringia as Thuringia,
)
from .greece import Greece as Greece
from .guernsey import Guernsey as Guernsey
from .hungary import Hungary as Hungary
from .iceland import Iceland as Iceland
from .ireland import Ireland as Ireland
from .italy import Italy as Italy
from .latvia import Latvia as Latvia
from .lithuania import Lithuania as Lithuania
from .luxembourg import Luxembourg as Luxembourg
from .malta import Malta as Malta
from .monaco import Monaco as Monaco
from .netherlands import Netherlands as Netherlands, NetherlandsWithSchoolHolidays as NetherlandsWithSchoolHolidays
from .norway import Norway as Norway
from .poland import Poland as Poland
from .portugal import Portugal as Portugal
from .romania import Romania as Romania
from .russia import Russia as Russia
from .scotland import (
    Aberdeen as Aberdeen,
    Angus as Angus,
    Arbroath as Arbroath,
    Ayr as Ayr,
    CarnoustieMonifieth as CarnoustieMonifieth,
    Clydebank as Clydebank,
    DumfriesGalloway as DumfriesGalloway,
    Dundee as Dundee,
    EastDunbartonshire as EastDunbartonshire,
    Edinburgh as Edinburgh,
    Elgin as Elgin,
    Falkirk as Falkirk,
    Fife as Fife,
    Galashiels as Galashiels,
    Glasgow as Glasgow,
    Hawick as Hawick,
    Inverclyde as Inverclyde,
    Inverness as Inverness,
    Kilmarnock as Kilmarnock,
    Lanark as Lanark,
    Linlithgow as Linlithgow,
    Lochaber as Lochaber,
    NorthLanarkshire as NorthLanarkshire,
    Paisley as Paisley,
    Perth as Perth,
    Scotland as Scotland,
    ScottishBorders as ScottishBorders,
    SouthLanarkshire as SouthLanarkshire,
    Stirling as Stirling,
    WestDunbartonshire as WestDunbartonshire,
)
from .serbia import Serbia as Serbia
from .slovakia import Slovakia as Slovakia
from .slovenia import Slovenia as Slovenia
from .spain import (
    Andalusia as Andalusia,
    Aragon as Aragon,
    Asturias as Asturias,
    BalearicIslands as BalearicIslands,
    BasqueCountry as BasqueCountry,
    CanaryIslands as CanaryIslands,
    Cantabria as Cantabria,
    CastileAndLeon as CastileAndLeon,
    CastillaLaMancha as CastillaLaMancha,
    Catalonia as Catalonia,
    CommunityofMadrid as CommunityofMadrid,
    Extremadura as Extremadura,
    Galicia as Galicia,
    LaRioja as LaRioja,
    Murcia as Murcia,
    Navarre as Navarre,
    Spain as Spain,
    ValencianCommunity as ValencianCommunity,
)
from .sweden import Sweden as Sweden
from .switzerland import (
    Aargau as Aargau,
    AppenzellAusserrhoden as AppenzellAusserrhoden,
    AppenzellInnerrhoden as AppenzellInnerrhoden,
    BaselLandschaft as BaselLandschaft,
    BaselStadt as BaselStadt,
    Bern as Bern,
    Fribourg as Fribourg,
    Geneva as Geneva,
    Glarus as Glarus,
    Graubunden as Graubunden,
    Jura as Jura,
    Luzern as Luzern,
    Neuchatel as Neuchatel,
    Nidwalden as Nidwalden,
    Obwalden as Obwalden,
    Schaffhausen as Schaffhausen,
    Schwyz as Schwyz,
    Solothurn as Solothurn,
    StGallen as StGallen,
    Switzerland as Switzerland,
    Thurgau as Thurgau,
    Ticino as Ticino,
    Uri as Uri,
    Valais as Valais,
    Vaud as Vaud,
    Zug as Zug,
    Zurich as Zurich,
)
from .turkey import Turkey as Turkey
from .ukraine import Ukraine as Ukraine
from .united_kingdom import UnitedKingdom as UnitedKingdom, UnitedKingdomNorthernIreland as UnitedKingdomNorthernIreland

__all__ = (
    "Austria",
    "Belarus",
    "Belgium",
    "Bulgaria",
    "CaymanIslands",
    "Croatia",
    "Cyprus",
    "CzechRepublic",
    "Denmark",
    "Estonia",
    "EuropeanCentralBank",
    "Finland",
    "France",
    "FranceAlsaceMoselle",
    "Georgia",
    "Greece",
    "Guernsey",
    "Hungary",
    "Iceland",
    "Ireland",
    "Italy",
    "Latvia",
    "Lithuania",
    "Luxembourg",
    "Malta",
    "Monaco",
    "Netherlands",
    "NetherlandsWithSchoolHolidays",
    "Norway",
    "Poland",
    "Portugal",
    "Romania",
    "Russia",
    "Serbia",
    "Slovakia",
    "Slovenia",
    "Sweden",
    "Switzerland",
    "Ukraine",
    "UnitedKingdom",
    "UnitedKingdomNorthernIreland",
    "Turkey",
    # Germany
    "Germany",
    "BadenWurttemberg",
    "Bavaria",
    "Berlin",
    "Brandenburg",
    "Bremen",
    "Hamburg",
    "Hesse",
    "MecklenburgVorpommern",
    "LowerSaxony",
    "NorthRhineWestphalia",
    "RhinelandPalatinate",
    "Saarland",
    "Saxony",
    "SaxonyAnhalt",
    "SchleswigHolstein",
    "Thuringia",
    # Scotland
    "Scotland",
    "Aberdeen",
    "Angus",
    "Arbroath",
    "Ayr",
    "CarnoustieMonifieth",
    "Clydebank",
    "DumfriesGalloway",
    "Dundee",
    "EastDunbartonshire",
    "Edinburgh",
    "Elgin",
    "Falkirk",
    "Fife",
    "Galashiels",
    "Glasgow",
    "Hawick",
    "Inverclyde",
    "Inverness",
    "Kilmarnock",
    "Lanark",
    "Linlithgow",
    "Lochaber",
    "NorthLanarkshire",
    "Paisley",
    "Perth",
    "ScottishBorders",
    "SouthLanarkshire",
    "Stirling",
    "WestDunbartonshire",
    # Spain
    "Spain",
    "Andalusia",
    "Aragon",
    "Catalonia",
    "CastileAndLeon",
    "CastillaLaMancha",
    "CanaryIslands",
    "Extremadura",
    "Galicia",
    "BalearicIslands",
    "LaRioja",
    "CommunityofMadrid",
    "Murcia",
    "Navarre",
    "Asturias",
    "BasqueCountry",
    "Cantabria",
    "ValencianCommunity",
    # Switzerland
    "Switzerland",
    "Aargau",
    "AppenzellInnerrhoden",
    "AppenzellAusserrhoden",
    "Bern",
    "BaselLandschaft",
    "BaselStadt",
    "Fribourg",
    "Geneva",
    "Glarus",
    "Graubunden",
    "Jura",
    "Luzern",
    "Neuchatel",
    "Nidwalden",
    "Obwalden",
    "StGallen",
    "Schaffhausen",
    "Solothurn",
    "Schwyz",
    "Thurgau",
    "Ticino",
    "Uri",
    "Vaud",
    "Valais",
    "Zug",
    "Zurich",
)
