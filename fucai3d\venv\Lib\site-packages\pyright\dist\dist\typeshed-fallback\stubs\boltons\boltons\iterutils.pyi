from _typeshed import Incomplete
from collections.abc import Generator

def is_iterable(obj) -> bool: ...
def is_scalar(obj) -> bool: ...
def is_collection(obj) -> bool: ...
def split(src, sep=None, maxsplit=None): ...
def split_iter(src, sep=None, maxsplit=None) -> Generator[Incomplete, None, Incomplete]: ...
def lstrip(iterable, strip_value=None): ...
def lstrip_iter(iterable, strip_value=None) -> Generator[Incomplete, None, None]: ...
def rstrip(iterable, strip_value=None): ...
def rstrip_iter(iterable, strip_value=None) -> Generator[Incomplete, None, None]: ...
def strip(iterable, strip_value=None): ...
def strip_iter(iterable, strip_value=None): ...
def chunked(src, size, count=None, **kw): ...
def chunked_iter(src, size, **kw) -> Generator[Incomplete, None, Incomplete]: ...
def chunk_ranges(
    input_size: int, chunk_size: int, input_offset: int = 0, overlap_size: int = 0, align: bool = False
) -> Generator[tuple[int, int], None, None]: ...
def pairwise(src, end=...): ...
def pairwise_iter(src, end=...): ...
def windowed(src, size, fill=...): ...
def windowed_iter(src, size, fill=...): ...
def xfrange(stop, start=None, step: float = 1.0) -> Generator[Incomplete, None, None]: ...
def frange(stop, start=None, step: float = 1.0): ...
def backoff(start, stop, count=None, factor: float = 2.0, jitter: bool = False): ...
def backoff_iter(start, stop, count=None, factor: float = 2.0, jitter: bool = False) -> Generator[Incomplete, None, None]: ...
def bucketize(src, key=..., value_transform=None, key_filter=None): ...
def partition(src, key=...): ...
def unique(src, key=None): ...
def unique_iter(src, key=None) -> Generator[Incomplete, None, Incomplete]: ...
def redundant(src, key=None, groups: bool = False): ...
def one(src, default=None, key=None): ...
def first(iterable, default=None, key=None): ...
def flatten_iter(iterable) -> Generator[Incomplete, None, None]: ...
def flatten(iterable): ...
def same(iterable, ref=...): ...
def default_visit(path, key, value): ...
def default_enter(path, key, value): ...
def default_exit(path, key, old_parent, new_parent, new_items): ...
def remap(root, visit=..., enter=..., exit=..., **kwargs): ...

class PathAccessError(KeyError, IndexError, TypeError):
    exc: Incomplete
    seg: Incomplete
    path: Incomplete
    def __init__(self, exc, seg, path) -> None: ...

def get_path(root, path, default=...): ...
def research(root, query=..., reraise: bool = False, enter=...): ...

class GUIDerator:
    size: Incomplete
    count: Incomplete
    def __init__(self, size: int = 24) -> None: ...
    pid: Incomplete
    salt: Incomplete
    def reseed(self) -> None: ...
    def __iter__(self): ...
    def __next__(self): ...
    next: Incomplete

class SequentialGUIDerator(GUIDerator):
    start: Incomplete
    def reseed(self) -> None: ...
    def __next__(self): ...
    next: Incomplete

guid_iter: Incomplete
seq_guid_iter: Incomplete

def soft_sorted(iterable, first=None, last=None, key=None, reverse: bool = False): ...
def untyped_sorted(iterable, key=None, reverse: bool = False): ...
