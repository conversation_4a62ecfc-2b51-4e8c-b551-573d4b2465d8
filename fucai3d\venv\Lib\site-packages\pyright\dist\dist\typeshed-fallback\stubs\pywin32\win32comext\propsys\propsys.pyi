from typing_extensions import TypeAlias

import _win32typing
from win32.lib.pywintypes import com_error

error: TypeAlias = com_error  # noqa: Y042

def PSGetItemPropertyHandler(
    Item: _win32typing.PyIShellItem, riid: _win32typing.PyIID, ReadWrite: int, /
) -> _win32typing.PyIPropertyStore: ...
def PSGetPropertyDescription(
    Key: _win32typing.PyPROPERTYKEY, riid: _win32typing.PyIID, /
) -> _win32typing.PyIPropertyDescription: ...
def PSGetPropertySystem(riid: _win32typing.PyIID, /) -> _win32typing.PyIPropertySystem: ...
def PSGetNameFromPropertyKey(Key: _win32typing.PyPROPERTYKEY, /) -> str: ...
def PSGetPropertyKeyFromName(Name, /) -> _win32typing.PyPROPERTYKEY: ...
def PSRegisterPropertySchema(filename, /) -> None: ...
def PSUnregisterPropertySchema(filename, /) -> None: ...
def SHGetPropertyStoreFromParsingName(
    Path: str, BindCtx: _win32typing.PyIBindCtx | None = ..., Flags: int = ..., riid: _win32typing.PyIID | str = ..., /
) -> _win32typing.PyIPropertyStore: ...
def StgSerializePropVariant(propvar: _win32typing.PyPROPVARIANT, /): ...
def StgDeserializePropVariant(prop, /) -> _win32typing.PyPROPVARIANT: ...
def PSCreateMemoryPropertyStore(riid: _win32typing.PyIID, /) -> _win32typing.PyIPropertyStore: ...
def PSCreatePropertyStoreFromPropertySetStorage(
    pss: _win32typing.PyIPropertySetStorage, Mode, riid: _win32typing.PyIID, /
) -> _win32typing.PyIPropertyStore: ...
def PSLookupPropertyHandlerCLSID(FilePath, /) -> _win32typing.PyIID: ...
def SHGetPropertyStoreForWindow(hwnd: int, riid: _win32typing.PyIID, /) -> _win32typing.PyIPropertyStore: ...
def PSGetPropertyFromPropertyStorage(ps, key: _win32typing.PyPROPERTYKEY, /) -> _win32typing.PyPROPVARIANT: ...
def PSGetNamedPropertyFromPropertyStorage(ps, name, /) -> _win32typing.PyPROPVARIANT: ...
def PSCreateSimplePropertyChange(
    flags, key: _win32typing.PyPROPERTYKEY, val: _win32typing.PyPROPVARIANT, riid: _win32typing.PyIID, /
) -> _win32typing.PyIPropertyChange: ...
def PSCreatePropertyChangeArray() -> _win32typing.PyIPropertyChangeArray: ...
def SHSetDefaultProperties(
    hwnd: int,
    Item: _win32typing.PyIShellItem,
    FileOpFlags: int = ...,
    Sink: _win32typing.PyGFileOperationProgressSink | None = ...,
    /,
) -> None: ...

IID_IInitializeWithFile: _win32typing.PyIID
IID_IInitializeWithStream: _win32typing.PyIID
IID_INamedPropertyStore: _win32typing.PyIID
IID_IObjectWithPropertyKey: _win32typing.PyIID
IID_IPersistSerializedPropStorage: _win32typing.PyIID
IID_IPropertyChange: _win32typing.PyIID
IID_IPropertyChangeArray: _win32typing.PyIID
IID_IPropertyDescription: _win32typing.PyIID
IID_IPropertyDescriptionAliasInfo: _win32typing.PyIID
IID_IPropertyDescriptionList: _win32typing.PyIID
IID_IPropertyDescriptionSearchInfo: _win32typing.PyIID
IID_IPropertyEnumType: _win32typing.PyIID
IID_IPropertyEnumTypeList: _win32typing.PyIID
IID_IPropertyStore: _win32typing.PyIID
IID_IPropertyStoreCache: _win32typing.PyIID
IID_IPropertyStoreCapabilities: _win32typing.PyIID
IID_IPropertySystem: _win32typing.PyIID
PROPVARIANTType = _win32typing.PyPROPVARIANT
