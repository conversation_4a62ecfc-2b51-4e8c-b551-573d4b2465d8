from nanoleafapi.nanoleaf import Nanoleaf

class NanoleafDigitalTwin:
    nanoleaf: Nanoleaf
    tile_dict: dict[str, dict[str, int]]
    def __init__(self, nl: Nanoleaf) -> None: ...
    def set_color(self, panel_id: int, rgb: tuple[int, int, int]) -> None: ...
    def set_all_colors(self, rgb: tuple[int, int, int]) -> None: ...
    def get_ids(self) -> list[int]: ...
    def get_color(self, panel_id: int) -> tuple[int, int, int]: ...
    def get_all_colors(self) -> dict[int, tuple[int, int, int]]: ...
    def sync(self) -> bool: ...
