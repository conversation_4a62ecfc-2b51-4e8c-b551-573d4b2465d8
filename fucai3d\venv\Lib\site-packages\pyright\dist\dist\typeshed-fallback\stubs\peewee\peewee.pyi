import itertools
import logging
import threading
from _typeshed import Incomplete, SupportsKeysAndGetItem
from collections.abc import Callable, Generator, Iterable, Iterator
from types import TracebackType
from typing import Any, ClassVar, Literal, NamedTuple, TypeVar
from typing_extensions import Self

class NullHandler(logging.Handler):
    def emit(self, record) -> None: ...

text_type = str
bytes_type = bytes
buffer_type = memoryview
basestring = str
long = int
izip_longest = itertools.zip_longest

_VT = TypeVar("_VT")
_F = TypeVar("_F", bound=Callable[..., Any])

class attrdict(dict[str, _VT]):
    def __getattr__(self, attr: str) -> _VT: ...
    def __setattr__(self, attr: str, value: _VT) -> None: ...
    # calls dict.update()
    def __iadd__(self, rhs: SupportsKeysAndGetItem[str, _VT] | Iterable[tuple[str, _VT]]) -> Self: ...
    def __add__(self, rhs: SupportsKeysAndGetItem[str, _VT] | Iterable[tuple[str, _VT]]) -> attrdict[_VT]: ...

OP: Incomplete
DJANGO_MAP: Incomplete
JOIN: Incomplete
PREFETCH_TYPE: attrdict[int]

def chunked(it, n) -> Generator[Incomplete, None, None]: ...

class _callable_context_manager:
    def __call__(self, fn): ...

class Proxy:
    def __init__(self) -> None: ...
    obj: Incomplete
    def initialize(self, obj) -> None: ...
    def attach_callback(self, callback): ...
    def passthrough(method): ...
    __enter__: Incomplete
    __exit__: Incomplete
    def __getattr__(self, attr: str): ...
    def __setattr__(self, attr: str, value) -> None: ...

class DatabaseProxy(Proxy):
    def connection_context(self): ...
    def atomic(self, *args, **kwargs): ...
    def manual_commit(self): ...
    def transaction(self, *args, **kwargs): ...
    def savepoint(self): ...
    @property
    def Model(self) -> type[Model]: ...

class ModelDescriptor: ...

class AliasManager:
    def __init__(self) -> None: ...
    @property
    def mapping(self): ...
    def add(self, source): ...
    def get(self, source, any_depth: bool = ...): ...
    def __getitem__(self, source): ...
    def __setitem__(self, source, alias) -> None: ...
    def push(self) -> None: ...
    def pop(self) -> None: ...

class State:
    def __new__(cls, scope=..., parentheses: bool = ..., **kwargs): ...
    def __call__(self, scope: Incomplete | None = ..., parentheses: Incomplete | None = ..., **kwargs): ...
    def __getattr__(self, attr_name: str): ...

class Context:
    stack: Incomplete
    alias_manager: Incomplete
    state: Incomplete
    def __init__(self, **settings) -> None: ...
    def as_new(self): ...
    def column_sort_key(self, item): ...
    @property
    def scope(self): ...
    @property
    def parentheses(self): ...
    @property
    def subquery(self): ...
    def __call__(self, **overrides): ...
    scope_normal: Incomplete
    scope_source: Incomplete
    scope_values: Incomplete
    scope_cte: Incomplete
    scope_column: Incomplete
    def __enter__(self): ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...
    def push_alias(self) -> Generator[None]: ...
    def sql(self, obj): ...
    def literal(self, keyword): ...
    def value(self, value, converter: Incomplete | None = ..., add_param: bool = ...): ...
    def __sql__(self, ctx): ...
    def parse(self, node): ...
    def query(self): ...

class Node:
    __isabstractmethod__: bool
    def clone(self): ...
    def __sql__(self, ctx) -> None: ...
    @staticmethod
    def copy(method): ...
    def coerce(self, _coerce: bool = ...): ...
    def is_alias(self): ...
    def unwrap(self): ...

class ColumnFactory:
    node: Node
    def __init__(self, node: Node) -> None: ...
    def __getattr__(self, attr: str) -> Column: ...
    def __getitem__(self, attr: str) -> Column: ...

class _DynamicColumn:
    def __get__(self, instance, instance_type: Incomplete | None = ...): ...

class _ExplicitColumn:
    def __get__(self, instance, instance_type: Incomplete | None = ...): ...

class Star(Node):
    def __init__(self, source) -> None: ...
    def __sql__(self, ctx): ...

class Source(Node):
    c: Incomplete
    def __init__(self, alias: Incomplete | None = ...) -> None: ...
    def alias(self, name) -> Self: ...
    def select(self, *columns): ...
    @property
    def __star__(self) -> Star: ...
    def join(self, dest, join_type=..., on: Incomplete | None = ...): ...
    def left_outer_join(self, dest, on: Incomplete | None = ...): ...
    def cte(self, name, recursive: bool = ..., columns: Incomplete | None = ..., materialized: Incomplete | None = ...): ...
    def get_sort_key(self, ctx): ...
    def apply_alias(self, ctx): ...
    def apply_column(self, ctx): ...

class _HashableSource:
    def __init__(self, *args, **kwargs) -> None: ...
    def alias(self, name) -> Self: ...
    def __hash__(self) -> int: ...
    def __eq__(self, other) -> Expression | bool: ...  # type: ignore[override]
    def __ne__(self, other) -> Expression | bool: ...  # type: ignore[override]
    __lt__: Callable[[Self, Any], Expression]
    __le__: Callable[[Self, Any], Expression]
    __gt__: Callable[[Self, Any], Expression]
    __ge__: Callable[[Self, Any], Expression]

class BaseTable(Source):
    __and__: Incomplete
    __add__: Incomplete
    __sub__: Incomplete
    __or__: Incomplete
    __mul__: Incomplete
    __rand__: Incomplete
    __radd__: Incomplete
    __rsub__: Incomplete
    __ror__: Incomplete
    __rmul__: Incomplete

class _BoundTableContext(_callable_context_manager):
    table: Incomplete
    database: Incomplete
    def __init__(self, table, database) -> None: ...
    def __enter__(self): ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...

class Table(_HashableSource, BaseTable):  # type: ignore[misc]
    __name__: Incomplete
    c: Incomplete
    primary_key: Incomplete
    def __init__(
        self,
        name,
        columns: Incomplete | None = ...,
        primary_key: Incomplete | None = ...,
        schema: str | None = None,
        alias: Incomplete | None = ...,
        _model: Incomplete | None = ...,
        _database=None,
    ) -> None: ...
    def clone(self): ...
    def bind(self, database=None): ...
    def bind_ctx(self, database=None): ...
    def select(self, *columns): ...
    def insert(self, insert: Incomplete | None = ..., columns: Incomplete | None = ..., **kwargs): ...
    def replace(self, insert: Incomplete | None = ..., columns: Incomplete | None = ..., **kwargs): ...
    def update(self, update: Incomplete | None = ..., **kwargs): ...
    def delete(self): ...
    def __sql__(self, ctx): ...

class Join(BaseTable):
    lhs: Incomplete
    rhs: Incomplete
    join_type: Incomplete
    def __init__(self, lhs, rhs, join_type=..., on: Incomplete | None = ..., alias: Incomplete | None = ...) -> None: ...
    def on(self, predicate): ...
    def __sql__(self, ctx): ...

class ValuesList(_HashableSource, BaseTable):  # type: ignore[misc]
    def __init__(self, values, columns: Incomplete | None = ..., alias: Incomplete | None = ...) -> None: ...
    def columns(self, *names) -> Self: ...
    def __sql__(self, ctx): ...

class CTE(_HashableSource, Source):  # type: ignore[misc]
    def __init__(
        self, name, query, recursive: bool = ..., columns: Incomplete | None = ..., materialized: Incomplete | None = ...
    ) -> None: ...
    def select_from(self, *columns): ...
    def union_all(self, rhs): ...
    __add__: Incomplete
    def union(self, rhs): ...
    __or__: Incomplete
    def __sql__(self, ctx): ...

class ColumnBase(Node):
    def converter(self, converter: Incomplete | None = ...) -> Self: ...
    def alias(self, alias): ...
    def unalias(self): ...
    def bind_to(self, dest): ...
    def cast(self, as_type): ...
    def asc(self, collation: Incomplete | None = ..., nulls: Incomplete | None = ...): ...
    __pos__: Incomplete
    def desc(self, collation: Incomplete | None = ..., nulls: Incomplete | None = ...): ...
    __neg__: Incomplete
    def __invert__(self): ...
    __and__: Incomplete
    __or__: Incomplete
    def __add__(self, rhs: Any) -> Expression: ...
    __sub__: Incomplete
    __mul__: ClassVar[Callable[[Self, Any], Expression]]
    __div__: ClassVar[Callable[[Self, Any], Expression]]
    __truediv__: ClassVar[Callable[[Self, Any], Expression]]
    __xor__: ClassVar[Callable[[Self, Any], Expression]]
    def __radd__(self, rhs: Any) -> Expression: ...
    __rsub__: ClassVar[Callable[[Self, Any], Expression]]
    __rmul__: ClassVar[Callable[[Self, Any], Expression]]
    __rdiv__: ClassVar[Callable[[Self, Any], Expression]]
    __rtruediv__: ClassVar[Callable[[Self, Any], Expression]]
    __rand__: ClassVar[Callable[[Self, Any], Expression]]
    __ror__: ClassVar[Callable[[Self, Any], Expression]]
    __rxor__: ClassVar[Callable[[Self, Any], Expression]]
    def __eq__(self, rhs) -> Expression: ...  # type: ignore[override]
    def __ne__(self, rhs) -> Expression: ...  # type: ignore[override]
    __lt__: ClassVar[Callable[[Self, Any], Expression]]
    __le__: ClassVar[Callable[[Self, Any], Expression]]
    __gt__: ClassVar[Callable[[Self, Any], Expression]]
    __ge__: ClassVar[Callable[[Self, Any], Expression]]
    __lshift__: ClassVar[Callable[[Self, Any], Expression]]
    __rshift__: ClassVar[Callable[[Self, Any], Expression]]
    __mod__: ClassVar[Callable[[Self, Any], Expression]]
    __pow__: ClassVar[Callable[[Self, Any], Expression]]
    like: ClassVar[Callable[[Self, Any], Expression]]
    ilike: ClassVar[Callable[[Self, Any], Expression]]
    bin_and: ClassVar[Callable[[Self, Any], Expression]]
    bin_or: ClassVar[Callable[[Self, Any], Expression]]
    in_: ClassVar[Callable[[Self, Any], Expression]]
    not_in: ClassVar[Callable[[Self, Any], Expression]]
    regexp: ClassVar[Callable[[Self, Any], Expression]]
    iregexp: ClassVar[Callable[[Self, Any], Expression]]
    def is_null(self, is_null: bool = ...) -> Expression: ...
    def contains(self, rhs) -> Expression: ...
    def startswith(self, rhs) -> Expression: ...
    def endswith(self, rhs) -> Expression: ...
    def between(self, lo, hi) -> Expression: ...
    def concat(self, rhs): ...
    def __getitem__(self, item): ...
    __iter__: Incomplete
    def distinct(self): ...
    def collate(self, collation): ...
    def get_sort_key(self, ctx): ...

class Column(ColumnBase):
    source: Incomplete
    name: Incomplete
    def __init__(self, source, name) -> None: ...
    def get_sort_key(self, ctx): ...
    def __hash__(self) -> int: ...
    def __sql__(self, ctx): ...

class WrappedNode(ColumnBase):
    node: Incomplete
    def __init__(self, node) -> None: ...
    def is_alias(self): ...
    def unwrap(self): ...

class EntityFactory:
    node: Incomplete
    def __init__(self, node) -> None: ...
    def __getattr__(self, attr: str): ...

class _DynamicEntity:
    def __get__(self, instance, instance_type: Incomplete | None = ...): ...

class Alias(WrappedNode):
    c: Incomplete
    def __init__(self, node, alias) -> None: ...
    def __hash__(self) -> int: ...
    @property
    def name(self): ...
    @name.setter
    def name(self, value) -> None: ...
    def alias(self, alias: Incomplete | None = ...): ...
    def unalias(self): ...
    def is_alias(self): ...
    def __sql__(self, ctx): ...

class BindTo(WrappedNode):
    dest: Incomplete
    def __init__(self, node, dest) -> None: ...
    def __sql__(self, ctx): ...

class Negated(WrappedNode):
    def __invert__(self): ...
    def __sql__(self, ctx): ...

class BitwiseMixin:
    def __and__(self, other): ...
    def __or__(self, other): ...
    def __sub__(self, other): ...
    def __invert__(self): ...

class BitwiseNegated(BitwiseMixin, WrappedNode):
    def __invert__(self): ...
    def __sql__(self, ctx): ...

class Value(ColumnBase):
    value: Incomplete
    converter: Incomplete
    multi: Incomplete
    values: Incomplete
    def __init__(self, value, converter: Incomplete | None = ..., unpack: bool = ...) -> None: ...
    def __sql__(self, ctx): ...

class ValueLiterals(WrappedNode):
    def __sql__(self, ctx): ...

def AsIs(value): ...

class Cast(WrappedNode):
    def __init__(self, node, cast) -> None: ...
    def __sql__(self, ctx): ...

class Ordering(WrappedNode):
    direction: Incomplete
    collation: Incomplete
    nulls: Incomplete
    def __init__(self, node, direction, collation: Incomplete | None = ..., nulls: Incomplete | None = ...) -> None: ...
    def collate(self, collation: Incomplete | None = ...): ...
    def __sql__(self, ctx): ...

class Expression(ColumnBase):
    lhs: Incomplete
    op: Incomplete
    rhs: Incomplete
    flat: Incomplete
    def __init__(self, lhs, op, rhs, flat: bool = ...) -> None: ...
    def __sql__(self, ctx): ...

class StringExpression(Expression):
    def __add__(self, rhs): ...
    def __radd__(self, lhs): ...

class Entity(ColumnBase):
    def __init__(self, *path) -> None: ...
    def __getattr__(self, attr: str): ...
    def get_sort_key(self, ctx): ...
    def __hash__(self) -> int: ...
    def __sql__(self, ctx): ...

class SQL(ColumnBase):
    sql: Incomplete
    params: Incomplete
    def __init__(self, sql, params: Incomplete | None = ...) -> None: ...
    def __sql__(self, ctx): ...

def Check(constraint, name: Incomplete | None = ...): ...

class Function(ColumnBase):
    no_coerce_functions: ClassVar[set[str]]
    name: Incomplete
    arguments: Incomplete
    def __init__(self, name, arguments, coerce: bool = ..., python_value: Incomplete | None = ...) -> None: ...
    def __getattr__(self, attr: str): ...
    def filter(self, where: Incomplete | None = ...) -> Self: ...
    def order_by(self, *ordering) -> Self: ...
    def python_value(self, func: Incomplete | None = ...) -> Self: ...
    def over(
        self,
        partition_by: Incomplete | None = ...,
        order_by: Incomplete | None = ...,
        start: Incomplete | None = ...,
        end: Incomplete | None = ...,
        frame_type: Incomplete | None = ...,
        window: Incomplete | None = ...,
        exclude: Incomplete | None = ...,
    ): ...
    def __sql__(self, ctx): ...

fn: Incomplete

class Window(Node):
    CURRENT_ROW: Incomplete
    GROUP: Incomplete
    TIES: Incomplete
    NO_OTHERS: Incomplete
    GROUPS: str
    RANGE: str
    ROWS: str
    partition_by: Incomplete
    order_by: Incomplete
    start: Incomplete
    end: Incomplete
    frame_type: Incomplete
    def __init__(
        self,
        partition_by: Incomplete | None = ...,
        order_by: Incomplete | None = ...,
        start: Incomplete | None = ...,
        end: Incomplete | None = ...,
        frame_type: Incomplete | None = ...,
        extends: Incomplete | None = ...,
        exclude: Incomplete | None = ...,
        alias: Incomplete | None = ...,
        _inline: bool = ...,
    ) -> None: ...
    def alias(self, alias: Incomplete | None = ...): ...
    def as_range(self) -> Self: ...
    def as_rows(self) -> Self: ...
    def as_groups(self) -> Self: ...
    def extends(self, window: Incomplete | None = ...) -> Self: ...
    def exclude(self, frame_exclusion: Incomplete | None = ...) -> Self: ...
    @staticmethod
    def following(value: Incomplete | None = ...): ...
    @staticmethod
    def preceding(value: Incomplete | None = ...): ...
    def __sql__(self, ctx): ...

class WindowAlias(Node):
    window: Incomplete
    def __init__(self, window) -> None: ...
    def alias(self, window_alias): ...
    def __sql__(self, ctx): ...

class ForUpdate(Node):
    def __init__(self, expr, of: Incomplete | None = ..., nowait: Incomplete | None = ...) -> None: ...
    def __sql__(self, ctx): ...

class Case(ColumnBase):
    predicate: Incomplete
    expression_tuples: Incomplete
    default: Incomplete | None
    def __init__(self, predicate, expression_tuples, default=None) -> None: ...
    def __sql__(self, ctx): ...

class NodeList(ColumnBase):
    nodes: Incomplete
    glue: Incomplete
    parens: Incomplete
    def __init__(self, nodes, glue: str = ..., parens: bool = ...) -> None: ...
    def __sql__(self, ctx): ...

class _Namespace(Node):
    def __init__(self, name) -> None: ...
    def __getattr__(self, attr: str): ...
    __getitem__: Incomplete

class NamespaceAttribute(ColumnBase):
    def __init__(self, namespace, attribute) -> None: ...
    def __sql__(self, ctx): ...

EXCLUDED: Incomplete

class DQ(ColumnBase):
    query: Incomplete
    def __init__(self, **query) -> None: ...
    def __invert__(self) -> Self: ...
    def clone(self): ...

Tuple: Incomplete

class QualifiedNames(WrappedNode):
    def __sql__(self, ctx): ...

class OnConflict(Node):
    def __init__(
        self,
        action: Incomplete | None = ...,
        update: Incomplete | None = ...,
        preserve: Incomplete | None = ...,
        where: Incomplete | None = ...,
        conflict_target: Incomplete | None = ...,
        conflict_where: Incomplete | None = ...,
        conflict_constraint: Incomplete | None = ...,
    ) -> None: ...
    def get_conflict_statement(self, ctx, query): ...
    def get_conflict_update(self, ctx, query): ...
    def preserve(self, *columns) -> Self: ...
    def update(self, _data: Incomplete | None = ..., **kwargs) -> Self: ...
    def where(self, *expressions) -> Self: ...
    def conflict_target(self, *constraints) -> Self: ...
    def conflict_where(self, *expressions) -> Self: ...
    def conflict_constraint(self, constraint) -> Self: ...

class BaseQuery(Node):
    default_row_type: Incomplete
    def __init__(self, _database=None, **kwargs) -> None: ...
    def bind(self, database=None): ...
    def clone(self): ...
    def dicts(self, as_dict: bool = ...) -> Self: ...
    def tuples(self, as_tuple: bool = ...) -> Self: ...
    def namedtuples(self, as_namedtuple: bool = ...) -> Self: ...
    def objects(self, constructor: Incomplete | None = ...) -> Self: ...
    def __sql__(self, ctx) -> None: ...
    def sql(self): ...
    def execute(self, database=None): ...
    def iterator(self, database=None): ...
    def __iter__(self): ...
    def __getitem__(self, value): ...
    def __len__(self) -> int: ...

class RawQuery(BaseQuery):
    def __init__(self, sql: Incomplete | None = ..., params: Incomplete | None = ..., **kwargs) -> None: ...
    def __sql__(self, ctx): ...

class Query(BaseQuery):
    def __init__(
        self,
        where: Incomplete | None = ...,
        order_by: Incomplete | None = ...,
        limit: Incomplete | None = ...,
        offset: Incomplete | None = ...,
        **kwargs,
    ) -> None: ...
    def with_cte(self, *cte_list) -> Self: ...
    def where(self, *expressions) -> Self: ...
    def orwhere(self, *expressions) -> Self: ...
    def order_by(self, *values) -> Self: ...
    def order_by_extend(self, *values) -> Self: ...
    def limit(self, value: Incomplete | None = ...) -> Self: ...
    def offset(self, value: Incomplete | None = ...) -> Self: ...
    def paginate(self, page, paginate_by: int = ...) -> Self: ...
    def __sql__(self, ctx): ...

class SelectQuery(Query):
    union_all: Incomplete
    __add__: Incomplete
    union: Incomplete
    __or__: Incomplete
    intersect: Incomplete
    __and__: Incomplete
    except_: Incomplete
    __sub__: Incomplete
    __radd__: Incomplete
    __ror__: Incomplete
    __rand__: Incomplete
    __rsub__: Incomplete
    def select_from(self, *columns): ...

class SelectBase(_HashableSource, Source, SelectQuery):  # type: ignore[misc]
    def peek(self, database=None, n: int = ...): ...
    def first(self, database=None, n: int = ...): ...
    def scalar(self, database=None, as_tuple: bool = ..., as_dict: bool = ...): ...
    def scalars(self, database=None) -> Generator[Incomplete, None, None]: ...
    def count(self, database=None, clear_limit: bool = ...): ...
    def exists(self, database=None): ...
    def get(self, database=None): ...

class CompoundSelectQuery(SelectBase):
    lhs: Incomplete
    op: Incomplete
    rhs: Incomplete
    def __init__(self, lhs, op, rhs) -> None: ...
    def exists(self, database=None): ...
    def __sql__(self, ctx): ...

class Select(SelectBase):
    def __init__(
        self,
        from_list: Incomplete | None = ...,
        columns: Incomplete | None = ...,
        group_by: Incomplete | None = ...,
        having: Incomplete | None = ...,
        distinct: Incomplete | None = ...,
        windows: Incomplete | None = ...,
        for_update: Incomplete | None = ...,
        for_update_of: Incomplete | None = ...,
        nowait: Incomplete | None = ...,
        lateral: Incomplete | None = ...,
        **kwargs,
    ) -> None: ...
    def clone(self): ...
    def columns(self, *columns, **kwargs) -> Self: ...
    select: Incomplete
    def select_extend(self, *columns) -> Self: ...
    @property
    def selected_columns(self): ...
    @selected_columns.setter
    def selected_columns(self, value) -> None: ...
    def from_(self, *sources) -> Self: ...
    def join(self, dest, join_type=..., on: Incomplete | None = ...) -> Self: ...
    def left_outer_join(self, dest, on: Incomplete | None = ...): ...
    def group_by(self, *columns) -> Self: ...
    def group_by_extend(self, *values): ...
    def having(self, *expressions) -> Self: ...
    def distinct(self, *columns) -> Self: ...
    def window(self, *windows) -> Self: ...
    def for_update(self, for_update: bool = ..., of: Incomplete | None = ..., nowait: Incomplete | None = ...) -> Self: ...
    def lateral(self, lateral: bool = ...) -> Self: ...
    def __sql_selection__(self, ctx, is_subquery: bool = ...): ...
    def __sql__(self, ctx): ...

class _WriteQuery(Query):
    table: Incomplete
    def __init__(self, table, returning: Incomplete | None = ..., **kwargs) -> None: ...
    def cte(self, name, recursive: bool = ..., columns: Incomplete | None = ..., materialized: Incomplete | None = ...): ...
    def returning(self, *returning) -> Self: ...
    def apply_returning(self, ctx): ...
    def execute_returning(self, database): ...
    def handle_result(self, database, cursor): ...
    def __sql__(self, ctx): ...

class Update(_WriteQuery):
    def __init__(self, table, update: Incomplete | None = ..., **kwargs) -> None: ...
    def from_(self, *sources) -> Self: ...
    def __sql__(self, ctx): ...

class Insert(_WriteQuery):
    SIMPLE: int
    QUERY: int
    MULTI: int

    class DefaultValuesException(Exception): ...

    def __init__(
        self,
        table,
        insert: Incomplete | None = ...,
        columns: Incomplete | None = ...,
        on_conflict: Incomplete | None = ...,
        **kwargs,
    ) -> None: ...
    def where(self, *expressions) -> None: ...  # type: ignore[override]
    def as_rowcount(self, _as_rowcount: bool = ...) -> Self: ...
    def on_conflict_ignore(self, ignore: bool = ...) -> Self: ...
    def on_conflict_replace(self, replace: bool = ...) -> Self: ...
    def on_conflict(self, *args, **kwargs) -> Self: ...
    def get_default_data(self): ...
    def get_default_columns(self): ...
    def __sql__(self, ctx): ...
    def handle_result(self, database, cursor): ...

class Delete(_WriteQuery):
    def __sql__(self, ctx): ...

class Index(Node):
    def __init__(
        self,
        name,
        table,
        expressions,
        unique: bool = ...,
        safe: bool = ...,
        where: Incomplete | None = ...,
        using: Incomplete | None = ...,
    ) -> None: ...
    def safe(self, _safe: bool = ...) -> Self: ...
    def where(self, *expressions) -> Self: ...
    def using(self, _using: Incomplete | None = ...) -> Self: ...
    def __sql__(self, ctx): ...

class ModelIndex(Index):
    def __init__(
        self,
        model,
        fields,
        unique: bool = ...,
        safe: bool = ...,
        where: Incomplete | None = ...,
        using: Incomplete | None = ...,
        name: Incomplete | None = ...,
    ) -> None: ...

class PeeweeException(Exception):
    def __init__(self, *args) -> None: ...

class ImproperlyConfigured(PeeweeException): ...
class DatabaseError(PeeweeException): ...
class DataError(DatabaseError): ...
class IntegrityError(DatabaseError): ...
class InterfaceError(PeeweeException): ...
class InternalError(DatabaseError): ...
class NotSupportedError(DatabaseError): ...
class OperationalError(DatabaseError): ...
class ProgrammingError(DatabaseError): ...

class ExceptionWrapper:
    exceptions: Incomplete
    def __init__(self, exceptions) -> None: ...
    def __enter__(self) -> None: ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_value: BaseException | None, traceback: TracebackType | None
    ) -> None: ...

class IndexMetadata(NamedTuple):
    name: Incomplete
    sql: Incomplete
    columns: Incomplete
    unique: Incomplete
    table: Incomplete

class ColumnMetadata(NamedTuple):
    name: Incomplete
    data_type: Incomplete
    null: Incomplete
    primary_key: Incomplete
    table: Incomplete
    default: Incomplete

class ForeignKeyMetadata(NamedTuple):
    column: Incomplete
    dest_table: Incomplete
    dest_column: Incomplete
    table: Incomplete

class ViewMetadata(NamedTuple):
    name: Incomplete
    sql: Incomplete

class _ConnectionState:
    def __init__(self, **kwargs) -> None: ...
    closed: bool
    conn: Incomplete
    ctx: Incomplete
    transactions: Incomplete
    def reset(self) -> None: ...
    def set_connection(self, conn) -> None: ...

class _ConnectionLocal(_ConnectionState, threading.local): ...

class _NoopLock:
    def __enter__(self): ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...

class ConnectionContext(_callable_context_manager):
    db: Incomplete
    def __init__(self, db) -> None: ...
    def __enter__(self) -> None: ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...

class Database(_callable_context_manager):
    context_class: Incomplete
    field_types: Incomplete
    operations: Incomplete
    param: str
    quote: str
    server_version: Incomplete
    commit_select: bool
    compound_select_parentheses: Incomplete
    for_update: bool
    index_schema_prefix: bool
    index_using_precedes_table: bool
    limit_max: Incomplete
    nulls_ordering: bool
    returning_clause: bool
    safe_create_index: bool
    safe_drop_index: bool
    sequences: bool
    truncate_table: bool
    autoconnect: Incomplete
    thread_safe: Incomplete
    connect_params: Incomplete
    def __init__(
        self,
        database,
        thread_safe: bool = ...,
        autorollback: bool = ...,
        field_types: Incomplete | None = ...,
        operations: Incomplete | None = ...,
        autocommit: Incomplete | None = ...,
        autoconnect: bool = ...,
        **kwargs,
    ) -> None: ...
    database: Incomplete
    deferred: Incomplete
    def init(self, database, **kwargs) -> None: ...
    def __enter__(self): ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...
    def connection_context(self): ...
    def connect(self, reuse_if_open: bool = False) -> bool: ...
    def close(self) -> bool: ...
    def is_closed(self) -> bool: ...
    def is_connection_usable(self) -> bool: ...
    def connection(self): ...
    def cursor(self, commit=None, named_cursor=None): ...
    def execute_sql(self, sql, params: Incomplete | None = ..., commit=...): ...
    def execute(self, query, commit=..., **context_options): ...
    def get_context_options(self): ...
    def get_sql_context(self, **context_options): ...
    def conflict_statement(self, on_conflict, query) -> None: ...
    def conflict_update(self, on_conflict, query) -> None: ...
    def last_insert_id(self, cursor, query_type: Incomplete | None = ...): ...
    def rows_affected(self, cursor): ...
    def default_values_insert(self, ctx): ...
    def session_start(self): ...
    def session_commit(self): ...
    def session_rollback(self): ...
    def in_transaction(self): ...
    def push_transaction(self, transaction) -> None: ...
    def pop_transaction(self): ...
    def transaction_depth(self): ...
    def top_transaction(self): ...
    def atomic(self, *args, **kwargs): ...
    def manual_commit(self): ...
    def transaction(self, *args, **kwargs): ...
    def savepoint(self): ...
    def begin(self) -> None: ...
    def commit(self): ...
    def rollback(self): ...
    def batch_commit(self, it, n) -> Generator[Incomplete, None, None]: ...
    def table_exists(self, table_name, schema: str | None = None): ...
    def get_tables(self, schema: str | None = None) -> list[str]: ...
    def get_indexes(self, table, schema: str | None = None) -> list[IndexMetadata]: ...
    def get_columns(self, table, schema: str | None = None) -> list[ColumnMetadata]: ...
    def get_primary_keys(self, table, schema: str | None = None) -> None: ...
    def get_foreign_keys(self, table, schema: str | None = None) -> list[ForeignKeyMetadata]: ...
    def sequence_exists(self, seq) -> None: ...
    def create_tables(self, models: Iterable[type[Model]], **options) -> None: ...
    def drop_tables(self, models: Iterable[type[Model]], **kwargs) -> None: ...
    def extract_date(self, date_part, date_field) -> None: ...
    def truncate_date(self, date_part, date_field) -> None: ...
    def to_timestamp(self, date_field) -> None: ...
    def from_timestamp(self, date_field) -> None: ...
    def random(self): ...
    def bind(self, models: Iterable[type[Model]], bind_refs: bool = ..., bind_backrefs: bool = ...) -> None: ...
    def bind_ctx(self, models: Iterable[type[Model]], bind_refs: bool = ..., bind_backrefs: bool = ...): ...
    def get_noop_select(self, ctx): ...
    @property
    def Model(self) -> type[Model]: ...

class SqliteDatabase(Database):
    field_types: Incomplete
    operations: Incomplete
    index_schema_prefix: bool
    limit_max: int
    server_version: Incomplete
    truncate_table: bool
    nulls_ordering: Incomplete
    def __init__(self, database, *args, **kwargs) -> None: ...
    returning_clause: Incomplete
    def init(
        self, database, pragmas: Incomplete | None = ..., timeout: int = ..., returning_clause: Incomplete | None = ..., **kwargs
    ) -> None: ...
    def pragma(self, key, value=..., permanent: bool = ..., schema: str | None = None): ...
    cache_size: Incomplete
    foreign_keys: Incomplete
    journal_mode: Incomplete
    journal_size_limit: Incomplete
    mmap_size: Incomplete
    page_size: Incomplete
    read_uncommitted: Incomplete
    synchronous: Incomplete
    wal_autocheckpoint: Incomplete
    application_id: Incomplete
    user_version: Incomplete
    data_version: Incomplete
    @property
    def timeout(self): ...
    @timeout.setter
    def timeout(self, seconds) -> None: ...
    def register_aggregate(self, klass, name: Incomplete | None = ..., num_params: int = ...) -> None: ...
    def aggregate(self, name: Incomplete | None = ..., num_params: int = ...): ...
    def register_collation(self, fn, name: Incomplete | None = ...): ...
    def collation(self, name: Incomplete | None = ...): ...
    def register_function(self, fn, name: str | None = None, num_params: int = -1, deterministic: bool | None = None) -> None: ...
    def func(self, name: str | None = None, num_params: int = -1, deterministic: bool | None = None) -> Callable[[_F], _F]: ...
    def register_window_function(self, klass, name: Incomplete | None = ..., num_params: int = ...) -> None: ...
    def window_function(self, name: Incomplete | None = ..., num_params: int = ...): ...
    def register_table_function(self, klass, name: Incomplete | None = ...) -> None: ...
    def table_function(self, name: Incomplete | None = ...): ...
    def unregister_aggregate(self, name) -> None: ...
    def unregister_collation(self, name) -> None: ...
    def unregister_function(self, name) -> None: ...
    def unregister_window_function(self, name) -> None: ...
    def unregister_table_function(self, name): ...
    def load_extension(self, extension) -> None: ...
    def unload_extension(self, extension) -> None: ...
    def attach(self, filename, name): ...
    def detach(self, name): ...
    def last_insert_id(self, cursor, query_type: Incomplete | None = ...): ...
    def rows_affected(self, cursor): ...
    def begin(self, lock_type: Incomplete | None = ...) -> None: ...
    def get_tables(self, schema: str | None = None) -> list[str]: ...
    def get_views(self, schema: str | None = None) -> list[ViewMetadata]: ...
    def get_indexes(self, table, schema: str | None = None) -> list[IndexMetadata]: ...
    def get_columns(self, table, schema: str | None = None) -> list[ColumnMetadata]: ...
    def get_primary_keys(self, table, schema: str | None = None): ...
    def get_foreign_keys(self, table, schema: str | None = None) -> list[ForeignKeyMetadata]: ...
    def get_binary_type(self): ...
    def conflict_statement(self, on_conflict, query): ...
    def conflict_update(self, oc, query): ...
    def extract_date(self, date_part, date_field): ...
    def truncate_date(self, date_part, date_field): ...
    def to_timestamp(self, date_field): ...
    def from_timestamp(self, date_field): ...

class PostgresqlDatabase(Database):
    field_types: Incomplete
    operations: Incomplete
    param: str
    commit_select: bool
    compound_select_parentheses: Incomplete
    for_update: bool
    nulls_ordering: bool
    returning_clause: bool
    safe_create_index: bool
    sequences: bool
    def init(
        self,
        database,
        register_unicode: bool = ...,
        encoding: Incomplete | None = ...,
        isolation_level: Incomplete | None = ...,
        **kwargs,
    ) -> None: ...
    def is_connection_usable(self) -> bool: ...
    def last_insert_id(self, cursor, query_type: Incomplete | None = ...): ...
    def rows_affected(self, cursor): ...
    def begin(self, isolation_level: str | None = None) -> None: ...
    def get_tables(self, schema: str | None = None) -> list[str]: ...
    def get_views(self, schema: str | None = None) -> list[ViewMetadata]: ...
    def get_indexes(self, table, schema: str | None = None) -> list[IndexMetadata]: ...
    def get_columns(self, table, schema: str | None = None) -> list[ColumnMetadata]: ...
    def get_primary_keys(self, table, schema: str | None = None): ...
    def get_foreign_keys(self, table, schema: str | None = None) -> list[ForeignKeyMetadata]: ...
    def sequence_exists(self, sequence): ...
    def get_binary_type(self): ...
    def conflict_statement(self, on_conflict, query) -> None: ...
    def conflict_update(self, oc, query): ...
    def extract_date(self, date_part, date_field): ...
    def truncate_date(self, date_part, date_field): ...
    def to_timestamp(self, date_field): ...
    def from_timestamp(self, date_field): ...
    def get_noop_select(self, ctx): ...
    def set_time_zone(self, timezone) -> None: ...

class MySQLDatabase(Database):
    field_types: Incomplete
    operations: Incomplete
    param: str
    quote: str
    commit_select: bool
    compound_select_parentheses: Incomplete
    for_update: bool
    index_using_precedes_table: bool
    limit_max: Incomplete
    safe_create_index: bool
    safe_drop_index: bool
    sql_mode: str
    def init(self, database, **kwargs) -> None: ...
    def is_connection_usable(self) -> bool: ...
    def default_values_insert(self, ctx): ...
    def begin(self, isolation_level: str | None = None) -> None: ...
    def get_tables(self, schema: str | None = None) -> list[str]: ...
    def get_views(self, schema: str | None = None) -> list[ViewMetadata]: ...
    def get_indexes(self, table, schema: str | None = None) -> list[IndexMetadata]: ...
    def get_columns(self, table, schema: str | None = None) -> list[ColumnMetadata]: ...
    def get_primary_keys(self, table, schema: str | None = None): ...
    def get_foreign_keys(self, table, schema: str | None = None) -> list[ForeignKeyMetadata]: ...
    def get_binary_type(self): ...
    def conflict_statement(self, on_conflict, query): ...
    def conflict_update(self, on_conflict, query): ...
    def extract_date(self, date_part, date_field): ...
    def truncate_date(self, date_part, date_field): ...
    def to_timestamp(self, date_field): ...
    def from_timestamp(self, date_field): ...
    def random(self): ...
    def get_noop_select(self, ctx): ...

class _manual(_callable_context_manager):
    db: Incomplete
    def __init__(self, db) -> None: ...
    def __enter__(self) -> None: ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...

class _atomic(_callable_context_manager):
    db: Incomplete
    def __init__(self, db, *args, **kwargs) -> None: ...
    def __enter__(self): ...
    def __exit__(self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None): ...

class _transaction(_callable_context_manager):
    db: Incomplete
    def __init__(self, db, *args, **kwargs) -> None: ...
    def commit(self, begin: bool = ...) -> None: ...
    def rollback(self, begin: bool = ...) -> None: ...
    def __enter__(self): ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...

class _savepoint(_callable_context_manager):
    db: Incomplete
    sid: Incomplete
    quoted_sid: Incomplete
    def __init__(self, db, sid: Incomplete | None = ...) -> None: ...
    def commit(self, begin: bool = ...) -> None: ...
    def rollback(self) -> None: ...
    def __enter__(self): ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...

class CursorWrapper:
    cursor: Incomplete
    count: int
    index: int
    initialized: bool
    populated: bool
    row_cache: Incomplete
    def __init__(self, cursor) -> None: ...
    def __iter__(self): ...
    def __getitem__(self, item): ...
    def __len__(self) -> int: ...
    def initialize(self) -> None: ...
    def iterate(self, cache: bool = ...): ...
    def process_row(self, row): ...
    def iterator(self) -> Generator[Incomplete, None, None]: ...
    def fill_cache(self, n: int = ...) -> None: ...

class DictCursorWrapper(CursorWrapper):
    initialize: Incomplete
    process_row: Incomplete

class NamedTupleCursorWrapper(CursorWrapper):
    tuple_class: Incomplete
    def initialize(self) -> None: ...
    def process_row(self, row): ...

class ObjectCursorWrapper(DictCursorWrapper):
    constructor: Incomplete
    def __init__(self, cursor, constructor) -> None: ...
    def process_row(self, row): ...

class ResultIterator:
    cursor_wrapper: Incomplete
    index: int
    def __init__(self, cursor_wrapper) -> None: ...
    def __iter__(self): ...
    def next(self): ...
    __next__: Incomplete

class FieldAccessor:
    model: Incomplete
    field: Incomplete
    name: Incomplete
    def __init__(self, model, field, name) -> None: ...
    def __get__(self, instance, instance_type: Incomplete | None = ...): ...
    def __set__(self, instance, value) -> None: ...

class ForeignKeyAccessor(FieldAccessor):
    rel_model: Incomplete
    def __init__(self, model, field, name) -> None: ...
    def get_rel_instance(self, instance): ...
    def __get__(self, instance, instance_type: Incomplete | None = ...): ...
    def __set__(self, instance, obj) -> None: ...

class BackrefAccessor:
    field: Incomplete
    model: Incomplete
    rel_model: Incomplete
    def __init__(self, field) -> None: ...
    def __get__(self, instance, instance_type: Incomplete | None = ...): ...

class ObjectIdAccessor:
    field: Incomplete
    def __init__(self, field) -> None: ...
    def __get__(self, instance, instance_type: Incomplete | None = ...): ...
    def __set__(self, instance, value) -> None: ...

class Field(ColumnBase):
    accessor_class: Incomplete
    auto_increment: bool
    default_index_type: Incomplete
    field_type: ClassVar[str]
    unpack: bool
    null: Incomplete
    index: Incomplete
    unique: Incomplete
    column_name: Incomplete
    default: Incomplete
    primary_key: Incomplete
    constraints: Incomplete
    sequence: Incomplete
    collation: Incomplete
    unindexed: Incomplete
    choices: Incomplete
    help_text: Incomplete
    verbose_name: Incomplete
    index_type: Incomplete
    def __init__(
        self,
        null: bool = ...,
        index: bool = ...,
        unique: bool = ...,
        column_name: Incomplete | None = ...,
        default: Incomplete | None = ...,
        primary_key: bool = ...,
        constraints: Incomplete | None = ...,
        sequence: Incomplete | None = ...,
        collation: Incomplete | None = ...,
        unindexed: bool = ...,
        choices: Incomplete | None = ...,
        help_text: Incomplete | None = ...,
        verbose_name: Incomplete | None = ...,
        index_type: Incomplete | None = ...,
        db_column: Incomplete | None = ...,
        _hidden: bool = ...,
    ) -> None: ...
    def __hash__(self) -> int: ...
    model: Incomplete
    name: Incomplete
    def bind(self, model, name, set_attribute: bool = ...) -> None: ...
    @property
    def column(self): ...
    def adapt(self, value): ...
    def db_value(self, value): ...
    def python_value(self, value): ...
    def to_value(self, value): ...
    def get_sort_key(self, ctx): ...
    def __sql__(self, ctx): ...
    def get_modifiers(self) -> None: ...
    def ddl_datatype(self, ctx): ...
    def ddl(self, ctx): ...

class AnyField(Field): ...

class IntegerField(Field):
    def adapt(self, value): ...

class BigIntegerField(IntegerField): ...
class SmallIntegerField(IntegerField): ...

class AutoField(IntegerField):
    auto_increment: bool
    def __init__(self, *args, **kwargs) -> None: ...

class BigAutoField(AutoField): ...

class IdentityField(AutoField):
    def __init__(self, generate_always: bool = ..., **kwargs) -> None: ...

class PrimaryKeyField(AutoField):
    def __init__(self, *args, **kwargs) -> None: ...

class FloatField(Field):
    def adapt(self, value): ...

class DoubleField(FloatField): ...

class DecimalField(Field):
    max_digits: Incomplete
    decimal_places: Incomplete
    auto_round: Incomplete
    rounding: Incomplete
    def __init__(
        self,
        max_digits: int = ...,
        decimal_places: int = ...,
        auto_round: bool = ...,
        rounding: Incomplete | None = ...,
        *args,
        **kwargs,
    ) -> None: ...
    def get_modifiers(self): ...
    def db_value(self, value): ...
    def python_value(self, value): ...

class _StringField(Field):
    def adapt(self, value): ...
    def __add__(self, other): ...
    def __radd__(self, other): ...

class CharField(_StringField):
    max_length: Incomplete
    def __init__(self, max_length: int = ..., *args, **kwargs) -> None: ...
    def get_modifiers(self): ...

class FixedCharField(CharField):
    def python_value(self, value): ...

class TextField(_StringField): ...

class BlobField(Field):
    def bind(self, model, name, set_attribute: bool = ...): ...
    def db_value(self, value): ...

class BitField(BitwiseMixin, BigIntegerField):
    def __init__(self, *args, **kwargs) -> None: ...
    def flag(self, value: Incomplete | None = ...): ...

class BigBitFieldData:
    instance: Incomplete
    name: Incomplete
    def __init__(self, instance, name) -> None: ...
    def clear(self) -> None: ...
    def set_bit(self, idx) -> None: ...
    def clear_bit(self, idx) -> None: ...
    def toggle_bit(self, idx): ...
    def is_set(self, idx): ...
    __getitem__ = is_set
    def __setitem__(self, item: int, value: bool) -> None: ...
    __delitem__ = clear_bit
    def __len__(self) -> int: ...
    def __and__(self, other: BigBitFieldData | bytes | bytearray | memoryview) -> bytearray: ...
    def __or__(self, other: BigBitFieldData | bytes | bytearray | memoryview) -> bytearray: ...
    def __xor__(self, other: BigBitFieldData | bytes | bytearray | memoryview) -> bytearray: ...
    def __iter__(self) -> Iterator[Literal[0, 1]]: ...
    def __bytes__(self) -> bytes: ...

class BigBitFieldAccessor(FieldAccessor):
    def __get__(self, instance, instance_type: Incomplete | None = ...): ...
    def __set__(self, instance, value) -> None: ...

class BigBitField(BlobField):
    accessor_class: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def db_value(self, value): ...

class UUIDField(Field):
    def db_value(self, value): ...
    def python_value(self, value): ...

class BinaryUUIDField(BlobField):
    def db_value(self, value): ...
    def python_value(self, value): ...

class _BaseFormattedField(Field):
    formats: Incomplete
    def __init__(self, formats: Incomplete | None = ..., *args, **kwargs) -> None: ...

class DateTimeField(_BaseFormattedField):
    formats: Incomplete
    def adapt(self, value): ...
    def to_timestamp(self): ...
    def truncate(self, part): ...
    @property
    def year(self): ...
    @property
    def month(self): ...
    @property
    def day(self): ...
    @property
    def hour(self): ...
    @property
    def minute(self): ...
    @property
    def second(self): ...

class DateField(_BaseFormattedField):
    formats: Incomplete
    def adapt(self, value): ...
    def to_timestamp(self): ...
    def truncate(self, part): ...
    @property
    def year(self): ...
    @property
    def month(self): ...
    @property
    def day(self): ...

class TimeField(_BaseFormattedField):
    formats: Incomplete
    def adapt(self, value): ...
    @property
    def hour(self): ...
    @property
    def minute(self): ...
    @property
    def second(self): ...

class TimestampField(BigIntegerField):
    valid_resolutions: Incomplete
    resolution: Incomplete
    ticks_to_microsecond: Incomplete
    utc: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def local_to_utc(self, dt): ...
    def utc_to_local(self, dt): ...
    def get_timestamp(self, value): ...
    def db_value(self, value): ...
    def python_value(self, value): ...
    def from_timestamp(self): ...
    @property
    def year(self): ...
    @property
    def month(self): ...
    @property
    def day(self): ...
    @property
    def hour(self): ...
    @property
    def minute(self): ...
    @property
    def second(self): ...

class IPField(BigIntegerField):
    def db_value(self, val): ...
    def python_value(self, val): ...

class BooleanField(Field):
    adapt: Incomplete

class BareField(Field):
    adapt: Incomplete
    def __init__(self, adapt: Incomplete | None = ..., *args, **kwargs) -> None: ...
    def ddl_datatype(self, ctx) -> None: ...

class ForeignKeyField(Field):
    accessor_class: Incomplete
    backref_accessor_class: Incomplete
    rel_model: Incomplete
    rel_field: Incomplete
    declared_backref: Incomplete
    backref: Incomplete
    on_delete: Incomplete
    on_update: Incomplete
    deferrable: Incomplete
    deferred: Incomplete
    object_id_name: Incomplete
    lazy_load: Incomplete
    constraint_name: str | None
    def __init__(
        self,
        model,
        field: Incomplete | None = ...,
        backref: Incomplete | None = ...,
        on_delete: Incomplete | None = ...,
        on_update: Incomplete | None = ...,
        deferrable: Incomplete | None = ...,
        _deferred: Incomplete | None = ...,
        rel_model: Incomplete | None = ...,
        to_field: Incomplete | None = ...,
        object_id_name: Incomplete | None = ...,
        lazy_load: bool = ...,
        constraint_name: str | None = ...,
        related_name: Incomplete | None = ...,
        *args,
        **kwargs,
    ) -> None: ...
    @property
    def field_type(self): ...  # type: ignore[override]
    def get_modifiers(self): ...
    def adapt(self, value): ...
    def db_value(self, value): ...
    def python_value(self, value): ...
    column_name: Incomplete
    safe_name: Incomplete
    def bind(self, model, name, set_attribute: bool = ...) -> None: ...
    def foreign_key_constraint(self, explicit_name: bool = False) -> NodeList: ...
    def get_constraint_name(self) -> str: ...
    def __getattr__(self, attr: str): ...

class DeferredForeignKey(Field):
    field_kwargs: Incomplete
    rel_model_name: Incomplete
    def __init__(self, rel_model_name, **kwargs) -> None: ...
    __hash__: Incomplete
    def __deepcopy__(self, memo: Incomplete | None = ...): ...
    def set_model(self, rel_model) -> None: ...
    @staticmethod
    def resolve(model_cls) -> None: ...

class DeferredThroughModel:
    def __init__(self) -> None: ...
    def set_field(self, model, field, name) -> None: ...
    def set_model(self, through_model) -> None: ...

class MetaField(Field):
    column_name: Incomplete
    default: Incomplete
    model: Incomplete
    name: Incomplete
    primary_key: bool

class ManyToManyFieldAccessor(FieldAccessor):
    model: Incomplete
    rel_model: Incomplete
    through_model: Incomplete
    src_fk: Incomplete
    dest_fk: Incomplete
    def __init__(self, model, field, name) -> None: ...
    def __get__(self, instance, instance_type: Incomplete | None = ..., force_query: bool = ...): ...
    def __set__(self, instance, value) -> None: ...

class ManyToManyField(MetaField):
    accessor_class: Incomplete
    rel_model: Incomplete
    backref: Incomplete
    def __init__(
        self,
        model,
        backref: Incomplete | None = ...,
        through_model: Incomplete | None = ...,
        on_delete: Incomplete | None = ...,
        on_update: Incomplete | None = ...,
        prevent_unsaved: bool = True,
        _is_backref: bool = ...,
    ) -> None: ...
    def bind(self, model, name, set_attribute: bool = ...) -> None: ...
    def get_models(self): ...
    @property
    def through_model(self): ...
    @through_model.setter
    def through_model(self, value) -> None: ...
    def get_through_model(self): ...

class VirtualField(MetaField):
    field_class: Incomplete
    field_instance: Incomplete
    def __init__(self, field_class: Incomplete | None = ..., *args, **kwargs) -> None: ...
    def db_value(self, value): ...
    def python_value(self, value): ...
    model: Incomplete
    column_name: Incomplete
    def bind(self, model, name, set_attribute: bool = ...) -> None: ...

class CompositeKey(MetaField):
    sequence: Incomplete
    field_names: Incomplete
    def __init__(self, *field_names) -> None: ...
    @property
    def safe_field_names(self): ...
    def __get__(self, instance, instance_type: Incomplete | None = ...): ...
    def __set__(self, instance, value) -> None: ...
    def __eq__(self, other) -> Expression | bool: ...  # type: ignore[override]
    def __ne__(self, other) -> Expression | bool: ...  # type: ignore[override]
    def __hash__(self) -> int: ...
    def __sql__(self, ctx): ...
    model: Incomplete
    column_name: Incomplete
    def bind(self, model, name, set_attribute: bool = ...) -> None: ...

class _SortedFieldList:
    def __init__(self) -> None: ...
    def __getitem__(self, i): ...
    def __iter__(self): ...
    def __contains__(self, item): ...
    def index(self, field): ...
    def insert(self, item) -> None: ...
    def remove(self, item) -> None: ...

class SchemaManager:
    model: Incomplete
    context_options: Incomplete
    def __init__(self, model, database=None, **context_options) -> None: ...
    @property
    def database(self): ...
    @database.setter
    def database(self, value) -> None: ...
    def create_table(self, safe: bool = ..., **options) -> None: ...
    def create_table_as(self, table_name, query, safe: bool = ..., **meta) -> None: ...
    def drop_table(self, safe: bool = ..., **options) -> None: ...
    def truncate_table(self, restart_identity: bool = ..., cascade: bool = ...) -> None: ...
    def create_indexes(self, safe: bool = ...) -> None: ...
    def drop_indexes(self, safe: bool = ...) -> None: ...
    def create_sequence(self, field) -> None: ...
    def drop_sequence(self, field) -> None: ...
    def create_foreign_key(self, field) -> None: ...
    def create_sequences(self) -> None: ...
    def create_all(self, safe: bool = ..., **table_options) -> None: ...
    def drop_sequences(self) -> None: ...
    def drop_all(self, safe: bool = ..., drop_sequences: bool = ..., **options) -> None: ...

class Metadata:
    model: Incomplete
    database: Incomplete
    fields: Incomplete
    columns: Incomplete
    combined: Incomplete
    sorted_fields: Incomplete
    sorted_field_names: Incomplete
    defaults: Incomplete
    name: Incomplete
    table_function: Incomplete
    legacy_table_names: Incomplete
    table_name: Incomplete
    indexes: Incomplete
    constraints: Incomplete
    primary_key: Incomplete
    composite_key: Incomplete
    only_save_dirty: Incomplete
    depends_on: Incomplete
    table_settings: Incomplete
    without_rowid: Incomplete
    strict_tables: Incomplete
    temporary: Incomplete
    refs: Incomplete
    backrefs: Incomplete
    model_refs: Incomplete
    model_backrefs: Incomplete
    manytomany: Incomplete
    options: Incomplete
    def __init__(
        self,
        model,
        database=None,
        table_name: Incomplete | None = ...,
        indexes: Incomplete | None = ...,
        primary_key: Incomplete | None = ...,
        constraints: Incomplete | None = ...,
        schema: str | None = None,
        only_save_dirty: bool = ...,
        depends_on: Incomplete | None = ...,
        options: Incomplete | None = ...,
        db_table: Incomplete | None = ...,
        table_function: Incomplete | None = ...,
        table_settings: Incomplete | None = ...,
        without_rowid: bool = ...,
        temporary: bool = ...,
        strict_tables: Incomplete | None = ...,
        legacy_table_names: bool = ...,
        **kwargs,
    ) -> None: ...
    def make_table_name(self): ...
    def model_graph(self, refs: bool = ..., backrefs: bool = ..., depth_first: bool = ...): ...
    def add_ref(self, field) -> None: ...
    def remove_ref(self, field) -> None: ...
    def add_manytomany(self, field) -> None: ...
    def remove_manytomany(self, field) -> None: ...
    @property
    def table(self): ...
    @table.deleter
    def table(self) -> None: ...
    @property
    def schema(self): ...
    @schema.setter
    def schema(self, value) -> None: ...
    @property
    def entity(self): ...
    def get_rel_for_model(self, model): ...
    def add_field(self, field_name, field, set_attribute: bool = ...) -> None: ...
    def remove_field(self, field_name) -> None: ...
    auto_increment: Incomplete
    def set_primary_key(self, name, field) -> None: ...
    def get_primary_keys(self): ...
    def get_default_dict(self): ...
    def fields_to_index(self): ...
    def set_database(self, database) -> None: ...
    def set_table_name(self, table_name) -> None: ...

class SubclassAwareMetadata(Metadata):
    models: Incomplete
    def __init__(self, model, *args, **kwargs) -> None: ...
    def map_models(self, fn) -> None: ...

class DoesNotExist(Exception): ...

class ModelBase(type):
    inheritable: Incomplete
    def __new__(cls, name, bases, attrs): ...
    def __iter__(self): ...
    def __getitem__(self, key): ...
    def __setitem__(self, key, value) -> None: ...
    def __delitem__(self, key) -> None: ...
    def __contains__(self, key): ...
    def __len__(self) -> int: ...
    def __bool__(self) -> bool: ...
    def __nonzero__(self) -> bool: ...
    def __sql__(self, ctx): ...

class _BoundModelsContext(_callable_context_manager):
    models: Incomplete
    database: Incomplete
    bind_refs: Incomplete
    bind_backrefs: Incomplete
    def __init__(self, models, database, bind_refs, bind_backrefs) -> None: ...
    def __enter__(self): ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...

class Model(metaclass=ModelBase):
    __data__: Incomplete
    __rel__: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    @classmethod
    def validate_model(cls) -> None: ...
    @classmethod
    def alias(cls, alias: Incomplete | None = ...): ...
    @classmethod
    def select(cls, *fields): ...
    @classmethod
    def update(cls, data: Incomplete | None = ..., /, **update): ...
    @classmethod
    def insert(cls, data: Incomplete | None = ..., /, **insert): ...
    @classmethod
    def insert_many(cls, rows, fields: Incomplete | None = ...): ...
    @classmethod
    def insert_from(cls, query, fields): ...
    @classmethod
    def replace(cls, data: Incomplete | None = ..., /, **insert): ...
    @classmethod
    def replace_many(cls, rows, fields: Incomplete | None = ...): ...
    @classmethod
    def raw(cls, sql, *params): ...
    @classmethod
    def delete(cls): ...
    @classmethod
    def create(cls, **query): ...
    @classmethod
    def bulk_create(cls, model_list, batch_size: Incomplete | None = ...) -> None: ...
    @classmethod
    def bulk_update(cls, model_list, fields, batch_size: Incomplete | None = ...): ...
    @classmethod
    def noop(cls): ...
    @classmethod
    def get(cls, *query, **filters): ...
    @classmethod
    def get_or_none(cls, *query, **filters): ...
    @classmethod
    def get_by_id(cls, pk): ...
    @classmethod
    def set_by_id(cls, key, value): ...
    @classmethod
    def delete_by_id(cls, pk): ...
    @classmethod
    def get_or_create(cls, **kwargs): ...
    @classmethod
    def filter(cls, *dq_nodes, **filters): ...
    def get_id(self): ...
    def save(self, force_insert: bool = ..., only: Incomplete | None = ...): ...
    def is_dirty(self) -> bool: ...
    @property
    def dirty_fields(self): ...
    def dependencies(
        self, search_nullable: bool = True, exclude_null_children: bool = False
    ) -> Generator[Incomplete, None, None]: ...
    def delete_instance(self, recursive: bool = ..., delete_nullable: bool = ...): ...
    def __hash__(self) -> int: ...
    def __eq__(self, other) -> Expression | bool: ...  # type: ignore[override]
    def __ne__(self, other) -> Expression | bool: ...  # type: ignore[override]
    def __sql__(self, ctx): ...
    @classmethod
    def bind(cls, database, bind_refs: bool = ..., bind_backrefs: bool = ..., _exclude: Incomplete | None = ...): ...
    @classmethod
    def bind_ctx(cls, database, bind_refs: bool = ..., bind_backrefs: bool = ...): ...
    @classmethod
    def table_exists(cls): ...
    @classmethod
    def create_table(cls, safe: bool = ..., **options) -> None: ...
    @classmethod
    def drop_table(cls, safe: bool = ..., drop_sequences: bool = ..., **options) -> None: ...
    @classmethod
    def truncate_table(cls, **options) -> None: ...
    @classmethod
    def index(cls, *fields, **kwargs): ...
    @classmethod
    def add_index(cls, *fields, **kwargs) -> None: ...

class ModelAlias(Node):
    def __init__(self, model, alias: Incomplete | None = ...) -> None: ...
    def __getattr__(self, attr: str): ...
    def __setattr__(self, attr: str, value) -> None: ...
    def get_field_aliases(self): ...
    def select(self, *selection): ...
    def __call__(self, **kwargs): ...
    def __sql__(self, ctx): ...

class FieldAlias(Field):
    source: Incomplete
    model: Incomplete
    field: Incomplete
    def __init__(self, source, field) -> None: ...
    @classmethod
    def create(cls, source, field): ...
    def clone(self): ...
    def adapt(self, value): ...
    def python_value(self, value): ...
    def db_value(self, value): ...
    def __getattr__(self, attr: str): ...
    def __sql__(self, ctx): ...

class _ModelQueryHelper:
    default_row_type: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def objects(self, constructor: Incomplete | None = ...) -> Self: ...

class ModelRaw(_ModelQueryHelper, RawQuery):  # type: ignore[misc]
    model: Incomplete
    def __init__(self, model, sql, params, **kwargs) -> None: ...
    def get(self): ...

class BaseModelSelect(_ModelQueryHelper):
    def union_all(self, rhs): ...
    __add__: Incomplete
    def union(self, rhs): ...
    __or__: Incomplete
    def intersect(self, rhs): ...
    __and__: Incomplete
    def except_(self, rhs): ...
    __sub__: Incomplete
    def __iter__(self): ...
    def prefetch(self, *subqueries): ...
    def get(self, database=None): ...
    def get_or_none(self, database=None): ...
    def group_by(self, *columns) -> Self: ...

class ModelCompoundSelectQuery(BaseModelSelect, CompoundSelectQuery):  # type: ignore[misc]
    model: Incomplete
    def __init__(self, model, *args, **kwargs) -> None: ...

class ModelSelect(BaseModelSelect, Select):  # type: ignore[misc]
    model: Incomplete
    def __init__(self, model, fields_or_models, is_default: bool = ...) -> None: ...
    def clone(self): ...
    def select(self, *fields_or_models): ...
    def select_extend(self, *columns): ...
    def switch(self, ctx: Incomplete | None = ...): ...
    def join(
        self, dest, join_type=..., on: Incomplete | None = ..., src: Incomplete | None = ..., attr: Incomplete | None = ...
    ) -> Self: ...
    def left_outer_join(self, dest, on: Incomplete | None = ..., src: Incomplete | None = ..., attr: Incomplete | None = ...): ...
    def join_from(self, src, dest, join_type=..., on: Incomplete | None = ..., attr: Incomplete | None = ...): ...
    def ensure_join(self, lm, rm, on: Incomplete | None = ..., **join_kwargs): ...
    def convert_dict_to_node(self, qdict): ...
    def filter(self, *args, **kwargs): ...
    def create_table(self, name, safe: bool = ..., **meta): ...
    def __sql_selection__(self, ctx, is_subquery: bool = ...): ...

class NoopModelSelect(ModelSelect):
    def __sql__(self, ctx): ...

class _ModelWriteQueryHelper(_ModelQueryHelper):
    model: Incomplete
    def __init__(self, model, *args, **kwargs) -> None: ...
    def returning(self, *returning): ...

class ModelUpdate(_ModelWriteQueryHelper, Update): ...  # type: ignore[misc]

class ModelInsert(_ModelWriteQueryHelper, Insert):  # type: ignore[misc]
    default_row_type: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def returning(self, *returning): ...
    def get_default_data(self): ...
    def get_default_columns(self): ...

class ModelDelete(_ModelWriteQueryHelper, Delete): ...  # type: ignore[misc]

class ManyToManyQuery(ModelSelect):
    def __init__(self, instance, accessor, rel, *args, **kwargs) -> None: ...
    def add(self, value, clear_existing: bool = ...) -> None: ...
    def remove(self, value): ...
    def clear(self): ...

class BaseModelCursorWrapper(DictCursorWrapper):
    model: Incomplete
    select: Incomplete
    def __init__(self, cursor, model, columns) -> None: ...
    initialize: Incomplete
    def process_row(self, row) -> None: ...

class ModelDictCursorWrapper(BaseModelCursorWrapper):
    def process_row(self, row): ...

class ModelTupleCursorWrapper(ModelDictCursorWrapper):
    constructor: Incomplete
    def process_row(self, row): ...

class ModelNamedTupleCursorWrapper(ModelTupleCursorWrapper):
    tuple_class: Incomplete
    constructor: Incomplete
    def initialize(self): ...

class ModelObjectCursorWrapper(ModelDictCursorWrapper):
    constructor: Incomplete
    is_model: Incomplete
    def __init__(self, cursor, model, select, constructor) -> None: ...
    def process_row(self, row): ...

class ModelCursorWrapper(BaseModelCursorWrapper):
    from_list: Incomplete
    joins: Incomplete
    def __init__(self, cursor, model, select, from_list, joins) -> None: ...
    key_to_constructor: Incomplete
    src_is_dest: Incomplete
    src_to_dest: Incomplete
    column_keys: Incomplete
    def initialize(self) -> None: ...
    def process_row(self, row): ...

class PrefetchQuery:
    def __new__(
        cls,
        query,
        fields: Incomplete | None = ...,
        is_backref: Incomplete | None = ...,
        rel_models: Incomplete | None = ...,
        field_to_name: Incomplete | None = ...,
        model: Incomplete | None = ...,
    ): ...
    def populate_instance(self, instance, id_map) -> None: ...
    def store_instance(self, instance, id_map) -> None: ...

def prefetch(sq, *subqueries): ...

__all__ = [
    "AnyField",
    "AsIs",
    "AutoField",
    "BareField",
    "BigAutoField",
    "BigBitField",
    "BigIntegerField",
    "BinaryUUIDField",
    "BitField",
    "BlobField",
    "BooleanField",
    "Case",
    "Cast",
    "CharField",
    "Check",
    "chunked",
    "Column",
    "CompositeKey",
    "Context",
    "Database",
    "DatabaseError",
    "DatabaseProxy",
    "DataError",
    "DateField",
    "DateTimeField",
    "DecimalField",
    "DeferredForeignKey",
    "DeferredThroughModel",
    "DJANGO_MAP",
    "DoesNotExist",
    "DoubleField",
    "DQ",
    "EXCLUDED",
    "Field",
    "FixedCharField",
    "FloatField",
    "fn",
    "ForeignKeyField",
    "IdentityField",
    "ImproperlyConfigured",
    "Index",
    "IntegerField",
    "IntegrityError",
    "InterfaceError",
    "InternalError",
    "IPField",
    "JOIN",
    "ManyToManyField",
    "Model",
    "ModelIndex",
    "MySQLDatabase",
    "NotSupportedError",
    "OP",
    "OperationalError",
    "PostgresqlDatabase",
    "PrimaryKeyField",
    "prefetch",
    "PREFETCH_TYPE",
    "ProgrammingError",
    "Proxy",
    "QualifiedNames",
    "SchemaManager",
    "SmallIntegerField",
    "Select",
    "SQL",
    "SqliteDatabase",
    "Table",
    "TextField",
    "TimeField",
    "TimestampField",
    "Tuple",
    "UUIDField",
    "Value",
    "ValuesList",
    "Window",
]
