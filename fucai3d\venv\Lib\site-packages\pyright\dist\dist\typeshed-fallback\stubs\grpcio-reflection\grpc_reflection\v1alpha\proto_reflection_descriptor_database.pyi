import grpc
from google.protobuf.descriptor_database import DescriptorDatabase
from google.protobuf.descriptor_pb2 import FileDescriptorProto

class ProtoReflectionDescriptorDatabase(DescriptorDatabase):
    def __init__(self, channel: grpc.Channel) -> None: ...
    def get_services(self) -> list[str]: ...
    def FindFileByName(self, name: str) -> FileDescriptorProto: ...
    def FindFileContainingSymbol(self, symbol: str) -> FileDescriptorProto: ...
    def FindAllExtensionNumbers(self, extendee_name: str) -> list[int]: ...
    def FindFileContainingExtension(self, extendee_name: str, extension_number: int) -> FileDescriptorProto: ...
