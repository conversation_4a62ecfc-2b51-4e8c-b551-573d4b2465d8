from _typeshed import Incomplete
from collections.abc import Mapping
from typing import Final

import requests
from auth0.rest_async import RequestsResponse
from auth0.types import RequestData, TimeoutType

UNKNOWN_ERROR: Final[str]

class RestClientOptions:
    telemetry: bool
    timeout: TimeoutType
    retries: int
    def __init__(self, telemetry: bool = True, timeout: TimeoutType = 5.0, retries: int = 3) -> None: ...

class RestClient:
    options: RestClientOptions
    jwt: str | None
    base_headers: dict[str, str]
    telemetry: bool
    timeout: TimeoutType
    def __init__(
        self, jwt: str | None, telemetry: bool = True, timeout: TimeoutType = 5.0, options: RestClientOptions | None = None
    ) -> None: ...
    def MAX_REQUEST_RETRIES(self) -> int: ...
    def MAX_REQUEST_RETRY_JITTER(self) -> int: ...
    def MAX_REQUEST_RETRY_DELAY(self) -> int: ...
    def MIN_REQUEST_RETRY_DELAY(self) -> int: ...
    def get(self, url: str, params: dict[str, Incomplete] | None = None, headers: dict[str, str] | None = None): ...
    def post(self, url: str, data: RequestData | None = None, headers: dict[str, str] | None = None): ...
    def file_post(self, url: str, data: RequestData | None = None, files: dict[str, Incomplete] | None = None): ...
    def patch(self, url: str, data: RequestData | None = None): ...
    def put(self, url: str, data: RequestData | None = None): ...
    def delete(self, url: str, params: dict[str, Incomplete] | None = None, data: RequestData | None = None): ...

class Response:
    def __init__(self, status_code: int, content, headers: Mapping[str, str]) -> None: ...
    def content(self): ...

class JsonResponse(Response):
    def __init__(self, response: requests.Response | RequestsResponse) -> None: ...

class PlainResponse(Response):
    def __init__(self, response: requests.Response | RequestsResponse) -> None: ...

class EmptyResponse(Response):
    def __init__(self, status_code: int) -> None: ...
