from typing import Literal

class AuthlibBaseError(Exception):
    error: str | None
    description: str
    uri: str | None
    def __init__(self, error: str | None = None, description: str | None = None, uri: str | None = None) -> None: ...

class AuthlibHTTPError(AuthlibBaseError):
    status_code: int
    def __init__(
        self, error: str | None = None, description: str | None = None, uri: str | None = None, status_code: int | None = None
    ) -> None: ...
    def get_error_description(self) -> str: ...
    def get_body(self) -> list[tuple[Literal["error", "error_description", "error_uri"], str | None]]: ...
    def get_headers(self) -> list[tuple[str, str]]: ...
    def __call__(
        self, uri: str | None = None
    ) -> tuple[int, dict[Literal["error", "error_description", "error_uri"], str | None], list[tuple[str, str]]]: ...

class ContinueIteration(AuthlibBaseError): ...
