from typing import Final

NONE: Final = 0
ParentRelative: Final = 1
CopyFromParent: Final = 0
PointerWindow: Final = 0
InputFocus: Final = 1
PointerRoot: Final = 1
AnyPropertyType: Final = 0
AnyKey: Final = 0
AnyButton: Final = 0
AllTemporary: Final = 0
CurrentTime: Final = 0
NoSymbol: Final = 0
NoEventMask: Final = 0x0000000
KeyPressMask: Final = 0x0000001
KeyReleaseMask: Final = 0x0000002
ButtonPressMask: Final = 0x0000004
ButtonReleaseMask: Final = 0x0000008
EnterWindowMask: Final = 0x0000010
LeaveWindowMask: Final = 0x0000020
PointerMotionMask: Final = 0x0000040
PointerMotionHintMask: Final = 0x0000080
Button1MotionMask: Final = 0x0000100
Button2MotionMask: Final = 0x0000200
Button3MotionMask: Final = 0x0000400
Button4MotionMask: Final = 0x0000800
Button5MotionMask: Final = 0x0001000
ButtonMotionMask: Final = 0x0002000
KeymapStateMask: Final = 0x0004000
ExposureMask: Final = 0x0008000
VisibilityChangeMask: Final = 0x0010000
StructureNotifyMask: Final = 0x0020000
ResizeRedirectMask: Final = 0x0040000
SubstructureNotifyMask: Final = 0x0080000
SubstructureRedirectMask: Final = 0x0100000
FocusChangeMask: Final = 0x0200000
PropertyChangeMask: Final = 0x0400000
ColormapChangeMask: Final = 0x0800000
OwnerGrabButtonMask: Final = 0x1000000
KeyPress: Final = 2
KeyRelease: Final = 3
ButtonPress: Final = 4
ButtonRelease: Final = 5
MotionNotify: Final = 6
EnterNotify: Final = 7
LeaveNotify: Final = 8
FocusIn: Final = 9
FocusOut: Final = 10
KeymapNotify: Final = 11
Expose: Final = 12
GraphicsExpose: Final = 13
NoExpose: Final = 14
VisibilityNotify: Final = 15
CreateNotify: Final = 16
DestroyNotify: Final = 17
UnmapNotify: Final = 18
MapNotify: Final = 19
MapRequest: Final = 20
ReparentNotify: Final = 21
ConfigureNotify: Final = 22
ConfigureRequest: Final = 23
GravityNotify: Final = 24
ResizeRequest: Final = 25
CirculateNotify: Final = 26
CirculateRequest: Final = 27
PropertyNotify: Final = 28
SelectionClear: Final = 29
SelectionRequest: Final = 30
SelectionNotify: Final = 31
ColormapNotify: Final = 32
ClientMessage: Final = 33
MappingNotify: Final = 34
LASTEvent: Final = 35
ShiftMapIndex: Final = 0
LockMapIndex: Final = 1
ControlMapIndex: Final = 2
Mod1MapIndex: Final = 3
Mod2MapIndex: Final = 4
Mod3MapIndex: Final = 5
Mod4MapIndex: Final = 6
Mod5MapIndex: Final = 7
ShiftMask: Final = 0x0001
LockMask: Final = 0x0002
ControlMask: Final = 0x0004
Mod1Mask: Final = 0x0008
Mod2Mask: Final = 0x0010
Mod3Mask: Final = 0x0020
Mod4Mask: Final = 0x0040
Mod5Mask: Final = 0x0080
Button1Mask: Final = 0x0100
Button2Mask: Final = 0x0200
Button3Mask: Final = 0x0400
Button4Mask: Final = 0x0800
Button5Mask: Final = 0x1000
AnyModifier: Final = 0x8000
Button1: Final = 1
Button2: Final = 2
Button3: Final = 3
Button4: Final = 4
Button5: Final = 5
NotifyNormal: Final = 0
NotifyGrab: Final = 1
NotifyUngrab: Final = 2
NotifyWhileGrabbed: Final = 3
NotifyHint: Final = 1
NotifyAncestor: Final = 0
NotifyVirtual: Final = 1
NotifyInferior: Final = 2
NotifyNonlinear: Final = 3
NotifyNonlinearVirtual: Final = 4
NotifyPointer: Final = 5
NotifyPointerRoot: Final = 6
NotifyDetailNone: Final = 7
VisibilityUnobscured: Final = 0
VisibilityPartiallyObscured: Final = 1
VisibilityFullyObscured: Final = 2
PlaceOnTop: Final = 0
PlaceOnBottom: Final = 1
FamilyInternet: Final = 0
FamilyDECnet: Final = 1
FamilyChaos: Final = 2
FamilyServerInterpreted: Final = 5
FamilyInternetV6: Final = 6
PropertyNewValue: Final = 0
PropertyDelete: Final = 1
ColormapUninstalled: Final = 0
ColormapInstalled: Final = 1
GrabModeSync: Final = 0
GrabModeAsync: Final = 1
GrabSuccess: Final = 0
AlreadyGrabbed: Final = 1
GrabInvalidTime: Final = 2
GrabNotViewable: Final = 3
GrabFrozen: Final = 4
AsyncPointer: Final = 0
SyncPointer: Final = 1
ReplayPointer: Final = 2
AsyncKeyboard: Final = 3
SyncKeyboard: Final = 4
ReplayKeyboard: Final = 5
AsyncBoth: Final = 6
SyncBoth: Final = 7
RevertToNone: Final = 0
RevertToPointerRoot: Final = PointerRoot
RevertToParent: Final = 2
Success: Final = 0
BadRequest: Final = 1
BadValue: Final = 2
BadWindow: Final = 3
BadPixmap: Final = 4
BadAtom: Final = 5
BadCursor: Final = 6
BadFont: Final = 7
BadMatch: Final = 8
BadDrawable: Final = 9
BadAccess: Final = 10
BadAlloc: Final = 11
BadColor: Final = 12
BadGC: Final = 13
BadIDChoice: Final = 14
BadName: Final = 15
BadLength: Final = 16
BadImplementation: Final = 17
FirstExtensionError: Final = 128
LastExtensionError: Final = 255
InputOutput: Final = 1
InputOnly: Final = 2
CWBackPixmap: Final = 0x0001
CWBackPixel: Final = 0x0002
CWBorderPixmap: Final = 0x0004
CWBorderPixel: Final = 0x0008
CWBitGravity: Final = 0x0010
CWWinGravity: Final = 0x0020
CWBackingStore: Final = 0x0040
CWBackingPlanes: Final = 0x0080
CWBackingPixel: Final = 0x0100
CWOverrideRedirect: Final = 0x0200
CWSaveUnder: Final = 0x0400
CWEventMask: Final = 0x0800
CWDontPropagate: Final = 0x1000
CWColormap: Final = 0x2000
CWCursor: Final = 0x4000
CWX: Final = 0x01
CWY: Final = 0x02
CWWidth: Final = 0x04
CWHeight: Final = 0x08
CWBorderWidth: Final = 0x10
CWSibling: Final = 0x20
CWStackMode: Final = 0x40
ForgetGravity: Final = 0
NorthWestGravity: Final = 1
NorthGravity: Final = 2
NorthEastGravity: Final = 3
WestGravity: Final = 4
CenterGravity: Final = 5
EastGravity: Final = 6
SouthWestGravity: Final = 7
SouthGravity: Final = 8
SouthEastGravity: Final = 9
StaticGravity: Final = 10
UnmapGravity: Final = 0
NotUseful: Final = 0
WhenMapped: Final = 1
Always: Final = 2
IsUnmapped: Final = 0
IsUnviewable: Final = 1
IsViewable: Final = 2
SetModeInsert: Final = 0
SetModeDelete: Final = 1
DestroyAll: Final = 0
RetainPermanent: Final = 1
RetainTemporary: Final = 2
Above: Final = 0
Below: Final = 1
TopIf: Final = 2
BottomIf: Final = 3
Opposite: Final = 4
RaiseLowest: Final = 0
LowerHighest: Final = 1
PropModeReplace: Final = 0
PropModePrepend: Final = 1
PropModeAppend: Final = 2
GXclear: Final = 0x0
GXand: Final = 0x1
GXandReverse: Final = 0x2
GXcopy: Final = 0x3
GXandInverted: Final = 0x4
GXnoop: Final = 0x5
GXxor: Final = 0x6
GXor: Final = 0x7
GXnor: Final = 0x8
GXequiv: Final = 0x9
GXinvert: Final = 0xA
GXorReverse: Final = 0xB
GXcopyInverted: Final = 0xC
GXorInverted: Final = 0xD
GXnand: Final = 0xE
GXset: Final = 0xF
LineSolid: Final = 0
LineOnOffDash: Final = 1
LineDoubleDash: Final = 2
CapNotLast: Final = 0
CapButt: Final = 1
CapRound: Final = 2
CapProjecting: Final = 3
JoinMiter: Final = 0
JoinRound: Final = 1
JoinBevel: Final = 2
FillSolid: Final = 0
FillTiled: Final = 1
FillStippled: Final = 2
FillOpaqueStippled: Final = 3
EvenOddRule: Final = 0
WindingRule: Final = 1
ClipByChildren: Final = 0
IncludeInferiors: Final = 1
Unsorted: Final = 0
YSorted: Final = 1
YXSorted: Final = 2
YXBanded: Final = 3
CoordModeOrigin: Final = 0
CoordModePrevious: Final = 1
Complex: Final = 0
Nonconvex: Final = 1
Convex: Final = 2
ArcChord: Final = 0
ArcPieSlice: Final = 1
GCFunction: Final = 0x000001
GCPlaneMask: Final = 0x000002
GCForeground: Final = 0x000004
GCBackground: Final = 0x000008
GCLineWidth: Final = 0x000010
GCLineStyle: Final = 0x000020
GCCapStyle: Final = 0x000040
GCJoinStyle: Final = 0x000080
GCFillStyle: Final = 0x000100
GCFillRule: Final = 0x000200
GCTile: Final = 0x000400
GCStipple: Final = 0x000800
GCTileStipXOrigin: Final = 0x001000
GCTileStipYOrigin: Final = 0x002000
GCFont: Final = 0x004000
GCSubwindowMode: Final = 0x008000
GCGraphicsExposures: Final = 0x010000
GCClipXOrigin: Final = 0x020000
GCClipYOrigin: Final = 0x040000
GCClipMask: Final = 0x080000
GCDashOffset: Final = 0x100000
GCDashList: Final = 0x200000
GCArcMode: Final = 0x400000
GCLastBit: Final = 22
FontLeftToRight: Final = 0
FontRightToLeft: Final = 1
FontChange: Final = 255
XYBitmap: Final = 0
XYPixmap: Final = 1
ZPixmap: Final = 2
AllocNone: Final = 0
AllocAll: Final = 1
DoRed: Final = 0x1
DoGreen: Final = 0x2
DoBlue: Final = 0x4
CursorShape: Final = 0
TileShape: Final = 1
StippleShape: Final = 2
AutoRepeatModeOff: Final = 0
AutoRepeatModeOn: Final = 1
AutoRepeatModeDefault: Final = 2
LedModeOff: Final = 0
LedModeOn: Final = 1
KBKeyClickPercent: Final = 0x01
KBBellPercent: Final = 0x02
KBBellPitch: Final = 0x04
KBBellDuration: Final = 0x08
KBLed: Final = 0x10
KBLedMode: Final = 0x20
KBKey: Final = 0x40
KBAutoRepeatMode: Final = 0x80
MappingSuccess: Final = 0
MappingBusy: Final = 1
MappingFailed: Final = 2
MappingModifier: Final = 0
MappingKeyboard: Final = 1
MappingPointer: Final = 2
DontPreferBlanking: Final = 0
PreferBlanking: Final = 1
DefaultBlanking: Final = 2
DisableScreenSaver: Final = 0
DisableScreenInterval: Final = 0
DontAllowExposures: Final = 0
AllowExposures: Final = 1
DefaultExposures: Final = 2
ScreenSaverReset: Final = 0
ScreenSaverActive: Final = 1
HostInsert: Final = 0
HostDelete: Final = 1
EnableAccess: Final = 1
DisableAccess: Final = 0
StaticGray: Final = 0
GrayScale: Final = 1
StaticColor: Final = 2
PseudoColor: Final = 3
TrueColor: Final = 4
DirectColor: Final = 5
LSBFirst: Final = 0
MSBFirst: Final = 1
