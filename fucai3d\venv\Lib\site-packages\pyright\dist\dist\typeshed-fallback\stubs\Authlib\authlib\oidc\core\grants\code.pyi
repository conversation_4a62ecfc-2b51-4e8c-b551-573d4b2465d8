from logging import Logger

from authlib.oauth2 import OAuth2Request
from authlib.oauth2.rfc6749 import BaseGrant
from authlib.oidc.core import UserInfo

log: Logger

class OpenIDToken:
    def get_jwt_config(self, grant: BaseGrant) -> dict[str, str | int]: ...
    def generate_user_info(self, user, scope: str) -> UserInfo: ...
    def get_audiences(self, request: OAuth2Request) -> list[str]: ...
    def process_token(self, grant: BaseGrant, response) -> dict[str, str | int]: ...
    def __call__(self, grant: BaseGrant) -> None: ...

class OpenIDCode(OpenIDToken):
    require_nonce: bool
    def __init__(self, require_nonce: bool = False) -> None: ...
    def exists_nonce(self, nonce: str, request: OAuth2Request) -> bool: ...
    def validate_openid_authorization_request(self, grant: BaseGrant, redirect_uri) -> None: ...
    def __call__(self, grant: BaseGrant) -> None: ...
