from _typeshed import Incomplete

from aws_xray_sdk.core.context import Context

from .sampling_rule import SamplingRule

class ServiceConnector:
    def __init__(self) -> None: ...
    def fetch_sampling_rules(self) -> list[SamplingRule]: ...
    def fetch_sampling_target(self, rules) -> tuple[Incomplete, int]: ...
    def setup_xray_client(self, ip: str, port: str | int, client) -> None: ...
    @property
    def context(self) -> Context: ...
    @context.setter
    def context(self, v: Context) -> None: ...
