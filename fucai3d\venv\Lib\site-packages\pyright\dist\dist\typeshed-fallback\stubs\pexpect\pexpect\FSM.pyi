from _typeshed import Incomplete

class ExceptionFSM(Exception):
    value: Incomplete
    def __init__(self, value) -> None: ...

class FSM:
    state_transitions: Incomplete
    state_transitions_any: Incomplete
    default_transition: Incomplete
    input_symbol: Incomplete
    initial_state: Incomplete
    current_state: Incomplete
    next_state: Incomplete
    action: Incomplete
    memory: Incomplete
    def __init__(self, initial_state, memory=None) -> None: ...
    def reset(self) -> None: ...
    def add_transition(self, input_symbol, state, action=None, next_state=None) -> None: ...
    def add_transition_list(self, list_input_symbols, state, action=None, next_state=None) -> None: ...
    def add_transition_any(self, state, action=None, next_state=None) -> None: ...
    def set_default_transition(self, action, next_state) -> None: ...
    def get_transition(self, input_symbol, state): ...
    def process(self, input_symbol) -> None: ...
    def process_list(self, input_symbols) -> None: ...

PY3: Incomplete

def BeginBuildNumber(fsm) -> None: ...
def BuildNumber(fsm) -> None: ...
def EndBuildNumber(fsm) -> None: ...
def DoOperator(fsm) -> None: ...
def DoEqual(fsm) -> None: ...
def Error(fsm) -> None: ...
def main() -> None: ...
