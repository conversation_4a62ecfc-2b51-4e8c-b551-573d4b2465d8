from re import Pattern
from typing import Final

from dateparser.conf import Settings

NUMERAL_PATTERN: Final[Pattern[str]]

class Locale:
    shortname: str
    def __init__(self, shortname: str, language_info) -> None: ...
    def is_applicable(self, date_string: str, strip_timezone: bool = False, settings: Settings | None = None) -> bool: ...
    def count_applicability(self, text: str, strip_timezone: bool = False, settings: Settings | None = None): ...
    @staticmethod
    def clean_dictionary(dictionary, threshold: int = 2): ...
    def translate(self, date_string: str, keep_formatting: bool = False, settings: Settings | None = None) -> str: ...
    def translate_search(self, search_string, settings: Settings | None = None): ...
    def get_wordchars_for_detection(self, settings): ...
    def to_parserinfo(self, base_cls=...): ...
