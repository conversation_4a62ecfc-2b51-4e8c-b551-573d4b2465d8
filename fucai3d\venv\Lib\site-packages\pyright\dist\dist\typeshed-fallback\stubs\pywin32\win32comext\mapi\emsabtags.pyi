from _typeshed import Incomplete

from win32comext.mapi.mapitags import (
    PROP_TAG as PROP_TAG,
    PT_APPTIME as PT_APPTIME,
    PT_BINARY as PT_BINARY,
    PT_BOOLEAN as PT_BOOLEAN,
    PT_CLSID as PT_CLSID,
    PT_CURRENCY as PT_CURRENCY,
    PT_DOUBLE as PT_DOUBLE,
    PT_ERROR as PT_ERROR,
    PT_FLOAT as PT_FLOAT,
    PT_I2 as PT_I2,
    PT_I4 as PT_I4,
    PT_I8 as PT_I8,
    PT_LONG as PT_LONG,
    PT_LONGLONG as PT_LONGLONG,
    PT_MV_APPTIME as PT_MV_APPTIME,
    PT_MV_BINARY as PT_MV_BINARY,
    PT_MV_CLSID as PT_MV_CLSID,
    PT_MV_CURRENCY as PT_MV_CURRENCY,
    PT_MV_DOUBLE as PT_MV_DOUBLE,
    PT_MV_FLOAT as PT_MV_FLOAT,
    PT_MV_I2 as PT_MV_I2,
    PT_MV_I4 as PT_MV_I4,
    PT_MV_I8 as PT_MV_I8,
    PT_MV_LONG as PT_MV_LONG,
    PT_MV_LONGLONG as PT_MV_LONGLONG,
    PT_MV_R4 as PT_MV_R4,
    PT_MV_R8 as PT_MV_R8,
    PT_MV_SHORT as PT_MV_SHORT,
    PT_MV_STRING8 as PT_MV_STRING8,
    PT_MV_SYSTIME as PT_MV_SYSTIME,
    PT_MV_TSTRING as PT_MV_TSTRING,
    PT_MV_UNICODE as PT_MV_UNICODE,
    PT_NULL as PT_NULL,
    PT_OBJECT as PT_OBJECT,
    PT_R4 as PT_R4,
    PT_SHORT as PT_SHORT,
    PT_STRING8 as PT_STRING8,
    PT_SYSTIME as PT_SYSTIME,
    PT_TSTRING as PT_TSTRING,
    PT_UNICODE as PT_UNICODE,
    PT_UNSPECIFIED as PT_UNSPECIFIED,
)

AB_SHOW_PHANTOMS: int
AB_SHOW_OTHERS: int
EMS_AB_ADDRESS_LOOKUP: int
PR_EMS_AB_SERVER: Incomplete
PR_EMS_AB_SERVER_A: Incomplete
PR_EMS_AB_SERVER_W: Incomplete
PR_EMS_AB_CONTAINERID: Incomplete
PR_EMS_AB_DOS_ENTRYID: Incomplete
PR_EMS_AB_PARENT_ENTRYID: Incomplete
PR_EMS_AB_IS_MASTER: Incomplete
PR_EMS_AB_OBJECT_OID: Incomplete
PR_EMS_AB_HIERARCHY_PATH: Incomplete
PR_EMS_AB_HIERARCHY_PATH_A: Incomplete
PR_EMS_AB_HIERARCHY_PATH_W: Incomplete
PR_EMS_AB_CHILD_RDNS: Incomplete
MIN_EMS_AB_CONSTRUCTED_PROP_ID: int
PR_EMS_AB_OTHER_RECIPS: Incomplete
PR_EMS_AB_DISPLAY_NAME_PRINTABLE: Incomplete
PR_EMS_AB_DISPLAY_NAME_PRINTABLE_A: Incomplete
PR_EMS_AB_DISPLAY_NAME_PRINTABLE_W: Incomplete
PR_EMS_AB_ACCESS_CATEGORY: Incomplete
PR_EMS_AB_ACTIVATION_SCHEDULE: Incomplete
PR_EMS_AB_ACTIVATION_STYLE: Incomplete
PR_EMS_AB_ADDRESS_ENTRY_DISPLAY_TABLE: Incomplete
PR_EMS_AB_ADDRESS_ENTRY_DISPLAY_TABLE_MSDOS: Incomplete
PR_EMS_AB_ADDRESS_SYNTAX: Incomplete
PR_EMS_AB_ADDRESS_TYPE: Incomplete
PR_EMS_AB_ADDRESS_TYPE_A: Incomplete
PR_EMS_AB_ADDRESS_TYPE_W: Incomplete
PR_EMS_AB_ADMD: Incomplete
PR_EMS_AB_ADMD_A: Incomplete
PR_EMS_AB_ADMD_W: Incomplete
PR_EMS_AB_ADMIN_DESCRIPTION: Incomplete
PR_EMS_AB_ADMIN_DESCRIPTION_A: Incomplete
PR_EMS_AB_ADMIN_DESCRIPTION_W: Incomplete
PR_EMS_AB_ADMIN_DISPLAY_NAME: Incomplete
PR_EMS_AB_ADMIN_DISPLAY_NAME_A: Incomplete
PR_EMS_AB_ADMIN_DISPLAY_NAME_W: Incomplete
PR_EMS_AB_ADMIN_EXTENSION_DLL: Incomplete
PR_EMS_AB_ADMIN_EXTENSION_DLL_A: Incomplete
PR_EMS_AB_ADMIN_EXTENSION_DLL_W: Incomplete
PR_EMS_AB_ALIASED_OBJECT_NAME: Incomplete
PR_EMS_AB_ALIASED_OBJECT_NAME_A: Incomplete
PR_EMS_AB_ALIASED_OBJECT_NAME_W: Incomplete
PR_EMS_AB_ALIASED_OBJECT_NAME_O: Incomplete
PR_EMS_AB_ALIASED_OBJECT_NAME_T: Incomplete
PR_EMS_AB_ALT_RECIPIENT: Incomplete
PR_EMS_AB_ALT_RECIPIENT_A: Incomplete
PR_EMS_AB_ALT_RECIPIENT_W: Incomplete
PR_EMS_AB_ALT_RECIPIENT_O: Incomplete
PR_EMS_AB_ALT_RECIPIENT_T: Incomplete
PR_EMS_AB_ALT_RECIPIENT_BL: Incomplete
PR_EMS_AB_ALT_RECIPIENT_BL_A: Incomplete
PR_EMS_AB_ALT_RECIPIENT_BL_W: Incomplete
PR_EMS_AB_ALT_RECIPIENT_BL_O: Incomplete
PR_EMS_AB_ALT_RECIPIENT_BL_T: Incomplete
PR_EMS_AB_ANCESTOR_ID: Incomplete
PR_EMS_AB_ASSOC_NT_ACCOUNT: Incomplete
PR_EMS_AB_ASSOC_REMOTE_DXA: Incomplete
PR_EMS_AB_ASSOC_REMOTE_DXA_A: Incomplete
PR_EMS_AB_ASSOC_REMOTE_DXA_W: Incomplete
PR_EMS_AB_ASSOC_REMOTE_DXA_O: Incomplete
PR_EMS_AB_ASSOC_REMOTE_DXA_T: Incomplete
PR_EMS_AB_ASSOCIATION_LIFETIME: Incomplete
PR_EMS_AB_AUTH_ORIG_BL: Incomplete
PR_EMS_AB_AUTH_ORIG_BL_A: Incomplete
PR_EMS_AB_AUTH_ORIG_BL_W: Incomplete
PR_EMS_AB_AUTH_ORIG_BL_O: Incomplete
PR_EMS_AB_AUTH_ORIG_BL_T: Incomplete
PR_EMS_AB_AUTHORITY_REVOCATION_LIST: Incomplete
PR_EMS_AB_AUTHORIZED_DOMAIN: Incomplete
PR_EMS_AB_AUTHORIZED_DOMAIN_A: Incomplete
PR_EMS_AB_AUTHORIZED_DOMAIN_W: Incomplete
PR_EMS_AB_AUTHORIZED_PASSWORD: Incomplete
PR_EMS_AB_AUTHORIZED_USER: Incomplete
PR_EMS_AB_AUTHORIZED_USER_A: Incomplete
PR_EMS_AB_AUTHORIZED_USER_W: Incomplete
PR_EMS_AB_AUTOREPLY: Incomplete
PR_EMS_AB_AUTOREPLY_MESSAGE: Incomplete
PR_EMS_AB_AUTOREPLY_MESSAGE_A: Incomplete
PR_EMS_AB_AUTOREPLY_MESSAGE_W: Incomplete
PR_EMS_AB_AUTOREPLY_SUBJECT: Incomplete
PR_EMS_AB_AUTOREPLY_SUBJECT_A: Incomplete
PR_EMS_AB_AUTOREPLY_SUBJECT_W: Incomplete
PR_EMS_AB_BRIDGEHEAD_SERVERS: Incomplete
PR_EMS_AB_BRIDGEHEAD_SERVERS_A: Incomplete
PR_EMS_AB_BRIDGEHEAD_SERVERS_W: Incomplete
PR_EMS_AB_BRIDGEHEAD_SERVERS_O: Incomplete
PR_EMS_AB_BRIDGEHEAD_SERVERS_T: Incomplete
PR_EMS_AB_BUSINESS_CATEGORY: Incomplete
PR_EMS_AB_BUSINESS_CATEGORY_A: Incomplete
PR_EMS_AB_BUSINESS_CATEGORY_W: Incomplete
PR_EMS_AB_BUSINESS_ROLES: Incomplete
PR_EMS_AB_CA_CERTIFICATE: Incomplete
PR_EMS_AB_CAN_CREATE_PF: Incomplete
PR_EMS_AB_CAN_CREATE_PF_A: Incomplete
PR_EMS_AB_CAN_CREATE_PF_W: Incomplete
PR_EMS_AB_CAN_CREATE_PF_O: Incomplete
PR_EMS_AB_CAN_CREATE_PF_T: Incomplete
PR_EMS_AB_CAN_CREATE_PF_BL: Incomplete
PR_EMS_AB_CAN_CREATE_PF_BL_A: Incomplete
PR_EMS_AB_CAN_CREATE_PF_BL_W: Incomplete
PR_EMS_AB_CAN_CREATE_PF_BL_O: Incomplete
PR_EMS_AB_CAN_CREATE_PF_BL_T: Incomplete
PR_EMS_AB_CAN_CREATE_PF_DL: Incomplete
PR_EMS_AB_CAN_CREATE_PF_DL_A: Incomplete
PR_EMS_AB_CAN_CREATE_PF_DL_W: Incomplete
PR_EMS_AB_CAN_CREATE_PF_DL_O: Incomplete
PR_EMS_AB_CAN_CREATE_PF_DL_T: Incomplete
PR_EMS_AB_CAN_CREATE_PF_DL_BL: Incomplete
PR_EMS_AB_CAN_CREATE_PF_DL_BL_A: Incomplete
PR_EMS_AB_CAN_CREATE_PF_DL_BL_W: Incomplete
PR_EMS_AB_CAN_CREATE_PF_DL_BL_O: Incomplete
PR_EMS_AB_CAN_CREATE_PF_DL_BL_T: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_A: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_W: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_O: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_T: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_BL: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_BL_A: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_BL_W: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_BL_O: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_BL_T: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_DL: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_A: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_W: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_O: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_T: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_BL: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_BL_A: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_BL_W: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_BL_O: Incomplete
PR_EMS_AB_CAN_NOT_CREATE_PF_DL_BL_T: Incomplete
PR_EMS_AB_CAN_PRESERVE_DNS: Incomplete
PR_EMS_AB_CERTIFICATE_REVOCATION_LIST: Incomplete
PR_EMS_AB_CLOCK_ALERT_OFFSET: Incomplete
PR_EMS_AB_CLOCK_ALERT_REPAIR: Incomplete
PR_EMS_AB_CLOCK_WARNING_OFFSET: Incomplete
PR_EMS_AB_CLOCK_WARNING_REPAIR: Incomplete
PR_EMS_AB_COMPUTER_NAME: Incomplete
PR_EMS_AB_COMPUTER_NAME_A: Incomplete
PR_EMS_AB_COMPUTER_NAME_W: Incomplete
PR_EMS_AB_CONNECTED_DOMAINS: Incomplete
PR_EMS_AB_CONNECTED_DOMAINS_A: Incomplete
PR_EMS_AB_CONNECTED_DOMAINS_W: Incomplete
PR_EMS_AB_CONTAINER_INFO: Incomplete
PR_EMS_AB_COST: Incomplete
PR_EMS_AB_COUNTRY_NAME: Incomplete
PR_EMS_AB_COUNTRY_NAME_A: Incomplete
PR_EMS_AB_COUNTRY_NAME_W: Incomplete
PR_EMS_AB_CROSS_CERTIFICATE_PAIR: Incomplete
PR_EMS_AB_DELIV_CONT_LENGTH: Incomplete
PR_EMS_AB_DELIV_EITS: Incomplete
PR_EMS_AB_DELIV_EXT_CONT_TYPES: Incomplete
PR_EMS_AB_DELIVER_AND_REDIRECT: Incomplete
PR_EMS_AB_DELIVERY_MECHANISM: Incomplete
PR_EMS_AB_DESCRIPTION: Incomplete
PR_EMS_AB_DESCRIPTION_A: Incomplete
PR_EMS_AB_DESCRIPTION_W: Incomplete
PR_EMS_AB_DESTINATION_INDICATOR: Incomplete
PR_EMS_AB_DESTINATION_INDICATOR_A: Incomplete
PR_EMS_AB_DESTINATION_INDICATOR_W: Incomplete
PR_EMS_AB_DIAGNOSTIC_REG_KEY: Incomplete
PR_EMS_AB_DIAGNOSTIC_REG_KEY_A: Incomplete
PR_EMS_AB_DIAGNOSTIC_REG_KEY_W: Incomplete
PR_EMS_AB_DISPLAY_NAME_OVERRIDE: Incomplete
PR_EMS_AB_DL_MEM_REJECT_PERMS_BL: Incomplete
PR_EMS_AB_DL_MEM_REJECT_PERMS_BL_A: Incomplete
PR_EMS_AB_DL_MEM_REJECT_PERMS_BL_W: Incomplete
PR_EMS_AB_DL_MEM_REJECT_PERMS_BL_O: Incomplete
PR_EMS_AB_DL_MEM_REJECT_PERMS_BL_T: Incomplete
PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL: Incomplete
PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL_A: Incomplete
PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL_W: Incomplete
PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL_O: Incomplete
PR_EMS_AB_DL_MEM_SUBMIT_PERMS_BL_T: Incomplete
PR_EMS_AB_DL_MEMBER_RULE: Incomplete
PR_EMS_AB_DOMAIN_DEF_ALT_RECIP: Incomplete
PR_EMS_AB_DOMAIN_DEF_ALT_RECIP_A: Incomplete
PR_EMS_AB_DOMAIN_DEF_ALT_RECIP_W: Incomplete
PR_EMS_AB_DOMAIN_DEF_ALT_RECIP_O: Incomplete
PR_EMS_AB_DOMAIN_DEF_ALT_RECIP_T: Incomplete
PR_EMS_AB_DOMAIN_NAME: Incomplete
PR_EMS_AB_DOMAIN_NAME_A: Incomplete
PR_EMS_AB_DOMAIN_NAME_W: Incomplete
PR_EMS_AB_DSA_SIGNATURE: Incomplete
PR_EMS_AB_DXA_ADMIN_COPY: Incomplete
PR_EMS_AB_DXA_ADMIN_FORWARD: Incomplete
PR_EMS_AB_DXA_ADMIN_UPDATE: Incomplete
PR_EMS_AB_DXA_APPEND_REQCN: Incomplete
PR_EMS_AB_DXA_CONF_CONTAINER_LIST: Incomplete
PR_EMS_AB_DXA_CONF_CONTAINER_LIST_A: Incomplete
PR_EMS_AB_DXA_CONF_CONTAINER_LIST_W: Incomplete
PR_EMS_AB_DXA_CONF_CONTAINER_LIST_O: Incomplete
PR_EMS_AB_DXA_CONF_CONTAINER_LIST_T: Incomplete
PR_EMS_AB_DXA_CONF_REQ_TIME: Incomplete
PR_EMS_AB_DXA_CONF_SEQ: Incomplete
PR_EMS_AB_DXA_CONF_SEQ_A: Incomplete
PR_EMS_AB_DXA_CONF_SEQ_W: Incomplete
PR_EMS_AB_DXA_CONF_SEQ_USN: Incomplete
PR_EMS_AB_DXA_EXCHANGE_OPTIONS: Incomplete
PR_EMS_AB_DXA_EXPORT_NOW: Incomplete
PR_EMS_AB_DXA_FLAGS: Incomplete
PR_EMS_AB_DXA_IMP_SEQ: Incomplete
PR_EMS_AB_DXA_IMP_SEQ_A: Incomplete
PR_EMS_AB_DXA_IMP_SEQ_W: Incomplete
PR_EMS_AB_DXA_IMP_SEQ_TIME: Incomplete
PR_EMS_AB_DXA_IMP_SEQ_USN: Incomplete
PR_EMS_AB_DXA_IMPORT_NOW: Incomplete
PR_EMS_AB_DXA_IN_TEMPLATE_MAP: Incomplete
PR_EMS_AB_DXA_IN_TEMPLATE_MAP_A: Incomplete
PR_EMS_AB_DXA_IN_TEMPLATE_MAP_W: Incomplete
PR_EMS_AB_DXA_LOCAL_ADMIN: Incomplete
PR_EMS_AB_DXA_LOCAL_ADMIN_A: Incomplete
PR_EMS_AB_DXA_LOCAL_ADMIN_W: Incomplete
PR_EMS_AB_DXA_LOCAL_ADMIN_O: Incomplete
PR_EMS_AB_DXA_LOCAL_ADMIN_T: Incomplete
PR_EMS_AB_DXA_LOGGING_LEVEL: Incomplete
PR_EMS_AB_DXA_NATIVE_ADDRESS_TYPE: Incomplete
PR_EMS_AB_DXA_NATIVE_ADDRESS_TYPE_A: Incomplete
PR_EMS_AB_DXA_NATIVE_ADDRESS_TYPE_W: Incomplete
PR_EMS_AB_DXA_OUT_TEMPLATE_MAP: Incomplete
PR_EMS_AB_DXA_OUT_TEMPLATE_MAP_A: Incomplete
PR_EMS_AB_DXA_OUT_TEMPLATE_MAP_W: Incomplete
PR_EMS_AB_DXA_PASSWORD: Incomplete
PR_EMS_AB_DXA_PASSWORD_A: Incomplete
PR_EMS_AB_DXA_PASSWORD_W: Incomplete
PR_EMS_AB_DXA_PREV_EXCHANGE_OPTIONS: Incomplete
PR_EMS_AB_DXA_PREV_EXPORT_NATIVE_ONLY: Incomplete
PR_EMS_AB_DXA_PREV_IN_EXCHANGE_SENSITIVITY: Incomplete
PR_EMS_AB_DXA_PREV_REMOTE_ENTRIES: Incomplete
PR_EMS_AB_DXA_PREV_REMOTE_ENTRIES_A: Incomplete
PR_EMS_AB_DXA_PREV_REMOTE_ENTRIES_W: Incomplete
PR_EMS_AB_DXA_PREV_REMOTE_ENTRIES_O: Incomplete
PR_EMS_AB_DXA_PREV_REMOTE_ENTRIES_T: Incomplete
PR_EMS_AB_DXA_PREV_REPLICATION_SENSITIVITY: Incomplete
PR_EMS_AB_DXA_PREV_TEMPLATE_OPTIONS: Incomplete
PR_EMS_AB_DXA_PREV_TYPES: Incomplete
PR_EMS_AB_DXA_RECIPIENT_CP: Incomplete
PR_EMS_AB_DXA_RECIPIENT_CP_A: Incomplete
PR_EMS_AB_DXA_RECIPIENT_CP_W: Incomplete
PR_EMS_AB_DXA_REMOTE_CLIENT: Incomplete
PR_EMS_AB_DXA_REMOTE_CLIENT_A: Incomplete
PR_EMS_AB_DXA_REMOTE_CLIENT_W: Incomplete
PR_EMS_AB_DXA_REMOTE_CLIENT_O: Incomplete
PR_EMS_AB_DXA_REMOTE_CLIENT_T: Incomplete
PR_EMS_AB_DXA_REQ_SEQ: Incomplete
PR_EMS_AB_DXA_REQ_SEQ_A: Incomplete
PR_EMS_AB_DXA_REQ_SEQ_W: Incomplete
PR_EMS_AB_DXA_REQ_SEQ_TIME: Incomplete
PR_EMS_AB_DXA_REQ_SEQ_USN: Incomplete
PR_EMS_AB_DXA_REQNAME: Incomplete
PR_EMS_AB_DXA_REQNAME_A: Incomplete
PR_EMS_AB_DXA_REQNAME_W: Incomplete
PR_EMS_AB_DXA_SVR_SEQ: Incomplete
PR_EMS_AB_DXA_SVR_SEQ_A: Incomplete
PR_EMS_AB_DXA_SVR_SEQ_W: Incomplete
PR_EMS_AB_DXA_SVR_SEQ_TIME: Incomplete
PR_EMS_AB_DXA_SVR_SEQ_USN: Incomplete
PR_EMS_AB_DXA_TASK: Incomplete
PR_EMS_AB_DXA_TEMPLATE_OPTIONS: Incomplete
PR_EMS_AB_DXA_TEMPLATE_TIMESTAMP: Incomplete
PR_EMS_AB_DXA_TYPES: Incomplete
PR_EMS_AB_DXA_UNCONF_CONTAINER_LIST: Incomplete
PR_EMS_AB_DXA_UNCONF_CONTAINER_LIST_A: Incomplete
PR_EMS_AB_DXA_UNCONF_CONTAINER_LIST_W: Incomplete
PR_EMS_AB_DXA_UNCONF_CONTAINER_LIST_O: Incomplete
PR_EMS_AB_DXA_UNCONF_CONTAINER_LIST_T: Incomplete
PR_EMS_AB_ENABLED_PROTOCOLS: Incomplete
PR_EMS_AB_ENCAPSULATION_METHOD: Incomplete
PR_EMS_AB_ENCRYPT: Incomplete
PR_EMS_AB_ENCRYPT_ALG_LIST_NA: Incomplete
PR_EMS_AB_ENCRYPT_ALG_LIST_NA_A: Incomplete
PR_EMS_AB_ENCRYPT_ALG_LIST_NA_W: Incomplete
PR_EMS_AB_ENCRYPT_ALG_LIST_OTHER: Incomplete
PR_EMS_AB_ENCRYPT_ALG_LIST_OTHER_A: Incomplete
PR_EMS_AB_ENCRYPT_ALG_LIST_OTHER_W: Incomplete
PR_EMS_AB_ENCRYPT_ALG_SELECTED_NA: Incomplete
PR_EMS_AB_ENCRYPT_ALG_SELECTED_NA_A: Incomplete
PR_EMS_AB_ENCRYPT_ALG_SELECTED_NA_W: Incomplete
PR_EMS_AB_ENCRYPT_ALG_SELECTED_OTHER: Incomplete
PR_EMS_AB_ENCRYPT_ALG_SELECTED_OTHER_A: Incomplete
PR_EMS_AB_ENCRYPT_ALG_SELECTED_OTHER_W: Incomplete
PR_EMS_AB_EXPAND_DLS_LOCALLY: Incomplete
PR_EMS_AB_EXPIRATION_TIME: Incomplete
PR_EMS_AB_EXPORT_CONTAINERS: Incomplete
PR_EMS_AB_EXPORT_CONTAINERS_A: Incomplete
PR_EMS_AB_EXPORT_CONTAINERS_W: Incomplete
PR_EMS_AB_EXPORT_CONTAINERS_O: Incomplete
PR_EMS_AB_EXPORT_CONTAINERS_T: Incomplete
PR_EMS_AB_EXPORT_CUSTOM_RECIPIENTS: Incomplete
PR_EMS_AB_EXTENDED_CHARS_ALLOWED: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_1: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_1_A: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_1_W: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_10: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_10_A: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_10_W: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_2: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_2_A: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_2_W: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_3: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_3_A: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_3_W: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_4: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_4_A: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_4_W: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_5: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_5_A: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_5_W: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_6: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_6_A: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_6_W: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_7: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_7_A: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_7_W: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_8: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_8_A: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_8_W: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_9: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_9_A: Incomplete
PR_EMS_AB_EXTENSION_ATTRIBUTE_9_W: Incomplete
PR_EMS_AB_EXTENSION_DATA: Incomplete
PR_EMS_AB_EXTENSION_NAME: Incomplete
PR_EMS_AB_EXTENSION_NAME_A: Incomplete
PR_EMS_AB_EXTENSION_NAME_W: Incomplete
PR_EMS_AB_EXTENSION_NAME_INHERITED: Incomplete
PR_EMS_AB_EXTENSION_NAME_INHERITED_A: Incomplete
PR_EMS_AB_EXTENSION_NAME_INHERITED_W: Incomplete
PR_EMS_AB_FACSIMILE_TELEPHONE_NUMBER: Incomplete
PR_EMS_AB_FILE_VERSION: Incomplete
PR_EMS_AB_FILTER_LOCAL_ADDRESSES: Incomplete
PR_EMS_AB_FOLDER_PATHNAME: Incomplete
PR_EMS_AB_FOLDER_PATHNAME_A: Incomplete
PR_EMS_AB_FOLDER_PATHNAME_W: Incomplete
PR_EMS_AB_FOLDERS_CONTAINER: Incomplete
PR_EMS_AB_FOLDERS_CONTAINER_A: Incomplete
PR_EMS_AB_FOLDERS_CONTAINER_W: Incomplete
PR_EMS_AB_FOLDERS_CONTAINER_O: Incomplete
PR_EMS_AB_FOLDERS_CONTAINER_T: Incomplete
PR_EMS_AB_GARBAGE_COLL_PERIOD: Incomplete
PR_EMS_AB_GATEWAY_LOCAL_CRED: Incomplete
PR_EMS_AB_GATEWAY_LOCAL_CRED_A: Incomplete
PR_EMS_AB_GATEWAY_LOCAL_CRED_W: Incomplete
PR_EMS_AB_GATEWAY_LOCAL_DESIG: Incomplete
PR_EMS_AB_GATEWAY_LOCAL_DESIG_A: Incomplete
PR_EMS_AB_GATEWAY_LOCAL_DESIG_W: Incomplete
PR_EMS_AB_GATEWAY_PROXY: Incomplete
PR_EMS_AB_GATEWAY_PROXY_A: Incomplete
PR_EMS_AB_GATEWAY_PROXY_W: Incomplete
PR_EMS_AB_GATEWAY_ROUTING_TREE: Incomplete
PR_EMS_AB_GWART_LAST_MODIFIED: Incomplete
PR_EMS_AB_HAS_FULL_REPLICA_NCS: Incomplete
PR_EMS_AB_HAS_FULL_REPLICA_NCS_A: Incomplete
PR_EMS_AB_HAS_FULL_REPLICA_NCS_W: Incomplete
PR_EMS_AB_HAS_FULL_REPLICA_NCS_O: Incomplete
PR_EMS_AB_HAS_FULL_REPLICA_NCS_T: Incomplete
PR_EMS_AB_HAS_MASTER_NCS: Incomplete
PR_EMS_AB_HAS_MASTER_NCS_A: Incomplete
PR_EMS_AB_HAS_MASTER_NCS_W: Incomplete
PR_EMS_AB_HAS_MASTER_NCS_O: Incomplete
PR_EMS_AB_HAS_MASTER_NCS_T: Incomplete
PR_EMS_AB_HELP_DATA16: Incomplete
PR_EMS_AB_HELP_DATA32: Incomplete
PR_EMS_AB_HELP_FILE_NAME: Incomplete
PR_EMS_AB_HELP_FILE_NAME_A: Incomplete
PR_EMS_AB_HELP_FILE_NAME_W: Incomplete
PR_EMS_AB_HEURISTICS: Incomplete
PR_EMS_AB_HIDE_DL_MEMBERSHIP: Incomplete
PR_EMS_AB_HIDE_FROM_ADDRESS_BOOK: Incomplete
PR_EMS_AB_HOME_MDB: Incomplete
PR_EMS_AB_HOME_MDB_A: Incomplete
PR_EMS_AB_HOME_MDB_W: Incomplete
PR_EMS_AB_HOME_MDB_O: Incomplete
PR_EMS_AB_HOME_MDB_T: Incomplete
PR_EMS_AB_HOME_MDB_BL: Incomplete
PR_EMS_AB_HOME_MDB_BL_A: Incomplete
PR_EMS_AB_HOME_MDB_BL_W: Incomplete
PR_EMS_AB_HOME_MDB_BL_O: Incomplete
PR_EMS_AB_HOME_MDB_BL_T: Incomplete
PR_EMS_AB_HOME_MTA: Incomplete
PR_EMS_AB_HOME_MTA_A: Incomplete
PR_EMS_AB_HOME_MTA_W: Incomplete
PR_EMS_AB_HOME_MTA_O: Incomplete
PR_EMS_AB_HOME_MTA_T: Incomplete
PR_EMS_AB_HOME_PUBLIC_SERVER: Incomplete
PR_EMS_AB_HOME_PUBLIC_SERVER_A: Incomplete
PR_EMS_AB_HOME_PUBLIC_SERVER_W: Incomplete
PR_EMS_AB_HOME_PUBLIC_SERVER_O: Incomplete
PR_EMS_AB_HOME_PUBLIC_SERVER_T: Incomplete
PR_EMS_AB_IMPORT_CONTAINER: Incomplete
PR_EMS_AB_IMPORT_CONTAINER_A: Incomplete
PR_EMS_AB_IMPORT_CONTAINER_W: Incomplete
PR_EMS_AB_IMPORT_CONTAINER_O: Incomplete
PR_EMS_AB_IMPORT_CONTAINER_T: Incomplete
PR_EMS_AB_IMPORT_SENSITIVITY: Incomplete
PR_EMS_AB_IMPORTED_FROM: Incomplete
PR_EMS_AB_IMPORTED_FROM_A: Incomplete
PR_EMS_AB_IMPORTED_FROM_W: Incomplete
PR_EMS_AB_INBOUND_SITES: Incomplete
PR_EMS_AB_INBOUND_SITES_A: Incomplete
PR_EMS_AB_INBOUND_SITES_W: Incomplete
PR_EMS_AB_INBOUND_SITES_O: Incomplete
PR_EMS_AB_INBOUND_SITES_T: Incomplete
PR_EMS_AB_INSTANCE_TYPE: Incomplete
PR_EMS_AB_INTERNATIONAL_ISDN_NUMBER: Incomplete
PR_EMS_AB_INTERNATIONAL_ISDN_NUMBER_A: Incomplete
PR_EMS_AB_INTERNATIONAL_ISDN_NUMBER_W: Incomplete
PR_EMS_AB_INVOCATION_ID: Incomplete
PR_EMS_AB_IS_DELETED: Incomplete
PR_EMS_AB_IS_MEMBER_OF_DL: Incomplete
PR_EMS_AB_IS_MEMBER_OF_DL_A: Incomplete
PR_EMS_AB_IS_MEMBER_OF_DL_W: Incomplete
PR_EMS_AB_IS_MEMBER_OF_DL_O: Incomplete
PR_EMS_AB_IS_MEMBER_OF_DL_T: Incomplete
PR_EMS_AB_IS_SINGLE_VALUED: Incomplete
PR_EMS_AB_KCC_STATUS: Incomplete
PR_EMS_AB_KM_SERVER: Incomplete
PR_EMS_AB_KM_SERVER_A: Incomplete
PR_EMS_AB_KM_SERVER_W: Incomplete
PR_EMS_AB_KM_SERVER_O: Incomplete
PR_EMS_AB_KM_SERVER_T: Incomplete
PR_EMS_AB_KNOWLEDGE_INFORMATION: Incomplete
PR_EMS_AB_KNOWLEDGE_INFORMATION_A: Incomplete
PR_EMS_AB_KNOWLEDGE_INFORMATION_W: Incomplete
PR_EMS_AB_LANGUAGE: Incomplete
PR_EMS_AB_LDAP_DISPLAY_NAME: Incomplete
PR_EMS_AB_LDAP_DISPLAY_NAME_A: Incomplete
PR_EMS_AB_LDAP_DISPLAY_NAME_W: Incomplete
PR_EMS_AB_LINE_WRAP: Incomplete
PR_EMS_AB_LINK_ID: Incomplete
PR_EMS_AB_LOCAL_BRIDGE_HEAD: Incomplete
PR_EMS_AB_LOCAL_BRIDGE_HEAD_A: Incomplete
PR_EMS_AB_LOCAL_BRIDGE_HEAD_W: Incomplete
PR_EMS_AB_LOCAL_BRIDGE_HEAD_ADDRESS: Incomplete
PR_EMS_AB_LOCAL_BRIDGE_HEAD_ADDRESS_A: Incomplete
PR_EMS_AB_LOCAL_BRIDGE_HEAD_ADDRESS_W: Incomplete
PR_EMS_AB_LOCAL_INITIAL_TURN: Incomplete
PR_EMS_AB_LOCAL_SCOPE: Incomplete
PR_EMS_AB_LOCAL_SCOPE_A: Incomplete
PR_EMS_AB_LOCAL_SCOPE_W: Incomplete
PR_EMS_AB_LOCAL_SCOPE_O: Incomplete
PR_EMS_AB_LOCAL_SCOPE_T: Incomplete
PR_EMS_AB_LOG_FILENAME: Incomplete
PR_EMS_AB_LOG_FILENAME_A: Incomplete
PR_EMS_AB_LOG_FILENAME_W: Incomplete
PR_EMS_AB_LOG_ROLLOVER_INTERVAL: Incomplete
PR_EMS_AB_MAINTAIN_AUTOREPLY_HISTORY: Incomplete
PR_EMS_AB_MANAGER: Incomplete
PR_EMS_AB_MANAGER_A: Incomplete
PR_EMS_AB_MANAGER_W: Incomplete
PR_EMS_AB_MANAGER_O: Incomplete
PR_EMS_AB_MANAGER_T: Incomplete
PR_EMS_AB_MAPI_DISPLAY_TYPE: Incomplete
PR_EMS_AB_MAPI_ID: Incomplete
PR_EMS_AB_MAXIMUM_OBJECT_ID: Incomplete
PR_EMS_AB_MDB_BACKOFF_INTERVAL: Incomplete
PR_EMS_AB_MDB_MSG_TIME_OUT_PERIOD: Incomplete
PR_EMS_AB_MDB_OVER_QUOTA_LIMIT: Incomplete
PR_EMS_AB_MDB_STORAGE_QUOTA: Incomplete
PR_EMS_AB_MDB_UNREAD_LIMIT: Incomplete
PR_EMS_AB_MDB_USE_DEFAULTS: Incomplete
PR_EMS_AB_MEMBER: Incomplete
PR_EMS_AB_MEMBER_A: Incomplete
PR_EMS_AB_MEMBER_W: Incomplete
PR_EMS_AB_MEMBER_O: Incomplete
PR_EMS_AB_MEMBER_T: Incomplete
PR_EMS_AB_MESSAGE_TRACKING_ENABLED: Incomplete
PR_EMS_AB_MONITOR_CLOCK: Incomplete
PR_EMS_AB_MONITOR_SERVERS: Incomplete
PR_EMS_AB_MONITOR_SERVICES: Incomplete
PR_EMS_AB_MONITORED_CONFIGURATIONS: Incomplete
PR_EMS_AB_MONITORED_CONFIGURATIONS_A: Incomplete
PR_EMS_AB_MONITORED_CONFIGURATIONS_W: Incomplete
PR_EMS_AB_MONITORED_CONFIGURATIONS_O: Incomplete
PR_EMS_AB_MONITORED_CONFIGURATIONS_T: Incomplete
PR_EMS_AB_MONITORED_SERVERS: Incomplete
PR_EMS_AB_MONITORED_SERVERS_A: Incomplete
PR_EMS_AB_MONITORED_SERVERS_W: Incomplete
PR_EMS_AB_MONITORED_SERVERS_O: Incomplete
PR_EMS_AB_MONITORED_SERVERS_T: Incomplete
PR_EMS_AB_MONITORED_SERVICES: Incomplete
PR_EMS_AB_MONITORED_SERVICES_A: Incomplete
PR_EMS_AB_MONITORED_SERVICES_W: Incomplete
PR_EMS_AB_MONITORING_ALERT_DELAY: Incomplete
PR_EMS_AB_MONITORING_ALERT_UNITS: Incomplete
PR_EMS_AB_MONITORING_AVAILABILITY_STYLE: Incomplete
PR_EMS_AB_MONITORING_AVAILABILITY_WINDOW: Incomplete
PR_EMS_AB_MONITORING_CACHED_VIA_MAIL: Incomplete
PR_EMS_AB_MONITORING_CACHED_VIA_MAIL_A: Incomplete
PR_EMS_AB_MONITORING_CACHED_VIA_MAIL_W: Incomplete
PR_EMS_AB_MONITORING_CACHED_VIA_MAIL_O: Incomplete
PR_EMS_AB_MONITORING_CACHED_VIA_MAIL_T: Incomplete
PR_EMS_AB_MONITORING_CACHED_VIA_RPC: Incomplete
PR_EMS_AB_MONITORING_CACHED_VIA_RPC_A: Incomplete
PR_EMS_AB_MONITORING_CACHED_VIA_RPC_W: Incomplete
PR_EMS_AB_MONITORING_CACHED_VIA_RPC_O: Incomplete
PR_EMS_AB_MONITORING_CACHED_VIA_RPC_T: Incomplete
PR_EMS_AB_MONITORING_ESCALATION_PROCEDURE: Incomplete
PR_EMS_AB_MONITORING_HOTSITE_POLL_INTERVAL: Incomplete
PR_EMS_AB_MONITORING_HOTSITE_POLL_UNITS: Incomplete
PR_EMS_AB_MONITORING_MAIL_UPDATE_INTERVAL: Incomplete
PR_EMS_AB_MONITORING_MAIL_UPDATE_UNITS: Incomplete
PR_EMS_AB_MONITORING_NORMAL_POLL_INTERVAL: Incomplete
PR_EMS_AB_MONITORING_NORMAL_POLL_UNITS: Incomplete
PR_EMS_AB_MONITORING_RECIPIENTS: Incomplete
PR_EMS_AB_MONITORING_RECIPIENTS_A: Incomplete
PR_EMS_AB_MONITORING_RECIPIENTS_W: Incomplete
PR_EMS_AB_MONITORING_RECIPIENTS_O: Incomplete
PR_EMS_AB_MONITORING_RECIPIENTS_T: Incomplete
PR_EMS_AB_MONITORING_RECIPIENTS_NDR: Incomplete
PR_EMS_AB_MONITORING_RECIPIENTS_NDR_A: Incomplete
PR_EMS_AB_MONITORING_RECIPIENTS_NDR_W: Incomplete
PR_EMS_AB_MONITORING_RECIPIENTS_NDR_O: Incomplete
PR_EMS_AB_MONITORING_RECIPIENTS_NDR_T: Incomplete
PR_EMS_AB_MONITORING_RPC_UPDATE_INTERVAL: Incomplete
PR_EMS_AB_MONITORING_RPC_UPDATE_UNITS: Incomplete
PR_EMS_AB_MONITORING_WARNING_DELAY: Incomplete
PR_EMS_AB_MONITORING_WARNING_UNITS: Incomplete
PR_EMS_AB_MTA_LOCAL_CRED: Incomplete
PR_EMS_AB_MTA_LOCAL_CRED_A: Incomplete
PR_EMS_AB_MTA_LOCAL_CRED_W: Incomplete
PR_EMS_AB_MTA_LOCAL_DESIG: Incomplete
PR_EMS_AB_MTA_LOCAL_DESIG_A: Incomplete
PR_EMS_AB_MTA_LOCAL_DESIG_W: Incomplete
PR_EMS_AB_N_ADDRESS: Incomplete
PR_EMS_AB_N_ADDRESS_TYPE: Incomplete
PR_EMS_AB_NETWORK_ADDRESS: Incomplete
PR_EMS_AB_NETWORK_ADDRESS_A: Incomplete
PR_EMS_AB_NETWORK_ADDRESS_W: Incomplete
PR_EMS_AB_NNTP_CHARACTER_SET: Incomplete
PR_EMS_AB_NNTP_CHARACTER_SET_A: Incomplete
PR_EMS_AB_NNTP_CHARACTER_SET_W: Incomplete
PR_EMS_AB_NNTP_CONTENT_FORMAT: Incomplete
PR_EMS_AB_NNTP_CONTENT_FORMAT_A: Incomplete
PR_EMS_AB_NNTP_CONTENT_FORMAT_W: Incomplete
PR_EMS_AB_NT_MACHINE_NAME: Incomplete
PR_EMS_AB_NT_MACHINE_NAME_A: Incomplete
PR_EMS_AB_NT_MACHINE_NAME_W: Incomplete
PR_EMS_AB_NT_SECURITY_DESCRIPTOR: Incomplete
PR_EMS_AB_NUM_OF_OPEN_RETRIES: Incomplete
PR_EMS_AB_NUM_OF_TRANSFER_RETRIES: Incomplete
PR_EMS_AB_OBJ_DIST_NAME: Incomplete
PR_EMS_AB_OBJ_DIST_NAME_A: Incomplete
PR_EMS_AB_OBJ_DIST_NAME_W: Incomplete
PR_EMS_AB_OBJ_DIST_NAME_O: Incomplete
PR_EMS_AB_OBJ_DIST_NAME_T: Incomplete
PR_EMS_AB_OBJECT_CLASS_CATEGORY: Incomplete
PR_EMS_AB_OBJECT_VERSION: Incomplete
PR_EMS_AB_OFF_LINE_AB_CONTAINERS: Incomplete
PR_EMS_AB_OFF_LINE_AB_CONTAINERS_A: Incomplete
PR_EMS_AB_OFF_LINE_AB_CONTAINERS_W: Incomplete
PR_EMS_AB_OFF_LINE_AB_CONTAINERS_O: Incomplete
PR_EMS_AB_OFF_LINE_AB_CONTAINERS_T: Incomplete
PR_EMS_AB_OFF_LINE_AB_SCHEDULE: Incomplete
PR_EMS_AB_OFF_LINE_AB_SERVER: Incomplete
PR_EMS_AB_OFF_LINE_AB_SERVER_A: Incomplete
PR_EMS_AB_OFF_LINE_AB_SERVER_W: Incomplete
PR_EMS_AB_OFF_LINE_AB_SERVER_O: Incomplete
PR_EMS_AB_OFF_LINE_AB_SERVER_T: Incomplete
PR_EMS_AB_OFF_LINE_AB_STYLE: Incomplete
PR_EMS_AB_OID_TYPE: Incomplete
PR_EMS_AB_OM_OBJECT_CLASS: Incomplete
PR_EMS_AB_OM_SYNTAX: Incomplete
PR_EMS_AB_OOF_REPLY_TO_ORIGINATOR: Incomplete
PR_EMS_AB_OPEN_RETRY_INTERVAL: Incomplete
PR_EMS_AB_ORGANIZATION_NAME: Incomplete
PR_EMS_AB_ORGANIZATION_NAME_A: Incomplete
PR_EMS_AB_ORGANIZATION_NAME_W: Incomplete
PR_EMS_AB_ORGANIZATIONAL_UNIT_NAME: Incomplete
PR_EMS_AB_ORGANIZATIONAL_UNIT_NAME_A: Incomplete
PR_EMS_AB_ORGANIZATIONAL_UNIT_NAME_W: Incomplete
PR_EMS_AB_ORIGINAL_DISPLAY_TABLE: Incomplete
PR_EMS_AB_ORIGINAL_DISPLAY_TABLE_MSDOS: Incomplete
PR_EMS_AB_OUTBOUND_SITES: Incomplete
PR_EMS_AB_OUTBOUND_SITES_A: Incomplete
PR_EMS_AB_OUTBOUND_SITES_W: Incomplete
PR_EMS_AB_OUTBOUND_SITES_O: Incomplete
PR_EMS_AB_OUTBOUND_SITES_T: Incomplete
PR_EMS_AB_OWNER: Incomplete
PR_EMS_AB_OWNER_A: Incomplete
PR_EMS_AB_OWNER_W: Incomplete
PR_EMS_AB_OWNER_O: Incomplete
PR_EMS_AB_OWNER_T: Incomplete
PR_EMS_AB_OWNER_BL: Incomplete
PR_EMS_AB_OWNER_BL_A: Incomplete
PR_EMS_AB_OWNER_BL_W: Incomplete
PR_EMS_AB_OWNER_BL_O: Incomplete
PR_EMS_AB_OWNER_BL_T: Incomplete
PR_EMS_AB_P_SELECTOR: Incomplete
PR_EMS_AB_P_SELECTOR_INBOUND: Incomplete
PR_EMS_AB_PER_MSG_DIALOG_DISPLAY_TABLE: Incomplete
PR_EMS_AB_PER_RECIP_DIALOG_DISPLAY_TABLE: Incomplete
PR_EMS_AB_PERIOD_REP_SYNC_TIMES: Incomplete
PR_EMS_AB_PERIOD_REPL_STAGGER: Incomplete
PR_EMS_AB_PF_CONTACTS: Incomplete
PR_EMS_AB_PF_CONTACTS_A: Incomplete
PR_EMS_AB_PF_CONTACTS_W: Incomplete
PR_EMS_AB_PF_CONTACTS_O: Incomplete
PR_EMS_AB_PF_CONTACTS_T: Incomplete
PR_EMS_AB_POP_CHARACTER_SET: Incomplete
PR_EMS_AB_POP_CHARACTER_SET_A: Incomplete
PR_EMS_AB_POP_CHARACTER_SET_W: Incomplete
PR_EMS_AB_POP_CONTENT_FORMAT: Incomplete
PR_EMS_AB_POP_CONTENT_FORMAT_A: Incomplete
PR_EMS_AB_POP_CONTENT_FORMAT_W: Incomplete
PR_EMS_AB_POSTAL_ADDRESS: Incomplete
PR_EMS_AB_PREFERRED_DELIVERY_METHOD: Incomplete
PR_EMS_AB_PRMD: Incomplete
PR_EMS_AB_PRMD_A: Incomplete
PR_EMS_AB_PRMD_W: Incomplete
PR_EMS_AB_PROXY_ADDRESSES: Incomplete
PR_EMS_AB_PROXY_ADDRESSES_A: Incomplete
PR_EMS_AB_PROXY_ADDRESSES_W: Incomplete
PR_EMS_AB_PROXY_GENERATOR_DLL: Incomplete
PR_EMS_AB_PROXY_GENERATOR_DLL_A: Incomplete
PR_EMS_AB_PROXY_GENERATOR_DLL_W: Incomplete
PR_EMS_AB_PUBLIC_DELEGATES: Incomplete
PR_EMS_AB_PUBLIC_DELEGATES_A: Incomplete
PR_EMS_AB_PUBLIC_DELEGATES_W: Incomplete
PR_EMS_AB_PUBLIC_DELEGATES_O: Incomplete
PR_EMS_AB_PUBLIC_DELEGATES_T: Incomplete
PR_EMS_AB_PUBLIC_DELEGATES_BL: Incomplete
PR_EMS_AB_PUBLIC_DELEGATES_BL_A: Incomplete
PR_EMS_AB_PUBLIC_DELEGATES_BL_W: Incomplete
PR_EMS_AB_PUBLIC_DELEGATES_BL_O: Incomplete
PR_EMS_AB_PUBLIC_DELEGATES_BL_T: Incomplete
PR_EMS_AB_QUOTA_NOTIFICATION_SCHEDULE: Incomplete
PR_EMS_AB_QUOTA_NOTIFICATION_STYLE: Incomplete
PR_EMS_AB_RANGE_LOWER: Incomplete
PR_EMS_AB_RANGE_UPPER: Incomplete
PR_EMS_AB_RAS_CALLBACK_NUMBER: Incomplete
PR_EMS_AB_RAS_CALLBACK_NUMBER_A: Incomplete
PR_EMS_AB_RAS_CALLBACK_NUMBER_W: Incomplete
PR_EMS_AB_RAS_PHONE_NUMBER: Incomplete
PR_EMS_AB_RAS_PHONE_NUMBER_A: Incomplete
PR_EMS_AB_RAS_PHONE_NUMBER_W: Incomplete
PR_EMS_AB_RAS_PHONEBOOK_ENTRY_NAME: Incomplete
PR_EMS_AB_RAS_PHONEBOOK_ENTRY_NAME_A: Incomplete
PR_EMS_AB_RAS_PHONEBOOK_ENTRY_NAME_W: Incomplete
PR_EMS_AB_RAS_REMOTE_SRVR_NAME: Incomplete
PR_EMS_AB_RAS_REMOTE_SRVR_NAME_A: Incomplete
PR_EMS_AB_RAS_REMOTE_SRVR_NAME_W: Incomplete
PR_EMS_AB_REGISTERED_ADDRESS: Incomplete
PR_EMS_AB_REMOTE_BRIDGE_HEAD: Incomplete
PR_EMS_AB_REMOTE_BRIDGE_HEAD_A: Incomplete
PR_EMS_AB_REMOTE_BRIDGE_HEAD_W: Incomplete
PR_EMS_AB_REMOTE_BRIDGE_HEAD_ADDRESS: Incomplete
PR_EMS_AB_REMOTE_BRIDGE_HEAD_ADDRESS_A: Incomplete
PR_EMS_AB_REMOTE_BRIDGE_HEAD_ADDRESS_W: Incomplete
PR_EMS_AB_REMOTE_OUT_BH_SERVER: Incomplete
PR_EMS_AB_REMOTE_OUT_BH_SERVER_A: Incomplete
PR_EMS_AB_REMOTE_OUT_BH_SERVER_W: Incomplete
PR_EMS_AB_REMOTE_OUT_BH_SERVER_O: Incomplete
PR_EMS_AB_REMOTE_OUT_BH_SERVER_T: Incomplete
PR_EMS_AB_REMOTE_SITE: Incomplete
PR_EMS_AB_REMOTE_SITE_A: Incomplete
PR_EMS_AB_REMOTE_SITE_W: Incomplete
PR_EMS_AB_REMOTE_SITE_O: Incomplete
PR_EMS_AB_REMOTE_SITE_T: Incomplete
PR_EMS_AB_REPLICATION_MAIL_MSG_SIZE: Incomplete
PR_EMS_AB_REPLICATION_SENSITIVITY: Incomplete
PR_EMS_AB_REPLICATION_STAGGER: Incomplete
PR_EMS_AB_REPORT_TO_ORIGINATOR: Incomplete
PR_EMS_AB_REPORT_TO_OWNER: Incomplete
PR_EMS_AB_REPORTS: Incomplete
PR_EMS_AB_REPORTS_A: Incomplete
PR_EMS_AB_REPORTS_W: Incomplete
PR_EMS_AB_REPORTS_O: Incomplete
PR_EMS_AB_REPORTS_T: Incomplete
PR_EMS_AB_REQ_SEQ: Incomplete
PR_EMS_AB_RESPONSIBLE_LOCAL_DXA: Incomplete
PR_EMS_AB_RESPONSIBLE_LOCAL_DXA_A: Incomplete
PR_EMS_AB_RESPONSIBLE_LOCAL_DXA_W: Incomplete
PR_EMS_AB_RESPONSIBLE_LOCAL_DXA_O: Incomplete
PR_EMS_AB_RESPONSIBLE_LOCAL_DXA_T: Incomplete
PR_EMS_AB_RID_SERVER: Incomplete
PR_EMS_AB_RID_SERVER_A: Incomplete
PR_EMS_AB_RID_SERVER_W: Incomplete
PR_EMS_AB_RID_SERVER_O: Incomplete
PR_EMS_AB_RID_SERVER_T: Incomplete
PR_EMS_AB_ROLE_OCCUPANT: Incomplete
PR_EMS_AB_ROLE_OCCUPANT_A: Incomplete
PR_EMS_AB_ROLE_OCCUPANT_W: Incomplete
PR_EMS_AB_ROLE_OCCUPANT_O: Incomplete
PR_EMS_AB_ROLE_OCCUPANT_T: Incomplete
PR_EMS_AB_ROUTING_LIST: Incomplete
PR_EMS_AB_ROUTING_LIST_A: Incomplete
PR_EMS_AB_ROUTING_LIST_W: Incomplete
PR_EMS_AB_RTS_CHECKPOINT_SIZE: Incomplete
PR_EMS_AB_RTS_RECOVERY_TIMEOUT: Incomplete
PR_EMS_AB_RTS_WINDOW_SIZE: Incomplete
PR_EMS_AB_RUNS_ON: Incomplete
PR_EMS_AB_RUNS_ON_A: Incomplete
PR_EMS_AB_RUNS_ON_W: Incomplete
PR_EMS_AB_RUNS_ON_O: Incomplete
PR_EMS_AB_RUNS_ON_T: Incomplete
PR_EMS_AB_S_SELECTOR: Incomplete
PR_EMS_AB_S_SELECTOR_INBOUND: Incomplete
PR_EMS_AB_SCHEMA_FLAGS: Incomplete
PR_EMS_AB_SCHEMA_VERSION: Incomplete
PR_EMS_AB_SEARCH_FLAGS: Incomplete
PR_EMS_AB_SEARCH_GUIDE: Incomplete
PR_EMS_AB_SECURITY_PROTOCOL: Incomplete
PR_EMS_AB_SEE_ALSO: Incomplete
PR_EMS_AB_SEE_ALSO_A: Incomplete
PR_EMS_AB_SEE_ALSO_W: Incomplete
PR_EMS_AB_SEE_ALSO_O: Incomplete
PR_EMS_AB_SEE_ALSO_T: Incomplete
PR_EMS_AB_SERIAL_NUMBER: Incomplete
PR_EMS_AB_SERIAL_NUMBER_A: Incomplete
PR_EMS_AB_SERIAL_NUMBER_W: Incomplete
PR_EMS_AB_SERVICE_ACTION_FIRST: Incomplete
PR_EMS_AB_SERVICE_ACTION_OTHER: Incomplete
PR_EMS_AB_SERVICE_ACTION_SECOND: Incomplete
PR_EMS_AB_SERVICE_RESTART_DELAY: Incomplete
PR_EMS_AB_SERVICE_RESTART_MESSAGE: Incomplete
PR_EMS_AB_SERVICE_RESTART_MESSAGE_A: Incomplete
PR_EMS_AB_SERVICE_RESTART_MESSAGE_W: Incomplete
PR_EMS_AB_SESSION_DISCONNECT_TIMER: Incomplete
PR_EMS_AB_SITE_AFFINITY: Incomplete
PR_EMS_AB_SITE_AFFINITY_A: Incomplete
PR_EMS_AB_SITE_AFFINITY_W: Incomplete
PR_EMS_AB_SITE_FOLDER_GUID: Incomplete
PR_EMS_AB_SITE_FOLDER_SERVER: Incomplete
PR_EMS_AB_SITE_FOLDER_SERVER_A: Incomplete
PR_EMS_AB_SITE_FOLDER_SERVER_W: Incomplete
PR_EMS_AB_SITE_FOLDER_SERVER_O: Incomplete
PR_EMS_AB_SITE_FOLDER_SERVER_T: Incomplete
PR_EMS_AB_SITE_PROXY_SPACE: Incomplete
PR_EMS_AB_SITE_PROXY_SPACE_A: Incomplete
PR_EMS_AB_SITE_PROXY_SPACE_W: Incomplete
PR_EMS_AB_SPACE_LAST_COMPUTED: Incomplete
PR_EMS_AB_STREET_ADDRESS: Incomplete
PR_EMS_AB_STREET_ADDRESS_A: Incomplete
PR_EMS_AB_STREET_ADDRESS_W: Incomplete
PR_EMS_AB_SUB_REFS: Incomplete
PR_EMS_AB_SUB_REFS_A: Incomplete
PR_EMS_AB_SUB_REFS_W: Incomplete
PR_EMS_AB_SUB_REFS_O: Incomplete
PR_EMS_AB_SUB_REFS_T: Incomplete
PR_EMS_AB_SUB_SITE: Incomplete
PR_EMS_AB_SUB_SITE_A: Incomplete
PR_EMS_AB_SUB_SITE_W: Incomplete
PR_EMS_AB_SUBMISSION_CONT_LENGTH: Incomplete
PR_EMS_AB_SUPPORTED_APPLICATION_CONTEXT: Incomplete
PR_EMS_AB_SUPPORTING_STACK: Incomplete
PR_EMS_AB_SUPPORTING_STACK_A: Incomplete
PR_EMS_AB_SUPPORTING_STACK_W: Incomplete
PR_EMS_AB_SUPPORTING_STACK_O: Incomplete
PR_EMS_AB_SUPPORTING_STACK_T: Incomplete
PR_EMS_AB_SUPPORTING_STACK_BL: Incomplete
PR_EMS_AB_SUPPORTING_STACK_BL_A: Incomplete
PR_EMS_AB_SUPPORTING_STACK_BL_W: Incomplete
PR_EMS_AB_SUPPORTING_STACK_BL_O: Incomplete
PR_EMS_AB_SUPPORTING_STACK_BL_T: Incomplete
PR_EMS_AB_T_SELECTOR: Incomplete
PR_EMS_AB_T_SELECTOR_INBOUND: Incomplete
PR_EMS_AB_TARGET_ADDRESS: Incomplete
PR_EMS_AB_TARGET_ADDRESS_A: Incomplete
PR_EMS_AB_TARGET_ADDRESS_W: Incomplete
PR_EMS_AB_TARGET_MTAS: Incomplete
PR_EMS_AB_TARGET_MTAS_A: Incomplete
PR_EMS_AB_TARGET_MTAS_W: Incomplete
PR_EMS_AB_TELEPHONE_NUMBER: Incomplete
PR_EMS_AB_TELEPHONE_NUMBER_A: Incomplete
PR_EMS_AB_TELEPHONE_NUMBER_W: Incomplete
PR_EMS_AB_TELETEX_TERMINAL_IDENTIFIER: Incomplete
PR_EMS_AB_TEMP_ASSOC_THRESHOLD: Incomplete
PR_EMS_AB_TOMBSTONE_LIFETIME: Incomplete
PR_EMS_AB_TRACKING_LOG_PATH_NAME: Incomplete
PR_EMS_AB_TRACKING_LOG_PATH_NAME_A: Incomplete
PR_EMS_AB_TRACKING_LOG_PATH_NAME_W: Incomplete
PR_EMS_AB_TRANS_RETRY_MINS: Incomplete
PR_EMS_AB_TRANS_TIMEOUT_MINS: Incomplete
PR_EMS_AB_TRANSFER_RETRY_INTERVAL: Incomplete
PR_EMS_AB_TRANSFER_TIMEOUT_NON_URGENT: Incomplete
PR_EMS_AB_TRANSFER_TIMEOUT_NORMAL: Incomplete
PR_EMS_AB_TRANSFER_TIMEOUT_URGENT: Incomplete
PR_EMS_AB_TRANSLATION_TABLE_USED: Incomplete
PR_EMS_AB_TRANSPORT_EXPEDITED_DATA: Incomplete
PR_EMS_AB_TRUST_LEVEL: Incomplete
PR_EMS_AB_TURN_REQUEST_THRESHOLD: Incomplete
PR_EMS_AB_TWO_WAY_ALTERNATE_FACILITY: Incomplete
PR_EMS_AB_UNAUTH_ORIG_BL: Incomplete
PR_EMS_AB_UNAUTH_ORIG_BL_A: Incomplete
PR_EMS_AB_UNAUTH_ORIG_BL_W: Incomplete
PR_EMS_AB_UNAUTH_ORIG_BL_O: Incomplete
PR_EMS_AB_UNAUTH_ORIG_BL_T: Incomplete
PR_EMS_AB_USE_SERVER_VALUES: Incomplete
PR_EMS_AB_USER_PASSWORD: Incomplete
PR_EMS_AB_USN_CHANGED: Incomplete
PR_EMS_AB_USN_CREATED: Incomplete
PR_EMS_AB_USN_DSA_LAST_OBJ_REMOVED: Incomplete
PR_EMS_AB_USN_INTERSITE: Incomplete
PR_EMS_AB_USN_LAST_OBJ_REM: Incomplete
PR_EMS_AB_USN_SOURCE: Incomplete
PR_EMS_AB_WWW_HOME_PAGE: Incomplete
PR_EMS_AB_WWW_HOME_PAGE_A: Incomplete
PR_EMS_AB_WWW_HOME_PAGE_W: Incomplete
PR_EMS_AB_X121_ADDRESS: Incomplete
PR_EMS_AB_X121_ADDRESS_A: Incomplete
PR_EMS_AB_X121_ADDRESS_W: Incomplete
PR_EMS_AB_X25_CALL_USER_DATA_INCOMING: Incomplete
PR_EMS_AB_X25_CALL_USER_DATA_OUTGOING: Incomplete
PR_EMS_AB_X25_FACILITIES_DATA_INCOMING: Incomplete
PR_EMS_AB_X25_FACILITIES_DATA_OUTGOING: Incomplete
PR_EMS_AB_X25_LEASED_LINE_PORT: Incomplete
PR_EMS_AB_X25_LEASED_OR_SWITCHED: Incomplete
PR_EMS_AB_X25_REMOTE_MTA_PHONE: Incomplete
PR_EMS_AB_X25_REMOTE_MTA_PHONE_A: Incomplete
PR_EMS_AB_X25_REMOTE_MTA_PHONE_W: Incomplete
PR_EMS_AB_X400_ATTACHMENT_TYPE: Incomplete
PR_EMS_AB_X400_SELECTOR_SYNTAX: Incomplete
PR_EMS_AB_X500_ACCESS_CONTROL_LIST: Incomplete
PR_EMS_AB_XMIT_TIMEOUT_NON_URGENT: Incomplete
PR_EMS_AB_XMIT_TIMEOUT_NORMAL: Incomplete
PR_EMS_AB_XMIT_TIMEOUT_URGENT: Incomplete
