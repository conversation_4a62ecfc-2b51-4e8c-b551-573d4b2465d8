import re
from _typeshed import Incomplete
from collections.abc import Callable
from typing import Final

UNQUOTE_PAIRS: Final[re.Pattern[str]]
unquote: Callable[..., str]
tchar: Final[str]
token: Incomplete  # types from pyparsing library
token68: Incomplete
quoted_string: Incomplete
auth_param_name: Incomplete
auth_param: Incomplete
params: Incomplete
scheme: Incomplete
challenge: Incomplete
authentication_info: Incomplete
www_authenticate: Incomplete
downcaseTokens: Incomplete
