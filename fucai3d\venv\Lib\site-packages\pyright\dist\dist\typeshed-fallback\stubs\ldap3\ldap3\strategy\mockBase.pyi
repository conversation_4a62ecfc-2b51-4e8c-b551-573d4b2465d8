from typing import Any

SEARCH_CONTROLS: Any
SERVER_ENCODING: str

def random_cookie(): ...

class PagedSearchSet:
    size: Any
    response: Any
    cookie: Any
    sent: int
    done: bool
    def __init__(self, response, size, criticality) -> None: ...
    def next(self, size=None): ...

class MockBaseStrategy:
    entries: Any
    no_real_dsa: bool
    bound: Any
    custom_validators: Any
    operational_attributes: Any
    def __init__(self) -> None: ...
    def add_entry(self, dn, attributes, validate: bool = True): ...
    def remove_entry(self, dn): ...
    def entries_from_json(self, json_entry_file) -> None: ...
    def mock_bind(self, request_message, controls): ...
    def mock_delete(self, request_message, controls): ...
    def mock_add(self, request_message, controls): ...
    def mock_compare(self, request_message, controls): ...
    def mock_modify_dn(self, request_message, controls): ...
    def mock_modify(self, request_message, controls): ...
    def mock_search(self, request_message, controls): ...
    def mock_extended(self, request_message, controls): ...
    def evaluate_filter_node(self, node, candidates): ...
    def equal(self, dn, attribute_type, value_to_check): ...
    def send(self, message_type, request, controls=None): ...
