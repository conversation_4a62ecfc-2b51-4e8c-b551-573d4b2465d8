from hvac.api.system_backend.system_backend_mixin import SystemBackendMixin

class Key(SystemBackendMixin):
    def read_root_generation_progress(self): ...
    def start_root_token_generation(self, otp=None, pgp_key=None): ...
    def generate_root(self, key, nonce): ...
    def cancel_root_generation(self): ...
    def get_encryption_key_status(self): ...
    def rotate_encryption_key(self): ...
    def read_rekey_progress(self, recovery_key: bool = False): ...
    def start_rekey(
        self,
        secret_shares: int = 5,
        secret_threshold: int = 3,
        pgp_keys=None,
        backup: bool = False,
        require_verification: bool = False,
        recovery_key: bool = False,
    ): ...
    def cancel_rekey(self, recovery_key: bool = False): ...
    def rekey(self, key, nonce=None, recovery_key: bool = False): ...
    def rekey_multi(self, keys, nonce=None, recovery_key: bool = False): ...
    def read_backup_keys(self, recovery_key: bool = False): ...
    def cancel_rekey_verify(self): ...
    def rekey_verify(self, key, nonce): ...
    def rekey_verify_multi(self, keys, nonce): ...
    def read_rekey_verify_progress(self): ...
