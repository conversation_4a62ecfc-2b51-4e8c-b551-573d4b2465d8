from _typeshed import Incomplete
from typing_extensions import Self

class PayPalPayeeInput:
    def __init__(self, email_address: str | None = None, client_id: str | None = None) -> None: ...
    def to_graphql_variables(self) -> dict[str, Incomplete]: ...
    @staticmethod
    def builder() -> Builder: ...

    class Builder:
        def __init__(self) -> None: ...
        def email_address(self, email_address: str) -> Self: ...
        def client_id(self, client_id: str) -> Self: ...
        def build(self) -> PayPalPayeeInput: ...
