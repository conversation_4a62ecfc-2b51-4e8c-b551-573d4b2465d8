from _typeshed import Incomplete
from typing import Literal, overload
from xml.etree.ElementTree import Element

from ._inputstream import _InputStream
from ._tokenizer import HTMLTokenizer
from .treebuilders.base import TreeBuilder

@overload
def parse(
    doc: _InputStream, treebuilder: Literal["etree"] = "etree", namespaceHTMLElements: bool = True, **kwargs
) -> Element: ...
@overload
def parse(doc: _InputStream, treebuilder: str, namespaceHTMLElements: bool = True, **kwargs): ...
def parseFragment(
    doc: _InputStream, container: str = "div", treebuilder: str = "etree", namespaceHTMLElements: bool = True, **kwargs
): ...
def method_decorator_metaclass(function): ...

class HTMLParser:
    strict: bool
    tree: Incomplete
    errors: list[Incomplete]
    phases: Incomplete
    def __init__(
        self,
        tree: str | type[TreeBuilder] | None = None,
        strict: bool = False,
        namespaceHTMLElements: bool = True,
        debug: bool = False,
    ) -> None: ...
    firstStartTag: bool
    log: Incomplete
    compatMode: str
    container: str
    innerHTML: Incomplete
    phase: Incomplete
    lastPhase: Incomplete
    beforeRCDataPhase: Incomplete
    framesetOK: bool
    tokenizer: HTMLTokenizer
    def reset(self) -> None: ...
    @property
    def documentEncoding(self) -> str | None: ...
    def isHTMLIntegrationPoint(self, element: Element) -> bool: ...
    def isMathMLTextIntegrationPoint(self, element: Element) -> bool: ...
    def mainLoop(self) -> None: ...
    def parse(self, stream: _InputStream, scripting: bool = ..., **kwargs): ...
    def parseFragment(self, stream: _InputStream, *args, **kwargs): ...
    def parseError(self, errorcode: str = "XXX-undefined-error", datavars=None) -> None: ...
    def adjustMathMLAttributes(self, token) -> None: ...
    def adjustSVGAttributes(self, token) -> None: ...
    def adjustForeignAttributes(self, token) -> None: ...
    def reparseTokenNormal(self, token) -> None: ...
    def resetInsertionMode(self) -> None: ...
    originalPhase: Incomplete
    def parseRCDataRawtext(self, token, contentType) -> None: ...

def getPhases(debug): ...
def adjust_attributes(token, replacements) -> None: ...
def impliedTagToken(name, type: str = "EndTag", attributes=None, selfClosing: bool = False): ...

class ParseError(Exception): ...
