from collections.abc import Iterator, Mapping
from typing import Any
from typing_extensions import Self
from xml.sax import handler, xmlreader

def is_string(x: object) -> bool: ...

class Element:
    children: list[Element]
    is_root: bool
    cdata: str
    def __init__(self, name: str | None, attributes: Mapping[str, Any] | None) -> None: ...
    def add_child(self, element: Element) -> None: ...
    def add_cdata(self, cdata: str) -> None: ...
    def get_attribute(self, key: str) -> Any | None: ...
    def get_elements(self, name: str | None = ...) -> list[Element]: ...
    def __getitem__(self, key: str) -> Any | None: ...
    def __getattr__(self, key: str) -> Element: ...
    def __hasattribute__(self, name: str) -> bool: ...
    def __iter__(self) -> Iterator[Self]: ...
    def __bool__(self) -> bool: ...
    __nonzero__ = __bool__
    def __eq__(self, val: object) -> bool: ...
    def __dir__(self) -> list[str]: ...
    def __len__(self) -> int: ...
    def __contains__(self, key: str) -> bool: ...

class Handler(handler.ContentHandler):
    root: Element
    elements: list[Element]
    def __init__(self) -> None: ...
    def startElement(self, name: str, attributes: xmlreader.AttributesImpl) -> None: ...
    def endElement(self, name: str) -> None: ...
    def characters(self, cdata: str) -> None: ...

def parse(filename: str, **parser_features: bool) -> Element: ...
def is_url(string: str) -> bool: ...
