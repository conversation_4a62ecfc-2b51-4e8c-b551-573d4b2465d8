from _typeshed import Incomplete

def encode_client_secret_basic(client, method, uri, headers, body): ...
def encode_client_secret_post(client, method, uri, headers, body): ...
def encode_none(client, method, uri, headers, body): ...

class ClientAuth:
    DEFAULT_AUTH_METHODS: Incomplete
    client_id: Incomplete
    client_secret: Incomplete
    auth_method: Incomplete
    def __init__(self, client_id, client_secret, auth_method=None) -> None: ...
    def prepare(self, method, uri, headers, body): ...

class TokenAuth:
    DEFAULT_TOKEN_TYPE: str
    SIGN_METHODS: Incomplete
    token: Incomplete
    token_placement: Incomplete
    client: Incomplete
    hooks: Incomplete
    def __init__(self, token, token_placement: str = "header", client=None) -> None: ...
    def set_token(self, token) -> None: ...
    def prepare(self, uri, headers, body): ...
    def __del__(self) -> None: ...
