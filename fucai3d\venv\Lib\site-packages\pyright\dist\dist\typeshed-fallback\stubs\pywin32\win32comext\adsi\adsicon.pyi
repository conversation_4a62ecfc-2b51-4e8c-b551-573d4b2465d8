from _typeshed import Incomplete

ADS_ATTR_CLEAR: int
ADS_ATTR_UPDATE: int
ADS_ATTR_APPEND: int
ADS_ATTR_DELETE: int
ADS_EXT_MINEXTDISPID: int
ADS_EXT_MAXEXTDISPID: int
ADS_EXT_INITCREDENTIALS: int
ADS_EXT_INITIALIZE_COMPLETE: int
ADS_SEARCHPREF_ASYNCHRONOUS: int
ADS_SEARCHPREF_DEREF_ALIASES: int
ADS_SEARCHPREF_SIZE_LIMIT: int
ADS_SEARCHPREF_TIME_LIMIT: int
ADS_SEARCHPREF_ATTRIBTYPES_ONLY: int
ADS_SEARCHPREF_SEARCH_SCOPE: int
ADS_SEARCHPREF_TIMEOUT: int
ADS_SEARCHPREF_PAGESIZE: int
ADS_SEARCHPREF_PAGED_TIME_LIMIT: int
ADS_SEARCHPREF_CHASE_REFERRALS: int
ADS_SEARCHPREF_SORT_ON: int
ADS_SEARCHPREF_CACHE_RESULTS: int
ADS_SEARCHPREF_DIRSYNC: int
ADS_SEARCHPREF_TOMBSTONE: int
ADS_SCOPE_BASE: int
ADS_SCOPE_ONELEVEL: int
ADS_SCOPE_SUBTREE: int
ADS_SECURE_AUTHENTICATION: int
ADS_USE_ENCRYPTION: int
ADS_USE_SSL: int
ADS_READONLY_SERVER: int
ADS_PROMPT_CREDENTIALS: int
ADS_NO_AUTHENTICATION: int
ADS_FAST_BIND: int
ADS_USE_SIGNING: int
ADS_USE_SEALING: int
ADS_USE_DELEGATION: int
ADS_SERVER_BIND: int
ADSTYPE_INVALID: int
ADSTYPE_DN_STRING: Incomplete
ADSTYPE_CASE_EXACT_STRING: Incomplete
ADSTYPE_CASE_IGNORE_STRING: Incomplete
ADSTYPE_PRINTABLE_STRING: Incomplete
ADSTYPE_NUMERIC_STRING: Incomplete
ADSTYPE_BOOLEAN: Incomplete
ADSTYPE_INTEGER: Incomplete
ADSTYPE_OCTET_STRING: Incomplete
ADSTYPE_UTC_TIME: Incomplete
ADSTYPE_LARGE_INTEGER: Incomplete
ADSTYPE_PROV_SPECIFIC: Incomplete
ADSTYPE_OBJECT_CLASS: Incomplete
ADSTYPE_CASEIGNORE_LIST: Incomplete
ADSTYPE_OCTET_LIST: Incomplete
ADSTYPE_PATH: Incomplete
ADSTYPE_POSTALADDRESS: Incomplete
ADSTYPE_TIMESTAMP: Incomplete
ADSTYPE_BACKLINK: Incomplete
ADSTYPE_TYPEDNAME: Incomplete
ADSTYPE_HOLD: Incomplete
ADSTYPE_NETADDRESS: Incomplete
ADSTYPE_REPLICAPOINTER: Incomplete
ADSTYPE_FAXNUMBER: Incomplete
ADSTYPE_EMAIL: Incomplete
ADSTYPE_NT_SECURITY_DESCRIPTOR: Incomplete
ADSTYPE_UNKNOWN: Incomplete
ADSTYPE_DN_WITH_BINARY: Incomplete
ADSTYPE_DN_WITH_STRING: Incomplete
ADS_PROPERTY_CLEAR: int
ADS_PROPERTY_UPDATE: int
ADS_PROPERTY_APPEND: int
ADS_PROPERTY_DELETE: int
ADS_SYSTEMFLAG_DISALLOW_DELETE: int
ADS_SYSTEMFLAG_CONFIG_ALLOW_RENAME: int
ADS_SYSTEMFLAG_CONFIG_ALLOW_MOVE: int
ADS_SYSTEMFLAG_CONFIG_ALLOW_LIMITED_MOVE: int
ADS_SYSTEMFLAG_DOMAIN_DISALLOW_RENAME: int
ADS_SYSTEMFLAG_DOMAIN_DISALLOW_MOVE: int
ADS_SYSTEMFLAG_CR_NTDS_NC: int
ADS_SYSTEMFLAG_CR_NTDS_DOMAIN: int
ADS_SYSTEMFLAG_ATTR_NOT_REPLICATED: int
ADS_SYSTEMFLAG_ATTR_IS_CONSTRUCTED: int
ADS_GROUP_TYPE_GLOBAL_GROUP: int
ADS_GROUP_TYPE_DOMAIN_LOCAL_GROUP: int
ADS_GROUP_TYPE_LOCAL_GROUP: int
ADS_GROUP_TYPE_UNIVERSAL_GROUP: int
ADS_GROUP_TYPE_SECURITY_ENABLED: int
ADS_UF_SCRIPT: int
ADS_UF_ACCOUNTDISABLE: int
ADS_UF_HOMEDIR_REQUIRED: int
ADS_UF_LOCKOUT: int
ADS_UF_PASSWD_NOTREQD: int
ADS_UF_PASSWD_CANT_CHANGE: int
ADS_UF_ENCRYPTED_TEXT_PASSWORD_ALLOWED: int
ADS_UF_TEMP_DUPLICATE_ACCOUNT: int
ADS_UF_NORMAL_ACCOUNT: int
ADS_UF_INTERDOMAIN_TRUST_ACCOUNT: int
ADS_UF_WORKSTATION_TRUST_ACCOUNT: int
ADS_UF_SERVER_TRUST_ACCOUNT: int
ADS_UF_DONT_EXPIRE_PASSWD: int
ADS_UF_MNS_LOGON_ACCOUNT: int
ADS_UF_SMARTCARD_REQUIRED: int
ADS_UF_TRUSTED_FOR_DELEGATION: int
ADS_UF_NOT_DELEGATED: int
ADS_UF_USE_DES_KEY_ONLY: int
ADS_UF_DONT_REQUIRE_PREAUTH: int
ADS_UF_PASSWORD_EXPIRED: int
ADS_UF_TRUSTED_TO_AUTHENTICATE_FOR_DELEGATION: int
ADS_RIGHT_DELETE: int
ADS_RIGHT_READ_CONTROL: int
ADS_RIGHT_WRITE_DAC: int
ADS_RIGHT_WRITE_OWNER: int
ADS_RIGHT_SYNCHRONIZE: int
ADS_RIGHT_ACCESS_SYSTEM_SECURITY: int
ADS_RIGHT_GENERIC_READ: int
ADS_RIGHT_GENERIC_WRITE: int
ADS_RIGHT_GENERIC_EXECUTE: int
ADS_RIGHT_GENERIC_ALL: int
ADS_RIGHT_DS_CREATE_CHILD: int
ADS_RIGHT_DS_DELETE_CHILD: int
ADS_RIGHT_ACTRL_DS_LIST: int
ADS_RIGHT_DS_SELF: int
ADS_RIGHT_DS_READ_PROP: int
ADS_RIGHT_DS_WRITE_PROP: int
ADS_RIGHT_DS_DELETE_TREE: int
ADS_RIGHT_DS_LIST_OBJECT: int
ADS_RIGHT_DS_CONTROL_ACCESS: int
ADS_ACETYPE_ACCESS_ALLOWED: int
ADS_ACETYPE_ACCESS_DENIED: int
ADS_ACETYPE_SYSTEM_AUDIT: int
ADS_ACETYPE_ACCESS_ALLOWED_OBJECT: int
ADS_ACETYPE_ACCESS_DENIED_OBJECT: int
ADS_ACETYPE_SYSTEM_AUDIT_OBJECT: int
ADS_ACETYPE_SYSTEM_ALARM_OBJECT: int
ADS_ACETYPE_ACCESS_ALLOWED_CALLBACK: int
ADS_ACETYPE_ACCESS_DENIED_CALLBACK: int
ADS_ACETYPE_ACCESS_ALLOWED_CALLBACK_OBJECT: int
ADS_ACETYPE_ACCESS_DENIED_CALLBACK_OBJECT: int
ADS_ACETYPE_SYSTEM_AUDIT_CALLBACK: int
ADS_ACETYPE_SYSTEM_ALARM_CALLBACK: int
ADS_ACETYPE_SYSTEM_AUDIT_CALLBACK_OBJECT: int
ADS_ACETYPE_SYSTEM_ALARM_CALLBACK_OBJECT: int
ADS_ACEFLAG_INHERIT_ACE: int
ADS_ACEFLAG_NO_PROPAGATE_INHERIT_ACE: int
ADS_ACEFLAG_INHERIT_ONLY_ACE: int
ADS_ACEFLAG_INHERITED_ACE: int
ADS_ACEFLAG_VALID_INHERIT_FLAGS: int
ADS_ACEFLAG_SUCCESSFUL_ACCESS: int
ADS_ACEFLAG_FAILED_ACCESS: int
ADS_FLAG_OBJECT_TYPE_PRESENT: int
ADS_FLAG_INHERITED_OBJECT_TYPE_PRESENT: int
ADS_SD_CONTROL_SE_OWNER_DEFAULTED: int
ADS_SD_CONTROL_SE_GROUP_DEFAULTED: int
ADS_SD_CONTROL_SE_DACL_PRESENT: int
ADS_SD_CONTROL_SE_DACL_DEFAULTED: int
ADS_SD_CONTROL_SE_SACL_PRESENT: int
ADS_SD_CONTROL_SE_SACL_DEFAULTED: int
ADS_SD_CONTROL_SE_DACL_AUTO_INHERIT_REQ: int
ADS_SD_CONTROL_SE_SACL_AUTO_INHERIT_REQ: int
ADS_SD_CONTROL_SE_DACL_AUTO_INHERITED: int
ADS_SD_CONTROL_SE_SACL_AUTO_INHERITED: int
ADS_SD_CONTROL_SE_DACL_PROTECTED: int
ADS_SD_CONTROL_SE_SACL_PROTECTED: int
ADS_SD_CONTROL_SE_SELF_RELATIVE: int
ADS_SD_REVISION_DS: int
ADS_NAME_TYPE_1779: int
ADS_NAME_TYPE_CANONICAL: int
ADS_NAME_TYPE_NT4: int
ADS_NAME_TYPE_DISPLAY: int
ADS_NAME_TYPE_DOMAIN_SIMPLE: int
ADS_NAME_TYPE_ENTERPRISE_SIMPLE: int
ADS_NAME_TYPE_GUID: int
ADS_NAME_TYPE_UNKNOWN: int
ADS_NAME_TYPE_USER_PRINCIPAL_NAME: int
ADS_NAME_TYPE_CANONICAL_EX: int
ADS_NAME_TYPE_SERVICE_PRINCIPAL_NAME: int
ADS_NAME_TYPE_SID_OR_SID_HISTORY_NAME: int
ADS_NAME_INITTYPE_DOMAIN: int
ADS_NAME_INITTYPE_SERVER: int
ADS_NAME_INITTYPE_GC: int
ADS_OPTION_SERVERNAME: int
ADS_OPTION_REFERRALS: Incomplete
ADS_OPTION_PAGE_SIZE: Incomplete
ADS_OPTION_SECURITY_MASK: Incomplete
ADS_OPTION_MUTUAL_AUTH_STATUS: Incomplete
ADS_OPTION_QUOTA: Incomplete
ADS_OPTION_PASSWORD_PORTNUMBER: Incomplete
ADS_OPTION_PASSWORD_METHOD: Incomplete
ADS_SECURITY_INFO_OWNER: int
ADS_SECURITY_INFO_GROUP: int
ADS_SECURITY_INFO_DACL: int
ADS_SECURITY_INFO_SACL: int
ADS_SETTYPE_FULL: int
ADS_SETTYPE_PROVIDER: int
ADS_SETTYPE_SERVER: int
ADS_SETTYPE_DN: int
ADS_FORMAT_WINDOWS: int
ADS_FORMAT_WINDOWS_NO_SERVER: int
ADS_FORMAT_WINDOWS_DN: int
ADS_FORMAT_WINDOWS_PARENT: int
ADS_FORMAT_X500: int
ADS_FORMAT_X500_NO_SERVER: int
ADS_FORMAT_X500_DN: int
ADS_FORMAT_X500_PARENT: int
ADS_FORMAT_SERVER: int
ADS_FORMAT_PROVIDER: int
ADS_FORMAT_LEAF: int
ADS_DISPLAY_FULL: int
ADS_DISPLAY_VALUE_ONLY: int
ADS_ESCAPEDMODE_DEFAULT: int
ADS_ESCAPEDMODE_ON: int
ADS_ESCAPEDMODE_OFF: int
ADS_ESCAPEDMODE_OFF_EX: int
ADS_PATH_FILE: int
ADS_PATH_FILESHARE: int
ADS_PATH_REGISTRY: int
ADS_SD_FORMAT_IID: int
ADS_SD_FORMAT_RAW: int
ADS_SD_FORMAT_HEXSTRING: int
E_ADS_BAD_PATHNAME: Incomplete
E_ADS_INVALID_DOMAIN_OBJECT: Incomplete
E_ADS_INVALID_USER_OBJECT: Incomplete
E_ADS_INVALID_COMPUTER_OBJECT: Incomplete
E_ADS_UNKNOWN_OBJECT: Incomplete
E_ADS_PROPERTY_NOT_SET: Incomplete
E_ADS_PROPERTY_NOT_SUPPORTED: Incomplete
E_ADS_PROPERTY_INVALID: Incomplete
E_ADS_BAD_PARAMETER: Incomplete
E_ADS_OBJECT_UNBOUND: Incomplete
E_ADS_PROPERTY_NOT_MODIFIED: Incomplete
E_ADS_PROPERTY_MODIFIED: Incomplete
E_ADS_CANT_CONVERT_DATATYPE: Incomplete
E_ADS_PROPERTY_NOT_FOUND: Incomplete
E_ADS_OBJECT_EXISTS: Incomplete
E_ADS_SCHEMA_VIOLATION: Incomplete
E_ADS_COLUMN_NOT_SET: Incomplete
S_ADS_ERRORSOCCURRED: Incomplete
S_ADS_NOMORE_ROWS: Incomplete
S_ADS_NOMORE_COLUMNS: Incomplete
E_ADS_INVALID_FILTER: Incomplete
ADS_DEREF_NEVER: int
ADS_DEREF_SEARCHING: int
ADS_DEREF_FINDING: int
ADS_DEREF_ALWAYS: int
ADSIPROP_ASYNCHRONOUS: int
ADSIPROP_DEREF_ALIASES: int
ADSIPROP_SIZE_LIMIT: int
ADSIPROP_TIME_LIMIT: int
ADSIPROP_ATTRIBTYPES_ONLY: int
ADSIPROP_SEARCH_SCOPE: int
ADSIPROP_TIMEOUT: int
ADSIPROP_PAGESIZE: int
ADSIPROP_PAGED_TIME_LIMIT: int
ADSIPROP_CHASE_REFERRALS: int
ADSIPROP_SORT_ON: int
ADSIPROP_CACHE_RESULTS: int
ADSIPROP_ADSIFLAG: int
ADSI_DIALECT_LDAP: int
ADSI_DIALECT_SQL: int
ADS_CHASE_REFERRALS_NEVER: int
ADS_CHASE_REFERRALS_SUBORDINATE: int
ADS_CHASE_REFERRALS_EXTERNAL: int
ADS_CHASE_REFERRALS_ALWAYS: Incomplete
DSOP_SCOPE_TYPE_TARGET_COMPUTER: int
DSOP_SCOPE_TYPE_UPLEVEL_JOINED_DOMAIN: int
DSOP_SCOPE_TYPE_DOWNLEVEL_JOINED_DOMAIN: int
DSOP_SCOPE_TYPE_ENTERPRISE_DOMAIN: int
DSOP_SCOPE_TYPE_GLOBAL_CATALOG: int
DSOP_SCOPE_TYPE_EXTERNAL_UPLEVEL_DOMAIN: int
DSOP_SCOPE_TYPE_EXTERNAL_DOWNLEVEL_DOMAIN: int
DSOP_SCOPE_TYPE_WORKGROUP: int
DSOP_SCOPE_TYPE_USER_ENTERED_UPLEVEL_SCOPE: int
DSOP_SCOPE_TYPE_USER_ENTERED_DOWNLEVEL_SCOPE: int
DSOP_SCOPE_FLAG_STARTING_SCOPE: int
DSOP_SCOPE_FLAG_WANT_PROVIDER_WINNT: int
DSOP_SCOPE_FLAG_WANT_PROVIDER_LDAP: int
DSOP_SCOPE_FLAG_WANT_PROVIDER_GC: int
DSOP_SCOPE_FLAG_WANT_SID_PATH: int
DSOP_SCOPE_FLAG_WANT_DOWNLEVEL_BUILTIN_PATH: int
DSOP_SCOPE_FLAG_DEFAULT_FILTER_USERS: int
DSOP_SCOPE_FLAG_DEFAULT_FILTER_GROUPS: int
DSOP_SCOPE_FLAG_DEFAULT_FILTER_COMPUTERS: int
DSOP_SCOPE_FLAG_DEFAULT_FILTER_CONTACTS: int
DSOP_FILTER_INCLUDE_ADVANCED_VIEW: int
DSOP_FILTER_USERS: int
DSOP_FILTER_BUILTIN_GROUPS: int
DSOP_FILTER_WELL_KNOWN_PRINCIPALS: int
DSOP_FILTER_UNIVERSAL_GROUPS_DL: int
DSOP_FILTER_UNIVERSAL_GROUPS_SE: int
DSOP_FILTER_GLOBAL_GROUPS_DL: int
DSOP_FILTER_GLOBAL_GROUPS_SE: int
DSOP_FILTER_DOMAIN_LOCAL_GROUPS_DL: int
DSOP_FILTER_DOMAIN_LOCAL_GROUPS_SE: int
DSOP_FILTER_CONTACTS: int
DSOP_FILTER_COMPUTERS: int
DSOP_DOWNLEVEL_FILTER_USERS: int
DSOP_DOWNLEVEL_FILTER_LOCAL_GROUPS: int
DSOP_DOWNLEVEL_FILTER_GLOBAL_GROUPS: int
DSOP_DOWNLEVEL_FILTER_COMPUTERS: int
DSOP_DOWNLEVEL_FILTER_WORLD: int
DSOP_DOWNLEVEL_FILTER_AUTHENTICATED_USER: int
DSOP_DOWNLEVEL_FILTER_ANONYMOUS: int
DSOP_DOWNLEVEL_FILTER_BATCH: int
DSOP_DOWNLEVEL_FILTER_CREATOR_OWNER: int
DSOP_DOWNLEVEL_FILTER_CREATOR_GROUP: int
DSOP_DOWNLEVEL_FILTER_DIALUP: int
DSOP_DOWNLEVEL_FILTER_INTERACTIVE: int
DSOP_DOWNLEVEL_FILTER_NETWORK: int
DSOP_DOWNLEVEL_FILTER_SERVICE: int
DSOP_DOWNLEVEL_FILTER_SYSTEM: int
DSOP_DOWNLEVEL_FILTER_EXCLUDE_BUILTIN_GROUPS: int
DSOP_DOWNLEVEL_FILTER_TERMINAL_SERVER: int
DSOP_DOWNLEVEL_FILTER_ALL_WELLKNOWN_SIDS: int
DSOP_DOWNLEVEL_FILTER_LOCAL_SERVICE: int
DSOP_DOWNLEVEL_FILTER_NETWORK_SERVICE: int
DSOP_DOWNLEVEL_FILTER_REMOTE_LOGON: int
DSOP_FLAG_MULTISELECT: int
DSOP_FLAG_SKIP_TARGET_COMPUTER_DC_CHECK: int
CFSTR_DSOP_DS_SELECTION_LIST: str
