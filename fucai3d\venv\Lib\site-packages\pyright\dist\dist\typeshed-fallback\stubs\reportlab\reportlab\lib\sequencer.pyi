__all__ = ["Sequencer", "getSequencer", "setSequencer"]

class _Counter:
    def __init__(self) -> None: ...
    def setFormatter(self, formatFunc) -> None: ...
    def reset(self, value=None) -> None: ...
    def next(self): ...
    __next__ = next
    def nextf(self): ...
    def thisf(self): ...
    def chain(self, otherCounter) -> None: ...

class Sequencer:
    def __init__(self) -> None: ...
    def __next__(self): ...
    def next(self, counter=None): ...
    def thisf(self, counter=None): ...
    def nextf(self, counter=None): ...
    def setDefaultCounter(self, default=None) -> None: ...
    def registerFormat(self, format, func) -> None: ...
    def setFormat(self, counter, format) -> None: ...
    def reset(self, counter=None, base: int = 0) -> None: ...
    def chain(self, parent, child) -> None: ...
    def __getitem__(self, key): ...
    def format(self, template): ...
    def dump(self) -> None: ...

def getSequencer(): ...
def setSequencer(seq): ...
