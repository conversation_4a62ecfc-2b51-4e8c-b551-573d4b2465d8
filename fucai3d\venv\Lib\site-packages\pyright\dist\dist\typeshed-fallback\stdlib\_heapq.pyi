import sys
from typing import Any, Final, TypeVar

_T = TypeVar("_T")  # list items must be comparable

__about__: Final[str]

def heapify(heap: list[Any], /) -> None: ...  # list items must be comparable
def heappop(heap: list[_T], /) -> _T: ...
def heappush(heap: list[_T], item: _T, /) -> None: ...
def heappushpop(heap: list[_T], item: _T, /) -> _T: ...
def heapreplace(heap: list[_T], item: _T, /) -> _T: ...

if sys.version_info >= (3, 14):
    def heapify_max(heap: list[Any], /) -> None: ...  # list items must be comparable
    def heappop_max(heap: list[_T], /) -> _T: ...
    def heappush_max(heap: list[_T], item: _T, /) -> None: ...
    def heappushpop_max(heap: list[_T], item: _T, /) -> _T: ...
    def heapreplace_max(heap: list[_T], item: _T, /) -> _T: ...
