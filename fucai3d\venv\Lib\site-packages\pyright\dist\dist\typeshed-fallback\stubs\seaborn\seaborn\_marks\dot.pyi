from dataclasses import dataclass

from seaborn._marks.base import (
    MappableBool,
    MappableColor,
    MappableFloat,
    MappableString,
    MappableStyle,
    Mark,
    document_properties,
)

class DotBase(Mark): ...

@document_properties
@dataclass
class Dot(DotBase):
    marker: MappableString = ...
    pointsize: MappableFloat = ...
    stroke: MappableFloat = ...
    color: MappableColor = ...
    alpha: MappableFloat = ...
    fill: MappableBool = ...
    edgecolor: MappableColor = ...
    edgealpha: MappableFloat = ...
    edgewidth: MappableFloat = ...
    edgestyle: MappableStyle = ...

@document_properties
@dataclass
class Dots(DotBase):
    marker: MappableString = ...
    pointsize: MappableFloat = ...
    stroke: MappableFloat = ...
    color: MappableColor = ...
    alpha: MappableFloat = ...
    fill: MappableBool = ...
    fillcolor: MappableColor = ...
    fillalpha: MappableFloat = ...
