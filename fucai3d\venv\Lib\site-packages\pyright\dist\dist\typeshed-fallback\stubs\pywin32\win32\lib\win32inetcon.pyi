INTERNET_INVALID_PORT_NUMBER: int
INTERNET_DEFAULT_FTP_PORT: int
INTERNET_DEFAULT_GOPHER_PORT: int
INTERNET_DEFAULT_HTTP_PORT: int
INTERNET_DEFAULT_HTTPS_PORT: int
INTERNET_DEFAULT_SOCKS_PORT: int
INTERNET_MAX_HOST_NAME_LENGTH: int
INTERNET_MAX_USER_NAME_LENGTH: int
INTERNET_MAX_PASSWORD_LENGTH: int
INTERNET_MAX_PORT_NUMBER_LENGTH: int
INTERNET_MAX_PORT_NUMBER_VALUE: int
INTERNET_MAX_PATH_LENGTH: int
INTERNET_MAX_SCHEME_LENGTH: int
INTERNET_KEEP_ALIVE_ENABLED: int
INTERNET_KEEP_ALIVE_DISABLED: int
INTERNET_REQFLAG_FROM_CACHE: int
INTERNET_REQFLAG_ASYNC: int
INTERNET_REQFLAG_VIA_PROXY: int
INTERNET_REQFLAG_NO_HEADERS: int
INTERNET_REQFLAG_PASSIVE: int
INTERNET_REQFLAG_CACHE_WRITE_DISABLED: int
INTERNET_REQFLAG_NET_TIMEOUT: int
INTERNET_FLAG_RELOAD: int
INTERNET_FLAG_RAW_DATA: int
INTERNET_FLAG_EXISTING_CONNECT: int
INTERNET_FLAG_ASYNC: int
INTERNET_FLAG_PASSIVE: int
INTERNET_FLAG_NO_CACHE_WRITE: int
INTERNET_FLAG_DONT_CACHE: int
INTERNET_FLAG_MAKE_PERSISTENT: int
INTERNET_FLAG_FROM_CACHE: int
INTERNET_FLAG_OFFLINE: int
INTERNET_FLAG_SECURE: int
INTERNET_FLAG_KEEP_CONNECTION: int
INTERNET_FLAG_NO_AUTO_REDIRECT: int
INTERNET_FLAG_READ_PREFETCH: int
INTERNET_FLAG_NO_COOKIES: int
INTERNET_FLAG_NO_AUTH: int
INTERNET_FLAG_RESTRICTED_ZONE: int
INTERNET_FLAG_CACHE_IF_NET_FAIL: int
INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTP: int
INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTPS: int
INTERNET_FLAG_IGNORE_CERT_DATE_INVALID: int
INTERNET_FLAG_IGNORE_CERT_CN_INVALID: int
INTERNET_FLAG_RESYNCHRONIZE: int
INTERNET_FLAG_HYPERLINK: int
INTERNET_FLAG_NO_UI: int
INTERNET_FLAG_PRAGMA_NOCACHE: int
INTERNET_FLAG_CACHE_ASYNC: int
INTERNET_FLAG_FORMS_SUBMIT: int
INTERNET_FLAG_FWD_BACK: int
INTERNET_FLAG_NEED_FILE: int
INTERNET_FLAG_MUST_CACHE_REQUEST: int
SECURITY_INTERNET_MASK: int
INTERNET_ERROR_MASK_INSERT_CDROM: int
INTERNET_ERROR_MASK_COMBINED_SEC_CERT: int
INTERNET_ERROR_MASK_NEED_MSN_SSPI_PKG: int
INTERNET_ERROR_MASK_LOGIN_FAILURE_DISPLAY_ENTITY_BODY: int
WININET_API_FLAG_ASYNC: int
WININET_API_FLAG_SYNC: int
WININET_API_FLAG_USE_CONTEXT: int
INTERNET_NO_CALLBACK: int
IDSI_FLAG_KEEP_ALIVE: int
IDSI_FLAG_SECURE: int
IDSI_FLAG_PROXY: int
IDSI_FLAG_TUNNEL: int
INTERNET_PER_CONN_FLAGS: int
INTERNET_PER_CONN_PROXY_SERVER: int
INTERNET_PER_CONN_PROXY_BYPASS: int
INTERNET_PER_CONN_AUTOCONFIG_URL: int
INTERNET_PER_CONN_AUTODISCOVERY_FLAGS: int
INTERNET_PER_CONN_AUTOCONFIG_SECONDARY_URL: int
INTERNET_PER_CONN_AUTOCONFIG_RELOAD_DELAY_MINS: int
INTERNET_PER_CONN_AUTOCONFIG_LAST_DETECT_TIME: int
INTERNET_PER_CONN_AUTOCONFIG_LAST_DETECT_URL: int
PROXY_TYPE_DIRECT: int
PROXY_TYPE_PROXY: int
PROXY_TYPE_AUTO_PROXY_URL: int
PROXY_TYPE_AUTO_DETECT: int
AUTO_PROXY_FLAG_USER_SET: int
AUTO_PROXY_FLAG_ALWAYS_DETECT: int
AUTO_PROXY_FLAG_DETECTION_RUN: int
AUTO_PROXY_FLAG_MIGRATED: int
AUTO_PROXY_FLAG_DONT_CACHE_PROXY_RESULT: int
AUTO_PROXY_FLAG_CACHE_INIT_RUN: int
AUTO_PROXY_FLAG_DETECTION_SUSPECT: int
ISO_FORCE_DISCONNECTED: int
INTERNET_RFC1123_FORMAT: int
INTERNET_RFC1123_BUFSIZE: int
ICU_ESCAPE: int
ICU_USERNAME: int
ICU_NO_ENCODE: int
ICU_DECODE: int
ICU_NO_META: int
ICU_ENCODE_SPACES_ONLY: int
ICU_BROWSER_MODE: int
ICU_ENCODE_PERCENT: int
INTERNET_OPEN_TYPE_PRECONFIG: int
INTERNET_OPEN_TYPE_DIRECT: int
INTERNET_OPEN_TYPE_PROXY: int
INTERNET_OPEN_TYPE_PRECONFIG_WITH_NO_AUTOPROXY: int
PRE_CONFIG_INTERNET_ACCESS: int
LOCAL_INTERNET_ACCESS: int
CERN_PROXY_INTERNET_ACCESS: int
INTERNET_SERVICE_FTP: int
INTERNET_SERVICE_GOPHER: int
INTERNET_SERVICE_HTTP: int
IRF_ASYNC: int
IRF_SYNC: int
IRF_USE_CONTEXT: int
IRF_NO_WAIT: int
ISO_GLOBAL: int
ISO_REGISTRY: int
ISO_VALID_FLAGS: int
INTERNET_OPTION_CALLBACK: int
INTERNET_OPTION_CONNECT_TIMEOUT: int
INTERNET_OPTION_CONNECT_RETRIES: int
INTERNET_OPTION_CONNECT_BACKOFF: int
INTERNET_OPTION_SEND_TIMEOUT: int
INTERNET_OPTION_CONTROL_SEND_TIMEOUT: int
INTERNET_OPTION_RECEIVE_TIMEOUT: int
INTERNET_OPTION_CONTROL_RECEIVE_TIMEOUT: int
INTERNET_OPTION_DATA_SEND_TIMEOUT: int
INTERNET_OPTION_DATA_RECEIVE_TIMEOUT: int
INTERNET_OPTION_HANDLE_TYPE: int
INTERNET_OPTION_READ_BUFFER_SIZE: int
INTERNET_OPTION_WRITE_BUFFER_SIZE: int
INTERNET_OPTION_ASYNC_ID: int
INTERNET_OPTION_ASYNC_PRIORITY: int
INTERNET_OPTION_PARENT_HANDLE: int
INTERNET_OPTION_KEEP_CONNECTION: int
INTERNET_OPTION_REQUEST_FLAGS: int
INTERNET_OPTION_EXTENDED_ERROR: int
INTERNET_OPTION_OFFLINE_MODE: int
INTERNET_OPTION_CACHE_STREAM_HANDLE: int
INTERNET_OPTION_USERNAME: int
INTERNET_OPTION_PASSWORD: int
INTERNET_OPTION_ASYNC: int
INTERNET_OPTION_SECURITY_FLAGS: int
INTERNET_OPTION_SECURITY_CERTIFICATE_STRUCT: int
INTERNET_OPTION_DATAFILE_NAME: int
INTERNET_OPTION_URL: int
INTERNET_OPTION_SECURITY_CERTIFICATE: int
INTERNET_OPTION_SECURITY_KEY_BITNESS: int
INTERNET_OPTION_REFRESH: int
INTERNET_OPTION_PROXY: int
INTERNET_OPTION_SETTINGS_CHANGED: int
INTERNET_OPTION_VERSION: int
INTERNET_OPTION_USER_AGENT: int
INTERNET_OPTION_END_BROWSER_SESSION: int
INTERNET_OPTION_PROXY_USERNAME: int
INTERNET_OPTION_PROXY_PASSWORD: int
INTERNET_OPTION_CONTEXT_VALUE: int
INTERNET_OPTION_CONNECT_LIMIT: int
INTERNET_OPTION_SECURITY_SELECT_CLIENT_CERT: int
INTERNET_OPTION_POLICY: int
INTERNET_OPTION_DISCONNECTED_TIMEOUT: int
INTERNET_OPTION_CONNECTED_STATE: int
INTERNET_OPTION_IDLE_STATE: int
INTERNET_OPTION_OFFLINE_SEMANTICS: int
INTERNET_OPTION_SECONDARY_CACHE_KEY: int
INTERNET_OPTION_CALLBACK_FILTER: int
INTERNET_OPTION_CONNECT_TIME: int
INTERNET_OPTION_SEND_THROUGHPUT: int
INTERNET_OPTION_RECEIVE_THROUGHPUT: int
INTERNET_OPTION_REQUEST_PRIORITY: int
INTERNET_OPTION_HTTP_VERSION: int
INTERNET_OPTION_RESET_URLCACHE_SESSION: int
INTERNET_OPTION_ERROR_MASK: int
INTERNET_OPTION_FROM_CACHE_TIMEOUT: int
INTERNET_OPTION_BYPASS_EDITED_ENTRY: int
INTERNET_OPTION_DIAGNOSTIC_SOCKET_INFO: int
INTERNET_OPTION_CODEPAGE: int
INTERNET_OPTION_CACHE_TIMESTAMPS: int
INTERNET_OPTION_DISABLE_AUTODIAL: int
INTERNET_OPTION_MAX_CONNS_PER_SERVER: int
INTERNET_OPTION_MAX_CONNS_PER_1_0_SERVER: int
INTERNET_OPTION_PER_CONNECTION_OPTION: int
INTERNET_OPTION_DIGEST_AUTH_UNLOAD: int
INTERNET_OPTION_IGNORE_OFFLINE: int
INTERNET_OPTION_IDENTITY: int
INTERNET_OPTION_REMOVE_IDENTITY: int
INTERNET_OPTION_ALTER_IDENTITY: int
INTERNET_OPTION_SUPPRESS_BEHAVIOR: int
INTERNET_OPTION_AUTODIAL_MODE: int
INTERNET_OPTION_AUTODIAL_CONNECTION: int
INTERNET_OPTION_CLIENT_CERT_CONTEXT: int
INTERNET_OPTION_AUTH_FLAGS: int
INTERNET_OPTION_COOKIES_3RD_PARTY: int
INTERNET_OPTION_DISABLE_PASSPORT_AUTH: int
INTERNET_OPTION_SEND_UTF8_SERVERNAME_TO_PROXY: int
INTERNET_OPTION_EXEMPT_CONNECTION_LIMIT: int
INTERNET_OPTION_ENABLE_PASSPORT_AUTH: int
INTERNET_OPTION_HIBERNATE_INACTIVE_WORKER_THREADS: int
INTERNET_OPTION_ACTIVATE_WORKER_THREADS: int
INTERNET_OPTION_RESTORE_WORKER_THREAD_DEFAULTS: int
INTERNET_OPTION_SOCKET_SEND_BUFFER_LENGTH: int
INTERNET_OPTION_PROXY_SETTINGS_CHANGED: int
INTERNET_FIRST_OPTION: int
INTERNET_LAST_OPTION: int
INTERNET_PRIORITY_FOREGROUND: int
INTERNET_HANDLE_TYPE_INTERNET: int
INTERNET_HANDLE_TYPE_CONNECT_FTP: int
INTERNET_HANDLE_TYPE_CONNECT_GOPHER: int
INTERNET_HANDLE_TYPE_CONNECT_HTTP: int
INTERNET_HANDLE_TYPE_FTP_FIND: int
INTERNET_HANDLE_TYPE_FTP_FIND_HTML: int
INTERNET_HANDLE_TYPE_FTP_FILE: int
INTERNET_HANDLE_TYPE_FTP_FILE_HTML: int
INTERNET_HANDLE_TYPE_GOPHER_FIND: int
INTERNET_HANDLE_TYPE_GOPHER_FIND_HTML: int
INTERNET_HANDLE_TYPE_GOPHER_FILE: int
INTERNET_HANDLE_TYPE_GOPHER_FILE_HTML: int
INTERNET_HANDLE_TYPE_HTTP_REQUEST: int
INTERNET_HANDLE_TYPE_FILE_REQUEST: int
AUTH_FLAG_DISABLE_NEGOTIATE: int
AUTH_FLAG_ENABLE_NEGOTIATE: int
SECURITY_FLAG_SECURE: int
SECURITY_FLAG_STRENGTH_WEAK: int
SECURITY_FLAG_STRENGTH_MEDIUM: int
SECURITY_FLAG_STRENGTH_STRONG: int
SECURITY_FLAG_UNKNOWNBIT: int
SECURITY_FLAG_FORTEZZA: int
SECURITY_FLAG_NORMALBITNESS: int
SECURITY_FLAG_SSL: int
SECURITY_FLAG_SSL3: int
SECURITY_FLAG_PCT: int
SECURITY_FLAG_PCT4: int
SECURITY_FLAG_IETFSSL4: int
SECURITY_FLAG_40BIT: int
SECURITY_FLAG_128BIT: int
SECURITY_FLAG_56BIT: int
SECURITY_FLAG_IGNORE_REVOCATION: int
SECURITY_FLAG_IGNORE_UNKNOWN_CA: int
SECURITY_FLAG_IGNORE_WRONG_USAGE: int
SECURITY_FLAG_IGNORE_CERT_CN_INVALID: int
SECURITY_FLAG_IGNORE_CERT_DATE_INVALID: int
SECURITY_FLAG_IGNORE_REDIRECT_TO_HTTPS: int
SECURITY_FLAG_IGNORE_REDIRECT_TO_HTTP: int
SECURITY_SET_MASK: int
AUTODIAL_MODE_NEVER: int
AUTODIAL_MODE_ALWAYS: int
AUTODIAL_MODE_NO_NETWORK_PRESENT: int
INTERNET_STATUS_RESOLVING_NAME: int
INTERNET_STATUS_NAME_RESOLVED: int
INTERNET_STATUS_CONNECTING_TO_SERVER: int
INTERNET_STATUS_CONNECTED_TO_SERVER: int
INTERNET_STATUS_SENDING_REQUEST: int
INTERNET_STATUS_REQUEST_SENT: int
INTERNET_STATUS_RECEIVING_RESPONSE: int
INTERNET_STATUS_RESPONSE_RECEIVED: int
INTERNET_STATUS_CTL_RESPONSE_RECEIVED: int
INTERNET_STATUS_PREFETCH: int
INTERNET_STATUS_CLOSING_CONNECTION: int
INTERNET_STATUS_CONNECTION_CLOSED: int
INTERNET_STATUS_HANDLE_CREATED: int
INTERNET_STATUS_HANDLE_CLOSING: int
INTERNET_STATUS_DETECTING_PROXY: int
INTERNET_STATUS_REQUEST_COMPLETE: int
INTERNET_STATUS_REDIRECT: int
INTERNET_STATUS_INTERMEDIATE_RESPONSE: int
INTERNET_STATUS_USER_INPUT_REQUIRED: int
INTERNET_STATUS_STATE_CHANGE: int
INTERNET_STATUS_COOKIE_SENT: int
INTERNET_STATUS_COOKIE_RECEIVED: int
INTERNET_STATUS_PRIVACY_IMPACTED: int
INTERNET_STATUS_P3P_HEADER: int
INTERNET_STATUS_P3P_POLICYREF: int
INTERNET_STATUS_COOKIE_HISTORY: int
INTERNET_STATE_CONNECTED: int
INTERNET_STATE_DISCONNECTED: int
INTERNET_STATE_DISCONNECTED_BY_USER: int
INTERNET_STATE_IDLE: int
INTERNET_STATE_BUSY: int
FTP_TRANSFER_TYPE_UNKNOWN: int
FTP_TRANSFER_TYPE_ASCII: int
FTP_TRANSFER_TYPE_BINARY: int
FTP_TRANSFER_TYPE_MASK: int
MAX_GOPHER_DISPLAY_TEXT: int
MAX_GOPHER_SELECTOR_TEXT: int
MAX_GOPHER_HOST_NAME: int
MAX_GOPHER_LOCATOR_LENGTH: int
GOPHER_TYPE_TEXT_FILE: int
GOPHER_TYPE_DIRECTORY: int
GOPHER_TYPE_CSO: int
GOPHER_TYPE_ERROR: int
GOPHER_TYPE_MAC_BINHEX: int
GOPHER_TYPE_DOS_ARCHIVE: int
GOPHER_TYPE_UNIX_UUENCODED: int
GOPHER_TYPE_INDEX_SERVER: int
GOPHER_TYPE_TELNET: int
GOPHER_TYPE_BINARY: int
GOPHER_TYPE_REDUNDANT: int
GOPHER_TYPE_TN3270: int
GOPHER_TYPE_GIF: int
GOPHER_TYPE_IMAGE: int
GOPHER_TYPE_BITMAP: int
GOPHER_TYPE_MOVIE: int
GOPHER_TYPE_SOUND: int
GOPHER_TYPE_HTML: int
GOPHER_TYPE_PDF: int
GOPHER_TYPE_CALENDAR: int
GOPHER_TYPE_INLINE: int
GOPHER_TYPE_UNKNOWN: int
GOPHER_TYPE_ASK: int
GOPHER_TYPE_GOPHER_PLUS: int
GOPHER_TYPE_FILE_MASK: int
MAX_GOPHER_CATEGORY_NAME: int
MAX_GOPHER_ATTRIBUTE_NAME: int
MIN_GOPHER_ATTRIBUTE_LENGTH: int
GOPHER_ATTRIBUTE_ID_BASE: int
GOPHER_CATEGORY_ID_ALL: int
GOPHER_CATEGORY_ID_INFO: int
GOPHER_CATEGORY_ID_ADMIN: int
GOPHER_CATEGORY_ID_VIEWS: int
GOPHER_CATEGORY_ID_ABSTRACT: int
GOPHER_CATEGORY_ID_VERONICA: int
GOPHER_CATEGORY_ID_ASK: int
GOPHER_CATEGORY_ID_UNKNOWN: int
GOPHER_ATTRIBUTE_ID_ALL: int
GOPHER_ATTRIBUTE_ID_ADMIN: int
GOPHER_ATTRIBUTE_ID_MOD_DATE: int
GOPHER_ATTRIBUTE_ID_TTL: int
GOPHER_ATTRIBUTE_ID_SCORE: int
GOPHER_ATTRIBUTE_ID_RANGE: int
GOPHER_ATTRIBUTE_ID_SITE: int
GOPHER_ATTRIBUTE_ID_ORG: int
GOPHER_ATTRIBUTE_ID_LOCATION: int
GOPHER_ATTRIBUTE_ID_GEOG: int
GOPHER_ATTRIBUTE_ID_TIMEZONE: int
GOPHER_ATTRIBUTE_ID_PROVIDER: int
GOPHER_ATTRIBUTE_ID_VERSION: int
GOPHER_ATTRIBUTE_ID_ABSTRACT: int
GOPHER_ATTRIBUTE_ID_VIEW: int
GOPHER_ATTRIBUTE_ID_TREEWALK: int
GOPHER_ATTRIBUTE_ID_UNKNOWN: int
HTTP_MAJOR_VERSION: int
HTTP_MINOR_VERSION: int
HTTP_VERSIONA: str
HTTP_VERSION: str
HTTP_QUERY_MIME_VERSION: int
HTTP_QUERY_CONTENT_TYPE: int
HTTP_QUERY_CONTENT_TRANSFER_ENCODING: int
HTTP_QUERY_CONTENT_ID: int
HTTP_QUERY_CONTENT_DESCRIPTION: int
HTTP_QUERY_CONTENT_LENGTH: int
HTTP_QUERY_CONTENT_LANGUAGE: int
HTTP_QUERY_ALLOW: int
HTTP_QUERY_PUBLIC: int
HTTP_QUERY_DATE: int
HTTP_QUERY_EXPIRES: int
HTTP_QUERY_LAST_MODIFIED: int
HTTP_QUERY_MESSAGE_ID: int
HTTP_QUERY_URI: int
HTTP_QUERY_DERIVED_FROM: int
HTTP_QUERY_COST: int
HTTP_QUERY_LINK: int
HTTP_QUERY_PRAGMA: int
HTTP_QUERY_VERSION: int
HTTP_QUERY_STATUS_CODE: int
HTTP_QUERY_STATUS_TEXT: int
HTTP_QUERY_RAW_HEADERS: int
HTTP_QUERY_RAW_HEADERS_CRLF: int
HTTP_QUERY_CONNECTION: int
HTTP_QUERY_ACCEPT: int
HTTP_QUERY_ACCEPT_CHARSET: int
HTTP_QUERY_ACCEPT_ENCODING: int
HTTP_QUERY_ACCEPT_LANGUAGE: int
HTTP_QUERY_AUTHORIZATION: int
HTTP_QUERY_CONTENT_ENCODING: int
HTTP_QUERY_FORWARDED: int
HTTP_QUERY_FROM: int
HTTP_QUERY_IF_MODIFIED_SINCE: int
HTTP_QUERY_LOCATION: int
HTTP_QUERY_ORIG_URI: int
HTTP_QUERY_REFERER: int
HTTP_QUERY_RETRY_AFTER: int
HTTP_QUERY_SERVER: int
HTTP_QUERY_TITLE: int
HTTP_QUERY_USER_AGENT: int
HTTP_QUERY_WWW_AUTHENTICATE: int
HTTP_QUERY_PROXY_AUTHENTICATE: int
HTTP_QUERY_ACCEPT_RANGES: int
HTTP_QUERY_SET_COOKIE: int
HTTP_QUERY_COOKIE: int
HTTP_QUERY_REQUEST_METHOD: int
HTTP_QUERY_REFRESH: int
HTTP_QUERY_CONTENT_DISPOSITION: int
HTTP_QUERY_AGE: int
HTTP_QUERY_CACHE_CONTROL: int
HTTP_QUERY_CONTENT_BASE: int
HTTP_QUERY_CONTENT_LOCATION: int
HTTP_QUERY_CONTENT_MD5: int
HTTP_QUERY_CONTENT_RANGE: int
HTTP_QUERY_ETAG: int
HTTP_QUERY_HOST: int
HTTP_QUERY_IF_MATCH: int
HTTP_QUERY_IF_NONE_MATCH: int
HTTP_QUERY_IF_RANGE: int
HTTP_QUERY_IF_UNMODIFIED_SINCE: int
HTTP_QUERY_MAX_FORWARDS: int
HTTP_QUERY_PROXY_AUTHORIZATION: int
HTTP_QUERY_RANGE: int
HTTP_QUERY_TRANSFER_ENCODING: int
HTTP_QUERY_UPGRADE: int
HTTP_QUERY_VARY: int
HTTP_QUERY_VIA: int
HTTP_QUERY_WARNING: int
HTTP_QUERY_EXPECT: int
HTTP_QUERY_PROXY_CONNECTION: int
HTTP_QUERY_UNLESS_MODIFIED_SINCE: int
HTTP_QUERY_ECHO_REQUEST: int
HTTP_QUERY_ECHO_REPLY: int
HTTP_QUERY_ECHO_HEADERS: int
HTTP_QUERY_ECHO_HEADERS_CRLF: int
HTTP_QUERY_PROXY_SUPPORT: int
HTTP_QUERY_AUTHENTICATION_INFO: int
HTTP_QUERY_PASSPORT_URLS: int
HTTP_QUERY_PASSPORT_CONFIG: int
HTTP_QUERY_MAX: int
HTTP_QUERY_CUSTOM: int
HTTP_QUERY_FLAG_REQUEST_HEADERS: int
HTTP_QUERY_FLAG_SYSTEMTIME: int
HTTP_QUERY_FLAG_NUMBER: int
HTTP_QUERY_FLAG_COALESCE: int
HTTP_QUERY_MODIFIER_FLAGS_MASK: int
HTTP_QUERY_HEADER_MASK: int
HTTP_STATUS_CONTINUE: int
HTTP_STATUS_SWITCH_PROTOCOLS: int
HTTP_STATUS_OK: int
HTTP_STATUS_CREATED: int
HTTP_STATUS_ACCEPTED: int
HTTP_STATUS_PARTIAL: int
HTTP_STATUS_NO_CONTENT: int
HTTP_STATUS_RESET_CONTENT: int
HTTP_STATUS_PARTIAL_CONTENT: int
HTTP_STATUS_AMBIGUOUS: int
HTTP_STATUS_MOVED: int
HTTP_STATUS_REDIRECT: int
HTTP_STATUS_REDIRECT_METHOD: int
HTTP_STATUS_NOT_MODIFIED: int
HTTP_STATUS_USE_PROXY: int
HTTP_STATUS_REDIRECT_KEEP_VERB: int
HTTP_STATUS_BAD_REQUEST: int
HTTP_STATUS_DENIED: int
HTTP_STATUS_PAYMENT_REQ: int
HTTP_STATUS_FORBIDDEN: int
HTTP_STATUS_NOT_FOUND: int
HTTP_STATUS_BAD_METHOD: int
HTTP_STATUS_NONE_ACCEPTABLE: int
HTTP_STATUS_PROXY_AUTH_REQ: int
HTTP_STATUS_REQUEST_TIMEOUT: int
HTTP_STATUS_CONFLICT: int
HTTP_STATUS_GONE: int
HTTP_STATUS_LENGTH_REQUIRED: int
HTTP_STATUS_PRECOND_FAILED: int
HTTP_STATUS_REQUEST_TOO_LARGE: int
HTTP_STATUS_URI_TOO_LONG: int
HTTP_STATUS_UNSUPPORTED_MEDIA: int
HTTP_STATUS_RETRY_WITH: int
HTTP_STATUS_SERVER_ERROR: int
HTTP_STATUS_NOT_SUPPORTED: int
HTTP_STATUS_BAD_GATEWAY: int
HTTP_STATUS_SERVICE_UNAVAIL: int
HTTP_STATUS_GATEWAY_TIMEOUT: int
HTTP_STATUS_VERSION_NOT_SUP: int
HTTP_STATUS_FIRST: int
HTTP_STATUS_LAST: int
HTTP_ADDREQ_INDEX_MASK: int
HTTP_ADDREQ_FLAGS_MASK: int
HTTP_ADDREQ_FLAG_ADD_IF_NEW: int
HTTP_ADDREQ_FLAG_ADD: int
HTTP_ADDREQ_FLAG_COALESCE_WITH_COMMA: int
HTTP_ADDREQ_FLAG_COALESCE_WITH_SEMICOLON: int
HTTP_ADDREQ_FLAG_COALESCE: int
HTTP_ADDREQ_FLAG_REPLACE: int
HSR_ASYNC: int
HSR_SYNC: int
HSR_USE_CONTEXT: int
HSR_INITIATE: int
HSR_DOWNLOAD: int
HSR_CHUNKED: int
INTERNET_COOKIE_IS_SECURE: int
INTERNET_COOKIE_IS_SESSION: int
INTERNET_COOKIE_THIRD_PARTY: int
INTERNET_COOKIE_PROMPT_REQUIRED: int
INTERNET_COOKIE_EVALUATE_P3P: int
INTERNET_COOKIE_APPLY_P3P: int
INTERNET_COOKIE_P3P_ENABLED: int
INTERNET_COOKIE_IS_RESTRICTED: int
INTERNET_COOKIE_IE6: int
INTERNET_COOKIE_IS_LEGACY: int
FLAG_ICC_FORCE_CONNECTION: int
FLAGS_ERROR_UI_FILTER_FOR_ERRORS: int
FLAGS_ERROR_UI_FLAGS_CHANGE_OPTIONS: int
FLAGS_ERROR_UI_FLAGS_GENERATE_DATA: int
FLAGS_ERROR_UI_FLAGS_NO_UI: int
FLAGS_ERROR_UI_SERIALIZE_DIALOGS: int
INTERNET_ERROR_BASE: int
ERROR_INTERNET_OUT_OF_HANDLES: int
ERROR_INTERNET_TIMEOUT: int
ERROR_INTERNET_EXTENDED_ERROR: int
ERROR_INTERNET_INTERNAL_ERROR: int
ERROR_INTERNET_INVALID_URL: int
ERROR_INTERNET_UNRECOGNIZED_SCHEME: int
ERROR_INTERNET_NAME_NOT_RESOLVED: int
ERROR_INTERNET_PROTOCOL_NOT_FOUND: int
ERROR_INTERNET_INVALID_OPTION: int
ERROR_INTERNET_BAD_OPTION_LENGTH: int
ERROR_INTERNET_OPTION_NOT_SETTABLE: int
ERROR_INTERNET_SHUTDOWN: int
ERROR_INTERNET_INCORRECT_USER_NAME: int
ERROR_INTERNET_INCORRECT_PASSWORD: int
ERROR_INTERNET_LOGIN_FAILURE: int
ERROR_INTERNET_INVALID_OPERATION: int
ERROR_INTERNET_OPERATION_CANCELLED: int
ERROR_INTERNET_INCORRECT_HANDLE_TYPE: int
ERROR_INTERNET_INCORRECT_HANDLE_STATE: int
ERROR_INTERNET_NOT_PROXY_REQUEST: int
ERROR_INTERNET_REGISTRY_VALUE_NOT_FOUND: int
ERROR_INTERNET_BAD_REGISTRY_PARAMETER: int
ERROR_INTERNET_NO_DIRECT_ACCESS: int
ERROR_INTERNET_NO_CONTEXT: int
ERROR_INTERNET_NO_CALLBACK: int
ERROR_INTERNET_REQUEST_PENDING: int
ERROR_INTERNET_INCORRECT_FORMAT: int
ERROR_INTERNET_ITEM_NOT_FOUND: int
ERROR_INTERNET_CANNOT_CONNECT: int
ERROR_INTERNET_CONNECTION_ABORTED: int
ERROR_INTERNET_CONNECTION_RESET: int
ERROR_INTERNET_FORCE_RETRY: int
ERROR_INTERNET_INVALID_PROXY_REQUEST: int
ERROR_INTERNET_NEED_UI: int
ERROR_INTERNET_HANDLE_EXISTS: int
ERROR_INTERNET_SEC_CERT_DATE_INVALID: int
ERROR_INTERNET_SEC_CERT_CN_INVALID: int
ERROR_INTERNET_HTTP_TO_HTTPS_ON_REDIR: int
ERROR_INTERNET_HTTPS_TO_HTTP_ON_REDIR: int
ERROR_INTERNET_MIXED_SECURITY: int
ERROR_INTERNET_CHG_POST_IS_NON_SECURE: int
ERROR_INTERNET_POST_IS_NON_SECURE: int
ERROR_INTERNET_CLIENT_AUTH_CERT_NEEDED: int
ERROR_INTERNET_INVALID_CA: int
ERROR_INTERNET_CLIENT_AUTH_NOT_SETUP: int
ERROR_INTERNET_ASYNC_THREAD_FAILED: int
ERROR_INTERNET_REDIRECT_SCHEME_CHANGE: int
ERROR_INTERNET_DIALOG_PENDING: int
ERROR_INTERNET_RETRY_DIALOG: int
ERROR_INTERNET_HTTPS_HTTP_SUBMIT_REDIR: int
ERROR_INTERNET_INSERT_CDROM: int
ERROR_INTERNET_FORTEZZA_LOGIN_NEEDED: int
ERROR_INTERNET_SEC_CERT_ERRORS: int
ERROR_INTERNET_SEC_CERT_NO_REV: int
ERROR_INTERNET_SEC_CERT_REV_FAILED: int
ERROR_FTP_TRANSFER_IN_PROGRESS: int
ERROR_FTP_DROPPED: int
ERROR_FTP_NO_PASSIVE_MODE: int
ERROR_GOPHER_PROTOCOL_ERROR: int
ERROR_GOPHER_NOT_FILE: int
ERROR_GOPHER_DATA_ERROR: int
ERROR_GOPHER_END_OF_DATA: int
ERROR_GOPHER_INVALID_LOCATOR: int
ERROR_GOPHER_INCORRECT_LOCATOR_TYPE: int
ERROR_GOPHER_NOT_GOPHER_PLUS: int
ERROR_GOPHER_ATTRIBUTE_NOT_FOUND: int
ERROR_GOPHER_UNKNOWN_LOCATOR: int
ERROR_HTTP_HEADER_NOT_FOUND: int
ERROR_HTTP_DOWNLEVEL_SERVER: int
ERROR_HTTP_INVALID_SERVER_RESPONSE: int
ERROR_HTTP_INVALID_HEADER: int
ERROR_HTTP_INVALID_QUERY_REQUEST: int
ERROR_HTTP_HEADER_ALREADY_EXISTS: int
ERROR_HTTP_REDIRECT_FAILED: int
ERROR_HTTP_NOT_REDIRECTED: int
ERROR_HTTP_COOKIE_NEEDS_CONFIRMATION: int
ERROR_HTTP_COOKIE_DECLINED: int
ERROR_HTTP_REDIRECT_NEEDS_CONFIRMATION: int
ERROR_INTERNET_SECURITY_CHANNEL_ERROR: int
ERROR_INTERNET_UNABLE_TO_CACHE_FILE: int
ERROR_INTERNET_TCPIP_NOT_INSTALLED: int
ERROR_INTERNET_DISCONNECTED: int
ERROR_INTERNET_SERVER_UNREACHABLE: int
ERROR_INTERNET_PROXY_SERVER_UNREACHABLE: int
ERROR_INTERNET_BAD_AUTO_PROXY_SCRIPT: int
ERROR_INTERNET_UNABLE_TO_DOWNLOAD_SCRIPT: int
ERROR_INTERNET_SEC_INVALID_CERT: int
ERROR_INTERNET_SEC_CERT_REVOKED: int
ERROR_INTERNET_FAILED_DUETOSECURITYCHECK: int
ERROR_INTERNET_NOT_INITIALIZED: int
ERROR_INTERNET_NEED_MSN_SSPI_PKG: int
ERROR_INTERNET_LOGIN_FAILURE_DISPLAY_ENTITY_BODY: int
INTERNET_ERROR_LAST: int
NORMAL_CACHE_ENTRY: int
STICKY_CACHE_ENTRY: int
EDITED_CACHE_ENTRY: int
TRACK_OFFLINE_CACHE_ENTRY: int
TRACK_ONLINE_CACHE_ENTRY: int
SPARSE_CACHE_ENTRY: int
COOKIE_CACHE_ENTRY: int
URLHISTORY_CACHE_ENTRY: int
URLCACHE_FIND_DEFAULT_FILTER: int
CACHEGROUP_ATTRIBUTE_GET_ALL: int
CACHEGROUP_ATTRIBUTE_BASIC: int
CACHEGROUP_ATTRIBUTE_FLAG: int
CACHEGROUP_ATTRIBUTE_TYPE: int
CACHEGROUP_ATTRIBUTE_QUOTA: int
CACHEGROUP_ATTRIBUTE_GROUPNAME: int
CACHEGROUP_ATTRIBUTE_STORAGE: int
CACHEGROUP_FLAG_NONPURGEABLE: int
CACHEGROUP_FLAG_GIDONLY: int
CACHEGROUP_FLAG_FLUSHURL_ONDELETE: int
CACHEGROUP_SEARCH_ALL: int
CACHEGROUP_SEARCH_BYURL: int
CACHEGROUP_TYPE_INVALID: int
CACHEGROUP_READWRITE_MASK: int
GROUPNAME_MAX_LENGTH: int
GROUP_OWNER_STORAGE_SIZE: int
CACHE_ENTRY_ATTRIBUTE_FC: int
CACHE_ENTRY_HITRATE_FC: int
CACHE_ENTRY_MODTIME_FC: int
CACHE_ENTRY_EXPTIME_FC: int
CACHE_ENTRY_ACCTIME_FC: int
CACHE_ENTRY_SYNCTIME_FC: int
CACHE_ENTRY_HEADERINFO_FC: int
CACHE_ENTRY_EXEMPT_DELTA_FC: int
INTERNET_CACHE_GROUP_ADD: int
INTERNET_CACHE_GROUP_REMOVE: int
INTERNET_DIAL_FORCE_PROMPT: int
INTERNET_DIAL_SHOW_OFFLINE: int
INTERNET_DIAL_UNATTENDED: int
INTERENT_GOONLINE_REFRESH: int
INTERENT_GOONLINE_MASK: int
INTERNET_AUTODIAL_FORCE_ONLINE: int
INTERNET_AUTODIAL_FORCE_UNATTENDED: int
INTERNET_AUTODIAL_FAILIFSECURITYCHECK: int
INTERNET_AUTODIAL_OVERRIDE_NET_PRESENT: int
INTERNET_AUTODIAL_FLAGS_MASK: int
PROXY_AUTO_DETECT_TYPE_DHCP: int
PROXY_AUTO_DETECT_TYPE_DNS_A: int
INTERNET_CONNECTION_MODEM: int
INTERNET_CONNECTION_LAN: int
INTERNET_CONNECTION_PROXY: int
INTERNET_CONNECTION_MODEM_BUSY: int
INTERNET_RAS_INSTALLED: int
INTERNET_CONNECTION_OFFLINE: int
INTERNET_CONNECTION_CONFIGURED: int
INTERNET_CUSTOMDIAL_CONNECT: int
INTERNET_CUSTOMDIAL_UNATTENDED: int
INTERNET_CUSTOMDIAL_DISCONNECT: int
INTERNET_CUSTOMDIAL_SHOWOFFLINE: int
INTERNET_CUSTOMDIAL_SAFE_FOR_UNATTENDED: int
INTERNET_CUSTOMDIAL_WILL_SUPPLY_STATE: int
INTERNET_CUSTOMDIAL_CAN_HANGUP: int
INTERNET_DIALSTATE_DISCONNECTED: int
INTERNET_IDENTITY_FLAG_PRIVATE_CACHE: int
INTERNET_IDENTITY_FLAG_SHARED_CACHE: int
INTERNET_IDENTITY_FLAG_CLEAR_DATA: int
INTERNET_IDENTITY_FLAG_CLEAR_COOKIES: int
INTERNET_IDENTITY_FLAG_CLEAR_HISTORY: int
INTERNET_IDENTITY_FLAG_CLEAR_CONTENT: int
INTERNET_SUPPRESS_RESET_ALL: int
INTERNET_SUPPRESS_COOKIE_POLICY: int
INTERNET_SUPPRESS_COOKIE_POLICY_RESET: int
PRIVACY_TEMPLATE_NO_COOKIES: int
PRIVACY_TEMPLATE_HIGH: int
PRIVACY_TEMPLATE_MEDIUM_HIGH: int
PRIVACY_TEMPLATE_MEDIUM: int
PRIVACY_TEMPLATE_MEDIUM_LOW: int
PRIVACY_TEMPLATE_LOW: int
PRIVACY_TEMPLATE_CUSTOM: int
PRIVACY_TEMPLATE_ADVANCED: int
PRIVACY_TEMPLATE_MAX: int
PRIVACY_TYPE_FIRST_PARTY: int
PRIVACY_TYPE_THIRD_PARTY: int
INTERNET_DEFAULT_PORT: int
WINHTTP_FLAG_ASYNC: int
WINHTTP_FLAG_SECURE: int
WINHTTP_FLAG_ESCAPE_PERCENT: int
WINHTTP_FLAG_NULL_CODEPAGE: int
WINHTTP_FLAG_BYPASS_PROXY_CACHE: int
WINHTTP_FLAG_REFRESH: int
WINHTTP_FLAG_ESCAPE_DISABLE: int
WINHTTP_FLAG_ESCAPE_DISABLE_QUERY: int
SECURITY_FLAG_IGNORE_CERT_WRONG_USAGE: int
INTERNET_SCHEME_HTTP: int
INTERNET_SCHEME_HTTPS: int
WINHTTP_AUTOPROXY_AUTO_DETECT: int
WINHTTP_AUTOPROXY_CONFIG_URL: int
WINHTTP_AUTOPROXY_RUN_INPROCESS: int
WINHTTP_AUTOPROXY_RUN_OUTPROCESS_ONLY: int
WINHTTP_AUTO_DETECT_TYPE_DHCP: int
WINHTTP_AUTO_DETECT_TYPE_DNS_A: int
WINHTTP_TIME_FORMAT_BUFSIZE: int
ICU_ESCAPE_AUTHORITY: int
ICU_REJECT_USERPWD: int
WINHTTP_ACCESS_TYPE_DEFAULT_PROXY: int
WINHTTP_ACCESS_TYPE_NO_PROXY: int
WINHTTP_ACCESS_TYPE_NAMED_PROXY: int
WINHTTP_OPTION_CALLBACK: int
WINHTTP_OPTION_RESOLVE_TIMEOUT: int
WINHTTP_OPTION_CONNECT_TIMEOUT: int
WINHTTP_OPTION_CONNECT_RETRIES: int
WINHTTP_OPTION_SEND_TIMEOUT: int
WINHTTP_OPTION_RECEIVE_TIMEOUT: int
WINHTTP_OPTION_RECEIVE_RESPONSE_TIMEOUT: int
WINHTTP_OPTION_HANDLE_TYPE: int
WINHTTP_OPTION_READ_BUFFER_SIZE: int
WINHTTP_OPTION_WRITE_BUFFER_SIZE: int
WINHTTP_OPTION_PARENT_HANDLE: int
WINHTTP_OPTION_EXTENDED_ERROR: int
WINHTTP_OPTION_SECURITY_FLAGS: int
WINHTTP_OPTION_SECURITY_CERTIFICATE_STRUCT: int
WINHTTP_OPTION_URL: int
WINHTTP_OPTION_SECURITY_KEY_BITNESS: int
WINHTTP_OPTION_PROXY: int
WINHTTP_OPTION_USER_AGENT: int
WINHTTP_OPTION_CONTEXT_VALUE: int
WINHTTP_OPTION_CLIENT_CERT_CONTEXT: int
WINHTTP_OPTION_REQUEST_PRIORITY: int
WINHTTP_OPTION_HTTP_VERSION: int
WINHTTP_OPTION_DISABLE_FEATURE: int
WINHTTP_OPTION_CODEPAGE: int
WINHTTP_OPTION_MAX_CONNS_PER_SERVER: int
WINHTTP_OPTION_MAX_CONNS_PER_1_0_SERVER: int
WINHTTP_OPTION_AUTOLOGON_POLICY: int
WINHTTP_OPTION_SERVER_CERT_CONTEXT: int
WINHTTP_OPTION_ENABLE_FEATURE: int
WINHTTP_OPTION_WORKER_THREAD_COUNT: int
WINHTTP_OPTION_PASSPORT_COBRANDING_TEXT: int
WINHTTP_OPTION_PASSPORT_COBRANDING_URL: int
WINHTTP_OPTION_CONFIGURE_PASSPORT_AUTH: int
WINHTTP_OPTION_SECURE_PROTOCOLS: int
WINHTTP_OPTION_ENABLETRACING: int
WINHTTP_OPTION_PASSPORT_SIGN_OUT: int
WINHTTP_OPTION_PASSPORT_RETURN_URL: int
WINHTTP_OPTION_REDIRECT_POLICY: int
WINHTTP_OPTION_MAX_HTTP_AUTOMATIC_REDIRECTS: int
WINHTTP_OPTION_MAX_HTTP_STATUS_CONTINUE: int
WINHTTP_OPTION_MAX_RESPONSE_HEADER_SIZE: int
WINHTTP_OPTION_MAX_RESPONSE_DRAIN_SIZE: int
WINHTTP_OPTION_CONNECTION_INFO: int
WINHTTP_OPTION_SPN: int
WINHTTP_OPTION_GLOBAL_PROXY_CREDS: int
WINHTTP_OPTION_GLOBAL_SERVER_CREDS: int
WINHTTP_OPTION_UNLOAD_NOTIFY_EVENT: int
WINHTTP_OPTION_REJECT_USERPWD_IN_URL: int
WINHTTP_OPTION_USE_GLOBAL_SERVER_CREDENTIALS: int
WINHTTP_LAST_OPTION: int
WINHTTP_OPTION_USERNAME: int
WINHTTP_OPTION_PASSWORD: int
WINHTTP_OPTION_PROXY_USERNAME: int
WINHTTP_OPTION_PROXY_PASSWORD: int
WINHTTP_CONNS_PER_SERVER_UNLIMITED: int
WINHTTP_AUTOLOGON_SECURITY_LEVEL_MEDIUM: int
WINHTTP_AUTOLOGON_SECURITY_LEVEL_LOW: int
WINHTTP_AUTOLOGON_SECURITY_LEVEL_HIGH: int
WINHTTP_AUTOLOGON_SECURITY_LEVEL_DEFAULT: int
WINHTTP_OPTION_REDIRECT_POLICY_NEVER: int
WINHTTP_OPTION_REDIRECT_POLICY_DISALLOW_HTTPS_TO_HTTP: int
WINHTTP_OPTION_REDIRECT_POLICY_ALWAYS: int
WINHTTP_OPTION_REDIRECT_POLICY_LAST: int
WINHTTP_OPTION_REDIRECT_POLICY_DEFAULT: int
WINHTTP_DISABLE_PASSPORT_AUTH: int
WINHTTP_ENABLE_PASSPORT_AUTH: int
WINHTTP_DISABLE_PASSPORT_KEYRING: int
WINHTTP_ENABLE_PASSPORT_KEYRING: int
WINHTTP_DISABLE_COOKIES: int
WINHTTP_DISABLE_REDIRECTS: int
WINHTTP_DISABLE_AUTHENTICATION: int
WINHTTP_DISABLE_KEEP_ALIVE: int
WINHTTP_ENABLE_SSL_REVOCATION: int
WINHTTP_ENABLE_SSL_REVERT_IMPERSONATION: int
WINHTTP_DISABLE_SPN_SERVER_PORT: int
WINHTTP_ENABLE_SPN_SERVER_PORT: int
WINHTTP_OPTION_SPN_MASK: int
WINHTTP_HANDLE_TYPE_SESSION: int
WINHTTP_HANDLE_TYPE_CONNECT: int
WINHTTP_HANDLE_TYPE_REQUEST: int
WINHTTP_AUTH_SCHEME_BASIC: int
WINHTTP_AUTH_SCHEME_NTLM: int
WINHTTP_AUTH_SCHEME_PASSPORT: int
WINHTTP_AUTH_SCHEME_DIGEST: int
WINHTTP_AUTH_SCHEME_NEGOTIATE: int
WINHTTP_AUTH_TARGET_SERVER: int
WINHTTP_AUTH_TARGET_PROXY: int
WINHTTP_CALLBACK_STATUS_FLAG_CERT_REV_FAILED: int
WINHTTP_CALLBACK_STATUS_FLAG_INVALID_CERT: int
WINHTTP_CALLBACK_STATUS_FLAG_CERT_REVOKED: int
WINHTTP_CALLBACK_STATUS_FLAG_INVALID_CA: int
WINHTTP_CALLBACK_STATUS_FLAG_CERT_CN_INVALID: int
WINHTTP_CALLBACK_STATUS_FLAG_CERT_DATE_INVALID: int
WINHTTP_CALLBACK_STATUS_FLAG_CERT_WRONG_USAGE: int
WINHTTP_CALLBACK_STATUS_FLAG_SECURITY_CHANNEL_ERROR: int
WINHTTP_FLAG_SECURE_PROTOCOL_SSL2: int
WINHTTP_FLAG_SECURE_PROTOCOL_SSL3: int
WINHTTP_FLAG_SECURE_PROTOCOL_TLS1: int
WINHTTP_FLAG_SECURE_PROTOCOL_ALL: int
WINHTTP_CALLBACK_STATUS_RESOLVING_NAME: int
WINHTTP_CALLBACK_STATUS_NAME_RESOLVED: int
WINHTTP_CALLBACK_STATUS_CONNECTING_TO_SERVER: int
WINHTTP_CALLBACK_STATUS_CONNECTED_TO_SERVER: int
WINHTTP_CALLBACK_STATUS_SENDING_REQUEST: int
WINHTTP_CALLBACK_STATUS_REQUEST_SENT: int
WINHTTP_CALLBACK_STATUS_RECEIVING_RESPONSE: int
WINHTTP_CALLBACK_STATUS_RESPONSE_RECEIVED: int
WINHTTP_CALLBACK_STATUS_CLOSING_CONNECTION: int
WINHTTP_CALLBACK_STATUS_CONNECTION_CLOSED: int
WINHTTP_CALLBACK_STATUS_HANDLE_CREATED: int
WINHTTP_CALLBACK_STATUS_HANDLE_CLOSING: int
WINHTTP_CALLBACK_STATUS_DETECTING_PROXY: int
WINHTTP_CALLBACK_STATUS_REDIRECT: int
WINHTTP_CALLBACK_STATUS_INTERMEDIATE_RESPONSE: int
WINHTTP_CALLBACK_STATUS_SECURE_FAILURE: int
WINHTTP_CALLBACK_STATUS_HEADERS_AVAILABLE: int
WINHTTP_CALLBACK_STATUS_DATA_AVAILABLE: int
WINHTTP_CALLBACK_STATUS_READ_COMPLETE: int
WINHTTP_CALLBACK_STATUS_WRITE_COMPLETE: int
WINHTTP_CALLBACK_STATUS_REQUEST_ERROR: int
WINHTTP_CALLBACK_STATUS_SENDREQUEST_COMPLETE: int
API_RECEIVE_RESPONSE: int
API_QUERY_DATA_AVAILABLE: int
API_READ_DATA: int
API_WRITE_DATA: int
API_SEND_REQUEST: int
WINHTTP_CALLBACK_FLAG_RESOLVE_NAME: int
WINHTTP_CALLBACK_FLAG_CONNECT_TO_SERVER: int
WINHTTP_CALLBACK_FLAG_SEND_REQUEST: int
WINHTTP_CALLBACK_FLAG_RECEIVE_RESPONSE: int
WINHTTP_CALLBACK_FLAG_CLOSE_CONNECTION: int
WINHTTP_CALLBACK_FLAG_HANDLES: int
WINHTTP_CALLBACK_FLAG_DETECTING_PROXY: int
WINHTTP_CALLBACK_FLAG_REDIRECT: int
WINHTTP_CALLBACK_FLAG_INTERMEDIATE_RESPONSE: int
WINHTTP_CALLBACK_FLAG_SECURE_FAILURE: int
WINHTTP_CALLBACK_FLAG_SENDREQUEST_COMPLETE: int
WINHTTP_CALLBACK_FLAG_HEADERS_AVAILABLE: int
WINHTTP_CALLBACK_FLAG_DATA_AVAILABLE: int
WINHTTP_CALLBACK_FLAG_READ_COMPLETE: int
WINHTTP_CALLBACK_FLAG_WRITE_COMPLETE: int
WINHTTP_CALLBACK_FLAG_REQUEST_ERROR: int
WINHTTP_CALLBACK_FLAG_ALL_COMPLETIONS: int
WINHTTP_CALLBACK_FLAG_ALL_NOTIFICATIONS: int
WINHTTP_QUERY_MIME_VERSION: int
WINHTTP_QUERY_CONTENT_TYPE: int
WINHTTP_QUERY_CONTENT_TRANSFER_ENCODING: int
WINHTTP_QUERY_CONTENT_ID: int
WINHTTP_QUERY_CONTENT_DESCRIPTION: int
WINHTTP_QUERY_CONTENT_LENGTH: int
WINHTTP_QUERY_CONTENT_LANGUAGE: int
WINHTTP_QUERY_ALLOW: int
WINHTTP_QUERY_PUBLIC: int
WINHTTP_QUERY_DATE: int
WINHTTP_QUERY_EXPIRES: int
WINHTTP_QUERY_LAST_MODIFIED: int
WINHTTP_QUERY_MESSAGE_ID: int
WINHTTP_QUERY_URI: int
WINHTTP_QUERY_DERIVED_FROM: int
WINHTTP_QUERY_COST: int
WINHTTP_QUERY_LINK: int
WINHTTP_QUERY_PRAGMA: int
WINHTTP_QUERY_VERSION: int
WINHTTP_QUERY_STATUS_CODE: int
WINHTTP_QUERY_STATUS_TEXT: int
WINHTTP_QUERY_RAW_HEADERS: int
WINHTTP_QUERY_RAW_HEADERS_CRLF: int
WINHTTP_QUERY_CONNECTION: int
WINHTTP_QUERY_ACCEPT: int
WINHTTP_QUERY_ACCEPT_CHARSET: int
WINHTTP_QUERY_ACCEPT_ENCODING: int
WINHTTP_QUERY_ACCEPT_LANGUAGE: int
WINHTTP_QUERY_AUTHORIZATION: int
WINHTTP_QUERY_CONTENT_ENCODING: int
WINHTTP_QUERY_FORWARDED: int
WINHTTP_QUERY_FROM: int
WINHTTP_QUERY_IF_MODIFIED_SINCE: int
WINHTTP_QUERY_LOCATION: int
WINHTTP_QUERY_ORIG_URI: int
WINHTTP_QUERY_REFERER: int
WINHTTP_QUERY_RETRY_AFTER: int
WINHTTP_QUERY_SERVER: int
WINHTTP_QUERY_TITLE: int
WINHTTP_QUERY_USER_AGENT: int
WINHTTP_QUERY_WWW_AUTHENTICATE: int
WINHTTP_QUERY_PROXY_AUTHENTICATE: int
WINHTTP_QUERY_ACCEPT_RANGES: int
WINHTTP_QUERY_SET_COOKIE: int
WINHTTP_QUERY_COOKIE: int
WINHTTP_QUERY_REQUEST_METHOD: int
WINHTTP_QUERY_REFRESH: int
WINHTTP_QUERY_CONTENT_DISPOSITION: int
WINHTTP_QUERY_AGE: int
WINHTTP_QUERY_CACHE_CONTROL: int
WINHTTP_QUERY_CONTENT_BASE: int
WINHTTP_QUERY_CONTENT_LOCATION: int
WINHTTP_QUERY_CONTENT_MD5: int
WINHTTP_QUERY_CONTENT_RANGE: int
WINHTTP_QUERY_ETAG: int
WINHTTP_QUERY_HOST: int
WINHTTP_QUERY_IF_MATCH: int
WINHTTP_QUERY_IF_NONE_MATCH: int
WINHTTP_QUERY_IF_RANGE: int
WINHTTP_QUERY_IF_UNMODIFIED_SINCE: int
WINHTTP_QUERY_MAX_FORWARDS: int
WINHTTP_QUERY_PROXY_AUTHORIZATION: int
WINHTTP_QUERY_RANGE: int
WINHTTP_QUERY_TRANSFER_ENCODING: int
WINHTTP_QUERY_UPGRADE: int
WINHTTP_QUERY_VARY: int
WINHTTP_QUERY_VIA: int
WINHTTP_QUERY_WARNING: int
WINHTTP_QUERY_EXPECT: int
WINHTTP_QUERY_PROXY_CONNECTION: int
WINHTTP_QUERY_UNLESS_MODIFIED_SINCE: int
WINHTTP_QUERY_PROXY_SUPPORT: int
WINHTTP_QUERY_AUTHENTICATION_INFO: int
WINHTTP_QUERY_PASSPORT_URLS: int
WINHTTP_QUERY_PASSPORT_CONFIG: int
WINHTTP_QUERY_MAX: int
WINHTTP_QUERY_CUSTOM: int
WINHTTP_QUERY_FLAG_REQUEST_HEADERS: int
WINHTTP_QUERY_FLAG_SYSTEMTIME: int
WINHTTP_QUERY_FLAG_NUMBER: int
HTTP_STATUS_WEBDAV_MULTI_STATUS: int
WINHTTP_ADDREQ_INDEX_MASK: int
WINHTTP_ADDREQ_FLAGS_MASK: int
WINHTTP_ADDREQ_FLAG_ADD_IF_NEW: int
WINHTTP_ADDREQ_FLAG_ADD: int
WINHTTP_ADDREQ_FLAG_COALESCE_WITH_COMMA: int
WINHTTP_ADDREQ_FLAG_COALESCE_WITH_SEMICOLON: int
WINHTTP_ADDREQ_FLAG_COALESCE: int
WINHTTP_ADDREQ_FLAG_REPLACE: int
WINHTTP_IGNORE_REQUEST_TOTAL_LENGTH: int
WINHTTP_ERROR_BASE: int
ERROR_WINHTTP_OUT_OF_HANDLES: int
ERROR_WINHTTP_TIMEOUT: int
ERROR_WINHTTP_INTERNAL_ERROR: int
ERROR_WINHTTP_INVALID_URL: int
ERROR_WINHTTP_UNRECOGNIZED_SCHEME: int
ERROR_WINHTTP_NAME_NOT_RESOLVED: int
ERROR_WINHTTP_INVALID_OPTION: int
ERROR_WINHTTP_OPTION_NOT_SETTABLE: int
ERROR_WINHTTP_SHUTDOWN: int
ERROR_WINHTTP_LOGIN_FAILURE: int
ERROR_WINHTTP_OPERATION_CANCELLED: int
ERROR_WINHTTP_INCORRECT_HANDLE_TYPE: int
ERROR_WINHTTP_INCORRECT_HANDLE_STATE: int
ERROR_WINHTTP_CANNOT_CONNECT: int
ERROR_WINHTTP_CONNECTION_ERROR: int
ERROR_WINHTTP_RESEND_REQUEST: int
ERROR_WINHTTP_CLIENT_AUTH_CERT_NEEDED: int
ERROR_WINHTTP_CANNOT_CALL_BEFORE_OPEN: int
ERROR_WINHTTP_CANNOT_CALL_BEFORE_SEND: int
ERROR_WINHTTP_CANNOT_CALL_AFTER_SEND: int
ERROR_WINHTTP_CANNOT_CALL_AFTER_OPEN: int
ERROR_WINHTTP_HEADER_NOT_FOUND: int
ERROR_WINHTTP_INVALID_SERVER_RESPONSE: int
ERROR_WINHTTP_INVALID_HEADER: int
ERROR_WINHTTP_INVALID_QUERY_REQUEST: int
ERROR_WINHTTP_HEADER_ALREADY_EXISTS: int
ERROR_WINHTTP_REDIRECT_FAILED: int
ERROR_WINHTTP_AUTO_PROXY_SERVICE_ERROR: int
ERROR_WINHTTP_BAD_AUTO_PROXY_SCRIPT: int
ERROR_WINHTTP_UNABLE_TO_DOWNLOAD_SCRIPT: int
ERROR_WINHTTP_NOT_INITIALIZED: int
ERROR_WINHTTP_SECURE_FAILURE: int
ERROR_WINHTTP_SECURE_CERT_DATE_INVALID: int
ERROR_WINHTTP_SECURE_CERT_CN_INVALID: int
ERROR_WINHTTP_SECURE_INVALID_CA: int
ERROR_WINHTTP_SECURE_CERT_REV_FAILED: int
ERROR_WINHTTP_SECURE_CHANNEL_ERROR: int
ERROR_WINHTTP_SECURE_INVALID_CERT: int
ERROR_WINHTTP_SECURE_CERT_REVOKED: int
ERROR_WINHTTP_SECURE_CERT_WRONG_USAGE: int
ERROR_WINHTTP_AUTODETECTION_FAILED: int
ERROR_WINHTTP_HEADER_COUNT_EXCEEDED: int
ERROR_WINHTTP_HEADER_SIZE_OVERFLOW: int
ERROR_WINHTTP_CHUNKED_ENCODING_HEADER_SIZE_OVERFLOW: int
ERROR_WINHTTP_RESPONSE_DRAIN_OVERFLOW: int
ERROR_WINHTTP_CLIENT_CERT_NO_PRIVATE_KEY: int
ERROR_WINHTTP_CLIENT_CERT_NO_ACCESS_PRIVATE_KEY: int
WINHTTP_ERROR_LAST: int
WINHTTP_NO_PROXY_NAME: None
WINHTTP_NO_PROXY_BYPASS: None
WINHTTP_NO_REFERER: None
WINHTTP_DEFAULT_ACCEPT_TYPES: None
WINHTTP_NO_ADDITIONAL_HEADERS: None
WINHTTP_NO_REQUEST_DATA: None
INTERNET_OPTION_LISTEN_TIMEOUT: int
WINHTTP_OPTION_CLIENT_CERT_ISSUER_LIST: int
