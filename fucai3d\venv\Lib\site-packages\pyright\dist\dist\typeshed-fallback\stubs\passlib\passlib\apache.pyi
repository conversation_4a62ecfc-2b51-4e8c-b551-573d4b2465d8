from typing_extensions import Self

from .context import CryptContext
from .hash import htdigest

class _CommonFile:
    encoding: str
    return_unicode: bool
    autosave: bool
    @classmethod
    def from_string(
        cls,
        data: str | bytes,
        *,
        new: bool = False,
        autoload: bool = True,
        autosave: bool = False,
        encoding: str = "utf-8",
        return_unicode: bool = True,
    ) -> Self: ...
    @classmethod
    def from_path(
        cls,
        path: str,
        *,
        new: bool = False,
        autoload: bool = True,
        autosave: bool = False,
        encoding: str = "utf-8",
        return_unicode: bool = True,
    ) -> Self: ...
    def __init__(
        self,
        path: str | None = None,
        new: bool = False,
        autoload: bool = True,
        autosave: bool = False,
        encoding: str = "utf-8",
        return_unicode: bool = True,
    ) -> None: ...
    @property
    def path(self) -> str: ...
    @path.setter
    def path(self, value: str) -> None: ...
    @property
    def mtime(self) -> float: ...
    def load_if_changed(self) -> bool: ...
    def load(self, path: str | None = None, force: bool = True) -> bool: ...
    def load_string(self, data: str | bytes) -> None: ...
    def save(self, path: str | None = None) -> None: ...
    def to_string(self) -> bytes: ...

class HtpasswdFile(_CommonFile):
    context: CryptContext
    def __init__(
        self,
        path: str | None = None,
        default_scheme: str | None = None,
        context: CryptContext = ...,
        *,
        new: bool = False,
        autoload: bool = True,
        autosave: bool = False,
        encoding: str = "utf-8",
        return_unicode: bool = True,
    ) -> None: ...
    def users(self) -> list[str | bytes]: ...
    def set_password(self, user: str, password: str | bytes) -> bool: ...
    def update(self, user: str, password: str | bytes) -> bool: ...
    def get_hash(self, user: str) -> bytes | None: ...
    def set_hash(self, user: str, hash: str | bytes) -> bool: ...
    def find(self, user: str) -> bytes | None: ...
    def delete(self, user: str) -> bool: ...
    def check_password(self, user: str, password: str | bytes) -> bool | None: ...
    def verify(self, user: str, password: str | bytes) -> bool | None: ...

class HtdigestFile(_CommonFile):
    default_realm: str | None
    def __init__(
        self,
        path: str | None = None,
        default_realm: str | None = None,
        *,
        new: bool = False,
        autoload: bool = True,
        autosave: bool = False,
        encoding: str = "utf-8",
        return_unicode: bool = True,
    ) -> None: ...
    def realms(self) -> list[str | bytes]: ...
    def users(self, realm: str | None = None) -> list[str | bytes]: ...
    def set_password(self, user: str, realm: str | None = None, password: str | bytes = ...) -> bool: ...
    def update(self, user: str, realm: str | None, password: str | bytes) -> bool: ...
    def get_hash(self, user: str, realm: str | None = None) -> htdigest | None: ...
    def set_hash(self, user: str, realm: str | None = None, hash: str | bytes = ...) -> bool: ...
    def find(self, user: str, realm: str | None) -> htdigest | None: ...
    def delete(self, user: str, realm: str | None = None) -> bool: ...
    def delete_realm(self, realm: str | None) -> int: ...
    def check_password(self, user: str, realm: str | None = None, password: str | bytes = ...) -> bool | None: ...
    def verify(self, user: str, realm: str | None, password: str | bytes) -> bool | None: ...

__all__ = ["HtpasswdFile", "HtdigestFile"]
