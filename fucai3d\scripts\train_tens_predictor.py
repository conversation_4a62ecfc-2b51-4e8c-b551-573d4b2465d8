#!/usr/bin/env python3
"""
P4-十位预测器训练脚本

完整的十位预测器训练流程，支持单模型和全模型训练。

功能：
- 支持命令行参数
- 配置文件驱动
- 进度监控和日志
- 错误处理和恢复
- 性能评估和报告

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
import argparse
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.predictors.tens_predictor import TensPredictor
    from config.config_loader import setup_logging
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='P4-十位预测器训练脚本')
    
    parser.add_argument(
        '--model', '-m',
        type=str,
        choices=['xgb', 'lgb', 'lstm', 'ensemble', 'all'],
        default='all',
        help='要训练的模型类型 (默认: all)'
    )
    
    parser.add_argument(
        '--limit', '-l',
        type=int,
        default=None,
        help='限制训练样本数量 (默认: 使用全部数据)'
    )
    
    parser.add_argument(
        '--db-path', '-d',
        type=str,
        default='data/lottery.db',
        help='数据库文件路径 (默认: data/lottery.db)'
    )
    
    parser.add_argument(
        '--save-models', '-s',
        action='store_true',
        help='训练完成后保存模型'
    )
    
    parser.add_argument(
        '--evaluate', '-e',
        action='store_true',
        help='训练完成后评估模型性能'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    return parser.parse_args()

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🚀 P4-十位预测器训练系统")
    print("基于独立位置预测理念 | 复用P3成功架构")
    print("=" * 60)

def train_single_model(predictor: TensPredictor, model_type: str, 
                      limit: int = None, verbose: bool = False) -> dict:
    """训练单个模型"""
    print(f"\n🔧 开始训练 {model_type.upper()} 模型...")
    
    start_time = time.time()
    
    try:
        # 训练模型
        result = predictor.train_model(model_type, limit)
        
        training_time = time.time() - start_time
        
        # 输出结果
        print(f"✅ {model_type.upper()} 模型训练完成!")
        print(f"   训练时间: {training_time:.2f}秒")
        
        if 'train_accuracy' in result:
            print(f"   训练准确率: {result['train_accuracy']:.4f}")
        if 'val_accuracy' in result:
            print(f"   验证准确率: {result['val_accuracy']:.4f}")
        
        if verbose:
            print(f"   详细结果: {result}")
        
        return result
        
    except Exception as e:
        print(f"❌ {model_type.upper()} 模型训练失败: {e}")
        return {'error': str(e)}

def train_all_models(predictor: TensPredictor, limit: int = None, 
                    verbose: bool = False) -> dict:
    """训练所有模型"""
    print(f"\n🔧 开始训练所有模型...")
    
    start_time = time.time()
    
    try:
        # 训练所有模型
        results = predictor.train_all_models(limit)
        
        total_time = time.time() - start_time
        
        # 输出结果
        print(f"✅ 所有模型训练完成!")
        print(f"   总训练时间: {total_time:.2f}秒")
        
        # 统计成功和失败的模型
        success_models = [name for name, result in results.items() if 'error' not in result]
        failed_models = [name for name, result in results.items() if 'error' in result]
        
        print(f"   成功模型: {success_models}")
        if failed_models:
            print(f"   失败模型: {failed_models}")
        
        # 显示各模型性能
        for model_name, result in results.items():
            if 'error' not in result:
                val_acc = result.get('val_accuracy', 0)
                print(f"   {model_name.upper()}: 验证准确率 {val_acc:.4f}")
        
        if verbose:
            print(f"   详细结果: {results}")
        
        return results
        
    except Exception as e:
        print(f"❌ 模型训练失败: {e}")
        return {'error': str(e)}

def evaluate_models(predictor: TensPredictor, model_types: list, 
                   verbose: bool = False):
    """评估模型性能"""
    print(f"\n📊 开始评估模型性能...")
    
    for model_type in model_types:
        try:
            print(f"\n🔍 评估 {model_type.upper()} 模型...")
            
            # 评估模型
            eval_result = predictor.evaluate_model(model_type, test_periods=100)
            
            # 输出评估结果
            accuracy = eval_result.get('accuracy', 0)
            top3_accuracy = eval_result.get('top3_accuracy', 0)
            avg_confidence = eval_result.get('avg_confidence', 0)
            
            print(f"   准确率: {accuracy:.4f}")
            print(f"   Top3准确率: {top3_accuracy:.4f}")
            print(f"   平均置信度: {avg_confidence:.4f}")
            
            if verbose:
                print(f"   详细评估结果: {eval_result}")
                
        except Exception as e:
            print(f"❌ {model_type.upper()} 模型评估失败: {e}")

def save_models(predictor: TensPredictor, model_types: list):
    """保存模型"""
    print(f"\n💾 开始保存模型...")
    
    try:
        saved_paths = predictor.save_models(model_types)
        
        for model_type, path in saved_paths.items():
            if not path.startswith("Error"):
                print(f"   ✅ {model_type.upper()}: {path}")
            else:
                print(f"   ❌ {model_type.upper()}: {path}")
                
    except Exception as e:
        print(f"❌ 模型保存失败: {e}")

def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()
    
    # 设置日志
    try:
        setup_logging()
    except:
        pass
    
    # 打印横幅
    print_banner()
    
    # 验证数据库文件
    if not os.path.exists(args.db_path):
        print(f"❌ 数据库文件不存在: {args.db_path}")
        sys.exit(1)
    
    print(f"📂 数据库路径: {args.db_path}")
    print(f"🎯 训练模型: {args.model}")
    if args.limit:
        print(f"📊 样本限制: {args.limit}")
    
    try:
        # 初始化预测器
        print(f"\n🔧 初始化十位预测器...")
        predictor = TensPredictor(args.db_path)
        print(f"✅ 预测器初始化完成")
        
        # 训练模型
        training_results = {}
        trained_models = []
        
        if args.model == 'all':
            # 训练所有模型
            training_results = train_all_models(predictor, args.limit, args.verbose)
            trained_models = [name for name, result in training_results.items() if 'error' not in result]
        else:
            # 训练单个模型
            result = train_single_model(predictor, args.model, args.limit, args.verbose)
            training_results[args.model] = result
            if 'error' not in result:
                trained_models.append(args.model)
        
        # 评估模型
        if args.evaluate and trained_models:
            evaluate_models(predictor, trained_models, args.verbose)
        
        # 保存模型
        if args.save_models and trained_models:
            save_models(predictor, trained_models)
        
        # 输出总结
        print(f"\n" + "=" * 60)
        print(f"🎉 P4-十位预测器训练完成!")
        print(f"✅ 成功训练模型: {trained_models}")
        
        if args.model == 'all':
            failed_models = [name for name, result in training_results.items() if 'error' in result]
            if failed_models:
                print(f"❌ 失败模型: {failed_models}")
        
        print(f"=" * 60)
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断训练")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 训练过程发生错误: {e}")
        import traceback
        if args.verbose:
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
