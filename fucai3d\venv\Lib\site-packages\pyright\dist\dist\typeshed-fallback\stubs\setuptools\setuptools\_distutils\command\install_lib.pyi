from _typeshed import Incomplete, <PERSON><PERSON><PERSON>
from typing import ClassVar

from ..cmd import Command

class install_lib(Command):
    description: ClassVar[str]
    user_options: ClassVar[list[tuple[str, str | None, str]]]
    boolean_options: ClassVar[list[str]]
    negative_opt: ClassVar[dict[str, str]]
    install_dir: Incomplete
    build_dir: Incomplete
    force: bool
    compile: Incomplete
    optimize: Incomplete
    skip_build: Incomplete
    def initialize_options(self) -> None: ...
    def finalize_options(self) -> None: ...
    def run(self) -> None: ...
    def build(self) -> None: ...
    def install(self) -> list[str] | MaybeNone: ...
    def byte_compile(self, files) -> None: ...
    def get_outputs(self): ...
    def get_inputs(self): ...
