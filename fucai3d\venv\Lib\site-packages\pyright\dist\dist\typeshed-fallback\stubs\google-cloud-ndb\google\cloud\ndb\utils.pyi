import threading
from typing import Any

TRUTHY_STRINGS: Any

def asbool(value): ...

DEBUG: Any

def code_info(*args, **kwargs) -> None: ...
def decorator(*args, **kwargs) -> None: ...
def frame_info(*args, **kwargs) -> None: ...
def func_info(*args, **kwargs) -> None: ...
def gen_info(*args, **kwargs) -> None: ...
def get_stack(*args, **kwargs) -> None: ...
def logging_debug(log, message, *args, **kwargs) -> None: ...

class keyword_only:
    defaults: Any
    def __init__(self, **kwargs) -> None: ...
    def __call__(self, wrapped): ...

def positional(max_pos_args): ...

threading_local = threading.local

def tweak_logging(*args, **kwargs) -> None: ...
def wrapping(*args, **kwargs) -> None: ...
