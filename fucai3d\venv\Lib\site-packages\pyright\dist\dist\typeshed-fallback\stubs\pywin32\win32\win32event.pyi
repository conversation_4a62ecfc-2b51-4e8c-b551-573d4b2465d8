from collections.abc import Iterable

import _win32typing
from win32.lib.pywintypes import error as error

def CancelWaitableTimer() -> None: ...
def CreateEvent(
    EventAttributes: _win32typing.PySECURITY_ATTRIBUTES | None,
    bManualReset: int | bool,
    bInitialState: int | bool,
    Name: str | None,
    /,
) -> int: ...
def CreateMutex(MutexAttributes: _win32typing.PySECURITY_ATTRIBUTES, InitialOwner, Name: str, /) -> int: ...
def CreateSemaphore(
    SemaphoreAttributes: _win32typing.PySECURITY_ATTRIBUTES, InitialCount, MaximumCount, SemaphoreName, /
) -> int: ...
def CreateWaitableTimer(TimerAttributes: _win32typing.PySECURITY_ATTRIBUTES, ManualReset, TimerName, /) -> int: ...
def CreateWaitableTimerEx(
    lpTimerAttributes: _win32typing.PySECURITY_ATTRIBUTES | None, lpTimerName: str | None, dwFlags: int, dwDesiredAccess: int, /
) -> _win32typing.PyHANDLE: ...
def MsgWaitForMultipleObjects(handlelist: Iterable[int], bWaitAll: int, milliseconds: int, wakeMask: int, /) -> int: ...
def MsgWaitForMultipleObjectsEx(handlelist: list[int], milliseconds, wakeMask, waitFlags, /): ...
def OpenEvent(desiredAccess, bInheritHandle, name: str, /) -> int: ...
def OpenMutex(desiredAccess, bInheritHandle, name: str, /) -> int: ...
def OpenSemaphore(desiredAccess, bInheritHandle, name: str, /) -> int: ...
def OpenWaitableTimer(desiredAccess, bInheritHandle, timerName, /) -> int: ...
def PulseEvent(hEvent: int, /) -> None: ...
def ReleaseMutex(hEvent: int, /) -> None: ...
def ReleaseSemaphore(hEvent: int, lReleaseCount, /): ...
def ResetEvent(hEvent: int, /) -> None: ...
def SetEvent(hEvent: int, /) -> None: ...
def SetWaitableTimer(handle: int, dueTime, period, func, param, resume_state, /) -> None: ...
def WaitForMultipleObjects(handlelist: list[int], bWaitAll, milliseconds, /): ...
def WaitForMultipleObjectsEx(handlelist: list[int], bWaitAll, milliseconds, bAlertable, /): ...
def WaitForSingleObject(hHandle: int, milliseconds: int, /) -> int: ...
def WaitForSingleObjectEx(hHandle: int, milliseconds, bAlertable, /): ...
def WaitForInputIdle(hProcess: int, milliseconds, /): ...
def SignalObjectAndWait(*args): ...  # incomplete

CREATE_WAITABLE_TIMER_HIGH_RESOLUTION: int
CREATE_WAITABLE_TIMER_MANUAL_RESET: int
EVENT_ALL_ACCESS: int
EVENT_MODIFY_STATE: int
INFINITE: int
MAXIMUM_WAIT_OBJECTS: int
QS_ALLEVENTS: int
QS_ALLINPUT: int
QS_HOTKEY: int
QS_INPUT: int
QS_KEY: int
QS_MOUSE: int
QS_MOUSEBUTTON: int
QS_MOUSEMOVE: int
QS_PAINT: int
QS_POSTMESSAGE: int
QS_SENDMESSAGE: int
QS_TIMER: int
SYNCHRONIZE: int
TIMER_ALL_ACCESS: int
TIMER_MODIFY_STATE: int
TIMER_QUERY_STATE: int
WAIT_ABANDONED: int
WAIT_ABANDONED_0: int
WAIT_FAILED: int
WAIT_IO_COMPLETION: int
WAIT_OBJECT_0: int
WAIT_TIMEOUT: int
UNICODE: int
