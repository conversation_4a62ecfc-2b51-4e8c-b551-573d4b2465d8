from _typeshed import Incomplete

from reportlab.graphics.barcode.common import Barcode

class USPS_4State(Barcode):
    tops: Incomplete
    bottoms: Incomplete
    dimensions: Incomplete
    def __init__(self, value: str = "01234567094987654321", routing: str = "", **kwd) -> None: ...
    @staticmethod
    def scale(kind, D, s): ...
    @property
    def tracking(self): ...
    @tracking.setter
    def tracking(self, tracking) -> None: ...
    @property
    def routing(self): ...
    @routing.setter
    def routing(self, routing) -> None: ...
    @property
    def widthSize(self): ...
    @widthSize.setter
    def widthSize(self, value) -> None: ...
    @property
    def heightSize(self): ...
    @heightSize.setter
    def heightSize(self, value) -> None: ...
    @property
    def fontSize(self): ...
    @fontSize.setter
    def fontSize(self, value) -> None: ...
    @property
    def humanReadable(self): ...
    @humanReadable.setter
    def humanReadable(self, value) -> None: ...
    @property
    def binary(self): ...
    @property
    def codewords(self): ...
    @property
    def table1(self): ...
    @property
    def table2(self): ...
    @property
    def characters(self): ...
    @property
    def barcodes(self): ...
    table4: Incomplete
    @property
    def horizontalClearZone(self): ...
    @property
    def verticalClearZone(self): ...
    @property
    def barWidth(self): ...
    @barWidth.setter
    def barWidth(self, value) -> None: ...
    @property
    def pitch(self): ...
    @pitch.setter
    def pitch(self, value) -> None: ...
    @property
    def barHeight(self): ...
    @barHeight.setter
    def barHeight(self, value) -> None: ...
    @property
    def widthScale(self): ...
    @property
    def heightScale(self): ...
    @property
    def width(self): ...
    @width.setter
    def width(self, v) -> None: ...
    @property
    def height(self): ...
    @height.setter
    def height(self, v) -> None: ...
    def computeSize(self) -> None: ...
    def wrap(self, aW, aH): ...
    def draw(self) -> None: ...
    @property
    def value(self): ...
    @value.setter
    def value(self, value) -> None: ...
    def drawHumanReadable(self) -> None: ...
    def annotate(self, x, y, text, fontName, fontSize, anchor: str = "middle") -> None: ...

__all__ = ("USPS_4State",)
