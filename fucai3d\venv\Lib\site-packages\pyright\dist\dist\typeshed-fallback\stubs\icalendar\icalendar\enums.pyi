from enum import Enum

class PARTSTAT(Enum):
    NEEDS_ACTION = "NEEDS-ACTION"
    ACCEPTED = "ACCEPTED"
    DECLINED = "DECLINED"
    TENTATIVE = "TENTATIVE"
    DELEGATED = "DELEGATED"
    COMPLETED = "COMPLETED"
    IN_PROCESS = "IN-PROCESS"

class FBTYPE(Enum):
    FREE = "FREE"
    BUSY = "BUSY"
    BUSY_UNAVAILABLE = "BUSY-UNAVAILABLE"
    BUSY_TENTATIVE = "BUSY-TENTATIVE"

class CUTYPE(Enum):
    INDIVIDUAL = "INDIVIDUAL"
    GROUP = "GROUP"
    RESOURCE = "RESOURCE"
    ROOM = "ROOM"
    UNKNOWN = "UNKNOWN"

class RELTYPE(Enum):
    PARENT = "PARENT"
    CHILD = "CHILD"
    SIBLING = "SIBLING"

class RANGE(Enum):
    THISANDFUTURE = "THISANDFUTURE"
    THISANDPRIOR = "THISANDPRIOR"  # deprecated

class RELATED(Enum):
    START = "START"
    END = "END"

class ROLE(Enum):
    CHAIR = "CHAIR"
    REQ_PARTICIPANT = "REQ-PARTICIPANT"
    OPT_PARTICIPANT = "OPT-PARTICIPANT"
    NON_PARTICIPANT = "NON-PARTICIPANT"

__all__ = ["PARTSTAT", "FBTYPE", "CUTYPE", "RANGE", "RELATED", "ROLE", "RELTYPE"]
