from _typeshed import Incomplete
from collections.abc import Iterable
from typing import ClassVar

from authlib.jose.rfc7516 import JWEAlgorithmWithTagAwareKeyAgreement

class ECDH1PUAlgorithm(JWEAlgorithmWithTagAwareKeyAgreement):
    EXTRA_HEADERS: ClassVar[Iterable[str]]
    ALLOWED_KEY_CLS: Incomplete
    name: str
    description: str
    key_size: Incomplete
    aeskw: Incomplete
    def __init__(self, key_size=None) -> None: ...
    def prepare_key(self, raw_data): ...
    def generate_preset(self, enc_alg, key): ...
    def compute_shared_key(self, shared_key_e, shared_key_s): ...
    def compute_fixed_info(self, headers, bit_size, tag): ...
    def compute_derived_key(self, shared_key, fixed_info, bit_size): ...
    def deliver_at_sender(self, sender_static_key, sender_ephemeral_key, recipient_pubkey, headers, bit_size, tag): ...
    def deliver_at_recipient(self, recipient_key, sender_static_pubkey, sender_ephemeral_pubkey, headers, bit_size, tag): ...
    def generate_keys_and_prepare_headers(self, enc_alg, key, sender_key, preset=None): ...
    def agree_upon_key_and_wrap_cek(self, enc_alg, headers, key, sender_key, epk, cek, tag): ...
    def wrap(self, enc_alg, headers, key, sender_key, preset=None): ...
    def unwrap(self, enc_alg, ek, headers, key, sender_key, tag=None): ...

JWE_DRAFT_ALG_ALGORITHMS: Incomplete

def register_jwe_alg_draft(cls) -> None: ...
