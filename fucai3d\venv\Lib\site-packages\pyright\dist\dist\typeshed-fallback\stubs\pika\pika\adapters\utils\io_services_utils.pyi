import abc

from pika.adapters.utils.nbio_interface import AbstractIOReference, AbstractStreamTransport

def check_callback_arg(callback, name) -> None: ...
def check_fd_arg(fd) -> None: ...

class SocketConnectionMixin:
    def connect_socket(self, sock, resolved_addr, on_done): ...

class StreamingConnectionMixin:
    def create_streaming_connection(self, protocol_factory, sock, on_done, ssl_context=None, server_hostname=None): ...

class _AsyncServiceAsyncHandle(AbstractIOReference):
    def __init__(self, subject) -> None: ...
    def cancel(self): ...

class _AsyncSocketConnector:
    def __init__(self, nbio, sock, resolved_addr, on_done) -> None: ...
    def start(self): ...
    def cancel(self): ...

class _AsyncStreamConnector:
    def __init__(self, nbio, protocol_factory, sock, ssl_context, server_hostname, on_done) -> None: ...
    def start(self): ...
    def cancel(self): ...

class _AsyncTransportBase(AbstractStreamTransport, metaclass=abc.ABCMeta):
    class RxEndOfFile(OSError):
        def __init__(self) -> None: ...

    def __init__(self, sock, protocol, nbio) -> None: ...
    def abort(self) -> None: ...
    def get_protocol(self): ...
    def get_write_buffer_size(self): ...

class _AsyncPlaintextTransport(_AsyncTransportBase):
    def __init__(self, sock, protocol, nbio) -> None: ...
    def write(self, data) -> None: ...

class _AsyncSSLTransport(_AsyncTransportBase):
    def __init__(self, sock, protocol, nbio) -> None: ...
    def write(self, data) -> None: ...
