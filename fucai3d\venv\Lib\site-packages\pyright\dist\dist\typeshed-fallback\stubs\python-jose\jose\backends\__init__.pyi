from .base import <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>
from .cryptography_backend import (
    Cryptography<PERSON><PERSON><PERSON><PERSON> as Cryptography<PERSON><PERSON><PERSON><PERSON>,
    Cryptography<PERSON><PERSON><PERSON> as Cryptography<PERSON><PERSON><PERSON>,
    CryptographyHMA<PERSON>Key as CryptographyHMACK<PERSON>,
    Cryptography<PERSON><PERSON><PERSON><PERSON> as CryptographyRS<PERSON><PERSON><PERSON>,
)
from .ecdsa_backend import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .native import <PERSON><PERSON><PERSON><PERSON> as NativeHMA<PERSON><PERSON><PERSON>, get_random_bytes as get_random_bytes
from .rsa_backend import <PERSON><PERSON><PERSON><PERSON> as BackendRS<PERSON>K<PERSON>

# python-jose relies on importing from cryptography_backend
# then falling back on other imports
# these are all the potential options
AESKey: type[CryptographyAESKey] | None
HMACKey: type[CryptographyH<PERSON>C<PERSON>ey | NativeHMACKey]
RSAKey: type[Cryptography<PERSON><PERSON>K<PERSON> | BackendRSAKey] | None
ECKey: type[CryptographyECKey | ECDSAECKey]
