from typing import Any, Literal

from .backends import <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ey as <PERSON><PERSON><PERSON>, HMAC<PERSON>ey as HMAC<PERSON><PERSON>, RS<PERSON><PERSON><PERSON> as RSA<PERSON><PERSON>
from .backends.base import <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>, Key

def get_key(algorithm: str) -> type[Key] | None: ...
def register_key(algorithm: str, key_class: type[Key]) -> Literal[True]: ...
def construct(
    # explicitly checks for key_data as dict instance, instead of a Mapping
    key_data: str | bytes | dict[str, Any] | Key,
    algorithm: str | None = None,
) -> Key: ...
