from _typeshed import Incomplete
from builtins import list as _list

from ..rest import RestClientOptions
from ..types import TimeoutType

class Users:
    domain: Incomplete
    protocol: Incomplete
    client: Incomplete
    def __init__(
        self,
        domain: str,
        token: str,
        telemetry: bool = True,
        timeout: TimeoutType = 5.0,
        protocol: str = "https",
        rest_options: RestClientOptions | None = None,
    ) -> None: ...
    def list(
        self,
        page: int = 0,
        per_page: int = 25,
        sort: str | None = None,
        connection: str | None = None,
        q: str | None = None,
        search_engine: str | None = None,
        include_totals: bool = True,
        fields: _list[str] | None = None,
        include_fields: bool = True,
    ) -> dict[str, Incomplete]: ...
    async def list_async(
        self,
        page: int = 0,
        per_page: int = 25,
        sort: str | None = None,
        connection: str | None = None,
        q: str | None = None,
        search_engine: str | None = None,
        include_totals: bool = True,
        fields: _list[str] | None = None,
        include_fields: bool = True,
    ) -> dict[str, Incomplete]: ...
    def create(self, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    async def create_async(self, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    def get(self, id: str, fields: _list[str] | None = None, include_fields: bool = True) -> dict[str, Incomplete]: ...
    async def get_async(
        self, id: str, fields: _list[str] | None = None, include_fields: bool = True
    ) -> dict[str, Incomplete]: ...
    def delete(self, id: str): ...
    async def delete_async(self, id: str): ...
    def update(self, id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    async def update_async(self, id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    def list_organizations(
        self, id: str, page: int = 0, per_page: int = 25, include_totals: bool = True
    ) -> dict[str, Incomplete]: ...
    async def list_organizations_async(
        self, id: str, page: int = 0, per_page: int = 25, include_totals: bool = True
    ) -> dict[str, Incomplete]: ...
    def list_roles(self, id: str, page: int = 0, per_page: int = 25, include_totals: bool = True) -> dict[str, Incomplete]: ...
    async def list_roles_async(
        self, id: str, page: int = 0, per_page: int = 25, include_totals: bool = True
    ) -> dict[str, Incomplete]: ...
    def remove_roles(self, id: str, roles: _list[str]): ...
    async def remove_roles_async(self, id: str, roles: _list[str]): ...
    def add_roles(self, id: str, roles: _list[str]) -> dict[str, Incomplete]: ...
    async def add_roles_async(self, id: str, roles: _list[str]) -> dict[str, Incomplete]: ...
    def list_permissions(
        self, id: str, page: int = 0, per_page: int = 25, include_totals: bool = True
    ) -> dict[str, Incomplete]: ...
    async def list_permissions_async(
        self, id: str, page: int = 0, per_page: int = 25, include_totals: bool = True
    ) -> dict[str, Incomplete]: ...
    def remove_permissions(self, id: str, permissions: _list[str]): ...
    async def remove_permissions_async(self, id: str, permissions: _list[str]): ...
    def add_permissions(self, id: str, permissions: _list[str]) -> dict[str, Incomplete]: ...
    async def add_permissions_async(self, id: str, permissions: _list[str]) -> dict[str, Incomplete]: ...
    def delete_multifactor(self, id: str, provider: str): ...
    async def delete_multifactor_async(self, id: str, provider: str): ...
    def delete_authenticators(self, id: str): ...
    async def delete_authenticators_async(self, id: str): ...
    def unlink_user_account(self, id: str, provider: str, user_id: str): ...
    async def unlink_user_account_async(self, id: str, provider: str, user_id: str): ...
    def link_user_account(self, user_id: str, body: dict[str, Incomplete]) -> _list[dict[str, Incomplete]]: ...
    async def link_user_account_async(self, user_id: str, body: dict[str, Incomplete]) -> _list[dict[str, Incomplete]]: ...
    def regenerate_recovery_code(self, user_id: str) -> dict[str, Incomplete]: ...
    async def regenerate_recovery_code_async(self, user_id: str) -> dict[str, Incomplete]: ...
    def get_guardian_enrollments(self, user_id: str) -> dict[str, Incomplete]: ...
    async def get_guardian_enrollments_async(self, user_id: str) -> dict[str, Incomplete]: ...
    def get_log_events(
        self, user_id: str, page: int = 0, per_page: int = 50, sort: str | None = None, include_totals: bool = False
    ) -> dict[str, Incomplete]: ...
    async def get_log_events_async(
        self, user_id: str, page: int = 0, per_page: int = 50, sort: str | None = None, include_totals: bool = False
    ) -> dict[str, Incomplete]: ...
    def invalidate_remembered_browsers(self, user_id: str) -> dict[str, Incomplete]: ...
    async def invalidate_remembered_browsers_async(self, user_id: str) -> dict[str, Incomplete]: ...
    def get_authentication_methods(self, user_id: str) -> dict[str, Incomplete]: ...
    async def get_authentication_methods_async(self, user_id: str) -> dict[str, Incomplete]: ...
    def get_authentication_method_by_id(self, user_id: str, authentication_method_id: str) -> dict[str, Incomplete]: ...
    async def get_authentication_method_by_id_async(
        self, user_id: str, authentication_method_id: str
    ) -> dict[str, Incomplete]: ...
    def create_authentication_method(self, user_id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    async def create_authentication_method_async(self, user_id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    def update_authentication_methods(self, user_id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    async def update_authentication_methods_async(self, user_id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    def update_authentication_method_by_id(
        self, user_id: str, authentication_method_id: str, body: dict[str, Incomplete]
    ) -> dict[str, Incomplete]: ...
    async def update_authentication_method_by_id_async(
        self, user_id: str, authentication_method_id: str, body: dict[str, Incomplete]
    ) -> dict[str, Incomplete]: ...
    def delete_authentication_methods(self, user_id: str): ...
    async def delete_authentication_methods_async(self, user_id: str): ...
    def delete_authentication_method_by_id(self, user_id: str, authentication_method_id: str): ...
    async def delete_authentication_method_by_id_async(self, user_id: str, authentication_method_id: str): ...
    def list_tokensets(self, id: str, page: int = 0, per_page: int = 25, include_totals: bool = True): ...
    def delete_tokenset_by_id(self, user_id: str, tokenset_id: str): ...
