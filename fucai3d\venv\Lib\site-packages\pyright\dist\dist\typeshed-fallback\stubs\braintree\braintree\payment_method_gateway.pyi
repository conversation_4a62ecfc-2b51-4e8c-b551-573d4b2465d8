from _typeshed import Incomplete

from braintree.error_result import ErrorResult
from braintree.resource import Resource
from braintree.successful_result import SuccessfulResult

class PaymentMethodGateway:
    gateway: Incomplete
    config: Incomplete
    def __init__(self, gateway) -> None: ...
    def create(self, params: dict[str, Incomplete] | None = None) -> SuccessfulResult | ErrorResult: ...
    def find(self, payment_method_token: str) -> Resource: ...
    def update(self, payment_method_token: str, params) -> SuccessfulResult | ErrorResult: ...
    def delete(self, payment_method_token: str, options=None) -> SuccessfulResult: ...
    options: dict[str, Incomplete]
    def grant(self, payment_method_token: str, options=None) -> SuccessfulResult | ErrorResult: ...
    def revoke(self, payment_method_token: str) -> SuccessfulResult | ErrorResult: ...
