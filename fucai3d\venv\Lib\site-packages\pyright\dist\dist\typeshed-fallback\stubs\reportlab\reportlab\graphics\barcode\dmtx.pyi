from _typeshed import Incomplete

from reportlab.graphics.barcode.common import Barcode
from reportlab.graphics.widgetbase import Widget

class _DMTXCheck:
    @classmethod
    def pylibdmtx_check(cls) -> None: ...

class DataMatrix(Barcode, _DMTXCheck):
    color: Incomplete
    bgColor: Incomplete
    def __init__(self, value: str = "", **kwds) -> None: ...
    @property
    def value(self): ...
    @value.setter
    def value(self, v) -> None: ...
    @property
    def size(self): ...
    @size.setter
    def size(self, v) -> None: ...
    @property
    def border(self): ...
    @border.setter
    def border(self, v) -> None: ...
    @property
    def x(self): ...
    @x.setter
    def x(self, v) -> None: ...
    @property
    def y(self): ...
    @y.setter
    def y(self, v) -> None: ...
    @property
    def cellSize(self): ...
    @cellSize.setter
    def cellSize(self, v) -> None: ...
    @property
    def encoding(self): ...
    @encoding.setter
    def encoding(self, v) -> None: ...
    @property
    def anchor(self): ...
    @anchor.setter
    def anchor(self, v) -> None: ...
    def recalc(self) -> None: ...
    @property
    def matrix(self): ...
    @property  # type: ignore[misc]  # TODO: for mypy < 1.16
    def width(self): ...  # type: ignore[override]
    @property  # type: ignore[misc]  # TODO: for mypy < 1.16
    def height(self): ...  # type: ignore[override]
    @property
    def cellWidth(self): ...
    @property
    def cellHeight(self): ...
    def draw(self) -> None: ...

class DataMatrixWidget(Widget, _DMTXCheck):
    codeName: str
    value: Incomplete
    def __init__(self, value: str = "Hello Cruel World!", **kwds) -> None: ...
    def rect(self, x, y, w, h, fill: int = 1, stroke: int = 0) -> None: ...
    def saveState(self, *args, **kwds) -> None: ...
    restoreState = saveState
    setStrokeColor = saveState
    def setFillColor(self, c) -> None: ...
    def draw(self): ...
