CNLEN: int
LM20_CNLEN: int
DNLEN: int
LM20_DNLEN: int
UNCLEN: int
LM20_UNCLEN: int
NNLEN: int
LM20_NNLEN: int
RMLEN: int
LM20_RMLEN: int
SNLEN: int
LM20_SNLEN: int
STXTLEN: int
LM20_STXTLEN: int
PATHLEN: int
LM20_PATHLEN: int
DEVLEN: int
LM20_DEVLEN: int
EVLEN: int
UNLEN: int
LM20_UNLEN: int
GNLEN: int
LM20_GNLEN: int
PWLEN: int
LM20_PWLEN: int
SHPWLEN: int
CLTYPE_LEN: int
MAXCOMMENTSZ: int
LM20_MAXCOMMENTSZ: int
QNLEN: int
LM20_QNLEN: int
ALERTSZ: int
NETBIOS_NAME_LEN: int
CRYPT_KEY_LEN: int
CRYPT_TXT_LEN: int
ENCRYPTED_PWLEN: int
SESSION_PWLEN: int
SESSION_CRYPT_KLEN: int
PARMNUM_ALL: int
PARM_ERROR_NONE: int
PARMNUM_BASE_INFOLEVEL: int
NULL: int
PLATFORM_ID_DOS: int
PLATFORM_ID_OS2: int
PLATFORM_ID_NT: int
PLATFORM_ID_OSF: int
PLATFORM_ID_VMS: int
MAX_LANMAN_MESSAGE_ID: int
UF_SCRIPT: int
UF_ACCOUNTDISABLE: int
UF_HOMEDIR_REQUIRED: int
UF_LOCKOUT: int
UF_PASSWD_NOTREQD: int
UF_PASSWD_CANT_CHANGE: int
UF_TEMP_DUPLICATE_ACCOUNT: int
UF_NORMAL_ACCOUNT: int
UF_INTERDOMAIN_TRUST_ACCOUNT: int
UF_WORKSTATION_TRUST_ACCOUNT: int
UF_SERVER_TRUST_ACCOUNT: int
UF_MACHINE_ACCOUNT_MASK: int
UF_ACCOUNT_TYPE_MASK: int
UF_DONT_EXPIRE_PASSWD: int
UF_MNS_LOGON_ACCOUNT: int
UF_SETTABLE_BITS: int
FILTER_TEMP_DUPLICATE_ACCOUNT: int
FILTER_NORMAL_ACCOUNT: int
FILTER_INTERDOMAIN_TRUST_ACCOUNT: int
FILTER_WORKSTATION_TRUST_ACCOUNT: int
FILTER_SERVER_TRUST_ACCOUNT: int
LG_INCLUDE_INDIRECT: int
AF_OP_PRINT: int
AF_OP_COMM: int
AF_OP_SERVER: int
AF_OP_ACCOUNTS: int
AF_SETTABLE_BITS: int
UAS_ROLE_STANDALONE: int
UAS_ROLE_MEMBER: int
UAS_ROLE_BACKUP: int
UAS_ROLE_PRIMARY: int
USER_NAME_PARMNUM: int
USER_PASSWORD_PARMNUM: int
USER_PASSWORD_AGE_PARMNUM: int
USER_PRIV_PARMNUM: int
USER_HOME_DIR_PARMNUM: int
USER_COMMENT_PARMNUM: int
USER_FLAGS_PARMNUM: int
USER_SCRIPT_PATH_PARMNUM: int
USER_AUTH_FLAGS_PARMNUM: int
USER_FULL_NAME_PARMNUM: int
USER_USR_COMMENT_PARMNUM: int
USER_PARMS_PARMNUM: int
USER_WORKSTATIONS_PARMNUM: int
USER_LAST_LOGON_PARMNUM: int
USER_LAST_LOGOFF_PARMNUM: int
USER_ACCT_EXPIRES_PARMNUM: int
USER_MAX_STORAGE_PARMNUM: int
USER_UNITS_PER_WEEK_PARMNUM: int
USER_LOGON_HOURS_PARMNUM: int
USER_PAD_PW_COUNT_PARMNUM: int
USER_NUM_LOGONS_PARMNUM: int
USER_LOGON_SERVER_PARMNUM: int
USER_COUNTRY_CODE_PARMNUM: int
USER_CODE_PAGE_PARMNUM: int
USER_PRIMARY_GROUP_PARMNUM: int
USER_PROFILE: int
USER_PROFILE_PARMNUM: int
USER_HOME_DIR_DRIVE_PARMNUM: int
USER_NAME_INFOLEVEL: int
USER_PASSWORD_INFOLEVEL: int
USER_PASSWORD_AGE_INFOLEVEL: int
USER_PRIV_INFOLEVEL: int
USER_HOME_DIR_INFOLEVEL: int
USER_COMMENT_INFOLEVEL: int
USER_FLAGS_INFOLEVEL: int
USER_SCRIPT_PATH_INFOLEVEL: int
USER_AUTH_FLAGS_INFOLEVEL: int
USER_FULL_NAME_INFOLEVEL: int
USER_USR_COMMENT_INFOLEVEL: int
USER_PARMS_INFOLEVEL: int
USER_WORKSTATIONS_INFOLEVEL: int
USER_LAST_LOGON_INFOLEVEL: int
USER_LAST_LOGOFF_INFOLEVEL: int
USER_ACCT_EXPIRES_INFOLEVEL: int
USER_MAX_STORAGE_INFOLEVEL: int
USER_UNITS_PER_WEEK_INFOLEVEL: int
USER_LOGON_HOURS_INFOLEVEL: int
USER_PAD_PW_COUNT_INFOLEVEL: int
USER_NUM_LOGONS_INFOLEVEL: int
USER_LOGON_SERVER_INFOLEVEL: int
USER_COUNTRY_CODE_INFOLEVEL: int
USER_CODE_PAGE_INFOLEVEL: int
USER_PRIMARY_GROUP_INFOLEVEL: int
USER_HOME_DIR_DRIVE_INFOLEVEL: int
NULL_USERSETINFO_PASSWD: str
UNITS_PER_DAY: int
UNITS_PER_WEEK: int
USER_PRIV_MASK: int
USER_PRIV_GUEST: int
USER_PRIV_USER: int
USER_PRIV_ADMIN: int
MAX_PASSWD_LEN: int
DEF_MIN_PWLEN: int
DEF_PWUNIQUENESS: int
DEF_MAX_PWHIST: int
DEF_MAX_BADPW: int
VALIDATED_LOGON: int
PASSWORD_EXPIRED: int
NON_VALIDATED_LOGON: int
VALID_LOGOFF: int
MODALS_MIN_PASSWD_LEN_PARMNUM: int
MODALS_MAX_PASSWD_AGE_PARMNUM: int
MODALS_MIN_PASSWD_AGE_PARMNUM: int
MODALS_FORCE_LOGOFF_PARMNUM: int
MODALS_PASSWD_HIST_LEN_PARMNUM: int
MODALS_ROLE_PARMNUM: int
MODALS_PRIMARY_PARMNUM: int
MODALS_DOMAIN_NAME_PARMNUM: int
MODALS_DOMAIN_ID_PARMNUM: int
MODALS_LOCKOUT_DURATION_PARMNUM: int
MODALS_LOCKOUT_OBSERVATION_WINDOW_PARMNUM: int
MODALS_LOCKOUT_THRESHOLD_PARMNUM: int
MODALS_MIN_PASSWD_LEN_INFOLEVEL: int
MODALS_MAX_PASSWD_AGE_INFOLEVEL: int
MODALS_MIN_PASSWD_AGE_INFOLEVEL: int
MODALS_FORCE_LOGOFF_INFOLEVEL: int
MODALS_PASSWD_HIST_LEN_INFOLEVEL: int
MODALS_ROLE_INFOLEVEL: int
MODALS_PRIMARY_INFOLEVEL: int
MODALS_DOMAIN_NAME_INFOLEVEL: int
MODALS_DOMAIN_ID_INFOLEVEL: int
GROUPIDMASK: int
GROUP_ALL_PARMNUM: int
GROUP_NAME_PARMNUM: int
GROUP_COMMENT_PARMNUM: int
GROUP_ATTRIBUTES_PARMNUM: int
GROUP_ALL_INFOLEVEL: int
GROUP_NAME_INFOLEVEL: int
GROUP_COMMENT_INFOLEVEL: int
GROUP_ATTRIBUTES_INFOLEVEL: int
LOCALGROUP_NAME_PARMNUM: int
LOCALGROUP_COMMENT_PARMNUM: int
MAXPERMENTRIES: int
ACCESS_NONE: int
ACCESS_READ: int
ACCESS_WRITE: int
ACCESS_CREATE: int
ACCESS_EXEC: int
ACCESS_DELETE: int
ACCESS_ATRIB: int
ACCESS_PERM: int
ACCESS_GROUP: int
ACCESS_AUDIT: int
ACCESS_SUCCESS_OPEN: int
ACCESS_SUCCESS_WRITE: int
ACCESS_SUCCESS_DELETE: int
ACCESS_SUCCESS_ACL: int
ACCESS_SUCCESS_MASK: int
ACCESS_FAIL_OPEN: int
ACCESS_FAIL_WRITE: int
ACCESS_FAIL_DELETE: int
ACCESS_FAIL_ACL: int
ACCESS_FAIL_MASK: int
ACCESS_FAIL_SHIFT: int
ACCESS_RESOURCE_NAME_PARMNUM: int
ACCESS_ATTR_PARMNUM: int
ACCESS_COUNT_PARMNUM: int
ACCESS_RESOURCE_NAME_INFOLEVEL: int
ACCESS_ATTR_INFOLEVEL: int
ACCESS_COUNT_INFOLEVEL: int
ACCESS_LETTERS: str
NETLOGON_CONTROL_QUERY: int
NETLOGON_CONTROL_REPLICATE: int
NETLOGON_CONTROL_SYNCHRONIZE: int
NETLOGON_CONTROL_PDC_REPLICATE: int
NETLOGON_CONTROL_REDISCOVER: int
NETLOGON_CONTROL_TC_QUERY: int
NETLOGON_CONTROL_TRANSPORT_NOTIFY: int
NETLOGON_CONTROL_FIND_USER: int
NETLOGON_CONTROL_UNLOAD_NETLOGON_DLL: int
NETLOGON_CONTROL_BACKUP_CHANGE_LOG: int
NETLOGON_CONTROL_TRUNCATE_LOG: int
NETLOGON_CONTROL_SET_DBFLAG: int
NETLOGON_CONTROL_BREAKPOINT: int
NETLOGON_REPLICATION_NEEDED: int
NETLOGON_REPLICATION_IN_PROGRESS: int
NETLOGON_FULL_SYNC_REPLICATION: int
NETLOGON_REDO_NEEDED: int

def TEXT(x: str) -> str: ...

MAX_PREFERRED_LENGTH: int
PARM_ERROR_UNKNOWN: int
MESSAGE_FILENAME: str
OS2MSG_FILENAME: str
HELP_MSG_FILENAME: str
BACKUP_MSG_FILENAME: str
TIMEQ_FOREVER: int
USER_MAXSTORAGE_UNLIMITED: int
USER_NO_LOGOFF: int
DEF_MAX_PWAGE: int
DEF_MIN_PWAGE: int
DEF_FORCE_LOGOFF: int
ONE_DAY: int
GROUP_SPECIALGRP_USERS: str
GROUP_SPECIALGRP_ADMINS: str
GROUP_SPECIALGRP_GUESTS: str
GROUP_SPECIALGRP_LOCAL: str
ACCESS_ALL: int
SV_PLATFORM_ID_OS2: int
SV_PLATFORM_ID_NT: int
MAJOR_VERSION_MASK: int
SV_TYPE_WORKSTATION: int
SV_TYPE_SERVER: int
SV_TYPE_SQLSERVER: int
SV_TYPE_DOMAIN_CTRL: int
SV_TYPE_DOMAIN_BAKCTRL: int
SV_TYPE_TIME_SOURCE: int
SV_TYPE_AFP: int
SV_TYPE_NOVELL: int
SV_TYPE_DOMAIN_MEMBER: int
SV_TYPE_PRINTQ_SERVER: int
SV_TYPE_DIALIN_SERVER: int
SV_TYPE_XENIX_SERVER: int
SV_TYPE_SERVER_UNIX: int
SV_TYPE_NT: int
SV_TYPE_WFW: int
SV_TYPE_SERVER_MFPN: int
SV_TYPE_SERVER_NT: int
SV_TYPE_POTENTIAL_BROWSER: int
SV_TYPE_BACKUP_BROWSER: int
SV_TYPE_MASTER_BROWSER: int
SV_TYPE_DOMAIN_MASTER: int
SV_TYPE_SERVER_OSF: int
SV_TYPE_SERVER_VMS: int
SV_TYPE_WINDOWS: int
SV_TYPE_DFS: int
SV_TYPE_CLUSTER_NT: int
SV_TYPE_DCE: int
SV_TYPE_ALTERNATE_XPORT: int
SV_TYPE_DOMAIN_ENUM: int
SV_TYPE_ALL: int
SV_NODISC: int
SV_USERSECURITY: int
SV_SHARESECURITY: int
SV_HIDDEN: int
SV_VISIBLE: int
SV_PLATFORM_ID_PARMNUM: int
SV_NAME_PARMNUM: int
SV_VERSION_MAJOR_PARMNUM: int
SV_VERSION_MINOR_PARMNUM: int
SV_TYPE_PARMNUM: int
SV_COMMENT_PARMNUM: int
SV_USERS_PARMNUM: int
SV_DISC_PARMNUM: int
SV_HIDDEN_PARMNUM: int
SV_ANNOUNCE_PARMNUM: int
SV_ANNDELTA_PARMNUM: int
SV_USERPATH_PARMNUM: int
SV_ALERTS_PARMNUM: int
SV_SECURITY_PARMNUM: int
SV_NUMADMIN_PARMNUM: int
SV_LANMASK_PARMNUM: int
SV_GUESTACC_PARMNUM: int
SV_CHDEVQ_PARMNUM: int
SV_CHDEVJOBS_PARMNUM: int
SV_CONNECTIONS_PARMNUM: int
SV_SHARES_PARMNUM: int
SV_OPENFILES_PARMNUM: int
SV_SESSREQS_PARMNUM: int
SV_ACTIVELOCKS_PARMNUM: int
SV_NUMREQBUF_PARMNUM: int
SV_NUMBIGBUF_PARMNUM: int
SV_NUMFILETASKS_PARMNUM: int
SV_ALERTSCHED_PARMNUM: int
SV_ERRORALERT_PARMNUM: int
SV_LOGONALERT_PARMNUM: int
SV_ACCESSALERT_PARMNUM: int
SV_DISKALERT_PARMNUM: int
SV_NETIOALERT_PARMNUM: int
SV_MAXAUDITSZ_PARMNUM: int
SV_SRVHEURISTICS_PARMNUM: int
SV_SESSOPENS_PARMNUM: int
SV_SESSVCS_PARMNUM: int
SV_OPENSEARCH_PARMNUM: int
SV_SIZREQBUF_PARMNUM: int
SV_INITWORKITEMS_PARMNUM: int
SV_MAXWORKITEMS_PARMNUM: int
SV_RAWWORKITEMS_PARMNUM: int
SV_IRPSTACKSIZE_PARMNUM: int
SV_MAXRAWBUFLEN_PARMNUM: int
SV_SESSUSERS_PARMNUM: int
SV_SESSCONNS_PARMNUM: int
SV_MAXNONPAGEDMEMORYUSAGE_PARMNUM: int
SV_MAXPAGEDMEMORYUSAGE_PARMNUM: int
SV_ENABLESOFTCOMPAT_PARMNUM: int
SV_ENABLEFORCEDLOGOFF_PARMNUM: int
SV_TIMESOURCE_PARMNUM: int
SV_ACCEPTDOWNLEVELAPIS_PARMNUM: int
SV_LMANNOUNCE_PARMNUM: int
SV_DOMAIN_PARMNUM: int
SV_MAXCOPYREADLEN_PARMNUM: int
SV_MAXCOPYWRITELEN_PARMNUM: int
SV_MINKEEPSEARCH_PARMNUM: int
SV_MAXKEEPSEARCH_PARMNUM: int
SV_MINKEEPCOMPLSEARCH_PARMNUM: int
SV_MAXKEEPCOMPLSEARCH_PARMNUM: int
SV_THREADCOUNTADD_PARMNUM: int
SV_NUMBLOCKTHREADS_PARMNUM: int
SV_SCAVTIMEOUT_PARMNUM: int
SV_MINRCVQUEUE_PARMNUM: int
SV_MINFREEWORKITEMS_PARMNUM: int
SV_XACTMEMSIZE_PARMNUM: int
SV_THREADPRIORITY_PARMNUM: int
SV_MAXMPXCT_PARMNUM: int
SV_OPLOCKBREAKWAIT_PARMNUM: int
SV_OPLOCKBREAKRESPONSEWAIT_PARMNUM: int
SV_ENABLEOPLOCKS_PARMNUM: int
SV_ENABLEOPLOCKFORCECLOSE_PARMNUM: int
SV_ENABLEFCBOPENS_PARMNUM: int
SV_ENABLERAW_PARMNUM: int
SV_ENABLESHAREDNETDRIVES_PARMNUM: int
SV_MINFREECONNECTIONS_PARMNUM: int
SV_MAXFREECONNECTIONS_PARMNUM: int
SV_INITSESSTABLE_PARMNUM: int
SV_INITCONNTABLE_PARMNUM: int
SV_INITFILETABLE_PARMNUM: int
SV_INITSEARCHTABLE_PARMNUM: int
SV_ALERTSCHEDULE_PARMNUM: int
SV_ERRORTHRESHOLD_PARMNUM: int
SV_NETWORKERRORTHRESHOLD_PARMNUM: int
SV_DISKSPACETHRESHOLD_PARMNUM: int
SV_MAXLINKDELAY_PARMNUM: int
SV_MINLINKTHROUGHPUT_PARMNUM: int
SV_LINKINFOVALIDTIME_PARMNUM: int
SV_SCAVQOSINFOUPDATETIME_PARMNUM: int
SV_MAXWORKITEMIDLETIME_PARMNUM: int
SV_MAXRAWWORKITEMS_PARMNUM: int
SV_PRODUCTTYPE_PARMNUM: int
SV_SERVERSIZE_PARMNUM: int
SV_CONNECTIONLESSAUTODISC_PARMNUM: int
SV_SHARINGVIOLATIONRETRIES_PARMNUM: int
SV_SHARINGVIOLATIONDELAY_PARMNUM: int
SV_MAXGLOBALOPENSEARCH_PARMNUM: int
SV_REMOVEDUPLICATESEARCHES_PARMNUM: int
SV_LOCKVIOLATIONRETRIES_PARMNUM: int
SV_LOCKVIOLATIONOFFSET_PARMNUM: int
SV_LOCKVIOLATIONDELAY_PARMNUM: int
SV_MDLREADSWITCHOVER_PARMNUM: int
SV_CACHEDOPENLIMIT_PARMNUM: int
SV_CRITICALTHREADS_PARMNUM: int
SV_RESTRICTNULLSESSACCESS_PARMNUM: int
SV_ENABLEWFW311DIRECTIPX_PARMNUM: int
SV_OTHERQUEUEAFFINITY_PARMNUM: int
SV_QUEUESAMPLESECS_PARMNUM: int
SV_BALANCECOUNT_PARMNUM: int
SV_PREFERREDAFFINITY_PARMNUM: int
SV_MAXFREERFCBS_PARMNUM: int
SV_MAXFREEMFCBS_PARMNUM: int
SV_MAXFREELFCBS_PARMNUM: int
SV_MAXFREEPAGEDPOOLCHUNKS_PARMNUM: int
SV_MINPAGEDPOOLCHUNKSIZE_PARMNUM: int
SV_MAXPAGEDPOOLCHUNKSIZE_PARMNUM: int
SV_SENDSFROMPREFERREDPROCESSOR_PARMNUM: int
SV_MAXTHREADSPERQUEUE_PARMNUM: int
SV_CACHEDDIRECTORYLIMIT_PARMNUM: int
SV_MAXCOPYLENGTH_PARMNUM: int
SV_ENABLEBULKTRANSFER_PARMNUM: int
SV_ENABLECOMPRESSION_PARMNUM: int
SV_AUTOSHAREWKS_PARMNUM: int
SV_AUTOSHARESERVER_PARMNUM: int
SV_ENABLESECURITYSIGNATURE_PARMNUM: int
SV_REQUIRESECURITYSIGNATURE_PARMNUM: int
SV_MINCLIENTBUFFERSIZE_PARMNUM: int
SV_CONNECTIONNOSESSIONSTIMEOUT_PARMNUM: int
SVI1_NUM_ELEMENTS: int
SVI2_NUM_ELEMENTS: int
SVI3_NUM_ELEMENTS: int
SW_AUTOPROF_LOAD_MASK: int
SW_AUTOPROF_SAVE_MASK: int
SV_MAX_SRV_HEUR_LEN: int
SV_USERS_PER_LICENSE: int
SVTI2_REMAP_PIPE_NAMES: int
SHARE_NETNAME_PARMNUM: int
SHARE_TYPE_PARMNUM: int
SHARE_REMARK_PARMNUM: int
SHARE_PERMISSIONS_PARMNUM: int
SHARE_MAX_USES_PARMNUM: int
SHARE_CURRENT_USES_PARMNUM: int
SHARE_PATH_PARMNUM: int
SHARE_PASSWD_PARMNUM: int
SHARE_FILE_SD_PARMNUM: int
SHI1_NUM_ELEMENTS: int
SHI2_NUM_ELEMENTS: int
STYPE_DISKTREE: int
STYPE_PRINTQ: int
STYPE_DEVICE: int
STYPE_IPC: int
STYPE_SPECIAL: int
SHI1005_FLAGS_DFS: int
SHI1005_FLAGS_DFS_ROOT: int
COW_PERMACHINE: int
COW_PERUSER: int
CSC_CACHEABLE: int
CSC_NOFLOWOPS: int
CSC_AUTO_INWARD: int
CSC_AUTO_OUTWARD: int
SHI1005_VALID_FLAGS_SET: int
SHI1007_VALID_FLAGS_SET: int
SESS_GUEST: int
SESS_NOENCRYPTION: int
SESI1_NUM_ELEMENTS: int
SESI2_NUM_ELEMENTS: int
PERM_FILE_READ: int
PERM_FILE_WRITE: int
PERM_FILE_CREATE: int
WNNC_NET_MSNET: int
WNNC_NET_LANMAN: int
WNNC_NET_NETWARE: int
WNNC_NET_VINES: int
WNNC_NET_10NET: int
WNNC_NET_LOCUS: int
WNNC_NET_SUN_PC_NFS: int
WNNC_NET_LANSTEP: int
WNNC_NET_9TILES: int
WNNC_NET_LANTASTIC: int
WNNC_NET_AS400: int
WNNC_NET_FTP_NFS: int
WNNC_NET_PATHWORKS: int
WNNC_NET_LIFENET: int
WNNC_NET_POWERLAN: int
WNNC_NET_BWNFS: int
WNNC_NET_COGENT: int
WNNC_NET_FARALLON: int
WNNC_NET_APPLETALK: int
WNNC_NET_INTERGRAPH: int
WNNC_NET_SYMFONET: int
WNNC_NET_CLEARCASE: int
WNNC_NET_FRONTIER: int
WNNC_NET_BMC: int
WNNC_NET_DCE: int
WNNC_NET_DECORB: int
WNNC_NET_PROTSTOR: int
WNNC_NET_FJ_REDIR: int
WNNC_NET_DISTINCT: int
WNNC_NET_TWINS: int
WNNC_NET_RDR2SAMPLE: int
RESOURCE_CONNECTED: int
RESOURCE_GLOBALNET: int
RESOURCE_REMEMBERED: int
RESOURCE_RECENT: int
RESOURCE_CONTEXT: int
RESOURCETYPE_ANY: int
RESOURCETYPE_DISK: int
RESOURCETYPE_PRINT: int
RESOURCETYPE_RESERVED: int
RESOURCETYPE_UNKNOWN: int
RESOURCEUSAGE_CONNECTABLE: int
RESOURCEUSAGE_CONTAINER: int
RESOURCEUSAGE_NOLOCALDEVICE: int
RESOURCEUSAGE_SIBLING: int
RESOURCEUSAGE_ATTACHED: int
RESOURCEUSAGE_ALL: int
RESOURCEUSAGE_RESERVED: int
RESOURCEDISPLAYTYPE_GENERIC: int
RESOURCEDISPLAYTYPE_DOMAIN: int
RESOURCEDISPLAYTYPE_SERVER: int
RESOURCEDISPLAYTYPE_SHARE: int
RESOURCEDISPLAYTYPE_FILE: int
RESOURCEDISPLAYTYPE_GROUP: int
RESOURCEDISPLAYTYPE_NETWORK: int
RESOURCEDISPLAYTYPE_ROOT: int
RESOURCEDISPLAYTYPE_SHAREADMIN: int
RESOURCEDISPLAYTYPE_DIRECTORY: int
RESOURCEDISPLAYTYPE_TREE: int
RESOURCEDISPLAYTYPE_NDSCONTAINER: int
NETPROPERTY_PERSISTENT: int
CONNECT_UPDATE_PROFILE: int
CONNECT_UPDATE_RECENT: int
CONNECT_TEMPORARY: int
CONNECT_INTERACTIVE: int
CONNECT_PROMPT: int
CONNECT_NEED_DRIVE: int
CONNECT_REFCOUNT: int
CONNECT_REDIRECT: int
CONNECT_LOCALDRIVE: int
CONNECT_CURRENT_MEDIA: int
CONNECT_DEFERRED: int
CONNECT_RESERVED: int
CONNDLG_RO_PATH: int
CONNDLG_CONN_POINT: int
CONNDLG_USE_MRU: int
CONNDLG_HIDE_BOX: int
CONNDLG_PERSIST: int
CONNDLG_NOT_PERSIST: int
DISC_UPDATE_PROFILE: int
DISC_NO_FORCE: int
UNIVERSAL_NAME_INFO_LEVEL: int
REMOTE_NAME_INFO_LEVEL: int
WNFMT_MULTILINE: int
WNFMT_ABBREVIATED: int
WNFMT_INENUM: int
WNFMT_CONNECTION: int
NETINFO_DLL16: int
NETINFO_DISKRED: int
NETINFO_PRINTERRED: int
RP_LOGON: int
RP_INIFILE: int
PP_DISPLAYERRORS: int
WNCON_FORNETCARD: int
WNCON_NOTROUTED: int
WNCON_SLOWLINK: int
WNCON_DYNAMIC: int
NetSetupUnknown: int
NetSetupMachine: int
NetSetupWorkgroup: int
NetSetupDomain: int
NetSetupNonExistentDomain: int
NetSetupDnsMachine: int
NetSetupUnknownStatus: int
NetSetupUnjoined: int
NetSetupWorkgroupName: int
NetSetupDomainName: int
NetValidateAuthentication: int
NetValidatePasswordChange: int
NetValidatePasswordReset: int
ACCESS_ACCESS_LIST_INFOLEVEL: int
ACCESS_ACCESS_LIST_PARMNUM: int
SV_ALIST_MTIME_PARMNUM: int
SV_GLIST_MTIME_PARMNUM: int
SV_TYPE_LOCAL_LIST_ONLY: int
SV_ULIST_MTIME_PARMNUM: int
