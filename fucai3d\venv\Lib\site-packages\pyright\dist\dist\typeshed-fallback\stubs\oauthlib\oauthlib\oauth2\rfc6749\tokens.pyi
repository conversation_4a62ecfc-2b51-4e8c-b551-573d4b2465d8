import datetime
from _typeshed import Incomplete
from collections.abc import Callable
from typing import Literal

from oauthlib.common import Request, _HTTPMethod
from oauthlib.oauth2.rfc6749.request_validator import RequestValidator

class OAuth2Token(dict[str, Incomplete]):
    def __init__(
        self, params: dict[str, Incomplete], old_scope: str | set[object] | tuple[object] | list[object] | None = None
    ) -> None: ...
    @property
    def scope_changed(self) -> bool: ...
    @property
    def old_scope(self) -> str | None: ...
    @property
    def old_scopes(self) -> list[str]: ...
    @property
    def scope(self) -> str | None: ...
    @property
    def scopes(self) -> list[str]: ...
    @property
    def missing_scopes(self) -> list[str]: ...
    @property
    def additional_scopes(self) -> list[str]: ...

def prepare_mac_header(
    token: str,
    uri: str,
    key: str | bytes | bytearray,
    http_method: _HTTPMethod,
    nonce: str | None = None,
    headers: dict[str, str] | None = None,
    body: str | None = None,
    ext: str = "",
    hash_algorithm: str = "hmac-sha-1",
    issue_time: datetime.datetime | None = None,
    draft: int = 0,
) -> dict[str, str]: ...
def prepare_bearer_uri(token: str, uri: str) -> str: ...
def prepare_bearer_headers(token: str, headers: dict[str, str] | None = None) -> dict[str, str]: ...
def prepare_bearer_body(token: str, body: str = "") -> str: ...
def random_token_generator(request: Request, refresh_token: bool = False) -> str: ...
def signed_token_generator(private_pem: str, **kwargs) -> Callable[[Request], str]: ...
def get_token_from_header(request: Request) -> str | None: ...

class TokenBase:
    def __call__(self, request: Request, refresh_token: bool = False) -> None: ...
    def validate_request(self, request: Request) -> bool: ...
    def estimate_type(self, request: Request) -> int: ...

class BearerToken(TokenBase):
    request_validator: RequestValidator | None
    token_generator: Callable[[Request], str]
    refresh_token_generator: Callable[[Request], str]
    expires_in: int | Callable[[Request], int]
    def __init__(
        self,
        request_validator: RequestValidator | None = None,
        token_generator: Callable[[Request], str] | None = None,
        expires_in: int | Callable[[Request], int] | None = None,
        refresh_token_generator: Callable[[Request], str] | None = None,
    ) -> None: ...
    def create_token(self, request: Request, refresh_token: bool = False, **kwargs) -> OAuth2Token: ...
    def validate_request(self, request: Request) -> bool: ...
    def estimate_type(self, request: Request) -> Literal[9, 5, 0]: ...
