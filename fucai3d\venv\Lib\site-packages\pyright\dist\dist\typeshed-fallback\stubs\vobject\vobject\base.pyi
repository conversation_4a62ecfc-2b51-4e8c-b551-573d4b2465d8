import logging
import re
from _typeshed import Incomplete, MaybeNone, SupportsWrite
from collections.abc import Generator, Iterator
from typing import Any, AnyStr, Final, Literal, TypeVar, overload
from typing_extensions import Self

_V = TypeVar("_V", bound=VBase)
_W = TypeVar("_W", bound=SupportsWrite[bytes])

VERSION: Final[str]

def to_unicode(value: str | bytes | bytearray) -> str: ...
def to_basestring(s: str | bytes) -> bytes: ...

logger: logging.Logger
DEBUG: bool
CR: str
LF: str
CRLF: str
SPACE: str
TAB: str
SPACEORTAB: str

class VBase:
    group: Incomplete | None
    behavior: Incomplete | None
    parentBehavior: Incomplete | None
    isNative: bool
    def __init__(self, group=None) -> None: ...
    def copy(self, copyit: VBase) -> None: ...
    def validate(self, *args, **kwds) -> bool: ...
    def getChildren(self) -> list[Incomplete]: ...
    def clearBehavior(self, cascade: bool = True) -> None: ...
    def autoBehavior(self, cascade: bool = False) -> None: ...
    def setBehavior(self, behavior, cascade: bool = True) -> None: ...
    def transformToNative(self): ...
    def transformFromNative(self): ...
    def transformChildrenToNative(self) -> None: ...
    def transformChildrenFromNative(self, clearBehavior: bool = True) -> None: ...
    # Use Any because args and kwargs are passed to the behavior object
    @overload
    def serialize(
        self, buf: None = None, lineLength: int = 75, validate: bool = True, behavior=None, *args: Any, **kwargs: Any
    ) -> str: ...
    @overload
    def serialize(self, buf: _W, lineLength: int = 75, validate: bool = True, behavior=None, *args: Any, **kwargs: Any) -> _W: ...

def toVName(name: str, stripNum: int = 0, upper: bool = False) -> str: ...

class ContentLine(VBase):
    name: str
    encoded: bool
    params: dict[Incomplete, list[Incomplete]]
    singletonparams: list[Incomplete]
    isNative: bool
    lineNumber: int | None
    value: str
    def __init__(
        self,
        name: str,
        params: dict[Incomplete, list[Incomplete]],
        value: str,
        group=None,
        encoded: bool = False,
        isNative: bool = False,
        lineNumber: int | None = None,
        *args,
        **kwds,
    ) -> None: ...
    @classmethod
    def duplicate(cls, copyit) -> Self: ...
    def copy(self, copyit) -> None: ...
    def __eq__(self, other): ...
    def __getattr__(self, name: str): ...
    def __setattr__(self, name: str, value) -> None: ...
    def __delattr__(self, name: str) -> None: ...
    def valueRepr(self) -> str: ...
    def __unicode__(self) -> str: ...
    def prettyPrint(self, level: int = 0, tabwidth: int = 3) -> None: ...

class Component(VBase):
    contents: dict[str, list[VBase]]
    name: str
    useBegin: bool
    def __init__(self, name: str | None = None, *args, **kwds) -> None: ...
    @classmethod
    def duplicate(cls, copyit) -> Self: ...
    def copy(self, copyit) -> None: ...
    def setProfile(self, name: str) -> None: ...
    def __getattr__(self, name: str): ...
    normal_attributes: list[str]
    def __setattr__(self, name: str, value) -> None: ...
    def __delattr__(self, name: str) -> None: ...
    def getChildValue(self, childName: str, default=None, childNumber: int = 0): ...
    @overload
    def add(self, objOrName: _V, group: str | None = None) -> _V: ...
    @overload
    def add(self, objOrName: Literal["vevent"], group: str | None = None) -> Component: ...
    @overload
    def add(
        self, objOrName: Literal["uid", "summary", "description", "dtstart", "dtend"], group: str | None = None
    ) -> ContentLine: ...
    @overload
    def add(self, objOrName: str, group: str | None = None) -> Any: ...  # returns VBase sub-class
    def remove(self, obj) -> None: ...
    def getChildren(self) -> list[Incomplete]: ...
    def components(self) -> Generator[Component]: ...
    def lines(self) -> Generator[ContentLine]: ...
    def sortChildKeys(self) -> list[Incomplete]: ...
    def getSortedChildren(self) -> list[Incomplete]: ...
    def setBehaviorFromVersionLine(self, versionLine) -> None: ...
    def transformChildrenToNative(self) -> None: ...
    def transformChildrenFromNative(self, clearBehavior: bool = True) -> None: ...
    def prettyPrint(self, level: int = 0, tabwidth: int = 3) -> None: ...

class VObjectError(Exception):
    msg: str
    lineNumber: int
    def __init__(self, msg: str, lineNumber: int | None = None) -> None: ...

class ParseError(VObjectError): ...
class ValidateError(VObjectError): ...
class NativeError(VObjectError): ...

patterns: dict[str, str]
param_values_re: re.Pattern[str]
params_re: re.Pattern[str]
line_re: re.Pattern[str]
begin_re: re.Pattern[str]

def parseParams(string: str) -> list[list[Any]]: ...  # Any was taken from re module stubs
def parseLine(
    line: str, lineNumber: int | None = None
) -> tuple[str, list[list[Any]], str | MaybeNone, str | MaybeNone]: ...  # Any is result of parseParams()

wrap_re: re.Pattern[str]
logical_lines_re: re.Pattern[str]
testLines: str

def getLogicalLines(fp, allowQP: bool = True) -> Generator[tuple[str, int]]: ...
def textLineToContentLine(text, n: int | None = None) -> ContentLine: ...
def dquoteEscape(param: str) -> str: ...
def foldOneLine(outbuf: SupportsWrite[AnyStr], input: AnyStr, lineLength: int = 75) -> None: ...
def defaultSerialize(obj: Component | ContentLine, buf, lineLength: int): ...

class Stack:
    stack: list[Incomplete]
    def __len__(self) -> int: ...
    def top(self): ...
    def topName(self): ...
    def modifyTop(self, item) -> None: ...
    def push(self, obj) -> None: ...
    def pop(self): ...

def readComponents(
    streamOrString, validate: bool = False, transform: bool = True, ignoreUnreadable: bool = False, allowQP: bool = False
) -> Iterator[Component]: ...
def readOne(stream, validate: bool = False, transform: bool = True, ignoreUnreadable: bool = False, allowQP: bool = False): ...
def registerBehavior(behavior, name: str | None = None, default: bool = False, id=None) -> None: ...
def getBehavior(name: str, id=None): ...
def newFromBehavior(name: str, id=None) -> Component | ContentLine: ...
def backslashEscape(s: str) -> str: ...
