STGC_DEFAULT: int
STGC_OVERWRITE: int
STGC_ONLYIFCURRENT: int
STGC_DANGEROUSLYCOMMITMERELYTODISKCACHE: int
STGC_CONSOLIDATE: int
STGTY_STORAGE: int
STGTY_STREAM: int
STGTY_LOCKBYTES: int
STGTY_PROPERTY: int
STREAM_SEEK_SET: int
STREAM_SEEK_CUR: int
STREAM_SEEK_END: int
LOCK_WRITE: int
LOCK_EXCLUSIVE: int
LOCK_ONLYONCE: int
CWCSTORAGENAME: int
STGM_DIRECT: int
STGM_TRANSACTED: int
STGM_SIMPLE: int
STGM_READ: int
STGM_WRITE: int
STGM_READWRITE: int
STGM_SHARE_DENY_NONE: int
STGM_SHARE_DENY_READ: int
STGM_SHARE_DENY_WRITE: int
STGM_SHARE_EXCLUSIVE: int
STGM_PRIORITY: int
STGM_DELETEONRELEASE: int
STGM_NOSCRATCH: int
STGM_CREATE: int
STGM_CONVERT: int
STGM_FAILIFTHERE: int
STGM_NOSNAPSHOT: int
ASYNC_MODE_COMPATIBILITY: int
ASYNC_MODE_DEFAULT: int
STGTY_REPEAT: int
STG_TOEND: int
STG_LAYOUT_SEQUENTIAL: int
STG_LAYOUT_INTERLEAVED: int
COM_RIGHTS_EXECUTE: int
COM_RIGHTS_EXECUTE_LOCAL: int
COM_RIGHTS_EXECUTE_REMOTE: int
COM_RIGHTS_ACTIVATE_LOCAL: int
COM_RIGHTS_ACTIVATE_REMOTE: int
STGFMT_DOCUMENT: int
STGFMT_STORAGE: int
STGFMT_NATIVE: int
STGFMT_FILE: int
STGFMT_ANY: int
STGFMT_DOCFILE: int
PID_DICTIONARY: int
PID_CODEPAGE: int
PID_FIRST_USABLE: int
PID_FIRST_NAME_DEFAULT: int
PID_LOCALE: int
PID_MODIFY_TIME: int
PID_SECURITY: int
PID_BEHAVIOR: int
PID_ILLEGAL: int
PID_MIN_READONLY: int
PID_MAX_READONLY: int
PIDDI_THUMBNAIL: int
PIDSI_TITLE: int
PIDSI_SUBJECT: int
PIDSI_AUTHOR: int
PIDSI_KEYWORDS: int
PIDSI_COMMENTS: int
PIDSI_TEMPLATE: int
PIDSI_LASTAUTHOR: int
PIDSI_REVNUMBER: int
PIDSI_EDITTIME: int
PIDSI_LASTPRINTED: int
PIDSI_CREATE_DTM: int
PIDSI_LASTSAVE_DTM: int
PIDSI_PAGECOUNT: int
PIDSI_WORDCOUNT: int
PIDSI_CHARCOUNT: int
PIDSI_THUMBNAIL: int
PIDSI_APPNAME: int
PIDSI_DOC_SECURITY: int
PIDDSI_CATEGORY: int
PIDDSI_PRESFORMAT: int
PIDDSI_BYTECOUNT: int
PIDDSI_LINECOUNT: int
PIDDSI_PARCOUNT: int
PIDDSI_SLIDECOUNT: int
PIDDSI_NOTECOUNT: int
PIDDSI_HIDDENCOUNT: int
PIDDSI_MMCLIPCOUNT: int
PIDDSI_SCALE: int
PIDDSI_HEADINGPAIR: int
PIDDSI_DOCPARTS: int
PIDDSI_MANAGER: int
PIDDSI_COMPANY: int
PIDDSI_LINKSDIRTY: int
PIDMSI_EDITOR: int
PIDMSI_SUPPLIER: int
PIDMSI_SOURCE: int
PIDMSI_SEQUENCE_NO: int
PIDMSI_PROJECT: int
PIDMSI_STATUS: int
PIDMSI_OWNER: int
PIDMSI_RATING: int
PIDMSI_PRODUCTION: int
PIDMSI_COPYRIGHT: int
PROPSETFLAG_DEFAULT: int
PROPSETFLAG_NONSIMPLE: int
PROPSETFLAG_ANSI: int
PROPSETFLAG_UNBUFFERED: int
PROPSETFLAG_CASE_SENSITIVE: int
STGMOVE_MOVE: int
STGMOVE_COPY: int
STGMOVE_SHALLOWCOPY: int
