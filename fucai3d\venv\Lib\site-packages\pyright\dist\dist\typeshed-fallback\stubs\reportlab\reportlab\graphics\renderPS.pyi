from _typeshed import Incomplete
from typing import IO, Final

from reportlab.graphics.renderbase import Renderer
from reportlab.graphics.shapes import Drawing
from reportlab.pdfgen.canvas import Canvas

__version__: Final[str]
PS_WinAnsiEncoding: Final[str]

class PSCanvas:
    comments: int
    code: Incomplete
    code_append: Incomplete
    PostScriptLevel: Incomplete
    def __init__(self, size=(300, 300), PostScriptLevel: int = 2) -> None: ...
    def comment(self, msg) -> None: ...
    def drawImage(self, image, x1, y1, width=None, height=None) -> None: ...
    def clear(self) -> None: ...
    def save(self, f=None) -> None: ...
    def saveState(self) -> None: ...
    def restoreState(self) -> None: ...
    def stringWidth(self, s, font=None, fontSize=None): ...
    def setLineCap(self, v) -> None: ...
    def setLineJoin(self, v) -> None: ...
    def setDash(self, array=[], phase: int = 0) -> None: ...
    def setStrokeColor(self, color) -> None: ...
    def setColor(self, color) -> None: ...
    def setFillColor(self, color) -> None: ...
    def setFillMode(self, v) -> None: ...
    def setLineWidth(self, width) -> None: ...
    def setFont(self, font, fontSize, leading=None) -> None: ...
    def line(self, x1, y1, x2, y2) -> None: ...
    def drawString(self, x, y, s, angle: int = 0, text_anchor: str = "left", textRenderMode: int = 0) -> None: ...
    def drawCentredString(self, x, y, text, text_anchor: str = "middle", textRenderMode: int = 0) -> None: ...
    def drawRightString(self, text, x, y, text_anchor: str = "end", textRenderMode: int = 0) -> None: ...
    def drawCurve(self, x1, y1, x2, y2, x3, y3, x4, y4, closed: int = 0) -> None: ...
    def rect(self, x1, y1, x2, y2, stroke: int = 1, fill: int = 1) -> None: ...
    def roundRect(self, x1, y1, x2, y2, rx: int = 8, ry: int = 8) -> None: ...
    def ellipse(self, x1, y1, x2, y2) -> None: ...
    def circle(self, xc, yc, r) -> None: ...
    def drawArc(self, x1, y1, x2, y2, startAng: int = 0, extent: int = 360, fromcenter: int = 0) -> None: ...
    def polygon(self, p, closed: int = 0, stroke: int = 1, fill: int = 1) -> None: ...
    def lines(self, lineList, color=None, width=None) -> None: ...
    def moveTo(self, x, y) -> None: ...
    def lineTo(self, x, y) -> None: ...
    def curveTo(self, x1, y1, x2, y2, x3, y3) -> None: ...
    def closePath(self) -> None: ...
    def polyLine(self, p) -> None: ...
    def drawFigure(self, partList, closed: int = 0) -> None: ...
    def translate(self, x, y) -> None: ...
    def scale(self, x, y) -> None: ...
    def transform(self, a, b, c, d, e, f) -> None: ...

def draw(drawing: Drawing, canvas: Canvas, x: float = 0, y: float = 0, showBoundary=0) -> None: ...

class _PSRenderer(Renderer):
    def drawNode(self, node) -> None: ...
    def drawRect(self, rect) -> None: ...
    def drawLine(self, line) -> None: ...
    def drawCircle(self, circle) -> None: ...
    def drawWedge(self, wedge) -> None: ...
    def drawPolyLine(self, p) -> None: ...
    def drawEllipse(self, ellipse) -> None: ...
    def drawPolygon(self, p) -> None: ...
    def drawString(self, stringObj) -> None: ...
    def drawPath(self, path, fillMode=None): ...
    def applyStateChanges(self, delta, newState) -> None: ...
    def drawImage(self, image) -> None: ...

def drawToFile(d: Drawing, fn: IO[bytes], showBoundary=0, **kwd) -> None: ...
def drawToString(d: Drawing, showBoundary=0) -> str: ...
def test(outDir: str = "epsout", shout: bool = False) -> None: ...
