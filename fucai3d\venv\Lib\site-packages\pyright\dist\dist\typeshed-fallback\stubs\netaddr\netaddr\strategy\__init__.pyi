from collections.abc import Iterable, Sequence

def bytes_to_bits() -> list[str]: ...

BYTES_TO_BITS: list[str]

def valid_words(words: Iterable[int], word_size: int, num_words: int) -> bool: ...
def int_to_words(int_val: int, word_size: int, num_words: int) -> tuple[int, ...]: ...
def words_to_int(words: Sequence[int], word_size: int, num_words: int) -> int: ...
def valid_bits(bits: str, width: int, word_sep: str = "") -> bool: ...
def bits_to_int(bits: str, width: int, word_sep: str = "") -> int: ...
def int_to_bits(int_val: int, word_size: int, num_words: int, word_sep: str = "") -> str: ...
def valid_bin(bin_val: str, width: int) -> bool: ...
def int_to_bin(int_val: int, width: int) -> str: ...
def bin_to_int(bin_val: str, width: int) -> int: ...
