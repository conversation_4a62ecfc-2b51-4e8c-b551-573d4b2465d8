from typing_extensions import <PERSON><PERSON><PERSON><PERSON>

_SixIntTuple: TypeAlias = tuple[int, int, int, int, int, int]

DELETE: int
READ_CONTROL: int
WRITE_DAC: int
WRITE_OWNER: int
SYNCHRONIZE: int
STANDARD_RIGHTS_REQUIRED: int
STANDARD_RIGHTS_READ: int
STANDARD_RIGHTS_WRITE: int
STANDARD_RIGHTS_EXECUTE: int
STANDARD_RIGHTS_ALL: int
SPECIFIC_RIGHTS_ALL: int
ACCESS_SYSTEM_SECURITY: int
MAXIMUM_ALLOWED: int
GENERIC_READ: int
GENERIC_WRITE: int
GENERIC_EXECUTE: int
GENERIC_ALL: int
FILE_READ_DATA: int
FILE_WRITE_DATA: int
FILE_ADD_FILE: int
FILE_APPEND_DATA: int
FILE_ADD_SUBDIRECTORY: int
FILE_CREATE_PIPE_INSTANCE: int
FILE_READ_EA: int
FILE_WRITE_EA: int
FILE_EXECUTE: int
FILE_TRAVERSE: int
FILE_DELETE_CHILD: int
FILE_READ_ATTRIBUTES: int
FILE_WRITE_ATTRIBUTES: int
FILE_ALL_ACCESS: int
FILE_GENERIC_READ: int
FILE_GENERIC_WRITE: int
FILE_GENERIC_EXECUTE: int
SECURITY_NULL_SID_AUTHORITY: _SixIntTuple
SECURITY_WORLD_SID_AUTHORITY: _SixIntTuple
SECURITY_LOCAL_SID_AUTHORITY: _SixIntTuple
SECURITY_CREATOR_SID_AUTHORITY: _SixIntTuple
SECURITY_NON_UNIQUE_AUTHORITY: _SixIntTuple
SECURITY_RESOURCE_MANAGER_AUTHORITY: _SixIntTuple
SECURITY_NULL_RID: int
SECURITY_WORLD_RID: int
SECURITY_LOCAL_RID: int
SECURITY_CREATOR_OWNER_RID: int
SECURITY_CREATOR_GROUP_RID: int
SECURITY_CREATOR_OWNER_SERVER_RID: int
SECURITY_CREATOR_GROUP_SERVER_RID: int
SECURITY_CREATOR_OWNER_RIGHTS_RID: int
SECURITY_NT_AUTHORITY: _SixIntTuple
SECURITY_DIALUP_RID: int
SECURITY_NETWORK_RID: int
SECURITY_BATCH_RID: int
SECURITY_INTERACTIVE_RID: int
SECURITY_SERVICE_RID: int
SECURITY_ANONYMOUS_LOGON_RID: int
SECURITY_PROXY_RID: int
SECURITY_SERVER_LOGON_RID: int
SECURITY_LOGON_IDS_RID: int
SECURITY_LOGON_IDS_RID_COUNT: int
SECURITY_LOCAL_SYSTEM_RID: int
SECURITY_NT_NON_UNIQUE: int
SECURITY_BUILTIN_DOMAIN_RID: int
DOMAIN_USER_RID_ADMIN: int
DOMAIN_USER_RID_GUEST: int
DOMAIN_USER_RID_KRBTGT: int
DOMAIN_USER_RID_MAX: int
DOMAIN_GROUP_RID_ADMINS: int
DOMAIN_GROUP_RID_USERS: int
DOMAIN_GROUP_RID_GUESTS: int
DOMAIN_GROUP_RID_COMPUTERS: int
DOMAIN_GROUP_RID_CONTROLLERS: int
DOMAIN_GROUP_RID_CERT_ADMINS: int
DOMAIN_GROUP_RID_SCHEMA_ADMINS: int
DOMAIN_GROUP_RID_ENTERPRISE_ADMINS: int
DOMAIN_GROUP_RID_POLICY_ADMINS: int
DOMAIN_GROUP_RID_READONLY_CONTROLLERS: int
DOMAIN_ALIAS_RID_ADMINS: int
DOMAIN_ALIAS_RID_USERS: int
DOMAIN_ALIAS_RID_GUESTS: int
DOMAIN_ALIAS_RID_POWER_USERS: int
DOMAIN_ALIAS_RID_ACCOUNT_OPS: int
DOMAIN_ALIAS_RID_SYSTEM_OPS: int
DOMAIN_ALIAS_RID_PRINT_OPS: int
DOMAIN_ALIAS_RID_BACKUP_OPS: int
DOMAIN_ALIAS_RID_REPLICATOR: int
DOMAIN_ALIAS_RID_RAS_SERVERS: int
DOMAIN_ALIAS_RID_PREW2KCOMPACCESS: int
DOMAIN_ALIAS_RID_REMOTE_DESKTOP_USERS: int
DOMAIN_ALIAS_RID_NETWORK_CONFIGURATION_OPS: int
DOMAIN_ALIAS_RID_INCOMING_FOREST_TRUST_BUILDERS: int
DOMAIN_ALIAS_RID_MONITORING_USERS: int
DOMAIN_ALIAS_RID_LOGGING_USERS: int
DOMAIN_ALIAS_RID_AUTHORIZATIONACCESS: int
DOMAIN_ALIAS_RID_TS_LICENSE_SERVERS: int
DOMAIN_ALIAS_RID_DCOM_USERS: int
DOMAIN_ALIAS_RID_IUSERS: int
DOMAIN_ALIAS_RID_CRYPTO_OPERATORS: int
DOMAIN_ALIAS_RID_CACHEABLE_PRINCIPALS_GROUP: int
DOMAIN_ALIAS_RID_NON_CACHEABLE_PRINCIPALS_GROUP: int
DOMAIN_ALIAS_RID_EVENT_LOG_READERS_GROUP: int
SECURITY_MANDATORY_LABEL_AUTHORITY: _SixIntTuple
SECURITY_MANDATORY_UNTRUSTED_RID: int
SECURITY_MANDATORY_LOW_RID: int
SECURITY_MANDATORY_MEDIUM_RID: int
SECURITY_MANDATORY_HIGH_RID: int
SECURITY_MANDATORY_SYSTEM_RID: int
SECURITY_MANDATORY_PROTECTED_PROCESS_RID: int
SECURITY_MANDATORY_MAXIMUM_USER_RID: int
SYSTEM_LUID: tuple[int, int]
ANONYMOUS_LOGON_LUID: tuple[int, int]
LOCALSERVICE_LUID: tuple[int, int]
NETWORKSERVICE_LUID: tuple[int, int]
IUSER_LUID: tuple[int, int]
SE_GROUP_MANDATORY: int
SE_GROUP_ENABLED_BY_DEFAULT: int
SE_GROUP_ENABLED: int
SE_GROUP_OWNER: int
SE_GROUP_USE_FOR_DENY_ONLY: int
SE_GROUP_INTEGRITY: int
SE_GROUP_INTEGRITY_ENABLED: int
SE_GROUP_RESOURCE: int
SE_GROUP_LOGON_ID: int
ACCESS_MIN_MS_ACE_TYPE: int
ACCESS_ALLOWED_ACE_TYPE: int
ACCESS_DENIED_ACE_TYPE: int
SYSTEM_AUDIT_ACE_TYPE: int
SYSTEM_ALARM_ACE_TYPE: int
ACCESS_MAX_MS_V2_ACE_TYPE: int
ACCESS_ALLOWED_COMPOUND_ACE_TYPE: int
ACCESS_MAX_MS_V3_ACE_TYPE: int
ACCESS_MIN_MS_OBJECT_ACE_TYPE: int
ACCESS_ALLOWED_OBJECT_ACE_TYPE: int
ACCESS_DENIED_OBJECT_ACE_TYPE: int
SYSTEM_AUDIT_OBJECT_ACE_TYPE: int
SYSTEM_ALARM_OBJECT_ACE_TYPE: int
ACCESS_MAX_MS_OBJECT_ACE_TYPE: int
ACCESS_MAX_MS_V4_ACE_TYPE: int
ACCESS_MAX_MS_ACE_TYPE: int
ACCESS_ALLOWED_CALLBACK_ACE_TYPE: int
ACCESS_DENIED_CALLBACK_ACE_TYPE: int
ACCESS_ALLOWED_CALLBACK_OBJECT_ACE_TYPE: int
ACCESS_DENIED_CALLBACK_OBJECT_ACE_TYPE: int
SYSTEM_AUDIT_CALLBACK_ACE_TYPE: int
SYSTEM_ALARM_CALLBACK_ACE_TYPE: int
SYSTEM_AUDIT_CALLBACK_OBJECT_ACE_TYPE: int
SYSTEM_ALARM_CALLBACK_OBJECT_ACE_TYPE: int
SYSTEM_MANDATORY_LABEL_ACE_TYPE: int
ACCESS_MAX_MS_V5_ACE_TYPE: int
OBJECT_INHERIT_ACE: int
CONTAINER_INHERIT_ACE: int
NO_PROPAGATE_INHERIT_ACE: int
INHERIT_ONLY_ACE: int
VALID_INHERIT_FLAGS: int
SUCCESSFUL_ACCESS_ACE_FLAG: int
FAILED_ACCESS_ACE_FLAG: int
SE_OWNER_DEFAULTED: int
SE_GROUP_DEFAULTED: int
SE_DACL_PRESENT: int
SE_DACL_DEFAULTED: int
SE_SACL_PRESENT: int
SE_SACL_DEFAULTED: int
SE_SELF_RELATIVE: int
SE_PRIVILEGE_ENABLED_BY_DEFAULT: int
SE_PRIVILEGE_ENABLED: int
SE_PRIVILEGE_USED_FOR_ACCESS: int
PRIVILEGE_SET_ALL_NECESSARY: int
SE_CREATE_TOKEN_NAME: str
SE_ASSIGNPRIMARYTOKEN_NAME: str
SE_LOCK_MEMORY_NAME: str
SE_INCREASE_QUOTA_NAME: str
SE_UNSOLICITED_INPUT_NAME: str
SE_MACHINE_ACCOUNT_NAME: str
SE_TCB_NAME: str
SE_SECURITY_NAME: str
SE_TAKE_OWNERSHIP_NAME: str
SE_LOAD_DRIVER_NAME: str
SE_SYSTEM_PROFILE_NAME: str
SE_SYSTEMTIME_NAME: str
SE_PROF_SINGLE_PROCESS_NAME: str
SE_INC_BASE_PRIORITY_NAME: str
SE_CREATE_PAGEFILE_NAME: str
SE_CREATE_PERMANENT_NAME: str
SE_BACKUP_NAME: str
SE_RESTORE_NAME: str
SE_SHUTDOWN_NAME: str
SE_DEBUG_NAME: str
SE_AUDIT_NAME: str
SE_SYSTEM_ENVIRONMENT_NAME: str
SE_CHANGE_NOTIFY_NAME: str
SE_REMOTE_SHUTDOWN_NAME: str
SecurityAnonymous: int
SecurityIdentification: int
SecurityImpersonation: int
SecurityDelegation: int
SECURITY_MAX_IMPERSONATION_LEVEL: int
DEFAULT_IMPERSONATION_LEVEL: int
TOKEN_ASSIGN_PRIMARY: int
TOKEN_DUPLICATE: int
TOKEN_IMPERSONATE: int
TOKEN_QUERY: int
TOKEN_QUERY_SOURCE: int
TOKEN_ADJUST_PRIVILEGES: int
TOKEN_ADJUST_GROUPS: int
TOKEN_ADJUST_DEFAULT: int
TOKEN_ALL_ACCESS: int
TOKEN_READ: int
TOKEN_WRITE: int
TOKEN_EXECUTE: int
SidTypeUser: int
SidTypeGroup: int
SidTypeDomain: int
SidTypeAlias: int
SidTypeWellKnownGroup: int
SidTypeDeletedAccount: int
SidTypeInvalid: int
SidTypeUnknown: int
SidTypeComputer: int
SidTypeLabel: int
TokenPrimary: int
TokenImpersonation: int
TokenUser: int
TokenGroups: int
TokenPrivileges: int
TokenOwner: int
TokenPrimaryGroup: int
TokenDefaultDacl: int
TokenSource: int
TokenType: int
TokenImpersonationLevel: int
TokenStatistics: int
TokenRestrictedSids: int
TokenSessionId: int
TokenGroupsAndPrivileges: int
TokenSessionReference: int
TokenSandBoxInert: int
TokenAuditPolicy: int
TokenOrigin: int
TokenElevationType: int
TokenLinkedToken: int
TokenElevation: int
TokenHasRestrictions: int
TokenAccessInformation: int
TokenVirtualizationAllowed: int
TokenVirtualizationEnabled: int
TokenIntegrityLevel: int
TokenUIAccess: int
TokenMandatoryPolicy: int
TokenLogonSid: int
DS_BEHAVIOR_WIN2000: int
DS_BEHAVIOR_WIN2003_WITH_MIXED_DOMAINS: int
DS_BEHAVIOR_WIN2003: int
DS_SYNCED_EVENT_NAME: str
ACTRL_DS_OPEN: int
ACTRL_DS_CREATE_CHILD: int
ACTRL_DS_DELETE_CHILD: int
ACTRL_DS_SELF: int
ACTRL_DS_READ_PROP: int
ACTRL_DS_WRITE_PROP: int
ACTRL_DS_DELETE_TREE: int
ACTRL_DS_CONTROL_ACCESS: int
NTDSAPI_BIND_ALLOW_DELEGATION: int
DS_REPSYNC_ASYNCHRONOUS_OPERATION: int
DS_REPSYNC_WRITEABLE: int
DS_REPSYNC_PERIODIC: int
DS_REPSYNC_INTERSITE_MESSAGING: int
DS_REPSYNC_ALL_SOURCES: int
DS_REPSYNC_FULL: int
DS_REPSYNC_URGENT: int
DS_REPSYNC_NO_DISCARD: int
DS_REPSYNC_FORCE: int
DS_REPSYNC_ADD_REFERENCE: int
DS_REPSYNC_NEVER_COMPLETED: int
DS_REPSYNC_TWO_WAY: int
DS_REPSYNC_NEVER_NOTIFY: int
DS_REPSYNC_INITIAL: int
DS_REPSYNC_USE_COMPRESSION: int
DS_REPSYNC_ABANDONED: int
DS_REPSYNC_INITIAL_IN_PROGRESS: int
DS_REPSYNC_PARTIAL_ATTRIBUTE_SET: int
DS_REPSYNC_REQUEUE: int
DS_REPSYNC_NOTIFICATION: int
DS_REPSYNC_ASYNCHRONOUS_REPLICA: int
DS_REPSYNC_CRITICAL: int
DS_REPSYNC_FULL_IN_PROGRESS: int
DS_REPSYNC_PREEMPTED: int
DS_REPADD_ASYNCHRONOUS_OPERATION: int
DS_REPADD_WRITEABLE: int
DS_REPADD_INITIAL: int
DS_REPADD_PERIODIC: int
DS_REPADD_INTERSITE_MESSAGING: int
DS_REPADD_ASYNCHRONOUS_REPLICA: int
DS_REPADD_DISABLE_NOTIFICATION: int
DS_REPADD_DISABLE_PERIODIC: int
DS_REPADD_USE_COMPRESSION: int
DS_REPADD_NEVER_NOTIFY: int
DS_REPADD_TWO_WAY: int
DS_REPADD_CRITICAL: int
DS_REPDEL_ASYNCHRONOUS_OPERATION: int
DS_REPDEL_WRITEABLE: int
DS_REPDEL_INTERSITE_MESSAGING: int
DS_REPDEL_IGNORE_ERRORS: int
DS_REPDEL_LOCAL_ONLY: int
DS_REPDEL_NO_SOURCE: int
DS_REPDEL_REF_OK: int
DS_REPMOD_ASYNCHRONOUS_OPERATION: int
DS_REPMOD_WRITEABLE: int
DS_REPMOD_UPDATE_FLAGS: int
DS_REPMOD_UPDATE_ADDRESS: int
DS_REPMOD_UPDATE_SCHEDULE: int
DS_REPMOD_UPDATE_RESULT: int
DS_REPMOD_UPDATE_TRANSPORT: int
DS_REPUPD_ASYNCHRONOUS_OPERATION: int
DS_REPUPD_WRITEABLE: int
DS_REPUPD_ADD_REFERENCE: int
DS_REPUPD_DELETE_REFERENCE: int
DS_INSTANCETYPE_IS_NC_HEAD: int
DS_INSTANCETYPE_NC_IS_WRITEABLE: int
DS_INSTANCETYPE_NC_COMING: int
DS_INSTANCETYPE_NC_GOING: int
NTDSDSA_OPT_IS_GC: int
NTDSDSA_OPT_DISABLE_INBOUND_REPL: int
NTDSDSA_OPT_DISABLE_OUTBOUND_REPL: int
NTDSDSA_OPT_DISABLE_NTDSCONN_XLATE: int
NTDSCONN_OPT_IS_GENERATED: int
NTDSCONN_OPT_TWOWAY_SYNC: int
NTDSCONN_OPT_OVERRIDE_NOTIFY_DEFAULT: int
NTDSCONN_OPT_USE_NOTIFY: int
NTDSCONN_OPT_DISABLE_INTERSITE_COMPRESSION: int
NTDSCONN_OPT_USER_OWNED_SCHEDULE: int
NTDSCONN_KCC_NO_REASON: int
NTDSCONN_KCC_GC_TOPOLOGY: int
NTDSCONN_KCC_RING_TOPOLOGY: int
NTDSCONN_KCC_MINIMIZE_HOPS_TOPOLOGY: int
NTDSCONN_KCC_STALE_SERVERS_TOPOLOGY: int
NTDSCONN_KCC_OSCILLATING_CONNECTION_TOPOLOGY: int
NTDSCONN_KCC_INTERSITE_GC_TOPOLOGY: int
NTDSCONN_KCC_INTERSITE_TOPOLOGY: int
NTDSCONN_KCC_SERVER_FAILOVER_TOPOLOGY: int
NTDSCONN_KCC_SITE_FAILOVER_TOPOLOGY: int
NTDSCONN_KCC_REDUNDANT_SERVER_TOPOLOGY: int
FRSCONN_PRIORITY_MASK: int
FRSCONN_MAX_PRIORITY: int
NTDSCONN_OPT_IGNORE_SCHEDULE_MASK: int
NTDSSETTINGS_OPT_IS_AUTO_TOPOLOGY_DISABLED: int
NTDSSETTINGS_OPT_IS_TOPL_CLEANUP_DISABLED: int
NTDSSETTINGS_OPT_IS_TOPL_MIN_HOPS_DISABLED: int
NTDSSETTINGS_OPT_IS_TOPL_DETECT_STALE_DISABLED: int
NTDSSETTINGS_OPT_IS_INTER_SITE_AUTO_TOPOLOGY_DISABLED: int
NTDSSETTINGS_OPT_IS_GROUP_CACHING_ENABLED: int
NTDSSETTINGS_OPT_FORCE_KCC_WHISTLER_BEHAVIOR: int
NTDSSETTINGS_OPT_FORCE_KCC_W2K_ELECTION: int
NTDSSETTINGS_OPT_IS_RAND_BH_SELECTION_DISABLED: int
NTDSSETTINGS_OPT_IS_SCHEDULE_HASHING_ENABLED: int
NTDSSETTINGS_OPT_IS_REDUNDANT_SERVER_TOPOLOGY_ENABLED: int
NTDSSETTINGS_DEFAULT_SERVER_REDUNDANCY: int
NTDSTRANSPORT_OPT_IGNORE_SCHEDULES: int
NTDSTRANSPORT_OPT_BRIDGES_REQUIRED: int
NTDSSITECONN_OPT_USE_NOTIFY: int
NTDSSITECONN_OPT_TWOWAY_SYNC: int
NTDSSITECONN_OPT_DISABLE_COMPRESSION: int
NTDSSITELINK_OPT_USE_NOTIFY: int
NTDSSITELINK_OPT_TWOWAY_SYNC: int
NTDSSITELINK_OPT_DISABLE_COMPRESSION: int
GUID_USERS_CONTAINER_A: str
GUID_COMPUTRS_CONTAINER_A: str
GUID_SYSTEMS_CONTAINER_A: str
GUID_DOMAIN_CONTROLLERS_CONTAINER_A: str
GUID_INFRASTRUCTURE_CONTAINER_A: str
GUID_DELETED_OBJECTS_CONTAINER_A: str
GUID_LOSTANDFOUND_CONTAINER_A: str
GUID_FOREIGNSECURITYPRINCIPALS_CONTAINER_A: str
GUID_PROGRAM_DATA_CONTAINER_A: str
GUID_MICROSOFT_PROGRAM_DATA_CONTAINER_A: str
GUID_NTDS_QUOTAS_CONTAINER_A: str
GUID_USERS_CONTAINER_BYTE: str
GUID_COMPUTRS_CONTAINER_BYTE: str
GUID_SYSTEMS_CONTAINER_BYTE: str
GUID_DOMAIN_CONTROLLERS_CONTAINER_BYTE: str
GUID_INFRASTRUCTURE_CONTAINER_BYTE: str
GUID_DELETED_OBJECTS_CONTAINER_BYTE: str
GUID_LOSTANDFOUND_CONTAINER_BYTE: str
GUID_FOREIGNSECURITYPRINCIPALS_CONTAINER_BYTE: str
GUID_PROGRAM_DATA_CONTAINER_BYTE: str
GUID_MICROSOFT_PROGRAM_DATA_CONTAINER_BYTE: str
GUID_NTDS_QUOTAS_CONTAINER_BYTE: str
DS_REPSYNCALL_NO_OPTIONS: int
DS_REPSYNCALL_ABORT_IF_SERVER_UNAVAILABLE: int
DS_REPSYNCALL_SYNC_ADJACENT_SERVERS_ONLY: int
DS_REPSYNCALL_ID_SERVERS_BY_DN: int
DS_REPSYNCALL_DO_NOT_SYNC: int
DS_REPSYNCALL_SKIP_INITIAL_CHECK: int
DS_REPSYNCALL_PUSH_CHANGES_OUTWARD: int
DS_REPSYNCALL_CROSS_SITE_BOUNDARIES: int
DS_ROLE_SCHEMA_OWNER: int
DS_ROLE_DOMAIN_OWNER: int
DS_ROLE_PDC_OWNER: int
DS_ROLE_RID_OWNER: int
DS_ROLE_INFRASTRUCTURE_OWNER: int
DS_SCHEMA_GUID_NOT_FOUND: int
DS_SCHEMA_GUID_ATTR: int
DS_SCHEMA_GUID_ATTR_SET: int
DS_SCHEMA_GUID_CLASS: int
DS_SCHEMA_GUID_CONTROL_RIGHT: int
DS_KCC_FLAG_ASYNC_OP: int
DS_KCC_FLAG_DAMPED: int
DS_EXIST_ADVISORY_MODE: int
DS_REPL_INFO_FLAG_IMPROVE_LINKED_ATTRS: int
DS_REPL_NBR_WRITEABLE: int
DS_REPL_NBR_SYNC_ON_STARTUP: int
DS_REPL_NBR_DO_SCHEDULED_SYNCS: int
DS_REPL_NBR_USE_ASYNC_INTERSITE_TRANSPORT: int
DS_REPL_NBR_TWO_WAY_SYNC: int
DS_REPL_NBR_RETURN_OBJECT_PARENTS: int
DS_REPL_NBR_FULL_SYNC_IN_PROGRESS: int
DS_REPL_NBR_FULL_SYNC_NEXT_PACKET: int
DS_REPL_NBR_NEVER_SYNCED: int
DS_REPL_NBR_PREEMPTED: int
DS_REPL_NBR_IGNORE_CHANGE_NOTIFICATIONS: int
DS_REPL_NBR_DISABLE_SCHEDULED_SYNC: int
DS_REPL_NBR_COMPRESS_CHANGES: int
DS_REPL_NBR_NO_CHANGE_NOTIFICATIONS: int
DS_REPL_NBR_PARTIAL_ATTRIBUTE_SET: int
DS_REPL_NBR_MODIFIABLE_MASK: int
DS_UNKNOWN_NAME: int
DS_FQDN_1779_NAME: int
DS_NT4_ACCOUNT_NAME: int
DS_DISPLAY_NAME: int
DS_UNIQUE_ID_NAME: int
DS_CANONICAL_NAME: int
DS_USER_PRINCIPAL_NAME: int
DS_CANONICAL_NAME_EX: int
DS_SERVICE_PRINCIPAL_NAME: int
DS_SID_OR_SID_HISTORY_NAME: int
DS_DNS_DOMAIN_NAME: int
DS_DOMAIN_SIMPLE_NAME: int
DS_ENTERPRISE_SIMPLE_NAME: int
DS_NAME_NO_FLAGS: int
DS_NAME_FLAG_SYNTACTICAL_ONLY: int
DS_NAME_FLAG_EVAL_AT_DC: int
DS_NAME_FLAG_GCVERIFY: int
DS_NAME_FLAG_TRUST_REFERRAL: int
DS_NAME_NO_ERROR: int
DS_NAME_ERROR_RESOLVING: int
DS_NAME_ERROR_NOT_FOUND: int
DS_NAME_ERROR_NOT_UNIQUE: int
DS_NAME_ERROR_NO_MAPPING: int
DS_NAME_ERROR_DOMAIN_ONLY: int
DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING: int
DS_NAME_ERROR_TRUST_REFERRAL: int
DS_SPN_DNS_HOST: int
DS_SPN_DN_HOST: int
DS_SPN_NB_HOST: int
DS_SPN_DOMAIN: int
DS_SPN_NB_DOMAIN: int
DS_SPN_SERVICE: int
DS_SPN_ADD_SPN_OP: int
DS_SPN_REPLACE_SPN_OP: int
DS_SPN_DELETE_SPN_OP: int
DS_FORCE_REDISCOVERY: int
DS_DIRECTORY_SERVICE_REQUIRED: int
DS_DIRECTORY_SERVICE_PREFERRED: int
DS_GC_SERVER_REQUIRED: int
DS_PDC_REQUIRED: int
DS_BACKGROUND_ONLY: int
DS_IP_REQUIRED: int
DS_KDC_REQUIRED: int
DS_TIMESERV_REQUIRED: int
DS_WRITABLE_REQUIRED: int
DS_GOOD_TIMESERV_PREFERRED: int
DS_AVOID_SELF: int
DS_ONLY_LDAP_NEEDED: int
DS_IS_FLAT_NAME: int
DS_IS_DNS_NAME: int
DS_RETURN_DNS_NAME: int
DS_RETURN_FLAT_NAME: int
DSGETDC_VALID_FLAGS: int
DS_INET_ADDRESS: int
DS_NETBIOS_ADDRESS: int
DS_PDC_FLAG: int
DS_GC_FLAG: int
DS_LDAP_FLAG: int
DS_DS_FLAG: int
DS_KDC_FLAG: int
DS_TIMESERV_FLAG: int
DS_CLOSEST_FLAG: int
DS_WRITABLE_FLAG: int
DS_GOOD_TIMESERV_FLAG: int
DS_NDNC_FLAG: int
DS_PING_FLAGS: int
DS_DNS_CONTROLLER_FLAG: int
DS_DNS_DOMAIN_FLAG: int
DS_DNS_FOREST_FLAG: int
DS_DOMAIN_IN_FOREST: int
DS_DOMAIN_DIRECT_OUTBOUND: int
DS_DOMAIN_TREE_ROOT: int
DS_DOMAIN_PRIMARY: int
DS_DOMAIN_NATIVE_MODE: int
DS_DOMAIN_DIRECT_INBOUND: int
DS_DOMAIN_VALID_FLAGS: int
DS_GFTI_UPDATE_TDO: int
DS_GFTI_VALID_FLAGS: int
DS_ONLY_DO_SITE_NAME: int
DS_NOTIFY_AFTER_SITE_RECORDS: int
DS_OPEN_VALID_OPTION_FLAGS: int
DS_OPEN_VALID_FLAGS: int
SI_EDIT_PERMS: int
SI_EDIT_OWNER: int
SI_EDIT_AUDITS: int
SI_CONTAINER: int
SI_READONLY: int
SI_ADVANCED: int
SI_RESET: int
SI_OWNER_READONLY: int
SI_EDIT_PROPERTIES: int
SI_OWNER_RECURSE: int
SI_NO_ACL_PROTECT: int
SI_NO_TREE_APPLY: int
SI_PAGE_TITLE: int
SI_SERVER_IS_DC: int
SI_RESET_DACL_TREE: int
SI_RESET_SACL_TREE: int
SI_OBJECT_GUID: int
SI_EDIT_EFFECTIVE: int
SI_RESET_DACL: int
SI_RESET_SACL: int
SI_RESET_OWNER: int
SI_NO_ADDITIONAL_PERMISSION: int
SI_MAY_WRITE: int
SI_EDIT_ALL: int
SI_AUDITS_ELEVATION_REQUIRED: int
SI_VIEW_ONLY: int
SI_OWNER_ELEVATION_REQUIRED: int
SI_PERMS_ELEVATION_REQUIRED: int
SI_ACCESS_SPECIFIC: int
SI_ACCESS_GENERAL: int
SI_ACCESS_CONTAINER: int
SI_ACCESS_PROPERTY: int
SI_PAGE_PERM: int
SI_PAGE_ADVPERM: int
SI_PAGE_AUDIT: int
SI_PAGE_OWNER: int
SI_PAGE_EFFECTIVE: int
PSPCB_SI_INITDIALOG: int
ACTRL_DS_LIST: int
ACTRL_DS_LIST_OBJECT: int
CFSTR_ACLUI_SID_INFO_LIST: str
DS_LIST_ACCOUNT_OBJECT_FOR_SERVER: int
DS_LIST_DNS_HOST_NAME_FOR_SERVER: int
DS_LIST_DSA_OBJECT_FOR_SERVER: int
FILE_LIST_DIRECTORY: int
