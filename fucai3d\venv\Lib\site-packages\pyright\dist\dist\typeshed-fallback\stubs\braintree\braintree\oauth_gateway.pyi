from _typeshed import Incomplete

from braintree.error_result import ErrorResult
from braintree.successful_result import SuccessfulResult

class OAuthGateway:
    gateway: Incomplete
    config: Incomplete
    def __init__(self, gateway) -> None: ...
    def create_token_from_code(self, params: dict[str, Incomplete]) -> SuccessfulResult | ErrorResult: ...
    def create_token_from_refresh_token(self, params: dict[str, Incomplete]) -> SuccessfulResult | ErrorResult: ...
    def revoke_access_token(self, access_token: str) -> type[SuccessfulResult] | ErrorResult: ...
    def connect_url(self, raw_params: dict[str, Incomplete]) -> str: ...
