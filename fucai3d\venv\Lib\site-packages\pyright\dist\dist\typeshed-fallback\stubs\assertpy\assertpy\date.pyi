from datetime import date
from typing_extensions import Self

__tracebackhide__: bool

class DateMixin:
    def is_before(self, other: date) -> Self: ...
    def is_after(self, other: date) -> Self: ...
    def is_equal_to_ignoring_milliseconds(self, other: date) -> Self: ...
    def is_equal_to_ignoring_seconds(self, other: date) -> Self: ...
    def is_equal_to_ignoring_time(self, other: date) -> Self: ...
