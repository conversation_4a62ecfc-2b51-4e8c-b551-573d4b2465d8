from _typeshed import <PERSON>Des<PERSON><PERSON><PERSON>r<PERSON><PERSON>, ReadableBuffer
from typing import IO

from paramiko.message import Message
from paramiko.pkey import PKey

class Ed25519Key(PKey):
    public_blob: None
    def __init__(
        self,
        msg: Message | None = None,
        data: ReadableBuffer | None = None,
        filename: FileDescriptorOrPath | None = None,
        password: str | None = None,
        file_obj: IO[str] | None = None,
    ) -> None: ...
    def asbytes(self) -> bytes: ...
    def __hash__(self) -> int: ...
    def get_name(self) -> str: ...
    def get_bits(self) -> int: ...
    def can_sign(self) -> bool: ...
    def sign_ssh_data(self, data: bytes, algorithm: str | None = None) -> Message: ...
    def verify_ssh_sig(self, data: bytes, msg: Message) -> bool: ...
