from collections.abc import Generator
from typing import NamedTuple

from .violation import Violation

class Statistics:
    def __init__(self) -> None: ...
    def error_codes(self) -> list[str]: ...
    def record(self, error: Violation) -> None: ...
    def statistics_for(self, prefix: str, filename: str | None = None) -> Generator[Statistic, None, None]: ...

class Key(NamedTuple):
    filename: str
    code: str
    @classmethod
    def create_from(cls, error: Violation) -> Key: ...
    def matches(self, prefix: str, filename: str | None) -> bool: ...

class Statistic:
    error_code: str
    filename: str
    message: str
    count: int
    def __init__(self, error_code: str, filename: str, message: str, count: int) -> None: ...
    @classmethod
    def create_from(cls, error: Violation) -> Statistic: ...
    def increment(self) -> None: ...
