import unittest
from _typeshed import Incomplete
from configparser import Config<PERSON>arser
from typing import Final, Literal

__version__: Final[str]

def haveRenderPM(): ...
def isWritable(D): ...

RL_HOME: Incomplete
testsFolder: Incomplete

DEJAVUSANS: tuple[
    Literal["DejaVuSans"], Literal["DejaVuSans-Bold"], Literal["DejaVuSans-Oblique"], Literal["DejaVuSans-BoldOblique"]
] = ...

def haveDejaVu() -> bool: ...
def setOutDir(name): ...
def mockUrlRead(name): ...
def outputfile(fn): ...
def printLocation(depth: int = 1) -> None: ...
def makeSuiteForClasses(*classes, testMethodPrefix=None): ...
def getCVSEntries(folder, files: int = 1, folders: int = 0): ...

class ExtConfigParser(ConfigParser):
    pat: Incomplete
    def getstringlist(self, section, option): ...

class GlobDirectoryWalker:
    index: int
    pattern: Incomplete
    stack: Incomplete
    files: Incomplete
    directory: Incomplete
    def __init__(self, directory, pattern: str = "*") -> None: ...
    def __getitem__(self, index): ...
    def filterFiles(self, folder, files): ...

class RestrictedGlobDirectoryWalker(GlobDirectoryWalker):
    ignorePatterns: Incomplete
    def __init__(self, directory, pattern: str = "*", ignore=None) -> None: ...
    def filterFiles(self, folder, files): ...

class CVSGlobDirectoryWalker(GlobDirectoryWalker):
    def filterFiles(self, folder, files): ...

class SecureTestCase(unittest.TestCase):
    def setUp(self) -> None: ...
    def tearDown(self) -> None: ...

class NearTestCase(unittest.TestCase):
    @staticmethod
    def assertNear(a, b, accuracy: float = 1e-05) -> None: ...

class ScriptThatMakesFileTest(unittest.TestCase):
    scriptDir: Incomplete
    scriptName: Incomplete
    outFileName: Incomplete
    verbose: Incomplete
    def __init__(self, scriptDir, scriptName, outFileName, verbose: int = 0) -> None: ...
    cwd: Incomplete
    def setUp(self) -> None: ...
    def tearDown(self) -> None: ...
    def runTest(self) -> None: ...

def equalStrings(a, b, enc: str = "utf8"): ...
def eqCheck(r, x) -> None: ...
def rlextraNeeded(): ...
def rlSkipIf(cond, reason, __module__=None): ...
def rlSkipUnless(cond, reason, __module__=None): ...
def rlSkip(reason, __module__=None): ...
