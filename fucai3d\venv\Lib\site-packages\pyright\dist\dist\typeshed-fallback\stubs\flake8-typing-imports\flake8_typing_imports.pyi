import argparse
import ast
from collections.abc import Generator
from typing import Any, Final, NamedTuple
from typing_extensions import Self

class Version(NamedTuple):
    major: int = 0
    minor: int = 0
    patch: int = 0
    @classmethod
    def parse(cls, s: str) -> Self: ...

SYMBOLS: Final[list[tuple[Version, frozenset[str]]]]
VERSIONS: Final[frozenset[Version]]

class Visitor(ast.NodeVisitor):
    imports: dict[str, list[tuple[int, int]]]
    attributes: dict[str, list[tuple[int, int]]]
    defined_overload: bool
    unions_pattern_or_match: list[tuple[int, int]]
    from_imported_names: set[str]
    namedtuple_methods: list[tuple[int, int]]
    namedtuple_defaults: list[tuple[int, int]]
    def __init__(self) -> None: ...
    def visit_ImportFrom(self, node: ast.ImportFrom) -> None: ...
    def visit_Attribute(self, node: ast.Attribute) -> None: ...
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None: ...
    def visit_Subscript(self, node: ast.Subscript) -> None: ...
    def visit_ClassDef(self, node: ast.ClassDef) -> None: ...
    def visit_AnnAssign(self, node: ast.AnnAssign) -> None: ...
    def generic_visit(self, node: ast.AST) -> None: ...

class Plugin:
    @staticmethod
    def add_options(option_manager: Any) -> None: ...
    @classmethod
    def parse_options(cls, options: argparse.Namespace) -> None: ...
    def __init__(self, tree: ast.AST) -> None: ...
    def run(self) -> Generator[tuple[int, int, str, type[Any]]]: ...
