from opentracing.span import Span

from ..tracer import Tracer

class APICompatibilityCheckMixin:
    def tracer(self) -> Tracer: ...
    def check_baggage_values(self) -> bool: ...
    def check_scope_manager(self) -> bool: ...
    def is_parent(self, parent: Span, span: Span) -> bool: ...
    def test_active_span(self) -> None: ...
    def test_start_active_span(self) -> None: ...
    def test_start_active_span_parent(self) -> None: ...
    def test_start_active_span_ignore_active_span(self) -> None: ...
    def test_start_active_span_not_finish_on_close(self) -> None: ...
    def test_start_active_span_finish_on_close(self) -> None: ...
    def test_start_active_span_default_finish_on_close(self) -> None: ...
    def test_start_span(self) -> None: ...
    def test_start_span_propagation(self) -> None: ...
    def test_start_span_propagation_ignore_active_span(self) -> None: ...
    def test_start_span_with_parent(self) -> None: ...
    def test_start_child_span(self) -> None: ...
    def test_set_operation_name(self) -> None: ...
    def test_span_as_context_manager(self) -> None: ...
    def test_span_tag_value_types(self) -> None: ...
    def test_span_tags_with_chaining(self) -> None: ...
    def test_span_logs(self) -> None: ...
    def test_span_baggage(self) -> None: ...
    def test_context_baggage(self) -> None: ...
    def test_text_propagation(self) -> None: ...
    def test_binary_propagation(self) -> None: ...
    def test_mandatory_formats(self) -> None: ...
    def test_unknown_format(self) -> None: ...
    def test_tracer_start_active_span_scope(self) -> None: ...
    def test_tracer_start_span_scope(self) -> None: ...
