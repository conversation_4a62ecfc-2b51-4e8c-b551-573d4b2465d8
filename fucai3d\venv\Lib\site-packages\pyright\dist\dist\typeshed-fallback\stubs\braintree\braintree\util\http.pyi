from _typeshed import Incomplete
from typing import Final

from braintree.configuration import Configuration
from braintree.environment import Environment

class Http:
    class ContentType:
        Xml: Final = "application/xml"
        Multipart: Final = "multipart/form-data"
        Json: Final = "application/json"

    @staticmethod
    def is_error_status(status: int) -> bool: ...
    @staticmethod
    def raise_exception_from_status(status: int, message: str | None = None) -> None: ...
    config: Configuration
    environment: Environment
    def __init__(self, config: Configuration, environment: Environment | None = None) -> None: ...
    def post(self, path: str, params: dict[str, Incomplete] | None = None): ...
    def delete(self, path: str): ...
    def get(self, path: str): ...
    def put(self, path: str, params: dict[str, Incomplete] | None = None): ...
    def post_multipart(self, path: str, files, params: dict[str, Incomplete] | None = None): ...
    def http_do(
        self, http_verb: str, path: str, headers: dict[str, Incomplete], request_body: str | tuple[str, Incomplete] | None
    ) -> list[int | str]: ...
    def handle_exception(self, exception: IOError) -> None: ...
