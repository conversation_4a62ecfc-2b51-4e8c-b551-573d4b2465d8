from grpc_channelz.v1 import channelz_pb2, channelz_pb2_grpc

class ChannelzServicer(channelz_pb2_grpc.ChannelzServicer):
    @staticmethod
    async def GetTopChannels(request: channelz_pb2.GetTopChannelsRequest, context) -> channelz_pb2.GetTopChannelsResponse: ...
    @staticmethod
    async def GetServers(request: channelz_pb2.GetServersRequest, context) -> channelz_pb2.GetServersResponse: ...
    @staticmethod
    async def GetServer(request: channelz_pb2.GetServerRequest, context) -> channelz_pb2.GetServerResponse: ...
    @staticmethod
    async def GetServerSockets(
        request: channelz_pb2.GetServerSocketsRequest, context
    ) -> channelz_pb2.GetServerSocketsResponse: ...
    @staticmethod
    async def GetChannel(request: channelz_pb2.GetChannelRequest, context) -> channelz_pb2.GetChannelResponse: ...
    @staticmethod
    async def GetSubchannel(request: channelz_pb2.GetSubchannelRequest, context) -> channelz_pb2.GetSubchannelResponse: ...
    @staticmethod
    async def GetSocket(request: channelz_pb2.GetSocketRequest, context) -> channelz_pb2.GetSocketResponse: ...
