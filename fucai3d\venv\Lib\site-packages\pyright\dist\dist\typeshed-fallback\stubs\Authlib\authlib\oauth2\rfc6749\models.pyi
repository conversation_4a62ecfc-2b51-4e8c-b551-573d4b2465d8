from collections.abc import Collection

class ClientMixin:
    def get_client_id(self) -> str: ...
    def get_default_redirect_uri(self) -> str: ...
    def get_allowed_scope(self, scope: Collection[str] | str) -> str: ...
    def check_redirect_uri(self, redirect_uri: str) -> bool: ...
    def check_client_secret(self, client_secret: str) -> bool: ...
    def check_endpoint_auth_method(self, method: str, endpoint: str) -> bool: ...
    def check_response_type(self, response_type: str) -> bool: ...
    def check_grant_type(self, grant_type: str) -> bool: ...

class AuthorizationCodeMixin:
    def get_redirect_uri(self) -> str: ...
    def get_scope(self) -> str: ...

class TokenMixin:
    def check_client(self, client) -> bool: ...
    def get_scope(self) -> str: ...
    def get_expires_in(self) -> int: ...
    def is_expired(self) -> bool: ...
    def is_revoked(self) -> bool: ...
    def get_user(self): ...
    def get_client(self) -> ClientMixin: ...
