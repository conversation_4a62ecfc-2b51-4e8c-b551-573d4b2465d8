from _typeshed import ReadableBuffer

from serial.serialutil import SerialBase

class Serial(SerialBase):
    def open(self) -> None: ...
    def from_url(self, url: str) -> bytes: ...
    @property
    def in_waiting(self) -> int: ...
    def reset_input_buffer(self) -> None: ...
    def reset_output_buffer(self) -> None: ...
    def read(self, size: int = 1) -> bytes: ...
    def write(self, b: ReadableBuffer, /) -> int | None: ...
