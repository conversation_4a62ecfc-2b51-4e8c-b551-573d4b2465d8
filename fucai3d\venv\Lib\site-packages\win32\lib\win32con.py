# Generated by h2py from commdlg.h (plus modifications 4jan98)
WINVER = 1280
WM_USER = 1024
PY_0U = 0
OFN_READONLY = 1
OFN_OVERWRITEPROMPT = 2
OFN_HIDEREADONLY = 4
OFN_NOCHANGEDIR = 8
OFN_SHOWHELP = 16
OFN_ENABLEHOOK = 32
OFN_ENABLETEMPLATE = 64
OFN_ENABLETEMPLATEHANDLE = 128
OFN_NOVALIDATE = 256
OFN_ALLOWMULTISELECT = 512
OFN_EXTENSIONDIFFERENT = 1024
OFN_PATHMUSTEXIST = 2048
OFN_FILEMUSTEXIST = 4096
OFN_CREATEPROMPT = 8192
OFN_SHAREAWARE = 16384
OFN_NOREADONLYRETURN = 32768
OFN_NOTESTFILECREATE = 65536
OFN_NONETWORKBUTTON = 131072
OFN_NOLONGNAMES = 262144
OFN_EXPLORER = 524288  # new look commdlg
OFN_NODEREFERENCELINKS = 1048576
OFN_LONGNAMES = 2097152  # force long names for Python 3 modules
OFN_ENABLEINCLUDENOTIFY = 4194304  # send include message to callback
OFN_ENABLESIZING = 8388608
OFN_DONTADDTORECENT = 33554432
OFN_FORCESHOWHIDDEN = *********  # Show All files including System and hidden files
OFN_EX_NOPLACESBAR = 1
OFN_SHAREFALLTHROUGH = 2
OFN_SHARENOWARN = 1
OFN_SHAREWARN = 0
CDN_FIRST = PY_0U - 601
CDN_LAST = PY_0U - 699
CDN_INITDONE = CDN_FIRST - 0
CDN_SELCHANGE = CDN_FIRST - 1
CDN_FOLDERCHANGE = CDN_FIRST - 2
CDN_SHAREVIOLATION = CDN_FIRST - 3
CDN_HELP = CDN_FIRST - 4
CDN_FILEOK = CDN_FIRST - 5
CDN_TYPECHANGE = CDN_FIRST - 6
CDN_INCLUDEITEM = CDN_FIRST - 7
CDM_FIRST = WM_USER + 100
CDM_LAST = WM_USER + 200
CDM_GETSPEC = CDM_FIRST + 0
CDM_GETFILEPATH = CDM_FIRST + 1
CDM_GETFOLDERPATH = CDM_FIRST + 2
CDM_GETFOLDERIDLIST = CDM_FIRST + 3
CDM_SETCONTROLTEXT = CDM_FIRST + 4
CDM_HIDECONTROL = CDM_FIRST + 5
CDM_SETDEFEXT = CDM_FIRST + 6
CC_RGBINIT = 1
CC_FULLOPEN = 2
CC_PREVENTFULLOPEN = 4
CC_SHOWHELP = 8
CC_ENABLEHOOK = 16
CC_ENABLETEMPLATE = 32
CC_ENABLETEMPLATEHANDLE = 64
CC_SOLIDCOLOR = 128
CC_ANYCOLOR = 256
FR_DOWN = 1
FR_WHOLEWORD = 2
FR_MATCHCASE = 4
FR_FINDNEXT = 8
FR_REPLACE = 16
FR_REPLACEALL = 32
FR_DIALOGTERM = 64
FR_SHOWHELP = 128
FR_ENABLEHOOK = 256
FR_ENABLETEMPLATE = 512
FR_NOUPDOWN = 1024
FR_NOMATCHCASE = 2048
FR_NOWHOLEWORD = 4096
FR_ENABLETEMPLATEHANDLE = 8192
FR_HIDEUPDOWN = 16384
FR_HIDEMATCHCASE = 32768
FR_HIDEWHOLEWORD = 65536
CF_SCREENFONTS = 1
CF_PRINTERFONTS = 2
CF_BOTH = CF_SCREENFONTS | CF_PRINTERFONTS
CF_SHOWHELP = 4
CF_ENABLEHOOK = 8
CF_ENABLETEMPLATE = 16
CF_ENABLETEMPLATEHANDLE = 32
CF_INITTOLOGFONTSTRUCT = 64
CF_USESTYLE = 128
CF_EFFECTS = 256
CF_APPLY = 512
CF_ANSIONLY = 1024
CF_SCRIPTSONLY = CF_ANSIONLY
CF_NOVECTORFONTS = 2048
CF_NOOEMFONTS = CF_NOVECTORFONTS
CF_NOSIMULATIONS = 4096
CF_LIMITSIZE = 8192
CF_FIXEDPITCHONLY = 16384
CF_WYSIWYG = 32768  # must also have CF_SCREENFONTS & CF_PRINTERFONTS
CF_FORCEFONTEXIST = 65536
CF_SCALABLEONLY = 131072
CF_TTONLY = 262144
CF_NOFACESEL = 524288
CF_NOSTYLESEL = 1048576
CF_NOSIZESEL = 2097152
CF_SELECTSCRIPT = 4194304
CF_NOSCRIPTSEL = 8388608
CF_NOVERTFONTS = 16777216
SIMULATED_FONTTYPE = 32768
PRINTER_FONTTYPE = 16384
SCREEN_FONTTYPE = 8192
BOLD_FONTTYPE = 256
ITALIC_FONTTYPE = 512
REGULAR_FONTTYPE = 1024
OPENTYPE_FONTTYPE = 65536
TYPE1_FONTTYPE = 131072
DSIG_FONTTYPE = 262144
WM_CHOOSEFONT_GETLOGFONT = WM_USER + 1
WM_CHOOSEFONT_SETLOGFONT = WM_USER + 101
WM_CHOOSEFONT_SETFLAGS = WM_USER + 102
LBSELCHSTRINGA = "commdlg_LBSelChangedNotify"
SHAREVISTRINGA = "commdlg_ShareViolation"
FILEOKSTRINGA = "commdlg_FileNameOK"
COLOROKSTRINGA = "commdlg_ColorOK"
SETRGBSTRINGA = "commdlg_SetRGBColor"
HELPMSGSTRINGA = "commdlg_help"
FINDMSGSTRINGA = "commdlg_FindReplace"
LBSELCHSTRING = LBSELCHSTRINGA
SHAREVISTRING = SHAREVISTRINGA
FILEOKSTRING = FILEOKSTRINGA
COLOROKSTRING = COLOROKSTRINGA
SETRGBSTRING = SETRGBSTRINGA
HELPMSGSTRING = HELPMSGSTRINGA
FINDMSGSTRING = FINDMSGSTRINGA
CD_LBSELNOITEMS = -1
CD_LBSELCHANGE = 0
CD_LBSELSUB = 1
CD_LBSELADD = 2
PD_ALLPAGES = 0
PD_SELECTION = 1
PD_PAGENUMS = 2
PD_NOSELECTION = 4
PD_NOPAGENUMS = 8
PD_COLLATE = 16
PD_PRINTTOFILE = 32
PD_PRINTSETUP = 64
PD_NOWARNING = 128
PD_RETURNDC = 256
PD_RETURNIC = 512
PD_RETURNDEFAULT = 1024
PD_SHOWHELP = 2048
PD_ENABLEPRINTHOOK = 4096
PD_ENABLESETUPHOOK = 8192
PD_ENABLEPRINTTEMPLATE = 16384
PD_ENABLESETUPTEMPLATE = 32768
PD_ENABLEPRINTTEMPLATEHANDLE = 65536
PD_ENABLESETUPTEMPLATEHANDLE = 131072
PD_USEDEVMODECOPIES = 262144
PD_DISABLEPRINTTOFILE = 524288
PD_HIDEPRINTTOFILE = 1048576
PD_NONETWORKBUTTON = 2097152
DN_DEFAULTPRN = 1
WM_PSD_PAGESETUPDLG = WM_USER
WM_PSD_FULLPAGERECT = WM_USER + 1
WM_PSD_MINMARGINRECT = WM_USER + 2
WM_PSD_MARGINRECT = WM_USER + 3
WM_PSD_GREEKTEXTRECT = WM_USER + 4
WM_PSD_ENVSTAMPRECT = WM_USER + 5
WM_PSD_YAFULLPAGERECT = WM_USER + 6
PSD_DEFAULTMINMARGINS = 0  # default (printer's)
PSD_INWININIINTLMEASURE = 0  # 1st of 4 possible
PSD_MINMARGINS = 1  # use caller's
PSD_MARGINS = 2  # use caller's
PSD_INTHOUSANDTHSOFINCHES = 4  # 2nd of 4 possible
PSD_INHUNDREDTHSOFMILLIMETERS = 8  # 3rd of 4 possible
PSD_DISABLEMARGINS = 16
PSD_DISABLEPRINTER = 32
PSD_NOWARNING = 128  # must be same as PD_*
PSD_DISABLEORIENTATION = 256
PSD_RETURNDEFAULT = 1024  # must be same as PD_*
PSD_DISABLEPAPER = 512
PSD_SHOWHELP = 2048  # must be same as PD_*
PSD_ENABLEPAGESETUPHOOK = 8192  # must be same as PD_*
PSD_ENABLEPAGESETUPTEMPLATE = 32768  # must be same as PD_*
PSD_ENABLEPAGESETUPTEMPLATEHANDLE = 131072  # must be same as PD_*
PSD_ENABLEPAGEPAINTHOOK = 262144
PSD_DISABLEPAGEPAINTING = 524288
PSD_NONETWORKBUTTON = 2097152  # must be same as PD_*

# Generated by h2py from winreg.h
HKEY_CLASSES_ROOT = -**********
HKEY_CURRENT_USER = -2147483647
HKEY_LOCAL_MACHINE = -2147483646
HKEY_USERS = -2147483645
HKEY_PERFORMANCE_DATA = -2147483644
HKEY_CURRENT_CONFIG = -2147483643
HKEY_DYN_DATA = -2147483642
HKEY_PERFORMANCE_TEXT = -2147483568  # ?? 4Jan98
HKEY_PERFORMANCE_NLSTEXT = -2147483552  # ?? 4Jan98

# Generated by h2py from winuser.h
HWND_BROADCAST = 65535
HWND_DESKTOP = 0
HWND_TOP = 0
HWND_BOTTOM = 1
HWND_TOPMOST = -1
HWND_NOTOPMOST = -2
HWND_MESSAGE = -3

# winuser.h line 4601
SM_CXSCREEN = 0
SM_CYSCREEN = 1
SM_CXVSCROLL = 2
SM_CYHSCROLL = 3
SM_CYCAPTION = 4
SM_CXBORDER = 5
SM_CYBORDER = 6
SM_CXDLGFRAME = 7
SM_CYDLGFRAME = 8
SM_CYVTHUMB = 9
SM_CXHTHUMB = 10
SM_CXICON = 11
SM_CYICON = 12
SM_CXCURSOR = 13
SM_CYCURSOR = 14
SM_CYMENU = 15
SM_CXFULLSCREEN = 16
SM_CYFULLSCREEN = 17
SM_CYKANJIWINDOW = 18
SM_MOUSEPRESENT = 19
SM_CYVSCROLL = 20
SM_CXHSCROLL = 21
SM_DEBUG = 22
SM_SWAPBUTTON = 23
SM_RESERVED1 = 24
SM_RESERVED2 = 25
SM_RESERVED3 = 26
SM_RESERVED4 = 27
SM_CXMIN = 28
SM_CYMIN = 29
SM_CXSIZE = 30
SM_CYSIZE = 31
SM_CXFRAME = 32
SM_CYFRAME = 33
SM_CXMINTRACK = 34
SM_CYMINTRACK = 35
SM_CXDOUBLECLK = 36
SM_CYDOUBLECLK = 37
SM_CXICONSPACING = 38
SM_CYICONSPACING = 39
SM_MENUDROPALIGNMENT = 40
SM_PENWINDOWS = 41
SM_DBCSENABLED = 42
SM_CMOUSEBUTTONS = 43
SM_CXFIXEDFRAME = SM_CXDLGFRAME
SM_CYFIXEDFRAME = SM_CYDLGFRAME
SM_CXSIZEFRAME = SM_CXFRAME
SM_CYSIZEFRAME = SM_CYFRAME
SM_SECURE = 44
SM_CXEDGE = 45
SM_CYEDGE = 46
SM_CXMINSPACING = 47
SM_CYMINSPACING = 48
SM_CXSMICON = 49
SM_CYSMICON = 50
SM_CYSMCAPTION = 51
SM_CXSMSIZE = 52
SM_CYSMSIZE = 53
SM_CXMENUSIZE = 54
SM_CYMENUSIZE = 55
SM_ARRANGE = 56
SM_CXMINIMIZED = 57
SM_CYMINIMIZED = 58
SM_CXMAXTRACK = 59
SM_CYMAXTRACK = 60
SM_CXMAXIMIZED = 61
SM_CYMAXIMIZED = 62
SM_NETWORK = 63
SM_CLEANBOOT = 67
SM_CXDRAG = 68
SM_CYDRAG = 69
SM_SHOWSOUNDS = 70
SM_CXMENUCHECK = 71
SM_CYMENUCHECK = 72
SM_SLOWMACHINE = 73
SM_MIDEASTENABLED = 74
SM_MOUSEWHEELPRESENT = 75
SM_XVIRTUALSCREEN = 76
SM_YVIRTUALSCREEN = 77
SM_CXVIRTUALSCREEN = 78
SM_CYVIRTUALSCREEN = 79
SM_CMONITORS = 80
SM_SAMEDISPLAYFORMAT = 81
SM_CMETRICS = 83
MNC_IGNORE = 0
MNC_CLOSE = 1
MNC_EXECUTE = 2
MNC_SELECT = 3
MNS_NOCHECK = -**********
MNS_MODELESS = **********
MNS_DRAGDROP = *********
MNS_AUTODISMISS = *********
MNS_NOTIFYBYPOS = *********
MNS_CHECKORBMP = ********
MIM_MAXHEIGHT = 1
MIM_BACKGROUND = 2
MIM_HELPID = 4
MIM_MENUDATA = 8
MIM_STYLE = 16
MIM_APPLYTOSUBMENUS = -**********
MND_CONTINUE = 0
MND_ENDMENU = 1
MNGOF_GAP = 3
MNGO_NOINTERFACE = 0
MNGO_NOERROR = 1
MIIM_STATE = 1
MIIM_ID = 2
MIIM_SUBMENU = 4
MIIM_CHECKMARKS = 8
MIIM_TYPE = 16
MIIM_DATA = 32
MIIM_STRING = 64
MIIM_BITMAP = 128
MIIM_FTYPE = 256
HBMMENU_CALLBACK = -1
HBMMENU_SYSTEM = 1
HBMMENU_MBAR_RESTORE = 2
HBMMENU_MBAR_MINIMIZE = 3
HBMMENU_MBAR_CLOSE = 5
HBMMENU_MBAR_CLOSE_D = 6
HBMMENU_MBAR_MINIMIZE_D = 7
HBMMENU_POPUP_CLOSE = 8
HBMMENU_POPUP_RESTORE = 9
HBMMENU_POPUP_MAXIMIZE = 10
HBMMENU_POPUP_MINIMIZE = 11
GMDI_USEDISABLED = 1
GMDI_GOINTOPOPUPS = 2
TPM_LEFTBUTTON = 0
TPM_RIGHTBUTTON = 2
TPM_LEFTALIGN = 0
TPM_CENTERALIGN = 4
TPM_RIGHTALIGN = 8
TPM_TOPALIGN = 0
TPM_VCENTERALIGN = 16
TPM_BOTTOMALIGN = 32
TPM_HORIZONTAL = 0
TPM_VERTICAL = 64
TPM_NONOTIFY = 128
TPM_RETURNCMD = 256
TPM_RECURSE = 1
DOF_EXECUTABLE = 32769
DOF_DOCUMENT = 32770
DOF_DIRECTORY = 32771
DOF_MULTIPLE = 32772
DOF_PROGMAN = 1
DOF_SHELLDATA = 2
DO_DROPFILE = 1162627398
DO_PRINTFILE = 1414419024
DT_TOP = 0
DT_LEFT = 0
DT_CENTER = 1
DT_RIGHT = 2
DT_VCENTER = 4
DT_BOTTOM = 8
DT_WORDBREAK = 16
DT_SINGLELINE = 32
DT_EXPANDTABS = 64
DT_TABSTOP = 128
DT_NOCLIP = 256
DT_EXTERNALLEADING = 512
DT_CALCRECT = 1024
DT_NOPREFIX = 2048
DT_INTERNAL = 4096
DT_EDITCONTROL = 8192
DT_PATH_ELLIPSIS = 16384
DT_END_ELLIPSIS = 32768
DT_MODIFYSTRING = 65536
DT_RTLREADING = 131072
DT_WORD_ELLIPSIS = 262144
DST_COMPLEX = 0
DST_TEXT = 1
DST_PREFIXTEXT = 2
DST_ICON = 3
DST_BITMAP = 4
DSS_NORMAL = 0
DSS_UNION = 16
DSS_DISABLED = 32
DSS_MONO = 128
DSS_RIGHT = 32768
DCX_WINDOW = 1
DCX_CACHE = 2
DCX_NORESETATTRS = 4
DCX_CLIPCHILDREN = 8
DCX_CLIPSIBLINGS = 16
DCX_PARENTCLIP = 32
DCX_EXCLUDERGN = 64
DCX_INTERSECTRGN = 128
DCX_EXCLUDEUPDATE = 256
DCX_INTERSECTUPDATE = 512
DCX_LOCKWINDOWUPDATE = 1024
DCX_VALIDATE = 2097152
CUDR_NORMAL = 0
CUDR_NOSNAPTOGRID = 1
CUDR_NORESOLVEPOSITIONS = 2
CUDR_NOCLOSEGAPS = 4
CUDR_NEGATIVECOORDS = 8
CUDR_NOPRIMARY = 16
RDW_INVALIDATE = 1
RDW_INTERNALPAINT = 2
RDW_ERASE = 4
RDW_VALIDATE = 8
RDW_NOINTERNALPAINT = 16
RDW_NOERASE = 32
RDW_NOCHILDREN = 64
RDW_ALLCHILDREN = 128
RDW_UPDATENOW = 256
RDW_ERASENOW = 512
RDW_FRAME = 1024
RDW_NOFRAME = 2048
SW_SCROLLCHILDREN = 1
SW_INVALIDATE = 2
SW_ERASE = 4
SW_SMOOTHSCROLL = 16  # Use smooth scrolling
ESB_ENABLE_BOTH = 0
ESB_DISABLE_BOTH = 3
ESB_DISABLE_LEFT = 1
ESB_DISABLE_RIGHT = 2
ESB_DISABLE_UP = 1
ESB_DISABLE_DOWN = 2
ESB_DISABLE_LTUP = ESB_DISABLE_LEFT
ESB_DISABLE_RTDN = ESB_DISABLE_RIGHT
HELPINFO_WINDOW = 1
HELPINFO_MENUITEM = 2
MB_OK = 0
MB_OKCANCEL = 1
MB_ABORTRETRYIGNORE = 2
MB_YESNOCANCEL = 3
MB_YESNO = 4
MB_RETRYCANCEL = 5
MB_ICONHAND = 16
MB_ICONQUESTION = 32
MB_ICONEXCLAMATION = 48
MB_ICONASTERISK = 64
MB_ICONWARNING = MB_ICONEXCLAMATION
MB_ICONERROR = MB_ICONHAND
MB_ICONINFORMATION = MB_ICONASTERISK
MB_ICONSTOP = MB_ICONHAND
MB_DEFBUTTON1 = 0
MB_DEFBUTTON2 = 256
MB_DEFBUTTON3 = 512
MB_DEFBUTTON4 = 768
MB_APPLMODAL = 0
MB_SYSTEMMODAL = 4096
MB_TASKMODAL = 8192
MB_HELP = 16384
MB_NOFOCUS = 32768
MB_SETFOREGROUND = 65536
MB_DEFAULT_DESKTOP_ONLY = 131072
MB_TOPMOST = 262144
MB_RIGHT = 524288
MB_RTLREADING = 1048576
MB_SERVICE_NOTIFICATION = 2097152
MB_TYPEMASK = 15
MB_USERICON = 128
MB_ICONMASK = 240
MB_DEFMASK = 3840
MB_MODEMASK = 12288
MB_MISCMASK = 49152
# winuser.h line 6373
CWP_ALL = 0
CWP_SKIPINVISIBLE = 1
CWP_SKIPDISABLED = 2
CWP_SKIPTRANSPARENT = 4
CTLCOLOR_MSGBOX = 0
CTLCOLOR_EDIT = 1
CTLCOLOR_LISTBOX = 2
CTLCOLOR_BTN = 3
CTLCOLOR_DLG = 4
CTLCOLOR_SCROLLBAR = 5
CTLCOLOR_STATIC = 6
CTLCOLOR_MAX = 7
COLOR_SCROLLBAR = 0
COLOR_BACKGROUND = 1
COLOR_ACTIVECAPTION = 2
COLOR_INACTIVECAPTION = 3
COLOR_MENU = 4
COLOR_WINDOW = 5
COLOR_WINDOWFRAME = 6
COLOR_MENUTEXT = 7
COLOR_WINDOWTEXT = 8
COLOR_CAPTIONTEXT = 9
COLOR_ACTIVEBORDER = 10
COLOR_INACTIVEBORDER = 11
COLOR_APPWORKSPACE = 12
COLOR_HIGHLIGHT = 13
COLOR_HIGHLIGHTTEXT = 14
COLOR_BTNFACE = 15
COLOR_BTNSHADOW = 16
COLOR_GRAYTEXT = 17
COLOR_BTNTEXT = 18
COLOR_INACTIVECAPTIONTEXT = 19
COLOR_BTNHIGHLIGHT = 20
COLOR_3DDKSHADOW = 21
COLOR_3DLIGHT = 22
COLOR_INFOTEXT = 23
COLOR_INFOBK = 24
COLOR_HOTLIGHT = 26
COLOR_GRADIENTACTIVECAPTION = 27
COLOR_GRADIENTINACTIVECAPTION = 28
COLOR_DESKTOP = COLOR_BACKGROUND
COLOR_3DFACE = COLOR_BTNFACE
COLOR_3DSHADOW = COLOR_BTNSHADOW
COLOR_3DHIGHLIGHT = COLOR_BTNHIGHLIGHT
COLOR_3DHILIGHT = COLOR_BTNHIGHLIGHT
COLOR_BTNHILIGHT = COLOR_BTNHIGHLIGHT
GW_HWNDFIRST = 0
GW_HWNDLAST = 1
GW_HWNDNEXT = 2
GW_HWNDPREV = 3
GW_OWNER = 4
GW_CHILD = 5
GW_ENABLEDPOPUP = 6
GW_MAX = 6
MF_INSERT = 0
MF_CHANGE = 128
MF_APPEND = 256
MF_DELETE = 512
MF_REMOVE = 4096
MF_BYCOMMAND = 0
MF_BYPOSITION = 1024
MF_SEPARATOR = 2048
MF_ENABLED = 0
MF_GRAYED = 1
MF_DISABLED = 2
MF_UNCHECKED = 0
MF_CHECKED = 8
MF_USECHECKBITMAPS = 512
MF_STRING = 0
MF_BITMAP = 4
MF_OWNERDRAW = 256
MF_POPUP = 16
MF_MENUBARBREAK = 32
MF_MENUBREAK = 64
MF_UNHILITE = 0
MF_HILITE = 128
MF_DEFAULT = 4096
MF_SYSMENU = 8192
MF_HELP = 16384
MF_RIGHTJUSTIFY = 16384
MF_MOUSESELECT = 32768
MF_END = 128
MFT_STRING = MF_STRING
MFT_BITMAP = MF_BITMAP
MFT_MENUBARBREAK = MF_MENUBARBREAK
MFT_MENUBREAK = MF_MENUBREAK
MFT_OWNERDRAW = MF_OWNERDRAW
MFT_RADIOCHECK = 512
MFT_SEPARATOR = MF_SEPARATOR
MFT_RIGHTORDER = 8192
MFT_RIGHTJUSTIFY = MF_RIGHTJUSTIFY
MFS_GRAYED = 3
MFS_DISABLED = MFS_GRAYED
MFS_CHECKED = MF_CHECKED
MFS_HILITE = MF_HILITE
MFS_ENABLED = MF_ENABLED
MFS_UNCHECKED = MF_UNCHECKED
MFS_UNHILITE = MF_UNHILITE
MFS_DEFAULT = MF_DEFAULT
MFS_MASK = 4235
MFS_HOTTRACKDRAWN = *********
MFS_CACHEDBMP = *********
MFS_BOTTOMGAPDROP = **********
MFS_TOPGAPDROP = -**********
MFS_GAPDROP = -**********
SC_SIZE = 61440
SC_MOVE = 61456
SC_MINIMIZE = 61472
SC_MAXIMIZE = 61488
SC_NEXTWINDOW = 61504
SC_PREVWINDOW = 61520
SC_CLOSE = 61536
SC_VSCROLL = 61552
SC_HSCROLL = 61568
SC_MOUSEMENU = 61584
SC_KEYMENU = 61696
SC_ARRANGE = 61712
SC_RESTORE = 61728
SC_TASKLIST = 61744
SC_SCREENSAVE = 61760
SC_HOTKEY = 61776
SC_DEFAULT = 61792
SC_MONITORPOWER = 61808
SC_CONTEXTHELP = 61824
SC_SEPARATOR = 61455
SC_ICON = SC_MINIMIZE
SC_ZOOM = SC_MAXIMIZE
IDC_ARROW = 32512
IDC_IBEAM = 32513
IDC_WAIT = 32514
IDC_CROSS = 32515
IDC_UPARROW = 32516
IDC_SIZE = 32640  # OBSOLETE: use IDC_SIZEALL
IDC_ICON = 32641  # OBSOLETE: use IDC_ARROW
IDC_SIZENWSE = 32642
IDC_SIZENESW = 32643
IDC_SIZEWE = 32644
IDC_SIZENS = 32645
IDC_SIZEALL = 32646
IDC_NO = 32648
IDC_HAND = 32649
IDC_APPSTARTING = 32650
IDC_HELP = 32651
IDC_PIN = 32671
IDC_PERSON = 32672
IMAGE_BITMAP = 0
IMAGE_ICON = 1
IMAGE_CURSOR = 2
IMAGE_ENHMETAFILE = 3
LR_DEFAULTCOLOR = 0
LR_MONOCHROME = 1
LR_COLOR = 2
LR_COPYRETURNORG = 4
LR_COPYDELETEORG = 8
LR_LOADFROMFILE = 16
LR_LOADTRANSPARENT = 32
LR_DEFAULTSIZE = 64
LR_LOADREALSIZE = 128
LR_LOADMAP3DCOLORS = 4096
LR_CREATEDIBSECTION = 8192
LR_COPYFROMRESOURCE = 16384
LR_SHARED = 32768
DI_MASK = 1
DI_IMAGE = 2
DI_NORMAL = 3
DI_COMPAT = 4
DI_DEFAULTSIZE = 8
RES_ICON = 1
RES_CURSOR = 2
OBM_CLOSE = 32754
OBM_UPARROW = 32753
OBM_DNARROW = 32752
OBM_RGARROW = 32751
OBM_LFARROW = 32750
OBM_REDUCE = 32749
OBM_ZOOM = 32748
OBM_RESTORE = 32747
OBM_REDUCED = 32746
OBM_ZOOMD = 32745
OBM_RESTORED = 32744
OBM_UPARROWD = 32743
OBM_DNARROWD = 32742
OBM_RGARROWD = 32741
OBM_LFARROWD = 32740
OBM_MNARROW = 32739
OBM_COMBO = 32738
OBM_UPARROWI = 32737
OBM_DNARROWI = 32736
OBM_RGARROWI = 32735
OBM_LFARROWI = 32734
OBM_OLD_CLOSE = 32767
OBM_SIZE = 32766
OBM_OLD_UPARROW = 32765
OBM_OLD_DNARROW = 32764
OBM_OLD_RGARROW = 32763
OBM_OLD_LFARROW = 32762
OBM_BTSIZE = 32761
OBM_CHECK = 32760
OBM_CHECKBOXES = 32759
OBM_BTNCORNERS = 32758
OBM_OLD_REDUCE = 32757
OBM_OLD_ZOOM = 32756
OBM_OLD_RESTORE = 32755
OCR_NORMAL = 32512
OCR_IBEAM = 32513
OCR_WAIT = 32514
OCR_CROSS = 32515
OCR_UP = 32516
OCR_SIZE = 32640
OCR_ICON = 32641
OCR_SIZENWSE = 32642
OCR_SIZENESW = 32643
OCR_SIZEWE = 32644
OCR_SIZENS = 32645
OCR_SIZEALL = 32646
OCR_ICOCUR = 32647
OCR_NO = 32648
OCR_HAND = 32649
OCR_APPSTARTING = 32650
# winuser.h line 7455
OIC_SAMPLE = 32512
OIC_HAND = 32513
OIC_QUES = 32514
OIC_BANG = 32515
OIC_NOTE = 32516
OIC_WINLOGO = 32517
OIC_WARNING = OIC_BANG
OIC_ERROR = OIC_HAND
OIC_INFORMATION = OIC_NOTE
ORD_LANGDRIVER = 1
IDI_APPLICATION = 32512
IDI_HAND = 32513
IDI_QUESTION = 32514
IDI_EXCLAMATION = 32515
IDI_ASTERISK = 32516
IDI_WINLOGO = 32517
IDI_WARNING = IDI_EXCLAMATION
IDI_ERROR = IDI_HAND
IDI_INFORMATION = IDI_ASTERISK
IDOK = 1
IDCANCEL = 2
IDABORT = 3
IDRETRY = 4
IDIGNORE = 5
IDYES = 6
IDNO = 7
IDCLOSE = 8
IDHELP = 9
ES_LEFT = 0
ES_CENTER = 1
ES_RIGHT = 2
ES_MULTILINE = 4
ES_UPPERCASE = 8
ES_LOWERCASE = 16
ES_PASSWORD = 32
ES_AUTOVSCROLL = 64
ES_AUTOHSCROLL = 128
ES_NOHIDESEL = 256
ES_OEMCONVERT = 1024
ES_READONLY = 2048
ES_WANTRETURN = 4096
ES_NUMBER = 8192
EN_SETFOCUS = 256
EN_KILLFOCUS = 512
EN_CHANGE = 768
EN_UPDATE = 1024
EN_ERRSPACE = 1280
EN_MAXTEXT = 1281
EN_HSCROLL = 1537
EN_VSCROLL = 1538
EC_LEFTMARGIN = 1
EC_RIGHTMARGIN = 2
EC_USEFONTINFO = 65535
EMSIS_COMPOSITIONSTRING = 1
EIMES_GETCOMPSTRATONCE = 1
EIMES_CANCELCOMPSTRINFOCUS = 2
EIMES_COMPLETECOMPSTRKILLFOCUS = 4
EM_GETSEL = 176
EM_SETSEL = 177
EM_GETRECT = 178
EM_SETRECT = 179
EM_SETRECTNP = 180
EM_SCROLL = 181
EM_LINESCROLL = 182
EM_SCROLLCARET = 183
EM_GETMODIFY = 184
EM_SETMODIFY = 185
EM_GETLINECOUNT = 186
EM_LINEINDEX = 187
EM_SETHANDLE = 188
EM_GETHANDLE = 189
EM_GETTHUMB = 190
EM_LINELENGTH = 193
EM_REPLACESEL = 194
EM_GETLINE = 196
EM_LIMITTEXT = 197
EM_CANUNDO = 198
EM_UNDO = 199
EM_FMTLINES = 200
EM_LINEFROMCHAR = 201
EM_SETTABSTOPS = 203
EM_SETPASSWORDCHAR = 204
EM_EMPTYUNDOBUFFER = 205
EM_GETFIRSTVISIBLELINE = 206
EM_SETREADONLY = 207
EM_SETWORDBREAKPROC = 208
EM_GETWORDBREAKPROC = 209
EM_GETPASSWORDCHAR = 210
EM_SETMARGINS = 211
EM_GETMARGINS = 212
EM_SETLIMITTEXT = EM_LIMITTEXT
EM_GETLIMITTEXT = 213
EM_POSFROMCHAR = 214
EM_CHARFROMPOS = 215
EM_SETIMESTATUS = 216
EM_GETIMESTATUS = 217
WB_LEFT = 0
WB_RIGHT = 1
WB_ISDELIMITER = 2
BS_PUSHBUTTON = 0
BS_DEFPUSHBUTTON = 1
BS_CHECKBOX = 2
BS_AUTOCHECKBOX = 3
BS_RADIOBUTTON = 4
BS_3STATE = 5
BS_AUTO3STATE = 6
BS_GROUPBOX = 7
BS_USERBUTTON = 8
BS_AUTORADIOBUTTON = 9
BS_OWNERDRAW = 11
BS_LEFTTEXT = 32
BS_TEXT = 0
BS_ICON = 64
BS_BITMAP = 128
BS_LEFT = 256
BS_RIGHT = 512
BS_CENTER = 768
BS_TOP = 1024
BS_BOTTOM = 2048
BS_VCENTER = 3072
BS_PUSHLIKE = 4096
BS_MULTILINE = 8192
BS_NOTIFY = 16384
BS_FLAT = 32768
BS_RIGHTBUTTON = BS_LEFTTEXT
BN_CLICKED = 0
BN_PAINT = 1
BN_HILITE = 2
BN_UNHILITE = 3
BN_DISABLE = 4
BN_DOUBLECLICKED = 5
BN_PUSHED = BN_HILITE
BN_UNPUSHED = BN_UNHILITE
BN_DBLCLK = BN_DOUBLECLICKED
BN_SETFOCUS = 6
BN_KILLFOCUS = 7
BM_GETCHECK = 240
BM_SETCHECK = 241
BM_GETSTATE = 242
BM_SETSTATE = 243
BM_SETSTYLE = 244
BM_CLICK = 245
BM_GETIMAGE = 246
BM_SETIMAGE = 247
BST_UNCHECKED = 0
BST_CHECKED = 1
BST_INDETERMINATE = 2
BST_PUSHED = 4
BST_FOCUS = 8
SS_LEFT = 0
SS_CENTER = 1
SS_RIGHT = 2
SS_ICON = 3
SS_BLACKRECT = 4
SS_GRAYRECT = 5
SS_WHITERECT = 6
SS_BLACKFRAME = 7
SS_GRAYFRAME = 8
SS_WHITEFRAME = 9
SS_USERITEM = 10
SS_SIMPLE = 11
SS_LEFTNOWORDWRAP = 12
SS_BITMAP = 14
SS_OWNERDRAW = 13
SS_ENHMETAFILE = 15
SS_ETCHEDHORZ = 16
SS_ETCHEDVERT = 17
SS_ETCHEDFRAME = 18
SS_TYPEMASK = 31
SS_NOPREFIX = 128
SS_NOTIFY = 256
SS_CENTERIMAGE = 512
SS_RIGHTJUST = 1024
SS_REALSIZEIMAGE = 2048
SS_SUNKEN = 4096
SS_ENDELLIPSIS = 16384
SS_PATHELLIPSIS = 32768
SS_WORDELLIPSIS = 49152
SS_ELLIPSISMASK = 49152
STM_SETICON = 368
STM_GETICON = 369
STM_SETIMAGE = 370
STM_GETIMAGE = 371
STN_CLICKED = 0
STN_DBLCLK = 1
STN_ENABLE = 2
STN_DISABLE = 3
STM_MSGMAX = 372
DWL_MSGRESULT = 0
DWL_DLGPROC = 4
DWL_USER = 8
DDL_READWRITE = 0
DDL_READONLY = 1
DDL_HIDDEN = 2
DDL_SYSTEM = 4
DDL_DIRECTORY = 16
DDL_ARCHIVE = 32
DDL_POSTMSGS = 8192
DDL_DRIVES = 16384
DDL_EXCLUSIVE = 32768

# from winuser.h line 153
RT_CURSOR = 1
RT_BITMAP = 2
RT_ICON = 3
RT_MENU = 4
RT_DIALOG = 5
RT_STRING = 6
RT_FONTDIR = 7
RT_FONT = 8
RT_ACCELERATOR = 9
RT_RCDATA = 10
RT_MESSAGETABLE = 11
DIFFERENCE = 11
RT_GROUP_CURSOR = RT_CURSOR + DIFFERENCE
RT_GROUP_ICON = RT_ICON + DIFFERENCE
RT_VERSION = 16
RT_DLGINCLUDE = 17
RT_PLUGPLAY = 19
RT_VXD = 20
RT_ANICURSOR = 21
RT_ANIICON = 22
RT_HTML = 23
# from winuser.h line 218
SB_HORZ = 0
SB_VERT = 1
SB_CTL = 2
SB_BOTH = 3
SB_LINEUP = 0
SB_LINELEFT = 0
SB_LINEDOWN = 1
SB_LINERIGHT = 1
SB_PAGEUP = 2
SB_PAGELEFT = 2
SB_PAGEDOWN = 3
SB_PAGERIGHT = 3
SB_THUMBPOSITION = 4
SB_THUMBTRACK = 5
SB_TOP = 6
SB_LEFT = 6
SB_BOTTOM = 7
SB_RIGHT = 7
SB_ENDSCROLL = 8
SW_HIDE = 0
SW_SHOWNORMAL = 1
SW_NORMAL = 1
SW_SHOWMINIMIZED = 2
SW_SHOWMAXIMIZED = 3
SW_MAXIMIZE = 3
SW_SHOWNOACTIVATE = 4
SW_SHOW = 5
SW_MINIMIZE = 6
SW_SHOWMINNOACTIVE = 7
SW_SHOWNA = 8
SW_RESTORE = 9
SW_SHOWDEFAULT = 10
SW_FORCEMINIMIZE = 11
SW_MAX = 11
HIDE_WINDOW = 0
SHOW_OPENWINDOW = 1
SHOW_ICONWINDOW = 2
SHOW_FULLSCREEN = 3
SHOW_OPENNOACTIVATE = 4
SW_PARENTCLOSING = 1
SW_OTHERZOOM = 2
SW_PARENTOPENING = 3
SW_OTHERUNZOOM = 4
AW_HOR_POSITIVE = 1
AW_HOR_NEGATIVE = 2
AW_VER_POSITIVE = 4
AW_VER_NEGATIVE = 8
AW_CENTER = 16
AW_HIDE = 65536
AW_ACTIVATE = 131072
AW_SLIDE = 262144
AW_BLEND = 524288
KF_EXTENDED = 256
KF_DLGMODE = 2048
KF_MENUMODE = 4096
KF_ALTDOWN = 8192
KF_REPEAT = 16384
KF_UP = 32768
VK_LBUTTON = 1
VK_RBUTTON = 2
VK_CANCEL = 3
VK_MBUTTON = 4
VK_BACK = 8
VK_TAB = 9
VK_CLEAR = 12
VK_RETURN = 13
VK_SHIFT = 16
VK_CONTROL = 17
VK_MENU = 18
VK_PAUSE = 19
VK_CAPITAL = 20
VK_KANA = 21
VK_HANGEUL = 21  # old name - should be here for compatibility
VK_HANGUL = 21
VK_JUNJA = 23
VK_FINAL = 24
VK_HANJA = 25
VK_KANJI = 25
VK_ESCAPE = 27
VK_CONVERT = 28
VK_NONCONVERT = 29
VK_ACCEPT = 30
VK_MODECHANGE = 31
VK_SPACE = 32
VK_PRIOR = 33
VK_NEXT = 34
VK_END = 35
VK_HOME = 36
VK_LEFT = 37
VK_UP = 38
VK_RIGHT = 39
VK_DOWN = 40
VK_SELECT = 41
VK_PRINT = 42
VK_EXECUTE = 43
VK_SNAPSHOT = 44
VK_INSERT = 45
VK_DELETE = 46
VK_HELP = 47
VK_LWIN = 91
VK_RWIN = 92
VK_APPS = 93
VK_NUMPAD0 = 96
VK_NUMPAD1 = 97
VK_NUMPAD2 = 98
VK_NUMPAD3 = 99
VK_NUMPAD4 = 100
VK_NUMPAD5 = 101
VK_NUMPAD6 = 102
VK_NUMPAD7 = 103
VK_NUMPAD8 = 104
VK_NUMPAD9 = 105
VK_MULTIPLY = 106
VK_ADD = 107
VK_SEPARATOR = 108
VK_SUBTRACT = 109
VK_DECIMAL = 110
VK_DIVIDE = 111
VK_F1 = 112
VK_F2 = 113
VK_F3 = 114
VK_F4 = 115
VK_F5 = 116
VK_F6 = 117
VK_F7 = 118
VK_F8 = 119
VK_F9 = 120
VK_F10 = 121
VK_F11 = 122
VK_F12 = 123
VK_F13 = 124
VK_F14 = 125
VK_F15 = 126
VK_F16 = 127
VK_F17 = 128
VK_F18 = 129
VK_F19 = 130
VK_F20 = 131
VK_F21 = 132
VK_F22 = 133
VK_F23 = 134
VK_F24 = 135
VK_NUMLOCK = 144
VK_SCROLL = 145
VK_LSHIFT = 160
VK_RSHIFT = 161
VK_LCONTROL = 162
VK_RCONTROL = 163
VK_LMENU = 164
VK_RMENU = 165
VK_PROCESSKEY = 229
VK_ATTN = 246
VK_CRSEL = 247
VK_EXSEL = 248
VK_EREOF = 249
VK_PLAY = 250
VK_ZOOM = 251
VK_NONAME = 252
VK_PA1 = 253
VK_OEM_CLEAR = 254
# multi-media related "keys"
VK_XBUTTON1 = 0x05
VK_XBUTTON2 = 0x06
VK_VOLUME_MUTE = 0xAD
VK_VOLUME_DOWN = 0xAE
VK_VOLUME_UP = 0xAF
VK_MEDIA_NEXT_TRACK = 0xB0
VK_MEDIA_PREV_TRACK = 0xB1
VK_MEDIA_PLAY_PAUSE = 0xB3
VK_BROWSER_BACK = 0xA6
VK_BROWSER_FORWARD = 0xA7
WH_MIN = -1
WH_MSGFILTER = -1
WH_JOURNALRECORD = 0
WH_JOURNALPLAYBACK = 1
WH_KEYBOARD = 2
WH_GETMESSAGE = 3
WH_CALLWNDPROC = 4
WH_CBT = 5
WH_SYSMSGFILTER = 6
WH_MOUSE = 7
WH_HARDWARE = 8
WH_DEBUG = 9
WH_SHELL = 10
WH_FOREGROUNDIDLE = 11
WH_CALLWNDPROCRET = 12
WH_KEYBOARD_LL = 13
WH_MOUSE_LL = 14
WH_MAX = 14
WH_MINHOOK = WH_MIN
WH_MAXHOOK = WH_MAX
HC_ACTION = 0
HC_GETNEXT = 1
HC_SKIP = 2
HC_NOREMOVE = 3
HC_NOREM = HC_NOREMOVE
HC_SYSMODALON = 4
HC_SYSMODALOFF = 5
HCBT_MOVESIZE = 0
HCBT_MINMAX = 1
HCBT_QS = 2
HCBT_CREATEWND = 3
HCBT_DESTROYWND = 4
HCBT_ACTIVATE = 5
HCBT_CLICKSKIPPED = 6
HCBT_KEYSKIPPED = 7
HCBT_SYSCOMMAND = 8
HCBT_SETFOCUS = 9
MSGF_DIALOGBOX = 0
MSGF_MESSAGEBOX = 1
MSGF_MENU = 2
# MSGF_MOVE = 3
# MSGF_SIZE = 4
MSGF_SCROLLBAR = 5
MSGF_NEXTWINDOW = 6
# MSGF_MAINLOOP = 8
MSGF_MAX = 8
MSGF_USER = 4096
HSHELL_WINDOWCREATED = 1
HSHELL_WINDOWDESTROYED = 2
HSHELL_ACTIVATESHELLWINDOW = 3
HSHELL_WINDOWACTIVATED = 4
HSHELL_GETMINRECT = 5
HSHELL_REDRAW = 6
HSHELL_TASKMAN = 7
HSHELL_LANGUAGE = 8
HSHELL_ACCESSIBILITYSTATE = 11
ACCESS_STICKYKEYS = 1
ACCESS_FILTERKEYS = 2
ACCESS_MOUSEKEYS = 3
# winuser.h line 624
LLKHF_EXTENDED = 1
LLKHF_INJECTED = 16
LLKHF_ALTDOWN = 32
LLKHF_UP = 128
LLKHF_LOWER_IL_INJECTED = 2
LLMHF_INJECTED = 1
LLMHF_LOWER_IL_INJECTED = 2
# line 692
HKL_PREV = 0
HKL_NEXT = 1
KLF_ACTIVATE = 1
KLF_SUBSTITUTE_OK = 2
KLF_UNLOADPREVIOUS = 4
KLF_REORDER = 8
KLF_REPLACELANG = 16
KLF_NOTELLSHELL = 128
KLF_SETFORPROCESS = 256
KL_NAMELENGTH = 9
DESKTOP_READOBJECTS = 1
DESKTOP_CREATEWINDOW = 2
DESKTOP_CREATEMENU = 4
DESKTOP_HOOKCONTROL = 8
DESKTOP_JOURNALRECORD = 16
DESKTOP_JOURNALPLAYBACK = 32
DESKTOP_ENUMERATE = 64
DESKTOP_WRITEOBJECTS = 128
DESKTOP_SWITCHDESKTOP = 256
DF_ALLOWOTHERACCOUNTHOOK = 1
WINSTA_ENUMDESKTOPS = 1
WINSTA_READATTRIBUTES = 2
WINSTA_ACCESSCLIPBOARD = 4
WINSTA_CREATEDESKTOP = 8
WINSTA_WRITEATTRIBUTES = 16
WINSTA_ACCESSGLOBALATOMS = 32
WINSTA_EXITWINDOWS = 64
WINSTA_ENUMERATE = 256
WINSTA_READSCREEN = 512
WSF_VISIBLE = 1
UOI_FLAGS = 1
UOI_NAME = 2
UOI_TYPE = 3
UOI_USER_SID = 4
GWL_WNDPROC = -4
GWL_HINSTANCE = -6
GWL_HWNDPARENT = -8
GWL_STYLE = -16
GWL_EXSTYLE = -20
GWL_USERDATA = -21
GWL_ID = -12
GCL_MENUNAME = -8
GCL_HBRBACKGROUND = -10
GCL_HCURSOR = -12
GCL_HICON = -14
GCL_HMODULE = -16
GCL_CBWNDEXTRA = -18
GCL_CBCLSEXTRA = -20
GCL_WNDPROC = -24
GCL_STYLE = -26
GCW_ATOM = -32
GCL_HICONSM = -34
# line 1291
WM_NULL = 0
WM_CREATE = 1
WM_DESTROY = 2
WM_MOVE = 3
WM_SIZE = 5
WM_ACTIVATE = 6
WA_INACTIVE = 0
WA_ACTIVE = 1
WA_CLICKACTIVE = 2
WM_SETFOCUS = 7
WM_KILLFOCUS = 8
WM_ENABLE = 10
WM_SETREDRAW = 11
WM_SETTEXT = 12
WM_GETTEXT = 13
WM_GETTEXTLENGTH = 14
WM_PAINT = 15
WM_CLOSE = 16
WM_QUERYENDSESSION = 17
WM_QUIT = 18
WM_QUERYOPEN = 19
WM_ERASEBKGND = 20
WM_SYSCOLORCHANGE = 21
WM_ENDSESSION = 22
WM_SHOWWINDOW = 24
WM_WININICHANGE = 26
WM_SETTINGCHANGE = WM_WININICHANGE
WM_DEVMODECHANGE = 27
WM_ACTIVATEAPP = 28
WM_FONTCHANGE = 29
WM_TIMECHANGE = 30
WM_CANCELMODE = 31
WM_SETCURSOR = 32
WM_MOUSEACTIVATE = 33
WM_CHILDACTIVATE = 34
WM_QUEUESYNC = 35
WM_GETMINMAXINFO = 36
WM_PAINTICON = 38
WM_ICONERASEBKGND = 39
WM_NEXTDLGCTL = 40
WM_SPOOLERSTATUS = 42
WM_DRAWITEM = 43
WM_MEASUREITEM = 44
WM_DELETEITEM = 45
WM_VKEYTOITEM = 46
WM_CHARTOITEM = 47
WM_SETFONT = 48
WM_GETFONT = 49
WM_SETHOTKEY = 50
WM_GETHOTKEY = 51
WM_QUERYDRAGICON = 55
WM_COMPAREITEM = 57
WM_GETOBJECT = 61
WM_COMPACTING = 65
WM_COMMNOTIFY = 68
WM_WINDOWPOSCHANGING = 70
WM_WINDOWPOSCHANGED = 71
WM_POWER = 72
PWR_OK = 1
PWR_FAIL = -1
PWR_SUSPENDREQUEST = 1
PWR_SUSPENDRESUME = 2
PWR_CRITICALRESUME = 3
WM_COPYDATA = 74
WM_CANCELJOURNAL = 75
WM_INPUTLANGCHANGEREQUEST = 80
WM_INPUTLANGCHANGE = 81
WM_TCARD = 82
WM_HELP = 83
WM_USERCHANGED = 84
WM_NOTIFYFORMAT = 85
NFR_ANSI = 1
NFR_UNICODE = 2
NF_QUERY = 3
NF_REQUERY = 4
WM_STYLECHANGING = 124
WM_STYLECHANGED = 125
WM_DISPLAYCHANGE = 126
WM_GETICON = 127
WM_SETICON = 128
WM_NCCREATE = 129
WM_NCDESTROY = 130
WM_NCCALCSIZE = 131
WM_NCHITTEST = 132
WM_NCPAINT = 133
WM_NCACTIVATE = 134
WM_GETDLGCODE = 135
WM_SYNCPAINT = 136
WM_NCMOUSEMOVE = 160
WM_NCLBUTTONDOWN = 161
WM_NCLBUTTONUP = 162
WM_NCLBUTTONDBLCLK = 163
WM_NCRBUTTONDOWN = 164
WM_NCRBUTTONUP = 165
WM_NCRBUTTONDBLCLK = 166
WM_NCMBUTTONDOWN = 167
WM_NCMBUTTONUP = 168
WM_NCMBUTTONDBLCLK = 169
WM_KEYFIRST = 256
WM_KEYDOWN = 256
WM_KEYUP = 257
WM_CHAR = 258
WM_DEADCHAR = 259
WM_SYSKEYDOWN = 260
WM_SYSKEYUP = 261
WM_SYSCHAR = 262
WM_SYSDEADCHAR = 263
WM_KEYLAST = 264
WM_IME_STARTCOMPOSITION = 269
WM_IME_ENDCOMPOSITION = 270
WM_IME_COMPOSITION = 271
WM_IME_KEYLAST = 271
WM_INITDIALOG = 272
WM_COMMAND = 273
WM_SYSCOMMAND = 274
WM_TIMER = 275
WM_HSCROLL = 276
WM_VSCROLL = 277
WM_INITMENU = 278
WM_INITMENUPOPUP = 279
WM_MENUSELECT = 287
WM_MENUCHAR = 288
WM_ENTERIDLE = 289
WM_MENURBUTTONUP = 290
WM_MENUDRAG = 291
WM_MENUGETOBJECT = 292
WM_UNINITMENUPOPUP = 293
WM_MENUCOMMAND = 294
WM_CTLCOLORMSGBOX = 306
WM_CTLCOLOREDIT = 307
WM_CTLCOLORLISTBOX = 308
WM_CTLCOLORBTN = 309
WM_CTLCOLORDLG = 310
WM_CTLCOLORSCROLLBAR = 311
WM_CTLCOLORSTATIC = 312
WM_MOUSEFIRST = 512
WM_MOUSEMOVE = 512
WM_LBUTTONDOWN = 513
WM_LBUTTONUP = 514
WM_LBUTTONDBLCLK = 515
WM_RBUTTONDOWN = 516
WM_RBUTTONUP = 517
WM_RBUTTONDBLCLK = 518
WM_MBUTTONDOWN = 519
WM_MBUTTONUP = 520
WM_MBUTTONDBLCLK = 521
WM_MOUSEWHEEL = 522
WM_MOUSELAST = 522
WHEEL_DELTA = 120  # Value for rolling one detent
WHEEL_PAGESCROLL = -1  # Scroll one page
WM_PARENTNOTIFY = 528
MENULOOP_WINDOW = 0
MENULOOP_POPUP = 1
WM_ENTERMENULOOP = 529
WM_EXITMENULOOP = 530
WM_NEXTMENU = 531
WM_SIZING = 532
WM_CAPTURECHANGED = 533
WM_MOVING = 534
WM_POWERBROADCAST = 536
PBT_APMQUERYSUSPEND = 0
PBT_APMQUERYSTANDBY = 1
PBT_APMQUERYSUSPENDFAILED = 2
PBT_APMQUERYSTANDBYFAILED = 3
PBT_APMSUSPEND = 4
PBT_APMSTANDBY = 5
PBT_APMRESUMECRITICAL = 6
PBT_APMRESUMESUSPEND = 7
PBT_APMRESUMESTANDBY = 8
PBTF_APMRESUMEFROMFAILURE = 1
PBT_APMBATTERYLOW = 9
PBT_APMPOWERSTATUSCHANGE = 10
PBT_APMOEMEVENT = 11
PBT_APMRESUMEAUTOMATIC = 18
WM_MDICREATE = 544
WM_MDIDESTROY = 545
WM_MDIACTIVATE = 546
WM_MDIRESTORE = 547
WM_MDINEXT = 548
WM_MDIMAXIMIZE = 549
WM_MDITILE = 550
WM_MDICASCADE = 551
WM_MDIICONARRANGE = 552
WM_MDIGETACTIVE = 553
WM_MDISETMENU = 560
WM_ENTERSIZEMOVE = 561
WM_EXITSIZEMOVE = 562
WM_DROPFILES = 563
WM_MDIREFRESHMENU = 564
WM_IME_SETCONTEXT = 641
WM_IME_NOTIFY = 642
WM_IME_CONTROL = 643
WM_IME_COMPOSITIONFULL = 644
WM_IME_SELECT = 645
WM_IME_CHAR = 646
WM_IME_REQUEST = 648
WM_IME_KEYDOWN = 656
WM_IME_KEYUP = 657
WM_MOUSEHOVER = 673
WM_MOUSELEAVE = 675
WM_CUT = 768
WM_COPY = 769
WM_PASTE = 770
WM_CLEAR = 771
WM_UNDO = 772
WM_RENDERFORMAT = 773
WM_RENDERALLFORMATS = 774
WM_DESTROYCLIPBOARD = 775
WM_DRAWCLIPBOARD = 776
WM_PAINTCLIPBOARD = 777
WM_VSCROLLCLIPBOARD = 778
WM_SIZECLIPBOARD = 779
WM_ASKCBFORMATNAME = 780
WM_CHANGECBCHAIN = 781
WM_HSCROLLCLIPBOARD = 782
WM_QUERYNEWPALETTE = 783
WM_PALETTEISCHANGING = 784
WM_PALETTECHANGED = 785
WM_HOTKEY = 786
WM_PRINT = 791
WM_HANDHELDFIRST = 856
WM_HANDHELDLAST = 863
WM_AFXFIRST = 864
WM_AFXLAST = 895
WM_PENWINFIRST = 896
WM_PENWINLAST = 911
WM_APP = 32768
WMSZ_LEFT = 1
WMSZ_RIGHT = 2
WMSZ_TOP = 3
WMSZ_TOPLEFT = 4
WMSZ_TOPRIGHT = 5
WMSZ_BOTTOM = 6
WMSZ_BOTTOMLEFT = 7
WMSZ_BOTTOMRIGHT = 8
# ST_BEGINSWP = 0
# ST_ENDSWP = 1
HTERROR = -2
HTTRANSPARENT = -1
HTNOWHERE = 0
HTCLIENT = 1
HTCAPTION = 2
HTSYSMENU = 3
HTGROWBOX = 4
HTSIZE = HTGROWBOX
HTMENU = 5
HTHSCROLL = 6
HTVSCROLL = 7
HTMINBUTTON = 8
HTMAXBUTTON = 9
HTLEFT = 10
HTRIGHT = 11
HTTOP = 12
HTTOPLEFT = 13
HTTOPRIGHT = 14
HTBOTTOM = 15
HTBOTTOMLEFT = 16
HTBOTTOMRIGHT = 17
HTBORDER = 18
HTREDUCE = HTMINBUTTON
HTZOOM = HTMAXBUTTON
HTSIZEFIRST = HTLEFT
HTSIZELAST = HTBOTTOMRIGHT
HTOBJECT = 19
HTCLOSE = 20
HTHELP = 21
SMTO_NORMAL = 0
SMTO_BLOCK = 1
SMTO_ABORTIFHUNG = 2
SMTO_NOTIMEOUTIFNOTHUNG = 8
MA_ACTIVATE = 1
MA_ACTIVATEANDEAT = 2
MA_NOACTIVATE = 3
MA_NOACTIVATEANDEAT = 4
ICON_SMALL = 0
ICON_BIG = 1
SIZE_RESTORED = 0
SIZE_MINIMIZED = 1
SIZE_MAXIMIZED = 2
SIZE_MAXSHOW = 3
SIZE_MAXHIDE = 4
SIZENORMAL = SIZE_RESTORED
SIZEICONIC = SIZE_MINIMIZED
SIZEFULLSCREEN = SIZE_MAXIMIZED
SIZEZOOMSHOW = SIZE_MAXSHOW
SIZEZOOMHIDE = SIZE_MAXHIDE
WVR_ALIGNTOP = 16
WVR_ALIGNLEFT = 32
WVR_ALIGNBOTTOM = 64
WVR_ALIGNRIGHT = 128
WVR_HREDRAW = 256
WVR_VREDRAW = 512
WVR_REDRAW = WVR_HREDRAW | WVR_VREDRAW
WVR_VALIDRECTS = 1024
MK_LBUTTON = 1
MK_RBUTTON = 2
MK_SHIFT = 4
MK_CONTROL = 8
MK_MBUTTON = 16
TME_HOVER = 1
TME_LEAVE = 2
TME_QUERY = **********
TME_CANCEL = -**********
HOVER_DEFAULT = -1
WS_OVERLAPPED = 0
WS_POPUP = -**********
WS_CHILD = **********
WS_MINIMIZE = *********
WS_VISIBLE = *********
WS_DISABLED = *********
WS_CLIPSIBLINGS = ********
WS_CLIPCHILDREN = 33554432
WS_MAXIMIZE = 16777216
WS_CAPTION = 12582912
WS_BORDER = 8388608
WS_DLGFRAME = 4194304
WS_VSCROLL = 2097152
WS_HSCROLL = 1048576
WS_SYSMENU = 524288
WS_THICKFRAME = 262144
WS_GROUP = 131072
WS_TABSTOP = 65536
WS_MINIMIZEBOX = 131072
WS_MAXIMIZEBOX = 65536
WS_TILED = WS_OVERLAPPED
WS_ICONIC = WS_MINIMIZE
WS_SIZEBOX = WS_THICKFRAME
WS_OVERLAPPEDWINDOW = (
    WS_OVERLAPPED
    | WS_CAPTION
    | WS_SYSMENU
    | WS_THICKFRAME
    | WS_MINIMIZEBOX
    | WS_MAXIMIZEBOX
)
WS_POPUPWINDOW = WS_POPUP | WS_BORDER | WS_SYSMENU
WS_CHILDWINDOW = WS_CHILD
WS_TILEDWINDOW = WS_OVERLAPPEDWINDOW
WS_EX_DLGMODALFRAME = 1
WS_EX_NOPARENTNOTIFY = 4
WS_EX_TOPMOST = 8
WS_EX_ACCEPTFILES = 16
WS_EX_TRANSPARENT = 32
WS_EX_MDICHILD = 64
WS_EX_TOOLWINDOW = 128
WS_EX_WINDOWEDGE = 256
WS_EX_CLIENTEDGE = 512
WS_EX_CONTEXTHELP = 1024
WS_EX_RIGHT = 4096
WS_EX_LEFT = 0
WS_EX_RTLREADING = 8192
WS_EX_LTRREADING = 0
WS_EX_LEFTSCROLLBAR = 16384
WS_EX_RIGHTSCROLLBAR = 0
WS_EX_CONTROLPARENT = 65536
WS_EX_STATICEDGE = 131072
WS_EX_APPWINDOW = 262144
WS_EX_OVERLAPPEDWINDOW = WS_EX_WINDOWEDGE | WS_EX_CLIENTEDGE
WS_EX_PALETTEWINDOW = WS_EX_WINDOWEDGE | WS_EX_TOOLWINDOW | WS_EX_TOPMOST
WS_EX_LAYERED = 0x00080000
WS_EX_NOINHERITLAYOUT = 0x00100000
WS_EX_LAYOUTRTL = 0x00400000
WS_EX_COMPOSITED = 0x02000000
WS_EX_NOACTIVATE = 0x08000000

CS_VREDRAW = 1
CS_HREDRAW = 2
# CS_KEYCVTWINDOW = 0x0004
CS_DBLCLKS = 8
CS_OWNDC = 32
CS_CLASSDC = 64
CS_PARENTDC = 128
# CS_NOKEYCVT = 0x0100
CS_NOCLOSE = 512
CS_SAVEBITS = 2048
CS_BYTEALIGNCLIENT = 4096
CS_BYTEALIGNWINDOW = 8192
CS_GLOBALCLASS = 16384
CS_IME = 65536
PRF_CHECKVISIBLE = 1
PRF_NONCLIENT = 2
PRF_CLIENT = 4
PRF_ERASEBKGND = 8
PRF_CHILDREN = 16
PRF_OWNED = 32
BDR_RAISEDOUTER = 1
BDR_SUNKENOUTER = 2
BDR_RAISEDINNER = 4
BDR_SUNKENINNER = 8
BDR_OUTER = 3
BDR_INNER = 12
# BDR_RAISED = 0x0005
# BDR_SUNKEN = 0x000a
EDGE_RAISED = BDR_RAISEDOUTER | BDR_RAISEDINNER
EDGE_SUNKEN = BDR_SUNKENOUTER | BDR_SUNKENINNER
EDGE_ETCHED = BDR_SUNKENOUTER | BDR_RAISEDINNER
EDGE_BUMP = BDR_RAISEDOUTER | BDR_SUNKENINNER

# winuser.h line 2879
ISMEX_NOSEND = 0
ISMEX_SEND = 1
ISMEX_NOTIFY = 2
ISMEX_CALLBACK = 4
ISMEX_REPLIED = 8
CW_USEDEFAULT = -**********
FLASHW_STOP = 0
FLASHW_CAPTION = 1
FLASHW_TRAY = 2
FLASHW_ALL = FLASHW_CAPTION | FLASHW_TRAY
FLASHW_TIMER = 4
FLASHW_TIMERNOFG = 12

# winuser.h line 7963
DS_ABSALIGN = 1
DS_SYSMODAL = 2
DS_LOCALEDIT = 32
DS_SETFONT = 64
DS_MODALFRAME = 128
DS_NOIDLEMSG = 256
DS_SETFOREGROUND = 512
DS_3DLOOK = 4
DS_FIXEDSYS = 8
DS_NOFAILCREATE = 16
DS_CONTROL = 1024
DS_CENTER = 2048
DS_CENTERMOUSE = 4096
DS_CONTEXTHELP = 8192
DM_GETDEFID = WM_USER + 0
DM_SETDEFID = WM_USER + 1
DM_REPOSITION = WM_USER + 2
# PSM_PAGEINFO = (WM_USER+100)
# PSM_SHEETINFO = (WM_USER+101)
# PSI_SETACTIVE = 0x0001
# PSI_KILLACTIVE = 0x0002
# PSI_APPLY = 0x0003
# PSI_RESET = 0x0004
# PSI_HASHELP = 0x0005
# PSI_HELP = 0x0006
# PSI_CHANGED = 0x0001
# PSI_GUISTART = 0x0002
# PSI_REBOOT = 0x0003
# PSI_GETSIBLINGS = 0x0004
DC_HASDEFID = 21323
DLGC_WANTARROWS = 1
DLGC_WANTTAB = 2
DLGC_WANTALLKEYS = 4
DLGC_WANTMESSAGE = 4
DLGC_HASSETSEL = 8
DLGC_DEFPUSHBUTTON = 16
DLGC_UNDEFPUSHBUTTON = 32
DLGC_RADIOBUTTON = 64
DLGC_WANTCHARS = 128
DLGC_STATIC = 256
DLGC_BUTTON = 8192
LB_CTLCODE = 0
LB_OKAY = 0
LB_ERR = -1
LB_ERRSPACE = -2
LBN_ERRSPACE = -2
LBN_SELCHANGE = 1
LBN_DBLCLK = 2
LBN_SELCANCEL = 3
LBN_SETFOCUS = 4
LBN_KILLFOCUS = 5
LB_ADDSTRING = 384
LB_INSERTSTRING = 385
LB_DELETESTRING = 386
LB_SELITEMRANGEEX = 387
LB_RESETCONTENT = 388
LB_SETSEL = 389
LB_SETCURSEL = 390
LB_GETSEL = 391
LB_GETCURSEL = 392
LB_GETTEXT = 393
LB_GETTEXTLEN = 394
LB_GETCOUNT = 395
LB_SELECTSTRING = 396
LB_DIR = 397
LB_GETTOPINDEX = 398
LB_FINDSTRING = 399
LB_GETSELCOUNT = 400
LB_GETSELITEMS = 401
LB_SETTABSTOPS = 402
LB_GETHORIZONTALEXTENT = 403
LB_SETHORIZONTALEXTENT = 404
LB_SETCOLUMNWIDTH = 405
LB_ADDFILE = 406
LB_SETTOPINDEX = 407
LB_GETITEMRECT = 408
LB_GETITEMDATA = 409
LB_SETITEMDATA = 410
LB_SELITEMRANGE = 411
LB_SETANCHORINDEX = 412
LB_GETANCHORINDEX = 413
LB_SETCARETINDEX = 414
LB_GETCARETINDEX = 415
LB_SETITEMHEIGHT = 416
LB_GETITEMHEIGHT = 417
LB_FINDSTRINGEXACT = 418
LB_SETLOCALE = 421
LB_GETLOCALE = 422
LB_SETCOUNT = 423
LB_INITSTORAGE = 424
LB_ITEMFROMPOINT = 425
LB_MSGMAX = 432
LBS_NOTIFY = 1
LBS_SORT = 2
LBS_NOREDRAW = 4
LBS_MULTIPLESEL = 8
LBS_OWNERDRAWFIXED = 16
LBS_OWNERDRAWVARIABLE = 32
LBS_HASSTRINGS = 64
LBS_USETABSTOPS = 128
LBS_NOINTEGRALHEIGHT = 256
LBS_MULTICOLUMN = 512
LBS_WANTKEYBOARDINPUT = 1024
LBS_EXTENDEDSEL = 2048
LBS_DISABLENOSCROLL = 4096
LBS_NODATA = 8192
LBS_NOSEL = 16384
LBS_STANDARD = LBS_NOTIFY | LBS_SORT | WS_VSCROLL | WS_BORDER
CB_OKAY = 0
CB_ERR = -1
CB_ERRSPACE = -2
CBN_ERRSPACE = -1
CBN_SELCHANGE = 1
CBN_DBLCLK = 2
CBN_SETFOCUS = 3
CBN_KILLFOCUS = 4
CBN_EDITCHANGE = 5
CBN_EDITUPDATE = 6
CBN_DROPDOWN = 7
CBN_CLOSEUP = 8
CBN_SELENDOK = 9
CBN_SELENDCANCEL = 10
CBS_SIMPLE = 1
CBS_DROPDOWN = 2
CBS_DROPDOWNLIST = 3
CBS_OWNERDRAWFIXED = 16
CBS_OWNERDRAWVARIABLE = 32
CBS_AUTOHSCROLL = 64
CBS_OEMCONVERT = 128
CBS_SORT = 256
CBS_HASSTRINGS = 512
CBS_NOINTEGRALHEIGHT = 1024
CBS_DISABLENOSCROLL = 2048
CBS_UPPERCASE = 8192
CBS_LOWERCASE = 16384
CB_GETEDITSEL = 320
CB_LIMITTEXT = 321
CB_SETEDITSEL = 322
CB_ADDSTRING = 323
CB_DELETESTRING = 324
CB_DIR = 325
CB_GETCOUNT = 326
CB_GETCURSEL = 327
CB_GETLBTEXT = 328
CB_GETLBTEXTLEN = 329
CB_INSERTSTRING = 330
CB_RESETCONTENT = 331
CB_FINDSTRING = 332
CB_SELECTSTRING = 333
CB_SETCURSEL = 334
CB_SHOWDROPDOWN = 335
CB_GETITEMDATA = 336
CB_SETITEMDATA = 337
CB_GETDROPPEDCONTROLRECT = 338
CB_SETITEMHEIGHT = 339
CB_GETITEMHEIGHT = 340
CB_SETEXTENDEDUI = 341
CB_GETEXTENDEDUI = 342
CB_GETDROPPEDSTATE = 343
CB_FINDSTRINGEXACT = 344
CB_SETLOCALE = 345
CB_GETLOCALE = 346
CB_GETTOPINDEX = 347
CB_SETTOPINDEX = 348
CB_GETHORIZONTALEXTENT = 349
CB_SETHORIZONTALEXTENT = 350
CB_GETDROPPEDWIDTH = 351
CB_SETDROPPEDWIDTH = 352
CB_INITSTORAGE = 353
CB_MSGMAX = 354
SBS_HORZ = 0
SBS_VERT = 1
SBS_TOPALIGN = 2
SBS_LEFTALIGN = 2
SBS_BOTTOMALIGN = 4
SBS_RIGHTALIGN = 4
SBS_SIZEBOXTOPLEFTALIGN = 2
SBS_SIZEBOXBOTTOMRIGHTALIGN = 4
SBS_SIZEBOX = 8
SBS_SIZEGRIP = 16
SBM_SETPOS = 224
SBM_GETPOS = 225
SBM_SETRANGE = 226
SBM_SETRANGEREDRAW = 230
SBM_GETRANGE = 227
SBM_ENABLE_ARROWS = 228
SBM_SETSCROLLINFO = 233
SBM_GETSCROLLINFO = 234
SIF_RANGE = 1
SIF_PAGE = 2
SIF_POS = 4
SIF_DISABLENOSCROLL = 8
SIF_TRACKPOS = 16
SIF_ALL = SIF_RANGE | SIF_PAGE | SIF_POS | SIF_TRACKPOS
MDIS_ALLCHILDSTYLES = 1
MDITILE_VERTICAL = 0
MDITILE_HORIZONTAL = 1
MDITILE_SKIPDISABLED = 2
MDITILE_ZORDER = 4

IMC_GETCANDIDATEPOS = 7
IMC_SETCANDIDATEPOS = 8
IMC_GETCOMPOSITIONFONT = 9
IMC_SETCOMPOSITIONFONT = 10
IMC_GETCOMPOSITIONWINDOW = 11
IMC_SETCOMPOSITIONWINDOW = 12
IMC_GETSTATUSWINDOWPOS = 15
IMC_SETSTATUSWINDOWPOS = 16
IMC_CLOSESTATUSWINDOW = 33
IMC_OPENSTATUSWINDOW = 34
# Generated by h2py from \msvc20\include\winnt.h
# hacked and split by mhammond.
DELETE = 65536
READ_CONTROL = 131072
WRITE_DAC = 262144
WRITE_OWNER = 524288
SYNCHRONIZE = 1048576
STANDARD_RIGHTS_REQUIRED = 983040
STANDARD_RIGHTS_READ = READ_CONTROL
STANDARD_RIGHTS_WRITE = READ_CONTROL
STANDARD_RIGHTS_EXECUTE = READ_CONTROL
STANDARD_RIGHTS_ALL = 2031616
SPECIFIC_RIGHTS_ALL = 65535
ACCESS_SYSTEM_SECURITY = 16777216
MAXIMUM_ALLOWED = 33554432
GENERIC_READ = -**********
GENERIC_WRITE = **********
GENERIC_EXECUTE = *********
GENERIC_ALL = *********

SERVICE_KERNEL_DRIVER = 1
SERVICE_FILE_SYSTEM_DRIVER = 2
SERVICE_ADAPTER = 4
SERVICE_RECOGNIZER_DRIVER = 8
SERVICE_DRIVER = (
    SERVICE_KERNEL_DRIVER | SERVICE_FILE_SYSTEM_DRIVER | SERVICE_RECOGNIZER_DRIVER
)
SERVICE_WIN32_OWN_PROCESS = 16
SERVICE_WIN32_SHARE_PROCESS = 32
SERVICE_WIN32 = SERVICE_WIN32_OWN_PROCESS | SERVICE_WIN32_SHARE_PROCESS
SERVICE_INTERACTIVE_PROCESS = 256
SERVICE_TYPE_ALL = (
    SERVICE_WIN32 | SERVICE_ADAPTER | SERVICE_DRIVER | SERVICE_INTERACTIVE_PROCESS
)
SERVICE_BOOT_START = 0
SERVICE_SYSTEM_START = 1
SERVICE_AUTO_START = 2
SERVICE_DEMAND_START = 3
SERVICE_DISABLED = 4
SERVICE_ERROR_IGNORE = 0
SERVICE_ERROR_NORMAL = 1
SERVICE_ERROR_SEVERE = 2
SERVICE_ERROR_CRITICAL = 3
TAPE_ERASE_SHORT = 0
TAPE_ERASE_LONG = 1
TAPE_LOAD = 0
TAPE_UNLOAD = 1
TAPE_TENSION = 2
TAPE_LOCK = 3
TAPE_UNLOCK = 4
TAPE_FORMAT = 5
TAPE_SETMARKS = 0
TAPE_FILEMARKS = 1
TAPE_SHORT_FILEMARKS = 2
TAPE_LONG_FILEMARKS = 3
TAPE_ABSOLUTE_POSITION = 0
TAPE_LOGICAL_POSITION = 1
TAPE_PSEUDO_LOGICAL_POSITION = 2
TAPE_REWIND = 0
TAPE_ABSOLUTE_BLOCK = 1
TAPE_LOGICAL_BLOCK = 2
TAPE_PSEUDO_LOGICAL_BLOCK = 3
TAPE_SPACE_END_OF_DATA = 4
TAPE_SPACE_RELATIVE_BLOCKS = 5
TAPE_SPACE_FILEMARKS = 6
TAPE_SPACE_SEQUENTIAL_FMKS = 7
TAPE_SPACE_SETMARKS = 8
TAPE_SPACE_SEQUENTIAL_SMKS = 9
TAPE_DRIVE_FIXED = 1
TAPE_DRIVE_SELECT = 2
TAPE_DRIVE_INITIATOR = 4
TAPE_DRIVE_ERASE_SHORT = 16
TAPE_DRIVE_ERASE_LONG = 32
TAPE_DRIVE_ERASE_BOP_ONLY = 64
TAPE_DRIVE_ERASE_IMMEDIATE = 128
TAPE_DRIVE_TAPE_CAPACITY = 256
TAPE_DRIVE_TAPE_REMAINING = 512
TAPE_DRIVE_FIXED_BLOCK = 1024
TAPE_DRIVE_VARIABLE_BLOCK = 2048
TAPE_DRIVE_WRITE_PROTECT = 4096
TAPE_DRIVE_EOT_WZ_SIZE = 8192
TAPE_DRIVE_ECC = 65536
TAPE_DRIVE_COMPRESSION = 131072
TAPE_DRIVE_PADDING = 262144
TAPE_DRIVE_REPORT_SMKS = 524288
TAPE_DRIVE_GET_ABSOLUTE_BLK = 1048576
TAPE_DRIVE_GET_LOGICAL_BLK = 2097152
TAPE_DRIVE_SET_EOT_WZ_SIZE = 4194304
TAPE_DRIVE_LOAD_UNLOAD = -2147483647
TAPE_DRIVE_TENSION = -2147483646
TAPE_DRIVE_LOCK_UNLOCK = -2147483644
TAPE_DRIVE_REWIND_IMMEDIATE = -2147483640
TAPE_DRIVE_SET_BLOCK_SIZE = -2147483632
TAPE_DRIVE_LOAD_UNLD_IMMED = -2147483616
TAPE_DRIVE_TENSION_IMMED = -2147483584
TAPE_DRIVE_LOCK_UNLK_IMMED = -2147483520
TAPE_DRIVE_SET_ECC = -2147483392
TAPE_DRIVE_SET_COMPRESSION = -2147483136
TAPE_DRIVE_SET_PADDING = -2147482624
TAPE_DRIVE_SET_REPORT_SMKS = -2147481600
TAPE_DRIVE_ABSOLUTE_BLK = -2147479552
TAPE_DRIVE_ABS_BLK_IMMED = -2147475456
TAPE_DRIVE_LOGICAL_BLK = -2147467264
TAPE_DRIVE_LOG_BLK_IMMED = -2147450880
TAPE_DRIVE_END_OF_DATA = -2147418112
TAPE_DRIVE_RELATIVE_BLKS = -2147352576
TAPE_DRIVE_FILEMARKS = -2147221504
TAPE_DRIVE_SEQUENTIAL_FMKS = -2146959360
TAPE_DRIVE_SETMARKS = -2146435072
TAPE_DRIVE_SEQUENTIAL_SMKS = -2145386496
TAPE_DRIVE_REVERSE_POSITION = -2143289344
TAPE_DRIVE_SPACE_IMMEDIATE = -2139095040
TAPE_DRIVE_WRITE_SETMARKS = -2130706432
TAPE_DRIVE_WRITE_FILEMARKS = -2113929216
TAPE_DRIVE_WRITE_SHORT_FMKS = -2080374784
TAPE_DRIVE_WRITE_LONG_FMKS = -2013265920
TAPE_DRIVE_WRITE_MARK_IMMED = -1879048192
TAPE_DRIVE_FORMAT = -1610612736
TAPE_DRIVE_FORMAT_IMMEDIATE = -**********
TAPE_FIXED_PARTITIONS = 0
TAPE_SELECT_PARTITIONS = 1
TAPE_INITIATOR_PARTITIONS = 2
# Generated by h2py from \msvc20\include\winnt.h
# hacked and split by mhammond.

APPLICATION_ERROR_MASK = *********
ERROR_SEVERITY_SUCCESS = 0
ERROR_SEVERITY_INFORMATIONAL = **********
ERROR_SEVERITY_WARNING = -**********
ERROR_SEVERITY_ERROR = -**********
MINCHAR = 128
MAXCHAR = 127
MINSHORT = 32768
MAXSHORT = 32767
MINLONG = -**********
MAXLONG = 2147483647
MAXBYTE = 255
MAXWORD = 65535
MAXDWORD = -1
LANG_NEUTRAL = 0
LANG_BULGARIAN = 2
LANG_CHINESE = 4
LANG_CROATIAN = 26
LANG_CZECH = 5
LANG_DANISH = 6
LANG_DUTCH = 19
LANG_ENGLISH = 9
LANG_FINNISH = 11
LANG_FRENCH = 12
LANG_GERMAN = 7
LANG_GREEK = 8
LANG_HUNGARIAN = 14
LANG_ICELANDIC = 15
LANG_ITALIAN = 16
LANG_JAPANESE = 17
LANG_KOREAN = 18
LANG_NORWEGIAN = 20
LANG_POLISH = 21
LANG_PORTUGUESE = 22
LANG_ROMANIAN = 24
LANG_RUSSIAN = 25
LANG_SLOVAK = 27
LANG_SLOVENIAN = 36
LANG_SPANISH = 10
LANG_SWEDISH = 29
LANG_TURKISH = 31
SUBLANG_NEUTRAL = 0
SUBLANG_DEFAULT = 1
SUBLANG_SYS_DEFAULT = 2
SUBLANG_CHINESE_TRADITIONAL = 1
SUBLANG_CHINESE_SIMPLIFIED = 2
SUBLANG_CHINESE_HONGKONG = 3
SUBLANG_CHINESE_SINGAPORE = 4
SUBLANG_DUTCH = 1
SUBLANG_DUTCH_BELGIAN = 2
SUBLANG_ENGLISH_US = 1
SUBLANG_ENGLISH_UK = 2
SUBLANG_ENGLISH_AUS = 3
SUBLANG_ENGLISH_CAN = 4
SUBLANG_ENGLISH_NZ = 5
SUBLANG_ENGLISH_EIRE = 6
SUBLANG_FRENCH = 1
SUBLANG_FRENCH_BELGIAN = 2
SUBLANG_FRENCH_CANADIAN = 3
SUBLANG_FRENCH_SWISS = 4
SUBLANG_GERMAN = 1
SUBLANG_GERMAN_SWISS = 2
SUBLANG_GERMAN_AUSTRIAN = 3
SUBLANG_ITALIAN = 1
SUBLANG_ITALIAN_SWISS = 2
SUBLANG_NORWEGIAN_BOKMAL = 1
SUBLANG_NORWEGIAN_NYNORSK = 2
SUBLANG_PORTUGUESE = 2
SUBLANG_PORTUGUESE_BRAZILIAN = 1
SUBLANG_SPANISH = 1
SUBLANG_SPANISH_MEXICAN = 2
SUBLANG_SPANISH_MODERN = 3
SORT_DEFAULT = 0
SORT_JAPANESE_XJIS = 0
SORT_JAPANESE_UNICODE = 1
SORT_CHINESE_BIG5 = 0
SORT_CHINESE_UNICODE = 1
SORT_KOREAN_KSC = 0
SORT_KOREAN_UNICODE = 1


def PRIMARYLANGID(lgid):
    return (lgid) & 1023


def SUBLANGID(lgid):
    return (lgid) >> 10


NLS_VALID_LOCALE_MASK = 1048575
CONTEXT_PORTABLE_32BIT = 1048576
CONTEXT_ALPHA = 131072
SIZE_OF_80387_REGISTERS = 80
CONTEXT_CONTROL = 1
CONTEXT_FLOATING_POINT = 2
CONTEXT_INTEGER = 4
CONTEXT_FULL = CONTEXT_CONTROL | CONTEXT_FLOATING_POINT | CONTEXT_INTEGER
PROCESS_TERMINATE = 1
PROCESS_CREATE_THREAD = 2
PROCESS_VM_OPERATION = 8
PROCESS_VM_READ = 16
PROCESS_VM_WRITE = 32
PROCESS_DUP_HANDLE = 64
PROCESS_CREATE_PROCESS = 128
PROCESS_SET_QUOTA = 256
PROCESS_SET_INFORMATION = 512
PROCESS_QUERY_INFORMATION = 1024
PROCESS_SUSPEND_RESUME = 2048
PROCESS_QUERY_LIMITED_INFORMATION = 4096
PROCESS_SET_LIMITED_INFORMATION = 8192
PROCESS_ALL_ACCESS = STANDARD_RIGHTS_REQUIRED | SYNCHRONIZE | 4095
THREAD_TERMINATE = 1
THREAD_SUSPEND_RESUME = 2
THREAD_GET_CONTEXT = 8
THREAD_SET_CONTEXT = 16
THREAD_SET_INFORMATION = 32
THREAD_QUERY_INFORMATION = 64
THREAD_SET_THREAD_TOKEN = 128
THREAD_IMPERSONATE = 256
THREAD_DIRECT_IMPERSONATION = 512
THREAD_SET_LIMITED_INFORMATION = 1024
THREAD_QUERY_LIMITED_INFORMATION = 2048
THREAD_RESUME = 4096
TLS_MINIMUM_AVAILABLE = 64
EVENT_MODIFY_STATE = 2
MUTANT_QUERY_STATE = 1
SEMAPHORE_MODIFY_STATE = 2
TIME_ZONE_ID_UNKNOWN = 0
TIME_ZONE_ID_STANDARD = 1
TIME_ZONE_ID_DAYLIGHT = 2
PROCESSOR_INTEL_386 = 386
PROCESSOR_INTEL_486 = 486
PROCESSOR_INTEL_PENTIUM = 586
PROCESSOR_INTEL_860 = 860
PROCESSOR_MIPS_R2000 = 2000
PROCESSOR_MIPS_R3000 = 3000
PROCESSOR_MIPS_R4000 = 4000
PROCESSOR_ALPHA_21064 = 21064
PROCESSOR_PPC_601 = 601
PROCESSOR_PPC_603 = 603
PROCESSOR_PPC_604 = 604
PROCESSOR_PPC_620 = 620
SECTION_QUERY = 1
SECTION_MAP_WRITE = 2
SECTION_MAP_READ = 4
SECTION_MAP_EXECUTE = 8
SECTION_EXTEND_SIZE = 16
PAGE_NOACCESS = 1
PAGE_READONLY = 2
PAGE_READWRITE = 4
PAGE_WRITECOPY = 8
PAGE_EXECUTE = 16
PAGE_EXECUTE_READ = 32
PAGE_EXECUTE_READWRITE = 64
PAGE_EXECUTE_WRITECOPY = 128
PAGE_GUARD = 256
PAGE_NOCACHE = 512
MEM_COMMIT = 4096
MEM_RESERVE = 8192
MEM_DECOMMIT = 16384
MEM_RELEASE = 32768
MEM_FREE = 65536
MEM_PRIVATE = 131072
MEM_MAPPED = 262144
MEM_TOP_DOWN = 1048576

# Generated by h2py from \msvc20\include\winnt.h
# hacked and split by mhammond.
SEC_FILE = 8388608
SEC_IMAGE = 16777216
SEC_RESERVE = ********
SEC_COMMIT = *********
SEC_NOCACHE = *********
MEM_IMAGE = SEC_IMAGE
FILE_SHARE_READ = 1
FILE_SHARE_WRITE = 2
FILE_SHARE_DELETE = 4
FILE_ATTRIBUTE_READONLY = 1
FILE_ATTRIBUTE_HIDDEN = 2
FILE_ATTRIBUTE_SYSTEM = 4
FILE_ATTRIBUTE_DIRECTORY = 16
FILE_ATTRIBUTE_ARCHIVE = 32
FILE_ATTRIBUTE_DEVICE = 64
FILE_ATTRIBUTE_NORMAL = 128
FILE_ATTRIBUTE_TEMPORARY = 256
FILE_ATTRIBUTE_SPARSE_FILE = 512
FILE_ATTRIBUTE_REPARSE_POINT = 1024
FILE_ATTRIBUTE_COMPRESSED = 2048
FILE_ATTRIBUTE_OFFLINE = 4096
FILE_ATTRIBUTE_NOT_CONTENT_INDEXED = 8192
FILE_ATTRIBUTE_ENCRYPTED = 16384
FILE_ATTRIBUTE_VIRTUAL = 65536
# These FILE_ATTRIBUTE_* flags  are apparently old definitions from Windows 95
# and conflict with current values above - but they live on for b/w compat...
FILE_ATTRIBUTE_ATOMIC_WRITE = 512
FILE_ATTRIBUTE_XACTION_WRITE = 1024

FILE_NOTIFY_CHANGE_FILE_NAME = 1
FILE_NOTIFY_CHANGE_DIR_NAME = 2
FILE_NOTIFY_CHANGE_ATTRIBUTES = 4
FILE_NOTIFY_CHANGE_SIZE = 8
FILE_NOTIFY_CHANGE_LAST_WRITE = 16
FILE_NOTIFY_CHANGE_SECURITY = 256
FILE_CASE_SENSITIVE_SEARCH = 1
FILE_CASE_PRESERVED_NAMES = 2
FILE_FILE_COMPRESSION = 16
FILE_NAMED_STREAMS = 262144
FILE_PERSISTENT_ACLS = 0x00000008
FILE_READ_ONLY_VOLUME = 0x00080000
FILE_SEQUENTIAL_WRITE_ONCE = 0x00100000
FILE_SUPPORTS_ENCRYPTION = 0x00020000
FILE_SUPPORTS_EXTENDED_ATTRIBUTES = 0x00800000
FILE_SUPPORTS_HARD_LINKS = 0x00400000
FILE_SUPPORTS_OBJECT_IDS = 0x00010000
FILE_SUPPORTS_OPEN_BY_FILE_ID = 0x01000000
FILE_SUPPORTS_REPARSE_POINTS = 0x00000080
FILE_SUPPORTS_SPARSE_FILES = 0x00000040
FILE_SUPPORTS_TRANSACTIONS = 0x00200000
FILE_SUPPORTS_USN_JOURNAL = 0x02000000
FILE_UNICODE_ON_DISK = 0x00000004
FILE_VOLUME_QUOTAS = 0x00000020
FILE_VOLUME_IS_COMPRESSED = 32768
IO_COMPLETION_MODIFY_STATE = 2
DUPLICATE_CLOSE_SOURCE = 1
DUPLICATE_SAME_ACCESS = 2
SID_MAX_SUB_AUTHORITIES = 15
SECURITY_NULL_RID = 0
SECURITY_WORLD_RID = 0
SECURITY_LOCAL_RID = 0x00000000
SECURITY_CREATOR_OWNER_RID = 0
SECURITY_CREATOR_GROUP_RID = 1
SECURITY_DIALUP_RID = 1
SECURITY_NETWORK_RID = 2
SECURITY_BATCH_RID = 3
SECURITY_INTERACTIVE_RID = 4
SECURITY_SERVICE_RID = 6
SECURITY_ANONYMOUS_LOGON_RID = 7
SECURITY_LOGON_IDS_RID = 5
SECURITY_LOGON_IDS_RID_COUNT = 3
SECURITY_LOCAL_SYSTEM_RID = 18
SECURITY_NT_NON_UNIQUE = 21
SECURITY_BUILTIN_DOMAIN_RID = 32
DOMAIN_USER_RID_ADMIN = 500
DOMAIN_USER_RID_GUEST = 501
DOMAIN_GROUP_RID_ADMINS = 512
DOMAIN_GROUP_RID_USERS = 513
DOMAIN_GROUP_RID_GUESTS = 514
DOMAIN_ALIAS_RID_ADMINS = 544
DOMAIN_ALIAS_RID_USERS = 545
DOMAIN_ALIAS_RID_GUESTS = 546
DOMAIN_ALIAS_RID_POWER_USERS = 547
DOMAIN_ALIAS_RID_ACCOUNT_OPS = 548
DOMAIN_ALIAS_RID_SYSTEM_OPS = 549
DOMAIN_ALIAS_RID_PRINT_OPS = 550
DOMAIN_ALIAS_RID_BACKUP_OPS = 551
DOMAIN_ALIAS_RID_REPLICATOR = 552
SE_GROUP_MANDATORY = 1
SE_GROUP_ENABLED_BY_DEFAULT = 2
SE_GROUP_ENABLED = 4
SE_GROUP_OWNER = 8
SE_GROUP_LOGON_ID = -**********
ACL_REVISION = 2
ACL_REVISION1 = 1
ACL_REVISION2 = 2
ACCESS_ALLOWED_ACE_TYPE = 0
ACCESS_DENIED_ACE_TYPE = 1
SYSTEM_AUDIT_ACE_TYPE = 2
SYSTEM_ALARM_ACE_TYPE = 3
OBJECT_INHERIT_ACE = 1
CONTAINER_INHERIT_ACE = 2
NO_PROPAGATE_INHERIT_ACE = 4
INHERIT_ONLY_ACE = 8
VALID_INHERIT_FLAGS = 15
SUCCESSFUL_ACCESS_ACE_FLAG = 64
FAILED_ACCESS_ACE_FLAG = 128
SECURITY_DESCRIPTOR_REVISION = 1
SECURITY_DESCRIPTOR_REVISION1 = 1
SECURITY_DESCRIPTOR_MIN_LENGTH = 20
SE_OWNER_DEFAULTED = 1
SE_GROUP_DEFAULTED = 2
SE_DACL_PRESENT = 4
SE_DACL_DEFAULTED = 8
SE_SACL_PRESENT = 16
SE_SACL_DEFAULTED = 32
SE_SELF_RELATIVE = 32768
SE_PRIVILEGE_ENABLED_BY_DEFAULT = 1
SE_PRIVILEGE_ENABLED = 2
SE_PRIVILEGE_USED_FOR_ACCESS = -**********
PRIVILEGE_SET_ALL_NECESSARY = 1
SE_CREATE_TOKEN_NAME = "SeCreateTokenPrivilege"
SE_ASSIGNPRIMARYTOKEN_NAME = "SeAssignPrimaryTokenPrivilege"
SE_LOCK_MEMORY_NAME = "SeLockMemoryPrivilege"
SE_INCREASE_QUOTA_NAME = "SeIncreaseQuotaPrivilege"
SE_UNSOLICITED_INPUT_NAME = "SeUnsolicitedInputPrivilege"
SE_MACHINE_ACCOUNT_NAME = "SeMachineAccountPrivilege"
SE_TCB_NAME = "SeTcbPrivilege"
SE_SECURITY_NAME = "SeSecurityPrivilege"
SE_TAKE_OWNERSHIP_NAME = "SeTakeOwnershipPrivilege"
SE_LOAD_DRIVER_NAME = "SeLoadDriverPrivilege"
SE_SYSTEM_PROFILE_NAME = "SeSystemProfilePrivilege"
SE_SYSTEMTIME_NAME = "SeSystemtimePrivilege"
SE_PROF_SINGLE_PROCESS_NAME = "SeProfileSingleProcessPrivilege"
SE_INC_BASE_PRIORITY_NAME = "SeIncreaseBasePriorityPrivilege"
SE_CREATE_PAGEFILE_NAME = "SeCreatePagefilePrivilege"
SE_CREATE_PERMANENT_NAME = "SeCreatePermanentPrivilege"
SE_BACKUP_NAME = "SeBackupPrivilege"
SE_RESTORE_NAME = "SeRestorePrivilege"
SE_SHUTDOWN_NAME = "SeShutdownPrivilege"
SE_DEBUG_NAME = "SeDebugPrivilege"
SE_AUDIT_NAME = "SeAuditPrivilege"
SE_SYSTEM_ENVIRONMENT_NAME = "SeSystemEnvironmentPrivilege"
SE_CHANGE_NOTIFY_NAME = "SeChangeNotifyPrivilege"
SE_REMOTE_SHUTDOWN_NAME = "SeRemoteShutdownPrivilege"

TOKEN_ASSIGN_PRIMARY = 1
TOKEN_DUPLICATE = 2
TOKEN_IMPERSONATE = 4
TOKEN_QUERY = 8
TOKEN_QUERY_SOURCE = 16
TOKEN_ADJUST_PRIVILEGES = 32
TOKEN_ADJUST_GROUPS = 64
TOKEN_ADJUST_DEFAULT = 128
TOKEN_ADJUST_SESSIONID = 256
TOKEN_ALL_ACCESS = (
    STANDARD_RIGHTS_REQUIRED
    | TOKEN_ASSIGN_PRIMARY
    | TOKEN_DUPLICATE
    | TOKEN_IMPERSONATE
    | TOKEN_QUERY
    | TOKEN_QUERY_SOURCE
    | TOKEN_ADJUST_PRIVILEGES
    | TOKEN_ADJUST_GROUPS
    | TOKEN_ADJUST_DEFAULT
    | TOKEN_ADJUST_SESSIONID
)
TOKEN_READ = STANDARD_RIGHTS_READ | TOKEN_QUERY
TOKEN_WRITE = (
    STANDARD_RIGHTS_WRITE
    | TOKEN_ADJUST_PRIVILEGES
    | TOKEN_ADJUST_GROUPS
    | TOKEN_ADJUST_DEFAULT
)
TOKEN_EXECUTE = STANDARD_RIGHTS_EXECUTE
TOKEN_SOURCE_LENGTH = 8

KEY_QUERY_VALUE = 1
KEY_SET_VALUE = 2
KEY_CREATE_SUB_KEY = 4
KEY_ENUMERATE_SUB_KEYS = 8
KEY_NOTIFY = 16
KEY_CREATE_LINK = 32
KEY_WOW64_32KEY = 512
KEY_WOW64_64KEY = 256
KEY_WOW64_RES = 768
KEY_READ = (
    STANDARD_RIGHTS_READ | KEY_QUERY_VALUE | KEY_ENUMERATE_SUB_KEYS | KEY_NOTIFY
) & (~SYNCHRONIZE)
KEY_WRITE = (STANDARD_RIGHTS_WRITE | KEY_SET_VALUE | KEY_CREATE_SUB_KEY) & (
    ~SYNCHRONIZE
)
KEY_EXECUTE = (KEY_READ) & (~SYNCHRONIZE)
KEY_ALL_ACCESS = (
    STANDARD_RIGHTS_ALL
    | KEY_QUERY_VALUE
    | KEY_SET_VALUE
    | KEY_CREATE_SUB_KEY
    | KEY_ENUMERATE_SUB_KEYS
    | KEY_NOTIFY
    | KEY_CREATE_LINK
) & (~SYNCHRONIZE)
REG_NOTIFY_CHANGE_ATTRIBUTES = 2
REG_NOTIFY_CHANGE_SECURITY = 8
REG_NONE = 0  # No value type
REG_SZ = 1  # Unicode nul terminated string
REG_EXPAND_SZ = 2  # Unicode nul terminated string
# (with environment variable references)
REG_BINARY = 3  # Free form binary
REG_DWORD = 4  # 32-bit number
REG_DWORD_LITTLE_ENDIAN = 4  # 32-bit number (same as REG_DWORD)
REG_DWORD_BIG_ENDIAN = 5  # 32-bit number
REG_LINK = 6  # Symbolic Link (unicode)
REG_MULTI_SZ = 7  # Multiple Unicode strings
REG_RESOURCE_LIST = 8  # Resource list in the resource map
REG_FULL_RESOURCE_DESCRIPTOR = 9  # Resource list in the hardware description
REG_RESOURCE_REQUIREMENTS_LIST = 10
REG_QWORD = 11  # 64-bit number
REG_QWORD_LITTLE_ENDIAN = 11  # 64-bit number (same as REG_QWORD)


# Generated by h2py from \msvc20\include\winnt.h
# hacked and split by mhammond.
# Included from string.h
_NLSCMPERROR = 2147483647
NULL = 0
HEAP_NO_SERIALIZE = 1
HEAP_GROWABLE = 2
HEAP_GENERATE_EXCEPTIONS = 4
HEAP_ZERO_MEMORY = 8
HEAP_REALLOC_IN_PLACE_ONLY = 16
HEAP_TAIL_CHECKING_ENABLED = 32
HEAP_FREE_CHECKING_ENABLED = 64
HEAP_DISABLE_COALESCE_ON_FREE = 128
IS_TEXT_UNICODE_ASCII16 = 1
IS_TEXT_UNICODE_REVERSE_ASCII16 = 16
IS_TEXT_UNICODE_STATISTICS = 2
IS_TEXT_UNICODE_REVERSE_STATISTICS = 32
IS_TEXT_UNICODE_CONTROLS = 4
IS_TEXT_UNICODE_REVERSE_CONTROLS = 64
IS_TEXT_UNICODE_SIGNATURE = 8
IS_TEXT_UNICODE_REVERSE_SIGNATURE = 128
IS_TEXT_UNICODE_ILLEGAL_CHARS = 256
IS_TEXT_UNICODE_ODD_LENGTH = 512
IS_TEXT_UNICODE_DBCS_LEADBYTE = 1024
IS_TEXT_UNICODE_NULL_BYTES = 4096
IS_TEXT_UNICODE_UNICODE_MASK = 15
IS_TEXT_UNICODE_REVERSE_MASK = 240
IS_TEXT_UNICODE_NOT_UNICODE_MASK = 3840
IS_TEXT_UNICODE_NOT_ASCII_MASK = 61440
COMPRESSION_FORMAT_NONE = 0
COMPRESSION_FORMAT_DEFAULT = 1
COMPRESSION_FORMAT_LZNT1 = 2
COMPRESSION_ENGINE_STANDARD = 0
COMPRESSION_ENGINE_MAXIMUM = 256
MESSAGE_RESOURCE_UNICODE = 1
RTL_CRITSECT_TYPE = 0
RTL_RESOURCE_TYPE = 1
DLL_PROCESS_ATTACH = 1
DLL_THREAD_ATTACH = 2
DLL_THREAD_DETACH = 3
DLL_PROCESS_DETACH = 0
EVENTLOG_SEQUENTIAL_READ = 0x0001
EVENTLOG_SEEK_READ = 0x0002
EVENTLOG_FORWARDS_READ = 0x0004
EVENTLOG_BACKWARDS_READ = 0x0008
EVENTLOG_SUCCESS = 0x0000
EVENTLOG_ERROR_TYPE = 1
EVENTLOG_WARNING_TYPE = 2
EVENTLOG_INFORMATION_TYPE = 4
EVENTLOG_AUDIT_SUCCESS = 8
EVENTLOG_AUDIT_FAILURE = 16
EVENTLOG_START_PAIRED_EVENT = 1
EVENTLOG_END_PAIRED_EVENT = 2
EVENTLOG_END_ALL_PAIRED_EVENTS = 4
EVENTLOG_PAIRED_EVENT_ACTIVE = 8
EVENTLOG_PAIRED_EVENT_INACTIVE = 16
# Generated by h2py from \msvc20\include\winnt.h
# hacked and split by mhammond.
OWNER_SECURITY_INFORMATION = 0x00000001
GROUP_SECURITY_INFORMATION = 0x00000002
DACL_SECURITY_INFORMATION = 0x00000004
SACL_SECURITY_INFORMATION = 0x00000008
IMAGE_SIZEOF_FILE_HEADER = 20
IMAGE_FILE_MACHINE_UNKNOWN = 0
IMAGE_NUMBEROF_DIRECTORY_ENTRIES = 16
IMAGE_SIZEOF_ROM_OPTIONAL_HEADER = 56
IMAGE_SIZEOF_STD_OPTIONAL_HEADER = 28
IMAGE_SIZEOF_NT_OPTIONAL_HEADER = 224
IMAGE_NT_OPTIONAL_HDR_MAGIC = 267
IMAGE_ROM_OPTIONAL_HDR_MAGIC = 263
IMAGE_SIZEOF_SHORT_NAME = 8
IMAGE_SIZEOF_SECTION_HEADER = 40
IMAGE_SIZEOF_SYMBOL = 18
IMAGE_SYM_CLASS_NULL = 0
IMAGE_SYM_CLASS_AUTOMATIC = 1
IMAGE_SYM_CLASS_EXTERNAL = 2
IMAGE_SYM_CLASS_STATIC = 3
IMAGE_SYM_CLASS_REGISTER = 4
IMAGE_SYM_CLASS_EXTERNAL_DEF = 5
IMAGE_SYM_CLASS_LABEL = 6
IMAGE_SYM_CLASS_UNDEFINED_LABEL = 7
IMAGE_SYM_CLASS_MEMBER_OF_STRUCT = 8
IMAGE_SYM_CLASS_ARGUMENT = 9
IMAGE_SYM_CLASS_STRUCT_TAG = 10
IMAGE_SYM_CLASS_MEMBER_OF_UNION = 11
IMAGE_SYM_CLASS_UNION_TAG = 12
IMAGE_SYM_CLASS_TYPE_DEFINITION = 13
IMAGE_SYM_CLASS_UNDEFINED_STATIC = 14
IMAGE_SYM_CLASS_ENUM_TAG = 15
IMAGE_SYM_CLASS_MEMBER_OF_ENUM = 16
IMAGE_SYM_CLASS_REGISTER_PARAM = 17
IMAGE_SYM_CLASS_BIT_FIELD = 18
IMAGE_SYM_CLASS_BLOCK = 100
IMAGE_SYM_CLASS_FUNCTION = 101
IMAGE_SYM_CLASS_END_OF_STRUCT = 102
IMAGE_SYM_CLASS_FILE = 103
IMAGE_SYM_CLASS_SECTION = 104
IMAGE_SYM_CLASS_WEAK_EXTERNAL = 105
N_BTMASK = 15
N_TMASK = 48
N_TMASK1 = 192
N_TMASK2 = 240
N_BTSHFT = 4
N_TSHIFT = 2
IMAGE_SIZEOF_AUX_SYMBOL = 18
IMAGE_COMDAT_SELECT_NODUPLICATES = 1
IMAGE_COMDAT_SELECT_ANY = 2
IMAGE_COMDAT_SELECT_SAME_SIZE = 3
IMAGE_COMDAT_SELECT_EXACT_MATCH = 4
IMAGE_COMDAT_SELECT_ASSOCIATIVE = 5
IMAGE_WEAK_EXTERN_SEARCH_NOLIBRARY = 1
IMAGE_WEAK_EXTERN_SEARCH_LIBRARY = 2
IMAGE_WEAK_EXTERN_SEARCH_ALIAS = 3
IMAGE_SIZEOF_RELOCATION = 10
IMAGE_REL_I386_SECTION = 10
IMAGE_REL_I386_SECREL = 11
IMAGE_REL_MIPS_REFHALF = 1
IMAGE_REL_MIPS_REFWORD = 2
IMAGE_REL_MIPS_JMPADDR = 3
IMAGE_REL_MIPS_REFHI = 4
IMAGE_REL_MIPS_REFLO = 5
IMAGE_REL_MIPS_GPREL = 6
IMAGE_REL_MIPS_LITERAL = 7
IMAGE_REL_MIPS_SECTION = 10
IMAGE_REL_MIPS_SECREL = 11
IMAGE_REL_MIPS_REFWORDNB = 34
IMAGE_REL_MIPS_PAIR = 37
IMAGE_REL_ALPHA_ABSOLUTE = 0
IMAGE_REL_ALPHA_REFLONG = 1
IMAGE_REL_ALPHA_REFQUAD = 2
IMAGE_REL_ALPHA_GPREL32 = 3
IMAGE_REL_ALPHA_LITERAL = 4
IMAGE_REL_ALPHA_LITUSE = 5
IMAGE_REL_ALPHA_GPDISP = 6
IMAGE_REL_ALPHA_BRADDR = 7
IMAGE_REL_ALPHA_HINT = 8
IMAGE_REL_ALPHA_INLINE_REFLONG = 9
IMAGE_REL_ALPHA_REFHI = 10
IMAGE_REL_ALPHA_REFLO = 11
IMAGE_REL_ALPHA_PAIR = 12
IMAGE_REL_ALPHA_MATCH = 13
IMAGE_REL_ALPHA_SECTION = 14
IMAGE_REL_ALPHA_SECREL = 15
IMAGE_REL_ALPHA_REFLONGNB = 16
IMAGE_SIZEOF_BASE_RELOCATION = 8
IMAGE_REL_BASED_ABSOLUTE = 0
IMAGE_REL_BASED_HIGH = 1
IMAGE_REL_BASED_LOW = 2
IMAGE_REL_BASED_HIGHLOW = 3
IMAGE_REL_BASED_HIGHADJ = 4
IMAGE_REL_BASED_MIPS_JMPADDR = 5
IMAGE_SIZEOF_LINENUMBER = 6
IMAGE_ARCHIVE_START_SIZE = 8
IMAGE_ARCHIVE_START = "!<arch>\n"
IMAGE_ARCHIVE_END = "`\n"
IMAGE_ARCHIVE_PAD = "\n"
IMAGE_ARCHIVE_LINKER_MEMBER = "/               "
IMAGE_ARCHIVE_LONGNAMES_MEMBER = "//              "
IMAGE_SIZEOF_ARCHIVE_MEMBER_HDR = 60
IMAGE_ORDINAL_FLAG = -**********


def IMAGE_SNAP_BY_ORDINAL(Ordinal):
    return (Ordinal & IMAGE_ORDINAL_FLAG) != 0


def IMAGE_ORDINAL(Ordinal):
    return Ordinal & 65535


IMAGE_RESOURCE_NAME_IS_STRING = -**********
IMAGE_RESOURCE_DATA_IS_DIRECTORY = -**********
IMAGE_DEBUG_TYPE_UNKNOWN = 0
IMAGE_DEBUG_TYPE_COFF = 1
IMAGE_DEBUG_TYPE_CODEVIEW = 2
IMAGE_DEBUG_TYPE_FPO = 3
IMAGE_DEBUG_TYPE_MISC = 4
IMAGE_DEBUG_TYPE_EXCEPTION = 5
IMAGE_DEBUG_TYPE_FIXUP = 6
IMAGE_DEBUG_TYPE_OMAP_TO_SRC = 7
IMAGE_DEBUG_TYPE_OMAP_FROM_SRC = 8
FRAME_FPO = 0
FRAME_TRAP = 1
FRAME_TSS = 2
SIZEOF_RFPO_DATA = 16
IMAGE_DEBUG_MISC_EXENAME = 1
IMAGE_SEPARATE_DEBUG_SIGNATURE = 18756
# Generated by h2py from \msvcnt\include\wingdi.h
# hacked and split manually by mhammond.
NEWFRAME = 1
ABORTDOC = 2
NEXTBAND = 3
SETCOLORTABLE = 4
GETCOLORTABLE = 5
FLUSHOUTPUT = 6
DRAFTMODE = 7
QUERYESCSUPPORT = 8
SETABORTPROC = 9
STARTDOC = 10
ENDDOC = 11
GETPHYSPAGESIZE = 12
GETPRINTINGOFFSET = 13
GETSCALINGFACTOR = 14
MFCOMMENT = 15
GETPENWIDTH = 16
SETCOPYCOUNT = 17
SELECTPAPERSOURCE = 18
DEVICEDATA = 19
PASSTHROUGH = 19
GETTECHNOLGY = 20
GETTECHNOLOGY = 20
SETLINECAP = 21
SETLINEJOIN = 22
SETMITERLIMIT = 23
BANDINFO = 24
DRAWPATTERNRECT = 25
GETVECTORPENSIZE = 26
GETVECTORBRUSHSIZE = 27
ENABLEDUPLEX = 28
GETSETPAPERBINS = 29
GETSETPRINTORIENT = 30
ENUMPAPERBINS = 31
SETDIBSCALING = 32
EPSPRINTING = 33
ENUMPAPERMETRICS = 34
GETSETPAPERMETRICS = 35
POSTSCRIPT_DATA = 37
POSTSCRIPT_IGNORE = 38
MOUSETRAILS = 39
GETDEVICEUNITS = 42
GETEXTENDEDTEXTMETRICS = 256
GETEXTENTTABLE = 257
GETPAIRKERNTABLE = 258
GETTRACKKERNTABLE = 259
EXTTEXTOUT = 512
GETFACENAME = 513
DOWNLOADFACE = 514
ENABLERELATIVEWIDTHS = 768
ENABLEPAIRKERNING = 769
SETKERNTRACK = 770
SETALLJUSTVALUES = 771
SETCHARSET = 772
STRETCHBLT = 2048
GETSETSCREENPARAMS = 3072
BEGIN_PATH = 4096
CLIP_TO_PATH = 4097
END_PATH = 4098
EXT_DEVICE_CAPS = 4099
RESTORE_CTM = 4100
SAVE_CTM = 4101
SET_ARC_DIRECTION = 4102
SET_BACKGROUND_COLOR = 4103
SET_POLY_MODE = 4104
SET_SCREEN_ANGLE = 4105
SET_SPREAD = 4106
TRANSFORM_CTM = 4107
SET_CLIP_BOX = 4108
SET_BOUNDS = 4109
SET_MIRROR_MODE = 4110
OPENCHANNEL = 4110
DOWNLOADHEADER = 4111
CLOSECHANNEL = 4112
POSTSCRIPT_PASSTHROUGH = 4115
ENCAPSULATED_POSTSCRIPT = 4116
SP_NOTREPORTED = 16384
SP_ERROR = -1
SP_APPABORT = -2
SP_USERABORT = -3
SP_OUTOFDISK = -4
SP_OUTOFMEMORY = -5
PR_JOBSTATUS = 0

## GDI object types
OBJ_PEN = 1
OBJ_BRUSH = 2
OBJ_DC = 3
OBJ_METADC = 4
OBJ_PAL = 5
OBJ_FONT = 6
OBJ_BITMAP = 7
OBJ_REGION = 8
OBJ_METAFILE = 9
OBJ_MEMDC = 10
OBJ_EXTPEN = 11
OBJ_ENHMETADC = 12
OBJ_ENHMETAFILE = 13
OBJ_COLORSPACE = 14

MWT_IDENTITY = 1
MWT_LEFTMULTIPLY = 2
MWT_RIGHTMULTIPLY = 3
MWT_MIN = MWT_IDENTITY
MWT_MAX = MWT_RIGHTMULTIPLY
BI_RGB = 0
BI_RLE8 = 1
BI_RLE4 = 2
BI_BITFIELDS = 3
TMPF_FIXED_PITCH = 1
TMPF_VECTOR = 2
TMPF_DEVICE = 8
TMPF_TRUETYPE = 4
NTM_REGULAR = 64
NTM_BOLD = 32
NTM_ITALIC = 1
LF_FACESIZE = 32
LF_FULLFACESIZE = 64
OUT_DEFAULT_PRECIS = 0
OUT_STRING_PRECIS = 1
OUT_CHARACTER_PRECIS = 2
OUT_STROKE_PRECIS = 3
OUT_TT_PRECIS = 4
OUT_DEVICE_PRECIS = 5
OUT_RASTER_PRECIS = 6
OUT_TT_ONLY_PRECIS = 7
OUT_OUTLINE_PRECIS = 8
CLIP_DEFAULT_PRECIS = 0
CLIP_CHARACTER_PRECIS = 1
CLIP_STROKE_PRECIS = 2
CLIP_MASK = 15
CLIP_LH_ANGLES = 1 << 4
CLIP_TT_ALWAYS = 2 << 4
CLIP_EMBEDDED = 8 << 4
DEFAULT_QUALITY = 0
DRAFT_QUALITY = 1
PROOF_QUALITY = 2
NONANTIALIASED_QUALITY = 3
ANTIALIASED_QUALITY = 4
CLEARTYPE_QUALITY = 5
CLEARTYPE_NATURAL_QUALITY = 6
DEFAULT_PITCH = 0
FIXED_PITCH = 1
VARIABLE_PITCH = 2
ANSI_CHARSET = 0
DEFAULT_CHARSET = 1
SYMBOL_CHARSET = 2
SHIFTJIS_CHARSET = 128
HANGEUL_CHARSET = 129
CHINESEBIG5_CHARSET = 136
OEM_CHARSET = 255
JOHAB_CHARSET = 130
HEBREW_CHARSET = 177
ARABIC_CHARSET = 178
GREEK_CHARSET = 161
TURKISH_CHARSET = 162
VIETNAMESE_CHARSET = 163
THAI_CHARSET = 222
EASTEUROPE_CHARSET = 238
RUSSIAN_CHARSET = 204
MAC_CHARSET = 77
BALTIC_CHARSET = 186
FF_DONTCARE = 0 << 4
FF_ROMAN = 1 << 4
FF_SWISS = 2 << 4
FF_MODERN = 3 << 4
FF_SCRIPT = 4 << 4
FF_DECORATIVE = 5 << 4
FW_DONTCARE = 0
FW_THIN = 100
FW_EXTRALIGHT = 200
FW_LIGHT = 300
FW_NORMAL = 400
FW_MEDIUM = 500
FW_SEMIBOLD = 600
FW_BOLD = 700
FW_EXTRABOLD = 800
FW_HEAVY = 900
FW_ULTRALIGHT = FW_EXTRALIGHT
FW_REGULAR = FW_NORMAL
FW_DEMIBOLD = FW_SEMIBOLD
FW_ULTRABOLD = FW_EXTRABOLD
FW_BLACK = FW_HEAVY
# Generated by h2py from \msvcnt\include\wingdi.h
# hacked and split manually by mhammond.
BS_SOLID = 0
BS_NULL = 1
BS_HOLLOW = BS_NULL
BS_HATCHED = 2
BS_PATTERN = 3
BS_INDEXED = 4
BS_DIBPATTERN = 5
BS_DIBPATTERNPT = 6
BS_PATTERN8X8 = 7
BS_DIBPATTERN8X8 = 8
HS_HORIZONTAL = 0
HS_VERTICAL = 1
HS_FDIAGONAL = 2
HS_BDIAGONAL = 3
HS_CROSS = 4
HS_DIAGCROSS = 5
HS_FDIAGONAL1 = 6
HS_BDIAGONAL1 = 7
HS_SOLID = 8
HS_DENSE1 = 9
HS_DENSE2 = 10
HS_DENSE3 = 11
HS_DENSE4 = 12
HS_DENSE5 = 13
HS_DENSE6 = 14
HS_DENSE7 = 15
HS_DENSE8 = 16
HS_NOSHADE = 17
HS_HALFTONE = 18
HS_SOLIDCLR = 19
HS_DITHEREDCLR = 20
HS_SOLIDTEXTCLR = 21
HS_DITHEREDTEXTCLR = 22
HS_SOLIDBKCLR = 23
HS_DITHEREDBKCLR = 24
HS_API_MAX = 25
PS_SOLID = 0
PS_DASH = 1
PS_DOT = 2
PS_DASHDOT = 3
PS_DASHDOTDOT = 4
PS_NULL = 5
PS_INSIDEFRAME = 6
PS_USERSTYLE = 7
PS_ALTERNATE = 8
PS_STYLE_MASK = 15
PS_ENDCAP_ROUND = 0
PS_ENDCAP_SQUARE = 256
PS_ENDCAP_FLAT = 512
PS_ENDCAP_MASK = 3840
PS_JOIN_ROUND = 0
PS_JOIN_BEVEL = 4096
PS_JOIN_MITER = 8192
PS_JOIN_MASK = 61440
PS_COSMETIC = 0
PS_GEOMETRIC = 65536
PS_TYPE_MASK = 983040
AD_COUNTERCLOCKWISE = 1
AD_CLOCKWISE = 2
DRIVERVERSION = 0
TECHNOLOGY = 2
HORZSIZE = 4
VERTSIZE = 6
HORZRES = 8
VERTRES = 10
BITSPIXEL = 12
PLANES = 14
NUMBRUSHES = 16
NUMPENS = 18
NUMMARKERS = 20
NUMFONTS = 22
NUMCOLORS = 24
PDEVICESIZE = 26
CURVECAPS = 28
LINECAPS = 30
POLYGONALCAPS = 32
TEXTCAPS = 34
CLIPCAPS = 36
RASTERCAPS = 38
ASPECTX = 40
ASPECTY = 42
ASPECTXY = 44
LOGPIXELSX = 88
LOGPIXELSY = 90
SIZEPALETTE = 104
NUMRESERVED = 106
COLORRES = 108

PHYSICALWIDTH = 110
PHYSICALHEIGHT = 111
PHYSICALOFFSETX = 112
PHYSICALOFFSETY = 113
SCALINGFACTORX = 114
SCALINGFACTORY = 115
VREFRESH = 116
DESKTOPVERTRES = 117
DESKTOPHORZRES = 118
BLTALIGNMENT = 119
SHADEBLENDCAPS = 120
COLORMGMTCAPS = 121

DT_PLOTTER = 0
DT_RASDISPLAY = 1
DT_RASPRINTER = 2
DT_RASCAMERA = 3
DT_CHARSTREAM = 4
DT_METAFILE = 5
DT_DISPFILE = 6
CC_NONE = 0
CC_CIRCLES = 1
CC_PIE = 2
CC_CHORD = 4
CC_ELLIPSES = 8
CC_WIDE = 16
CC_STYLED = 32
CC_WIDESTYLED = 64
CC_INTERIORS = 128
CC_ROUNDRECT = 256
LC_NONE = 0
LC_POLYLINE = 2
LC_MARKER = 4
LC_POLYMARKER = 8
LC_WIDE = 16
LC_STYLED = 32
LC_WIDESTYLED = 64
LC_INTERIORS = 128
PC_NONE = 0
PC_POLYGON = 1
PC_RECTANGLE = 2
PC_WINDPOLYGON = 4
PC_TRAPEZOID = 4
PC_SCANLINE = 8
PC_WIDE = 16
PC_STYLED = 32
PC_WIDESTYLED = 64
PC_INTERIORS = 128
CP_NONE = 0
CP_RECTANGLE = 1
CP_REGION = 2
TC_OP_CHARACTER = 1
TC_OP_STROKE = 2
TC_CP_STROKE = 4
TC_CR_90 = 8
TC_CR_ANY = 16
TC_SF_X_YINDEP = 32
TC_SA_DOUBLE = 64
TC_SA_INTEGER = 128
TC_SA_CONTIN = 256
TC_EA_DOUBLE = 512
TC_IA_ABLE = 1024
TC_UA_ABLE = 2048
TC_SO_ABLE = 4096
TC_RA_ABLE = 8192
TC_VA_ABLE = 16384
TC_RESERVED = 32768
TC_SCROLLBLT = 65536
RC_BITBLT = 1
RC_BANDING = 2
RC_SCALING = 4
RC_BITMAP64 = 8
RC_GDI20_OUTPUT = 16
RC_GDI20_STATE = 32
RC_SAVEBITMAP = 64
RC_DI_BITMAP = 128
RC_PALETTE = 256
RC_DIBTODEV = 512
RC_BIGFONT = 1024
RC_STRETCHBLT = 2048
RC_FLOODFILL = 4096
RC_STRETCHDIB = 8192
RC_OP_DX_OUTPUT = 16384
RC_DEVBITS = 32768
DIB_RGB_COLORS = 0
DIB_PAL_COLORS = 1
DIB_PAL_INDICES = 2
DIB_PAL_PHYSINDICES = 2
DIB_PAL_LOGINDICES = 4
SYSPAL_ERROR = 0
SYSPAL_STATIC = 1
SYSPAL_NOSTATIC = 2
CBM_CREATEDIB = 2
CBM_INIT = 4
FLOODFILLBORDER = 0
FLOODFILLSURFACE = 1
CCHFORMNAME = 32
# Generated by h2py from \msvcnt\include\wingdi.h
# hacked and split manually by mhammond.

# DEVMODE.dmFields
DM_SPECVERSION = 800
DM_ORIENTATION = 1
DM_PAPERSIZE = 2
DM_PAPERLENGTH = 4
DM_PAPERWIDTH = 8
DM_SCALE = 16
DM_POSITION = 32
DM_NUP = 64
DM_DISPLAYORIENTATION = 128
DM_COPIES = 256
DM_DEFAULTSOURCE = 512
DM_PRINTQUALITY = 1024
DM_COLOR = 2048
DM_DUPLEX = 4096
DM_YRESOLUTION = 8192
DM_TTOPTION = 16384
DM_COLLATE = 32768
DM_FORMNAME = 65536
DM_LOGPIXELS = 131072
DM_BITSPERPEL = 262144
DM_PELSWIDTH = 524288
DM_PELSHEIGHT = 1048576
DM_DISPLAYFLAGS = 2097152
DM_DISPLAYFREQUENCY = 4194304
DM_ICMMETHOD = 8388608
DM_ICMINTENT = 16777216
DM_MEDIATYPE = 33554432
DM_DITHERTYPE = ********
DM_PANNINGWIDTH = *********
DM_PANNINGHEIGHT = *********
DM_DISPLAYFIXEDOUTPUT = *********

# DEVMODE.dmOrientation
DMORIENT_PORTRAIT = 1
DMORIENT_LANDSCAPE = 2

# DEVMODE.dmDisplayOrientation
DMDO_DEFAULT = 0
DMDO_90 = 1
DMDO_180 = 2
DMDO_270 = 3

# DEVMODE.dmDisplayFixedOutput
DMDFO_DEFAULT = 0
DMDFO_STRETCH = 1
DMDFO_CENTER = 2

# DEVMODE.dmPaperSize
DMPAPER_LETTER = 1
DMPAPER_LETTERSMALL = 2
DMPAPER_TABLOID = 3
DMPAPER_LEDGER = 4
DMPAPER_LEGAL = 5
DMPAPER_STATEMENT = 6
DMPAPER_EXECUTIVE = 7
DMPAPER_A3 = 8
DMPAPER_A4 = 9
DMPAPER_A4SMALL = 10
DMPAPER_A5 = 11
DMPAPER_B4 = 12
DMPAPER_B5 = 13
DMPAPER_FOLIO = 14
DMPAPER_QUARTO = 15
DMPAPER_10X14 = 16
DMPAPER_11X17 = 17
DMPAPER_NOTE = 18
DMPAPER_ENV_9 = 19
DMPAPER_ENV_10 = 20
DMPAPER_ENV_11 = 21
DMPAPER_ENV_12 = 22
DMPAPER_ENV_14 = 23
DMPAPER_CSHEET = 24
DMPAPER_DSHEET = 25
DMPAPER_ESHEET = 26
DMPAPER_ENV_DL = 27
DMPAPER_ENV_C5 = 28
DMPAPER_ENV_C3 = 29
DMPAPER_ENV_C4 = 30
DMPAPER_ENV_C6 = 31
DMPAPER_ENV_C65 = 32
DMPAPER_ENV_B4 = 33
DMPAPER_ENV_B5 = 34
DMPAPER_ENV_B6 = 35
DMPAPER_ENV_ITALY = 36
DMPAPER_ENV_MONARCH = 37
DMPAPER_ENV_PERSONAL = 38
DMPAPER_FANFOLD_US = 39
DMPAPER_FANFOLD_STD_GERMAN = 40
DMPAPER_FANFOLD_LGL_GERMAN = 41
DMPAPER_ISO_B4 = 42
DMPAPER_JAPANESE_POSTCARD = 43
DMPAPER_9X11 = 44
DMPAPER_10X11 = 45
DMPAPER_15X11 = 46
DMPAPER_ENV_INVITE = 47
DMPAPER_RESERVED_48 = 48
DMPAPER_RESERVED_49 = 49
DMPAPER_LETTER_EXTRA = 50
DMPAPER_LEGAL_EXTRA = 51
DMPAPER_TABLOID_EXTRA = 52
DMPAPER_A4_EXTRA = 53
DMPAPER_LETTER_TRANSVERSE = 54
DMPAPER_A4_TRANSVERSE = 55
DMPAPER_LETTER_EXTRA_TRANSVERSE = 56
DMPAPER_A_PLUS = 57
DMPAPER_B_PLUS = 58
DMPAPER_LETTER_PLUS = 59
DMPAPER_A4_PLUS = 60
DMPAPER_A5_TRANSVERSE = 61
DMPAPER_B5_TRANSVERSE = 62
DMPAPER_A3_EXTRA = 63
DMPAPER_A5_EXTRA = 64
DMPAPER_B5_EXTRA = 65
DMPAPER_A2 = 66
DMPAPER_A3_TRANSVERSE = 67
DMPAPER_A3_EXTRA_TRANSVERSE = 68
DMPAPER_DBL_JAPANESE_POSTCARD = 69
DMPAPER_A6 = 70
DMPAPER_JENV_KAKU2 = 71
DMPAPER_JENV_KAKU3 = 72
DMPAPER_JENV_CHOU3 = 73
DMPAPER_JENV_CHOU4 = 74
DMPAPER_LETTER_ROTATED = 75
DMPAPER_A3_ROTATED = 76
DMPAPER_A4_ROTATED = 77
DMPAPER_A5_ROTATED = 78
DMPAPER_B4_JIS_ROTATED = 79
DMPAPER_B5_JIS_ROTATED = 80
DMPAPER_JAPANESE_POSTCARD_ROTATED = 81
DMPAPER_DBL_JAPANESE_POSTCARD_ROTATED = 82
DMPAPER_A6_ROTATED = 83
DMPAPER_JENV_KAKU2_ROTATED = 84
DMPAPER_JENV_KAKU3_ROTATED = 85
DMPAPER_JENV_CHOU3_ROTATED = 86
DMPAPER_JENV_CHOU4_ROTATED = 87
DMPAPER_B6_JIS = 88
DMPAPER_B6_JIS_ROTATED = 89
DMPAPER_12X11 = 90
DMPAPER_JENV_YOU4 = 91
DMPAPER_JENV_YOU4_ROTATED = 92
DMPAPER_P16K = 93
DMPAPER_P32K = 94
DMPAPER_P32KBIG = 95
DMPAPER_PENV_1 = 96
DMPAPER_PENV_2 = 97
DMPAPER_PENV_3 = 98
DMPAPER_PENV_4 = 99
DMPAPER_PENV_5 = 100
DMPAPER_PENV_6 = 101
DMPAPER_PENV_7 = 102
DMPAPER_PENV_8 = 103
DMPAPER_PENV_9 = 104
DMPAPER_PENV_10 = 105
DMPAPER_P16K_ROTATED = 106
DMPAPER_P32K_ROTATED = 107
DMPAPER_P32KBIG_ROTATED = 108
DMPAPER_PENV_1_ROTATED = 109
DMPAPER_PENV_2_ROTATED = 110
DMPAPER_PENV_3_ROTATED = 111
DMPAPER_PENV_4_ROTATED = 112
DMPAPER_PENV_5_ROTATED = 113
DMPAPER_PENV_6_ROTATED = 114
DMPAPER_PENV_7_ROTATED = 115
DMPAPER_PENV_8_ROTATED = 116
DMPAPER_PENV_9_ROTATED = 117
DMPAPER_PENV_10_ROTATED = 118
DMPAPER_LAST = DMPAPER_PENV_10_ROTATED
DMPAPER_USER = 256

# DEVMODE.dmDefaultSource
DMBIN_UPPER = 1
DMBIN_ONLYONE = 1
DMBIN_LOWER = 2
DMBIN_MIDDLE = 3
DMBIN_MANUAL = 4
DMBIN_ENVELOPE = 5
DMBIN_ENVMANUAL = 6
DMBIN_AUTO = 7
DMBIN_TRACTOR = 8
DMBIN_SMALLFMT = 9
DMBIN_LARGEFMT = 10
DMBIN_LARGECAPACITY = 11
DMBIN_CASSETTE = 14
DMBIN_FORMSOURCE = 15
DMBIN_LAST = DMBIN_FORMSOURCE
DMBIN_USER = 256

# DEVMODE.dmPrintQuality
DMRES_DRAFT = -1
DMRES_LOW = -2
DMRES_MEDIUM = -3
DMRES_HIGH = -4

# DEVMODE.dmColor
DMCOLOR_MONOCHROME = 1
DMCOLOR_COLOR = 2

# DEVMODE.dmDuplex
DMDUP_SIMPLEX = 1
DMDUP_VERTICAL = 2
DMDUP_HORIZONTAL = 3

# DEVMODE.dmTTOption
DMTT_BITMAP = 1
DMTT_DOWNLOAD = 2
DMTT_SUBDEV = 3
DMTT_DOWNLOAD_OUTLINE = 4

# DEVMODE.dmCollate
DMCOLLATE_FALSE = 0
DMCOLLATE_TRUE = 1

# DEVMODE.dmDisplayFlags
DM_GRAYSCALE = 1
DM_INTERLACED = 2

# DEVMODE.dmICMMethod
DMICMMETHOD_NONE = 1
DMICMMETHOD_SYSTEM = 2
DMICMMETHOD_DRIVER = 3
DMICMMETHOD_DEVICE = 4
DMICMMETHOD_USER = 256

# DEVMODE.dmICMIntent
DMICM_SATURATE = 1
DMICM_CONTRAST = 2
DMICM_COLORIMETRIC = 3
DMICM_ABS_COLORIMETRIC = 4
DMICM_USER = 256

# DEVMODE.dmMediaType
DMMEDIA_STANDARD = 1
DMMEDIA_TRANSPARENCY = 2
DMMEDIA_GLOSSY = 3
DMMEDIA_USER = 256

# DEVMODE.dmDitherType
DMDITHER_NONE = 1
DMDITHER_COARSE = 2
DMDITHER_FINE = 3
DMDITHER_LINEART = 4
DMDITHER_ERRORDIFFUSION = 5
DMDITHER_RESERVED6 = 6
DMDITHER_RESERVED7 = 7
DMDITHER_RESERVED8 = 8
DMDITHER_RESERVED9 = 9
DMDITHER_GRAYSCALE = 10
DMDITHER_USER = 256

# DEVMODE.dmNup
DMNUP_SYSTEM = 1
DMNUP_ONEUP = 2

# used with ExtEscape
FEATURESETTING_NUP = 0
FEATURESETTING_OUTPUT = 1
FEATURESETTING_PSLEVEL = 2
FEATURESETTING_CUSTPAPER = 3
FEATURESETTING_MIRROR = 4
FEATURESETTING_NEGATIVE = 5
FEATURESETTING_PROTOCOL = 6
FEATURESETTING_PRIVATE_BEGIN = 0x1000
FEATURESETTING_PRIVATE_END = 0x1FFF

RDH_RECTANGLES = 1
GGO_METRICS = 0
GGO_BITMAP = 1
GGO_NATIVE = 2
TT_POLYGON_TYPE = 24
TT_PRIM_LINE = 1
TT_PRIM_QSPLINE = 2
TT_AVAILABLE = 1
TT_ENABLED = 2
DM_UPDATE = 1
DM_COPY = 2
DM_PROMPT = 4
DM_MODIFY = 8
DM_IN_BUFFER = DM_MODIFY
DM_IN_PROMPT = DM_PROMPT
DM_OUT_BUFFER = DM_COPY
DM_OUT_DEFAULT = DM_UPDATE

# DISPLAY_DEVICE.StateFlags
DISPLAY_DEVICE_ATTACHED_TO_DESKTOP = 1
DISPLAY_DEVICE_MULTI_DRIVER = 2
DISPLAY_DEVICE_PRIMARY_DEVICE = 4
DISPLAY_DEVICE_MIRRORING_DRIVER = 8
DISPLAY_DEVICE_VGA_COMPATIBLE = 16
DISPLAY_DEVICE_REMOVABLE = 32
DISPLAY_DEVICE_MODESPRUNED = *********
DISPLAY_DEVICE_REMOTE = ********
DISPLAY_DEVICE_DISCONNECT = 33554432

# DeviceCapabilities types
DC_FIELDS = 1
DC_PAPERS = 2
DC_PAPERSIZE = 3
DC_MINEXTENT = 4
DC_MAXEXTENT = 5
DC_BINS = 6
DC_DUPLEX = 7
DC_SIZE = 8
DC_EXTRA = 9
DC_VERSION = 10
DC_DRIVER = 11
DC_BINNAMES = 12
DC_ENUMRESOLUTIONS = 13
DC_FILEDEPENDENCIES = 14
DC_TRUETYPE = 15
DC_PAPERNAMES = 16
DC_ORIENTATION = 17
DC_COPIES = 18
DC_BINADJUST = 19
DC_EMF_COMPLIANT = 20
DC_DATATYPE_PRODUCED = 21
DC_COLLATE = 22
DC_MANUFACTURER = 23
DC_MODEL = 24
DC_PERSONALITY = 25
DC_PRINTRATE = 26
DC_PRINTRATEUNIT = 27
DC_PRINTERMEM = 28
DC_MEDIAREADY = 29
DC_STAPLE = 30
DC_PRINTRATEPPM = 31
DC_COLORDEVICE = 32
DC_NUP = 33
DC_MEDIATYPENAMES = 34
DC_MEDIATYPES = 35

PRINTRATEUNIT_PPM = 1
PRINTRATEUNIT_CPS = 2
PRINTRATEUNIT_LPM = 3
PRINTRATEUNIT_IPM = 4

# TrueType constants
DCTT_BITMAP = 1
DCTT_DOWNLOAD = 2
DCTT_SUBDEV = 4
DCTT_DOWNLOAD_OUTLINE = 8

DCBA_FACEUPNONE = 0
DCBA_FACEUPCENTER = 1
DCBA_FACEUPLEFT = 2
DCBA_FACEUPRIGHT = 3
DCBA_FACEDOWNNONE = 256
DCBA_FACEDOWNCENTER = 257
DCBA_FACEDOWNLEFT = 258
DCBA_FACEDOWNRIGHT = 259

CA_NEGATIVE = 1
CA_LOG_FILTER = 2
ILLUMINANT_DEVICE_DEFAULT = 0
ILLUMINANT_A = 1
ILLUMINANT_B = 2
ILLUMINANT_C = 3
ILLUMINANT_D50 = 4
ILLUMINANT_D55 = 5
ILLUMINANT_D65 = 6
ILLUMINANT_D75 = 7
ILLUMINANT_F2 = 8
ILLUMINANT_MAX_INDEX = ILLUMINANT_F2
ILLUMINANT_TUNGSTEN = ILLUMINANT_A
ILLUMINANT_DAYLIGHT = ILLUMINANT_C
ILLUMINANT_FLUORESCENT = ILLUMINANT_F2
ILLUMINANT_NTSC = ILLUMINANT_C

# Generated by h2py from \msvcnt\include\wingdi.h
# hacked and split manually by mhammond.
FONTMAPPER_MAX = 10
ENHMETA_SIGNATURE = 1179469088
ENHMETA_STOCK_OBJECT = -**********
EMR_HEADER = 1
EMR_POLYBEZIER = 2
EMR_POLYGON = 3
EMR_POLYLINE = 4
EMR_POLYBEZIERTO = 5
EMR_POLYLINETO = 6
EMR_POLYPOLYLINE = 7
EMR_POLYPOLYGON = 8
EMR_SETWINDOWEXTEX = 9
EMR_SETWINDOWORGEX = 10
EMR_SETVIEWPORTEXTEX = 11
EMR_SETVIEWPORTORGEX = 12
EMR_SETBRUSHORGEX = 13
EMR_EOF = 14
EMR_SETPIXELV = 15
EMR_SETMAPPERFLAGS = 16
EMR_SETMAPMODE = 17
EMR_SETBKMODE = 18
EMR_SETPOLYFILLMODE = 19
EMR_SETROP2 = 20
EMR_SETSTRETCHBLTMODE = 21
EMR_SETTEXTALIGN = 22
EMR_SETCOLORADJUSTMENT = 23
EMR_SETTEXTCOLOR = 24
EMR_SETBKCOLOR = 25
EMR_OFFSETCLIPRGN = 26
EMR_MOVETOEX = 27
EMR_SETMETARGN = 28
EMR_EXCLUDECLIPRECT = 29
EMR_INTERSECTCLIPRECT = 30
EMR_SCALEVIEWPORTEXTEX = 31
EMR_SCALEWINDOWEXTEX = 32
EMR_SAVEDC = 33
EMR_RESTOREDC = 34
EMR_SETWORLDTRANSFORM = 35
EMR_MODIFYWORLDTRANSFORM = 36
EMR_SELECTOBJECT = 37
EMR_CREATEPEN = 38
EMR_CREATEBRUSHINDIRECT = 39
EMR_DELETEOBJECT = 40
EMR_ANGLEARC = 41
EMR_ELLIPSE = 42
EMR_RECTANGLE = 43
EMR_ROUNDRECT = 44
EMR_ARC = 45
EMR_CHORD = 46
EMR_PIE = 47
EMR_SELECTPALETTE = 48
EMR_CREATEPALETTE = 49
EMR_SETPALETTEENTRIES = 50
EMR_RESIZEPALETTE = 51
EMR_REALIZEPALETTE = 52
EMR_EXTFLOODFILL = 53
EMR_LINETO = 54
EMR_ARCTO = 55
EMR_POLYDRAW = 56
EMR_SETARCDIRECTION = 57
EMR_SETMITERLIMIT = 58
EMR_BEGINPATH = 59
EMR_ENDPATH = 60
EMR_CLOSEFIGURE = 61
EMR_FILLPATH = 62
EMR_STROKEANDFILLPATH = 63
EMR_STROKEPATH = 64
EMR_FLATTENPATH = 65
EMR_WIDENPATH = 66
EMR_SELECTCLIPPATH = 67
EMR_ABORTPATH = 68
EMR_GDICOMMENT = 70
EMR_FILLRGN = 71
EMR_FRAMERGN = 72
EMR_INVERTRGN = 73
EMR_PAINTRGN = 74
EMR_EXTSELECTCLIPRGN = 75
EMR_BITBLT = 76
EMR_STRETCHBLT = 77
EMR_MASKBLT = 78
EMR_PLGBLT = 79
EMR_SETDIBITSTODEVICE = 80
EMR_STRETCHDIBITS = 81
EMR_EXTCREATEFONTINDIRECTW = 82
EMR_EXTTEXTOUTA = 83
EMR_EXTTEXTOUTW = 84
EMR_POLYBEZIER16 = 85
EMR_POLYGON16 = 86
EMR_POLYLINE16 = 87
EMR_POLYBEZIERTO16 = 88
EMR_POLYLINETO16 = 89
EMR_POLYPOLYLINE16 = 90
EMR_POLYPOLYGON16 = 91
EMR_POLYDRAW16 = 92
EMR_CREATEMONOBRUSH = 93
EMR_CREATEDIBPATTERNBRUSHPT = 94
EMR_EXTCREATEPEN = 95
EMR_POLYTEXTOUTA = 96
EMR_POLYTEXTOUTW = 97
EMR_MIN = 1
EMR_MAX = 97
# Generated by h2py from \msvcnt\include\wingdi.h
# hacked and split manually by mhammond.
PANOSE_COUNT = 10
PAN_FAMILYTYPE_INDEX = 0
PAN_SERIFSTYLE_INDEX = 1
PAN_WEIGHT_INDEX = 2
PAN_PROPORTION_INDEX = 3
PAN_CONTRAST_INDEX = 4
PAN_STROKEVARIATION_INDEX = 5
PAN_ARMSTYLE_INDEX = 6
PAN_LETTERFORM_INDEX = 7
PAN_MIDLINE_INDEX = 8
PAN_XHEIGHT_INDEX = 9
PAN_CULTURE_LATIN = 0
PAN_ANY = 0
PAN_NO_FIT = 1
PAN_FAMILY_TEXT_DISPLAY = 2
PAN_FAMILY_SCRIPT = 3
PAN_FAMILY_DECORATIVE = 4
PAN_FAMILY_PICTORIAL = 5
PAN_SERIF_COVE = 2
PAN_SERIF_OBTUSE_COVE = 3
PAN_SERIF_SQUARE_COVE = 4
PAN_SERIF_OBTUSE_SQUARE_COVE = 5
PAN_SERIF_SQUARE = 6
PAN_SERIF_THIN = 7
PAN_SERIF_BONE = 8
PAN_SERIF_EXAGGERATED = 9
PAN_SERIF_TRIANGLE = 10
PAN_SERIF_NORMAL_SANS = 11
PAN_SERIF_OBTUSE_SANS = 12
PAN_SERIF_PERP_SANS = 13
PAN_SERIF_FLARED = 14
PAN_SERIF_ROUNDED = 15
PAN_WEIGHT_VERY_LIGHT = 2
PAN_WEIGHT_LIGHT = 3
PAN_WEIGHT_THIN = 4
PAN_WEIGHT_BOOK = 5
PAN_WEIGHT_MEDIUM = 6
PAN_WEIGHT_DEMI = 7
PAN_WEIGHT_BOLD = 8
PAN_WEIGHT_HEAVY = 9
PAN_WEIGHT_BLACK = 10
PAN_WEIGHT_NORD = 11
PAN_PROP_OLD_STYLE = 2
PAN_PROP_MODERN = 3
PAN_PROP_EVEN_WIDTH = 4
PAN_PROP_EXPANDED = 5
PAN_PROP_CONDENSED = 6
PAN_PROP_VERY_EXPANDED = 7
PAN_PROP_VERY_CONDENSED = 8
PAN_PROP_MONOSPACED = 9
PAN_CONTRAST_NONE = 2
PAN_CONTRAST_VERY_LOW = 3
PAN_CONTRAST_LOW = 4
PAN_CONTRAST_MEDIUM_LOW = 5
PAN_CONTRAST_MEDIUM = 6
PAN_CONTRAST_MEDIUM_HIGH = 7
PAN_CONTRAST_HIGH = 8
PAN_CONTRAST_VERY_HIGH = 9
PAN_STROKE_GRADUAL_DIAG = 2
PAN_STROKE_GRADUAL_TRAN = 3
PAN_STROKE_GRADUAL_VERT = 4
PAN_STROKE_GRADUAL_HORZ = 5
PAN_STROKE_RAPID_VERT = 6
PAN_STROKE_RAPID_HORZ = 7
PAN_STROKE_INSTANT_VERT = 8
PAN_STRAIGHT_ARMS_HORZ = 2
PAN_STRAIGHT_ARMS_WEDGE = 3
PAN_STRAIGHT_ARMS_VERT = 4
PAN_STRAIGHT_ARMS_SINGLE_SERIF = 5
PAN_STRAIGHT_ARMS_DOUBLE_SERIF = 6
PAN_BENT_ARMS_HORZ = 7
PAN_BENT_ARMS_WEDGE = 8
PAN_BENT_ARMS_VERT = 9
PAN_BENT_ARMS_SINGLE_SERIF = 10
PAN_BENT_ARMS_DOUBLE_SERIF = 11
PAN_LETT_NORMAL_CONTACT = 2
PAN_LETT_NORMAL_WEIGHTED = 3
PAN_LETT_NORMAL_BOXED = 4
PAN_LETT_NORMAL_FLATTENED = 5
PAN_LETT_NORMAL_ROUNDED = 6
PAN_LETT_NORMAL_OFF_CENTER = 7
PAN_LETT_NORMAL_SQUARE = 8
PAN_LETT_OBLIQUE_CONTACT = 9
PAN_LETT_OBLIQUE_WEIGHTED = 10
PAN_LETT_OBLIQUE_BOXED = 11
PAN_LETT_OBLIQUE_FLATTENED = 12
PAN_LETT_OBLIQUE_ROUNDED = 13
PAN_LETT_OBLIQUE_OFF_CENTER = 14
PAN_LETT_OBLIQUE_SQUARE = 15
PAN_MIDLINE_STANDARD_TRIMMED = 2
PAN_MIDLINE_STANDARD_POINTED = 3
PAN_MIDLINE_STANDARD_SERIFED = 4
PAN_MIDLINE_HIGH_TRIMMED = 5
PAN_MIDLINE_HIGH_POINTED = 6
PAN_MIDLINE_HIGH_SERIFED = 7
PAN_MIDLINE_CONSTANT_TRIMMED = 8
PAN_MIDLINE_CONSTANT_POINTED = 9
PAN_MIDLINE_CONSTANT_SERIFED = 10
PAN_MIDLINE_LOW_TRIMMED = 11
PAN_MIDLINE_LOW_POINTED = 12
PAN_MIDLINE_LOW_SERIFED = 13
PAN_XHEIGHT_CONSTANT_SMALL = 2
PAN_XHEIGHT_CONSTANT_STD = 3
PAN_XHEIGHT_CONSTANT_LARGE = 4
PAN_XHEIGHT_DUCKING_SMALL = 5
PAN_XHEIGHT_DUCKING_STD = 6
PAN_XHEIGHT_DUCKING_LARGE = 7
ELF_VENDOR_SIZE = 4
ELF_VERSION = 0
ELF_CULTURE_LATIN = 0
RASTER_FONTTYPE = 1
DEVICE_FONTTYPE = 2
TRUETYPE_FONTTYPE = 4


def PALETTEINDEX(i):
    return 16777216 | (i)


PC_RESERVED = 1
PC_EXPLICIT = 2
PC_NOCOLLAPSE = 4


def GetRValue(rgb):
    return rgb & 0xFF


def GetGValue(rgb):
    return (rgb >> 8) & 0xFF


def GetBValue(rgb):
    return (rgb >> 16) & 0xFF


TRANSPARENT = 1
OPAQUE = 2
BKMODE_LAST = 2
GM_COMPATIBLE = 1
GM_ADVANCED = 2
GM_LAST = 2
PT_CLOSEFIGURE = 1
PT_LINETO = 2
PT_BEZIERTO = 4
PT_MOVETO = 6
MM_TEXT = 1
MM_LOMETRIC = 2
MM_HIMETRIC = 3
MM_LOENGLISH = 4
MM_HIENGLISH = 5
MM_TWIPS = 6
MM_ISOTROPIC = 7
MM_ANISOTROPIC = 8
MM_MIN = MM_TEXT
MM_MAX = MM_ANISOTROPIC
MM_MAX_FIXEDSCALE = MM_TWIPS
ABSOLUTE = 1
RELATIVE = 2
WHITE_BRUSH = 0
LTGRAY_BRUSH = 1
GRAY_BRUSH = 2
DKGRAY_BRUSH = 3
BLACK_BRUSH = 4
NULL_BRUSH = 5
HOLLOW_BRUSH = NULL_BRUSH
WHITE_PEN = 6
BLACK_PEN = 7
NULL_PEN = 8
OEM_FIXED_FONT = 10
ANSI_FIXED_FONT = 11
ANSI_VAR_FONT = 12
SYSTEM_FONT = 13
DEVICE_DEFAULT_FONT = 14
DEFAULT_PALETTE = 15
SYSTEM_FIXED_FONT = 16
STOCK_LAST = 16
CLR_INVALID = -1

DC_BRUSH = 18
DC_PEN = 19

# Exception/Status codes from winuser.h and winnt.h
STATUS_WAIT_0 = 0
STATUS_ABANDONED_WAIT_0 = 128
STATUS_USER_APC = 192
STATUS_TIMEOUT = 258
STATUS_PENDING = 259
STATUS_SEGMENT_NOTIFICATION = 1073741829
STATUS_GUARD_PAGE_VIOLATION = -2147483647
STATUS_DATATYPE_MISALIGNMENT = -2147483646
STATUS_BREAKPOINT = -2147483645
STATUS_SINGLE_STEP = -2147483644
STATUS_ACCESS_VIOLATION = -1073741819
STATUS_IN_PAGE_ERROR = -1073741818
STATUS_INVALID_HANDLE = -1073741816
STATUS_NO_MEMORY = -1073741801
STATUS_ILLEGAL_INSTRUCTION = -1073741795
STATUS_NONCONTINUABLE_EXCEPTION = -1073741787
STATUS_INVALID_DISPOSITION = -1073741786
STATUS_ARRAY_BOUNDS_EXCEEDED = -1073741684
STATUS_FLOAT_DENORMAL_OPERAND = -1073741683
STATUS_FLOAT_DIVIDE_BY_ZERO = -1073741682
STATUS_FLOAT_INEXACT_RESULT = -1073741681
STATUS_FLOAT_INVALID_OPERATION = -1073741680
STATUS_FLOAT_OVERFLOW = -1073741679
STATUS_FLOAT_STACK_CHECK = -1073741678
STATUS_FLOAT_UNDERFLOW = -1073741677
STATUS_INTEGER_DIVIDE_BY_ZERO = -1073741676
STATUS_INTEGER_OVERFLOW = -1073741675
STATUS_PRIVILEGED_INSTRUCTION = -1073741674
STATUS_STACK_OVERFLOW = -1073741571
STATUS_CONTROL_C_EXIT = -1073741510


WAIT_FAILED = -1
WAIT_OBJECT_0 = STATUS_WAIT_0 + 0

WAIT_ABANDONED = STATUS_ABANDONED_WAIT_0 + 0
WAIT_ABANDONED_0 = STATUS_ABANDONED_WAIT_0 + 0

WAIT_TIMEOUT = STATUS_TIMEOUT
WAIT_IO_COMPLETION = STATUS_USER_APC
STILL_ACTIVE = STATUS_PENDING
EXCEPTION_ACCESS_VIOLATION = STATUS_ACCESS_VIOLATION
EXCEPTION_DATATYPE_MISALIGNMENT = STATUS_DATATYPE_MISALIGNMENT
EXCEPTION_BREAKPOINT = STATUS_BREAKPOINT
EXCEPTION_SINGLE_STEP = STATUS_SINGLE_STEP
EXCEPTION_ARRAY_BOUNDS_EXCEEDED = STATUS_ARRAY_BOUNDS_EXCEEDED
EXCEPTION_FLT_DENORMAL_OPERAND = STATUS_FLOAT_DENORMAL_OPERAND
EXCEPTION_FLT_DIVIDE_BY_ZERO = STATUS_FLOAT_DIVIDE_BY_ZERO
EXCEPTION_FLT_INEXACT_RESULT = STATUS_FLOAT_INEXACT_RESULT
EXCEPTION_FLT_INVALID_OPERATION = STATUS_FLOAT_INVALID_OPERATION
EXCEPTION_FLT_OVERFLOW = STATUS_FLOAT_OVERFLOW
EXCEPTION_FLT_STACK_CHECK = STATUS_FLOAT_STACK_CHECK
EXCEPTION_FLT_UNDERFLOW = STATUS_FLOAT_UNDERFLOW
EXCEPTION_INT_DIVIDE_BY_ZERO = STATUS_INTEGER_DIVIDE_BY_ZERO
EXCEPTION_INT_OVERFLOW = STATUS_INTEGER_OVERFLOW
EXCEPTION_PRIV_INSTRUCTION = STATUS_PRIVILEGED_INSTRUCTION
EXCEPTION_IN_PAGE_ERROR = STATUS_IN_PAGE_ERROR
EXCEPTION_ILLEGAL_INSTRUCTION = STATUS_ILLEGAL_INSTRUCTION
EXCEPTION_NONCONTINUABLE_EXCEPTION = STATUS_NONCONTINUABLE_EXCEPTION
EXCEPTION_STACK_OVERFLOW = STATUS_STACK_OVERFLOW
EXCEPTION_INVALID_DISPOSITION = STATUS_INVALID_DISPOSITION
EXCEPTION_GUARD_PAGE = STATUS_GUARD_PAGE_VIOLATION
EXCEPTION_INVALID_HANDLE = STATUS_INVALID_HANDLE
CONTROL_C_EXIT = STATUS_CONTROL_C_EXIT

# winuser.h line 8594
# constants used with SystemParametersInfo
SPI_GETBEEP = 1
SPI_SETBEEP = 2
SPI_GETMOUSE = 3
SPI_SETMOUSE = 4
SPI_GETBORDER = 5
SPI_SETBORDER = 6
SPI_GETKEYBOARDSPEED = 10
SPI_SETKEYBOARDSPEED = 11
SPI_LANGDRIVER = 12
SPI_ICONHORIZONTALSPACING = 13
SPI_GETSCREENSAVETIMEOUT = 14
SPI_SETSCREENSAVETIMEOUT = 15
SPI_GETSCREENSAVEACTIVE = 16
SPI_SETSCREENSAVEACTIVE = 17
SPI_GETGRIDGRANULARITY = 18
SPI_SETGRIDGRANULARITY = 19
SPI_SETDESKWALLPAPER = 20
SPI_SETDESKPATTERN = 21
SPI_GETKEYBOARDDELAY = 22
SPI_SETKEYBOARDDELAY = 23
SPI_ICONVERTICALSPACING = 24
SPI_GETICONTITLEWRAP = 25
SPI_SETICONTITLEWRAP = 26
SPI_GETMENUDROPALIGNMENT = 27
SPI_SETMENUDROPALIGNMENT = 28
SPI_SETDOUBLECLKWIDTH = 29
SPI_SETDOUBLECLKHEIGHT = 30
SPI_GETICONTITLELOGFONT = 31
SPI_SETDOUBLECLICKTIME = 32
SPI_SETMOUSEBUTTONSWAP = 33
SPI_SETICONTITLELOGFONT = 34
SPI_GETFASTTASKSWITCH = 35
SPI_SETFASTTASKSWITCH = 36
SPI_SETDRAGFULLWINDOWS = 37
SPI_GETDRAGFULLWINDOWS = 38
SPI_GETNONCLIENTMETRICS = 41
SPI_SETNONCLIENTMETRICS = 42
SPI_GETMINIMIZEDMETRICS = 43
SPI_SETMINIMIZEDMETRICS = 44
SPI_GETICONMETRICS = 45
SPI_SETICONMETRICS = 46
SPI_SETWORKAREA = 47
SPI_GETWORKAREA = 48
SPI_SETPENWINDOWS = 49
SPI_GETFILTERKEYS = 50
SPI_SETFILTERKEYS = 51
SPI_GETTOGGLEKEYS = 52
SPI_SETTOGGLEKEYS = 53
SPI_GETMOUSEKEYS = 54
SPI_SETMOUSEKEYS = 55
SPI_GETSHOWSOUNDS = 56
SPI_SETSHOWSOUNDS = 57
SPI_GETSTICKYKEYS = 58
SPI_SETSTICKYKEYS = 59
SPI_GETACCESSTIMEOUT = 60
SPI_SETACCESSTIMEOUT = 61
SPI_GETSERIALKEYS = 62
SPI_SETSERIALKEYS = 63
SPI_GETSOUNDSENTRY = 64
SPI_SETSOUNDSENTRY = 65
SPI_GETHIGHCONTRAST = 66
SPI_SETHIGHCONTRAST = 67
SPI_GETKEYBOARDPREF = 68
SPI_SETKEYBOARDPREF = 69
SPI_GETSCREENREADER = 70
SPI_SETSCREENREADER = 71
SPI_GETANIMATION = 72
SPI_SETANIMATION = 73
SPI_GETFONTSMOOTHING = 74
SPI_SETFONTSMOOTHING = 75
SPI_SETDRAGWIDTH = 76
SPI_SETDRAGHEIGHT = 77
SPI_SETHANDHELD = 78
SPI_GETLOWPOWERTIMEOUT = 79
SPI_GETPOWEROFFTIMEOUT = 80
SPI_SETLOWPOWERTIMEOUT = 81
SPI_SETPOWEROFFTIMEOUT = 82
SPI_GETLOWPOWERACTIVE = 83
SPI_GETPOWEROFFACTIVE = 84
SPI_SETLOWPOWERACTIVE = 85
SPI_SETPOWEROFFACTIVE = 86
SPI_SETCURSORS = 87
SPI_SETICONS = 88
SPI_GETDEFAULTINPUTLANG = 89
SPI_SETDEFAULTINPUTLANG = 90
SPI_SETLANGTOGGLE = 91
SPI_GETWINDOWSEXTENSION = 92
SPI_SETMOUSETRAILS = 93
SPI_GETMOUSETRAILS = 94
SPI_GETSNAPTODEFBUTTON = 95
SPI_SETSNAPTODEFBUTTON = 96
SPI_SETSCREENSAVERRUNNING = 97
SPI_SCREENSAVERRUNNING = SPI_SETSCREENSAVERRUNNING
SPI_GETMOUSEHOVERWIDTH = 98
SPI_SETMOUSEHOVERWIDTH = 99
SPI_GETMOUSEHOVERHEIGHT = 100
SPI_SETMOUSEHOVERHEIGHT = 101
SPI_GETMOUSEHOVERTIME = 102
SPI_SETMOUSEHOVERTIME = 103
SPI_GETWHEELSCROLLLINES = 104
SPI_SETWHEELSCROLLLINES = 105
SPI_GETMENUSHOWDELAY = 106
SPI_SETMENUSHOWDELAY = 107

SPI_GETSHOWIMEUI = 110
SPI_SETSHOWIMEUI = 111
SPI_GETMOUSESPEED = 112
SPI_SETMOUSESPEED = 113
SPI_GETSCREENSAVERRUNNING = 114
SPI_GETDESKWALLPAPER = 115

SPI_GETACTIVEWINDOWTRACKING = 4096
SPI_SETACTIVEWINDOWTRACKING = 4097
SPI_GETMENUANIMATION = 4098
SPI_SETMENUANIMATION = 4099
SPI_GETCOMBOBOXANIMATION = 4100
SPI_SETCOMBOBOXANIMATION = 4101
SPI_GETLISTBOXSMOOTHSCROLLING = 4102
SPI_SETLISTBOXSMOOTHSCROLLING = 4103
SPI_GETGRADIENTCAPTIONS = 4104
SPI_SETGRADIENTCAPTIONS = 4105
SPI_GETKEYBOARDCUES = 4106
SPI_SETKEYBOARDCUES = 4107
SPI_GETMENUUNDERLINES = 4106
SPI_SETMENUUNDERLINES = 4107
SPI_GETACTIVEWNDTRKZORDER = 4108
SPI_SETACTIVEWNDTRKZORDER = 4109
SPI_GETHOTTRACKING = 4110
SPI_SETHOTTRACKING = 4111

SPI_GETMENUFADE = 4114
SPI_SETMENUFADE = 4115
SPI_GETSELECTIONFADE = 4116
SPI_SETSELECTIONFADE = 4117
SPI_GETTOOLTIPANIMATION = 4118
SPI_SETTOOLTIPANIMATION = 4119
SPI_GETTOOLTIPFADE = 4120
SPI_SETTOOLTIPFADE = 4121
SPI_GETCURSORSHADOW = 4122
SPI_SETCURSORSHADOW = 4123
SPI_GETMOUSESONAR = 4124
SPI_SETMOUSESONAR = 4125
SPI_GETMOUSECLICKLOCK = 4126
SPI_SETMOUSECLICKLOCK = 4127
SPI_GETMOUSEVANISH = 4128
SPI_SETMOUSEVANISH = 4129
SPI_GETFLATMENU = 4130
SPI_SETFLATMENU = 4131
SPI_GETDROPSHADOW = 4132
SPI_SETDROPSHADOW = 4133
SPI_GETBLOCKSENDINPUTRESETS = 4134
SPI_SETBLOCKSENDINPUTRESETS = 4135
SPI_GETUIEFFECTS = 4158
SPI_SETUIEFFECTS = 4159

SPI_GETFOREGROUNDLOCKTIMEOUT = 8192
SPI_SETFOREGROUNDLOCKTIMEOUT = 8193
SPI_GETACTIVEWNDTRKTIMEOUT = 8194
SPI_SETACTIVEWNDTRKTIMEOUT = 8195
SPI_GETFOREGROUNDFLASHCOUNT = 8196
SPI_SETFOREGROUNDFLASHCOUNT = 8197
SPI_GETCARETWIDTH = 8198
SPI_SETCARETWIDTH = 8199
SPI_GETMOUSECLICKLOCKTIME = 8200
SPI_SETMOUSECLICKLOCKTIME = 8201
SPI_GETFONTSMOOTHINGTYPE = 8202
SPI_SETFONTSMOOTHINGTYPE = 8203
SPI_GETFONTSMOOTHINGCONTRAST = 8204
SPI_SETFONTSMOOTHINGCONTRAST = 8205
SPI_GETFOCUSBORDERWIDTH = 8206
SPI_SETFOCUSBORDERWIDTH = 8207
SPI_GETFOCUSBORDERHEIGHT = 8208
SPI_SETFOCUSBORDERHEIGHT = 8209
SPI_GETFONTSMOOTHINGORIENTATION = 8210
SPI_SETFONTSMOOTHINGORIENTATION = 8211

# fWinIni flags for SystemParametersInfo
SPIF_UPDATEINIFILE = 1
SPIF_SENDWININICHANGE = 2
SPIF_SENDCHANGE = SPIF_SENDWININICHANGE

# used with SystemParametersInfo and SPI_GETFONTSMOOTHINGTYPE/SPI_SETFONTSMOOTHINGTYPE
FE_FONTSMOOTHINGSTANDARD = 1
FE_FONTSMOOTHINGCLEARTYPE = 2
FE_FONTSMOOTHINGDOCKING = 32768

METRICS_USEDEFAULT = -1
ARW_BOTTOMLEFT = 0
ARW_BOTTOMRIGHT = 1
ARW_TOPLEFT = 2
ARW_TOPRIGHT = 3
ARW_STARTMASK = 3
ARW_STARTRIGHT = 1
ARW_STARTTOP = 2
ARW_LEFT = 0
ARW_RIGHT = 0
ARW_UP = 4
ARW_DOWN = 4
ARW_HIDE = 8
# ARW_VALID = 0x000F
SERKF_SERIALKEYSON = 1
SERKF_AVAILABLE = 2
SERKF_INDICATOR = 4
HCF_HIGHCONTRASTON = 1
HCF_AVAILABLE = 2
HCF_HOTKEYACTIVE = 4
HCF_CONFIRMHOTKEY = 8
HCF_HOTKEYSOUND = 16
HCF_INDICATOR = 32
HCF_HOTKEYAVAILABLE = 64
CDS_UPDATEREGISTRY = 1
CDS_TEST = 2
CDS_FULLSCREEN = 4
CDS_GLOBAL = 8
CDS_SET_PRIMARY = 16
CDS_RESET = **********
CDS_SETRECT = *********
CDS_NORESET = *********

# return values from ChangeDisplaySettings and ChangeDisplaySettingsEx
DISP_CHANGE_SUCCESSFUL = 0
DISP_CHANGE_RESTART = 1
DISP_CHANGE_FAILED = -1
DISP_CHANGE_BADMODE = -2
DISP_CHANGE_NOTUPDATED = -3
DISP_CHANGE_BADFLAGS = -4
DISP_CHANGE_BADPARAM = -5
DISP_CHANGE_BADDUALVIEW = -6

ENUM_CURRENT_SETTINGS = -1
ENUM_REGISTRY_SETTINGS = -2
FKF_FILTERKEYSON = 1
FKF_AVAILABLE = 2
FKF_HOTKEYACTIVE = 4
FKF_CONFIRMHOTKEY = 8
FKF_HOTKEYSOUND = 16
FKF_INDICATOR = 32
FKF_CLICKON = 64
SKF_STICKYKEYSON = 1
SKF_AVAILABLE = 2
SKF_HOTKEYACTIVE = 4
SKF_CONFIRMHOTKEY = 8
SKF_HOTKEYSOUND = 16
SKF_INDICATOR = 32
SKF_AUDIBLEFEEDBACK = 64
SKF_TRISTATE = 128
SKF_TWOKEYSOFF = 256
SKF_LALTLATCHED = *********
SKF_LCTLLATCHED = ********
SKF_LSHIFTLATCHED = 16777216
SKF_RALTLATCHED = *********
SKF_RCTLLATCHED = *********
SKF_RSHIFTLATCHED = 33554432
SKF_LWINLATCHED = **********
SKF_RWINLATCHED = -**********
SKF_LALTLOCKED = 1048576
SKF_LCTLLOCKED = 262144
SKF_LSHIFTLOCKED = 65536
SKF_RALTLOCKED = 2097152
SKF_RCTLLOCKED = 524288
SKF_RSHIFTLOCKED = 131072
SKF_LWINLOCKED = 4194304
SKF_RWINLOCKED = 8388608
MKF_MOUSEKEYSON = 1
MKF_AVAILABLE = 2
MKF_HOTKEYACTIVE = 4
MKF_CONFIRMHOTKEY = 8
MKF_HOTKEYSOUND = 16
MKF_INDICATOR = 32
MKF_MODIFIERS = 64
MKF_REPLACENUMBERS = 128
MKF_LEFTBUTTONSEL = *********
MKF_RIGHTBUTTONSEL = *********
MKF_LEFTBUTTONDOWN = 16777216
MKF_RIGHTBUTTONDOWN = 33554432
MKF_MOUSEMODE = -**********
ATF_TIMEOUTON = 1
ATF_ONOFFFEEDBACK = 2
SSGF_NONE = 0
SSGF_DISPLAY = 3
SSTF_NONE = 0
SSTF_CHARS = 1
SSTF_BORDER = 2
SSTF_DISPLAY = 3
SSWF_NONE = 0
SSWF_TITLE = 1
SSWF_WINDOW = 2
SSWF_DISPLAY = 3
SSWF_CUSTOM = 4
SSF_SOUNDSENTRYON = 1
SSF_AVAILABLE = 2
SSF_INDICATOR = 4
TKF_TOGGLEKEYSON = 1
TKF_AVAILABLE = 2
TKF_HOTKEYACTIVE = 4
TKF_CONFIRMHOTKEY = 8
TKF_HOTKEYSOUND = 16
TKF_INDICATOR = 32
SLE_ERROR = 1
SLE_MINORERROR = 2
SLE_WARNING = 3
MONITOR_DEFAULTTONULL = 0
MONITOR_DEFAULTTOPRIMARY = 1
MONITOR_DEFAULTTONEAREST = 2
MONITORINFOF_PRIMARY = 1
CCHDEVICENAME = 32
CHILDID_SELF = 0
INDEXID_OBJECT = 0
INDEXID_CONTAINER = 0
OBJID_WINDOW = 0
OBJID_SYSMENU = -1
OBJID_TITLEBAR = -2
OBJID_MENU = -3
OBJID_CLIENT = -4
OBJID_VSCROLL = -5
OBJID_HSCROLL = -6
OBJID_SIZEGRIP = -7
OBJID_CARET = -8
OBJID_CURSOR = -9
OBJID_ALERT = -10
OBJID_SOUND = -11
EVENT_MIN = 1
EVENT_MAX = 2147483647
EVENT_SYSTEM_SOUND = 1
EVENT_SYSTEM_ALERT = 2
EVENT_SYSTEM_FOREGROUND = 3
EVENT_SYSTEM_MENUSTART = 4
EVENT_SYSTEM_MENUEND = 5
EVENT_SYSTEM_MENUPOPUPSTART = 6
EVENT_SYSTEM_MENUPOPUPEND = 7
EVENT_SYSTEM_CAPTURESTART = 8
EVENT_SYSTEM_CAPTUREEND = 9
EVENT_SYSTEM_MOVESIZESTART = 10
EVENT_SYSTEM_MOVESIZEEND = 11
EVENT_SYSTEM_CONTEXTHELPSTART = 12
EVENT_SYSTEM_CONTEXTHELPEND = 13
EVENT_SYSTEM_DRAGDROPSTART = 14
EVENT_SYSTEM_DRAGDROPEND = 15
EVENT_SYSTEM_DIALOGSTART = 16
EVENT_SYSTEM_DIALOGEND = 17
EVENT_SYSTEM_SCROLLINGSTART = 18
EVENT_SYSTEM_SCROLLINGEND = 19
EVENT_SYSTEM_SWITCHSTART = 20
EVENT_SYSTEM_SWITCHEND = 21
EVENT_SYSTEM_MINIMIZESTART = 22
EVENT_SYSTEM_MINIMIZEEND = 23
EVENT_OBJECT_CREATE = 32768
EVENT_OBJECT_DESTROY = 32769
EVENT_OBJECT_SHOW = 32770
EVENT_OBJECT_HIDE = 32771
EVENT_OBJECT_REORDER = 32772
EVENT_OBJECT_FOCUS = 32773
EVENT_OBJECT_SELECTION = 32774
EVENT_OBJECT_SELECTIONADD = 32775
EVENT_OBJECT_SELECTIONREMOVE = 32776
EVENT_OBJECT_SELECTIONWITHIN = 32777
EVENT_OBJECT_STATECHANGE = 32778
EVENT_OBJECT_LOCATIONCHANGE = 32779
EVENT_OBJECT_NAMECHANGE = 32780
EVENT_OBJECT_DESCRIPTIONCHANGE = 32781
EVENT_OBJECT_VALUECHANGE = 32782
EVENT_OBJECT_PARENTCHANGE = 32783
EVENT_OBJECT_HELPCHANGE = 32784
EVENT_OBJECT_DEFACTIONCHANGE = 32785
EVENT_OBJECT_ACCELERATORCHANGE = 32786
SOUND_SYSTEM_STARTUP = 1
SOUND_SYSTEM_SHUTDOWN = 2
SOUND_SYSTEM_BEEP = 3
SOUND_SYSTEM_ERROR = 4
SOUND_SYSTEM_QUESTION = 5
SOUND_SYSTEM_WARNING = 6
SOUND_SYSTEM_INFORMATION = 7
SOUND_SYSTEM_MAXIMIZE = 8
SOUND_SYSTEM_MINIMIZE = 9
SOUND_SYSTEM_RESTOREUP = 10
SOUND_SYSTEM_RESTOREDOWN = 11
SOUND_SYSTEM_APPSTART = 12
SOUND_SYSTEM_FAULT = 13
SOUND_SYSTEM_APPEND = 14
SOUND_SYSTEM_MENUCOMMAND = 15
SOUND_SYSTEM_MENUPOPUP = 16
CSOUND_SYSTEM = 16
ALERT_SYSTEM_INFORMATIONAL = 1
ALERT_SYSTEM_WARNING = 2
ALERT_SYSTEM_ERROR = 3
ALERT_SYSTEM_QUERY = 4
ALERT_SYSTEM_CRITICAL = 5
CALERT_SYSTEM = 6
WINEVENT_OUTOFCONTEXT = 0
WINEVENT_SKIPOWNTHREAD = 1
WINEVENT_SKIPOWNPROCESS = 2
WINEVENT_INCONTEXT = 4
GUI_CARETBLINKING = 1
GUI_INMOVESIZE = 2
GUI_INMENUMODE = 4
GUI_SYSTEMMENUMODE = 8
GUI_POPUPMENUMODE = 16
STATE_SYSTEM_UNAVAILABLE = 1
STATE_SYSTEM_SELECTED = 2
STATE_SYSTEM_FOCUSED = 4
STATE_SYSTEM_PRESSED = 8
STATE_SYSTEM_CHECKED = 16
STATE_SYSTEM_MIXED = 32
STATE_SYSTEM_READONLY = 64
STATE_SYSTEM_HOTTRACKED = 128
STATE_SYSTEM_DEFAULT = 256
STATE_SYSTEM_EXPANDED = 512
STATE_SYSTEM_COLLAPSED = 1024
STATE_SYSTEM_BUSY = 2048
STATE_SYSTEM_FLOATING = 4096
STATE_SYSTEM_MARQUEED = 8192
STATE_SYSTEM_ANIMATED = 16384
STATE_SYSTEM_INVISIBLE = 32768
STATE_SYSTEM_OFFSCREEN = 65536
STATE_SYSTEM_SIZEABLE = 131072
STATE_SYSTEM_MOVEABLE = 262144
STATE_SYSTEM_SELFVOICING = 524288
STATE_SYSTEM_FOCUSABLE = 1048576
STATE_SYSTEM_SELECTABLE = 2097152
STATE_SYSTEM_LINKED = 4194304
STATE_SYSTEM_TRAVERSED = 8388608
STATE_SYSTEM_MULTISELECTABLE = 16777216
STATE_SYSTEM_EXTSELECTABLE = 33554432
STATE_SYSTEM_ALERT_LOW = ********
STATE_SYSTEM_ALERT_MEDIUM = *********
STATE_SYSTEM_ALERT_HIGH = *********
STATE_SYSTEM_VALID = 536870911
CCHILDREN_TITLEBAR = 5
CCHILDREN_SCROLLBAR = 5
CURSOR_SHOWING = 1
WS_ACTIVECAPTION = 1
GA_MIC = 1
GA_PARENT = 1
GA_ROOT = 2
GA_ROOTOWNER = 3
GA_MAC = 4

# winuser.h line 1979
BF_LEFT = 1
BF_TOP = 2
BF_RIGHT = 4
BF_BOTTOM = 8
BF_TOPLEFT = BF_TOP | BF_LEFT
BF_TOPRIGHT = BF_TOP | BF_RIGHT
BF_BOTTOMLEFT = BF_BOTTOM | BF_LEFT
BF_BOTTOMRIGHT = BF_BOTTOM | BF_RIGHT
BF_RECT = BF_LEFT | BF_TOP | BF_RIGHT | BF_BOTTOM
BF_DIAGONAL = 16
BF_DIAGONAL_ENDTOPRIGHT = BF_DIAGONAL | BF_TOP | BF_RIGHT
BF_DIAGONAL_ENDTOPLEFT = BF_DIAGONAL | BF_TOP | BF_LEFT
BF_DIAGONAL_ENDBOTTOMLEFT = BF_DIAGONAL | BF_BOTTOM | BF_LEFT
BF_DIAGONAL_ENDBOTTOMRIGHT = BF_DIAGONAL | BF_BOTTOM | BF_RIGHT
BF_MIDDLE = 2048
BF_SOFT = 4096
BF_ADJUST = 8192
BF_FLAT = 16384
BF_MONO = 32768
DFC_CAPTION = 1
DFC_MENU = 2
DFC_SCROLL = 3
DFC_BUTTON = 4
DFC_POPUPMENU = 5
DFCS_CAPTIONCLOSE = 0
DFCS_CAPTIONMIN = 1
DFCS_CAPTIONMAX = 2
DFCS_CAPTIONRESTORE = 3
DFCS_CAPTIONHELP = 4
DFCS_MENUARROW = 0
DFCS_MENUCHECK = 1
DFCS_MENUBULLET = 2
DFCS_MENUARROWRIGHT = 4
DFCS_SCROLLUP = 0
DFCS_SCROLLDOWN = 1
DFCS_SCROLLLEFT = 2
DFCS_SCROLLRIGHT = 3
DFCS_SCROLLCOMBOBOX = 5
DFCS_SCROLLSIZEGRIP = 8
DFCS_SCROLLSIZEGRIPRIGHT = 16
DFCS_BUTTONCHECK = 0
DFCS_BUTTONRADIOIMAGE = 1
DFCS_BUTTONRADIOMASK = 2
DFCS_BUTTONRADIO = 4
DFCS_BUTTON3STATE = 8
DFCS_BUTTONPUSH = 16
DFCS_INACTIVE = 256
DFCS_PUSHED = 512
DFCS_CHECKED = 1024
DFCS_TRANSPARENT = 2048
DFCS_HOT = 4096
DFCS_ADJUSTRECT = 8192
DFCS_FLAT = 16384
DFCS_MONO = 32768
DC_ACTIVE = 1
DC_SMALLCAP = 2
DC_ICON = 4
DC_TEXT = 8
DC_INBUTTON = 16
DC_GRADIENT = 32
IDANI_OPEN = 1
IDANI_CLOSE = 2
IDANI_CAPTION = 3
CF_TEXT = 1
CF_BITMAP = 2
CF_METAFILEPICT = 3
CF_SYLK = 4
CF_DIF = 5
CF_TIFF = 6
CF_OEMTEXT = 7
CF_DIB = 8
CF_PALETTE = 9
CF_PENDATA = 10
CF_RIFF = 11
CF_WAVE = 12
CF_UNICODETEXT = 13
CF_ENHMETAFILE = 14
CF_HDROP = 15
CF_LOCALE = 16
CF_DIBV5 = 17
CF_MAX = 18
CF_OWNERDISPLAY = 128
CF_DSPTEXT = 129
CF_DSPBITMAP = 130
CF_DSPMETAFILEPICT = 131
CF_DSPENHMETAFILE = 142
CF_PRIVATEFIRST = 512
CF_PRIVATELAST = 767
CF_GDIOBJFIRST = 768
CF_GDIOBJLAST = 1023
FVIRTKEY = 1
FNOINVERT = 2
FSHIFT = 4
FCONTROL = 8
FALT = 16
WPF_SETMINPOSITION = 1
WPF_RESTORETOMAXIMIZED = 2
ODT_MENU = 1
ODT_LISTBOX = 2
ODT_COMBOBOX = 3
ODT_BUTTON = 4
ODT_STATIC = 5
ODA_DRAWENTIRE = 1
ODA_SELECT = 2
ODA_FOCUS = 4
ODS_SELECTED = 1
ODS_GRAYED = 2
ODS_DISABLED = 4
ODS_CHECKED = 8
ODS_FOCUS = 16
ODS_DEFAULT = 32
ODS_COMBOBOXEDIT = 4096
ODS_HOTLIGHT = 64
ODS_INACTIVE = 128
PM_NOREMOVE = 0
PM_REMOVE = 1
PM_NOYIELD = 2
MOD_ALT = 1
MOD_CONTROL = 2
MOD_SHIFT = 4
MOD_WIN = 8
MOD_NOREPEAT = 16384
IDHOT_SNAPWINDOW = -1
IDHOT_SNAPDESKTOP = -2
# EW_RESTARTWINDOWS = 0x0042
# EW_REBOOTSYSTEM = 0x0043
# EW_EXITANDEXECAPP = 0x0044
ENDSESSION_LOGOFF = -**********
EWX_LOGOFF = 0
EWX_SHUTDOWN = 1
EWX_REBOOT = 2
EWX_FORCE = 4
EWX_POWEROFF = 8
EWX_FORCEIFHUNG = 16
BSM_ALLDESKTOPS = 16
BROADCAST_QUERY_DENY = 1112363332  # Return this value to deny a query.

DBWF_LPARAMPOINTER = 32768

# winuser.h line 3232
SWP_NOSIZE = 1
SWP_NOMOVE = 2
SWP_NOZORDER = 4
SWP_NOREDRAW = 8
SWP_NOACTIVATE = 16
SWP_FRAMECHANGED = 32
SWP_SHOWWINDOW = 64
SWP_HIDEWINDOW = 128
SWP_NOCOPYBITS = 256
SWP_NOOWNERZORDER = 512
SWP_NOSENDCHANGING = 1024
SWP_DRAWFRAME = SWP_FRAMECHANGED
SWP_NOREPOSITION = SWP_NOOWNERZORDER
SWP_DEFERERASE = 8192
SWP_ASYNCWINDOWPOS = 16384

DLGWINDOWEXTRA = 30
# winuser.h line 4249
KEYEVENTF_EXTENDEDKEY = 1
KEYEVENTF_KEYUP = 2
KEYEVENTF_UNICODE = 4
KEYEVENTF_SCANCODE = 8
MOUSEEVENTF_MOVE = 1
MOUSEEVENTF_LEFTDOWN = 2
MOUSEEVENTF_LEFTUP = 4
MOUSEEVENTF_RIGHTDOWN = 8
MOUSEEVENTF_RIGHTUP = 16
MOUSEEVENTF_MIDDLEDOWN = 32
MOUSEEVENTF_MIDDLEUP = 64
MOUSEEVENTF_XDOWN = 128
MOUSEEVENTF_XUP = 256
MOUSEEVENTF_WHEEL = 2048
MOUSEEVENTF_HWHEEL = 4096
MOUSEEVENTF_MOVE_NOCOALESCE = 8192
MOUSEEVENTF_VIRTUALDESK = 16384
MOUSEEVENTF_ABSOLUTE = 32768
INPUT_MOUSE = 0
INPUT_KEYBOARD = 1
INPUT_HARDWARE = 2
MWMO_WAITALL = 1
MWMO_ALERTABLE = 2
MWMO_INPUTAVAILABLE = 4
QS_KEY = 1
QS_MOUSEMOVE = 2
QS_MOUSEBUTTON = 4
QS_POSTMESSAGE = 8
QS_TIMER = 16
QS_PAINT = 32
QS_SENDMESSAGE = 64
QS_HOTKEY = 128
QS_MOUSE = QS_MOUSEMOVE | QS_MOUSEBUTTON
QS_INPUT = QS_MOUSE | QS_KEY
QS_ALLEVENTS = QS_INPUT | QS_POSTMESSAGE | QS_TIMER | QS_PAINT | QS_HOTKEY
QS_ALLINPUT = (
    QS_INPUT | QS_POSTMESSAGE | QS_TIMER | QS_PAINT | QS_HOTKEY | QS_SENDMESSAGE
)


IMN_CLOSESTATUSWINDOW = 1
IMN_OPENSTATUSWINDOW = 2
IMN_CHANGECANDIDATE = 3
IMN_CLOSECANDIDATE = 4
IMN_OPENCANDIDATE = 5
IMN_SETCONVERSIONMODE = 6
IMN_SETSENTENCEMODE = 7
IMN_SETOPENSTATUS = 8
IMN_SETCANDIDATEPOS = 9
IMN_SETCOMPOSITIONFONT = 10
IMN_SETCOMPOSITIONWINDOW = 11
IMN_SETSTATUSWINDOWPOS = 12
IMN_GUIDELINE = 13
IMN_PRIVATE = 14

# winuser.h line 8518
HELP_CONTEXT = 1
HELP_QUIT = 2
HELP_INDEX = 3
HELP_CONTENTS = 3
HELP_HELPONHELP = 4
HELP_SETINDEX = 5
HELP_SETCONTENTS = 5
HELP_CONTEXTPOPUP = 8
HELP_FORCEFILE = 9
HELP_KEY = 257
HELP_COMMAND = 258
HELP_PARTIALKEY = 261
HELP_MULTIKEY = 513
HELP_SETWINPOS = 515
HELP_CONTEXTMENU = 10
HELP_FINDER = 11
HELP_WM_HELP = 12
HELP_SETPOPUP_POS = 13
HELP_TCARD = 32768
HELP_TCARD_DATA = 16
HELP_TCARD_OTHER_CALLER = 17
IDH_NO_HELP = 28440
IDH_MISSING_CONTEXT = 28441  # Control doesn't have matching help context
IDH_GENERIC_HELP_BUTTON = 28442  # Property sheet help button
IDH_OK = 28443
IDH_CANCEL = 28444
IDH_HELP = 28445
GR_GDIOBJECTS = 0  # Count of GDI objects
GR_USEROBJECTS = 1  # Count of USER objects
# Generated by h2py from \msvcnt\include\wingdi.h
# manually added (missed by generation some how!
SRCCOPY = 13369376  # dest = source
SRCPAINT = 15597702  # dest = source OR dest
SRCAND = 8913094  # dest = source AND dest
SRCINVERT = 6684742  # dest = source XOR dest
SRCERASE = 4457256  # dest = source AND (NOT dest )
NOTSRCCOPY = 3342344  # dest = (NOT source)
NOTSRCERASE = 1114278  # dest = (NOT src) AND (NOT dest)
MERGECOPY = 12583114  # dest = (source AND pattern)
MERGEPAINT = 12255782  # dest = (NOT source) OR dest
PATCOPY = 15728673  # dest = pattern
PATPAINT = 16452105  # dest = DPSnoo
PATINVERT = 5898313  # dest = pattern XOR dest
DSTINVERT = 5570569  # dest = (NOT dest)
BLACKNESS = 66  # dest = BLACK
WHITENESS = 16711778  # dest = WHITE

# hacked and split manually by mhammond.
R2_BLACK = 1
R2_NOTMERGEPEN = 2
R2_MASKNOTPEN = 3
R2_NOTCOPYPEN = 4
R2_MASKPENNOT = 5
R2_NOT = 6
R2_XORPEN = 7
R2_NOTMASKPEN = 8
R2_MASKPEN = 9
R2_NOTXORPEN = 10
R2_NOP = 11
R2_MERGENOTPEN = 12
R2_COPYPEN = 13
R2_MERGEPENNOT = 14
R2_MERGEPEN = 15
R2_WHITE = 16
R2_LAST = 16
GDI_ERROR = -1
ERROR = 0
NULLREGION = 1
SIMPLEREGION = 2
COMPLEXREGION = 3
RGN_ERROR = ERROR
RGN_AND = 1
RGN_OR = 2
RGN_XOR = 3
RGN_DIFF = 4
RGN_COPY = 5
RGN_MIN = RGN_AND
RGN_MAX = RGN_COPY

## Stretching modes used with Get/SetStretchBltMode
BLACKONWHITE = 1
WHITEONBLACK = 2
COLORONCOLOR = 3
HALFTONE = 4
MAXSTRETCHBLTMODE = 4
STRETCH_ANDSCANS = BLACKONWHITE
STRETCH_ORSCANS = WHITEONBLACK
STRETCH_DELETESCANS = COLORONCOLOR
STRETCH_HALFTONE = HALFTONE

ALTERNATE = 1
WINDING = 2
POLYFILL_LAST = 2

## flags used with SetLayout
LAYOUT_RTL = 1
LAYOUT_BTT = 2
LAYOUT_VBH = 4
LAYOUT_ORIENTATIONMASK = LAYOUT_RTL | LAYOUT_BTT | LAYOUT_VBH
LAYOUT_BITMAPORIENTATIONPRESERVED = 8

TA_NOUPDATECP = 0
TA_UPDATECP = 1
TA_LEFT = 0
TA_RIGHT = 2
TA_CENTER = 6
TA_TOP = 0
TA_BOTTOM = 8
TA_BASELINE = 24
TA_MASK = TA_BASELINE + TA_CENTER + TA_UPDATECP
VTA_BASELINE = TA_BASELINE
VTA_LEFT = TA_BOTTOM
VTA_RIGHT = TA_TOP
VTA_CENTER = TA_CENTER
VTA_BOTTOM = TA_RIGHT
VTA_TOP = TA_LEFT
ETO_GRAYED = 1
ETO_OPAQUE = 2
ETO_CLIPPED = 4
ASPECT_FILTERING = 1
DCB_RESET = 1
DCB_ACCUMULATE = 2
DCB_DIRTY = DCB_ACCUMULATE
DCB_SET = DCB_RESET | DCB_ACCUMULATE
DCB_ENABLE = 4
DCB_DISABLE = 8
META_SETBKCOLOR = 513
META_SETBKMODE = 258
META_SETMAPMODE = 259
META_SETROP2 = 260
META_SETRELABS = 261
META_SETPOLYFILLMODE = 262
META_SETSTRETCHBLTMODE = 263
META_SETTEXTCHAREXTRA = 264
META_SETTEXTCOLOR = 521
META_SETTEXTJUSTIFICATION = 522
META_SETWINDOWORG = 523
META_SETWINDOWEXT = 524
META_SETVIEWPORTORG = 525
META_SETVIEWPORTEXT = 526
META_OFFSETWINDOWORG = 527
META_SCALEWINDOWEXT = 1040
META_OFFSETVIEWPORTORG = 529
META_SCALEVIEWPORTEXT = 1042
META_LINETO = 531
META_MOVETO = 532
META_EXCLUDECLIPRECT = 1045
META_INTERSECTCLIPRECT = 1046
META_ARC = 2071
META_ELLIPSE = 1048
META_FLOODFILL = 1049
META_PIE = 2074
META_RECTANGLE = 1051
META_ROUNDRECT = 1564
META_PATBLT = 1565
META_SAVEDC = 30
META_SETPIXEL = 1055
META_OFFSETCLIPRGN = 544
META_TEXTOUT = 1313
META_BITBLT = 2338
META_STRETCHBLT = 2851
META_POLYGON = 804
META_POLYLINE = 805
META_ESCAPE = 1574
META_RESTOREDC = 295
META_FILLREGION = 552
META_FRAMEREGION = 1065
META_INVERTREGION = 298
META_PAINTREGION = 299
META_SELECTCLIPREGION = 300
META_SELECTOBJECT = 301
META_SETTEXTALIGN = 302
META_CHORD = 2096
META_SETMAPPERFLAGS = 561
META_EXTTEXTOUT = 2610
META_SETDIBTODEV = 3379
META_SELECTPALETTE = 564
META_REALIZEPALETTE = 53
META_ANIMATEPALETTE = 1078
META_SETPALENTRIES = 55
META_POLYPOLYGON = 1336
META_RESIZEPALETTE = 313
META_DIBBITBLT = 2368
META_DIBSTRETCHBLT = 2881
META_DIBCREATEPATTERNBRUSH = 322
META_STRETCHDIB = 3907
META_EXTFLOODFILL = 1352
META_DELETEOBJECT = 496
META_CREATEPALETTE = 247
META_CREATEPATTERNBRUSH = 505
META_CREATEPENINDIRECT = 762
META_CREATEFONTINDIRECT = 763
META_CREATEBRUSHINDIRECT = 764
META_CREATEREGION = 1791
FILE_BEGIN = 0
FILE_CURRENT = 1
FILE_END = 2
FILE_FLAG_WRITE_THROUGH = -**********
FILE_FLAG_OVERLAPPED = **********
FILE_FLAG_NO_BUFFERING = *********
FILE_FLAG_RANDOM_ACCESS = *********
FILE_FLAG_SEQUENTIAL_SCAN = *********
FILE_FLAG_DELETE_ON_CLOSE = ********
FILE_FLAG_BACKUP_SEMANTICS = 33554432
FILE_FLAG_POSIX_SEMANTICS = 16777216
CREATE_NEW = 1
CREATE_ALWAYS = 2
OPEN_EXISTING = 3
OPEN_ALWAYS = 4
TRUNCATE_EXISTING = 5
PIPE_ACCESS_INBOUND = 1
PIPE_ACCESS_OUTBOUND = 2
PIPE_ACCESS_DUPLEX = 3
PIPE_CLIENT_END = 0
PIPE_SERVER_END = 1
PIPE_WAIT = 0
PIPE_NOWAIT = 1
PIPE_READMODE_BYTE = 0
PIPE_READMODE_MESSAGE = 2
PIPE_TYPE_BYTE = 0
PIPE_TYPE_MESSAGE = 4
PIPE_UNLIMITED_INSTANCES = 255
SECURITY_CONTEXT_TRACKING = 262144
SECURITY_EFFECTIVE_ONLY = 524288
SECURITY_SQOS_PRESENT = 1048576
SECURITY_VALID_SQOS_FLAGS = 2031616
DTR_CONTROL_DISABLE = 0
DTR_CONTROL_ENABLE = 1
DTR_CONTROL_HANDSHAKE = 2
RTS_CONTROL_DISABLE = 0
RTS_CONTROL_ENABLE = 1
RTS_CONTROL_HANDSHAKE = 2
RTS_CONTROL_TOGGLE = 3
GMEM_FIXED = 0
GMEM_MOVEABLE = 2
GMEM_NOCOMPACT = 16
GMEM_NODISCARD = 32
GMEM_ZEROINIT = 64
GMEM_MODIFY = 128
GMEM_DISCARDABLE = 256
GMEM_NOT_BANKED = 4096
GMEM_SHARE = 8192
GMEM_DDESHARE = 8192
GMEM_NOTIFY = 16384
GMEM_LOWER = GMEM_NOT_BANKED
GMEM_VALID_FLAGS = 32626
GMEM_INVALID_HANDLE = 32768
GHND = GMEM_MOVEABLE | GMEM_ZEROINIT
GPTR = GMEM_FIXED | GMEM_ZEROINIT
GMEM_DISCARDED = 16384
GMEM_LOCKCOUNT = 255
LMEM_FIXED = 0
LMEM_MOVEABLE = 2
LMEM_NOCOMPACT = 16
LMEM_NODISCARD = 32
LMEM_ZEROINIT = 64
LMEM_MODIFY = 128
LMEM_DISCARDABLE = 3840
LMEM_VALID_FLAGS = 3954
LMEM_INVALID_HANDLE = 32768
LHND = LMEM_MOVEABLE | LMEM_ZEROINIT
LPTR = LMEM_FIXED | LMEM_ZEROINIT
NONZEROLHND = LMEM_MOVEABLE
NONZEROLPTR = LMEM_FIXED
LMEM_DISCARDED = 16384
LMEM_LOCKCOUNT = 255
DEBUG_PROCESS = 1
DEBUG_ONLY_THIS_PROCESS = 2
CREATE_SUSPENDED = 4
DETACHED_PROCESS = 8
CREATE_NEW_CONSOLE = 16
NORMAL_PRIORITY_CLASS = 32
IDLE_PRIORITY_CLASS = 64
HIGH_PRIORITY_CLASS = 128
REALTIME_PRIORITY_CLASS = 256
CREATE_NEW_PROCESS_GROUP = 512
CREATE_UNICODE_ENVIRONMENT = 1024
CREATE_SEPARATE_WOW_VDM = 2048
CREATE_SHARED_WOW_VDM = 4096
CREATE_DEFAULT_ERROR_MODE = ********
CREATE_NO_WINDOW = *********
PROFILE_USER = *********
PROFILE_KERNEL = *********
PROFILE_SERVER = **********
THREAD_BASE_PRIORITY_LOWRT = 15
THREAD_BASE_PRIORITY_MAX = 2
THREAD_BASE_PRIORITY_MIN = -2
THREAD_BASE_PRIORITY_IDLE = -15
THREAD_PRIORITY_LOWEST = THREAD_BASE_PRIORITY_MIN
THREAD_PRIORITY_BELOW_NORMAL = THREAD_PRIORITY_LOWEST + 1
THREAD_PRIORITY_HIGHEST = THREAD_BASE_PRIORITY_MAX
THREAD_PRIORITY_ABOVE_NORMAL = THREAD_PRIORITY_HIGHEST - 1
THREAD_PRIORITY_ERROR_RETURN = MAXLONG
THREAD_PRIORITY_TIME_CRITICAL = THREAD_BASE_PRIORITY_LOWRT
THREAD_PRIORITY_IDLE = THREAD_BASE_PRIORITY_IDLE
THREAD_PRIORITY_NORMAL = 0
THREAD_MODE_BACKGROUND_BEGIN = 0x00010000
THREAD_MODE_BACKGROUND_END = 0x00020000

EXCEPTION_DEBUG_EVENT = 1
CREATE_THREAD_DEBUG_EVENT = 2
CREATE_PROCESS_DEBUG_EVENT = 3
EXIT_THREAD_DEBUG_EVENT = 4
EXIT_PROCESS_DEBUG_EVENT = 5
LOAD_DLL_DEBUG_EVENT = 6
UNLOAD_DLL_DEBUG_EVENT = 7
OUTPUT_DEBUG_STRING_EVENT = 8
RIP_EVENT = 9
DRIVE_UNKNOWN = 0
DRIVE_NO_ROOT_DIR = 1
DRIVE_REMOVABLE = 2
DRIVE_FIXED = 3
DRIVE_REMOTE = 4
DRIVE_CDROM = 5
DRIVE_RAMDISK = 6
FILE_TYPE_UNKNOWN = 0
FILE_TYPE_DISK = 1
FILE_TYPE_CHAR = 2
FILE_TYPE_PIPE = 3
FILE_TYPE_REMOTE = 32768
NOPARITY = 0
ODDPARITY = 1
EVENPARITY = 2
MARKPARITY = 3
SPACEPARITY = 4
ONESTOPBIT = 0
ONE5STOPBITS = 1
TWOSTOPBITS = 2
CBR_110 = 110
CBR_300 = 300
CBR_600 = 600
CBR_1200 = 1200
CBR_2400 = 2400
CBR_4800 = 4800
CBR_9600 = 9600
CBR_14400 = 14400
CBR_19200 = 19200
CBR_38400 = 38400
CBR_56000 = 56000
CBR_57600 = 57600
CBR_115200 = 115200
CBR_128000 = 128000
CBR_256000 = 256000
S_QUEUEEMPTY = 0
S_THRESHOLD = 1
S_ALLTHRESHOLD = 2
S_NORMAL = 0
S_LEGATO = 1
S_STACCATO = 2
NMPWAIT_WAIT_FOREVER = -1
NMPWAIT_NOWAIT = 1
NMPWAIT_USE_DEFAULT_WAIT = 0
OF_READ = 0
OF_WRITE = 1
OF_READWRITE = 2
OF_SHARE_COMPAT = 0
OF_SHARE_EXCLUSIVE = 16
OF_SHARE_DENY_WRITE = 32
OF_SHARE_DENY_READ = 48
OF_SHARE_DENY_NONE = 64
OF_PARSE = 256
OF_DELETE = 512
OF_VERIFY = 1024
OF_CANCEL = 2048
OF_CREATE = 4096
OF_PROMPT = 8192
OF_EXIST = 16384
OF_REOPEN = 32768
OFS_MAXPATHNAME = 128
MAXINTATOM = 49152

# winbase.h
PROCESS_HEAP_REGION = 1
PROCESS_HEAP_UNCOMMITTED_RANGE = 2
PROCESS_HEAP_ENTRY_BUSY = 4
PROCESS_HEAP_ENTRY_MOVEABLE = 16
PROCESS_HEAP_ENTRY_DDESHARE = 32
SCS_32BIT_BINARY = 0
SCS_DOS_BINARY = 1
SCS_WOW_BINARY = 2
SCS_PIF_BINARY = 3
SCS_POSIX_BINARY = 4
SCS_OS216_BINARY = 5
SEM_FAILCRITICALERRORS = 1
SEM_NOGPFAULTERRORBOX = 2
SEM_NOALIGNMENTFAULTEXCEPT = 4
SEM_NOOPENFILEERRORBOX = 32768
LOCKFILE_FAIL_IMMEDIATELY = 1
LOCKFILE_EXCLUSIVE_LOCK = 2
HANDLE_FLAG_INHERIT = 1
HANDLE_FLAG_PROTECT_FROM_CLOSE = 2
HINSTANCE_ERROR = 32
GET_TAPE_MEDIA_INFORMATION = 0
GET_TAPE_DRIVE_INFORMATION = 1
SET_TAPE_MEDIA_INFORMATION = 0
SET_TAPE_DRIVE_INFORMATION = 1
FORMAT_MESSAGE_ALLOCATE_BUFFER = 256
FORMAT_MESSAGE_IGNORE_INSERTS = 512
FORMAT_MESSAGE_FROM_STRING = 1024
FORMAT_MESSAGE_FROM_HMODULE = 2048
FORMAT_MESSAGE_FROM_SYSTEM = 4096
FORMAT_MESSAGE_ARGUMENT_ARRAY = 8192
FORMAT_MESSAGE_MAX_WIDTH_MASK = 255
BACKUP_INVALID = 0
BACKUP_DATA = 1
BACKUP_EA_DATA = 2
BACKUP_SECURITY_DATA = 3
BACKUP_ALTERNATE_DATA = 4
BACKUP_LINK = 5
BACKUP_PROPERTY_DATA = 6
BACKUP_OBJECT_ID = 7
BACKUP_REPARSE_DATA = 8
BACKUP_SPARSE_BLOCK = 9

STREAM_NORMAL_ATTRIBUTE = 0
STREAM_MODIFIED_WHEN_READ = 1
STREAM_CONTAINS_SECURITY = 2
STREAM_CONTAINS_PROPERTIES = 4
STARTF_USESHOWWINDOW = 1
STARTF_USESIZE = 2
STARTF_USEPOSITION = 4
STARTF_USECOUNTCHARS = 8
STARTF_USEFILLATTRIBUTE = 16
STARTF_FORCEONFEEDBACK = 64
STARTF_FORCEOFFFEEDBACK = 128
STARTF_USESTDHANDLES = 256
STARTF_USEHOTKEY = 512
SHUTDOWN_NORETRY = 1
DONT_RESOLVE_DLL_REFERENCES = 1
LOAD_LIBRARY_AS_DATAFILE = 2
LOAD_WITH_ALTERED_SEARCH_PATH = 8
DDD_RAW_TARGET_PATH = 1
DDD_REMOVE_DEFINITION = 2
DDD_EXACT_MATCH_ON_REMOVE = 4
MOVEFILE_REPLACE_EXISTING = 1
MOVEFILE_COPY_ALLOWED = 2
MOVEFILE_DELAY_UNTIL_REBOOT = 4
MAX_COMPUTERNAME_LENGTH = 15
LOGON32_LOGON_INTERACTIVE = 2
LOGON32_LOGON_NETWORK = 3
LOGON32_LOGON_BATCH = 4
LOGON32_LOGON_SERVICE = 5
LOGON32_LOGON_UNLOCK = 7
LOGON32_LOGON_NETWORK_CLEARTEXT = 8
LOGON32_LOGON_NEW_CREDENTIALS = 9
LOGON32_PROVIDER_DEFAULT = 0
LOGON32_PROVIDER_WINNT35 = 1
LOGON32_PROVIDER_WINNT40 = 2
LOGON32_PROVIDER_WINNT50 = 3
VER_PLATFORM_WIN32s = 0
VER_PLATFORM_WIN32_WINDOWS = 1
VER_PLATFORM_WIN32_NT = 2
TC_NORMAL = 0
TC_HARDERR = 1
TC_GP_TRAP = 2
TC_SIGNAL = 3
AC_LINE_OFFLINE = 0
AC_LINE_ONLINE = 1
AC_LINE_BACKUP_POWER = 2
AC_LINE_UNKNOWN = 255
BATTERY_FLAG_HIGH = 1
BATTERY_FLAG_LOW = 2
BATTERY_FLAG_CRITICAL = 4
BATTERY_FLAG_CHARGING = 8
BATTERY_FLAG_NO_BATTERY = 128
BATTERY_FLAG_UNKNOWN = 255
BATTERY_PERCENTAGE_UNKNOWN = 255
BATTERY_LIFE_UNKNOWN = -1

# Generated by h2py from d:\msdev\include\richedit.h
cchTextLimitDefault = 32767
WM_CONTEXTMENU = 123
WM_PRINTCLIENT = 792
EN_MSGFILTER = 1792
EN_REQUESTRESIZE = 1793
EN_SELCHANGE = 1794
EN_DROPFILES = 1795
EN_PROTECTED = 1796
EN_CORRECTTEXT = 1797
EN_STOPNOUNDO = 1798
EN_IMECHANGE = 1799
EN_SAVECLIPBOARD = 1800
EN_OLEOPFAILED = 1801
ENM_NONE = 0
ENM_CHANGE = 1
ENM_UPDATE = 2
ENM_SCROLL = 4
ENM_KEYEVENTS = 65536
ENM_MOUSEEVENTS = 131072
ENM_REQUESTRESIZE = 262144
ENM_SELCHANGE = 524288
ENM_DROPFILES = 1048576
ENM_PROTECTED = 2097152
ENM_CORRECTTEXT = 4194304
ENM_IMECHANGE = 8388608
ES_SAVESEL = 32768
ES_SUNKEN = 16384
ES_DISABLENOSCROLL = 8192
ES_SELECTIONBAR = 16777216
ES_EX_NOCALLOLEINIT = 16777216
ES_VERTICAL = 4194304
ES_NOIME = 524288
ES_SELFIME = 262144
ECO_AUTOWORDSELECTION = 1
ECO_AUTOVSCROLL = 64
ECO_AUTOHSCROLL = 128
ECO_NOHIDESEL = 256
ECO_READONLY = 2048
ECO_WANTRETURN = 4096
ECO_SAVESEL = 32768
ECO_SELECTIONBAR = 16777216
ECO_VERTICAL = 4194304
ECOOP_SET = 1
ECOOP_OR = 2
ECOOP_AND = 3
ECOOP_XOR = 4
WB_CLASSIFY = 3
WB_MOVEWORDLEFT = 4
WB_MOVEWORDRIGHT = 5
WB_LEFTBREAK = 6
WB_RIGHTBREAK = 7
WB_MOVEWORDPREV = 4
WB_MOVEWORDNEXT = 5
WB_PREVBREAK = 6
WB_NEXTBREAK = 7
PC_FOLLOWING = 1
PC_LEADING = 2
PC_OVERFLOW = 3
PC_DELIMITER = 4
WBF_WORDWRAP = 16
WBF_WORDBREAK = 32
WBF_OVERFLOW = 64
WBF_LEVEL1 = 128
WBF_LEVEL2 = 256
WBF_CUSTOM = 512
CFM_BOLD = 1
CFM_ITALIC = 2
CFM_UNDERLINE = 4
CFM_STRIKEOUT = 8
CFM_PROTECTED = 16
CFM_SIZE = -**********
CFM_COLOR = **********
CFM_FACE = *********
CFM_OFFSET = *********
CFM_CHARSET = *********
CFE_BOLD = 1
CFE_ITALIC = 2
CFE_UNDERLINE = 4
CFE_STRIKEOUT = 8
CFE_PROTECTED = 16
CFE_AUTOCOLOR = **********
yHeightCharPtsMost = 1638
SCF_SELECTION = 1
SCF_WORD = 2
SF_TEXT = 1
SF_RTF = 2
SF_RTFNOOBJS = 3
SF_TEXTIZED = 4
SFF_SELECTION = 32768
SFF_PLAINRTF = 16384
MAX_TAB_STOPS = 32
lDefaultTab = 720
PFM_STARTINDENT = 1
PFM_RIGHTINDENT = 2
PFM_OFFSET = 4
PFM_ALIGNMENT = 8
PFM_TABSTOPS = 16
PFM_NUMBERING = 32
PFM_OFFSETINDENT = -**********
PFN_BULLET = 1
PFA_LEFT = 1
PFA_RIGHT = 2
PFA_CENTER = 3
WM_NOTIFY = 78
SEL_EMPTY = 0
SEL_TEXT = 1
SEL_OBJECT = 2
SEL_MULTICHAR = 4
SEL_MULTIOBJECT = 8
OLEOP_DOVERB = 1
CF_RTF = "Rich Text Format"
CF_RTFNOOBJS = "Rich Text Format Without Objects"
CF_RETEXTOBJ = "RichEdit Text and Objects"

# From wincon.h
RIGHT_ALT_PRESSED = 1  # the right alt key is pressed.
LEFT_ALT_PRESSED = 2  # the left alt key is pressed.
RIGHT_CTRL_PRESSED = 4  # the right ctrl key is pressed.
LEFT_CTRL_PRESSED = 8  # the left ctrl key is pressed.
SHIFT_PRESSED = 16  # the shift key is pressed.
NUMLOCK_ON = 32  # the numlock light is on.
SCROLLLOCK_ON = 64  # the scrolllock light is on.
CAPSLOCK_ON = 128  # the capslock light is on.
ENHANCED_KEY = 256  # the key is enhanced.
NLS_DBCSCHAR = 65536  # DBCS for JPN: SBCS/DBCS mode.
NLS_ALPHANUMERIC = 0  # DBCS for JPN: Alphanumeric mode.
NLS_KATAKANA = 131072  # DBCS for JPN: Katakana mode.
NLS_HIRAGANA = 262144  # DBCS for JPN: Hiragana mode.
NLS_ROMAN = 4194304  # DBCS for JPN: Roman/Noroman mode.
NLS_IME_CONVERSION = 8388608  # DBCS for JPN: IME conversion.
NLS_IME_DISABLE = *********  # DBCS for JPN: IME enable/disable.

FROM_LEFT_1ST_BUTTON_PRESSED = 1
RIGHTMOST_BUTTON_PRESSED = 2
FROM_LEFT_2ND_BUTTON_PRESSED = 4
FROM_LEFT_3RD_BUTTON_PRESSED = 8
FROM_LEFT_4TH_BUTTON_PRESSED = 16

CTRL_C_EVENT = 0
CTRL_BREAK_EVENT = 1
CTRL_CLOSE_EVENT = 2
CTRL_LOGOFF_EVENT = 5
CTRL_SHUTDOWN_EVENT = 6

MOUSE_MOVED = 1
DOUBLE_CLICK = 2
MOUSE_WHEELED = 4

# property sheet window messages from prsht.h
PSM_SETCURSEL = WM_USER + 101
PSM_REMOVEPAGE = WM_USER + 102
PSM_ADDPAGE = WM_USER + 103
PSM_CHANGED = WM_USER + 104
PSM_RESTARTWINDOWS = WM_USER + 105
PSM_REBOOTSYSTEM = WM_USER + 106
PSM_CANCELTOCLOSE = WM_USER + 107
PSM_QUERYSIBLINGS = WM_USER + 108
PSM_UNCHANGED = WM_USER + 109
PSM_APPLY = WM_USER + 110
PSM_SETTITLEA = WM_USER + 111
PSM_SETTITLEW = WM_USER + 120
PSM_SETWIZBUTTONS = WM_USER + 112
PSM_PRESSBUTTON = WM_USER + 113
PSM_SETCURSELID = WM_USER + 114
PSM_SETFINISHTEXTA = WM_USER + 115
PSM_SETFINISHTEXTW = WM_USER + 121
PSM_GETTABCONTROL = WM_USER + 116
PSM_ISDIALOGMESSAGE = WM_USER + 117
PSM_GETCURRENTPAGEHWND = WM_USER + 118
PSM_INSERTPAGE = WM_USER + 119
PSM_SETHEADERTITLEA = WM_USER + 125
PSM_SETHEADERTITLEW = WM_USER + 126
PSM_SETHEADERSUBTITLEA = WM_USER + 127
PSM_SETHEADERSUBTITLEW = WM_USER + 128
PSM_HWNDTOINDEX = WM_USER + 129
PSM_INDEXTOHWND = WM_USER + 130
PSM_PAGETOINDEX = WM_USER + 131
PSM_INDEXTOPAGE = WM_USER + 132
PSM_IDTOINDEX = WM_USER + 133
PSM_INDEXTOID = WM_USER + 134
PSM_GETRESULT = WM_USER + 135
PSM_RECALCPAGESIZES = WM_USER + 136

# GetUserNameEx/GetComputerNameEx
NameUnknown = 0
NameFullyQualifiedDN = 1
NameSamCompatible = 2
NameDisplay = 3
NameUniqueId = 6
NameCanonical = 7
NameUserPrincipal = 8
NameCanonicalEx = 9
NameServicePrincipal = 10
NameDnsDomain = 12

ComputerNameNetBIOS = 0
ComputerNameDnsHostname = 1
ComputerNameDnsDomain = 2
ComputerNameDnsFullyQualified = 3
ComputerNamePhysicalNetBIOS = 4
ComputerNamePhysicalDnsHostname = 5
ComputerNamePhysicalDnsDomain = 6
ComputerNamePhysicalDnsFullyQualified = 7

LWA_COLORKEY = 0x00000001
LWA_ALPHA = 0x00000002
ULW_COLORKEY = 0x00000001
ULW_ALPHA = 0x00000002
ULW_OPAQUE = 0x00000004

# WinDef.h
TRUE = 1
FALSE = 0
MAX_PATH = 260
# WinGDI.h
AC_SRC_OVER = 0
AC_SRC_ALPHA = 1
GRADIENT_FILL_RECT_H = 0
GRADIENT_FILL_RECT_V = 1
GRADIENT_FILL_TRIANGLE = 2
GRADIENT_FILL_OP_FLAG = 255

## flags used with Get/SetSystemFileCacheSize
MM_WORKING_SET_MAX_HARD_ENABLE = 1
MM_WORKING_SET_MAX_HARD_DISABLE = 2
MM_WORKING_SET_MIN_HARD_ENABLE = 4
MM_WORKING_SET_MIN_HARD_DISABLE = 8

## Flags for GetFinalPathNameByHandle
VOLUME_NAME_DOS = 0
VOLUME_NAME_GUID = 1
VOLUME_NAME_NT = 2
VOLUME_NAME_NONE = 4
FILE_NAME_NORMALIZED = 0
FILE_NAME_OPENED = 8

DEVICE_NOTIFY_WINDOW_HANDLE = 0x00000000
DEVICE_NOTIFY_SERVICE_HANDLE = 0x00000001

# From Dbt.h
# Generated by h2py from Dbt.h
WM_DEVICECHANGE = 0x0219
BSF_QUERY = 0x00000001
BSF_IGNORECURRENTTASK = 0x00000002
BSF_FLUSHDISK = 0x00000004
BSF_NOHANG = 0x00000008
BSF_POSTMESSAGE = 0x00000010
BSF_FORCEIFHUNG = 0x00000020
BSF_NOTIMEOUTIFNOTHUNG = 0x00000040
BSF_MSGSRV32ISOK = -**********
BSF_MSGSRV32ISOK_BIT = 31
BSM_ALLCOMPONENTS = 0x00000000
BSM_VXDS = 0x00000001
BSM_NETDRIVER = 0x00000002
BSM_INSTALLABLEDRIVERS = 0x00000004
BSM_APPLICATIONS = 0x00000008
DBT_APPYBEGIN = 0x0000
DBT_APPYEND = 0x0001
DBT_DEVNODES_CHANGED = 0x0007
DBT_QUERYCHANGECONFIG = 0x0017
DBT_CONFIGCHANGED = 0x0018
DBT_CONFIGCHANGECANCELED = 0x0019
DBT_MONITORCHANGE = 0x001B
DBT_SHELLLOGGEDON = 0x0020
DBT_CONFIGMGAPI32 = 0x0022
DBT_VXDINITCOMPLETE = 0x0023
DBT_VOLLOCKQUERYLOCK = 0x8041
DBT_VOLLOCKLOCKTAKEN = 0x8042
DBT_VOLLOCKLOCKFAILED = 0x8043
DBT_VOLLOCKQUERYUNLOCK = 0x8044
DBT_VOLLOCKLOCKRELEASED = 0x8045
DBT_VOLLOCKUNLOCKFAILED = 0x8046
LOCKP_ALLOW_WRITES = 0x01
LOCKP_FAIL_WRITES = 0x00
LOCKP_FAIL_MEM_MAPPING = 0x02
LOCKP_ALLOW_MEM_MAPPING = 0x00
LOCKP_USER_MASK = 0x03
LOCKP_LOCK_FOR_FORMAT = 0x04
LOCKF_LOGICAL_LOCK = 0x00
LOCKF_PHYSICAL_LOCK = 0x01
DBT_NO_DISK_SPACE = 0x0047
DBT_LOW_DISK_SPACE = 0x0048
DBT_CONFIGMGPRIVATE = 0x7FFF
DBT_DEVICEARRIVAL = 0x8000
DBT_DEVICEQUERYREMOVE = 0x8001
DBT_DEVICEQUERYREMOVEFAILED = 0x8002
DBT_DEVICEREMOVEPENDING = 0x8003
DBT_DEVICEREMOVECOMPLETE = 0x8004
DBT_DEVICETYPESPECIFIC = 0x8005
DBT_CUSTOMEVENT = 0x8006
DBT_DEVTYP_OEM = 0x00000000
DBT_DEVTYP_DEVNODE = 0x00000001
DBT_DEVTYP_VOLUME = 0x00000002
DBT_DEVTYP_PORT = 0x00000003
DBT_DEVTYP_NET = 0x00000004
DBT_DEVTYP_DEVICEINTERFACE = 0x00000005
DBT_DEVTYP_HANDLE = 0x00000006
DBTF_MEDIA = 0x0001
DBTF_NET = 0x0002
DBTF_RESOURCE = 0x00000001
DBTF_XPORT = 0x00000002
DBTF_SLOWNET = 0x00000004
DBT_VPOWERDAPI = 0x8100
DBT_USERDEFINED = 0xFFFF

# From ime_cmodes.h
# bit field for conversion mode
IME_CMODE_ALPHANUMERIC = 0x0000
IME_CMODE_NATIVE = 0x0001
IME_CMODE_CHINESE = IME_CMODE_NATIVE
IME_CMODE_HANGUL = IME_CMODE_NATIVE
IME_CMODE_JAPANESE = IME_CMODE_NATIVE
IME_CMODE_KATAKANA = 0x0002  # only effect under IME_CMODE_NATIVE
IME_CMODE_LANGUAGE = 0x0003
IME_CMODE_FULLSHAPE = 0x0008
IME_CMODE_ROMAN = 0x0010
IME_CMODE_CHARCODE = 0x0020
IME_CMODE_HANJACONVERT = 0x0040
IME_CMODE_NATIVESYMBOL = 0x0080
