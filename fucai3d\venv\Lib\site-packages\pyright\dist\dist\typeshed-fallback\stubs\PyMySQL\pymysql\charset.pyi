from collections.abc import Callable
from typing import Final

MBLENGTH: Final[dict[int, int]]

class Charset:
    is_default: bool
    def __init__(self, id: int, name: str, collation: str, is_default: bool = False) -> None: ...
    @property
    def encoding(self) -> str: ...
    @property
    def is_binary(self) -> bool: ...

class Charsets:
    def __init__(self) -> None: ...
    def add(self, c: Charset) -> None: ...
    def by_id(self, id: int) -> Charset: ...
    def by_name(self, name: str) -> Charset: ...

_charsets: Charsets
charset_by_name: Callable[[str], Charset]
charset_by_id: Callable[[int], Charset]
