from hvac.api.vault_api_base import VaultApiBase

DEFAULT_MOUNT_POINT: str

class RabbitMQ(VaultApiBase):
    def configure(
        self,
        connection_uri: str = "",
        username: str = "",
        password: str = "",
        verify_connection: bool = True,
        mount_point="rabbitmq",
    ): ...
    def configure_lease(self, ttl, max_ttl, mount_point="rabbitmq"): ...
    def create_role(self, name, tags: str = "", vhosts: str = "", vhost_topics: str = "", mount_point: str = "rabbitmq"): ...
    def read_role(self, name, mount_point="rabbitmq"): ...
    def delete_role(self, name, mount_point="rabbitmq"): ...
    def generate_credentials(self, name, mount_point="rabbitmq"): ...
