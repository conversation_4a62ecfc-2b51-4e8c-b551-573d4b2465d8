from collections.abc import Callable, Iterable
from typing import Any, Final, TypeVar

_T = TypeVar("_T")

UNICODE_ASCII_CHARACTER_SET: Final[str]

def filter_params(
    target: Callable[[dict[str, Any] | Iterable[tuple[str, Any]], _T], object],
) -> Callable[[list[str], _T], object]: ...
def filter_oauth_params(
    params: dict[str, Any] | Iterable[tuple[str, Any]],
) -> list[str]: ...  # we don't care about second (Any) part
def escape(u: str) -> str: ...
def unescape(u: str) -> str: ...
def parse_keqv_list(l: list[str]) -> dict[str, str]: ...
def parse_http_list(u: str) -> list[str]: ...
def parse_authorization_header(authorization_header: str) -> list[tuple[str, str]]: ...
