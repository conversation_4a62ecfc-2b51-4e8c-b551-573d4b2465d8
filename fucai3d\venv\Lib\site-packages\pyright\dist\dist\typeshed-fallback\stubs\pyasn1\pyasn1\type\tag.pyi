__all__ = [
    "tagClassUniversal",
    "tagClassApplication",
    "tagClassContext",
    "tagClassPrivate",
    "tagFormatSimple",
    "tagFormatConstructed",
    "tagCategoryImplicit",
    "tagCategoryExplicit",
    "tagCategoryUntagged",
    "Tag",
    "TagSet",
]
tagClassUniversal: int
tagClassApplication: int
tagClassContext: int
tagClassPrivate: int
tagFormatSimple: int
tagFormatConstructed: int
tagCategoryImplicit: int
tagCategoryExplicit: int
tagCategoryUntagged: int

class Tag:
    def __init__(self, tagClass, tagFormat, tagId) -> None: ...
    def __eq__(self, other): ...
    def __ne__(self, other): ...
    def __lt__(self, other): ...
    def __le__(self, other): ...
    def __gt__(self, other): ...
    def __ge__(self, other): ...
    def __hash__(self): ...
    def __getitem__(self, idx): ...
    def __iter__(self): ...
    def __and__(self, otherTag): ...
    def __or__(self, otherTag): ...
    @property
    def tagClass(self): ...
    @property
    def tagFormat(self): ...
    @property
    def tagId(self): ...

class TagSet:
    def __init__(self, baseTag=(), *superTags) -> None: ...
    def __add__(self, superTag): ...
    def __radd__(self, superTag): ...
    def __getitem__(self, i): ...
    def __eq__(self, other): ...
    def __ne__(self, other): ...
    def __lt__(self, other): ...
    def __le__(self, other): ...
    def __gt__(self, other): ...
    def __ge__(self, other): ...
    def __hash__(self): ...
    def __len__(self) -> int: ...
    @property
    def baseTag(self): ...
    @property
    def superTags(self): ...
    def tagExplicitly(self, superTag): ...
    def tagImplicitly(self, superTag): ...
    def isSuperTagSetOf(self, tagSet): ...
    def getBaseTag(self): ...
