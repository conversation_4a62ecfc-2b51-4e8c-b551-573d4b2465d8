from collections.abc import <PERSON><PERSON><PERSON>, Iterable

def eval(a: Iterable[<PERSON><PERSON><PERSON>], b: Iterable[<PERSON><PERSON><PERSON>]) -> int: ...
def distance(a: Iterable[<PERSON><PERSON><PERSON>], b: Iterable[<PERSON><PERSON><PERSON>]) -> int: ...
def eval_criterion(a: Iterable[<PERSON><PERSON><PERSON>], b: Iterable[<PERSON><PERSON><PERSON>], thr: int) -> bool: ...
def distance_le_than(a: Iterable[<PERSON><PERSON><PERSON>], b: Iterable[<PERSON><PERSON><PERSON>], thr: int) -> bool: ...

__all__ = ("eval", "distance", "eval_criterion", "distance_le_than")
