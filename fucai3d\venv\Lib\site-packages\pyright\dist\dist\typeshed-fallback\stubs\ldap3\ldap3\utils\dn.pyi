STATE_ANY: int
STATE_ESCAPE: int
STATE_ESCAPE_HEX: int

def to_dn(
    iterator, decompose: bool = False, remove_space: bool = False, space_around_equal: bool = False, separate_rdn: bool = False
): ...
def parse_dn(dn, escape: bool = False, strip: bool = False): ...
def safe_dn(dn, decompose: bool = False, reverse: bool = False): ...
def safe_rdn(dn, decompose: bool = False): ...
def escape_rdn(rdn: str) -> str: ...
