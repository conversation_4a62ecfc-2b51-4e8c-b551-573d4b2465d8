from dataclasses import dataclass

@dataclass
class VersionMatchingError(Exception):
    line_number: int
    line: str
    how_to_fix: str = ...
    description: str = ...

class VersionMatchRuleBase:
    description: str
    how_to_fix: str
    version: float
    line_number: int
    line: str
    def __init__(self, version: float, line_number: int, line: str) -> None: ...
    def validate(self) -> bool: ...
    def get_error(self) -> VersionMatchingError: ...

class ValidIVInEXTXKEY(VersionMatchRuleBase): ...
class ValidFloatingPointEXTINF(VersionMatchRuleBase): ...
class ValidEXTXBYTERANGEOrEXTXIFRAMESONLY(VersionMatchRuleBase): ...

available_rules: list[type[VersionMatchRuleBase]]
