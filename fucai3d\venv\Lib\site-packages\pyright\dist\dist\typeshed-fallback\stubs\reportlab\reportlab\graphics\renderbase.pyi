from typing import Final

__version__: Final[str]

def getStateDelta(shape): ...

class StateTracker:
    def __init__(self, defaults=None, defaultObj=None) -> None: ...
    def push(self, delta) -> None: ...
    def pop(self): ...
    def getState(self): ...
    def getCTM(self): ...
    def __getitem__(self, key): ...
    def __setitem__(self, key, value) -> None: ...

def testStateTracker() -> None: ...
def renderScaledDrawing(d): ...

class Renderer:
    def undefined(self, operation) -> None: ...
    def draw(self, drawing, canvas, x: int = 0, y: int = 0, showBoundary=...) -> None: ...
    def initState(self, x, y) -> None: ...
    def pop(self) -> None: ...
    def drawNode(self, node) -> None: ...
    def getStateValue(self, key): ...
    def fillDerivedValues(self, node) -> None: ...
    def drawNodeDispatcher(self, anode) -> None: ...
    def drawGroup(self, group) -> None: ...
    def drawWedge(self, wedge) -> None: ...
    def drawPath(self, path) -> None: ...
    def drawRect(self, rect) -> None: ...
    def drawLine(self, line) -> None: ...
    def drawCircle(self, circle) -> None: ...
    def drawPolyLine(self, p) -> None: ...
    def drawEllipse(self, ellipse) -> None: ...
    def drawPolygon(self, p) -> None: ...
    def drawString(self, stringObj) -> None: ...
    def applyStateChanges(self, delta, newState) -> None: ...
    def drawImage(self, *args, **kwds) -> None: ...
