from _typeshed import Incomplete

MAXPNAMELEN: int
MAXERRORLENGTH: int
MAX_JOYSTICKOEMVXDNAME: int
MM_MICROSOFT: int
MM_MIDI_MAPPER: int
MM_WAVE_MAPPER: int
MM_SNDBLST_MIDIOUT: int
MM_SNDBLST_MIDIIN: int
MM_SNDBLST_SYNTH: int
MM_SNDBLST_WAVEOUT: int
MM_SNDBLST_WAVEIN: int
MM_ADLIB: int
MM_MPU401_MIDIOUT: int
MM_MPU401_MIDIIN: int
MM_PC_JOYSTICK: int
TIME_MS: int
TIME_SAMPLES: int
TIME_BYTES: int
TIME_SMPTE: int
TIME_MIDI: int
TIME_TICKS: int
MM_JOY1MOVE: int
MM_JOY2MOVE: int
MM_JOY1ZMOVE: int
MM_JOY2ZMOVE: int
MM_JOY1BUTTONDOWN: int
MM_JOY2BUTTONDOWN: int
MM_JOY1BUTTONUP: int
MM_JOY2BUTTONUP: int
MM_MCINOTIFY: int
MM_WOM_OPEN: int
MM_WOM_CLOSE: int
MM_WOM_DONE: int
MM_WIM_OPEN: int
MM_WIM_CLOSE: int
MM_WIM_DATA: int
MM_MIM_OPEN: int
MM_MIM_CLOSE: int
MM_MIM_DATA: int
MM_MIM_LONGDATA: int
MM_MIM_ERROR: int
MM_MIM_LONGERROR: int
MM_MOM_OPEN: int
MM_MOM_CLOSE: int
MM_MOM_DONE: int
MM_STREAM_OPEN: int
MM_STREAM_CLOSE: int
MM_STREAM_DONE: int
MM_STREAM_ERROR: int
MM_MOM_POSITIONCB: int
MM_MIM_MOREDATA: int
MM_MIXM_LINE_CHANGE: int
MM_MIXM_CONTROL_CHANGE: int
MMSYSERR_BASE: int
WAVERR_BASE: int
MIDIERR_BASE: int
TIMERR_BASE: int
JOYERR_BASE: int
MCIERR_BASE: int
MIXERR_BASE: int
MCI_STRING_OFFSET: int
MCI_VD_OFFSET: int
MCI_CD_OFFSET: int
MCI_WAVE_OFFSET: int
MCI_SEQ_OFFSET: int
MMSYSERR_NOERROR: int
MMSYSERR_ERROR: Incomplete
MMSYSERR_BADDEVICEID: Incomplete
MMSYSERR_NOTENABLED: Incomplete
MMSYSERR_ALLOCATED: Incomplete
MMSYSERR_INVALHANDLE: Incomplete
MMSYSERR_NODRIVER: Incomplete
MMSYSERR_NOMEM: Incomplete
MMSYSERR_NOTSUPPORTED: Incomplete
MMSYSERR_BADERRNUM: Incomplete
MMSYSERR_INVALFLAG: Incomplete
MMSYSERR_INVALPARAM: Incomplete
MMSYSERR_HANDLEBUSY: Incomplete
MMSYSERR_INVALIDALIAS: Incomplete
MMSYSERR_BADDB: Incomplete
MMSYSERR_KEYNOTFOUND: Incomplete
MMSYSERR_READERROR: Incomplete
MMSYSERR_WRITEERROR: Incomplete
MMSYSERR_DELETEERROR: Incomplete
MMSYSERR_VALNOTFOUND: Incomplete
MMSYSERR_NODRIVERCB: Incomplete
MMSYSERR_LASTERROR: Incomplete
DRV_LOAD: int
DRV_ENABLE: int
DRV_OPEN: int
DRV_CLOSE: int
DRV_DISABLE: int
DRV_FREE: int
DRV_CONFIGURE: int
DRV_QUERYCONFIGURE: int
DRV_INSTALL: int
DRV_REMOVE: int
DRV_EXITSESSION: int
DRV_POWER: int
DRV_RESERVED: int
DRV_USER: int
DRVCNF_CANCEL: int
DRVCNF_OK: int
DRVCNF_RESTART: int
DRV_CANCEL: int
DRV_OK: int
DRV_RESTART: int
DRV_MCI_FIRST: int
DRV_MCI_LAST: Incomplete
CALLBACK_TYPEMASK: int
CALLBACK_NULL: int
CALLBACK_WINDOW: int
CALLBACK_TASK: int
CALLBACK_FUNCTION: int
CALLBACK_THREAD: int
CALLBACK_EVENT: int
SND_SYNC: int
SND_ASYNC: int
SND_NODEFAULT: int
SND_MEMORY: int
SND_LOOP: int
SND_NOSTOP: int
SND_NOWAIT: int
SND_ALIAS: int
SND_ALIAS_ID: int
SND_FILENAME: int
SND_RESOURCE: int
SND_PURGE: int
SND_APPLICATION: int
SND_ALIAS_START: int
WAVERR_BADFORMAT: Incomplete
WAVERR_STILLPLAYING: Incomplete
WAVERR_UNPREPARED: Incomplete
WAVERR_SYNC: Incomplete
WAVERR_LASTERROR: Incomplete
WOM_OPEN: int
WOM_CLOSE: int
WOM_DONE: int
WIM_OPEN: int
WIM_CLOSE: int
WIM_DATA: int
WAVE_MAPPER: int
WAVE_FORMAT_QUERY: int
WAVE_ALLOWSYNC: int
WAVE_MAPPED: int
WAVE_FORMAT_DIRECT: int
WAVE_FORMAT_DIRECT_QUERY: Incomplete
WHDR_DONE: int
WHDR_PREPARED: int
WHDR_BEGINLOOP: int
WHDR_ENDLOOP: int
WHDR_INQUEUE: int
WAVECAPS_PITCH: int
WAVECAPS_PLAYBACKRATE: int
WAVECAPS_VOLUME: int
WAVECAPS_LRVOLUME: int
WAVECAPS_SYNC: int
WAVECAPS_SAMPLEACCURATE: int
WAVECAPS_DIRECTSOUND: int
WAVE_INVALIDFORMAT: int
WAVE_FORMAT_1M08: int
WAVE_FORMAT_1S08: int
WAVE_FORMAT_1M16: int
WAVE_FORMAT_1S16: int
WAVE_FORMAT_2M08: int
WAVE_FORMAT_2S08: int
WAVE_FORMAT_2M16: int
WAVE_FORMAT_2S16: int
WAVE_FORMAT_4M08: int
WAVE_FORMAT_4S08: int
WAVE_FORMAT_4M16: int
WAVE_FORMAT_4S16: int
WAVE_FORMAT_PCM: int
WAVE_FORMAT_IEEE_FLOAT: int
MIDIERR_UNPREPARED: Incomplete
MIDIERR_STILLPLAYING: Incomplete
MIDIERR_NOMAP: Incomplete
MIDIERR_NOTREADY: Incomplete
MIDIERR_NODEVICE: Incomplete
MIDIERR_INVALIDSETUP: Incomplete
MIDIERR_BADOPENMODE: Incomplete
MIDIERR_DONT_CONTINUE: Incomplete
MIDIERR_LASTERROR: Incomplete
MIDIPATCHSIZE: int
MIM_OPEN: int
MIM_CLOSE: int
MIM_DATA: int
MIM_LONGDATA: int
MIM_ERROR: int
MIM_LONGERROR: int
MOM_OPEN: int
MOM_CLOSE: int
MOM_DONE: int
MIM_MOREDATA: int
MOM_POSITIONCB: int
MIDI_IO_STATUS: int
MIDI_CACHE_ALL: int
MIDI_CACHE_BESTFIT: int
MIDI_CACHE_QUERY: int
MIDI_UNCACHE: int
MOD_MIDIPORT: int
MOD_SYNTH: int
MOD_SQSYNTH: int
MOD_FMSYNTH: int
MOD_MAPPER: int
MIDICAPS_VOLUME: int
MIDICAPS_LRVOLUME: int
MIDICAPS_CACHE: int
MIDICAPS_STREAM: int
MHDR_DONE: int
MHDR_PREPARED: int
MHDR_INQUEUE: int
MHDR_ISSTRM: int
MEVT_F_SHORT: int
MEVT_F_LONG: int
MEVT_F_CALLBACK: int

def MEVT_EVENTTYPE(x): ...
def MEVT_EVENTPARM(x): ...

MIDISTRM_ERROR: int
MIDIPROP_SET: int
MIDIPROP_GET: int
MIDIPROP_TIMEDIV: int
MIDIPROP_TEMPO: int
AUXCAPS_CDAUDIO: int
AUXCAPS_AUXIN: int
AUXCAPS_VOLUME: int
AUXCAPS_LRVOLUME: int
MIXER_SHORT_NAME_CHARS: int
MIXER_LONG_NAME_CHARS: int
MIXERR_INVALLINE: Incomplete
MIXERR_INVALCONTROL: Incomplete
MIXERR_INVALVALUE: Incomplete
MIXERR_LASTERROR: Incomplete
MIXER_OBJECTF_HANDLE: int
MIXER_OBJECTF_MIXER: int
MIXER_OBJECTF_HMIXER: Incomplete
MIXER_OBJECTF_WAVEOUT: int
MIXER_OBJECTF_HWAVEOUT: Incomplete
MIXER_OBJECTF_WAVEIN: int
MIXER_OBJECTF_HWAVEIN: Incomplete
MIXER_OBJECTF_MIDIOUT: int
MIXER_OBJECTF_HMIDIOUT: Incomplete
MIXER_OBJECTF_MIDIIN: int
MIXER_OBJECTF_HMIDIIN: Incomplete
MIXER_OBJECTF_AUX: int
MIXERLINE_LINEF_ACTIVE: int
MIXERLINE_LINEF_DISCONNECTED: int
MIXERLINE_LINEF_SOURCE: int
MIXERLINE_COMPONENTTYPE_DST_FIRST: int
MIXERLINE_COMPONENTTYPE_DST_UNDEFINED: Incomplete
MIXERLINE_COMPONENTTYPE_DST_DIGITAL: Incomplete
MIXERLINE_COMPONENTTYPE_DST_LINE: Incomplete
MIXERLINE_COMPONENTTYPE_DST_MONITOR: Incomplete
MIXERLINE_COMPONENTTYPE_DST_SPEAKERS: Incomplete
MIXERLINE_COMPONENTTYPE_DST_HEADPHONES: Incomplete
MIXERLINE_COMPONENTTYPE_DST_TELEPHONE: Incomplete
MIXERLINE_COMPONENTTYPE_DST_WAVEIN: Incomplete
MIXERLINE_COMPONENTTYPE_DST_VOICEIN: Incomplete
MIXERLINE_COMPONENTTYPE_DST_LAST: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_FIRST: int
MIXERLINE_COMPONENTTYPE_SRC_UNDEFINED: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_DIGITAL: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_LINE: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_MICROPHONE: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_SYNTHESIZER: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_COMPACTDISC: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_TELEPHONE: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_PCSPEAKER: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_WAVEOUT: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_AUXILIARY: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_ANALOG: Incomplete
MIXERLINE_COMPONENTTYPE_SRC_LAST: Incomplete
MIXERLINE_TARGETTYPE_UNDEFINED: int
MIXERLINE_TARGETTYPE_WAVEOUT: int
MIXERLINE_TARGETTYPE_WAVEIN: int
MIXERLINE_TARGETTYPE_MIDIOUT: int
MIXERLINE_TARGETTYPE_MIDIIN: int
MIXERLINE_TARGETTYPE_AUX: int
MIXER_GETLINEINFOF_DESTINATION: int
MIXER_GETLINEINFOF_SOURCE: int
MIXER_GETLINEINFOF_LINEID: int
MIXER_GETLINEINFOF_COMPONENTTYPE: int
MIXER_GETLINEINFOF_TARGETTYPE: int
MIXER_GETLINEINFOF_QUERYMASK: int
MIXERCONTROL_CONTROLF_UNIFORM: int
MIXERCONTROL_CONTROLF_MULTIPLE: int
MIXERCONTROL_CONTROLF_DISABLED: int
MIXERCONTROL_CT_CLASS_MASK: int
MIXERCONTROL_CT_CLASS_CUSTOM: int
MIXERCONTROL_CT_CLASS_METER: int
MIXERCONTROL_CT_CLASS_SWITCH: int
MIXERCONTROL_CT_CLASS_NUMBER: int
MIXERCONTROL_CT_CLASS_SLIDER: int
MIXERCONTROL_CT_CLASS_FADER: int
MIXERCONTROL_CT_CLASS_TIME: int
MIXERCONTROL_CT_CLASS_LIST: int
MIXERCONTROL_CT_SUBCLASS_MASK: int
MIXERCONTROL_CT_SC_SWITCH_BOOLEAN: int
MIXERCONTROL_CT_SC_SWITCH_BUTTON: int
MIXERCONTROL_CT_SC_METER_POLLED: int
MIXERCONTROL_CT_SC_TIME_MICROSECS: int
MIXERCONTROL_CT_SC_TIME_MILLISECS: int
MIXERCONTROL_CT_SC_LIST_SINGLE: int
MIXERCONTROL_CT_SC_LIST_MULTIPLE: int
MIXERCONTROL_CT_UNITS_MASK: int
MIXERCONTROL_CT_UNITS_CUSTOM: int
MIXERCONTROL_CT_UNITS_BOOLEAN: int
MIXERCONTROL_CT_UNITS_SIGNED: int
MIXERCONTROL_CT_UNITS_UNSIGNED: int
MIXERCONTROL_CT_UNITS_DECIBELS: int
MIXERCONTROL_CT_UNITS_PERCENT: int
MIXERCONTROL_CONTROLTYPE_CUSTOM: Incomplete
MIXERCONTROL_CONTROLTYPE_BOOLEANMETER: Incomplete
MIXERCONTROL_CONTROLTYPE_SIGNEDMETER: Incomplete
MIXERCONTROL_CONTROLTYPE_PEAKMETER: Incomplete
MIXERCONTROL_CONTROLTYPE_UNSIGNEDMETER: Incomplete
MIXERCONTROL_CONTROLTYPE_BOOLEAN: Incomplete
MIXERCONTROL_CONTROLTYPE_ONOFF: Incomplete
MIXERCONTROL_CONTROLTYPE_MUTE: Incomplete
MIXERCONTROL_CONTROLTYPE_MONO: Incomplete
MIXERCONTROL_CONTROLTYPE_LOUDNESS: Incomplete
MIXERCONTROL_CONTROLTYPE_STEREOENH: Incomplete
MIXERCONTROL_CONTROLTYPE_BUTTON: Incomplete
MIXERCONTROL_CONTROLTYPE_DECIBELS: Incomplete
MIXERCONTROL_CONTROLTYPE_SIGNED: Incomplete
MIXERCONTROL_CONTROLTYPE_UNSIGNED: Incomplete
MIXERCONTROL_CONTROLTYPE_PERCENT: Incomplete
MIXERCONTROL_CONTROLTYPE_SLIDER: Incomplete
MIXERCONTROL_CONTROLTYPE_PAN: Incomplete
MIXERCONTROL_CONTROLTYPE_QSOUNDPAN: Incomplete
MIXERCONTROL_CONTROLTYPE_FADER: Incomplete
MIXERCONTROL_CONTROLTYPE_VOLUME: Incomplete
MIXERCONTROL_CONTROLTYPE_BASS: Incomplete
MIXERCONTROL_CONTROLTYPE_TREBLE: Incomplete
MIXERCONTROL_CONTROLTYPE_EQUALIZER: Incomplete
MIXERCONTROL_CONTROLTYPE_SINGLESELECT: Incomplete
MIXERCONTROL_CONTROLTYPE_MUX: Incomplete
MIXERCONTROL_CONTROLTYPE_MULTIPLESELECT: Incomplete
MIXERCONTROL_CONTROLTYPE_MIXER: Incomplete
MIXERCONTROL_CONTROLTYPE_MICROTIME: Incomplete
MIXERCONTROL_CONTROLTYPE_MILLITIME: Incomplete
MIXER_GETLINECONTROLSF_ALL: int
MIXER_GETLINECONTROLSF_ONEBYID: int
MIXER_GETLINECONTROLSF_ONEBYTYPE: int
MIXER_GETLINECONTROLSF_QUERYMASK: int
MIXER_GETCONTROLDETAILSF_VALUE: int
MIXER_GETCONTROLDETAILSF_LISTTEXT: int
MIXER_GETCONTROLDETAILSF_QUERYMASK: int
MIXER_SETCONTROLDETAILSF_VALUE: int
MIXER_SETCONTROLDETAILSF_CUSTOM: int
MIXER_SETCONTROLDETAILSF_QUERYMASK: int
TIMERR_NOERROR: int
TIMERR_NOCANDO: Incomplete
TIMERR_STRUCT: Incomplete
TIME_ONESHOT: int
TIME_PERIODIC: int
TIME_CALLBACK_FUNCTION: int
TIME_CALLBACK_EVENT_SET: int
TIME_CALLBACK_EVENT_PULSE: int
JOYERR_NOERROR: int
JOYERR_PARMS: Incomplete
JOYERR_NOCANDO: Incomplete
JOYERR_UNPLUGGED: Incomplete
JOY_BUTTON1: int
JOY_BUTTON2: int
JOY_BUTTON3: int
JOY_BUTTON4: int
JOY_BUTTON1CHG: int
JOY_BUTTON2CHG: int
JOY_BUTTON3CHG: int
JOY_BUTTON4CHG: int
JOY_BUTTON5: int
JOY_BUTTON6: int
JOY_BUTTON7: int
JOY_BUTTON8: int
JOY_BUTTON9: int
JOY_BUTTON10: int
JOY_BUTTON11: int
JOY_BUTTON12: int
JOY_BUTTON13: int
JOY_BUTTON14: int
JOY_BUTTON15: int
JOY_BUTTON16: int
JOY_BUTTON17: int
JOY_BUTTON18: int
JOY_BUTTON19: int
JOY_BUTTON20: int
JOY_BUTTON21: int
JOY_BUTTON22: int
JOY_BUTTON23: int
JOY_BUTTON24: int
JOY_BUTTON25: int
JOY_BUTTON26: int
JOY_BUTTON27: int
JOY_BUTTON28: int
JOY_BUTTON29: int
JOY_BUTTON30: int
JOY_BUTTON31: int
JOY_BUTTON32: int
JOY_POVFORWARD: int
JOY_POVRIGHT: int
JOY_POVBACKWARD: int
JOY_POVLEFT: int
JOY_RETURNX: int
JOY_RETURNY: int
JOY_RETURNZ: int
JOY_RETURNR: int
JOY_RETURNU: int
JOY_RETURNV: int
JOY_RETURNPOV: int
JOY_RETURNBUTTONS: int
JOY_RETURNRAWDATA: int
JOY_RETURNPOVCTS: int
JOY_RETURNCENTERED: int
JOY_USEDEADZONE: int
JOY_RETURNALL: Incomplete
JOY_CAL_READALWAYS: int
JOY_CAL_READXYONLY: int
JOY_CAL_READ3: int
JOY_CAL_READ4: int
JOY_CAL_READXONLY: int
JOY_CAL_READYONLY: int
JOY_CAL_READ5: int
JOY_CAL_READ6: int
JOY_CAL_READZONLY: int
JOY_CAL_READRONLY: int
JOY_CAL_READUONLY: int
JOY_CAL_READVONLY: int
JOYSTICKID1: int
JOYSTICKID2: int
JOYCAPS_HASZ: int
JOYCAPS_HASR: int
JOYCAPS_HASU: int
JOYCAPS_HASV: int
JOYCAPS_HASPOV: int
JOYCAPS_POV4DIR: int
JOYCAPS_POVCTS: int
MMIOERR_BASE: int
MMIOERR_FILENOTFOUND: Incomplete
MMIOERR_OUTOFMEMORY: Incomplete
MMIOERR_CANNOTOPEN: Incomplete
MMIOERR_CANNOTCLOSE: Incomplete
MMIOERR_CANNOTREAD: Incomplete
MMIOERR_CANNOTWRITE: Incomplete
MMIOERR_CANNOTSEEK: Incomplete
MMIOERR_CANNOTEXPAND: Incomplete
MMIOERR_CHUNKNOTFOUND: Incomplete
MMIOERR_UNBUFFERED: Incomplete
MMIOERR_PATHNOTFOUND: Incomplete
MMIOERR_ACCESSDENIED: Incomplete
MMIOERR_SHARINGVIOLATION: Incomplete
MMIOERR_NETWORKERROR: Incomplete
MMIOERR_TOOMANYOPENFILES: Incomplete
MMIOERR_INVALIDFILE: Incomplete
CFSEPCHAR: Incomplete
MMIO_RWMODE: int
MMIO_SHAREMODE: int
MMIO_CREATE: int
MMIO_PARSE: int
MMIO_DELETE: int
MMIO_EXIST: int
MMIO_ALLOCBUF: int
MMIO_GETTEMP: int
MMIO_DIRTY: int
MMIO_READ: int
MMIO_WRITE: int
MMIO_READWRITE: int
MMIO_COMPAT: int
MMIO_EXCLUSIVE: int
MMIO_DENYWRITE: int
MMIO_DENYREAD: int
MMIO_DENYNONE: int
MMIO_FHOPEN: int
MMIO_EMPTYBUF: int
MMIO_TOUPPER: int
MMIO_INSTALLPROC: int
MMIO_GLOBALPROC: int
MMIO_REMOVEPROC: int
MMIO_UNICODEPROC: int
MMIO_FINDPROC: int
MMIO_FINDCHUNK: int
MMIO_FINDRIFF: int
MMIO_FINDLIST: int
MMIO_CREATERIFF: int
MMIO_CREATELIST: int
MMIOM_READ: int
MMIOM_WRITE: int
MMIOM_SEEK: int
MMIOM_OPEN: int
MMIOM_CLOSE: int
MMIOM_WRITEFLUSH: int
MMIOM_RENAME: int
MMIOM_USER: int
SEEK_SET: int
SEEK_CUR: int
SEEK_END: int
MMIO_DEFAULTBUFFER: int
MCIERR_INVALID_DEVICE_ID: Incomplete
MCIERR_UNRECOGNIZED_KEYWORD: Incomplete
MCIERR_UNRECOGNIZED_COMMAND: Incomplete
MCIERR_HARDWARE: Incomplete
MCIERR_INVALID_DEVICE_NAME: Incomplete
MCIERR_OUT_OF_MEMORY: Incomplete
MCIERR_DEVICE_OPEN: Incomplete
MCIERR_CANNOT_LOAD_DRIVER: Incomplete
MCIERR_MISSING_COMMAND_STRING: Incomplete
MCIERR_PARAM_OVERFLOW: Incomplete
MCIERR_MISSING_STRING_ARGUMENT: Incomplete
MCIERR_BAD_INTEGER: Incomplete
MCIERR_PARSER_INTERNAL: Incomplete
MCIERR_DRIVER_INTERNAL: Incomplete
MCIERR_MISSING_PARAMETER: Incomplete
MCIERR_UNSUPPORTED_FUNCTION: Incomplete
MCIERR_FILE_NOT_FOUND: Incomplete
MCIERR_DEVICE_NOT_READY: Incomplete
MCIERR_INTERNAL: Incomplete
MCIERR_DRIVER: Incomplete
MCIERR_CANNOT_USE_ALL: Incomplete
MCIERR_MULTIPLE: Incomplete
MCIERR_EXTENSION_NOT_FOUND: Incomplete
MCIERR_OUTOFRANGE: Incomplete
MCIERR_FLAGS_NOT_COMPATIBLE: Incomplete
MCIERR_FILE_NOT_SAVED: Incomplete
MCIERR_DEVICE_TYPE_REQUIRED: Incomplete
MCIERR_DEVICE_LOCKED: Incomplete
MCIERR_DUPLICATE_ALIAS: Incomplete
MCIERR_BAD_CONSTANT: Incomplete
MCIERR_MUST_USE_SHAREABLE: Incomplete
MCIERR_MISSING_DEVICE_NAME: Incomplete
MCIERR_BAD_TIME_FORMAT: Incomplete
MCIERR_NO_CLOSING_QUOTE: Incomplete
MCIERR_DUPLICATE_FLAGS: Incomplete
MCIERR_INVALID_FILE: Incomplete
MCIERR_NULL_PARAMETER_BLOCK: Incomplete
MCIERR_UNNAMED_RESOURCE: Incomplete
MCIERR_NEW_REQUIRES_ALIAS: Incomplete
MCIERR_NOTIFY_ON_AUTO_OPEN: Incomplete
MCIERR_NO_ELEMENT_ALLOWED: Incomplete
MCIERR_NONAPPLICABLE_FUNCTION: Incomplete
MCIERR_ILLEGAL_FOR_AUTO_OPEN: Incomplete
MCIERR_FILENAME_REQUIRED: Incomplete
MCIERR_EXTRA_CHARACTERS: Incomplete
MCIERR_DEVICE_NOT_INSTALLED: Incomplete
MCIERR_GET_CD: Incomplete
MCIERR_SET_CD: Incomplete
MCIERR_SET_DRIVE: Incomplete
MCIERR_DEVICE_LENGTH: Incomplete
MCIERR_DEVICE_ORD_LENGTH: Incomplete
MCIERR_NO_INTEGER: Incomplete
MCIERR_WAVE_OUTPUTSINUSE: Incomplete
MCIERR_WAVE_SETOUTPUTINUSE: Incomplete
MCIERR_WAVE_INPUTSINUSE: Incomplete
MCIERR_WAVE_SETINPUTINUSE: Incomplete
MCIERR_WAVE_OUTPUTUNSPECIFIED: Incomplete
MCIERR_WAVE_INPUTUNSPECIFIED: Incomplete
MCIERR_WAVE_OUTPUTSUNSUITABLE: Incomplete
MCIERR_WAVE_SETOUTPUTUNSUITABLE: Incomplete
MCIERR_WAVE_INPUTSUNSUITABLE: Incomplete
MCIERR_WAVE_SETINPUTUNSUITABLE: Incomplete
MCIERR_SEQ_DIV_INCOMPATIBLE: Incomplete
MCIERR_SEQ_PORT_INUSE: Incomplete
MCIERR_SEQ_PORT_NONEXISTENT: Incomplete
MCIERR_SEQ_PORT_MAPNODEVICE: Incomplete
MCIERR_SEQ_PORT_MISCERROR: Incomplete
MCIERR_SEQ_TIMER: Incomplete
MCIERR_SEQ_PORTUNSPECIFIED: Incomplete
MCIERR_SEQ_NOMIDIPRESENT: Incomplete
MCIERR_NO_WINDOW: Incomplete
MCIERR_CREATEWINDOW: Incomplete
MCIERR_FILE_READ: Incomplete
MCIERR_FILE_WRITE: Incomplete
MCIERR_NO_IDENTITY: Incomplete
MCIERR_CUSTOM_DRIVER_BASE: Incomplete
MCI_FIRST: int
MCI_OPEN: int
MCI_CLOSE: int
MCI_ESCAPE: int
MCI_PLAY: int
MCI_SEEK: int
MCI_STOP: int
MCI_PAUSE: int
MCI_INFO: int
MCI_GETDEVCAPS: int
MCI_SPIN: int
MCI_SET: int
MCI_STEP: int
MCI_RECORD: int
MCI_SYSINFO: int
MCI_BREAK: int
MCI_SAVE: int
MCI_STATUS: int
MCI_CUE: int
MCI_REALIZE: int
MCI_WINDOW: int
MCI_PUT: int
MCI_WHERE: int
MCI_FREEZE: int
MCI_UNFREEZE: int
MCI_LOAD: int
MCI_CUT: int
MCI_COPY: int
MCI_PASTE: int
MCI_UPDATE: int
MCI_RESUME: int
MCI_DELETE: int
MCI_USER_MESSAGES: Incomplete
MCI_LAST: int
MCI_DEVTYPE_VCR: int
MCI_DEVTYPE_VIDEODISC: int
MCI_DEVTYPE_OVERLAY: int
MCI_DEVTYPE_CD_AUDIO: int
MCI_DEVTYPE_DAT: int
MCI_DEVTYPE_SCANNER: int
MCI_DEVTYPE_ANIMATION: int
MCI_DEVTYPE_DIGITAL_VIDEO: int
MCI_DEVTYPE_OTHER: int
MCI_DEVTYPE_WAVEFORM_AUDIO: int
MCI_DEVTYPE_SEQUENCER: int
MCI_DEVTYPE_FIRST: int
MCI_DEVTYPE_LAST: int
MCI_DEVTYPE_FIRST_USER: int
MCI_MODE_NOT_READY: Incomplete
MCI_MODE_STOP: Incomplete
MCI_MODE_PLAY: Incomplete
MCI_MODE_RECORD: Incomplete
MCI_MODE_SEEK: Incomplete
MCI_MODE_PAUSE: Incomplete
MCI_MODE_OPEN: Incomplete
MCI_FORMAT_MILLISECONDS: int
MCI_FORMAT_HMS: int
MCI_FORMAT_MSF: int
MCI_FORMAT_FRAMES: int
MCI_FORMAT_SMPTE_24: int
MCI_FORMAT_SMPTE_25: int
MCI_FORMAT_SMPTE_30: int
MCI_FORMAT_SMPTE_30DROP: int
MCI_FORMAT_BYTES: int
MCI_FORMAT_SAMPLES: int
MCI_FORMAT_TMSF: int

def MCI_MSF_MINUTE(msf): ...
def MCI_MSF_SECOND(msf): ...
def MCI_MSF_FRAME(msf): ...
def MCI_TMSF_TRACK(tmsf): ...
def MCI_TMSF_MINUTE(tmsf): ...
def MCI_TMSF_SECOND(tmsf): ...
def MCI_TMSF_FRAME(tmsf): ...
def MCI_HMS_HOUR(hms): ...
def MCI_HMS_MINUTE(hms): ...
def MCI_HMS_SECOND(hms): ...

MCI_NOTIFY_SUCCESSFUL: int
MCI_NOTIFY_SUPERSEDED: int
MCI_NOTIFY_ABORTED: int
MCI_NOTIFY_FAILURE: int
MCI_NOTIFY: int
MCI_WAIT: int
MCI_FROM: int
MCI_TO: int
MCI_TRACK: int
MCI_OPEN_SHAREABLE: int
MCI_OPEN_ELEMENT: int
MCI_OPEN_ALIAS: int
MCI_OPEN_ELEMENT_ID: int
MCI_OPEN_TYPE_ID: int
MCI_OPEN_TYPE: int
MCI_SEEK_TO_START: int
MCI_SEEK_TO_END: int
MCI_STATUS_ITEM: int
MCI_STATUS_START: int
MCI_STATUS_LENGTH: int
MCI_STATUS_POSITION: int
MCI_STATUS_NUMBER_OF_TRACKS: int
MCI_STATUS_MODE: int
MCI_STATUS_MEDIA_PRESENT: int
MCI_STATUS_TIME_FORMAT: int
MCI_STATUS_READY: int
MCI_STATUS_CURRENT_TRACK: int
MCI_INFO_PRODUCT: int
MCI_INFO_FILE: int
MCI_INFO_MEDIA_UPC: int
MCI_INFO_MEDIA_IDENTITY: int
MCI_INFO_NAME: int
MCI_INFO_COPYRIGHT: int
MCI_GETDEVCAPS_ITEM: int
MCI_GETDEVCAPS_CAN_RECORD: int
MCI_GETDEVCAPS_HAS_AUDIO: int
MCI_GETDEVCAPS_HAS_VIDEO: int
MCI_GETDEVCAPS_DEVICE_TYPE: int
MCI_GETDEVCAPS_USES_FILES: int
MCI_GETDEVCAPS_COMPOUND_DEVICE: int
MCI_GETDEVCAPS_CAN_EJECT: int
MCI_GETDEVCAPS_CAN_PLAY: int
MCI_GETDEVCAPS_CAN_SAVE: int
MCI_SYSINFO_QUANTITY: int
MCI_SYSINFO_OPEN: int
MCI_SYSINFO_NAME: int
MCI_SYSINFO_INSTALLNAME: int
MCI_SET_DOOR_OPEN: int
MCI_SET_DOOR_CLOSED: int
MCI_SET_TIME_FORMAT: int
MCI_SET_AUDIO: int
MCI_SET_VIDEO: int
MCI_SET_ON: int
MCI_SET_OFF: int
MCI_SET_AUDIO_ALL: int
MCI_SET_AUDIO_LEFT: int
MCI_SET_AUDIO_RIGHT: int
MCI_BREAK_KEY: int
MCI_BREAK_HWND: int
MCI_BREAK_OFF: int
MCI_RECORD_INSERT: int
MCI_RECORD_OVERWRITE: int
MCI_SAVE_FILE: int
MCI_LOAD_FILE: int
MCI_VD_MODE_PARK: Incomplete
MCI_VD_MEDIA_CLV: Incomplete
MCI_VD_MEDIA_CAV: Incomplete
MCI_VD_MEDIA_OTHER: Incomplete
MCI_VD_FORMAT_TRACK: int
MCI_VD_PLAY_REVERSE: int
MCI_VD_PLAY_FAST: int
MCI_VD_PLAY_SPEED: int
MCI_VD_PLAY_SCAN: int
MCI_VD_PLAY_SLOW: int
MCI_VD_SEEK_REVERSE: int
MCI_VD_STATUS_SPEED: int
MCI_VD_STATUS_FORWARD: int
MCI_VD_STATUS_MEDIA_TYPE: int
MCI_VD_STATUS_SIDE: int
MCI_VD_STATUS_DISC_SIZE: int
MCI_VD_GETDEVCAPS_CLV: int
MCI_VD_GETDEVCAPS_CAV: int
MCI_VD_SPIN_UP: int
MCI_VD_SPIN_DOWN: int
MCI_VD_GETDEVCAPS_CAN_REVERSE: int
MCI_VD_GETDEVCAPS_FAST_RATE: int
MCI_VD_GETDEVCAPS_SLOW_RATE: int
MCI_VD_GETDEVCAPS_NORMAL_RATE: int
MCI_VD_STEP_FRAMES: int
MCI_VD_STEP_REVERSE: int
MCI_VD_ESCAPE_STRING: int
MCI_CDA_STATUS_TYPE_TRACK: int
MCI_CDA_TRACK_AUDIO: Incomplete
MCI_CDA_TRACK_OTHER: Incomplete
MCI_WAVE_PCM: Incomplete
MCI_WAVE_MAPPER: Incomplete
MCI_WAVE_OPEN_BUFFER: int
MCI_WAVE_SET_FORMATTAG: int
MCI_WAVE_SET_CHANNELS: int
MCI_WAVE_SET_SAMPLESPERSEC: int
MCI_WAVE_SET_AVGBYTESPERSEC: int
MCI_WAVE_SET_BLOCKALIGN: int
MCI_WAVE_SET_BITSPERSAMPLE: int
MCI_WAVE_INPUT: int
MCI_WAVE_OUTPUT: int
MCI_WAVE_STATUS_FORMATTAG: int
MCI_WAVE_STATUS_CHANNELS: int
MCI_WAVE_STATUS_SAMPLESPERSEC: int
MCI_WAVE_STATUS_AVGBYTESPERSEC: int
MCI_WAVE_STATUS_BLOCKALIGN: int
MCI_WAVE_STATUS_BITSPERSAMPLE: int
MCI_WAVE_STATUS_LEVEL: int
MCI_WAVE_SET_ANYINPUT: int
MCI_WAVE_SET_ANYOUTPUT: int
MCI_WAVE_GETDEVCAPS_INPUTS: int
MCI_WAVE_GETDEVCAPS_OUTPUTS: int
MCI_SEQ_DIV_PPQN: Incomplete
MCI_SEQ_DIV_SMPTE_24: Incomplete
MCI_SEQ_DIV_SMPTE_25: Incomplete
MCI_SEQ_DIV_SMPTE_30DROP: Incomplete
MCI_SEQ_DIV_SMPTE_30: Incomplete
MCI_SEQ_FORMAT_SONGPTR: int
MCI_SEQ_FILE: int
MCI_SEQ_MIDI: int
MCI_SEQ_SMPTE: int
MCI_SEQ_NONE: int
MCI_SEQ_MAPPER: int
MCI_SEQ_STATUS_TEMPO: int
MCI_SEQ_STATUS_PORT: int
MCI_SEQ_STATUS_SLAVE: int
MCI_SEQ_STATUS_MASTER: int
MCI_SEQ_STATUS_OFFSET: int
MCI_SEQ_STATUS_DIVTYPE: int
MCI_SEQ_STATUS_NAME: int
MCI_SEQ_STATUS_COPYRIGHT: int
MCI_SEQ_SET_TEMPO: int
MCI_SEQ_SET_PORT: int
MCI_SEQ_SET_SLAVE: int
MCI_SEQ_SET_MASTER: int
MCI_SEQ_SET_OFFSET: int
MCI_ANIM_OPEN_WS: int
MCI_ANIM_OPEN_PARENT: int
MCI_ANIM_OPEN_NOSTATIC: int
MCI_ANIM_PLAY_SPEED: int
MCI_ANIM_PLAY_REVERSE: int
MCI_ANIM_PLAY_FAST: int
MCI_ANIM_PLAY_SLOW: int
MCI_ANIM_PLAY_SCAN: int
MCI_ANIM_STEP_REVERSE: int
MCI_ANIM_STEP_FRAMES: int
MCI_ANIM_STATUS_SPEED: int
MCI_ANIM_STATUS_FORWARD: int
MCI_ANIM_STATUS_HWND: int
MCI_ANIM_STATUS_HPAL: int
MCI_ANIM_STATUS_STRETCH: int
MCI_ANIM_INFO_TEXT: int
MCI_ANIM_GETDEVCAPS_CAN_REVERSE: int
MCI_ANIM_GETDEVCAPS_FAST_RATE: int
MCI_ANIM_GETDEVCAPS_SLOW_RATE: int
MCI_ANIM_GETDEVCAPS_NORMAL_RATE: int
MCI_ANIM_GETDEVCAPS_PALETTES: int
MCI_ANIM_GETDEVCAPS_CAN_STRETCH: int
MCI_ANIM_GETDEVCAPS_MAX_WINDOWS: int
MCI_ANIM_REALIZE_NORM: int
MCI_ANIM_REALIZE_BKGD: int
MCI_ANIM_WINDOW_HWND: int
MCI_ANIM_WINDOW_STATE: int
MCI_ANIM_WINDOW_TEXT: int
MCI_ANIM_WINDOW_ENABLE_STRETCH: int
MCI_ANIM_WINDOW_DISABLE_STRETCH: int
MCI_ANIM_WINDOW_DEFAULT: int
MCI_ANIM_RECT: int
MCI_ANIM_PUT_SOURCE: int
MCI_ANIM_PUT_DESTINATION: int
MCI_ANIM_WHERE_SOURCE: int
MCI_ANIM_WHERE_DESTINATION: int
MCI_ANIM_UPDATE_HDC: int
MCI_OVLY_OPEN_WS: int
MCI_OVLY_OPEN_PARENT: int
MCI_OVLY_STATUS_HWND: int
MCI_OVLY_STATUS_STRETCH: int
MCI_OVLY_INFO_TEXT: int
MCI_OVLY_GETDEVCAPS_CAN_STRETCH: int
MCI_OVLY_GETDEVCAPS_CAN_FREEZE: int
MCI_OVLY_GETDEVCAPS_MAX_WINDOWS: int
MCI_OVLY_WINDOW_HWND: int
MCI_OVLY_WINDOW_STATE: int
MCI_OVLY_WINDOW_TEXT: int
MCI_OVLY_WINDOW_ENABLE_STRETCH: int
MCI_OVLY_WINDOW_DISABLE_STRETCH: int
MCI_OVLY_WINDOW_DEFAULT: int
MCI_OVLY_RECT: int
MCI_OVLY_PUT_SOURCE: int
MCI_OVLY_PUT_DESTINATION: int
MCI_OVLY_PUT_FRAME: int
MCI_OVLY_PUT_VIDEO: int
MCI_OVLY_WHERE_SOURCE: int
MCI_OVLY_WHERE_DESTINATION: int
MCI_OVLY_WHERE_FRAME: int
MCI_OVLY_WHERE_VIDEO: int
SELECTDIB: int

def DIBINDEX(n): ...
