from _typeshed import Incomplete

from authlib.oauth2.rfc8414 import AuthorizationServerMetadata

class OpenIDProviderMetadata(AuthorizationServerMetadata):
    REGISTRY_KEYS: Incomplete
    def validate_jwks_uri(self): ...
    def validate_acr_values_supported(self) -> None: ...
    def validate_subject_types_supported(self) -> None: ...
    def validate_id_token_signing_alg_values_supported(self) -> None: ...
    def validate_id_token_encryption_alg_values_supported(self) -> None: ...
    def validate_id_token_encryption_enc_values_supported(self) -> None: ...
    def validate_userinfo_signing_alg_values_supported(self) -> None: ...
    def validate_userinfo_encryption_alg_values_supported(self) -> None: ...
    def validate_userinfo_encryption_enc_values_supported(self) -> None: ...
    def validate_request_object_signing_alg_values_supported(self) -> None: ...
    def validate_request_object_encryption_alg_values_supported(self) -> None: ...
    def validate_request_object_encryption_enc_values_supported(self) -> None: ...
    def validate_display_values_supported(self) -> None: ...
    def validate_claim_types_supported(self) -> None: ...
    def validate_claims_supported(self) -> None: ...
    def validate_claims_locales_supported(self) -> None: ...
    def validate_claims_parameter_supported(self) -> None: ...
    def validate_request_parameter_supported(self) -> None: ...
    def validate_request_uri_parameter_supported(self) -> None: ...
    def validate_require_request_uri_registration(self) -> None: ...
    @property
    def claim_types_supported(self): ...
    @property
    def claims_parameter_supported(self): ...
    @property
    def request_parameter_supported(self): ...
    @property
    def request_uri_parameter_supported(self): ...
    @property
    def require_request_uri_registration(self): ...
