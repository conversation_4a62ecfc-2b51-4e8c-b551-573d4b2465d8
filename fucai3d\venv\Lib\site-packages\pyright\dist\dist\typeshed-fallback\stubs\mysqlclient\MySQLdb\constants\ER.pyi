ERROR_FIRST: int
NO: int
YES: int
CANT_CREATE_FILE: int
CANT_CREATE_TABLE: int
CANT_CREATE_DB: int
DB_CREATE_EXISTS: int
DB_DROP_EXISTS: int
DB_DROP_RMDIR: int
CANT_FIND_SYSTEM_REC: int
CANT_GET_STAT: int
CANT_LOCK: int
CANT_OPEN_FILE: int
FILE_NOT_FOUND: int
CANT_READ_DIR: int
CHECKREAD: int
DUP_KEY: int
ERROR_ON_READ: int
ERROR_ON_RENAME: int
ERROR_ON_WRITE: int
FILE_USED: int
FILSORT_ABORT: int
GET_ERRNO: int
ILLEGAL_HA: int
KEY_NOT_FOUND: int
NOT_FORM_FILE: int
NOT_KEYFILE: int
OLD_KEYFILE: int
OPEN_AS_READONLY: int
OUTOFMEMORY: int
OUT_OF_SORTMEMORY: int
CON_COUNT_ERROR: int
OUT_OF_RESOURCES: int
BAD_HOST_ERROR: int
HANDSHAKE_ERROR: int
DBACCESS_DENIED_ERROR: int
ACCESS_DENIED_ERROR: int
NO_DB_ERROR: int
UNKNOWN_COM_ERROR: int
BAD_NULL_ERROR: int
BAD_DB_ERROR: int
TABLE_EXISTS_ERROR: int
BAD_TABLE_ERROR: int
NON_UNIQ_ERROR: int
SERVER_SHUTDOWN: int
BAD_FIELD_ERROR: int
WRONG_FIELD_WITH_GROUP: int
WRONG_GROUP_FIELD: int
WRONG_SUM_SELECT: int
WRONG_VALUE_COUNT: int
TOO_LONG_IDENT: int
DUP_FIELDNAME: int
DUP_KEYNAME: int
DUP_ENTRY: int
WRONG_FIELD_SPEC: int
PARSE_ERROR: int
EMPTY_QUERY: int
NONUNIQ_TABLE: int
INVALID_DEFAULT: int
MULTIPLE_PRI_KEY: int
TOO_MANY_KEYS: int
TOO_MANY_KEY_PARTS: int
TOO_LONG_KEY: int
KEY_COLUMN_DOES_NOT_EXITS: int
BLOB_USED_AS_KEY: int
TOO_BIG_FIELDLENGTH: int
WRONG_AUTO_KEY: int
READY: int
SHUTDOWN_COMPLETE: int
FORCING_CLOSE: int
IPSOCK_ERROR: int
NO_SUCH_INDEX: int
WRONG_FIELD_TERMINATORS: int
BLOBS_AND_NO_TERMINATED: int
TEXTFILE_NOT_READABLE: int
FILE_EXISTS_ERROR: int
LOAD_INFO: int
ALTER_INFO: int
WRONG_SUB_KEY: int
CANT_REMOVE_ALL_FIELDS: int
CANT_DROP_FIELD_OR_KEY: int
INSERT_INFO: int
UPDATE_TABLE_USED: int
NO_SUCH_THREAD: int
KILL_DENIED_ERROR: int
NO_TABLES_USED: int
TOO_BIG_SET: int
NO_UNIQUE_LOGFILE: int
TABLE_NOT_LOCKED_FOR_WRITE: int
TABLE_NOT_LOCKED: int
BLOB_CANT_HAVE_DEFAULT: int
WRONG_DB_NAME: int
WRONG_TABLE_NAME: int
TOO_BIG_SELECT: int
UNKNOWN_ERROR: int
UNKNOWN_PROCEDURE: int
WRONG_PARAMCOUNT_TO_PROCEDURE: int
WRONG_PARAMETERS_TO_PROCEDURE: int
UNKNOWN_TABLE: int
FIELD_SPECIFIED_TWICE: int
INVALID_GROUP_FUNC_USE: int
UNSUPPORTED_EXTENSION: int
TABLE_MUST_HAVE_COLUMNS: int
RECORD_FILE_FULL: int
UNKNOWN_CHARACTER_SET: int
TOO_MANY_TABLES: int
TOO_MANY_FIELDS: int
TOO_BIG_ROWSIZE: int
STACK_OVERRUN: int
WRONG_OUTER_JOIN_UNUSED: int
NULL_COLUMN_IN_INDEX: int
CANT_FIND_UDF: int
CANT_INITIALIZE_UDF: int
UDF_NO_PATHS: int
UDF_EXISTS: int
CANT_OPEN_LIBRARY: int
CANT_FIND_DL_ENTRY: int
FUNCTION_NOT_DEFINED: int
HOST_IS_BLOCKED: int
HOST_NOT_PRIVILEGED: int
PASSWORD_ANONYMOUS_USER: int
PASSWORD_NOT_ALLOWED: int
PASSWORD_NO_MATCH: int
UPDATE_INFO: int
CANT_CREATE_THREAD: int
WRONG_VALUE_COUNT_ON_ROW: int
CANT_REOPEN_TABLE: int
INVALID_USE_OF_NULL: int
REGEXP_ERROR: int
MIX_OF_GROUP_FUNC_AND_FIELDS: int
NONEXISTING_GRANT: int
TABLEACCESS_DENIED_ERROR: int
COLUMNACCESS_DENIED_ERROR: int
ILLEGAL_GRANT_FOR_TABLE: int
GRANT_WRONG_HOST_OR_USER: int
NO_SUCH_TABLE: int
NONEXISTING_TABLE_GRANT: int
NOT_ALLOWED_COMMAND: int
SYNTAX_ERROR: int
ABORTING_CONNECTION: int
NET_PACKET_TOO_LARGE: int
NET_READ_ERROR_FROM_PIPE: int
NET_FCNTL_ERROR: int
NET_PACKETS_OUT_OF_ORDER: int
NET_UNCOMPRESS_ERROR: int
NET_READ_ERROR: int
NET_READ_INTERRUPTED: int
NET_ERROR_ON_WRITE: int
NET_WRITE_INTERRUPTED: int
TOO_LONG_STRING: int
TABLE_CANT_HANDLE_BLOB: int
TABLE_CANT_HANDLE_AUTO_INCREMENT: int
WRONG_COLUMN_NAME: int
WRONG_KEY_COLUMN: int
WRONG_MRG_TABLE: int
DUP_UNIQUE: int
BLOB_KEY_WITHOUT_LENGTH: int
PRIMARY_CANT_HAVE_NULL: int
TOO_MANY_ROWS: int
REQUIRES_PRIMARY_KEY: int
UPDATE_WITHOUT_KEY_IN_SAFE_MODE: int
KEY_DOES_NOT_EXITS: int
CHECK_NO_SUCH_TABLE: int
CHECK_NOT_IMPLEMENTED: int
CANT_DO_THIS_DURING_AN_TRANSACTION: int
ERROR_DURING_COMMIT: int
ERROR_DURING_ROLLBACK: int
ERROR_DURING_FLUSH_LOGS: int
NEW_ABORTING_CONNECTION: int
MASTER: int
MASTER_NET_READ: int
MASTER_NET_WRITE: int
FT_MATCHING_KEY_NOT_FOUND: int
LOCK_OR_ACTIVE_TRANSACTION: int
UNKNOWN_SYSTEM_VARIABLE: int
CRASHED_ON_USAGE: int
CRASHED_ON_REPAIR: int
WARNING_NOT_COMPLETE_ROLLBACK: int
TRANS_CACHE_FULL: int
SLAVE_NOT_RUNNING: int
BAD_SLAVE: int
MASTER_INFO: int
SLAVE_THREAD: int
TOO_MANY_USER_CONNECTIONS: int
SET_CONSTANTS_ONLY: int
LOCK_WAIT_TIMEOUT: int
LOCK_TABLE_FULL: int
READ_ONLY_TRANSACTION: int
WRONG_ARGUMENTS: int
NO_PERMISSION_TO_CREATE_USER: int
LOCK_DEADLOCK: int
TABLE_CANT_HANDLE_FT: int
CANNOT_ADD_FOREIGN: int
NO_REFERENCED_ROW: int
ROW_IS_REFERENCED: int
CONNECT_TO_MASTER: int
ERROR_WHEN_EXECUTING_COMMAND: int
WRONG_USAGE: int
WRONG_NUMBER_OF_COLUMNS_IN_SELECT: int
CANT_UPDATE_WITH_READLOCK: int
MIXING_NOT_ALLOWED: int
DUP_ARGUMENT: int
USER_LIMIT_REACHED: int
SPECIFIC_ACCESS_DENIED_ERROR: int
LOCAL_VARIABLE: int
GLOBAL_VARIABLE: int
NO_DEFAULT: int
WRONG_VALUE_FOR_VAR: int
WRONG_TYPE_FOR_VAR: int
VAR_CANT_BE_READ: int
CANT_USE_OPTION_HERE: int
NOT_SUPPORTED_YET: int
MASTER_FATAL_ERROR_READING_BINLOG: int
SLAVE_IGNORED_TABLE: int
INCORRECT_GLOBAL_LOCAL_VAR: int
WRONG_FK_DEF: int
KEY_REF_DO_NOT_MATCH_TABLE_REF: int
OPERAND_COLUMNS: int
SUBQUERY_NO_1_ROW: int
UNKNOWN_STMT_HANDLER: int
CORRUPT_HELP_DB: int
AUTO_CONVERT: int
ILLEGAL_REFERENCE: int
DERIVED_MUST_HAVE_ALIAS: int
SELECT_REDUCED: int
TABLENAME_NOT_ALLOWED_HERE: int
NOT_SUPPORTED_AUTH_MODE: int
SPATIAL_CANT_HAVE_NULL: int
COLLATION_CHARSET_MISMATCH: int
TOO_BIG_FOR_UNCOMPRESS: int
ZLIB_Z_MEM_ERROR: int
ZLIB_Z_BUF_ERROR: int
ZLIB_Z_DATA_ERROR: int
CUT_VALUE_GROUP_CONCAT: int
WARN_TOO_FEW_RECORDS: int
WARN_TOO_MANY_RECORDS: int
WARN_NULL_TO_NOTNULL: int
WARN_DATA_OUT_OF_RANGE: int
WARN_DATA_TRUNCATED: int
WARN_USING_OTHER_HANDLER: int
CANT_AGGREGATE_2COLLATIONS: int
REVOKE_GRANTS: int
CANT_AGGREGATE_3COLLATIONS: int
CANT_AGGREGATE_NCOLLATIONS: int
VARIABLE_IS_NOT_STRUCT: int
UNKNOWN_COLLATION: int
SLAVE_IGNORED_SSL_PARAMS: int
SERVER_IS_IN_SECURE_AUTH_MODE: int
WARN_FIELD_RESOLVED: int
BAD_SLAVE_UNTIL_COND: int
MISSING_SKIP_SLAVE: int
UNTIL_COND_IGNORED: int
WRONG_NAME_FOR_INDEX: int
WRONG_NAME_FOR_CATALOG: int
BAD_FT_COLUMN: int
UNKNOWN_KEY_CACHE: int
WARN_HOSTNAME_WONT_WORK: int
UNKNOWN_STORAGE_ENGINE: int
WARN_DEPRECATED_SYNTAX: int
NON_UPDATABLE_TABLE: int
FEATURE_DISABLED: int
OPTION_PREVENTS_STATEMENT: int
DUPLICATED_VALUE_IN_TYPE: int
TRUNCATED_WRONG_VALUE: int
INVALID_ON_UPDATE: int
UNSUPPORTED_PS: int
GET_ERRMSG: int
GET_TEMPORARY_ERRMSG: int
UNKNOWN_TIME_ZONE: int
WARN_INVALID_TIMESTAMP: int
INVALID_CHARACTER_STRING: int
WARN_ALLOWED_PACKET_OVERFLOWED: int
CONFLICTING_DECLARATIONS: int
SP_NO_RECURSIVE_CREATE: int
SP_ALREADY_EXISTS: int
SP_DOES_NOT_EXIST: int
SP_DROP_FAILED: int
SP_STORE_FAILED: int
SP_LILABEL_MISMATCH: int
SP_LABEL_REDEFINE: int
SP_LABEL_MISMATCH: int
SP_UNINIT_VAR: int
SP_BADSELECT: int
SP_BADRETURN: int
SP_BADSTATEMENT: int
UPDATE_LOG_DEPRECATED_IGNORED: int
UPDATE_LOG_DEPRECATED_TRANSLATED: int
QUERY_INTERRUPTED: int
SP_WRONG_NO_OF_ARGS: int
SP_COND_MISMATCH: int
SP_NORETURN: int
SP_NORETURNEND: int
SP_BAD_CURSOR_QUERY: int
SP_BAD_CURSOR_SELECT: int
SP_CURSOR_MISMATCH: int
SP_CURSOR_ALREADY_OPEN: int
SP_CURSOR_NOT_OPEN: int
SP_UNDECLARED_VAR: int
SP_WRONG_NO_OF_FETCH_ARGS: int
SP_FETCH_NO_DATA: int
SP_DUP_PARAM: int
SP_DUP_VAR: int
SP_DUP_COND: int
SP_DUP_CURS: int
SP_CANT_ALTER: int
SP_SUBSELECT_NYI: int
STMT_NOT_ALLOWED_IN_SF_OR_TRG: int
SP_VARCOND_AFTER_CURSHNDLR: int
SP_CURSOR_AFTER_HANDLER: int
SP_CASE_NOT_FOUND: int
FPARSER_TOO_BIG_FILE: int
FPARSER_BAD_HEADER: int
FPARSER_EOF_IN_COMMENT: int
FPARSER_ERROR_IN_PARAMETER: int
FPARSER_EOF_IN_UNKNOWN_PARAMETER: int
VIEW_NO_EXPLAIN: int
WRONG_OBJECT: int
NONUPDATEABLE_COLUMN: int
VIEW_SELECT_CLAUSE: int
VIEW_SELECT_VARIABLE: int
VIEW_SELECT_TMPTABLE: int
VIEW_WRONG_LIST: int
WARN_VIEW_MERGE: int
WARN_VIEW_WITHOUT_KEY: int
VIEW_INVALID: int
SP_NO_DROP_SP: int
TRG_ALREADY_EXISTS: int
TRG_DOES_NOT_EXIST: int
TRG_ON_VIEW_OR_TEMP_TABLE: int
TRG_CANT_CHANGE_ROW: int
TRG_NO_SUCH_ROW_IN_TRG: int
NO_DEFAULT_FOR_FIELD: int
DIVISION_BY_ZERO: int
TRUNCATED_WRONG_VALUE_FOR_FIELD: int
ILLEGAL_VALUE_FOR_TYPE: int
VIEW_NONUPD_CHECK: int
VIEW_CHECK_FAILED: int
PROCACCESS_DENIED_ERROR: int
RELAY_LOG_FAIL: int
UNKNOWN_TARGET_BINLOG: int
IO_ERR_LOG_INDEX_READ: int
BINLOG_PURGE_PROHIBITED: int
FSEEK_FAIL: int
BINLOG_PURGE_FATAL_ERR: int
LOG_IN_USE: int
LOG_PURGE_UNKNOWN_ERR: int
RELAY_LOG_INIT: int
NO_BINARY_LOGGING: int
RESERVED_SYNTAX: int
PS_MANY_PARAM: int
KEY_PART_0: int
VIEW_CHECKSUM: int
VIEW_MULTIUPDATE: int
VIEW_NO_INSERT_FIELD_LIST: int
VIEW_DELETE_MERGE_VIEW: int
CANNOT_USER: int
XAER_NOTA: int
XAER_INVAL: int
XAER_RMFAIL: int
XAER_OUTSIDE: int
XAER_RMERR: int
XA_RBROLLBACK: int
NONEXISTING_PROC_GRANT: int
PROC_AUTO_GRANT_FAIL: int
PROC_AUTO_REVOKE_FAIL: int
DATA_TOO_LONG: int
SP_BAD_SQLSTATE: int
STARTUP: int
LOAD_FROM_FIXED_SIZE_ROWS_TO_VAR: int
CANT_CREATE_USER_WITH_GRANT: int
WRONG_VALUE_FOR_TYPE: int
TABLE_DEF_CHANGED: int
SP_DUP_HANDLER: int
SP_NOT_VAR_ARG: int
SP_NO_RETSET: int
CANT_CREATE_GEOMETRY_OBJECT: int
BINLOG_UNSAFE_ROUTINE: int
BINLOG_CREATE_ROUTINE_NEED_SUPER: int
STMT_HAS_NO_OPEN_CURSOR: int
COMMIT_NOT_ALLOWED_IN_SF_OR_TRG: int
NO_DEFAULT_FOR_VIEW_FIELD: int
SP_NO_RECURSION: int
TOO_BIG_SCALE: int
TOO_BIG_PRECISION: int
M_BIGGER_THAN_D: int
WRONG_LOCK_OF_SYSTEM_TABLE: int
CONNECT_TO_FOREIGN_DATA_SOURCE: int
QUERY_ON_FOREIGN_DATA_SOURCE: int
FOREIGN_DATA_SOURCE_DOESNT_EXIST: int
FOREIGN_DATA_STRING_INVALID_CANT_CREATE: int
FOREIGN_DATA_STRING_INVALID: int
TRG_IN_WRONG_SCHEMA: int
STACK_OVERRUN_NEED_MORE: int
TOO_LONG_BODY: int
WARN_CANT_DROP_DEFAULT_KEYCACHE: int
TOO_BIG_DISPLAYWIDTH: int
XAER_DUPID: int
DATETIME_FUNCTION_OVERFLOW: int
CANT_UPDATE_USED_TABLE_IN_SF_OR_TRG: int
VIEW_PREVENT_UPDATE: int
PS_NO_RECURSION: int
SP_CANT_SET_AUTOCOMMIT: int
VIEW_FRM_NO_USER: int
VIEW_OTHER_USER: int
NO_SUCH_USER: int
FORBID_SCHEMA_CHANGE: int
ROW_IS_REFERENCED_2: int
NO_REFERENCED_ROW_2: int
SP_BAD_VAR_SHADOW: int
TRG_NO_DEFINER: int
OLD_FILE_FORMAT: int
SP_RECURSION_LIMIT: int
SP_WRONG_NAME: int
TABLE_NEEDS_UPGRADE: int
SP_NO_AGGREGATE: int
MAX_PREPARED_STMT_COUNT_REACHED: int
VIEW_RECURSIVE: int
NON_GROUPING_FIELD_USED: int
TABLE_CANT_HANDLE_SPKEYS: int
NO_TRIGGERS_ON_SYSTEM_SCHEMA: int
REMOVED_SPACES: int
AUTOINC_READ_FAILED: int
USERNAME: int
HOSTNAME: int
WRONG_STRING_LENGTH: int
NON_INSERTABLE_TABLE: int
ADMIN_WRONG_MRG_TABLE: int
TOO_HIGH_LEVEL_OF_NESTING_FOR_SELECT: int
NAME_BECOMES_EMPTY: int
AMBIGUOUS_FIELD_TERM: int
FOREIGN_SERVER_EXISTS: int
FOREIGN_SERVER_DOESNT_EXIST: int
ILLEGAL_HA_CREATE_OPTION: int
PARTITION_REQUIRES_VALUES_ERROR: int
PARTITION_WRONG_VALUES_ERROR: int
PARTITION_MAXVALUE_ERROR: int
PARTITION_WRONG_NO_PART_ERROR: int
PARTITION_WRONG_NO_SUBPART_ERROR: int
WRONG_EXPR_IN_PARTITION_FUNC_ERROR: int
FIELD_NOT_FOUND_PART_ERROR: int
INCONSISTENT_PARTITION_INFO_ERROR: int
PARTITION_FUNC_NOT_ALLOWED_ERROR: int
PARTITIONS_MUST_BE_DEFINED_ERROR: int
RANGE_NOT_INCREASING_ERROR: int
INCONSISTENT_TYPE_OF_FUNCTIONS_ERROR: int
MULTIPLE_DEF_CONST_IN_LIST_PART_ERROR: int
PARTITION_ENTRY_ERROR: int
MIX_HANDLER_ERROR: int
PARTITION_NOT_DEFINED_ERROR: int
TOO_MANY_PARTITIONS_ERROR: int
SUBPARTITION_ERROR: int
CANT_CREATE_HANDLER_FILE: int
BLOB_FIELD_IN_PART_FUNC_ERROR: int
UNIQUE_KEY_NEED_ALL_FIELDS_IN_PF: int
NO_PARTS_ERROR: int
PARTITION_MGMT_ON_NONPARTITIONED: int
FOREIGN_KEY_ON_PARTITIONED: int
DROP_PARTITION_NON_EXISTENT: int
DROP_LAST_PARTITION: int
COALESCE_ONLY_ON_HASH_PARTITION: int
REORG_HASH_ONLY_ON_SAME_NO: int
REORG_NO_PARAM_ERROR: int
ONLY_ON_RANGE_LIST_PARTITION: int
ADD_PARTITION_SUBPART_ERROR: int
ADD_PARTITION_NO_NEW_PARTITION: int
COALESCE_PARTITION_NO_PARTITION: int
REORG_PARTITION_NOT_EXIST: int
SAME_NAME_PARTITION: int
NO_BINLOG_ERROR: int
CONSECUTIVE_REORG_PARTITIONS: int
REORG_OUTSIDE_RANGE: int
PARTITION_FUNCTION_FAILURE: int
LIMITED_PART_RANGE: int
PLUGIN_IS_NOT_LOADED: int
WRONG_VALUE: int
NO_PARTITION_FOR_GIVEN_VALUE: int
FILEGROUP_OPTION_ONLY_ONCE: int
CREATE_FILEGROUP_FAILED: int
DROP_FILEGROUP_FAILED: int
TABLESPACE_AUTO_EXTEND_ERROR: int
WRONG_SIZE_NUMBER: int
SIZE_OVERFLOW_ERROR: int
ALTER_FILEGROUP_FAILED: int
BINLOG_ROW_LOGGING_FAILED: int
EVENT_ALREADY_EXISTS: int
EVENT_DOES_NOT_EXIST: int
EVENT_INTERVAL_NOT_POSITIVE_OR_TOO_BIG: int
EVENT_ENDS_BEFORE_STARTS: int
EVENT_EXEC_TIME_IN_THE_PAST: int
EVENT_SAME_NAME: int
DROP_INDEX_FK: int
WARN_DEPRECATED_SYNTAX_WITH_VER: int
CANT_LOCK_LOG_TABLE: int
FOREIGN_DUPLICATE_KEY_OLD_UNUSED: int
COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE: int
TEMP_TABLE_PREVENTS_SWITCH_OUT_OF_RBR: int
STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_FORMAT: int
PARTITION_NO_TEMPORARY: int
PARTITION_CONST_DOMAIN_ERROR: int
PARTITION_FUNCTION_IS_NOT_ALLOWED: int
NULL_IN_VALUES_LESS_THAN: int
WRONG_PARTITION_NAME: int
CANT_CHANGE_TX_CHARACTERISTICS: int
DUP_ENTRY_AUTOINCREMENT_CASE: int
EVENT_SET_VAR_ERROR: int
PARTITION_MERGE_ERROR: int
BASE64_DECODE_ERROR: int
EVENT_RECURSION_FORBIDDEN: int
ONLY_INTEGERS_ALLOWED: int
UNSUPORTED_LOG_ENGINE: int
BAD_LOG_STATEMENT: int
CANT_RENAME_LOG_TABLE: int
WRONG_PARAMCOUNT_TO_NATIVE_FCT: int
WRONG_PARAMETERS_TO_NATIVE_FCT: int
WRONG_PARAMETERS_TO_STORED_FCT: int
NATIVE_FCT_NAME_COLLISION: int
DUP_ENTRY_WITH_KEY_NAME: int
BINLOG_PURGE_EMFILE: int
EVENT_CANNOT_CREATE_IN_THE_PAST: int
EVENT_CANNOT_ALTER_IN_THE_PAST: int
NO_PARTITION_FOR_GIVEN_VALUE_SILENT: int
BINLOG_UNSAFE_STATEMENT: int
BINLOG_FATAL_ERROR: int
BINLOG_LOGGING_IMPOSSIBLE: int
VIEW_NO_CREATION_CTX: int
VIEW_INVALID_CREATION_CTX: int
TRG_CORRUPTED_FILE: int
TRG_NO_CREATION_CTX: int
TRG_INVALID_CREATION_CTX: int
EVENT_INVALID_CREATION_CTX: int
TRG_CANT_OPEN_TABLE: int
NO_FORMAT_DESCRIPTION_EVENT_BEFORE_BINLOG_STATEMENT: int
SLAVE_CORRUPT_EVENT: int
LOG_PURGE_NO_FILE: int
XA_RBTIMEOUT: int
XA_RBDEADLOCK: int
NEED_REPREPARE: int
WARN_NO_MASTER_INFO: int
WARN_OPTION_IGNORED: int
PLUGIN_DELETE_BUILTIN: int
WARN_PLUGIN_BUSY: int
VARIABLE_IS_READONLY: int
WARN_ENGINE_TRANSACTION_ROLLBACK: int
SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE: int
NDB_REPLICATION_SCHEMA_ERROR: int
CONFLICT_FN_PARSE_ERROR: int
EXCEPTIONS_WRITE_ERROR: int
TOO_LONG_TABLE_COMMENT: int
TOO_LONG_FIELD_COMMENT: int
FUNC_INEXISTENT_NAME_COLLISION: int
DATABASE_NAME: int
TABLE_NAME: int
PARTITION_NAME: int
SUBPARTITION_NAME: int
TEMPORARY_NAME: int
RENAMED_NAME: int
TOO_MANY_CONCURRENT_TRXS: int
WARN_NON_ASCII_SEPARATOR_NOT_IMPLEMENTED: int
DEBUG_SYNC_TIMEOUT: int
DEBUG_SYNC_HIT_LIMIT: int
DUP_SIGNAL_SET: int
SIGNAL_WARN: int
SIGNAL_NOT_FOUND: int
SIGNAL_EXCEPTION: int
RESIGNAL_WITHOUT_ACTIVE_HANDLER: int
SIGNAL_BAD_CONDITION_TYPE: int
WARN_COND_ITEM_TRUNCATED: int
COND_ITEM_TOO_LONG: int
UNKNOWN_LOCALE: int
SLAVE_IGNORE_SERVER_IDS: int
SAME_NAME_PARTITION_FIELD: int
PARTITION_COLUMN_LIST_ERROR: int
WRONG_TYPE_COLUMN_VALUE_ERROR: int
TOO_MANY_PARTITION_FUNC_FIELDS_ERROR: int
MAXVALUE_IN_VALUES_IN: int
TOO_MANY_VALUES_ERROR: int
ROW_SINGLE_PARTITION_FIELD_ERROR: int
FIELD_TYPE_NOT_ALLOWED_AS_PARTITION_FIELD: int
PARTITION_FIELDS_TOO_LONG: int
BINLOG_ROW_ENGINE_AND_STMT_ENGINE: int
BINLOG_ROW_MODE_AND_STMT_ENGINE: int
BINLOG_UNSAFE_AND_STMT_ENGINE: int
BINLOG_ROW_INJECTION_AND_STMT_ENGINE: int
BINLOG_STMT_MODE_AND_ROW_ENGINE: int
BINLOG_ROW_INJECTION_AND_STMT_MODE: int
BINLOG_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE: int
BINLOG_UNSAFE_LIMIT: int
BINLOG_UNSAFE_SYSTEM_TABLE: int
BINLOG_UNSAFE_AUTOINC_COLUMNS: int
BINLOG_UNSAFE_UDF: int
BINLOG_UNSAFE_SYSTEM_VARIABLE: int
BINLOG_UNSAFE_SYSTEM_FUNCTION: int
BINLOG_UNSAFE_NONTRANS_AFTER_TRANS: int
MESSAGE_AND_STATEMENT: int
SLAVE_CANT_CREATE_CONVERSION: int
INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_FORMAT: int
PATH_LENGTH: int
WARN_DEPRECATED_SYNTAX_NO_REPLACEMENT: int
WRONG_NATIVE_TABLE_STRUCTURE: int
WRONG_PERFSCHEMA_USAGE: int
WARN_I_S_SKIPPED_TABLE: int
INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_DIRECT: int
STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_DIRECT: int
SPATIAL_MUST_HAVE_GEOM_COL: int
TOO_LONG_INDEX_COMMENT: int
LOCK_ABORTED: int
DATA_OUT_OF_RANGE: int
WRONG_SPVAR_TYPE_IN_LIMIT: int
BINLOG_UNSAFE_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE: int
BINLOG_UNSAFE_MIXED_STATEMENT: int
INSIDE_TRANSACTION_PREVENTS_SWITCH_SQL_LOG_BIN: int
STORED_FUNCTION_PREVENTS_SWITCH_SQL_LOG_BIN: int
FAILED_READ_FROM_PAR_FILE: int
VALUES_IS_NOT_INT_TYPE_ERROR: int
ACCESS_DENIED_NO_PASSWORD_ERROR: int
SET_PASSWORD_AUTH_PLUGIN: int
TRUNCATE_ILLEGAL_FK: int
PLUGIN_IS_PERMANENT: int
SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE_MIN: int
SLAVE_HEARTBEAT_VALUE_OUT_OF_RANGE_MAX: int
STMT_CACHE_FULL: int
MULTI_UPDATE_KEY_CONFLICT: int
TABLE_NEEDS_REBUILD: int
WARN_OPTION_BELOW_LIMIT: int
INDEX_COLUMN_TOO_LONG: int
ERROR_IN_TRIGGER_BODY: int
ERROR_IN_UNKNOWN_TRIGGER_BODY: int
INDEX_CORRUPT: int
UNDO_RECORD_TOO_BIG: int
BINLOG_UNSAFE_INSERT_IGNORE_SELECT: int
BINLOG_UNSAFE_INSERT_SELECT_UPDATE: int
BINLOG_UNSAFE_REPLACE_SELECT: int
BINLOG_UNSAFE_CREATE_IGNORE_SELECT: int
BINLOG_UNSAFE_CREATE_REPLACE_SELECT: int
BINLOG_UNSAFE_UPDATE_IGNORE: int
PLUGIN_NO_UNINSTALL: int
PLUGIN_NO_INSTALL: int
BINLOG_UNSAFE_WRITE_AUTOINC_SELECT: int
BINLOG_UNSAFE_CREATE_SELECT_AUTOINC: int
BINLOG_UNSAFE_INSERT_TWO_KEYS: int
TABLE_IN_FK_CHECK: int
UNSUPPORTED_ENGINE: int
BINLOG_UNSAFE_AUTOINC_NOT_FIRST: int
CANNOT_LOAD_FROM_TABLE_V2: int
MASTER_DELAY_VALUE_OUT_OF_RANGE: int
ONLY_FD_AND_RBR_EVENTS_ALLOWED_IN_BINLOG_STATEMENT: int
PARTITION_EXCHANGE_DIFFERENT_OPTION: int
PARTITION_EXCHANGE_PART_TABLE: int
PARTITION_EXCHANGE_TEMP_TABLE: int
PARTITION_INSTEAD_OF_SUBPARTITION: int
UNKNOWN_PARTITION: int
TABLES_DIFFERENT_METADATA: int
ROW_DOES_NOT_MATCH_PARTITION: int
BINLOG_CACHE_SIZE_GREATER_THAN_MAX: int
WARN_INDEX_NOT_APPLICABLE: int
PARTITION_EXCHANGE_FOREIGN_KEY: int
RPL_INFO_DATA_TOO_LONG: int
BINLOG_STMT_CACHE_SIZE_GREATER_THAN_MAX: int
CANT_UPDATE_TABLE_IN_CREATE_TABLE_SELECT: int
PARTITION_CLAUSE_ON_NONPARTITIONED: int
ROW_DOES_NOT_MATCH_GIVEN_PARTITION_SET: int
CHANGE_RPL_INFO_REPOSITORY_FAILURE: int
WARNING_NOT_COMPLETE_ROLLBACK_WITH_CREATED_TEMP_TABLE: int
WARNING_NOT_COMPLETE_ROLLBACK_WITH_DROPPED_TEMP_TABLE: int
MTS_FEATURE_IS_NOT_SUPPORTED: int
MTS_UPDATED_DBS_GREATER_MAX: int
MTS_CANT_PARALLEL: int
MTS_INCONSISTENT_DATA: int
FULLTEXT_NOT_SUPPORTED_WITH_PARTITIONING: int
DA_INVALID_CONDITION_NUMBER: int
INSECURE_PLAIN_TEXT: int
INSECURE_CHANGE_MASTER: int
FOREIGN_DUPLICATE_KEY_WITH_CHILD_INFO: int
FOREIGN_DUPLICATE_KEY_WITHOUT_CHILD_INFO: int
SQLTHREAD_WITH_SECURE_SLAVE: int
TABLE_HAS_NO_FT: int
VARIABLE_NOT_SETTABLE_IN_SF_OR_TRIGGER: int
VARIABLE_NOT_SETTABLE_IN_TRANSACTION: int
SET_STATEMENT_CANNOT_INVOKE_FUNCTION: int
GTID_NEXT_CANT_BE_AUTOMATIC_IF_GTID_NEXT_LIST_IS_NON_NULL: int
MALFORMED_GTID_SET_SPECIFICATION: int
MALFORMED_GTID_SET_ENCODING: int
MALFORMED_GTID_SPECIFICATION: int
GNO_EXHAUSTED: int
BAD_SLAVE_AUTO_POSITION: int
AUTO_POSITION_REQUIRES_GTID_MODE_NOT_OFF: int
CANT_DO_IMPLICIT_COMMIT_IN_TRX_WHEN_GTID_NEXT_IS_SET: int
GTID_MODE_ON_REQUIRES_ENFORCE_GTID_CONSISTENCY_ON: int
CANT_SET_GTID_NEXT_TO_GTID_WHEN_GTID_MODE_IS_OFF: int
CANT_SET_GTID_NEXT_TO_ANONYMOUS_WHEN_GTID_MODE_IS_ON: int
CANT_SET_GTID_NEXT_LIST_TO_NON_NULL_WHEN_GTID_MODE_IS_OFF: int
GTID_UNSAFE_NON_TRANSACTIONAL_TABLE: int
GTID_UNSAFE_CREATE_SELECT: int
GTID_UNSAFE_CREATE_DROP_TEMPORARY_TABLE_IN_TRANSACTION: int
GTID_MODE_CAN_ONLY_CHANGE_ONE_STEP_AT_A_TIME: int
MASTER_HAS_PURGED_REQUIRED_GTIDS: int
CANT_SET_GTID_NEXT_WHEN_OWNING_GTID: int
UNKNOWN_EXPLAIN_FORMAT: int
CANT_EXECUTE_IN_READ_ONLY_TRANSACTION: int
TOO_LONG_TABLE_PARTITION_COMMENT: int
SLAVE_CONFIGURATION: int
INNODB_FT_LIMIT: int
INNODB_NO_FT_TEMP_TABLE: int
INNODB_FT_WRONG_DOCID_COLUMN: int
INNODB_FT_WRONG_DOCID_INDEX: int
INNODB_ONLINE_LOG_TOO_BIG: int
UNKNOWN_ALTER_ALGORITHM: int
UNKNOWN_ALTER_LOCK: int
MTS_CHANGE_MASTER_CANT_RUN_WITH_GAPS: int
MTS_RECOVERY_FAILURE: int
MTS_RESET_WORKERS: int
COL_COUNT_DOESNT_MATCH_CORRUPTED_V2: int
SLAVE_SILENT_RETRY_TRANSACTION: int
DISCARD_FK_CHECKS_RUNNING: int
TABLE_SCHEMA_MISMATCH: int
TABLE_IN_SYSTEM_TABLESPACE: int
IO_READ_ERROR: int
IO_WRITE_ERROR: int
TABLESPACE_MISSING: int
TABLESPACE_EXISTS: int
TABLESPACE_DISCARDED: int
INTERNAL_ERROR: int
INNODB_IMPORT_ERROR: int
INNODB_INDEX_CORRUPT: int
INVALID_YEAR_COLUMN_LENGTH: int
NOT_VALID_PASSWORD: int
MUST_CHANGE_PASSWORD: int
FK_NO_INDEX_CHILD: int
FK_NO_INDEX_PARENT: int
FK_FAIL_ADD_SYSTEM: int
FK_CANNOT_OPEN_PARENT: int
FK_INCORRECT_OPTION: int
FK_DUP_NAME: int
PASSWORD_FORMAT: int
FK_COLUMN_CANNOT_DROP: int
FK_COLUMN_CANNOT_DROP_CHILD: int
FK_COLUMN_NOT_NULL: int
DUP_INDEX: int
FK_COLUMN_CANNOT_CHANGE: int
FK_COLUMN_CANNOT_CHANGE_CHILD: int
MALFORMED_PACKET: int
READ_ONLY_MODE: int
GTID_NEXT_TYPE_UNDEFINED_GTID: int
VARIABLE_NOT_SETTABLE_IN_SP: int
CANT_SET_GTID_PURGED_WHEN_GTID_EXECUTED_IS_NOT_EMPTY: int
CANT_SET_GTID_PURGED_WHEN_OWNED_GTIDS_IS_NOT_EMPTY: int
GTID_PURGED_WAS_CHANGED: int
GTID_EXECUTED_WAS_CHANGED: int
BINLOG_STMT_MODE_AND_NO_REPL_TABLES: int
ALTER_OPERATION_NOT_SUPPORTED: int
ALTER_OPERATION_NOT_SUPPORTED_REASON: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_COPY: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_PARTITION: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_RENAME: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_COLUMN_TYPE: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_CHECK: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_NOPK: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_AUTOINC: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_HIDDEN_FTS: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_CHANGE_FTS: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_FTS: int
SQL_SLAVE_SKIP_COUNTER_NOT_SETTABLE_IN_GTID_MODE: int
DUP_UNKNOWN_IN_INDEX: int
IDENT_CAUSES_TOO_LONG_PATH: int
ALTER_OPERATION_NOT_SUPPORTED_REASON_NOT_NULL: int
MUST_CHANGE_PASSWORD_LOGIN: int
ROW_IN_WRONG_PARTITION: int
MTS_EVENT_BIGGER_PENDING_JOBS_SIZE_MAX: int
BINLOG_LOGICAL_CORRUPTION: int
WARN_PURGE_LOG_IN_USE: int
WARN_PURGE_LOG_IS_ACTIVE: int
AUTO_INCREMENT_CONFLICT: int
WARN_ON_BLOCKHOLE_IN_RBR: int
SLAVE_MI_INIT_REPOSITORY: int
SLAVE_RLI_INIT_REPOSITORY: int
ACCESS_DENIED_CHANGE_USER_ERROR: int
INNODB_READ_ONLY: int
STOP_SLAVE_SQL_THREAD_TIMEOUT: int
STOP_SLAVE_IO_THREAD_TIMEOUT: int
TABLE_CORRUPT: int
TEMP_FILE_WRITE_FAILURE: int
INNODB_FT_AUX_NOT_HEX_ID: int
OLD_TEMPORALS_UPGRADED: int
INNODB_FORCED_RECOVERY: int
AES_INVALID_IV: int
PLUGIN_CANNOT_BE_UNINSTALLED: int
GTID_UNSAFE_BINLOG_SPLITTABLE_STATEMENT_AND_ASSIGNED_GTID: int
SLAVE_HAS_MORE_GTIDS_THAN_MASTER: int
MISSING_KEY: int
ERROR_LAST: int
