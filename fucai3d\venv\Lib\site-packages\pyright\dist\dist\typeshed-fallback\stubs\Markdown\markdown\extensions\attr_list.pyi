from re import Pattern
from xml.etree.ElementTree import Element

from markdown.extensions import Extension
from markdown.treeprocessors import Treeprocessor

def get_attrs_and_remainder(attrs_string: str) -> tuple[list[tuple[str, str]], str]: ...
def get_attrs(str: str) -> list[tuple[str, str]]: ...
def isheader(elem: Element) -> bool: ...

class AttrListTreeprocessor(Treeprocessor):
    BASE_RE: str
    HEADER_RE: Pattern[str]
    BLOCK_RE: Pattern[str]
    INLINE_RE: Pattern[str]
    NAME_RE: Pattern[str]
    def run(self, doc: Element) -> None: ...
    def assign_attrs(self, elem: Element, attrs_string: str, *, strict: bool = False) -> str: ...
    def sanitize_name(self, name: str) -> str: ...

class AttrListExtension(Extension): ...

def makeExtension(**kwargs) -> AttrListExtension: ...
