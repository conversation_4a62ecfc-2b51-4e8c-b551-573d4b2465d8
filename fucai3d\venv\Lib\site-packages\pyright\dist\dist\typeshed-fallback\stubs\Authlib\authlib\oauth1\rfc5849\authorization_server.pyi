from _typeshed import Incomplete

from authlib.oauth1.rfc5849.base_server import BaseServer

class AuthorizationServer(BaseServer):
    TOKEN_RESPONSE_HEADER: Incomplete
    TEMPORARY_CREDENTIALS_METHOD: str
    def create_oauth1_request(self, request) -> None: ...
    def handle_response(self, status_code, payload, headers) -> None: ...
    def handle_error_response(self, error): ...
    def validate_temporary_credentials_request(self, request): ...
    def create_temporary_credentials_response(self, request=None): ...
    def validate_authorization_request(self, request): ...
    def create_authorization_response(self, request, grant_user=None): ...
    def validate_token_request(self, request): ...
    def create_token_response(self, request): ...
    def create_temporary_credential(self, request) -> None: ...
    def get_temporary_credential(self, request) -> None: ...
    def delete_temporary_credential(self, request) -> None: ...
    def create_authorization_verifier(self, request) -> None: ...
    def create_token_credential(self, request) -> None: ...
