from _typeshed import Incomplete

from antlr4.atn.ATN import ATN as ATN
from antlr4.atn.ATNDeserializationOptions import ATNDeserializationOptions as ATNDeserializationOptions
from antlr4.atn.ATNState import *
from antlr4.atn.ATNType import ATNType as ATNType
from antlr4.atn.LexerAction import *
from antlr4.atn.Transition import *
from antlr4.Token import Token as Token

SERIALIZED_VERSION: int

class ATNDeserializer:
    deserializationOptions: Incomplete
    def __init__(self, options: ATNDeserializationOptions | None = None) -> None: ...
    data: Incomplete
    pos: int
    def deserialize(self, data: list[int]): ...
    def checkVersion(self) -> None: ...
    def readATN(self): ...
    def readStates(self, atn: ATN): ...
    def readRules(self, atn: ATN): ...
    def readModes(self, atn: ATN): ...
    def readSets(self, atn: ATN, sets: list[Incomplete]): ...
    def readEdges(self, atn: ATN, sets: list[Incomplete]): ...
    def readDecisions(self, atn: ATN): ...
    def readLexerActions(self, atn: ATN): ...
    def generateRuleBypassTransitions(self, atn: ATN): ...
    def generateRuleBypassTransition(self, atn: ATN, idx: int): ...
    def stateIsEndStateFor(self, state: ATNState, idx: int): ...
    def markPrecedenceDecisions(self, atn: ATN): ...
    def verifyATN(self, atn: ATN): ...
    def checkCondition(self, condition: bool, message=None): ...
    def readInt(self): ...
    edgeFactories: Incomplete
    def edgeFactory(self, atn: ATN, type: int, src: int, trg: int, arg1: int, arg2: int, arg3: int, sets: list[Incomplete]): ...
    stateFactories: Incomplete
    def stateFactory(self, type: int, ruleIndex: int): ...
    CHANNEL: int
    CUSTOM: int
    MODE: int
    MORE: int
    POP_MODE: int
    PUSH_MODE: int
    SKIP: int
    TYPE: int
    actionFactories: Incomplete
    def lexerActionFactory(self, type: int, data1: int, data2: int): ...
