from _typeshed import Incomplete
from typing import Final

__version__: Final[str]

class Logger:
    def __init__(self) -> None: ...
    def add(self, fp) -> None: ...
    def remove(self, fp) -> None: ...
    def write(self, text) -> None: ...
    def __call__(self, text) -> None: ...

logger: Incomplete

class WarnOnce:
    uttered: Incomplete
    pfx: Incomplete
    enabled: int
    def __init__(self, kind: str = "Warn") -> None: ...
    def once(self, warning) -> None: ...
    def __call__(self, warning) -> None: ...

warnOnce: Incomplete
infoOnce: Incomplete
