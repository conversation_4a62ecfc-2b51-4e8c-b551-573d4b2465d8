from collections.abc import Generator

from networkx.utils.backends import _dispatchable

__all__ = ["generate_adjlist", "write_adjlist", "parse_adjlist", "read_adjlist"]

def generate_adjlist(G, delimiter: str = " ") -> Generator[str, None, None]: ...
def write_adjlist(G, path, comments: str = "#", delimiter: str = " ", encoding: str = "utf-8") -> None: ...
@_dispatchable
def parse_adjlist(lines, comments: str = "#", delimiter=None, create_using=None, nodetype=None): ...
@_dispatchable
def read_adjlist(path, comments: str = "#", delimiter=None, create_using=None, nodetype=None, encoding: str = "utf-8"): ...
