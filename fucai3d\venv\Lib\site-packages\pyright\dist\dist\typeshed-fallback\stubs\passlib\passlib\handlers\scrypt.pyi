from typing import ClassVar
from typing_extensions import Self

import passlib.utils.handlers as uh

class scrypt(uh.ParallelismMixin, uh.<PERSON>Rounds, uh.<PERSON><PERSON>awSal<PERSON>, uh.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, uh.<PERSON><PERSON>anyI<PERSON><PERSON>, uh.GenericHandler):  # type: ignore[misc]
    backends: ClassVar[tuple[str, ...]]
    name: ClassVar[str]
    checksum_size: ClassVar[int]
    default_ident: ClassVar[str]
    ident_values: ClassVar[tuple[str, ...]]
    default_salt_size: ClassVar[int]
    max_salt_size: ClassVar[int]
    default_rounds: ClassVar[int]
    min_rounds: ClassVar[int]
    max_rounds: ClassVar[int]
    rounds_cost: ClassVar[str]
    parallelism: int
    block_size: int
    @classmethod
    def using(cls, block_size=None, **kwds): ...  # type: ignore[override]
    @classmethod
    def from_string(cls, hash) -> Self: ...  # type: ignore[override]
    @classmethod
    def parse(cls, hash): ...
    def to_string(self): ...
    def __init__(self, block_size=None, **kwds) -> None: ...
    @classmethod
    def get_backend(cls): ...
    @classmethod
    def has_backend(cls, name: str = "any"): ...
    @classmethod
    def set_backend(cls, name: str = "any", dryrun: bool = False) -> None: ...

__all__ = ["scrypt"]
