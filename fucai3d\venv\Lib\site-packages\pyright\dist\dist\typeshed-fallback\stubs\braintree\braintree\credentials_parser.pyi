from braintree.environment import Environment

class CredentialsParser:
    client_id: str | None
    client_secret: str | None
    access_token: str | None
    environment: Environment | None
    merchant_id: str
    def __init__(
        self, client_id: str | None = None, client_secret: str | None = None, access_token: str | None = None
    ) -> None: ...
    def parse_client_credentials(self) -> None: ...
    def parse_access_token(self) -> None: ...
    def get_environment(self, credential: str) -> Environment | None: ...
    def get_merchant_id(self, credential: str) -> str: ...
