import re
from _typeshed import Incomplete

from . import base

fullTree: bool
tag_regexp: re.Pattern[str]
comment_type: Incomplete

class DocumentType:
    name: Incomplete
    publicId: Incomplete
    systemId: Incomplete
    def __init__(self, name, publicId, systemId) -> None: ...

class Document:
    def __init__(self) -> None: ...
    def appendChild(self, element) -> None: ...
    @property
    def childNodes(self): ...

def testSerializer(element) -> str: ...
def tostring(element) -> str: ...

class TreeBuilder(base.TreeBuilder):
    documentClass: Incomplete
    doctypeClass: Incomplete
    elementClass: Incomplete
    commentClass: Incomplete
    fragmentClass: Incomplete
    implementation: Incomplete
    namespaceHTMLElements: Incomplete
    def __init__(self, namespaceHTMLElements, fullTree: bool = False): ...
    insertComment: Incomplete
    initial_comments: Incomplete
    doctype: Incomplete
    def reset(self) -> None: ...
    def testSerializer(self, element): ...
    def getDocument(self): ...
    def getFragment(self): ...
    def insertDoctype(self, token) -> None: ...
    def insertCommentInitial(self, data, parent=None) -> None: ...
    def insertCommentMain(self, data, parent=None) -> None: ...
    document: Incomplete
    def insertRoot(self, token) -> None: ...
