from _typeshed import Incomplete
from typing import TextIO

from antlr4.atn.ATNDeserializationOptions import ATNDeserializationOptions as ATNDeserializationOptions
from antlr4.atn.ATNDeserializer import ATNDeserial<PERSON> as ATNDeserializer
from antlr4.BufferedTokenStream import TokenStream as TokenStream
from antlr4.CommonTokenFactory import TokenFactory as TokenFactory
from antlr4.error.Errors import (
    RecognitionException as RecognitionException,
    UnsupportedOperationException as UnsupportedOperationException,
)
from antlr4.error.ErrorStrategy import DefaultErrorStrategy as DefaultErrorStrategy
from antlr4.InputStream import InputStream as InputStream
from antlr4.Lexer import Lexer as Lexer
from antlr4.ParserRuleContext import ParserRuleContext as ParserRuleContext
from antlr4.Recognizer import Recognizer as Recognizer
from antlr4.RuleContext import RuleContext as RuleContext
from antlr4.Token import Token as Token
from antlr4.tree.ParseTreePatternMatcher import ParseTreePatternMatcher as ParseTreePatternMatcher
from antlr4.tree.Tree import ErrorNode as ErrorNode, ParseTreeListener as ParseTreeListener, TerminalNode as TerminalNode

class TraceListener(ParseTreeListener):
    def __init__(self, parser) -> None: ...
    def enterEveryRule(self, ctx) -> None: ...
    def visitTerminal(self, node) -> None: ...
    def visitErrorNode(self, node) -> None: ...
    def exitEveryRule(self, ctx) -> None: ...

class Parser(Recognizer):
    bypassAltsAtnCache: Incomplete
    buildParseTrees: bool
    def __init__(self, input: TokenStream, output: TextIO = ...) -> None: ...
    def reset(self) -> None: ...
    def match(self, ttype: int): ...
    def matchWildcard(self): ...
    def getParseListeners(self): ...
    def addParseListener(self, listener: ParseTreeListener): ...
    def removeParseListener(self, listener: ParseTreeListener): ...
    def removeParseListeners(self) -> None: ...
    def triggerEnterRuleEvent(self) -> None: ...
    def triggerExitRuleEvent(self) -> None: ...
    def getNumberOfSyntaxErrors(self): ...
    def getTokenFactory(self): ...
    def setTokenFactory(self, factory: TokenFactory): ...
    def getATNWithBypassAlts(self): ...
    def compileParseTreePattern(self, pattern: str, patternRuleIndex: int, lexer: Lexer | None = None): ...
    def getInputStream(self): ...
    def setInputStream(self, input: InputStream): ...
    def getTokenStream(self): ...
    def setTokenStream(self, input: TokenStream): ...
    def getCurrentToken(self): ...
    def notifyErrorListeners(self, msg: str, offendingToken: Token | None = None, e: RecognitionException | None = None): ...
    def consume(self): ...
    def addContextToParseTree(self) -> None: ...
    state: Incomplete
    def enterRule(self, localctx: ParserRuleContext, state: int, ruleIndex: int): ...
    def exitRule(self) -> None: ...
    def enterOuterAlt(self, localctx: ParserRuleContext, altNum: int): ...
    def getPrecedence(self): ...
    def enterRecursionRule(self, localctx: ParserRuleContext, state: int, ruleIndex: int, precedence: int): ...
    def pushNewRecursionContext(self, localctx: ParserRuleContext, state: int, ruleIndex: int): ...
    def unrollRecursionContexts(self, parentCtx: ParserRuleContext): ...
    def getInvokingContext(self, ruleIndex: int): ...
    def precpred(self, localctx: RuleContext, precedence: int): ...
    def inContext(self, context: str): ...
    def isExpectedToken(self, symbol: int): ...
    def getExpectedTokens(self): ...
    def getExpectedTokensWithinCurrentRule(self): ...
    def getRuleIndex(self, ruleName: str): ...
    def getRuleInvocationStack(self, p: RuleContext | None = None): ...
    def getDFAStrings(self): ...
    def dumpDFA(self) -> None: ...
    def getSourceName(self): ...
    def setTrace(self, trace: bool): ...
