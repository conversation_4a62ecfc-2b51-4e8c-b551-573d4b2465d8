from _typeshed import ReadableBuffer
from collections.abc import Iterable
from typing import Any, SupportsBytes, SupportsIndex, overload

@overload
def to_bytes(x: None, charset: str = "utf-8", errors: str = "strict") -> None: ...
@overload
def to_bytes(
    x: str | bytes | float | Iterable[SupportsIndex] | SupportsIndex | SupportsBytes | ReadableBuffer,
    charset: str = "utf-8",
    errors: str = "strict",
) -> bytes: ...
@overload
def to_unicode(x: None, charset: str = "utf-8", errors: str = "strict") -> None: ...
@overload
def to_unicode(x: object, charset: str = "utf-8", errors: str = "strict") -> str: ...
def to_native(x: str | bytes, encoding: str = "ascii") -> str: ...
def json_loads(s: str | bytes | bytearray) -> Any: ...  # returns json.loads()
def json_dumps(data: Any, ensure_ascii: bool = False) -> str: ...  # data pass to json.dumps()
def urlsafe_b64decode(s: bytes) -> bytes: ...
def urlsafe_b64encode(s: ReadableBuffer) -> bytes: ...
def base64_to_int(s: str | bytes | float | Iterable[SupportsIndex] | SupportsIndex | SupportsBytes | ReadableBuffer) -> int: ...
def int_to_base64(num: int) -> str: ...
def json_b64encode(
    text: str | bytes | float | Iterable[SupportsIndex] | SupportsIndex | SupportsBytes | ReadableBuffer,
) -> bytes: ...
