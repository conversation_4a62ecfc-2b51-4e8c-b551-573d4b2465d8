from typing import Generic, Literal

from ._typing import GeoT
from .lib import Geometry

class PreparedGeometry(Generic[GeoT]):
    context: GeoT
    prepared: Literal[True]
    def __init__(self, context: GeoT | PreparedGeometry[GeoT]) -> None: ...
    def contains(self, other: Geometry | None) -> bool: ...
    def contains_properly(self, other: Geometry | None) -> bool: ...
    def covers(self, other: Geometry | None) -> bool: ...
    def crosses(self, other: Geometry | None) -> bool: ...
    def disjoint(self, other: Geometry | None) -> bool: ...
    def intersects(self, other: Geometry | None) -> bool: ...
    def overlaps(self, other: Geometry | None) -> bool: ...
    def touches(self, other: Geometry | None) -> bool: ...
    def within(self, other: Geometry | None) -> bool: ...

def prep(ob: GeoT | PreparedGeometry[GeoT]) -> PreparedGeometry[GeoT]: ...
