from _typeshed import Incomplete
from collections.abc import Callable
from logging import Logger

from oauthlib.common import Request
from oauthlib.oauth2.rfc6749.request_validator import RequestValidator as OAuth2RequestValidator

log: Logger

class RequestValidator(OAuth2RequestValidator):
    def get_authorization_code_scopes(self, client_id: str, code: str, redirect_uri: str, request) -> list[str]: ...
    def get_authorization_code_nonce(self, client_id: str, code: str, redirect_uri: str, request) -> str: ...
    def get_jwt_bearer_token(self, token: dict[str, Incomplete], token_handler, request: Request) -> str: ...
    def get_id_token(self, token: dict[str, Incomplete], token_handler, request: Request) -> str: ...
    def finalize_id_token(
        self, id_token: dict[str, Incomplete], token: dict[str, Incomplete], token_handler: Callable[..., str], request: Request
    ) -> str: ...
    def validate_jwt_bearer_token(self, token: str, scopes, request: Request) -> bool: ...
    def validate_id_token(self, token: str, scopes, request: Request) -> bool: ...
    def validate_silent_authorization(self, request: Request) -> bool: ...
    def validate_silent_login(self, request: Request) -> bool: ...
    def validate_user_match(self, id_token_hint: str, scopes, claims: dict[str, Incomplete], request: Request) -> bool: ...
    def get_userinfo_claims(self, request: Request) -> dict[str, Incomplete] | str: ...
    def refresh_id_token(self, request: Request) -> bool: ...
