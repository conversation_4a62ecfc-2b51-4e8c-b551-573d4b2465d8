__all__ = [
    "powerlaw_sequence",
    "zipf_rv",
    "cumulative_distribution",
    "discrete_sequence",
    "random_weighted_sample",
    "weighted_choice",
]

def powerlaw_sequence(n, exponent: float = 2.0, seed=None): ...
def zipf_rv(alpha, xmin: int = 1, seed=None) -> int: ...
def cumulative_distribution(distribution): ...
def discrete_sequence(n, distribution=None, cdistribution=None, seed=None): ...
def random_weighted_sample(mapping, k, seed=None): ...
def weighted_choice(mapping, seed=None): ...
