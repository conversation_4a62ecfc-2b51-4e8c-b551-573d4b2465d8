from _typeshed import Unused
from collections.abc import Iterable
from logging import Logger

from oauthlib.common import Request, _HTTPMethod

log: Logger

def signature_base_string(http_method: _HTTPMethod, base_str_uri: str, normalized_encoded_request_parameters: str) -> str: ...
def base_string_uri(uri: str, host: str | None = None) -> str: ...
def collect_parameters(
    uri_query: str = "",
    body: str | bytes | dict[str, str] | Iterable[tuple[str, str]] | None = None,
    headers: dict[str, str] | None = None,
    exclude_oauth_signature: bool = True,
    with_realm: bool = False,
) -> list[tuple[str, str]]: ...
def normalize_parameters(params: dict[str, str]) -> str: ...
def sign_hmac_sha1_with_client(sig_base_str: str, client): ...
def verify_hmac_sha1(request: Request, client_secret=None, resource_owner_secret=None) -> bool: ...
def sign_hmac_sha1(base_string: str | bytes, client_secret, resource_owner_secret): ...
def sign_hmac_sha256_with_client(sig_base_str, client): ...
def verify_hmac_sha256(request, client_secret=None, resource_owner_secret=None) -> bool: ...
def sign_hmac_sha256(base_string: str | bytes, client_secret, resource_owner_secret): ...
def sign_hmac_sha512_with_client(sig_base_str: str, client): ...
def verify_hmac_sha512(request, client_secret: str | None = None, resource_owner_secret: str | None = None) -> bool: ...
def sign_rsa_sha1_with_client(sig_base_str: str | bytes, client): ...
def verify_rsa_sha1(request, rsa_public_key: str) -> bool: ...
def sign_rsa_sha1(base_string, rsa_private_key): ...
def sign_rsa_sha256_with_client(sig_base_str: str, client): ...
def verify_rsa_sha256(request, rsa_public_key: str) -> bool: ...
def sign_rsa_sha512_with_client(sig_base_str: str, client): ...
def verify_rsa_sha512(request, rsa_public_key: str) -> bool: ...
def sign_plaintext_with_client(_signature_base_string: Unused, client) -> str: ...
def sign_plaintext(client_secret: str | None, resource_owner_secret: str | None) -> str: ...
def verify_plaintext(request, client_secret: str | None = None, resource_owner_secret: str | None = None) -> bool: ...
