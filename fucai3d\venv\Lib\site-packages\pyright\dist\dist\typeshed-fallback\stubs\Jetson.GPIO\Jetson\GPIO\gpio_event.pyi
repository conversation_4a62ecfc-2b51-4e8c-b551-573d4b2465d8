from collections.abc import Callable
from typing import Any, Final, Literal

NO_EDGE: Final = 0
RISING_EDGE: Final = 1
FALLING_EDGE: Final = 2
BOTH_EDGE: Final = 3

def add_edge_detect(
    chip_fd: int, chip_name: str, channel: int, request: int, bouncetime: int, poll_time: float
) -> Literal[1, 2, 0]: ...
def remove_edge_detect(chip_name: str, channel: int, timeout: float = ...) -> None: ...
def add_edge_callback(chip_name: str, channel: int, callback: Callable[[int], None]) -> None: ...
def edge_event_detected(chip_name: str, channel: int) -> bool: ...
def gpio_event_added(chip_name: str, channel: int) -> Any: ...
def blocking_wait_for_edge(chip_fd: int, chip_name: str, channel: int, request: int, bouncetime: int, timeout: float) -> int: ...
def event_cleanup(chip_name: str, channel: int) -> None: ...
