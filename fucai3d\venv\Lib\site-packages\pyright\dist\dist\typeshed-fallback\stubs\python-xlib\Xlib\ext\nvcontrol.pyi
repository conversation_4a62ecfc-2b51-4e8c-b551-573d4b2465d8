from _typeshed import Unused
from typing import Final

from Xlib.display import Display
from Xlib.protocol import rq
from Xlib.xobject import resource

extname: Final = "NV-CONTROL"

def query_target_count(self: Display | resource.Resource, target: Target) -> int: ...
def query_int_attribute(self: Display | resource.Resource, target: Target, display_mask: int, attr: int) -> int | None: ...
def set_int_attribute(self: Display | resource.Resource, target: Target, display_mask: int, attr: int, value: int) -> bool: ...
def query_string_attribute(self: Display | resource.Resource, target: Target, display_mask: int, attr: int) -> str | None: ...
def query_valid_attr_values(
    self: Display | resource.Resource, target: Target, display_mask: int, attr: int
) -> tuple[int, int] | None: ...
def query_binary_data(self: Display | resource.Resource, target: Target, display_mask: int, attr: int) -> bytes | None: ...
def get_coolers_used_by_gpu(self: Display | resource.Resource, target: Target) -> list[int] | None: ...
def get_gpu_count(self: Display | resource.Resource) -> int: ...
def get_name(self: Display | resource.Resource, target: Target) -> str | None: ...
def get_driver_version(self: Display | resource.Resource, target: Target) -> str | None: ...
def get_vbios_version(self: Display | resource.Resource, target: Target) -> str | None: ...
def get_gpu_uuid(self: Display | resource.Resource, target: Target) -> str | None: ...
def get_utilization_rates(self: Display | resource.Resource, target: Target) -> dict[str, str | int]: ...
def get_performance_modes(self: Display | resource.Resource, target: Target) -> list[dict[str, str | int]]: ...
def get_clock_info(self: Display | resource.Resource, target: Target) -> dict[str, str | int]: ...
def get_vram(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_irq(self: Display | resource.Resource, target: Target) -> int | None: ...
def supports_framelock(self: Display | resource.Resource, target: Target) -> int | None: ...
def gvo_supported(self: Display | resource.Resource, screen: Target) -> int | None: ...
def get_core_temp(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_core_threshold(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_default_core_threshold(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_max_core_threshold(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_ambient_temp(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_cuda_cores(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_memory_bus_width(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_total_dedicated_gpu_memory(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_used_dedicated_gpu_memory(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_curr_pcie_link_width(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_max_pcie_link_width(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_curr_pcie_link_generation(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_encoder_utilization(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_decoder_utilization(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_current_performance_level(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_gpu_nvclock_offset(self: Display | resource.Resource, target: Target, perf_level: int) -> int | None: ...
def set_gpu_nvclock_offset(self: Display | resource.Resource, target: Target, perf_level: int, offset: int) -> bool: ...
def set_gpu_nvclock_offset_all_levels(self: Display | resource.Resource, target: Target, offset: int) -> bool: ...
def get_gpu_nvclock_offset_range(
    self: Display | resource.Resource, target: Target, perf_level: int
) -> tuple[int, int] | None: ...
def get_mem_transfer_rate_offset(self: Display | resource.Resource, target: Target, perf_level: int) -> int | None: ...
def set_mem_transfer_rate_offset(self: Display | resource.Resource, target: Target, perf_level: int, offset: int) -> bool: ...
def set_mem_transfer_rate_offset_all_levels(self: Display | resource.Resource, target: Target, offset: int) -> bool: ...
def get_mem_transfer_rate_offset_range(
    self: Display | resource.Resource, target: Target, perf_level: int
) -> tuple[int, int] | None: ...
def get_cooler_manual_control_enabled(self: Display | resource.Resource, target: Target) -> int | None: ...
def set_cooler_manual_control_enabled(self: Display | resource.Resource, target: Target, enabled: bool) -> bool: ...
def get_fan_duty(self: Display | resource.Resource, target: Target) -> int | None: ...
def set_fan_duty(self: Display | resource.Resource, cooler: Target, speed: int) -> bool: ...
def get_fan_rpm(self: Display | resource.Resource, target: Target) -> int | None: ...
def get_max_displays(self: Display | resource.Resource, target: Target) -> int | None: ...
def init(disp: Display, info: Unused) -> None: ...

NV_CTRL_FLATPANEL_SCALING: Final = 2
NV_CTRL_FLATPANEL_SCALING_DEFAULT: Final = 0
NV_CTRL_FLATPANEL_SCALING_NATIVE: Final = 1
NV_CTRL_FLATPANEL_SCALING_SCALED: Final = 2
NV_CTRL_FLATPANEL_SCALING_CENTERED: Final = 3
NV_CTRL_FLATPANEL_SCALING_ASPECT_SCALED: Final = 4
NV_CTRL_FLATPANEL_DITHERING: Final = 3
NV_CTRL_FLATPANEL_DITHERING_DEFAULT: Final = 0
NV_CTRL_FLATPANEL_DITHERING_ENABLED: Final = 1
NV_CTRL_FLATPANEL_DITHERING_DISABLED: Final = 2
NV_CTRL_DITHERING: Final = 3
NV_CTRL_DITHERING_AUTO: Final = 0
NV_CTRL_DITHERING_ENABLED: Final = 1
NV_CTRL_DITHERING_DISABLED: Final = 2
NV_CTRL_DIGITAL_VIBRANCE: Final = 4
NV_CTRL_BUS_TYPE: Final = 5
NV_CTRL_BUS_TYPE_AGP: Final = 0
NV_CTRL_BUS_TYPE_PCI: Final = 1
NV_CTRL_BUS_TYPE_PCI_EXPRESS: Final = 2
NV_CTRL_BUS_TYPE_INTEGRATED: Final = 3
NV_CTRL_TOTAL_GPU_MEMORY: Final = 6
NV_CTRL_VIDEO_RAM: Final = NV_CTRL_TOTAL_GPU_MEMORY
NV_CTRL_IRQ: Final = 7
NV_CTRL_OPERATING_SYSTEM: Final = 8
NV_CTRL_OPERATING_SYSTEM_LINUX: Final = 0
NV_CTRL_OPERATING_SYSTEM_FREEBSD: Final = 1
NV_CTRL_OPERATING_SYSTEM_SUNOS: Final = 2
NV_CTRL_SYNC_TO_VBLANK: Final = 9
NV_CTRL_SYNC_TO_VBLANK_OFF: Final = 0
NV_CTRL_SYNC_TO_VBLANK_ON: Final = 1
NV_CTRL_LOG_ANISO: Final = 10
NV_CTRL_FSAA_MODE: Final = 11
NV_CTRL_FSAA_MODE_NONE: Final = 0
NV_CTRL_FSAA_MODE_2x: Final = 1
NV_CTRL_FSAA_MODE_2x_5t: Final = 2
NV_CTRL_FSAA_MODE_15x15: Final = 3
NV_CTRL_FSAA_MODE_2x2: Final = 4
NV_CTRL_FSAA_MODE_4x: Final = 5
NV_CTRL_FSAA_MODE_4x_9t: Final = 6
NV_CTRL_FSAA_MODE_8x: Final = 7
NV_CTRL_FSAA_MODE_16x: Final = 8
NV_CTRL_FSAA_MODE_8xS: Final = 9
NV_CTRL_FSAA_MODE_8xQ: Final = 10
NV_CTRL_FSAA_MODE_16xS: Final = 11
NV_CTRL_FSAA_MODE_16xQ: Final = 12
NV_CTRL_FSAA_MODE_32xS: Final = 13
NV_CTRL_FSAA_MODE_32x: Final = 14
NV_CTRL_FSAA_MODE_64xS: Final = 15
NV_CTRL_FSAA_MODE_MAX: Final = NV_CTRL_FSAA_MODE_64xS
NV_CTRL_UBB: Final = 13
NV_CTRL_UBB_OFF: Final = 0
NV_CTRL_UBB_ON: Final = 1
NV_CTRL_OVERLAY: Final = 14
NV_CTRL_OVERLAY_OFF: Final = 0
NV_CTRL_OVERLAY_ON: Final = 1
NV_CTRL_STEREO: Final = 16
NV_CTRL_STEREO_OFF: Final = 0
NV_CTRL_STEREO_DDC: Final = 1
NV_CTRL_STEREO_BLUELINE: Final = 2
NV_CTRL_STEREO_DIN: Final = 3
NV_CTRL_STEREO_PASSIVE_EYE_PER_DPY: Final = 4
NV_CTRL_STEREO_VERTICAL_INTERLACED: Final = 5
NV_CTRL_STEREO_COLOR_INTERLACED: Final = 6
NV_CTRL_STEREO_HORIZONTAL_INTERLACED: Final = 7
NV_CTRL_STEREO_CHECKERBOARD_PATTERN: Final = 8
NV_CTRL_STEREO_INVERSE_CHECKERBOARD_PATTERN: Final = 9
NV_CTRL_STEREO_3D_VISION: Final = 10
NV_CTRL_STEREO_3D_VISION_PRO: Final = 11
NV_CTRL_STEREO_HDMI_3D: Final = 12
NV_CTRL_STEREO_TRIDELITY_SL: Final = 13
NV_CTRL_STEREO_INBAND_STEREO_SIGNALING: Final = 14
NV_CTRL_STEREO_MAX: Final = NV_CTRL_STEREO_INBAND_STEREO_SIGNALING
NV_CTRL_EMULATE: Final = 17
NV_CTRL_EMULATE_NONE: Final = 0
NV_CTRL_TWINVIEW: Final = 18
NV_CTRL_TWINVIEW_NOT_ENABLED: Final = 0
NV_CTRL_TWINVIEW_ENABLED: Final = 1
NV_CTRL_CONNECTED_DISPLAYS: Final = 19
NV_CTRL_ENABLED_DISPLAYS: Final = 20
NV_CTRL_FRAMELOCK: Final = 21
NV_CTRL_FRAMELOCK_NOT_SUPPORTED: Final = 0
NV_CTRL_FRAMELOCK_SUPPORTED: Final = 1
NV_CTRL_FRAMELOCK_MASTER: Final = 22
NV_CTRL_FRAMELOCK_MASTER_FALSE: Final = 0
NV_CTRL_FRAMELOCK_MASTER_TRUE: Final = 1
NV_CTRL_FRAMELOCK_POLARITY: Final = 23
NV_CTRL_FRAMELOCK_POLARITY_RISING_EDGE: Final = 0x1
NV_CTRL_FRAMELOCK_POLARITY_FALLING_EDGE: Final = 0x2
NV_CTRL_FRAMELOCK_POLARITY_BOTH_EDGES: Final = 0x3
NV_CTRL_FRAMELOCK_SYNC_DELAY: Final = 24
NV_CTRL_FRAMELOCK_SYNC_DELAY_MAX: Final = 2047
NV_CTRL_FRAMELOCK_SYNC_DELAY_FACTOR: Final[float]
NV_CTRL_FRAMELOCK_SYNC_INTERVAL: Final = 25
NV_CTRL_FRAMELOCK_PORT0_STATUS: Final = 26
NV_CTRL_FRAMELOCK_PORT0_STATUS_INPUT: Final = 0
NV_CTRL_FRAMELOCK_PORT0_STATUS_OUTPUT: Final = 1
NV_CTRL_FRAMELOCK_PORT1_STATUS: Final = 27
NV_CTRL_FRAMELOCK_PORT1_STATUS_INPUT: Final = 0
NV_CTRL_FRAMELOCK_PORT1_STATUS_OUTPUT: Final = 1
NV_CTRL_FRAMELOCK_HOUSE_STATUS: Final = 28
NV_CTRL_FRAMELOCK_HOUSE_STATUS_NOT_DETECTED: Final = 0
NV_CTRL_FRAMELOCK_HOUSE_STATUS_DETECTED: Final = 1
NV_CTRL_FRAMELOCK_SYNC: Final = 29
NV_CTRL_FRAMELOCK_SYNC_DISABLE: Final = 0
NV_CTRL_FRAMELOCK_SYNC_ENABLE: Final = 1
NV_CTRL_FRAMELOCK_SYNC_READY: Final = 30
NV_CTRL_FRAMELOCK_SYNC_READY_FALSE: Final = 0
NV_CTRL_FRAMELOCK_SYNC_READY_TRUE: Final = 1
NV_CTRL_FRAMELOCK_STEREO_SYNC: Final = 31
NV_CTRL_FRAMELOCK_STEREO_SYNC_FALSE: Final = 0
NV_CTRL_FRAMELOCK_STEREO_SYNC_TRUE: Final = 1
NV_CTRL_FRAMELOCK_TEST_SIGNAL: Final = 32
NV_CTRL_FRAMELOCK_TEST_SIGNAL_DISABLE: Final = 0
NV_CTRL_FRAMELOCK_TEST_SIGNAL_ENABLE: Final = 1
NV_CTRL_FRAMELOCK_ETHERNET_DETECTED: Final = 33
NV_CTRL_FRAMELOCK_ETHERNET_DETECTED_NONE: Final = 0
NV_CTRL_FRAMELOCK_ETHERNET_DETECTED_PORT0: Final = 0x1
NV_CTRL_FRAMELOCK_ETHERNET_DETECTED_PORT1: Final = 0x2
NV_CTRL_FRAMELOCK_VIDEO_MODE: Final = 34
NV_CTRL_FRAMELOCK_VIDEO_MODE_NONE: Final = 0
NV_CTRL_FRAMELOCK_VIDEO_MODE_TTL: Final = 1
NV_CTRL_FRAMELOCK_VIDEO_MODE_NTSCPALSECAM: Final = 2
NV_CTRL_FRAMELOCK_VIDEO_MODE_HDTV: Final = 3
NV_CTRL_FRAMELOCK_VIDEO_MODE_COMPOSITE_AUTO: Final = 0
NV_CTRL_FRAMELOCK_VIDEO_MODE_COMPOSITE_BI_LEVEL: Final = 2
NV_CTRL_FRAMELOCK_VIDEO_MODE_COMPOSITE_TRI_LEVEL: Final = 3
NV_CTRL_FRAMELOCK_SYNC_RATE: Final = 35
NV_CTRL_FORCE_GENERIC_CPU: Final = 37
NV_CTRL_FORCE_GENERIC_CPU_DISABLE: Final = 0
NV_CTRL_FORCE_GENERIC_CPU_ENABLE: Final = 1
NV_CTRL_OPENGL_AA_LINE_GAMMA: Final = 38
NV_CTRL_OPENGL_AA_LINE_GAMMA_DISABLE: Final = 0
NV_CTRL_OPENGL_AA_LINE_GAMMA_ENABLE: Final = 1
NV_CTRL_FRAMELOCK_TIMING: Final = 39
NV_CTRL_FRAMELOCK_TIMING_FALSE: Final = 0
NV_CTRL_FRAMELOCK_TIMING_TRUE: Final = 1
NV_CTRL_FLIPPING_ALLOWED: Final = 40
NV_CTRL_FLIPPING_ALLOWED_FALSE: Final = 0
NV_CTRL_FLIPPING_ALLOWED_TRUE: Final = 1
NV_CTRL_ARCHITECTURE: Final = 41
NV_CTRL_ARCHITECTURE_X86: Final = 0
NV_CTRL_ARCHITECTURE_X86_64: Final = 1
NV_CTRL_ARCHITECTURE_IA64: Final = 2
NV_CTRL_ARCHITECTURE_ARM: Final = 3
NV_CTRL_ARCHITECTURE_AARCH64: Final = 4
NV_CTRL_ARCHITECTURE_PPC64LE: Final = 5
NV_CTRL_TEXTURE_CLAMPING: Final = 42
NV_CTRL_TEXTURE_CLAMPING_EDGE: Final = 0
NV_CTRL_TEXTURE_CLAMPING_SPEC: Final = 1
NV_CTRL_CURSOR_SHADOW: Final = 43
NV_CTRL_CURSOR_SHADOW_DISABLE: Final = 0
NV_CTRL_CURSOR_SHADOW_ENABLE: Final = 1
NV_CTRL_CURSOR_SHADOW_ALPHA: Final = 44
NV_CTRL_CURSOR_SHADOW_RED: Final = 45
NV_CTRL_CURSOR_SHADOW_GREEN: Final = 46
NV_CTRL_CURSOR_SHADOW_BLUE: Final = 47
NV_CTRL_CURSOR_SHADOW_X_OFFSET: Final = 48
NV_CTRL_CURSOR_SHADOW_Y_OFFSET: Final = 49
NV_CTRL_FSAA_APPLICATION_CONTROLLED: Final = 50
NV_CTRL_FSAA_APPLICATION_CONTROLLED_ENABLED: Final = 1
NV_CTRL_FSAA_APPLICATION_CONTROLLED_DISABLED: Final = 0
NV_CTRL_LOG_ANISO_APPLICATION_CONTROLLED: Final = 51
NV_CTRL_LOG_ANISO_APPLICATION_CONTROLLED_ENABLED: Final = 1
NV_CTRL_LOG_ANISO_APPLICATION_CONTROLLED_DISABLED: Final = 0
NV_CTRL_IMAGE_SHARPENING: Final = 52
NV_CTRL_TV_OVERSCAN: Final = 53
NV_CTRL_TV_FLICKER_FILTER: Final = 54
NV_CTRL_TV_BRIGHTNESS: Final = 55
NV_CTRL_TV_HUE: Final = 56
NV_CTRL_TV_CONTRAST: Final = 57
NV_CTRL_TV_SATURATION: Final = 58
NV_CTRL_TV_RESET_SETTINGS: Final = 59
NV_CTRL_GPU_CORE_TEMPERATURE: Final = 60
NV_CTRL_GPU_CORE_THRESHOLD: Final = 61
NV_CTRL_GPU_DEFAULT_CORE_THRESHOLD: Final = 62
NV_CTRL_GPU_MAX_CORE_THRESHOLD: Final = 63
NV_CTRL_AMBIENT_TEMPERATURE: Final = 64
NV_CTRL_PBUFFER_SCANOUT_SUPPORTED: Final = 65
NV_CTRL_PBUFFER_SCANOUT_FALSE: Final = 0
NV_CTRL_PBUFFER_SCANOUT_TRUE: Final = 1
NV_CTRL_PBUFFER_SCANOUT_XID: Final = 66
NV_CTRL_GVO_SUPPORTED: Final = 67
NV_CTRL_GVO_SUPPORTED_FALSE: Final = 0
NV_CTRL_GVO_SUPPORTED_TRUE: Final = 1
NV_CTRL_GVO_SYNC_MODE: Final = 68
NV_CTRL_GVO_SYNC_MODE_FREE_RUNNING: Final = 0
NV_CTRL_GVO_SYNC_MODE_GENLOCK: Final = 1
NV_CTRL_GVO_SYNC_MODE_FRAMELOCK: Final = 2
NV_CTRL_GVO_SYNC_SOURCE: Final = 69
NV_CTRL_GVO_SYNC_SOURCE_COMPOSITE: Final = 0
NV_CTRL_GVO_SYNC_SOURCE_SDI: Final = 1
NV_CTRL_GVIO_REQUESTED_VIDEO_FORMAT: Final = 70
NV_CTRL_GVIO_VIDEO_FORMAT_NONE: Final = 0
NV_CTRL_GVIO_VIDEO_FORMAT_487I_59_94_SMPTE259_NTSC: Final = 1
NV_CTRL_GVIO_VIDEO_FORMAT_576I_50_00_SMPTE259_PAL: Final = 2
NV_CTRL_GVIO_VIDEO_FORMAT_720P_59_94_SMPTE296: Final = 3
NV_CTRL_GVIO_VIDEO_FORMAT_720P_60_00_SMPTE296: Final = 4
NV_CTRL_GVIO_VIDEO_FORMAT_1035I_59_94_SMPTE260: Final = 5
NV_CTRL_GVIO_VIDEO_FORMAT_1035I_60_00_SMPTE260: Final = 6
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_50_00_SMPTE295: Final = 7
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_50_00_SMPTE274: Final = 8
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_59_94_SMPTE274: Final = 9
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_60_00_SMPTE274: Final = 10
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_23_976_SMPTE274: Final = 11
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_24_00_SMPTE274: Final = 12
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_25_00_SMPTE274: Final = 13
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_29_97_SMPTE274: Final = 14
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_30_00_SMPTE274: Final = 15
NV_CTRL_GVIO_VIDEO_FORMAT_720P_50_00_SMPTE296: Final = 16
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_48_00_SMPTE274: Final = 17
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_47_96_SMPTE274: Final = 18
NV_CTRL_GVIO_VIDEO_FORMAT_720P_30_00_SMPTE296: Final = 19
NV_CTRL_GVIO_VIDEO_FORMAT_720P_29_97_SMPTE296: Final = 20
NV_CTRL_GVIO_VIDEO_FORMAT_720P_25_00_SMPTE296: Final = 21
NV_CTRL_GVIO_VIDEO_FORMAT_720P_24_00_SMPTE296: Final = 22
NV_CTRL_GVIO_VIDEO_FORMAT_720P_23_98_SMPTE296: Final = 23
NV_CTRL_GVIO_VIDEO_FORMAT_1080PSF_25_00_SMPTE274: Final = 24
NV_CTRL_GVIO_VIDEO_FORMAT_1080PSF_29_97_SMPTE274: Final = 25
NV_CTRL_GVIO_VIDEO_FORMAT_1080PSF_30_00_SMPTE274: Final = 26
NV_CTRL_GVIO_VIDEO_FORMAT_1080PSF_24_00_SMPTE274: Final = 27
NV_CTRL_GVIO_VIDEO_FORMAT_1080PSF_23_98_SMPTE274: Final = 28
NV_CTRL_GVIO_VIDEO_FORMAT_2048P_30_00_SMPTE372: Final = 29
NV_CTRL_GVIO_VIDEO_FORMAT_2048P_29_97_SMPTE372: Final = 30
NV_CTRL_GVIO_VIDEO_FORMAT_2048I_60_00_SMPTE372: Final = 31
NV_CTRL_GVIO_VIDEO_FORMAT_2048I_59_94_SMPTE372: Final = 32
NV_CTRL_GVIO_VIDEO_FORMAT_2048P_25_00_SMPTE372: Final = 33
NV_CTRL_GVIO_VIDEO_FORMAT_2048I_50_00_SMPTE372: Final = 34
NV_CTRL_GVIO_VIDEO_FORMAT_2048P_24_00_SMPTE372: Final = 35
NV_CTRL_GVIO_VIDEO_FORMAT_2048P_23_98_SMPTE372: Final = 36
NV_CTRL_GVIO_VIDEO_FORMAT_2048I_48_00_SMPTE372: Final = 37
NV_CTRL_GVIO_VIDEO_FORMAT_2048I_47_96_SMPTE372: Final = 38
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_50_00_3G_LEVEL_A_SMPTE274: Final = 39
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_59_94_3G_LEVEL_A_SMPTE274: Final = 40
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_60_00_3G_LEVEL_A_SMPTE274: Final = 41
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_60_00_3G_LEVEL_B_SMPTE274: Final = 42
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_60_00_3G_LEVEL_B_SMPTE274: Final = 43
NV_CTRL_GVIO_VIDEO_FORMAT_2048I_60_00_3G_LEVEL_B_SMPTE372: Final = 44
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_50_00_3G_LEVEL_B_SMPTE274: Final = 45
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_50_00_3G_LEVEL_B_SMPTE274: Final = 46
NV_CTRL_GVIO_VIDEO_FORMAT_2048I_50_00_3G_LEVEL_B_SMPTE372: Final = 47
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_30_00_3G_LEVEL_B_SMPTE274: Final = 48
NV_CTRL_GVIO_VIDEO_FORMAT_2048P_30_00_3G_LEVEL_B_SMPTE372: Final = 49
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_25_00_3G_LEVEL_B_SMPTE274: Final = 50
NV_CTRL_GVIO_VIDEO_FORMAT_2048P_25_00_3G_LEVEL_B_SMPTE372: Final = 51
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_24_00_3G_LEVEL_B_SMPTE274: Final = 52
NV_CTRL_GVIO_VIDEO_FORMAT_2048P_24_00_3G_LEVEL_B_SMPTE372: Final = 53
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_48_00_3G_LEVEL_B_SMPTE274: Final = 54
NV_CTRL_GVIO_VIDEO_FORMAT_2048I_48_00_3G_LEVEL_B_SMPTE372: Final = 55
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_59_94_3G_LEVEL_B_SMPTE274: Final = 56
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_59_94_3G_LEVEL_B_SMPTE274: Final = 57
NV_CTRL_GVIO_VIDEO_FORMAT_2048I_59_94_3G_LEVEL_B_SMPTE372: Final = 58
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_29_97_3G_LEVEL_B_SMPTE274: Final = 59
NV_CTRL_GVIO_VIDEO_FORMAT_2048P_29_97_3G_LEVEL_B_SMPTE372: Final = 60
NV_CTRL_GVIO_VIDEO_FORMAT_1080P_23_98_3G_LEVEL_B_SMPTE274: Final = 61
NV_CTRL_GVIO_VIDEO_FORMAT_2048P_23_98_3G_LEVEL_B_SMPTE372: Final = 62
NV_CTRL_GVIO_VIDEO_FORMAT_1080I_47_96_3G_LEVEL_B_SMPTE274: Final = 63
NV_CTRL_GVIO_VIDEO_FORMAT_2048I_47_96_3G_LEVEL_B_SMPTE372: Final = 64
NV_CTRL_GVO_OUTPUT_VIDEO_FORMAT: Final = 70
NV_CTRL_GVO_VIDEO_FORMAT_NONE: Final = 0
NV_CTRL_GVO_VIDEO_FORMAT_487I_59_94_SMPTE259_NTSC: Final = 1
NV_CTRL_GVO_VIDEO_FORMAT_576I_50_00_SMPTE259_PAL: Final = 2
NV_CTRL_GVO_VIDEO_FORMAT_720P_59_94_SMPTE296: Final = 3
NV_CTRL_GVO_VIDEO_FORMAT_720P_60_00_SMPTE296: Final = 4
NV_CTRL_GVO_VIDEO_FORMAT_1035I_59_94_SMPTE260: Final = 5
NV_CTRL_GVO_VIDEO_FORMAT_1035I_60_00_SMPTE260: Final = 6
NV_CTRL_GVO_VIDEO_FORMAT_1080I_50_00_SMPTE295: Final = 7
NV_CTRL_GVO_VIDEO_FORMAT_1080I_50_00_SMPTE274: Final = 8
NV_CTRL_GVO_VIDEO_FORMAT_1080I_59_94_SMPTE274: Final = 9
NV_CTRL_GVO_VIDEO_FORMAT_1080I_60_00_SMPTE274: Final = 10
NV_CTRL_GVO_VIDEO_FORMAT_1080P_23_976_SMPTE274: Final = 11
NV_CTRL_GVO_VIDEO_FORMAT_1080P_24_00_SMPTE274: Final = 12
NV_CTRL_GVO_VIDEO_FORMAT_1080P_25_00_SMPTE274: Final = 13
NV_CTRL_GVO_VIDEO_FORMAT_1080P_29_97_SMPTE274: Final = 14
NV_CTRL_GVO_VIDEO_FORMAT_1080P_30_00_SMPTE274: Final = 15
NV_CTRL_GVO_VIDEO_FORMAT_720P_50_00_SMPTE296: Final = 16
NV_CTRL_GVO_VIDEO_FORMAT_1080I_48_00_SMPTE274: Final = 17
NV_CTRL_GVO_VIDEO_FORMAT_1080I_47_96_SMPTE274: Final = 18
NV_CTRL_GVO_VIDEO_FORMAT_720P_30_00_SMPTE296: Final = 19
NV_CTRL_GVO_VIDEO_FORMAT_720P_29_97_SMPTE296: Final = 20
NV_CTRL_GVO_VIDEO_FORMAT_720P_25_00_SMPTE296: Final = 21
NV_CTRL_GVO_VIDEO_FORMAT_720P_24_00_SMPTE296: Final = 22
NV_CTRL_GVO_VIDEO_FORMAT_720P_23_98_SMPTE296: Final = 23
NV_CTRL_GVO_VIDEO_FORMAT_1080PSF_25_00_SMPTE274: Final = 24
NV_CTRL_GVO_VIDEO_FORMAT_1080PSF_29_97_SMPTE274: Final = 25
NV_CTRL_GVO_VIDEO_FORMAT_1080PSF_30_00_SMPTE274: Final = 26
NV_CTRL_GVO_VIDEO_FORMAT_1080PSF_24_00_SMPTE274: Final = 27
NV_CTRL_GVO_VIDEO_FORMAT_1080PSF_23_98_SMPTE274: Final = 28
NV_CTRL_GVO_VIDEO_FORMAT_2048P_30_00_SMPTE372: Final = 29
NV_CTRL_GVO_VIDEO_FORMAT_2048P_29_97_SMPTE372: Final = 30
NV_CTRL_GVO_VIDEO_FORMAT_2048I_60_00_SMPTE372: Final = 31
NV_CTRL_GVO_VIDEO_FORMAT_2048I_59_94_SMPTE372: Final = 32
NV_CTRL_GVO_VIDEO_FORMAT_2048P_25_00_SMPTE372: Final = 33
NV_CTRL_GVO_VIDEO_FORMAT_2048I_50_00_SMPTE372: Final = 34
NV_CTRL_GVO_VIDEO_FORMAT_2048P_24_00_SMPTE372: Final = 35
NV_CTRL_GVO_VIDEO_FORMAT_2048P_23_98_SMPTE372: Final = 36
NV_CTRL_GVO_VIDEO_FORMAT_2048I_48_00_SMPTE372: Final = 37
NV_CTRL_GVO_VIDEO_FORMAT_2048I_47_96_SMPTE372: Final = 38
NV_CTRL_GVIO_DETECTED_VIDEO_FORMAT: Final = 71
NV_CTRL_GVO_INPUT_VIDEO_FORMAT: Final = 71
NV_CTRL_GVO_DATA_FORMAT: Final = 72
NV_CTRL_GVO_DATA_FORMAT_R8G8B8_TO_YCRCB444: Final = 0
NV_CTRL_GVO_DATA_FORMAT_R8G8B8A8_TO_YCRCBA4444: Final = 1
NV_CTRL_GVO_DATA_FORMAT_R8G8B8Z10_TO_YCRCBZ4444: Final = 2
NV_CTRL_GVO_DATA_FORMAT_R8G8B8_TO_YCRCB422: Final = 3
NV_CTRL_GVO_DATA_FORMAT_R8G8B8A8_TO_YCRCBA4224: Final = 4
NV_CTRL_GVO_DATA_FORMAT_R8G8B8Z10_TO_YCRCBZ4224: Final = 5
NV_CTRL_GVO_DATA_FORMAT_R8G8B8_TO_RGB444: Final = 6
NV_CTRL_GVO_DATA_FORMAT_X8X8X8_444_PASSTHRU: Final = 6
NV_CTRL_GVO_DATA_FORMAT_R8G8B8A8_TO_RGBA4444: Final = 7
NV_CTRL_GVO_DATA_FORMAT_X8X8X8A8_4444_PASSTHRU: Final = 7
NV_CTRL_GVO_DATA_FORMAT_R8G8B8Z10_TO_RGBZ4444: Final = 8
NV_CTRL_GVO_DATA_FORMAT_X8X8X8Z8_4444_PASSTHRU: Final = 8
NV_CTRL_GVO_DATA_FORMAT_Y10CR10CB10_TO_YCRCB444: Final = 9
NV_CTRL_GVO_DATA_FORMAT_X10X10X10_444_PASSTHRU: Final = 9
NV_CTRL_GVO_DATA_FORMAT_Y10CR8CB8_TO_YCRCB444: Final = 10
NV_CTRL_GVO_DATA_FORMAT_X10X8X8_444_PASSTHRU: Final = 10
NV_CTRL_GVO_DATA_FORMAT_Y10CR8CB8A10_TO_YCRCBA4444: Final = 11
NV_CTRL_GVO_DATA_FORMAT_X10X8X8A10_4444_PASSTHRU: Final = 11
NV_CTRL_GVO_DATA_FORMAT_Y10CR8CB8Z10_TO_YCRCBZ4444: Final = 12
NV_CTRL_GVO_DATA_FORMAT_X10X8X8Z10_4444_PASSTHRU: Final = 12
NV_CTRL_GVO_DATA_FORMAT_DUAL_R8G8B8_TO_DUAL_YCRCB422: Final = 13
NV_CTRL_GVO_DATA_FORMAT_DUAL_Y8CR8CB8_TO_DUAL_YCRCB422: Final = 14
NV_CTRL_GVO_DATA_FORMAT_DUAL_X8X8X8_TO_DUAL_422_PASSTHRU: Final = 14
NV_CTRL_GVO_DATA_FORMAT_R10G10B10_TO_YCRCB422: Final = 15
NV_CTRL_GVO_DATA_FORMAT_R10G10B10_TO_YCRCB444: Final = 16
NV_CTRL_GVO_DATA_FORMAT_Y12CR12CB12_TO_YCRCB444: Final = 17
NV_CTRL_GVO_DATA_FORMAT_X12X12X12_444_PASSTHRU: Final = 17
NV_CTRL_GVO_DATA_FORMAT_R12G12B12_TO_YCRCB444: Final = 18
NV_CTRL_GVO_DATA_FORMAT_X8X8X8_422_PASSTHRU: Final = 19
NV_CTRL_GVO_DATA_FORMAT_X8X8X8A8_4224_PASSTHRU: Final = 20
NV_CTRL_GVO_DATA_FORMAT_X8X8X8Z8_4224_PASSTHRU: Final = 21
NV_CTRL_GVO_DATA_FORMAT_X10X10X10_422_PASSTHRU: Final = 22
NV_CTRL_GVO_DATA_FORMAT_X10X8X8_422_PASSTHRU: Final = 23
NV_CTRL_GVO_DATA_FORMAT_X10X8X8A10_4224_PASSTHRU: Final = 24
NV_CTRL_GVO_DATA_FORMAT_X10X8X8Z10_4224_PASSTHRU: Final = 25
NV_CTRL_GVO_DATA_FORMAT_X12X12X12_422_PASSTHRU: Final = 26
NV_CTRL_GVO_DATA_FORMAT_R12G12B12_TO_YCRCB422: Final = 27
NV_CTRL_GVO_DISPLAY_X_SCREEN: Final = 73
NV_CTRL_GVO_DISPLAY_X_SCREEN_ENABLE: Final = 1
NV_CTRL_GVO_DISPLAY_X_SCREEN_DISABLE: Final = 0
NV_CTRL_GVO_COMPOSITE_SYNC_INPUT_DETECTED: Final = 74
NV_CTRL_GVO_COMPOSITE_SYNC_INPUT_DETECTED_FALSE: Final = 0
NV_CTRL_GVO_COMPOSITE_SYNC_INPUT_DETECTED_TRUE: Final = 1
NV_CTRL_GVO_COMPOSITE_SYNC_INPUT_DETECT_MODE: Final = 75
NV_CTRL_GVO_COMPOSITE_SYNC_INPUT_DETECT_MODE_AUTO: Final = 0
NV_CTRL_GVO_COMPOSITE_SYNC_INPUT_DETECT_MODE_BI_LEVEL: Final = 1
NV_CTRL_GVO_COMPOSITE_SYNC_INPUT_DETECT_MODE_TRI_LEVEL: Final = 2
NV_CTRL_GVO_SDI_SYNC_INPUT_DETECTED: Final = 76
NV_CTRL_GVO_SDI_SYNC_INPUT_DETECTED_NONE: Final = 0
NV_CTRL_GVO_SDI_SYNC_INPUT_DETECTED_HD: Final = 1
NV_CTRL_GVO_SDI_SYNC_INPUT_DETECTED_SD: Final = 2
NV_CTRL_GVO_VIDEO_OUTPUTS: Final = 77
NV_CTRL_GVO_VIDEO_OUTPUTS_NONE: Final = 0
NV_CTRL_GVO_VIDEO_OUTPUTS_VIDEO1: Final = 1
NV_CTRL_GVO_VIDEO_OUTPUTS_VIDEO2: Final = 2
NV_CTRL_GVO_VIDEO_OUTPUTS_VIDEO_BOTH: Final = 3
NV_CTRL_GVO_FIRMWARE_VERSION: Final = 78
NV_CTRL_GVO_SYNC_DELAY_PIXELS: Final = 79
NV_CTRL_GVO_SYNC_DELAY_LINES: Final = 80
NV_CTRL_GVO_INPUT_VIDEO_FORMAT_REACQUIRE: Final = 81
NV_CTRL_GVO_INPUT_VIDEO_FORMAT_REACQUIRE_FALSE: Final = 0
NV_CTRL_GVO_INPUT_VIDEO_FORMAT_REACQUIRE_TRUE: Final = 1
NV_CTRL_GVO_GLX_LOCKED: Final = 82
NV_CTRL_GVO_GLX_LOCKED_FALSE: Final = 0
NV_CTRL_GVO_GLX_LOCKED_TRUE: Final = 1
NV_CTRL_GVIO_VIDEO_FORMAT_WIDTH: Final = 83
NV_CTRL_GVIO_VIDEO_FORMAT_HEIGHT: Final = 84
NV_CTRL_GVIO_VIDEO_FORMAT_REFRESH_RATE: Final = 85
NV_CTRL_GVO_VIDEO_FORMAT_WIDTH: Final = 83
NV_CTRL_GVO_VIDEO_FORMAT_HEIGHT: Final = 84
NV_CTRL_GVO_VIDEO_FORMAT_REFRESH_RATE: Final = 85
NV_CTRL_GVO_X_SCREEN_PAN_X: Final = 86
NV_CTRL_GVO_X_SCREEN_PAN_Y: Final = 87
NV_CTRL_GPU_OVERCLOCKING_STATE: Final = 88
NV_CTRL_GPU_OVERCLOCKING_STATE_NONE: Final = 0
NV_CTRL_GPU_OVERCLOCKING_STATE_MANUAL: Final = 1
NV_CTRL_GPU_2D_CLOCK_FREQS: Final = 89
NV_CTRL_GPU_3D_CLOCK_FREQS: Final = 90
NV_CTRL_GPU_DEFAULT_2D_CLOCK_FREQS: Final = 91
NV_CTRL_GPU_DEFAULT_3D_CLOCK_FREQS: Final = 92
NV_CTRL_GPU_CURRENT_CLOCK_FREQS: Final = 93
NV_CTRL_GPU_OPTIMAL_CLOCK_FREQS: Final = 94
NV_CTRL_GPU_OPTIMAL_CLOCK_FREQS_INVALID: Final = 0
NV_CTRL_GPU_OPTIMAL_CLOCK_FREQS_DETECTION: Final = 95
NV_CTRL_GPU_OPTIMAL_CLOCK_FREQS_DETECTION_START: Final = 0
NV_CTRL_GPU_OPTIMAL_CLOCK_FREQS_DETECTION_CANCEL: Final = 1
NV_CTRL_GPU_OPTIMAL_CLOCK_FREQS_DETECTION_STATE: Final = 96
NV_CTRL_GPU_OPTIMAL_CLOCK_FREQS_DETECTION_STATE_IDLE: Final = 0
NV_CTRL_GPU_OPTIMAL_CLOCK_FREQS_DETECTION_STATE_BUSY: Final = 1
NV_CTRL_FLATPANEL_CHIP_LOCATION: Final = 215
NV_CTRL_FLATPANEL_CHIP_LOCATION_INTERNAL: Final = 0
NV_CTRL_FLATPANEL_CHIP_LOCATION_EXTERNAL: Final = 1
NV_CTRL_FLATPANEL_LINK: Final = 216
NV_CTRL_FLATPANEL_LINK_SINGLE: Final = 0
NV_CTRL_FLATPANEL_LINK_DUAL: Final = 1
NV_CTRL_FLATPANEL_LINK_QUAD: Final = 3
NV_CTRL_FLATPANEL_SIGNAL: Final = 217
NV_CTRL_FLATPANEL_SIGNAL_LVDS: Final = 0
NV_CTRL_FLATPANEL_SIGNAL_TMDS: Final = 1
NV_CTRL_FLATPANEL_SIGNAL_DISPLAYPORT: Final = 2
NV_CTRL_USE_HOUSE_SYNC: Final = 218
NV_CTRL_USE_HOUSE_SYNC_DISABLED: Final = 0
NV_CTRL_USE_HOUSE_SYNC_INPUT: Final = 1
NV_CTRL_USE_HOUSE_SYNC_OUTPUT: Final = 2
NV_CTRL_USE_HOUSE_SYNC_FALSE: Final = 0
NV_CTRL_USE_HOUSE_SYNC_TRUE: Final = 1
NV_CTRL_EDID_AVAILABLE: Final = 219
NV_CTRL_EDID_AVAILABLE_FALSE: Final = 0
NV_CTRL_EDID_AVAILABLE_TRUE: Final = 1
NV_CTRL_FORCE_STEREO: Final = 220
NV_CTRL_FORCE_STEREO_FALSE: Final = 0
NV_CTRL_FORCE_STEREO_TRUE: Final = 1
NV_CTRL_IMAGE_SETTINGS: Final = 221
NV_CTRL_IMAGE_SETTINGS_HIGH_QUALITY: Final = 0
NV_CTRL_IMAGE_SETTINGS_QUALITY: Final = 1
NV_CTRL_IMAGE_SETTINGS_PERFORMANCE: Final = 2
NV_CTRL_IMAGE_SETTINGS_HIGH_PERFORMANCE: Final = 3
NV_CTRL_XINERAMA: Final = 222
NV_CTRL_XINERAMA_OFF: Final = 0
NV_CTRL_XINERAMA_ON: Final = 1
NV_CTRL_XINERAMA_STEREO: Final = 223
NV_CTRL_XINERAMA_STEREO_FALSE: Final = 0
NV_CTRL_XINERAMA_STEREO_TRUE: Final = 1
NV_CTRL_BUS_RATE: Final = 224
NV_CTRL_GPU_PCIE_MAX_LINK_WIDTH: Final = NV_CTRL_BUS_RATE
NV_CTRL_SHOW_SLI_VISUAL_INDICATOR: Final = 225
NV_CTRL_SHOW_SLI_VISUAL_INDICATOR_FALSE: Final = 0
NV_CTRL_SHOW_SLI_VISUAL_INDICATOR_TRUE: Final = 1
NV_CTRL_SHOW_SLI_HUD: Final = NV_CTRL_SHOW_SLI_VISUAL_INDICATOR
NV_CTRL_SHOW_SLI_HUD_FALSE: Final = NV_CTRL_SHOW_SLI_VISUAL_INDICATOR_FALSE
NV_CTRL_SHOW_SLI_HUD_TRUE: Final = NV_CTRL_SHOW_SLI_VISUAL_INDICATOR_TRUE
NV_CTRL_XV_SYNC_TO_DISPLAY: Final = 226
NV_CTRL_GVIO_REQUESTED_VIDEO_FORMAT2: Final = 227
NV_CTRL_GVO_OUTPUT_VIDEO_FORMAT2: Final = 227
NV_CTRL_GVO_OVERRIDE_HW_CSC: Final = 228
NV_CTRL_GVO_OVERRIDE_HW_CSC_FALSE: Final = 0
NV_CTRL_GVO_OVERRIDE_HW_CSC_TRUE: Final = 1
NV_CTRL_GVO_CAPABILITIES: Final = 229
NV_CTRL_GVO_CAPABILITIES_APPLY_CSC_IMMEDIATELY: Final = 0x00000001
NV_CTRL_GVO_CAPABILITIES_APPLY_CSC_TO_X_SCREEN: Final = 0x00000002
NV_CTRL_GVO_CAPABILITIES_COMPOSITE_TERMINATION: Final = 0x00000004
NV_CTRL_GVO_CAPABILITIES_SHARED_SYNC_BNC: Final = 0x00000008
NV_CTRL_GVO_CAPABILITIES_MULTIRATE_SYNC: Final = 0x00000010
NV_CTRL_GVO_CAPABILITIES_ADVANCE_SYNC_SKEW: Final = 0x00000020
NV_CTRL_GVO_COMPOSITE_TERMINATION: Final = 230
NV_CTRL_GVO_COMPOSITE_TERMINATION_ENABLE: Final = 1
NV_CTRL_GVO_COMPOSITE_TERMINATION_DISABLE: Final = 0
NV_CTRL_ASSOCIATED_DISPLAY_DEVICES: Final = 231
NV_CTRL_FRAMELOCK_SLAVES: Final = 232
NV_CTRL_FRAMELOCK_MASTERABLE: Final = 233
NV_CTRL_PROBE_DISPLAYS: Final = 234
NV_CTRL_REFRESH_RATE: Final = 235
NV_CTRL_GVO_FLIP_QUEUE_SIZE: Final = 236
NV_CTRL_CURRENT_SCANLINE: Final = 237
NV_CTRL_INITIAL_PIXMAP_PLACEMENT: Final = 238
NV_CTRL_INITIAL_PIXMAP_PLACEMENT_FORCE_SYSMEM: Final = 0
NV_CTRL_INITIAL_PIXMAP_PLACEMENT_SYSMEM: Final = 1
NV_CTRL_INITIAL_PIXMAP_PLACEMENT_VIDMEM: Final = 2
NV_CTRL_INITIAL_PIXMAP_PLACEMENT_RESERVED: Final = 3
NV_CTRL_INITIAL_PIXMAP_PLACEMENT_GPU_SYSMEM: Final = 4
NV_CTRL_PCI_BUS: Final = 239
NV_CTRL_PCI_DEVICE: Final = 240
NV_CTRL_PCI_FUNCTION: Final = 241
NV_CTRL_FRAMELOCK_FPGA_REVISION: Final = 242
NV_CTRL_MAX_SCREEN_WIDTH: Final = 243
NV_CTRL_MAX_SCREEN_HEIGHT: Final = 244
NV_CTRL_MAX_DISPLAYS: Final = 245
NV_CTRL_DYNAMIC_TWINVIEW: Final = 246
NV_CTRL_MULTIGPU_DISPLAY_OWNER: Final = 247
NV_CTRL_GPU_SCALING: Final = 248
NV_CTRL_GPU_SCALING_TARGET_INVALID: Final = 0
NV_CTRL_GPU_SCALING_TARGET_FLATPANEL_BEST_FIT: Final = 1
NV_CTRL_GPU_SCALING_TARGET_FLATPANEL_NATIVE: Final = 2
NV_CTRL_GPU_SCALING_METHOD_INVALID: Final = 0
NV_CTRL_GPU_SCALING_METHOD_STRETCHED: Final = 1
NV_CTRL_GPU_SCALING_METHOD_CENTERED: Final = 2
NV_CTRL_GPU_SCALING_METHOD_ASPECT_SCALED: Final = 3
NV_CTRL_FRONTEND_RESOLUTION: Final = 249
NV_CTRL_BACKEND_RESOLUTION: Final = 250
NV_CTRL_FLATPANEL_NATIVE_RESOLUTION: Final = 251
NV_CTRL_FLATPANEL_BEST_FIT_RESOLUTION: Final = 252
NV_CTRL_GPU_SCALING_ACTIVE: Final = 253
NV_CTRL_DFP_SCALING_ACTIVE: Final = 254
NV_CTRL_FSAA_APPLICATION_ENHANCED: Final = 255
NV_CTRL_FSAA_APPLICATION_ENHANCED_ENABLED: Final = 1
NV_CTRL_FSAA_APPLICATION_ENHANCED_DISABLED: Final = 0
NV_CTRL_FRAMELOCK_SYNC_RATE_4: Final = 256
NV_CTRL_GVO_LOCK_OWNER: Final = 257
NV_CTRL_GVO_LOCK_OWNER_NONE: Final = 0
NV_CTRL_GVO_LOCK_OWNER_GLX: Final = 1
NV_CTRL_GVO_LOCK_OWNER_CLONE: Final = 2
NV_CTRL_GVO_LOCK_OWNER_X_SCREEN: Final = 3
NV_CTRL_HWOVERLAY: Final = 258
NV_CTRL_HWOVERLAY_FALSE: Final = 0
NV_CTRL_HWOVERLAY_TRUE: Final = 1
NV_CTRL_NUM_GPU_ERRORS_RECOVERED: Final = 259
NV_CTRL_REFRESH_RATE_3: Final = 260
NV_CTRL_ONDEMAND_VBLANK_INTERRUPTS: Final = 261
NV_CTRL_ONDEMAND_VBLANK_INTERRUPTS_OFF: Final = 0
NV_CTRL_ONDEMAND_VBLANK_INTERRUPTS_ON: Final = 1
NV_CTRL_GPU_POWER_SOURCE: Final = 262
NV_CTRL_GPU_POWER_SOURCE_AC: Final = 0
NV_CTRL_GPU_POWER_SOURCE_BATTERY: Final = 1
NV_CTRL_GPU_CURRENT_PERFORMANCE_MODE: Final = 263
NV_CTRL_GPU_CURRENT_PERFORMANCE_MODE_DESKTOP: Final = 0
NV_CTRL_GPU_CURRENT_PERFORMANCE_MODE_MAXPERF: Final = 1
NV_CTRL_GLYPH_CACHE: Final = 264
NV_CTRL_GLYPH_CACHE_DISABLED: Final = 0
NV_CTRL_GLYPH_CACHE_ENABLED: Final = 1
NV_CTRL_GPU_CURRENT_PERFORMANCE_LEVEL: Final = 265
NV_CTRL_GPU_ADAPTIVE_CLOCK_STATE: Final = 266
NV_CTRL_GPU_ADAPTIVE_CLOCK_STATE_DISABLED: Final = 0
NV_CTRL_GPU_ADAPTIVE_CLOCK_STATE_ENABLED: Final = 1
NV_CTRL_GVO_OUTPUT_VIDEO_LOCKED: Final = 267
NV_CTRL_GVO_OUTPUT_VIDEO_LOCKED_FALSE: Final = 0
NV_CTRL_GVO_OUTPUT_VIDEO_LOCKED_TRUE: Final = 1
NV_CTRL_GVO_SYNC_LOCK_STATUS: Final = 268
NV_CTRL_GVO_SYNC_LOCK_STATUS_UNLOCKED: Final = 0
NV_CTRL_GVO_SYNC_LOCK_STATUS_LOCKED: Final = 1
NV_CTRL_GVO_ANC_TIME_CODE_GENERATION: Final = 269
NV_CTRL_GVO_ANC_TIME_CODE_GENERATION_DISABLE: Final = 0
NV_CTRL_GVO_ANC_TIME_CODE_GENERATION_ENABLE: Final = 1
NV_CTRL_GVO_COMPOSITE: Final = 270
NV_CTRL_GVO_COMPOSITE_DISABLE: Final = 0
NV_CTRL_GVO_COMPOSITE_ENABLE: Final = 1
NV_CTRL_GVO_COMPOSITE_ALPHA_KEY: Final = 271
NV_CTRL_GVO_COMPOSITE_ALPHA_KEY_DISABLE: Final = 0
NV_CTRL_GVO_COMPOSITE_ALPHA_KEY_ENABLE: Final = 1
NV_CTRL_GVO_COMPOSITE_LUMA_KEY_RANGE: Final = 272
NV_CTRL_GVO_COMPOSITE_CR_KEY_RANGE: Final = 273
NV_CTRL_GVO_COMPOSITE_CB_KEY_RANGE: Final = 274
NV_CTRL_GVO_COMPOSITE_NUM_KEY_RANGES: Final = 275
NV_CTRL_SWITCH_TO_DISPLAYS: Final = 276
NV_CTRL_NOTEBOOK_DISPLAY_CHANGE_LID_EVENT: Final = 277
NV_CTRL_NOTEBOOK_INTERNAL_LCD: Final = 278
NV_CTRL_DEPTH_30_ALLOWED: Final = 279
NV_CTRL_MODE_SET_EVENT: Final = 280
NV_CTRL_OPENGL_AA_LINE_GAMMA_VALUE: Final = 281
NV_CTRL_VCSC_HIGH_PERF_MODE: Final = 282
NV_CTRL_VCSC_HIGH_PERF_MODE_DISABLE: Final = 0
NV_CTRL_VCSC_HIGH_PERF_MODE_ENABLE: Final = 1
NV_CTRL_DISPLAYPORT_LINK_RATE: Final = 291
NV_CTRL_DISPLAYPORT_LINK_RATE_DISABLED: Final = 0x0
NV_CTRL_DISPLAYPORT_LINK_RATE_1_62GBPS: Final = 0x6
NV_CTRL_DISPLAYPORT_LINK_RATE_2_70GBPS: Final = 0xA
NV_CTRL_STEREO_EYES_EXCHANGE: Final = 292
NV_CTRL_STEREO_EYES_EXCHANGE_OFF: Final = 0
NV_CTRL_STEREO_EYES_EXCHANGE_ON: Final = 1
NV_CTRL_NO_SCANOUT: Final = 293
NV_CTRL_NO_SCANOUT_DISABLED: Final = 0
NV_CTRL_NO_SCANOUT_ENABLED: Final = 1
NV_CTRL_GVO_CSC_CHANGED_EVENT: Final = 294
NV_CTRL_FRAMELOCK_SLAVEABLE: Final = 295
NV_CTRL_GVO_SYNC_TO_DISPLAY: Final = 296
NV_CTRL_GVO_SYNC_TO_DISPLAY_DISABLE: Final = 0
NV_CTRL_GVO_SYNC_TO_DISPLAY_ENABLE: Final = 1
NV_CTRL_X_SERVER_UNIQUE_ID: Final = 297
NV_CTRL_PIXMAP_CACHE: Final = 298
NV_CTRL_PIXMAP_CACHE_DISABLE: Final = 0
NV_CTRL_PIXMAP_CACHE_ENABLE: Final = 1
NV_CTRL_PIXMAP_CACHE_ROUNDING_SIZE_KB: Final = 299
NV_CTRL_IS_GVO_DISPLAY: Final = 300
NV_CTRL_IS_GVO_DISPLAY_FALSE: Final = 0
NV_CTRL_IS_GVO_DISPLAY_TRUE: Final = 1
NV_CTRL_PCI_ID: Final = 301
NV_CTRL_GVO_FULL_RANGE_COLOR: Final = 302
NV_CTRL_GVO_FULL_RANGE_COLOR_DISABLED: Final = 0
NV_CTRL_GVO_FULL_RANGE_COLOR_ENABLED: Final = 1
NV_CTRL_SLI_MOSAIC_MODE_AVAILABLE: Final = 303
NV_CTRL_SLI_MOSAIC_MODE_AVAILABLE_FALSE: Final = 0
NV_CTRL_SLI_MOSAIC_MODE_AVAILABLE_TRUE: Final = 1
NV_CTRL_GVO_ENABLE_RGB_DATA: Final = 304
NV_CTRL_GVO_ENABLE_RGB_DATA_DISABLE: Final = 0
NV_CTRL_GVO_ENABLE_RGB_DATA_ENABLE: Final = 1
NV_CTRL_IMAGE_SHARPENING_DEFAULT: Final = 305
NV_CTRL_PCI_DOMAIN: Final = 306
NV_CTRL_GVI_NUM_JACKS: Final = 307
NV_CTRL_GVI_MAX_LINKS_PER_STREAM: Final = 308
NV_CTRL_GVI_DETECTED_CHANNEL_BITS_PER_COMPONENT: Final = 309
NV_CTRL_GVI_BITS_PER_COMPONENT_UNKNOWN: Final = 0
NV_CTRL_GVI_BITS_PER_COMPONENT_8: Final = 1
NV_CTRL_GVI_BITS_PER_COMPONENT_10: Final = 2
NV_CTRL_GVI_BITS_PER_COMPONENT_12: Final = 3
NV_CTRL_GVI_REQUESTED_STREAM_BITS_PER_COMPONENT: Final = 310
NV_CTRL_GVI_DETECTED_CHANNEL_COMPONENT_SAMPLING: Final = 311
NV_CTRL_GVI_COMPONENT_SAMPLING_UNKNOWN: Final = 0
NV_CTRL_GVI_COMPONENT_SAMPLING_4444: Final = 1
NV_CTRL_GVI_COMPONENT_SAMPLING_4224: Final = 2
NV_CTRL_GVI_COMPONENT_SAMPLING_444: Final = 3
NV_CTRL_GVI_COMPONENT_SAMPLING_422: Final = 4
NV_CTRL_GVI_COMPONENT_SAMPLING_420: Final = 5
NV_CTRL_GVI_REQUESTED_STREAM_COMPONENT_SAMPLING: Final = 312
NV_CTRL_GVI_REQUESTED_STREAM_CHROMA_EXPAND: Final = 313
NV_CTRL_GVI_CHROMA_EXPAND_FALSE: Final = 0
NV_CTRL_GVI_CHROMA_EXPAND_TRUE: Final = 1
NV_CTRL_GVI_DETECTED_CHANNEL_COLOR_SPACE: Final = 314
NV_CTRL_GVI_COLOR_SPACE_UNKNOWN: Final = 0
NV_CTRL_GVI_COLOR_SPACE_GBR: Final = 1
NV_CTRL_GVI_COLOR_SPACE_GBRA: Final = 2
NV_CTRL_GVI_COLOR_SPACE_GBRD: Final = 3
NV_CTRL_GVI_COLOR_SPACE_YCBCR: Final = 4
NV_CTRL_GVI_COLOR_SPACE_YCBCRA: Final = 5
NV_CTRL_GVI_COLOR_SPACE_YCBCRD: Final = 6
NV_CTRL_GVI_DETECTED_CHANNEL_LINK_ID: Final = 315
NV_CTRL_GVI_LINK_ID_UNKNOWN: Final = 0xFFFF
NV_CTRL_GVI_DETECTED_CHANNEL_SMPTE352_IDENTIFIER: Final = 316
NV_CTRL_GVI_GLOBAL_IDENTIFIER: Final = 317
NV_CTRL_FRAMELOCK_SYNC_DELAY_RESOLUTION: Final = 318
NV_CTRL_GPU_COOLER_MANUAL_CONTROL: Final = 319
NV_CTRL_GPU_COOLER_MANUAL_CONTROL_FALSE: Final = 0
NV_CTRL_GPU_COOLER_MANUAL_CONTROL_TRUE: Final = 1
NV_CTRL_THERMAL_COOLER_LEVEL: Final = 320
NV_CTRL_THERMAL_COOLER_LEVEL_SET_DEFAULT: Final = 321
NV_CTRL_THERMAL_COOLER_CONTROL_TYPE: Final = 322
NV_CTRL_THERMAL_COOLER_CONTROL_TYPE_NONE: Final = 0
NV_CTRL_THERMAL_COOLER_CONTROL_TYPE_TOGGLE: Final = 1
NV_CTRL_THERMAL_COOLER_CONTROL_TYPE_VARIABLE: Final = 2
NV_CTRL_THERMAL_COOLER_TARGET: Final = 323
NV_CTRL_THERMAL_COOLER_TARGET_NONE: Final = 0
NV_CTRL_THERMAL_COOLER_TARGET_GPU: Final = 1
NV_CTRL_THERMAL_COOLER_TARGET_MEMORY: Final = 2
NV_CTRL_THERMAL_COOLER_TARGET_POWER_SUPPLY: Final = 4
NV_CTRL_THERMAL_COOLER_TARGET_GPU_RELATED: Final = 7
NV_CTRL_GPU_ECC_SUPPORTED: Final = 324
NV_CTRL_GPU_ECC_SUPPORTED_FALSE: Final = 0
NV_CTRL_GPU_ECC_SUPPORTED_TRUE: Final = 1
NV_CTRL_GPU_ECC_STATUS: Final = 325
NV_CTRL_GPU_ECC_STATUS_DISABLED: Final = 0
NV_CTRL_GPU_ECC_STATUS_ENABLED: Final = 1
NV_CTRL_GPU_ECC_CONFIGURATION_SUPPORTED: Final = 326
NV_CTRL_GPU_ECC_CONFIGURATION_SUPPORTED_FALSE: Final = 0
NV_CTRL_GPU_ECC_CONFIGURATION_SUPPORTED_TRUE: Final = 1
NV_CTRL_GPU_ECC_CONFIGURATION: Final = 327
NV_CTRL_GPU_ECC_CONFIGURATION_DISABLED: Final = 0
NV_CTRL_GPU_ECC_CONFIGURATION_ENABLED: Final = 1
NV_CTRL_GPU_ECC_DEFAULT_CONFIGURATION: Final = 328
NV_CTRL_GPU_ECC_DEFAULT_CONFIGURATION_DISABLED: Final = 0
NV_CTRL_GPU_ECC_DEFAULT_CONFIGURATION_ENABLED: Final = 1
NV_CTRL_GPU_ECC_SINGLE_BIT_ERRORS: Final = 329
NV_CTRL_GPU_ECC_DOUBLE_BIT_ERRORS: Final = 330
NV_CTRL_GPU_ECC_AGGREGATE_SINGLE_BIT_ERRORS: Final = 331
NV_CTRL_GPU_ECC_AGGREGATE_DOUBLE_BIT_ERRORS: Final = 332
NV_CTRL_GPU_ECC_RESET_ERROR_STATUS: Final = 333
NV_CTRL_GPU_ECC_RESET_ERROR_STATUS_VOLATILE: Final = 0x00000001
NV_CTRL_GPU_ECC_RESET_ERROR_STATUS_AGGREGATE: Final = 0x00000002
NV_CTRL_GPU_POWER_MIZER_MODE: Final = 334
NV_CTRL_GPU_POWER_MIZER_MODE_ADAPTIVE: Final = 0
NV_CTRL_GPU_POWER_MIZER_MODE_PREFER_MAXIMUM_PERFORMANCE: Final = 1
NV_CTRL_GPU_POWER_MIZER_MODE_AUTO: Final = 2
NV_CTRL_GPU_POWER_MIZER_MODE_PREFER_CONSISTENT_PERFORMANCE: Final = 3
NV_CTRL_GVI_SYNC_OUTPUT_FORMAT: Final = 335
NV_CTRL_GVI_MAX_CHANNELS_PER_JACK: Final = 336
NV_CTRL_GVI_MAX_STREAMS: Final = 337
NV_CTRL_GVI_NUM_CAPTURE_SURFACES: Final = 338
NV_CTRL_OVERSCAN_COMPENSATION: Final = 339
NV_CTRL_GPU_PCIE_GENERATION: Final = 341
NV_CTRL_GPU_PCIE_GENERATION1: Final = 0x00000001
NV_CTRL_GPU_PCIE_GENERATION2: Final = 0x00000002
NV_CTRL_GPU_PCIE_GENERATION3: Final = 0x00000003
NV_CTRL_GVI_BOUND_GPU: Final = 342
NV_CTRL_GVIO_REQUESTED_VIDEO_FORMAT3: Final = 343
NV_CTRL_ACCELERATE_TRAPEZOIDS: Final = 344
NV_CTRL_ACCELERATE_TRAPEZOIDS_DISABLE: Final = 0
NV_CTRL_ACCELERATE_TRAPEZOIDS_ENABLE: Final = 1
NV_CTRL_GPU_CORES: Final = 345
NV_CTRL_GPU_MEMORY_BUS_WIDTH: Final = 346
NV_CTRL_GVI_TEST_MODE: Final = 347
NV_CTRL_GVI_TEST_MODE_DISABLE: Final = 0
NV_CTRL_GVI_TEST_MODE_ENABLE: Final = 1
NV_CTRL_COLOR_SPACE: Final = 348
NV_CTRL_COLOR_SPACE_RGB: Final = 0
NV_CTRL_COLOR_SPACE_YCbCr422: Final = 1
NV_CTRL_COLOR_SPACE_YCbCr444: Final = 2
NV_CTRL_COLOR_RANGE: Final = 349
NV_CTRL_COLOR_RANGE_FULL: Final = 0
NV_CTRL_COLOR_RANGE_LIMITED: Final = 1
NV_CTRL_GPU_SCALING_DEFAULT_TARGET: Final = 350
NV_CTRL_GPU_SCALING_DEFAULT_METHOD: Final = 351
NV_CTRL_DITHERING_MODE: Final = 352
NV_CTRL_DITHERING_MODE_AUTO: Final = 0
NV_CTRL_DITHERING_MODE_DYNAMIC_2X2: Final = 1
NV_CTRL_DITHERING_MODE_STATIC_2X2: Final = 2
NV_CTRL_DITHERING_MODE_TEMPORAL: Final = 3
NV_CTRL_CURRENT_DITHERING: Final = 353
NV_CTRL_CURRENT_DITHERING_DISABLED: Final = 0
NV_CTRL_CURRENT_DITHERING_ENABLED: Final = 1
NV_CTRL_CURRENT_DITHERING_MODE: Final = 354
NV_CTRL_CURRENT_DITHERING_MODE_NONE: Final = 0
NV_CTRL_CURRENT_DITHERING_MODE_DYNAMIC_2X2: Final = 1
NV_CTRL_CURRENT_DITHERING_MODE_STATIC_2X2: Final = 2
NV_CTRL_CURRENT_DITHERING_MODE_TEMPORAL: Final = 3
NV_CTRL_THERMAL_SENSOR_READING: Final = 355
NV_CTRL_THERMAL_SENSOR_PROVIDER: Final = 356
NV_CTRL_THERMAL_SENSOR_PROVIDER_NONE: Final = 0
NV_CTRL_THERMAL_SENSOR_PROVIDER_GPU_INTERNAL: Final = 1
NV_CTRL_THERMAL_SENSOR_PROVIDER_ADM1032: Final = 2
NV_CTRL_THERMAL_SENSOR_PROVIDER_ADT7461: Final = 3
NV_CTRL_THERMAL_SENSOR_PROVIDER_MAX6649: Final = 4
NV_CTRL_THERMAL_SENSOR_PROVIDER_MAX1617: Final = 5
NV_CTRL_THERMAL_SENSOR_PROVIDER_LM99: Final = 6
NV_CTRL_THERMAL_SENSOR_PROVIDER_LM89: Final = 7
NV_CTRL_THERMAL_SENSOR_PROVIDER_LM64: Final = 8
NV_CTRL_THERMAL_SENSOR_PROVIDER_G781: Final = 9
NV_CTRL_THERMAL_SENSOR_PROVIDER_ADT7473: Final = 10
NV_CTRL_THERMAL_SENSOR_PROVIDER_SBMAX6649: Final = 11
NV_CTRL_THERMAL_SENSOR_PROVIDER_VBIOSEVT: Final = 12
NV_CTRL_THERMAL_SENSOR_PROVIDER_OS: Final = 13
NV_CTRL_THERMAL_SENSOR_PROVIDER_UNKNOWN: Final = 0xFFFFFFFF
NV_CTRL_THERMAL_SENSOR_TARGET: Final = 357
NV_CTRL_THERMAL_SENSOR_TARGET_NONE: Final = 0
NV_CTRL_THERMAL_SENSOR_TARGET_GPU: Final = 1
NV_CTRL_THERMAL_SENSOR_TARGET_MEMORY: Final = 2
NV_CTRL_THERMAL_SENSOR_TARGET_POWER_SUPPLY: Final = 4
NV_CTRL_THERMAL_SENSOR_TARGET_BOARD: Final = 8
NV_CTRL_THERMAL_SENSOR_TARGET_UNKNOWN: Final = 0xFFFFFFFF
NV_CTRL_SHOW_MULTIGPU_VISUAL_INDICATOR: Final = 358
NV_CTRL_SHOW_MULTIGPU_VISUAL_INDICATOR_FALSE: Final = 0
NV_CTRL_SHOW_MULTIGPU_VISUAL_INDICATOR_TRUE: Final = 1
NV_CTRL_GPU_CURRENT_PROCESSOR_CLOCK_FREQS: Final = 359
NV_CTRL_GVIO_VIDEO_FORMAT_FLAGS: Final = 360
NV_CTRL_GVIO_VIDEO_FORMAT_FLAGS_NONE: Final = 0x00000000
NV_CTRL_GVIO_VIDEO_FORMAT_FLAGS_INTERLACED: Final = 0x00000001
NV_CTRL_GVIO_VIDEO_FORMAT_FLAGS_PROGRESSIVE: Final = 0x00000002
NV_CTRL_GVIO_VIDEO_FORMAT_FLAGS_PSF: Final = 0x00000004
NV_CTRL_GVIO_VIDEO_FORMAT_FLAGS_3G_LEVEL_A: Final = 0x00000008
NV_CTRL_GVIO_VIDEO_FORMAT_FLAGS_3G_LEVEL_B: Final = 0x00000010
NV_CTRL_GVIO_VIDEO_FORMAT_FLAGS_3G: Final = 24
NV_CTRL_GVIO_VIDEO_FORMAT_FLAGS_3G_1080P_NO_12BPC: Final = 0x00000020
NV_CTRL_GPU_PCIE_MAX_LINK_SPEED: Final = 361
NV_CTRL_3D_VISION_PRO_RESET_TRANSCEIVER_TO_FACTORY_SETTINGS: Final = 363
NV_CTRL_3D_VISION_PRO_TRANSCEIVER_CHANNEL: Final = 364
NV_CTRL_3D_VISION_PRO_TRANSCEIVER_MODE: Final = 365
NV_CTRL_3D_VISION_PRO_TRANSCEIVER_MODE_INVALID: Final = 0
NV_CTRL_3D_VISION_PRO_TRANSCEIVER_MODE_LOW_RANGE: Final = 1
NV_CTRL_3D_VISION_PRO_TRANSCEIVER_MODE_MEDIUM_RANGE: Final = 2
NV_CTRL_3D_VISION_PRO_TRANSCEIVER_MODE_HIGH_RANGE: Final = 3
NV_CTRL_3D_VISION_PRO_TRANSCEIVER_MODE_COUNT: Final = 4
NV_CTRL_SYNCHRONOUS_PALETTE_UPDATES: Final = 367
NV_CTRL_SYNCHRONOUS_PALETTE_UPDATES_DISABLE: Final = 0
NV_CTRL_SYNCHRONOUS_PALETTE_UPDATES_ENABLE: Final = 1
NV_CTRL_DITHERING_DEPTH: Final = 368
NV_CTRL_DITHERING_DEPTH_AUTO: Final = 0
NV_CTRL_DITHERING_DEPTH_6_BITS: Final = 1
NV_CTRL_DITHERING_DEPTH_8_BITS: Final = 2
NV_CTRL_CURRENT_DITHERING_DEPTH: Final = 369
NV_CTRL_CURRENT_DITHERING_DEPTH_NONE: Final = 0
NV_CTRL_CURRENT_DITHERING_DEPTH_6_BITS: Final = 1
NV_CTRL_CURRENT_DITHERING_DEPTH_8_BITS: Final = 2
NV_CTRL_3D_VISION_PRO_TRANSCEIVER_CHANNEL_FREQUENCY: Final = 370
NV_CTRL_3D_VISION_PRO_TRANSCEIVER_CHANNEL_QUALITY: Final = 371
NV_CTRL_3D_VISION_PRO_TRANSCEIVER_CHANNEL_COUNT: Final = 372
NV_CTRL_3D_VISION_PRO_PAIR_GLASSES: Final = 373
NV_CTRL_3D_VISION_PRO_PAIR_GLASSES_STOP: Final = 0
NV_CTRL_3D_VISION_PRO_PAIR_GLASSES_BEACON: Final = 0xFFFFFFFF
NV_CTRL_3D_VISION_PRO_UNPAIR_GLASSES: Final = 374
NV_CTRL_3D_VISION_PRO_DISCOVER_GLASSES: Final = 375
NV_CTRL_3D_VISION_PRO_IDENTIFY_GLASSES: Final = 376
NV_CTRL_3D_VISION_PRO_GLASSES_SYNC_CYCLE: Final = 378
NV_CTRL_3D_VISION_PRO_GLASSES_MISSED_SYNC_CYCLES: Final = 379
NV_CTRL_3D_VISION_PRO_GLASSES_BATTERY_LEVEL: Final = 380
NV_CTRL_GVO_ANC_PARITY_COMPUTATION: Final = 381
NV_CTRL_GVO_ANC_PARITY_COMPUTATION_AUTO: Final = 0
NV_CTRL_GVO_ANC_PARITY_COMPUTATION_ON: Final = 1
NV_CTRL_GVO_ANC_PARITY_COMPUTATION_OFF: Final = 2
NV_CTRL_3D_VISION_PRO_GLASSES_PAIR_EVENT: Final = 382
NV_CTRL_3D_VISION_PRO_GLASSES_UNPAIR_EVENT: Final = 383
NV_CTRL_GPU_PCIE_CURRENT_LINK_WIDTH: Final = 384
NV_CTRL_GPU_PCIE_CURRENT_LINK_SPEED: Final = 385
NV_CTRL_GVO_AUDIO_BLANKING: Final = 386
NV_CTRL_GVO_AUDIO_BLANKING_DISABLE: Final = 0
NV_CTRL_GVO_AUDIO_BLANKING_ENABLE: Final = 1
NV_CTRL_CURRENT_METAMODE_ID: Final = 387
NV_CTRL_DISPLAY_ENABLED: Final = 388
NV_CTRL_DISPLAY_ENABLED_TRUE: Final = 1
NV_CTRL_DISPLAY_ENABLED_FALSE: Final = 0
NV_CTRL_FRAMELOCK_INCOMING_HOUSE_SYNC_RATE: Final = 389
NV_CTRL_FXAA: Final = 390
NV_CTRL_FXAA_DISABLE: Final = 0
NV_CTRL_FXAA_ENABLE: Final = 1
NV_CTRL_DISPLAY_RANDR_OUTPUT_ID: Final = 391
NV_CTRL_FRAMELOCK_DISPLAY_CONFIG: Final = 392
NV_CTRL_FRAMELOCK_DISPLAY_CONFIG_DISABLED: Final = 0
NV_CTRL_FRAMELOCK_DISPLAY_CONFIG_CLIENT: Final = 1
NV_CTRL_FRAMELOCK_DISPLAY_CONFIG_SERVER: Final = 2
NV_CTRL_TOTAL_DEDICATED_GPU_MEMORY: Final = 393
NV_CTRL_USED_DEDICATED_GPU_MEMORY: Final = 394
NV_CTRL_GPU_DOUBLE_PRECISION_BOOST_IMMEDIATE: Final = 395
NV_CTRL_GPU_DOUBLE_PRECISION_BOOST_IMMEDIATE_DISABLED: Final = 0
NV_CTRL_GPU_DOUBLE_PRECISION_BOOST_IMMEDIATE_ENABLED: Final = 1
NV_CTRL_GPU_DOUBLE_PRECISION_BOOST_REBOOT: Final = 396
NV_CTRL_GPU_DOUBLE_PRECISION_BOOST_REBOOT_DISABLED: Final = 0
NV_CTRL_GPU_DOUBLE_PRECISION_BOOST_REBOOT_ENALED: Final = 1
NV_CTRL_DPY_HDMI_3D: Final = 397
NV_CTRL_DPY_HDMI_3D_DISABLED: Final = 0
NV_CTRL_DPY_HDMI_3D_ENABLED: Final = 1
NV_CTRL_BASE_MOSAIC: Final = 398
NV_CTRL_BASE_MOSAIC_DISABLED: Final = 0
NV_CTRL_BASE_MOSAIC_FULL: Final = 1
NV_CTRL_BASE_MOSAIC_LIMITED: Final = 2
NV_CTRL_MULTIGPU_MASTER_POSSIBLE: Final = 399
NV_CTRL_MULTIGPU_MASTER_POSSIBLE_FALSE: Final = 0
NV_CTRL_MULTIGPU_MASTER_POSSIBLE_TRUE: Final = 1
NV_CTRL_GPU_POWER_MIZER_DEFAULT_MODE: Final = 400
NV_CTRL_XV_SYNC_TO_DISPLAY_ID: Final = 401
NV_CTRL_XV_SYNC_TO_DISPLAY_ID_AUTO: Final = 0xFFFFFFFF
NV_CTRL_BACKLIGHT_BRIGHTNESS: Final = 402
NV_CTRL_GPU_LOGO_BRIGHTNESS: Final = 403
NV_CTRL_GPU_SLI_LOGO_BRIGHTNESS: Final = 404
NV_CTRL_THERMAL_COOLER_SPEED: Final = 405
NV_CTRL_PALETTE_UPDATE_EVENT: Final = 406
NV_CTRL_VIDEO_ENCODER_UTILIZATION: Final = 407
NV_CTRL_GSYNC_ALLOWED: Final = 408
NV_CTRL_GSYNC_ALLOWED_FALSE: Final = 0
NV_CTRL_GSYNC_ALLOWED_TRUE: Final = 1
NV_CTRL_GPU_NVCLOCK_OFFSET: Final = 409
NV_CTRL_GPU_MEM_TRANSFER_RATE_OFFSET: Final = 410
NV_CTRL_VIDEO_DECODER_UTILIZATION: Final = 411
NV_CTRL_GPU_OVER_VOLTAGE_OFFSET: Final = 412
NV_CTRL_GPU_CURRENT_CORE_VOLTAGE: Final = 413
NV_CTRL_CURRENT_COLOR_SPACE: Final = 414
NV_CTRL_CURRENT_COLOR_SPACE_RGB: Final = 0
NV_CTRL_CURRENT_COLOR_SPACE_YCbCr422: Final = 1
NV_CTRL_CURRENT_COLOR_SPACE_YCbCr444: Final = 2
NV_CTRL_CURRENT_COLOR_SPACE_YCbCr420: Final = 3
NV_CTRL_CURRENT_COLOR_RANGE: Final = 415
NV_CTRL_CURRENT_COLOR_RANGE_FULL: Final = 0
NV_CTRL_CURRENT_COLOR_RANGE_LIMITED: Final = 1
NV_CTRL_SHOW_GSYNC_VISUAL_INDICATOR: Final = 416
NV_CTRL_SHOW_GSYNC_VISUAL_INDICATOR_FALSE: Final = 0
NV_CTRL_SHOW_GSYNC_VISUAL_INDICATOR_TRUE: Final = 1
NV_CTRL_THERMAL_COOLER_CURRENT_LEVEL: Final = 417
NV_CTRL_STEREO_SWAP_MODE: Final = 418
NV_CTRL_STEREO_SWAP_MODE_APPLICATION_CONTROL: Final = 0
NV_CTRL_STEREO_SWAP_MODE_PER_EYE: Final = 1
NV_CTRL_STEREO_SWAP_MODE_PER_EYE_PAIR: Final = 2
NV_CTRL_CURRENT_XV_SYNC_TO_DISPLAY_ID: Final = 419
NV_CTRL_GPU_FRAMELOCK_FIRMWARE_UNSUPPORTED: Final = 420
NV_CTRL_GPU_FRAMELOCK_FIRMWARE_UNSUPPORTED_FALSE: Final = 0
NV_CTRL_GPU_FRAMELOCK_FIRMWARE_UNSUPPORTED_TRUE: Final = 1
NV_CTRL_DISPLAYPORT_CONNECTOR_TYPE: Final = 421
NV_CTRL_DISPLAYPORT_CONNECTOR_TYPE_UNKNOWN: Final = 0
NV_CTRL_DISPLAYPORT_CONNECTOR_TYPE_DISPLAYPORT: Final = 1
NV_CTRL_DISPLAYPORT_CONNECTOR_TYPE_HDMI: Final = 2
NV_CTRL_DISPLAYPORT_CONNECTOR_TYPE_DVI: Final = 3
NV_CTRL_DISPLAYPORT_CONNECTOR_TYPE_VGA: Final = 4
NV_CTRL_DISPLAYPORT_IS_MULTISTREAM: Final = 422
NV_CTRL_DISPLAYPORT_SINK_IS_AUDIO_CAPABLE: Final = 423
NV_CTRL_GPU_NVCLOCK_OFFSET_ALL_PERFORMANCE_LEVELS: Final = 424
NV_CTRL_GPU_MEM_TRANSFER_RATE_OFFSET_ALL_PERFORMANCE_LEVELS: Final = 425
NV_CTRL_FRAMELOCK_FIRMWARE_VERSION: Final = 426
NV_CTRL_FRAMELOCK_FIRMWARE_MINOR_VERSION: Final = 427
NV_CTRL_SHOW_GRAPHICS_VISUAL_INDICATOR: Final = 428
NV_CTRL_SHOW_GRAPHICS_VISUAL_INDICATOR_FALSE: Final = 0
NV_CTRL_SHOW_GRAPHICS_VISUAL_INDICATOR_TRUE: Final = 1
NV_CTRL_LAST_ATTRIBUTE: Final = NV_CTRL_SHOW_GRAPHICS_VISUAL_INDICATOR
NV_CTRL_STRING_PRODUCT_NAME: Final = 0
NV_CTRL_STRING_VBIOS_VERSION: Final = 1
NV_CTRL_STRING_NVIDIA_DRIVER_VERSION: Final = 3
NV_CTRL_STRING_DISPLAY_DEVICE_NAME: Final = 4
NV_CTRL_STRING_TV_ENCODER_NAME: Final = 5
NV_CTRL_STRING_GVIO_FIRMWARE_VERSION: Final = 8
NV_CTRL_STRING_GVO_FIRMWARE_VERSION: Final = 8
NV_CTRL_STRING_CURRENT_MODELINE: Final = 9
NV_CTRL_STRING_ADD_MODELINE: Final = 10
NV_CTRL_STRING_DELETE_MODELINE: Final = 11
NV_CTRL_STRING_CURRENT_METAMODE: Final = 12
NV_CTRL_STRING_CURRENT_METAMODE_VERSION_1: Final = NV_CTRL_STRING_CURRENT_METAMODE
NV_CTRL_STRING_ADD_METAMODE: Final = 13
NV_CTRL_STRING_DELETE_METAMODE: Final = 14
NV_CTRL_STRING_VCSC_PRODUCT_NAME: Final = 15
NV_CTRL_STRING_VCSC_PRODUCT_ID: Final = 16
NV_CTRL_STRING_VCSC_SERIAL_NUMBER: Final = 17
NV_CTRL_STRING_VCSC_BUILD_DATE: Final = 18
NV_CTRL_STRING_VCSC_FIRMWARE_VERSION: Final = 19
NV_CTRL_STRING_VCSC_FIRMWARE_REVISION: Final = 20
NV_CTRL_STRING_VCSC_HARDWARE_VERSION: Final = 21
NV_CTRL_STRING_VCSC_HARDWARE_REVISION: Final = 22
NV_CTRL_STRING_MOVE_METAMODE: Final = 23
NV_CTRL_STRING_VALID_HORIZ_SYNC_RANGES: Final = 24
NV_CTRL_STRING_VALID_VERT_REFRESH_RANGES: Final = 25
NV_CTRL_STRING_SCREEN_RECTANGLE: Final = 26
NV_CTRL_STRING_XINERAMA_SCREEN_INFO: Final = 26
NV_CTRL_STRING_NVIDIA_XINERAMA_INFO_ORDER: Final = 27
NV_CTRL_STRING_TWINVIEW_XINERAMA_INFO_ORDER: Final = NV_CTRL_STRING_NVIDIA_XINERAMA_INFO_ORDER
NV_CTRL_STRING_SLI_MODE: Final = 28
NV_CTRL_STRING_PERFORMANCE_MODES: Final = 29
NV_CTRL_STRING_VCSC_FAN_STATUS: Final = 30
NV_CTRL_STRING_VCSC_TEMPERATURES: Final = 31
NV_CTRL_STRING_VCSC_PSU_INFO: Final = 32
NV_CTRL_STRING_GVIO_VIDEO_FORMAT_NAME: Final = 33
NV_CTRL_STRING_GVO_VIDEO_FORMAT_NAME: Final = 33
NV_CTRL_STRING_GPU_CURRENT_CLOCK_FREQS: Final = 34
NV_CTRL_STRING_3D_VISION_PRO_TRANSCEIVER_HARDWARE_REVISION: Final = 35
NV_CTRL_STRING_3D_VISION_PRO_TRANSCEIVER_FIRMWARE_VERSION_A: Final = 36
NV_CTRL_STRING_3D_VISION_PRO_TRANSCEIVER_FIRMWARE_DATE_A: Final = 37
NV_CTRL_STRING_3D_VISION_PRO_TRANSCEIVER_FIRMWARE_VERSION_B: Final = 38
NV_CTRL_STRING_3D_VISION_PRO_TRANSCEIVER_FIRMWARE_DATE_B: Final = 39
NV_CTRL_STRING_3D_VISION_PRO_TRANSCEIVER_ADDRESS: Final = 40
NV_CTRL_STRING_3D_VISION_PRO_GLASSES_FIRMWARE_VERSION_A: Final = 41
NV_CTRL_STRING_3D_VISION_PRO_GLASSES_FIRMWARE_DATE_A: Final = 42
NV_CTRL_STRING_3D_VISION_PRO_GLASSES_ADDRESS: Final = 43
NV_CTRL_STRING_3D_VISION_PRO_GLASSES_NAME: Final = 44
NV_CTRL_STRING_CURRENT_METAMODE_VERSION_2: Final = 45
NV_CTRL_STRING_DISPLAY_NAME_TYPE_BASENAME: Final = 46
NV_CTRL_STRING_DISPLAY_NAME_TYPE_ID: Final = 47
NV_CTRL_STRING_DISPLAY_NAME_DP_GUID: Final = 48
NV_CTRL_STRING_DISPLAY_NAME_EDID_HASH: Final = 49
NV_CTRL_STRING_DISPLAY_NAME_TARGET_INDEX: Final = 50
NV_CTRL_STRING_DISPLAY_NAME_RANDR: Final = 51
NV_CTRL_STRING_GPU_UUID: Final = 52
NV_CTRL_STRING_GPU_UTILIZATION: Final = 53
NV_CTRL_STRING_MULTIGPU_MODE: Final = 54
NV_CTRL_STRING_PRIME_OUTPUTS_DATA: Final = 55
NV_CTRL_STRING_LAST_ATTRIBUTE: Final = NV_CTRL_STRING_PRIME_OUTPUTS_DATA
NV_CTRL_BINARY_DATA_EDID: Final = 0
NV_CTRL_BINARY_DATA_MODELINES: Final = 1
NV_CTRL_BINARY_DATA_METAMODES: Final = 2
NV_CTRL_BINARY_DATA_METAMODES_VERSION_1: Final = NV_CTRL_BINARY_DATA_METAMODES
NV_CTRL_BINARY_DATA_XSCREENS_USING_GPU: Final = 3
NV_CTRL_BINARY_DATA_GPUS_USED_BY_XSCREEN: Final = 4
NV_CTRL_BINARY_DATA_GPUS_USING_FRAMELOCK: Final = 5
NV_CTRL_BINARY_DATA_DISPLAY_VIEWPORT: Final = 6
NV_CTRL_BINARY_DATA_FRAMELOCKS_USED_BY_GPU: Final = 7
NV_CTRL_BINARY_DATA_GPUS_USING_VCSC: Final = 8
NV_CTRL_BINARY_DATA_VCSCS_USED_BY_GPU: Final = 9
NV_CTRL_BINARY_DATA_COOLERS_USED_BY_GPU: Final = 10
NV_CTRL_BINARY_DATA_GPUS_USED_BY_LOGICAL_XSCREEN: Final = 11
NV_CTRL_BINARY_DATA_THERMAL_SENSORS_USED_BY_GPU: Final = 12
NV_CTRL_BINARY_DATA_GLASSES_PAIRED_TO_3D_VISION_PRO_TRANSCEIVER: Final = 13
NV_CTRL_BINARY_DATA_DISPLAY_TARGETS: Final = 14
NV_CTRL_BINARY_DATA_DISPLAYS_CONNECTED_TO_GPU: Final = 15
NV_CTRL_BINARY_DATA_METAMODES_VERSION_2: Final = 16
NV_CTRL_BINARY_DATA_DISPLAYS_ENABLED_ON_XSCREEN: Final = 17
NV_CTRL_BINARY_DATA_DISPLAYS_ASSIGNED_TO_XSCREEN: Final = 18
NV_CTRL_BINARY_DATA_GPU_FLAGS: Final = 19
NV_CTRL_BINARY_DATA_GPU_FLAGS_STEREO_DISPLAY_TRANSFORM_EXCLUSIVE: Final = 0
NV_CTRL_BINARY_DATA_GPU_FLAGS_OVERLAY_DISPLAY_TRANSFORM_EXCLUSIVE: Final = 1
NV_CTRL_BINARY_DATA_GPU_FLAGS_DEPTH_8_DISPLAY_TRANSFORM_EXCLUSIVE: Final = 2
NV_CTRL_BINARY_DATA_DISPLAYS_ON_GPU: Final = 20
NV_CTRL_BINARY_DATA_LAST_ATTRIBUTE: Final = NV_CTRL_BINARY_DATA_DISPLAYS_ON_GPU
NV_CTRL_STRING_OPERATION_ADD_METAMODE: Final = 0
NV_CTRL_STRING_OPERATION_GTF_MODELINE: Final = 1
NV_CTRL_STRING_OPERATION_CVT_MODELINE: Final = 2
NV_CTRL_STRING_OPERATION_BUILD_MODEPOOL: Final = 3
NV_CTRL_STRING_OPERATION_GVI_CONFIGURE_STREAMS: Final = 4
NV_CTRL_STRING_OPERATION_PARSE_METAMODE: Final = 5
NV_CTRL_STRING_OPERATION_LAST_ATTRIBUTE: Final = NV_CTRL_STRING_OPERATION_PARSE_METAMODE
X_nvCtrlQueryExtension: Final = 0
X_nvCtrlQueryAttribute: Final = 2
X_nvCtrlQueryStringAttribute: Final = 4
X_nvCtrlQueryValidAttributeValues: Final = 5
X_nvCtrlSetStringAttribute: Final = 9
X_nvCtrlSetAttributeAndGetStatus: Final = 19
X_nvCtrlQueryBinaryData: Final = 20
X_nvCtrlQueryTargetCount: Final = 24
X_nvCtrlStringOperation: Final = 25
ATTRIBUTE_TYPE_UNKNOWN: Final = 0
ATTRIBUTE_TYPE_INTEGER: Final = 1
ATTRIBUTE_TYPE_BITMASK: Final = 2
ATTRIBUTE_TYPE_BOOL: Final = 3
ATTRIBUTE_TYPE_RANGE: Final = 4
ATTRIBUTE_TYPE_INT_BITS: Final = 5
ATTRIBUTE_TYPE_READ: Final = 0x01
ATTRIBUTE_TYPE_WRITE: Final = 0x02
ATTRIBUTE_TYPE_DISPLAY: Final = 0x04
ATTRIBUTE_TYPE_GPU: Final = 0x08
ATTRIBUTE_TYPE_FRAMELOCK: Final = 0x10
ATTRIBUTE_TYPE_X_SCREEN: Final = 0x20
ATTRIBUTE_TYPE_XINERAMA: Final = 0x40
ATTRIBUTE_TYPE_VCSC: Final = 0x80
NV_CTRL_TARGET_TYPE_X_SCREEN: Final = 0
NV_CTRL_TARGET_TYPE_GPU: Final = 1
NV_CTRL_TARGET_TYPE_FRAMELOCK: Final = 2
NV_CTRL_TARGET_TYPE_VCSC: Final = 3
NV_CTRL_TARGET_TYPE_GVI: Final = 4
NV_CTRL_TARGET_TYPE_COOLER: Final = 5
NV_CTRL_TARGET_TYPE_THERMAL_SENSOR: Final = 6
NV_CTRL_TARGET_TYPE_3D_VISION_PRO_TRANSCEIVER: Final = 7
NV_CTRL_TARGET_TYPE_DISPLAY: Final = 8

class Target:
    def id(self) -> int: ...
    def type(self) -> int: ...

class Gpu(Target):
    def __init__(self, ngpu: int = 0) -> None: ...

class Screen(Target):
    def __init__(self, nscr: int = 0) -> None: ...

class Cooler(Target):
    def __init__(self, nfan: int = 0) -> None: ...

class NVCtrlQueryTargetCountReplyRequest(rq.ReplyRequest): ...
class NVCtrlQueryAttributeReplyRequest(rq.ReplyRequest): ...
class NVCtrlSetAttributeAndGetStatusReplyRequest(rq.ReplyRequest): ...
class NVCtrlQueryStringAttributeReplyRequest(rq.ReplyRequest): ...
class NVCtrlQueryValidAttributeValuesReplyRequest(rq.ReplyRequest): ...
class NVCtrlQueryBinaryDataReplyRequest(rq.ReplyRequest): ...
class NVCtrlQueryListCard32ReplyRequest(rq.ReplyRequest): ...
