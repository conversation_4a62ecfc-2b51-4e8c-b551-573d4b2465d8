from _typeshed import Incomplete

from authlib.oauth2.rfc6750 import BearerTokenGenerator

class JWTBearerTokenGenerator(BearerTokenGenerator):
    issuer: Incomplete
    alg: Incomplete
    def __init__(self, issuer, alg: str = "RS256", refresh_token_generator=None, expires_generator=None) -> None: ...
    def get_jwks(self) -> None: ...
    def get_extra_claims(self, client, grant_type, user, scope): ...
    def get_audiences(self, client, user, scope) -> str | list[str]: ...
    def get_acr(self, user) -> str | None: ...
    def get_auth_time(self, user) -> int | None: ...
    def get_amr(self, user) -> list[str] | None: ...
    def get_jti(self, client, grant_type, user, scope) -> str: ...
    # Override seems safe, but my<PERSON> doesn't like that it's a callabe protocol in the base
    def access_token_generator(self, client, grant_type, user, scope): ...  # type: ignore[override]
