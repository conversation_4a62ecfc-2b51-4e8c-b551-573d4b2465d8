from _typeshed import Incomplete
from typing import Any

from google.cloud.ndb import tasklets as tasklets

class ContextCache:
    def get_and_validate(self, key): ...

class _GlobalCacheBatch:
    def full(self): ...
    def idle_callback(self) -> None: ...
    def done_callback(self, cache_call) -> None: ...
    def make_call(self) -> None: ...
    def future_info(self, key) -> None: ...

global_get: Any

class _GlobalCacheGetBatch(_GlobalCacheBatch):
    todo: Any
    keys: Any
    def __init__(self, ignore_options) -> None: ...
    def add(self, key): ...
    def done_callback(self, cache_call) -> None: ...
    def make_call(self): ...
    def future_info(self, key): ...

def global_set(key, value, expires: Incomplete | None = ..., read: bool = ...): ...

class _GlobalCacheSetBatch(_GlobalCacheBatch):
    expires: Any
    todo: object
    futures: object
    def __init__(self, options) -> None: ...
    def done_callback(self, cache_call) -> None: ...
    def add(self, key, value): ...
    def make_call(self): ...
    def future_info(self, key, value): ...  # type:ignore[override]

class _GlobalCacheSetIfNotExistsBatch(_GlobalCacheSetBatch):
    def add(self, key, value): ...
    def make_call(self): ...
    def future_info(self, key, value): ...  # type:ignore[override]

global_delete: Any

class _GlobalCacheDeleteBatch(_GlobalCacheBatch):
    keys: Any
    futures: Any
    def __init__(self, ignore_options) -> None: ...
    def add(self, key): ...
    def make_call(self): ...
    def future_info(self, key): ...

global_watch: Any

class _GlobalCacheWatchBatch(_GlobalCacheDeleteBatch):
    def make_call(self): ...
    def future_info(self, key, value): ...  # type:ignore[override]

def global_unwatch(key): ...

class _GlobalCacheUnwatchBatch(_GlobalCacheDeleteBatch):
    def make_call(self): ...
    def future_info(self, key): ...

global_compare_and_swap: Any

class _GlobalCacheCompareAndSwapBatch(_GlobalCacheSetBatch):
    def make_call(self): ...
    def future_info(self, key, value): ...  # type:ignore[override]

def is_locked_value(value): ...
def global_cache_key(key): ...
