from _typeshed import Incomplete
from collections.abc import Sequence
from math import cos as cos, pi as pi, sin as sin
from typing import IO, Final

from reportlab.graphics.renderbase import Renderer
from reportlab.graphics.shapes import Drawing
from reportlab.pdfgen.canvas import Canvas

AREA_STYLES: Final[Sequence[str]]
LINE_STYLES: Final[Sequence[str]]
TEXT_STYLES: Final[Sequence[str]]
EXTRA_STROKE_STYLES: Final[Sequence[str]]
EXTRA_FILL_STYLES: Final[Sequence[str]]

def drawToString(d: Drawing, showBoundary=0, **kwds) -> str: ...
def drawToFile(d: Drawing, fn: str | IO[str], showBoundary=0, **kwds) -> None: ...
def draw(drawing: Drawing, canvas: Canvas, x: float = 0, y: float = 0, showBoundary=0) -> None: ...
def transformNode(doc, newTag, node=None, **attrDict): ...

class EncodedWriter(list[Incomplete]):
    BOMS: Incomplete
    encoding: Incomplete
    def __init__(self, encoding, bom: bool = False) -> None: ...
    def write(self, u) -> None: ...
    def getvalue(self): ...

def py_fp_str(*args): ...

class SVGCanvas:
    verbose: Incomplete
    encoding: Incomplete
    bom: Incomplete
    fontHacks: Incomplete
    extraXmlDecl: Incomplete
    code: Incomplete
    style: Incomplete
    path: str
    fp_str: Incomplete
    cfp_str: Incomplete
    doc: Incomplete
    svg: Incomplete
    groupTree: Incomplete
    scaleTree: Incomplete
    currGroup: Incomplete
    def __init__(self, size=(300, 300), encoding: str = "utf-8", verbose: int = 0, bom: bool = False, **kwds) -> None: ...
    def save(self, fn=None) -> None: ...
    def NOTUSED_stringWidth(self, s, font=None, fontSize=None): ...
    def setLineCap(self, v) -> None: ...
    def setLineJoin(self, v) -> None: ...
    def setDash(self, array=[], phase: int = 0) -> None: ...
    def setStrokeColor(self, color) -> None: ...
    def setFillColor(self, color) -> None: ...
    def setFillMode(self, v) -> None: ...
    def setLineWidth(self, width) -> None: ...
    def setFont(self, font, fontSize) -> None: ...
    def rect(self, x1, y1, x2, y2, rx: int = 8, ry: int = 8, link_info=None, **_svgAttrs) -> None: ...
    def roundRect(self, x1, y1, x2, y2, rx: int = 8, ry: int = 8, link_info=None, **_svgAttrs) -> None: ...
    def drawString(
        self, s, x, y, angle: int = 0, link_info=None, text_anchor: str = "left", textRenderMode: int = 0, **_svgAttrs
    ) -> None: ...
    def drawCentredString(
        self, s, x, y, angle: int = 0, text_anchor: str = "middle", link_info=None, textRenderMode: int = 0, **_svgAttrs
    ) -> None: ...
    def drawRightString(
        self, text, x, y, angle: int = 0, text_anchor: str = "end", link_info=None, textRenderMode: int = 0, **_svgAttrs
    ) -> None: ...
    def comment(self, data) -> None: ...
    def drawImage(self, image, x, y, width, height, embed: bool = True) -> None: ...
    def line(self, x1, y1, x2, y2) -> None: ...
    def ellipse(self, x1, y1, x2, y2, link_info=None) -> None: ...
    def circle(self, xc, yc, r, link_info=None) -> None: ...
    def drawCurve(self, x1, y1, x2, y2, x3, y3, x4, y4, closed: int = 0) -> None: ...
    def drawArc(self, x1, y1, x2, y2, startAng: int = 0, extent: int = 360, fromcenter: int = 0) -> None: ...
    def polygon(self, points, closed: int = 0, link_info=None) -> None: ...
    def lines(self, lineList, color=None, width=None) -> None: ...
    def polyLine(self, points) -> None: ...
    def startGroup(self, attrDict={"transform": ""}): ...
    def endGroup(self, currGroup) -> None: ...
    def transform(self, a, b, c, d, e, f) -> None: ...
    def translate(self, x, y) -> None: ...
    def scale(self, sx, sy) -> None: ...
    def moveTo(self, x, y) -> None: ...
    def lineTo(self, x, y) -> None: ...
    def curveTo(self, x1, y1, x2, y2, x3, y3) -> None: ...
    def closePath(self) -> None: ...
    def saveState(self) -> None: ...
    def restoreState(self) -> None: ...

class _SVGRenderer(Renderer):
    verbose: int
    def __init__(self) -> None: ...
    def drawNode(self, node) -> None: ...
    def drawGroup(self, group) -> None: ...
    def drawRect(self, rect) -> None: ...
    def drawString(self, stringObj) -> None: ...
    def drawLine(self, line) -> None: ...
    def drawCircle(self, circle) -> None: ...
    def drawWedge(self, wedge) -> None: ...
    def drawPolyLine(self, p) -> None: ...
    def drawEllipse(self, ellipse) -> None: ...
    def drawPolygon(self, p) -> None: ...
    def drawPath(self, path, fillMode=0): ...
    def drawImage(self, image) -> None: ...
    def applyStateChanges(self, delta, newState) -> None: ...

def test(outDir: str = "out-svg") -> None: ...
