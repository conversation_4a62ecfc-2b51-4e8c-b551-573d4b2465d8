from typing import Final

DECIMAL: Final = 0
TINY: Final = 1
SHORT: Final = 2
LONG: Final = 3
FLOAT: Final = 4
DOUBLE: Final = 5
NULL: Final = 6
TIMESTAMP: Final = 7
LONGLONG: Final = 8
INT24: Final = 9
DATE: Final = 10
TIME: Final = 11
DATETIME: Final = 12
YEAR: Final = 13
NEWDATE: Final = 14
VARCHAR: Final = 15
BIT: Final = 16
JSON: Final = 245
NEWDECIMAL: Final = 246
ENUM: Final = 247
SET: Final = 248
TINY_BLOB: Final = 249
MEDIUM_BLOB: Final = 250
LONG_BLOB: Final = 251
BLOB: Final = 252
VAR_STRING: Final = 253
STRING: Final = 254
GEOMETRY: Final = 255
CHAR: Final = TINY
INTERVAL: Final = ENUM
