from typing import Any

class Server:
    def __init__(self, in_stream, out_stream, child_stream) -> None: ...
    def main_loop(self) -> None: ...
    def send_event(self, event: str, body: Any | None = None) -> None: ...  # body is an arbitrary object
    def send_event_later(self, event: str, body: Any | None = None) -> None: ...  # body is an arbitrary object
    def shutdown(self) -> None: ...

def pre_command_loop() -> None: ...
def run() -> None: ...

session_started: bool
