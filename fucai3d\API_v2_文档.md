# P2高级特征工程系统 - API v2 文档

## 📋 API概览

**API版本**: v2.0  
**基础URL**: `http://127.0.0.1:5000/api/v2/features`  
**协议**: HTTP/HTTPS  
**数据格式**: JSON  
**字符编码**: UTF-8

## 🚀 快速开始

### 启动API服务
```bash
cd fucai3d
python src/api/app.py
```

### 基础调用示例
```python
import requests

# 获取API信息
response = requests.get("http://127.0.0.1:5000/api/v2/features/info")
print(response.json())
```

## 📚 API端点详解

### 1. 获取单期高级特征

**端点**: `GET /advanced/<feature_type>/<issue>`

**描述**: 获取指定期号的高级特征数据

**参数**:
- `feature_type` (路径参数): 特征类型
  - `hundreds` - 百位特征
  - `tens` - 十位特征  
  - `units` - 个位特征
  - `sum` - 和值特征
  - `span` - 跨度特征
  - `common` - 通用特征
  - `all` - 所有特征
- `issue` (路径参数): 期号，格式如 `2025001`

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "issue": "2025001",
    "feature_type": "hundreds",
    "features": {
      "hundreds_frequency": 0.15,
      "hundreds_trend": 1.2,
      "hundreds_pattern": "ascending",
      "hundreds_correlation": 0.85
    },
    "feature_count": 4,
    "cache_stats": {
      "hit_rate": 0.85,
      "total_requests": 1250
    }
  },
  "performance": {
    "execution_time": 0.045,
    "timestamp": **********.89
  }
}
```

**错误响应**:
```json
{
  "status": "error",
  "message": "无效的期号格式",
  "error_type": "ValueError",
  "timestamp": **********.89
}
```

### 2. 批量获取特征

**端点**: `POST /batch`

**描述**: 批量获取多个期号的特征数据

**请求体**:
```json
{
  "issues": ["2025001", "2025002", "2025003"],
  "feature_type": "hundreds",
  "include_cache_stats": true
}
```

**参数说明**:
- `issues` (必需): 期号列表，最多100个
- `feature_type` (可选): 特征类型，默认 `all`
- `include_cache_stats` (可选): 是否包含缓存统计，默认 `false`

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "feature_type": "hundreds",
    "results": {
      "2025001": {
        "status": "success",
        "features": {
          "hundreds_frequency": 0.15,
          "hundreds_trend": 1.2
        },
        "feature_count": 2
      },
      "2025002": {
        "status": "success",
        "features": {
          "hundreds_frequency": 0.18,
          "hundreds_trend": 1.1
        },
        "feature_count": 2
      }
    },
    "summary": {
      "total_requested": 3,
      "success_count": 2,
      "error_count": 1,
      "success_rate": 0.667
    },
    "cache_stats": {
      "hit_rate": 0.75,
      "memory_hits": 150,
      "db_hits": 50
    }
  }
}
```

### 3. 特征重要性分析

**端点**: `POST /importance/<feature_type>`

**描述**: 分析指定特征类型的重要性

**请求体**:
```json
{
  "issues": ["2025001", "2025002", "2025003", "..."],
  "target_variable": "hundreds",
  "analysis_config": {
    "top_k_features": 20,
    "model_type": "auto"
  }
}
```

**参数说明**:
- `issues` (必需): 期号列表，至少10个
- `target_variable` (必需): 目标变量类型
- `analysis_config` (可选): 分析配置
  - `top_k_features`: 返回前K个重要特征，默认20
  - `model_type`: 模型类型 (`auto`, `classification`, `regression`)

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "feature_type": "hundreds",
    "target_variable": "hundreds",
    "analysis_summary": {
      "total_features": 45,
      "model_type": "RandomForestClassifier",
      "data_shape": [100, 45],
      "analysis_date": "2025-01-14T10:30:00"
    },
    "top_features": [
      ["hundreds_frequency", 0.156],
      ["hundreds_trend", 0.142],
      ["hundreds_pattern", 0.128],
      ["hundreds_correlation", 0.115],
      ["hundreds_variance", 0.098]
    ],
    "feature_statistics": {
      "max_importance": 0.156,
      "min_importance": 0.001,
      "mean_importance": 0.022,
      "std_importance": 0.035
    },
    "model_performance": {
      "test_accuracy": 0.75,
      "train_accuracy": 0.82,
      "test_precision": 0.73,
      "test_recall": 0.71,
      "test_f1": 0.72
    },
    "recommendations": [
      "建议优先使用5个高重要性特征",
      "建议考虑移除15个低重要性特征以简化模型"
    ]
  }
}
```

### 4. 缓存统计信息

**端点**: `GET /cache/stats`

**描述**: 获取系统缓存统计信息

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "feature_engineer_cache": {
      "hit_rate": 0.85,
      "total_requests": 1250,
      "memory_hits": 1000,
      "db_hits": 62,
      "misses": 188
    },
    "cache_optimizer": {
      "config": {
        "memory_size_limit": 1000,
        "db_cache_enabled": true,
        "cache_ttl": 3600
      },
      "stats": {
        "hit_rate": "82.50%",
        "memory_hit_rate": "75.20%",
        "db_hit_rate": "7.30%"
      },
      "size": {
        "memory_cache_size": 856,
        "db_cache_size": 2341,
        "memory_usage_rate": "85.60%"
      }
    },
    "timestamp": **********.89
  }
}
```

### 5. 清理缓存

**端点**: `POST /cache/clear`

**描述**: 清理系统缓存

**请求体**:
```json
{
  "cache_type": "all"
}
```

**参数说明**:
- `cache_type` (可选): 缓存类型
  - `all` - 清理所有缓存 (默认)
  - `memory` - 只清理内存缓存
  - `db` - 只清理数据库缓存

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "cache_type": "all",
    "cleared_caches": ["feature_engineer", "cache_optimizer"],
    "timestamp": **********.89
  }
}
```

### 6. 获取可用特征类型

**端点**: `GET /available_types`

**描述**: 获取系统支持的所有特征类型

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "feature_types": {
      "hundreds": "百位特征",
      "tens": "十位特征", 
      "units": "个位特征",
      "sum": "和值特征",
      "span": "跨度特征",
      "common": "通用特征"
    },
    "total_types": 6,
    "timestamp": **********.89
  }
}
```

### 7. 健康检查

**端点**: `GET /health`

**描述**: 检查API服务健康状态

**响应示例**:
```json
{
  "status": "healthy",
  "data": {
    "components": {
      "feature_engineer": true,
      "cache_optimizer": true,
      "importance_analyzer": true,
      "feature_service": true
    },
    "overall_health": true,
    "timestamp": **********.89
  }
}
```

### 8. 导出特征数据

**端点**: `POST /export`

**描述**: 导出特征数据到文件

**请求体**:
```json
{
  "issues": ["2025001", "2025002"],
  "feature_types": ["hundreds", "tens"],
  "format": "json",
  "include_metadata": true
}
```

**参数说明**:
- `issues` (必需): 期号列表
- `feature_types` (可选): 特征类型列表，默认 `["all"]`
- `format` (可选): 导出格式 (`json`, `csv`)，默认 `json`
- `include_metadata` (可选): 是否包含元数据，默认 `true`

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "features": {
      "2025001": {
        "hundreds": {
          "hundreds_frequency": 0.15,
          "hundreds_trend": 1.2
        },
        "tens": {
          "tens_frequency": 0.12,
          "tens_trend": 0.8
        }
      }
    },
    "export_info": {
      "total_issues": 2,
      "feature_types": ["hundreds", "tens"],
      "format": "json",
      "export_time": **********.89
    },
    "metadata": {
      "api_version": "v2",
      "feature_engineer_version": "P2",
      "cache_stats": {
        "hit_rate": 0.85
      }
    }
  }
}
```

### 9. API信息

**端点**: `GET /info`

**描述**: 获取API详细信息和使用说明

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "name": "Advanced Features API",
    "version": "v2.0",
    "description": "P2高级特征工程系统API接口",
    "endpoints": {
      "GET /api/v2/features/advanced/<feature_type>/<issue>": "获取单个期号的高级特征",
      "POST /api/v2/features/batch": "批量获取多个期号的特征",
      "POST /api/v2/features/importance/<feature_type>": "分析特征重要性",
      "GET /api/v2/features/cache/stats": "获取缓存统计信息",
      "POST /api/v2/features/cache/clear": "清理缓存",
      "GET /api/v2/features/available_types": "获取可用特征类型",
      "GET /api/v2/features/health": "健康检查",
      "POST /api/v2/features/export": "导出特征数据"
    },
    "supported_feature_types": [
      "hundreds", "tens", "units", "sum", "span", "common", "all"
    ]
  }
}
```

## 🔧 错误处理

### 标准错误格式
```json
{
  "status": "error",
  "message": "错误描述",
  "error_code": 400,
  "error_type": "ValueError",
  "timestamp": **********.89
}
```

### 常见错误码
- `400` - 请求参数错误
- `404` - 资源不存在
- `405` - HTTP方法不支持
- `500` - 服务器内部错误
- `503` - 服务不可用

### 错误处理示例
```python
import requests

try:
    response = requests.get("http://127.0.0.1:5000/api/v2/features/advanced/hundreds/2025001")
    response.raise_for_status()
    data = response.json()
    
    if data['status'] == 'error':
        print(f"API错误: {data['message']}")
    else:
        print(f"成功获取特征: {len(data['data']['features'])}个")
        
except requests.exceptions.RequestException as e:
    print(f"请求失败: {e}")
```

## 📊 性能指标

### 响应时间
- **单次特征获取**: < 100ms
- **批量特征获取**: < 30秒/1000期
- **特征重要性分析**: < 60秒/100期
- **缓存操作**: < 10ms

### 并发支持
- **最大并发**: 10个请求/秒
- **推荐并发**: 4个请求/秒
- **超时设置**: 30秒

### 数据限制
- **批量请求**: 最多100个期号
- **特征分析**: 最少10个期号
- **响应大小**: < 10MB

## 🛠️ 开发工具

### Python SDK示例
```python
class FucaiAPIClient:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v2/features"
    
    def get_features(self, feature_type, issue):
        """获取单期特征"""
        url = f"{self.api_base}/advanced/{feature_type}/{issue}"
        response = requests.get(url)
        return response.json()
    
    def batch_features(self, issues, feature_type="all"):
        """批量获取特征"""
        url = f"{self.api_base}/batch"
        data = {"issues": issues, "feature_type": feature_type}
        response = requests.post(url, json=data)
        return response.json()
    
    def analyze_importance(self, feature_type, issues, target_variable):
        """特征重要性分析"""
        url = f"{self.api_base}/importance/{feature_type}"
        data = {
            "issues": issues,
            "target_variable": target_variable
        }
        response = requests.post(url, json=data)
        return response.json()

# 使用示例
client = FucaiAPIClient()
features = client.get_features("hundreds", "2025001")
```

### 测试工具
```bash
# 使用curl测试
curl -X GET "http://127.0.0.1:5000/api/v2/features/health"

# 批量测试
curl -X POST "http://127.0.0.1:5000/api/v2/features/batch" \
  -H "Content-Type: application/json" \
  -d '{"issues": ["2025001", "2025002"], "feature_type": "hundreds"}'
```

## 📋 最佳实践

### 1. 缓存优化
- 优先使用批量接口减少请求次数
- 定期清理过期缓存
- 监控缓存命中率

### 2. 错误处理
- 始终检查响应状态
- 实现重试机制
- 记录错误日志

### 3. 性能优化
- 使用连接池
- 设置合理的超时时间
- 避免频繁的小批量请求

### 4. 安全考虑
- 验证输入参数
- 限制请求频率
- 监控异常访问

---

**API文档版本**: v2.0  
**最后更新**: 2025-01-14  
**维护状态**: 活跃维护
