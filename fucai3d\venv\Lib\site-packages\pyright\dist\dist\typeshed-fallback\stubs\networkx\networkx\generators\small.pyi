from networkx.utils.backends import _dispatchable

__all__ = [
    "LCF_graph",
    "bull_graph",
    "chvatal_graph",
    "cubical_graph",
    "desargues_graph",
    "diamond_graph",
    "dodecahedral_graph",
    "frucht_graph",
    "heawood_graph",
    "hoffman_singleton_graph",
    "house_graph",
    "house_x_graph",
    "icosahedral_graph",
    "krackhardt_kite_graph",
    "moebius_kantor_graph",
    "octahedral_graph",
    "pappus_graph",
    "petersen_graph",
    "sedgewick_maze_graph",
    "tetrahedral_graph",
    "truncated_cube_graph",
    "truncated_tetrahedron_graph",
    "tutte_graph",
]

@_dispatchable
def LCF_graph(n, shift_list, repeats, create_using=None): ...
@_dispatchable
def bull_graph(create_using=None): ...
@_dispatchable
def chvatal_graph(create_using=None): ...
@_dispatchable
def cubical_graph(create_using=None): ...
@_dispatchable
def desargues_graph(create_using=None): ...
@_dispatchable
def diamond_graph(create_using=None): ...
@_dispatchable
def dodecahedral_graph(create_using=None): ...
@_dispatchable
def frucht_graph(create_using=None): ...
@_dispatchable
def heawood_graph(create_using=None): ...
@_dispatchable
def hoffman_singleton_graph(): ...
@_dispatchable
def house_graph(create_using=None): ...
@_dispatchable
def house_x_graph(create_using=None): ...
@_dispatchable
def icosahedral_graph(create_using=None): ...
@_dispatchable
def krackhardt_kite_graph(create_using=None): ...
@_dispatchable
def moebius_kantor_graph(create_using=None): ...
@_dispatchable
def octahedral_graph(create_using=None): ...
@_dispatchable
def pappus_graph(): ...
@_dispatchable
def petersen_graph(create_using=None): ...
@_dispatchable
def sedgewick_maze_graph(create_using=None): ...
@_dispatchable
def tetrahedral_graph(create_using=None): ...
@_dispatchable
def truncated_cube_graph(create_using=None): ...
@_dispatchable
def truncated_tetrahedron_graph(create_using=None): ...
@_dispatchable
def tutte_graph(create_using=None): ...
