# P4-十位预测器配置文件
# 基于P3成功模板，复用独立位置预测理念

# 基础配置
predictor:
  name: "tens_predictor"
  version: "1.0.0"
  position: "tens"
  description: "独立十位预测器"

# 数据配置
data:
  db_path: "data/lottery.db"
  cache_path: "cache/tens_predictor_cache.db"
  feature_types: ["tens", "common"]
  window_size: 20
  lag_features: [1, 2, 3, 5, 7]
  max_training_samples: 8359
  validation_split: 0.2
  test_split: 0.1

# 特征工程配置
features:
  selection_enabled: true
  normalization: "standard"
  handle_missing: "ffill"
  max_features: 50
  importance_threshold: 0.01

# XGBoost模型配置
xgboost:
  objective: "multi:softprob"
  num_class: 10
  max_depth: 6
  learning_rate: 0.1
  n_estimators: 200
  subsample: 0.8
  colsample_bytree: 0.8
  reg_alpha: 0.1
  reg_lambda: 1.0
  random_state: 42
  n_jobs: -1
  early_stopping_rounds: 20
  eval_metric: "mlogloss"

# LightGBM模型配置
lightgbm:
  objective: "multiclass"
  num_class: 10
  max_depth: 6
  learning_rate: 0.05
  n_estimators: 300
  subsample: 0.8
  colsample_bytree: 0.8
  reg_alpha: 0.1
  reg_lambda: 1.0
  random_state: 42
  n_jobs: -1
  early_stopping_rounds: 20
  metric: "multi_logloss"
  verbose: -1

# LSTM模型配置
lstm:
  sequence_length: 10
  hidden_units: [64, 32]
  dropout_rate: 0.2
  activation: "relu"
  output_activation: "softmax"
  optimizer: "adam"
  learning_rate: 0.001
  batch_size: 32
  epochs: 100
  validation_split: 0.2
  early_stopping_patience: 10
  reduce_lr_patience: 5
  reduce_lr_factor: 0.5

# 集成模型配置
ensemble:
  method: "weighted_average"  # simple_average, weighted_average, stacking
  weights:
    xgb: 0.4
    lgb: 0.4
    lstm: 0.2
  stacking_meta_model: "logistic_regression"
  cross_validation_folds: 5

# 训练配置
training:
  auto_retrain: true
  retrain_threshold: 0.05  # 准确率下降超过5%时重训练
  retrain_interval_days: 30
  save_model: true
  model_save_path: "models/tens/"
  backup_models: 3

# 预测配置
prediction:
  confidence_threshold: 0.1
  top_k_predictions: 3
  save_predictions: true
  batch_prediction: false

# 性能监控配置
monitoring:
  enabled: true
  metrics: ["accuracy", "top3_accuracy", "confidence", "precision", "recall", "f1"]
  evaluation_window: 100  # 最近100期的性能
  alert_threshold: 0.3   # 准确率低于30%时告警
  save_performance: true

# 缓存配置
cache:
  memory_size: 200
  db_cache_enabled: true
  cache_ttl: 3600  # 1小时
  auto_cleanup: true
  cleanup_interval: 86400  # 24小时

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_enabled: true
  file_path: "logs/tens_predictor.log"
  max_file_size: "10MB"
  backup_count: 5
  console_enabled: true
