from _typeshed import Incomplete

from networkx.utils.backends import _dispatchable

from ..classes.graph import Graph

__all__ = ["write_dot", "read_dot", "graphviz_layout", "pydot_layout", "to_pydot", "from_pydot"]

def write_dot(G, path) -> None: ...
@_dispatchable
def read_dot(path) -> Graph[Incomplete]: ...
@_dispatchable
def from_pydot(P): ...
def to_pydot(N): ...
def graphviz_layout(G, prog: str = "neato", root=None): ...
def pydot_layout(G, prog: str = "neato", root=None): ...
