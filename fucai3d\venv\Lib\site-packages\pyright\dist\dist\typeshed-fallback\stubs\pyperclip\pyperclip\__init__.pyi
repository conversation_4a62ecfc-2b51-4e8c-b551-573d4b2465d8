__all__ = ["copy", "paste", "set_clipboard", "determine_clipboard"]

from collections.abc import Callable
from typing import Literal
from typing_extensions import TypeAlias

class PyperclipException(RuntimeError): ...

class PyperclipWindowsException(PyperclipException):
    def __init__(self, message: str) -> None: ...

class PyperclipTimeoutException(PyperclipException): ...

_ClipboardMechanismName: TypeAlias = Literal[
    "pbcopy", "pyobjc", "qt", "xclip", "xsel", "wl-clipboard", "klipper", "windows", "no"
]
_ClipboardCopyMechanism: TypeAlias = Callable[[str], None]
_ClipboardPasteMechanism: TypeAlias = Callable[[], str]

def copy(text: str) -> None: ...
def paste() -> str: ...
def set_clipboard(clipboard: _ClipboardMechanismName) -> None: ...
def determine_clipboard() -> tuple[_ClipboardCopyMechanism, _ClipboardPasteMechanism]: ...
def is_available() -> bool: ...
