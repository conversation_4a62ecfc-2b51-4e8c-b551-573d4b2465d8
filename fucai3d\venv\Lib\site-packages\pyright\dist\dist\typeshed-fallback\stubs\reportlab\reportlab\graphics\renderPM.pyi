from _typeshed import Incomplete
from typing import IO, Final

from reportlab.graphics.renderbase import Renderer
from reportlab.graphics.shapes import Drawing
from reportlab.pdfgen.canvas import Canvas

__version__: Final[str]

def Color2Hex(c): ...
def CairoColor(c): ...
def draw(drawing: Drawing, canvas: Canvas, x: float, y: float, showBoundary=...) -> None: ...

class _PMRenderer(Renderer):
    def pop(self) -> None: ...
    def push(self, node) -> None: ...
    def applyState(self) -> None: ...
    def initState(self, x, y) -> None: ...
    def drawNode(self, node) -> None: ...
    def drawRect(self, rect) -> None: ...
    def drawLine(self, line) -> None: ...
    def drawImage(self, image) -> None: ...
    def drawCircle(self, circle) -> None: ...
    def drawPolyLine(self, polyline, _doClose: int = 0) -> None: ...
    def drawEllipse(self, ellipse) -> None: ...
    def drawPolygon(self, polygon) -> None: ...
    def drawString(self, stringObj) -> None: ...
    def drawPath(self, path): ...

BEZIER_ARC_MAGIC: float

class PMCanvas:
    ctm: Incomplete
    def __init__(
        self, w, h, dpi: int = 72, bg: int = 16777215, configPIL=None, backend=None, backendFmt: str = "RGB"
    ) -> None: ...
    def toPIL(self): ...
    def saveToFile(self, fn, fmt=None): ...
    def saveToString(self, fmt: str = "GIF"): ...
    def setFont(self, fontName, fontSize, leading=None) -> None: ...
    def __setattr__(self, name, value) -> None: ...
    def __getattr__(self, name): ...
    def fillstrokepath(self, stroke: int = 1, fill: int = 1) -> None: ...
    def bezierArcCCW(self, cx, cy, rx, ry, theta0, theta1): ...
    def addEllipsoidalArc(self, cx, cy, rx, ry, ang1, ang2) -> None: ...
    def drawCentredString(
        self, x: float, y: float, text: str, text_anchor: str = "middle", direction: str | None = None, shaping: bool = False
    ) -> None: ...
    def drawRightString(self, text: str, x: float, y: float, direction: str | None = None) -> None: ...
    def drawString(
        self,
        x: float,
        y: float,
        text: str,
        _fontInfo=None,
        text_anchor: str = "left",
        direction: str | None = None,
        shaping: bool = False,
    ) -> None: ...
    def line(self, x1, y1, x2, y2) -> None: ...
    def rect(self, x, y, width, height, stroke: int = 1, fill: int = 1) -> None: ...
    def roundRect(self, x, y, width, height, rx, ry) -> None: ...
    def circle(self, cx, cy, r) -> None: ...
    def ellipse(self, cx, cy, rx, ry) -> None: ...
    def saveState(self) -> None: ...
    fillColor: Incomplete
    fillOpacity: Incomplete
    def setFillColor(self, aColor) -> None: ...
    strokeColor: Incomplete
    strokeOpacity: Incomplete
    def setStrokeColor(self, aColor) -> None: ...
    restoreState = saveState
    lineCap: Incomplete
    def setLineCap(self, cap) -> None: ...
    lineJoin: Incomplete
    def setLineJoin(self, join) -> None: ...
    strokeWidth: Incomplete
    def setLineWidth(self, width) -> None: ...
    def stringWidth(self, text, fontName=None, fontSize=None): ...

def drawToPMCanvas(
    d: Drawing,
    dpi: float = 72,
    bg: int = 0xFFFFFF,
    configPIL=None,
    showBoundary=...,
    backend="rlPyCairo",
    backendFmt: str = "RGB",
): ...
def drawToPIL(
    d: Drawing,
    dpi: float = 72,
    bg: int = 0xFFFFFF,
    configPIL=None,
    showBoundary=...,
    backend="rlPyCairo",
    backendFmt: str = "RGB",
): ...
def drawToPILP(
    d: Drawing,
    dpi: float = 72,
    bg: int = 0xFFFFFF,
    configPIL=None,
    showBoundary=...,
    backend="rlPyCairo",
    backendFmt: str = "RGB",
): ...
def drawToFile(
    d: Drawing,
    fn: str | IO[bytes],
    fmt: str = "GIF",
    dpi: float = 72,
    bg: int = 0xFFFFFF,
    configPIL=None,
    showBoundary=...,
    backend="rlPyCairo",
    backendFmt: str = "RGB",
) -> None: ...
def drawToString(
    d: Drawing,
    fmt: str = "GIF",
    dpi: float = 72,
    bg: int = 0xFFFFFF,
    configPIL=None,
    showBoundary=...,
    backend="rlPyCairo",
    backendFmt: str = "RGB",
) -> str: ...

save = drawToFile

def test(outDir: str = "pmout", shout: bool = False): ...
