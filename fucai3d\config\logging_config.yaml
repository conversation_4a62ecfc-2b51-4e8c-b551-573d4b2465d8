# P3-百位预测器日志配置
# 支持多级别、多输出的日志管理

version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  
  detailed:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  
  simple:
    format: "%(levelname)s - %(message)s"

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
  
  file_info:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/hundreds_predictor.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  file_error:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/hundreds_predictor_error.log
    maxBytes: 10485760  # 10MB
    backupCount: 3
    encoding: utf8
  
  file_debug:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/hundreds_predictor_debug.log
    maxBytes: 10485760  # 10MB
    backupCount: 2
    encoding: utf8

loggers:
  # 百位预测器主日志
  HundredsPredictor:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  # XGBoost模型日志
  XGBHundredsModel:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  # LightGBM模型日志
  LGBHundredsModel:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  # LSTM模型日志
  LSTMHundredsModel:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  # 集成模型日志
  EnsembleHundredsModel:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  # 数据访问层日志
  HundredsDataAccess:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  # 性能监控日志
  HundredsPerformanceMonitor:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  # 基础预测器日志
  BaseIndependentPredictor:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  # 调试日志（开发时使用）
  debug:
    level: DEBUG
    handlers: [file_debug]
    propagate: false

# 根日志配置
root:
  level: WARNING
  handlers: [console, file_error]
