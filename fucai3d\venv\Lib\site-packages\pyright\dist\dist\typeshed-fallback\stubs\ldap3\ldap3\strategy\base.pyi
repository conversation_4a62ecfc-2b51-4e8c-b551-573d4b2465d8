from typing import Any

unix_socket_available: bool
SESSION_TERMINATED_BY_SERVER: str
TRANSACTION_ERROR: str
RESPONSE_COMPLETE: str

class BaseStrategy:
    connection: Any
    sync: Any
    no_real_dsa: Any
    pooled: Any
    can_stream: Any
    referral_cache: Any
    thread_safe: bool
    def __init__(self, ldap_connection) -> None: ...
    def open(self, reset_usage: bool = True, read_server_info: bool = True) -> None: ...
    def close(self) -> None: ...
    def send(self, message_type, request, controls=None): ...
    def get_response(self, message_id, timeout=None, get_request: bool = False): ...
    @staticmethod
    def compute_ldap_message_size(data): ...
    def decode_response(self, ldap_message): ...
    def decode_response_fast(self, ldap_message): ...
    @staticmethod
    def decode_control(control): ...
    @staticmethod
    def decode_control_fast(control, from_server: bool = True): ...
    @staticmethod
    def decode_request(message_type, component, controls=None): ...
    def valid_referral_list(self, referrals): ...
    def do_next_range_search(self, request, response, attr_name): ...
    def do_search_on_auto_range(self, request, response): ...
    def create_referral_connection(self, referrals): ...
    def do_operation_on_referral(self, request, referrals): ...
    def sending(self, ldap_message) -> None: ...
    def receiving(self) -> None: ...
    def post_send_single_response(self, message_id) -> None: ...
    def post_send_search(self, message_id) -> None: ...
    def get_stream(self) -> None: ...
    def set_stream(self, value) -> None: ...
    def unbind_referral_cache(self) -> None: ...
