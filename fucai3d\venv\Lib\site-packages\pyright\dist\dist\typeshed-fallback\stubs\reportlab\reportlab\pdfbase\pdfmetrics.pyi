from _typeshed import Incomplete, StrOrBytesPath
from typing import Final

from reportlab.lib.rl_accel import unicode2T1 as unicode2T1

__version__: Final[str]
standardFonts: Incomplete
standardEncodings: Incomplete

class FontError(Exception): ...
class FontNotFoundError(Exception): ...

def parseAFMFile(afmFileName: StrOrBytesPath) -> tuple[dict[Incomplete, Incomplete], list[Incomplete]]: ...

class TypeFace:
    name: Incomplete
    glyphNames: Incomplete
    glyphWidths: Incomplete
    ascent: int
    descent: int
    familyName: Incomplete
    bold: int
    italic: int
    requiredEncoding: str
    builtIn: int
    def __init__(self, name) -> None: ...
    def getFontFiles(self): ...
    def findT1File(self, ext: str = ".pfb"): ...

def bruteForceSearchForFile(fn, searchPath=None): ...
def bruteForceSearchForAFM(faceName): ...

class Encoding:
    name: Incomplete
    frozen: int
    baseEncodingName: Incomplete
    vector: Incomplete
    def __init__(self, name, base=None) -> None: ...
    def __getitem__(self, index): ...
    def __setitem__(self, index, value) -> None: ...
    def freeze(self) -> None: ...
    def isEqual(self, other): ...
    def modifyRange(self, base, newNames) -> None: ...
    def getDifferences(self, otherEnc): ...
    def makePDFObject(self): ...

standardT1SubstitutionFonts: Incomplete

class Font:
    fontName: Incomplete
    encoding: Incomplete
    encName: Incomplete
    substitutionFonts: Incomplete
    shapable: bool
    def __init__(self, name, faceName, encName, substitutionFonts=None) -> None: ...
    def stringWidth(self, text: str | bytes, size: float, encoding: str = "utf8") -> float: ...
    def addObjects(self, doc) -> None: ...

PFB_MARKER: Final[str]
PFB_ASCII: Final[str]
PFB_BINARY: Final[str]
PFB_EOF: Final[str]

class EmbeddedType1Face(TypeFace):
    afmFileName: Incomplete
    pfbFileName: Incomplete
    requiredEncoding: Incomplete
    def __init__(self, afmFileName, pfbFileName) -> None: ...
    def getFontFiles(self): ...
    def addObjects(self, doc): ...

def registerTypeFace(face) -> None: ...
def registerEncoding(enc) -> None: ...
def registerFontFamily(family, normal=None, bold=None, italic=None, boldItalic=None) -> None: ...
def registerFont(font) -> None: ...
def getTypeFace(faceName): ...
def getEncoding(encName): ...
def findFontAndRegister(fontName: str) -> Font: ...
def getFont(fontName: str) -> Font: ...
def getAscentDescent(fontName: str, fontSize: float | None = None): ...
def getAscent(fontName: str, fontSize: float | None = None): ...
def getDescent(fontName: str, fontSize: float | None = None): ...
def getRegisteredFontNames() -> list[Incomplete]: ...
def stringWidth(text: str | bytes, fontName: str, fontSize: float, encoding: str = "utf8") -> float: ...
def dumpFontData() -> None: ...
def test3widths(texts) -> None: ...
def testStringWidthAlgorithms() -> None: ...
def test() -> None: ...
