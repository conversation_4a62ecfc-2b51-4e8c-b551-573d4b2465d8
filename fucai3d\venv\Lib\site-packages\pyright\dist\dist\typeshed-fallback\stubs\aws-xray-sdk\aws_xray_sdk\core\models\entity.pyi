from _typeshed import Incomplete
from logging import Logger
from traceback import <PERSON>ack<PERSON>ummary
from typing import Any, Final, Literal, overload

from .subsegment import Subsegment
from .throwable import Throwable

log: Logger
ORIGIN_TRACE_HEADER_ATTR_KEY: Final[str]

class Entity:
    id: str
    name: str
    start_time: float
    parent_id: str | None
    sampled: bool
    in_progress: bool
    http: dict[str, dict[str, str | int]]
    annotations: dict[str, float | str | bool]
    metadata: dict[str, dict[str, Any]]  # value is any object that can be serialized into JSON string
    aws: dict[str, Incomplete]
    cause: dict[str, str | list[Throwable]]
    subsegments: list[Subsegment]
    end_time: float
    def __init__(self, name: str, entity_id: str | None = None) -> None: ...
    def close(self, end_time: float | None = None) -> None: ...
    def add_subsegment(self, subsegment: Subsegment) -> None: ...
    def remove_subsegment(self, subsegment: Subsegment) -> None: ...
    @overload
    def put_http_meta(self, key: Literal["status", "content_length"], value: int) -> None: ...
    @overload
    def put_http_meta(self, key: Literal["url", "method", "user_agent", "client_ip", "x_forwarded_for"], value: str) -> None: ...
    def put_annotation(self, key: str, value: float | str | bool) -> None: ...
    def put_metadata(
        self, key: str, value: Any, namespace: str = "default"  # value is any object that can be serialized into JSON string
    ) -> None: ...
    def set_aws(self, aws_meta) -> None: ...
    throttle: bool
    def add_throttle_flag(self) -> None: ...
    fault: bool
    def add_fault_flag(self) -> None: ...
    error: bool
    def add_error_flag(self) -> None: ...
    def apply_status_code(self, status_code: int | None) -> None: ...
    def add_exception(self, exception: Exception, stack: StackSummary, remote: bool = False) -> None: ...
    def save_origin_trace_header(self, trace_header) -> None: ...
    def get_origin_trace_header(self): ...
    def serialize(self) -> str: ...
    def to_dict(self) -> dict[str, Incomplete]: ...
