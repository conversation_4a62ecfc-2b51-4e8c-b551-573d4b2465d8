import ast

from flake8_simplify.utils import If

def get_sim102(node: ast.If) -> list[tuple[int, int, str]]: ...
def get_sim103(node: ast.If) -> list[tuple[int, int, str]]: ...
def get_sim108(node: If) -> list[tuple[int, int, str]]: ...
def get_sim114(node: ast.If) -> list[tuple[int, int, str]]: ...
def get_sim116(node: ast.If) -> list[tuple[int, int, str]]: ...
def get_sim908(node: ast.If) -> list[tuple[int, int, str]]: ...
def get_sim401(node: ast.If) -> list[tuple[int, int, str]]: ...
