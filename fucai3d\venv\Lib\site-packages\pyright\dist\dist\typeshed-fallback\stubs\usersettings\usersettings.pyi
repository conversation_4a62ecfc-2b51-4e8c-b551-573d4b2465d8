from typing import Any, TypeVar

_S = TypeVar("_S")

class Settings(dict[str, Any]):
    app_id: str
    settings_directory: str
    settings_file: str
    def __init__(self, app_id: str) -> None: ...
    def add_setting(self, setting_name: str, setting_type: type[_S] = ..., default: _S | None = None) -> None: ...
    def load_settings(self) -> None: ...
    def save_settings(self) -> None: ...
    def __getattr__(self, setting_name: str) -> Any: ...
    def __setattr__(self, setting_name: str, setting_value: Any) -> None: ...
