from _typeshed import Incomplete

from hvac.api.vault_api_base import VaultApiBase

logger: Incomplete

class Aws(VaultApiBase):
    def configure(
        self,
        max_retries=None,
        access_key=None,
        secret_key=None,
        endpoint=None,
        iam_endpoint=None,
        sts_endpoint=None,
        iam_server_id_header_value=None,
        mount_point: str = "aws",
        sts_region: str | None = None,
    ): ...
    def read_config(self, mount_point: str = "aws"): ...
    def delete_config(self, mount_point: str = "aws"): ...
    def configure_identity_integration(
        self,
        iam_alias=None,
        ec2_alias=None,
        mount_point: str = "aws",
        iam_metadata: str | list[str] | None = None,
        ec2_metadata: str | list[str] | None = None,
    ): ...
    def read_identity_integration(self, mount_point: str = "aws"): ...
    def create_certificate_configuration(self, cert_name, aws_public_cert, document_type=None, mount_point: str = "aws"): ...
    def read_certificate_configuration(self, cert_name, mount_point: str = "aws"): ...
    def delete_certificate_configuration(self, cert_name, mount_point: str = "aws"): ...
    def list_certificate_configurations(self, mount_point: str = "aws"): ...
    def create_sts_role(self, account_id, sts_role, mount_point: str = "aws"): ...
    def read_sts_role(self, account_id, mount_point: str = "aws"): ...
    def list_sts_roles(self, mount_point: str = "aws"): ...
    def delete_sts_role(self, account_id, mount_point: str = "aws"): ...
    def configure_identity_whitelist_tidy(self, safety_buffer=None, disable_periodic_tidy=None, mount_point: str = "aws"): ...
    def read_identity_whitelist_tidy(self, mount_point: str = "aws"): ...
    def delete_identity_whitelist_tidy(self, mount_point: str = "aws"): ...
    def configure_role_tag_blacklist_tidy(self, safety_buffer=None, disable_periodic_tidy=None, mount_point: str = "aws"): ...
    def read_role_tag_blacklist_tidy(self, mount_point: str = "aws"): ...
    def delete_role_tag_blacklist_tidy(self, mount_point: str = "aws"): ...
    def create_role(
        self,
        role,
        auth_type=None,
        bound_ami_id=None,
        bound_account_id=None,
        bound_region=None,
        bound_vpc_id=None,
        bound_subnet_id=None,
        bound_iam_role_arn=None,
        bound_iam_instance_profile_arn=None,
        bound_ec2_instance_id=None,
        role_tag=None,
        bound_iam_principal_arn=None,
        inferred_entity_type=None,
        inferred_aws_region=None,
        resolve_aws_unique_ids=None,
        ttl=None,
        max_ttl=None,
        period=None,
        policies=None,
        allow_instance_migration=None,
        disallow_reauthentication=None,
        mount_point: str = "aws",
    ): ...
    def read_role(self, role, mount_point: str = "aws"): ...
    def list_roles(self, mount_point: str = "aws"): ...
    def delete_role(self, role, mount_point: str = "aws"): ...
    def create_role_tags(
        self,
        role,
        policies=None,
        max_ttl=None,
        instance_id=None,
        allow_instance_migration=None,
        disallow_reauthentication=None,
        mount_point: str = "aws",
    ): ...
    def iam_login(
        self,
        access_key,
        secret_key,
        session_token=None,
        header_value=None,
        role=None,
        use_token: bool = True,
        region: str = "us-east-1",
        mount_point: str = "aws",
    ): ...
    def ec2_login(self, pkcs7, nonce=None, role=None, use_token: bool = True, mount_point: str = "aws"): ...
    def place_role_tags_in_blacklist(self, role_tag, mount_point: str = "aws"): ...
    def read_role_tag_blacklist(self, role_tag, mount_point: str = "aws"): ...
    def list_blacklist_tags(self, mount_point: str = "aws"): ...
    def delete_blacklist_tags(self, role_tag, mount_point: str = "aws"): ...
    def tidy_blacklist_tags(self, safety_buffer: str = "72h", mount_point: str = "aws"): ...
    def read_identity_whitelist(self, instance_id, mount_point: str = "aws"): ...
    def list_identity_whitelist(self, mount_point: str = "aws"): ...
    def delete_identity_whitelist_entries(self, instance_id, mount_point: str = "aws"): ...
    def tidy_identity_whitelist_entries(self, safety_buffer: str = "72h", mount_point: str = "aws"): ...
