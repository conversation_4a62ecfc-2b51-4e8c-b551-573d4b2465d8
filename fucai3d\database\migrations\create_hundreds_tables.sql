-- P3-百位预测器数据库表创建脚本
-- 基于独立位置预测理念设计
-- 创建日期: 2025-01-14

-- 百位预测结果表（独立设计）
CREATE TABLE IF NOT EXISTS hundreds_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,                    -- 期号
    model_type TEXT NOT NULL,               -- 模型类型: xgb/lgb/lstm/ensemble
    prob_0 REAL NOT NULL,                   -- 数字0的概率
    prob_1 REAL NOT NULL,                   -- 数字1的概率
    prob_2 REAL NOT NULL,                   -- 数字2的概率
    prob_3 REAL NOT NULL,                   -- 数字3的概率
    prob_4 REAL NOT NULL,                   -- 数字4的概率
    prob_5 REAL NOT NULL,                   -- 数字5的概率
    prob_6 REAL NOT NULL,                   -- 数字6的概率
    prob_7 REAL NOT NULL,                   -- 数字7的概率
    prob_8 REAL NOT NULL,                   -- 数字8的概率
    prob_9 REAL NOT NULL,                   -- 数字9的概率
    predicted_digit INTEGER,                -- 最高概率的数字
    confidence REAL,                        -- 预测置信度
    feature_count INTEGER,                  -- 使用的特征数量
    training_samples INTEGER,               -- 训练样本数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(issue, model_type)
);

-- 百位模型性能表（独立监控）
CREATE TABLE IF NOT EXISTS hundreds_model_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL,               -- 模型类型
    evaluation_period TEXT NOT NULL,        -- 评估周期
    accuracy REAL NOT NULL,                 -- 准确率
    top3_accuracy REAL NOT NULL,            -- Top3准确率
    avg_confidence REAL NOT NULL,           -- 平均置信度
    precision_per_digit TEXT,               -- 每个数字的精确率(JSON)
    recall_per_digit TEXT,                  -- 每个数字的召回率(JSON)
    f1_score_per_digit TEXT,                -- 每个数字的F1分数(JSON)
    confusion_matrix TEXT,                  -- 混淆矩阵(JSON)
    feature_importance TEXT,                -- 特征重要性(JSON)
    training_time REAL,                     -- 训练时间(秒)
    prediction_time REAL,                   -- 预测时间(秒)
    model_size INTEGER,                     -- 模型大小(字节)
    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_hundreds_predictions_issue ON hundreds_predictions(issue);
CREATE INDEX IF NOT EXISTS idx_hundreds_predictions_model_type ON hundreds_predictions(model_type);
CREATE INDEX IF NOT EXISTS idx_hundreds_predictions_created_at ON hundreds_predictions(created_at);

CREATE INDEX IF NOT EXISTS idx_hundreds_performance_model_type ON hundreds_model_performance(model_type);
CREATE INDEX IF NOT EXISTS idx_hundreds_performance_period ON hundreds_model_performance(evaluation_period);
CREATE INDEX IF NOT EXISTS idx_hundreds_performance_evaluated_at ON hundreds_model_performance(evaluated_at);

-- 插入初始化数据（可选）
INSERT OR IGNORE INTO hundreds_model_performance (
    model_type, 
    evaluation_period, 
    accuracy, 
    top3_accuracy, 
    avg_confidence,
    precision_per_digit,
    recall_per_digit,
    f1_score_per_digit,
    confusion_matrix,
    feature_importance,
    training_time,
    prediction_time,
    model_size
) VALUES 
('xgb', 'initial', 0.0, 0.0, 0.0, '{}', '{}', '{}', '{}', '{}', 0.0, 0.0, 0),
('lgb', 'initial', 0.0, 0.0, 0.0, '{}', '{}', '{}', '{}', '{}', 0.0, 0.0, 0),
('lstm', 'initial', 0.0, 0.0, 0.0, '{}', '{}', '{}', '{}', '{}', 0.0, 0.0, 0),
('ensemble', 'initial', 0.0, 0.0, 0.0, '{}', '{}', '{}', '{}', '{}', 0.0, 0.0, 0);

-- 验证表创建
SELECT 'hundreds_predictions table created' as status;
SELECT 'hundreds_model_performance table created' as status;
