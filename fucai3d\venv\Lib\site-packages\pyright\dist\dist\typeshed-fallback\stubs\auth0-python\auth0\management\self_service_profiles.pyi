from _typeshed import Incomplete

from ..rest import RestClientOptions
from ..types import TimeoutType

class SelfServiceProfiles:
    def __init__(
        self,
        domain: str,
        token: str,
        telemetry: bool = True,
        timeout: TimeoutType = 5.0,
        protocol: str = "https",
        rest_options: RestClientOptions | None = None,
    ) -> None: ...
    def all(self, page: int = 0, per_page: int = 25, include_totals: bool = True) -> list[dict[str, Incomplete]]: ...
    def create(self, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    def get(self, profile_id: str) -> dict[str, Incomplete]: ...
    def delete(self, profile_id: str) -> None: ...
    def update(self, profile_id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    def get_custom_text(self, profile_id: str, language: str, page: str) -> dict[str, Incomplete]: ...
    def update_custom_text(
        self, profile_id: str, language: str, page: str, body: dict[str, Incomplete]
    ) -> dict[str, Incomplete]: ...
    def create_sso_ticket(self, profile_id: str, body: dict[str, Incomplete]) -> dict[str, Incomplete]: ...
    def revoke_sso_ticket(self, profile_id: str, ticket_id: str) -> None: ...
