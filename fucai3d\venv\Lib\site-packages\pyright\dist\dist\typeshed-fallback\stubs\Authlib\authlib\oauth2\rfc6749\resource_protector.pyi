from _typeshed import Incomplete

class TokenValidator:
    TOKEN_TYPE: str
    realm: Incomplete
    extra_attributes: Incomplete
    def __init__(self, realm=None, **extra_attributes) -> None: ...
    @staticmethod
    def scope_insufficient(token_scopes, required_scopes): ...
    def authenticate_token(self, token_string) -> None: ...
    def validate_request(self, request) -> None: ...
    def validate_token(self, token, scopes, request) -> None: ...

class ResourceProtector:
    def __init__(self) -> None: ...
    def register_token_validator(self, validator: TokenValidator): ...
    def get_token_validator(self, token_type): ...
    def parse_request_authorization(self, request): ...
    def validate_request(self, scopes, request, **kwargs): ...
