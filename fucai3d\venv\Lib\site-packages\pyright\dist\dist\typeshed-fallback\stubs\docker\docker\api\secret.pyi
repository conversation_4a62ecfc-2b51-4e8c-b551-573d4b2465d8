from collections.abc import Iterable
from typing import Any

from docker.types import DriverConfig

class SecretApiMixin:
    def create_secret(
        self, name: str, data: bytes, labels: dict[str, Any] | None = None, driver: DriverConfig | None = None
    ) -> dict[str, Any]: ...
    def inspect_secret(self, id: str) -> dict[str, Any]: ...
    def remove_secret(self, id: str) -> bool: ...
    def secrets(self, filters: dict[str, Any] | None = None) -> Iterable[dict[str, Any]]: ...
