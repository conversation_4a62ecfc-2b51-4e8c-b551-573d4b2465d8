__all__ = [
    "SingleValueConstraint",
    "ContainedSubtypeConstraint",
    "ValueRangeConstraint",
    "ValueSizeConstraint",
    "PermittedAlphabetConstraint",
    "InnerTypeConstraint",
    "ConstraintsExclusion",
    "ConstraintsIntersection",
    "ConstraintsUnion",
]

class AbstractConstraint:
    def __init__(self, *values) -> None: ...
    def __call__(self, value, idx: int | None = None) -> None: ...
    def __eq__(self, other): ...
    def __ne__(self, other): ...
    def __lt__(self, other): ...
    def __le__(self, other): ...
    def __gt__(self, other): ...
    def __ge__(self, other): ...
    def __bool__(self) -> bool: ...
    def __hash__(self): ...
    def getValueMap(self): ...
    def isSuperTypeOf(self, otherConstraint): ...
    def isSubTypeOf(self, otherConstraint): ...

class SingleValueConstraint(AbstractConstraint):
    def __contains__(self, item) -> bool: ...
    def __iter__(self): ...
    def __add__(self, constraint): ...
    def __sub__(self, constraint): ...

class ContainedSubtypeConstraint(AbstractConstraint): ...
class ValueRangeConstraint(AbstractConstraint): ...
class ValueSizeConstraint(ValueRangeConstraint): ...
class PermittedAlphabetConstraint(SingleValueConstraint): ...
class ComponentPresentConstraint(AbstractConstraint): ...
class ComponentAbsentConstraint(AbstractConstraint): ...
class WithComponentsConstraint(AbstractConstraint): ...
class InnerTypeConstraint(AbstractConstraint): ...
class ConstraintsExclusion(AbstractConstraint): ...

class AbstractConstraintSet(AbstractConstraint):
    def __getitem__(self, idx): ...
    def __iter__(self): ...
    def __add__(self, value): ...
    def __radd__(self, value): ...
    def __len__(self) -> int: ...

class ConstraintsIntersection(AbstractConstraintSet): ...
class ConstraintsUnion(AbstractConstraintSet): ...
