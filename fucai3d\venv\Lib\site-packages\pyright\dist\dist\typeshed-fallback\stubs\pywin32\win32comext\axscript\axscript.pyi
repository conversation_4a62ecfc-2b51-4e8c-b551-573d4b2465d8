import _win32typing

CATID_ActiveScript: _win32typing.PyIID
CATID_ActiveScriptParse: _win32typing.PyIID
IID_IActiveScript: _win32typing.PyIID
IID_IActiveScriptError: _win32typing.PyIID
IID_IActiveScriptParse: _win32typing.PyIID
IID_IActiveScriptParseProcedure: _win32typing.PyIID
IID_IActiveScriptSite: _win32typing.PyIID
IID_IObjectSafety: _win32typing.PyIID
IID_IProvideMultipleClassInfo: _win32typing.PyIID
INTERFACESAFE_FOR_UNTRUSTED_CALLER: int
INTERFACESAFE_FOR_UNTRUSTED_DATA: int
INTERFACE_USES_DISPEX: int
INTERFACE_USES_SECURITY_MANAGER: int
MULTICLASSINFO_GETIIDPRIMARY: int
MULTICLASSINFO_GETIIDSOURCE: int
MULTICLASSINFO_GETNUMRESERVEDDISPIDS: int
MULTICLASSINFO_GETTYPEINFO: int
SCRIPTINFO_ALL_FLAGS: int
SCRIPTINFO_ITYPEINFO: int
SCRIPTINFO_IUNKNOWN: int
SCRIPTINTERRUPT_ALL_FLAGS: int
SCRIPTINTERRUPT_DEBUG: int
SCRIPTINTERRUPT_RAISEEXCEPTION: int
SCRIPTITEM_ALL_FLAGS: int
SCRIPTITEM_CODEONLY: int
SCRIPTITEM_GLOBALMEMBERS: int
SCRIPTITEM_ISPERSISTENT: int
SCRIPTITEM_ISSOURCE: int
SCRIPTITEM_ISVISIBLE: int
SCRIPTITEM_NOCODE: int
SCRIPTPROC_ALL_FLAGS: int
SCRIPTPROC_HOSTMANAGESSOURCE: int
SCRIPTPROC_IMPLICIT_PARENTS: int
SCRIPTPROC_IMPLICIT_THIS: int
SCRIPTSTATE_CLOSED: int
SCRIPTSTATE_CONNECTED: int
SCRIPTSTATE_DISCONNECTED: int
SCRIPTSTATE_INITIALIZED: int
SCRIPTSTATE_STARTED: int
SCRIPTSTATE_UNINITIALIZED: int
SCRIPTTEXT_ALL_FLAGS: int
SCRIPTTEXT_ISEXPRESSION: int
SCRIPTTEXT_ISPERSISTENT: int
SCRIPTTEXT_ISVISIBLE: int
SCRIPTTHREADSTATE_NOTINSCRIPT: int
SCRIPTTHREADSTATE_RUNNING: int
SCRIPTTYPELIB_ISCONTROL: int
SCRIPTTYPELIB_ISPERSISTENT: int
SCRIPT_E_REPORTED: int
TIFLAGS_EXTENDDISPATCHONLY: int
