__all__ = ["PYTZ"]

import datetime
from typing import Literal

from dateutil.rrule import rrule

from ..cal import Timezone
from ..prop import vRecur
from .provider import TZProvider

class PYTZ(TZProvider):
    @property
    def name(self) -> Literal["pytz"]: ...
    def localize_utc(self, dt: datetime.datetime) -> datetime.datetime: ...
    def localize(self, dt: datetime.datetime, tz: datetime.tzinfo) -> datetime.datetime: ...
    def knows_timezone_id(self, id: str) -> bool: ...
    def fix_rrule_until(self, rrule: rrule, ical_rrule: vRecur) -> None: ...
    def create_timezone(self, tz: Timezone) -> datetime.tzinfo: ...  # type: ignore[override]
    def timezone(self, name: str) -> datetime.tzinfo | None: ...
    def uses_pytz(self) -> bool: ...
    def uses_zoneinfo(self) -> bool: ...
