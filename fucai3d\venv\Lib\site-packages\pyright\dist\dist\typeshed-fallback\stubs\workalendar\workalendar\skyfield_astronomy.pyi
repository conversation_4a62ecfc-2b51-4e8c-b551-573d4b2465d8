from _typeshed import Incomplete

hour: Incomplete
minute: Incomplete
second: Incomplete
newton_precision: Incomplete

def calculate_equinoxes(year, timezone: str = ...): ...
def get_current_longitude(current_date, earth, sun): ...
def newton(f, x0, x1, precision=..., **func_kwargs): ...
def newton_angle_function(t, ts, target_angle, body1, body2): ...
def solar_term(year, degrees, timezone: str = ...): ...
