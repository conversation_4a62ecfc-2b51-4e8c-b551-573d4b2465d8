import optparse
from _typeshed import Incomplete
from collections.abc import Iterable, Mapping
from configparser import <PERSON><PERSON>onfigParser
from typing import Any, ClassVar, Final

from docutils import SettingsSpec
from docutils.utils import DependencyList

__docformat__: Final = "reStructuredText"

def store_multiple(option, opt, value, parser, *args, **kwargs) -> None: ...
def read_config_file(option, opt, value, parser) -> None: ...
def validate_encoding(setting, value=None, option_parser=None, config_parser=None, config_section=None): ...
def validate_encoding_error_handler(setting, value=None, option_parser=None, config_parser=None, config_section=None): ...
def validate_encoding_and_error_handler(setting, value, option_parser, config_parser=None, config_section=None): ...
def validate_boolean(setting, value=None, option_parser=None, config_parser=None, config_section=None) -> bool: ...
def validate_ternary(setting, value=None, option_parser=None, config_parser=None, config_section=None): ...
def validate_nonnegative_int(setting, value=None, option_parser=None, config_parser=None, config_section=None) -> int: ...
def validate_threshold(setting, value=None, option_parser=None, config_parser=None, config_section=None) -> int: ...
def validate_colon_separated_string_list(
    setting, value=None, option_parser=None, config_parser=None, config_section=None
) -> list[str]: ...
def validate_comma_separated_list(
    setting, value=None, option_parser=None, config_parser=None, config_section=None
) -> list[str]: ...
def validate_math_output(setting, value=None, option_parser=None, config_parser=None, config_section=None): ...
def validate_url_trailing_slash(setting, value=None, option_parser=None, config_parser=None, config_section=None) -> str: ...
def validate_dependency_file(
    setting, value=None, option_parser=None, config_parser=None, config_section=None
) -> DependencyList: ...
def validate_strip_class(setting, value=None, option_parser=None, config_parser=None, config_section=None): ...
def validate_smartquotes_locales(
    setting, value=None, option_parser=None, config_parser=None, config_section=None
) -> list[tuple[str, str]]: ...
def make_paths_absolute(pathdict, keys, base_path=None) -> None: ...
def make_one_path_absolute(base_path, path) -> str: ...
def filter_settings_spec(settings_spec, *exclude, **replace) -> tuple[Any, ...]: ...

class Values(optparse.Values):
    record_dependencies: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def update(self, other_dict, option_parser) -> None: ...
    def copy(self) -> Values: ...
    def setdefault(self, name, default): ...

class Option(optparse.Option):
    ATTRS: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def process(self, opt, value, values, parser): ...

class OptionParser(optparse.OptionParser, SettingsSpec):
    standard_config_files: ClassVar[list[str]]
    threshold_choices: ClassVar[list[str]]
    thresholds: ClassVar[dict[str, int]]
    booleans: ClassVar[dict[str, bool]]
    default_error_encoding: ClassVar[str]
    default_error_encoding_error_handler: ClassVar[str]
    config_section: ClassVar[str]
    version_template: ClassVar[str]
    lists: Incomplete
    config_files: Incomplete
    relative_path_settings: ClassVar[tuple[str, ...]]
    version: Incomplete
    components: Incomplete
    def __init__(
        self,
        components: Iterable[SettingsSpec | type[SettingsSpec]] = (),
        defaults: Mapping[str, Any] | None = None,
        read_config_files: bool | None = False,
        *args,
        **kwargs,
    ) -> None: ...
    def populate_from_components(self, components) -> None: ...
    @classmethod
    def get_standard_config_files(cls): ...
    def get_standard_config_settings(self): ...
    def get_config_file_settings(self, config_file): ...
    def check_values(self, values, args): ...
    def check_args(self, args): ...
    def set_defaults_from_dict(self, defaults) -> None: ...
    def get_default_values(self): ...
    def get_option_by_dest(self, dest): ...

class ConfigParser(RawConfigParser):
    old_settings: Incomplete
    old_warning: str
    not_utf8_error: str
    def read(self, filenames, option_parser=None): ...
    def handle_old_config(self, filename) -> None: ...
    def validate_settings(self, filename, option_parser) -> None: ...
    def optionxform(self, optionstr): ...
    def get_section(self, section): ...

class ConfigDeprecationWarning(FutureWarning): ...

def get_default_settings(*components) -> Values: ...
