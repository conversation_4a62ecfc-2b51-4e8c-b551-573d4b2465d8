import json
from collections.abc import Callable, Generator, Iterator
from typing import Any

from docker._types import JSO<PERSON>

json_decoder: json.JSONDecoder

def stream_as_text(stream: Iterator[str | bytes]) -> Generator[str]: ...
def json_splitter(buffer: str) -> tuple[JSON, str] | None: ...
def json_stream(stream: Iterator[str]) -> Generator[JSON]: ...
def line_splitter(buffer: str, separator: str = "\n") -> tuple[str, str] | None: ...
def split_buffer(
    stream: Iterator[str | bytes], splitter: Callable[[str], tuple[str, str]] | None = None, decoder: Callable[[str], Any] = ...
) -> Generator[Any]: ...
