from typing import Final

def lookup(code: str, _cache: dict[str, str] = {}) -> str: ...

CLASS_SUCCESSFUL_COMPLETION: Final[str]
CLASS_WARNING: Final[str]
CLASS_NO_DATA: Final[str]
CLASS_SQL_STATEMENT_NOT_YET_COMPLETE: Final[str]
CLASS_CONNECTION_EXCEPTION: Final[str]
CLASS_TRIGGERED_ACTION_EXCEPTION: Final[str]
CLASS_FEATURE_NOT_SUPPORTED: Final[str]
CLASS_INVALID_TRANSACTION_INITIATION: Final[str]
CLASS_LOCATOR_EXCEPTION: Final[str]
CLASS_INVALID_GRANTOR: Final[str]
CLASS_INVALID_ROLE_SPECIFICATION: Final[str]
CLASS_DIAGNOSTICS_EXCEPTION: Final[str]
CLASS_CASE_NOT_FOUND: Final[str]
CLASS_CARDINALITY_VIOLATION: Final[str]
CLASS_DATA_EXCEPTION: Final[str]
CLASS_INTEGRITY_CONSTRAINT_VIOLATION: Final[str]
CLASS_INVALID_CURSOR_STATE: Final[str]
CLASS_INVALID_TRANSACTION_STATE: Final[str]
CLASS_INVALID_SQL_STATEMENT_NAME: Final[str]
CLASS_TRIGGERED_DATA_CHANGE_VIOLATION: Final[str]
CLASS_INVALID_AUTHORIZATION_SPECIFICATION: Final[str]
CLASS_DEPENDENT_PRIVILEGE_DESCRIPTORS_STILL_EXIST: Final[str]
CLASS_INVALID_TRANSACTION_TERMINATION: Final[str]
CLASS_SQL_ROUTINE_EXCEPTION: Final[str]
CLASS_INVALID_CURSOR_NAME: Final[str]
CLASS_EXTERNAL_ROUTINE_EXCEPTION: Final[str]
CLASS_EXTERNAL_ROUTINE_INVOCATION_EXCEPTION: Final[str]
CLASS_SAVEPOINT_EXCEPTION: Final[str]
CLASS_INVALID_CATALOG_NAME: Final[str]
CLASS_INVALID_SCHEMA_NAME: Final[str]
CLASS_TRANSACTION_ROLLBACK: Final[str]
CLASS_SYNTAX_ERROR_OR_ACCESS_RULE_VIOLATION: Final[str]
CLASS_WITH_CHECK_OPTION_VIOLATION: Final[str]
CLASS_INSUFFICIENT_RESOURCES: Final[str]
CLASS_PROGRAM_LIMIT_EXCEEDED: Final[str]
CLASS_OBJECT_NOT_IN_PREREQUISITE_STATE: Final[str]
CLASS_OPERATOR_INTERVENTION: Final[str]
CLASS_SYSTEM_ERROR: Final[str]
CLASS_SNAPSHOT_FAILURE: Final[str]
CLASS_CONFIGURATION_FILE_ERROR: Final[str]
CLASS_FOREIGN_DATA_WRAPPER_ERROR: Final[str]
CLASS_PL_PGSQL_ERROR: Final[str]
CLASS_INTERNAL_ERROR: Final[str]
SUCCESSFUL_COMPLETION: Final[str]
WARNING: Final[str]
NULL_VALUE_ELIMINATED_IN_SET_FUNCTION: Final[str]
STRING_DATA_RIGHT_TRUNCATION_: Final[str]
PRIVILEGE_NOT_REVOKED: Final[str]
PRIVILEGE_NOT_GRANTED: Final[str]
IMPLICIT_ZERO_BIT_PADDING: Final[str]
DYNAMIC_RESULT_SETS_RETURNED: Final[str]
DEPRECATED_FEATURE: Final[str]
NO_DATA: Final[str]
NO_ADDITIONAL_DYNAMIC_RESULT_SETS_RETURNED: Final[str]
SQL_STATEMENT_NOT_YET_COMPLETE: Final[str]
CONNECTION_EXCEPTION: Final[str]
SQLCLIENT_UNABLE_TO_ESTABLISH_SQLCONNECTION: Final[str]
CONNECTION_DOES_NOT_EXIST: Final[str]
SQLSERVER_REJECTED_ESTABLISHMENT_OF_SQLCONNECTION: Final[str]
CONNECTION_FAILURE: Final[str]
TRANSACTION_RESOLUTION_UNKNOWN: Final[str]
PROTOCOL_VIOLATION: Final[str]
TRIGGERED_ACTION_EXCEPTION: Final[str]
FEATURE_NOT_SUPPORTED: Final[str]
INVALID_TRANSACTION_INITIATION: Final[str]
LOCATOR_EXCEPTION: Final[str]
INVALID_LOCATOR_SPECIFICATION: Final[str]
INVALID_GRANTOR: Final[str]
INVALID_GRANT_OPERATION: Final[str]
INVALID_ROLE_SPECIFICATION: Final[str]
DIAGNOSTICS_EXCEPTION: Final[str]
STACKED_DIAGNOSTICS_ACCESSED_WITHOUT_ACTIVE_HANDLER: Final[str]
CASE_NOT_FOUND: Final[str]
CARDINALITY_VIOLATION: Final[str]
DATA_EXCEPTION: Final[str]
STRING_DATA_RIGHT_TRUNCATION: Final[str]
NULL_VALUE_NO_INDICATOR_PARAMETER: Final[str]
NUMERIC_VALUE_OUT_OF_RANGE: Final[str]
NULL_VALUE_NOT_ALLOWED_: Final[str]
ERROR_IN_ASSIGNMENT: Final[str]
INVALID_DATETIME_FORMAT: Final[str]
DATETIME_FIELD_OVERFLOW: Final[str]
INVALID_TIME_ZONE_DISPLACEMENT_VALUE: Final[str]
ESCAPE_CHARACTER_CONFLICT: Final[str]
INVALID_USE_OF_ESCAPE_CHARACTER: Final[str]
INVALID_ESCAPE_OCTET: Final[str]
ZERO_LENGTH_CHARACTER_STRING: Final[str]
MOST_SPECIFIC_TYPE_MISMATCH: Final[str]
SEQUENCE_GENERATOR_LIMIT_EXCEEDED: Final[str]
NOT_AN_XML_DOCUMENT: Final[str]
INVALID_XML_DOCUMENT: Final[str]
INVALID_XML_CONTENT: Final[str]
INVALID_XML_COMMENT: Final[str]
INVALID_XML_PROCESSING_INSTRUCTION: Final[str]
INVALID_INDICATOR_PARAMETER_VALUE: Final[str]
SUBSTRING_ERROR: Final[str]
DIVISION_BY_ZERO: Final[str]
INVALID_PRECEDING_OR_FOLLOWING_SIZE: Final[str]
INVALID_ARGUMENT_FOR_NTILE_FUNCTION: Final[str]
INTERVAL_FIELD_OVERFLOW: Final[str]
INVALID_ARGUMENT_FOR_NTH_VALUE_FUNCTION: Final[str]
INVALID_CHARACTER_VALUE_FOR_CAST: Final[str]
INVALID_ESCAPE_CHARACTER: Final[str]
INVALID_REGULAR_EXPRESSION: Final[str]
INVALID_ARGUMENT_FOR_LOGARITHM: Final[str]
INVALID_ARGUMENT_FOR_POWER_FUNCTION: Final[str]
INVALID_ARGUMENT_FOR_WIDTH_BUCKET_FUNCTION: Final[str]
INVALID_ROW_COUNT_IN_LIMIT_CLAUSE: Final[str]
INVALID_ROW_COUNT_IN_RESULT_OFFSET_CLAUSE: Final[str]
INVALID_LIMIT_VALUE: Final[str]
CHARACTER_NOT_IN_REPERTOIRE: Final[str]
INDICATOR_OVERFLOW: Final[str]
INVALID_PARAMETER_VALUE: Final[str]
UNTERMINATED_C_STRING: Final[str]
INVALID_ESCAPE_SEQUENCE: Final[str]
STRING_DATA_LENGTH_MISMATCH: Final[str]
TRIM_ERROR: Final[str]
ARRAY_SUBSCRIPT_ERROR: Final[str]
INVALID_TABLESAMPLE_REPEAT: Final[str]
INVALID_TABLESAMPLE_ARGUMENT: Final[str]
DUPLICATE_JSON_OBJECT_KEY_VALUE: Final[str]
INVALID_ARGUMENT_FOR_SQL_JSON_DATETIME_FUNCTION: Final[str]
INVALID_JSON_TEXT: Final[str]
INVALID_SQL_JSON_SUBSCRIPT: Final[str]
MORE_THAN_ONE_SQL_JSON_ITEM: Final[str]
NO_SQL_JSON_ITEM: Final[str]
NON_NUMERIC_SQL_JSON_ITEM: Final[str]
NON_UNIQUE_KEYS_IN_A_JSON_OBJECT: Final[str]
SINGLETON_SQL_JSON_ITEM_REQUIRED: Final[str]
SQL_JSON_ARRAY_NOT_FOUND: Final[str]
SQL_JSON_MEMBER_NOT_FOUND: Final[str]
SQL_JSON_NUMBER_NOT_FOUND: Final[str]
SQL_JSON_OBJECT_NOT_FOUND: Final[str]
TOO_MANY_JSON_ARRAY_ELEMENTS: Final[str]
TOO_MANY_JSON_OBJECT_MEMBERS: Final[str]
SQL_JSON_SCALAR_REQUIRED: Final[str]
FLOATING_POINT_EXCEPTION: Final[str]
INVALID_TEXT_REPRESENTATION: Final[str]
INVALID_BINARY_REPRESENTATION: Final[str]
BAD_COPY_FILE_FORMAT: Final[str]
UNTRANSLATABLE_CHARACTER: Final[str]
NONSTANDARD_USE_OF_ESCAPE_CHARACTER: Final[str]
INTEGRITY_CONSTRAINT_VIOLATION: Final[str]
RESTRICT_VIOLATION: Final[str]
NOT_NULL_VIOLATION: Final[str]
FOREIGN_KEY_VIOLATION: Final[str]
UNIQUE_VIOLATION: Final[str]
CHECK_VIOLATION: Final[str]
EXCLUSION_VIOLATION: Final[str]
INVALID_CURSOR_STATE: Final[str]
INVALID_TRANSACTION_STATE: Final[str]
ACTIVE_SQL_TRANSACTION: Final[str]
BRANCH_TRANSACTION_ALREADY_ACTIVE: Final[str]
INAPPROPRIATE_ACCESS_MODE_FOR_BRANCH_TRANSACTION: Final[str]
INAPPROPRIATE_ISOLATION_LEVEL_FOR_BRANCH_TRANSACTION: Final[str]
NO_ACTIVE_SQL_TRANSACTION_FOR_BRANCH_TRANSACTION: Final[str]
READ_ONLY_SQL_TRANSACTION: Final[str]
SCHEMA_AND_DATA_STATEMENT_MIXING_NOT_SUPPORTED: Final[str]
HELD_CURSOR_REQUIRES_SAME_ISOLATION_LEVEL: Final[str]
NO_ACTIVE_SQL_TRANSACTION: Final[str]
IN_FAILED_SQL_TRANSACTION: Final[str]
IDLE_IN_TRANSACTION_SESSION_TIMEOUT: Final[str]
INVALID_SQL_STATEMENT_NAME: Final[str]
TRIGGERED_DATA_CHANGE_VIOLATION: Final[str]
INVALID_AUTHORIZATION_SPECIFICATION: Final[str]
INVALID_PASSWORD: Final[str]
DEPENDENT_PRIVILEGE_DESCRIPTORS_STILL_EXIST: Final[str]
DEPENDENT_OBJECTS_STILL_EXIST: Final[str]
INVALID_TRANSACTION_TERMINATION: Final[str]
SQL_ROUTINE_EXCEPTION: Final[str]
MODIFYING_SQL_DATA_NOT_PERMITTED_: Final[str]
PROHIBITED_SQL_STATEMENT_ATTEMPTED_: Final[str]
READING_SQL_DATA_NOT_PERMITTED_: Final[str]
FUNCTION_EXECUTED_NO_RETURN_STATEMENT: Final[str]
INVALID_CURSOR_NAME: Final[str]
EXTERNAL_ROUTINE_EXCEPTION: Final[str]
CONTAINING_SQL_NOT_PERMITTED: Final[str]
MODIFYING_SQL_DATA_NOT_PERMITTED: Final[str]
PROHIBITED_SQL_STATEMENT_ATTEMPTED: Final[str]
READING_SQL_DATA_NOT_PERMITTED: Final[str]
EXTERNAL_ROUTINE_INVOCATION_EXCEPTION: Final[str]
INVALID_SQLSTATE_RETURNED: Final[str]
NULL_VALUE_NOT_ALLOWED: Final[str]
TRIGGER_PROTOCOL_VIOLATED: Final[str]
SRF_PROTOCOL_VIOLATED: Final[str]
EVENT_TRIGGER_PROTOCOL_VIOLATED: Final[str]
SAVEPOINT_EXCEPTION: Final[str]
INVALID_SAVEPOINT_SPECIFICATION: Final[str]
INVALID_CATALOG_NAME: Final[str]
INVALID_SCHEMA_NAME: Final[str]
TRANSACTION_ROLLBACK: Final[str]
SERIALIZATION_FAILURE: Final[str]
TRANSACTION_INTEGRITY_CONSTRAINT_VIOLATION: Final[str]
STATEMENT_COMPLETION_UNKNOWN: Final[str]
DEADLOCK_DETECTED: Final[str]
SYNTAX_ERROR_OR_ACCESS_RULE_VIOLATION: Final[str]
INSUFFICIENT_PRIVILEGE: Final[str]
SYNTAX_ERROR: Final[str]
INVALID_NAME: Final[str]
INVALID_COLUMN_DEFINITION: Final[str]
NAME_TOO_LONG: Final[str]
DUPLICATE_COLUMN: Final[str]
AMBIGUOUS_COLUMN: Final[str]
UNDEFINED_COLUMN: Final[str]
UNDEFINED_OBJECT: Final[str]
DUPLICATE_OBJECT: Final[str]
DUPLICATE_ALIAS: Final[str]
DUPLICATE_FUNCTION: Final[str]
AMBIGUOUS_FUNCTION: Final[str]
GROUPING_ERROR: Final[str]
DATATYPE_MISMATCH: Final[str]
WRONG_OBJECT_TYPE: Final[str]
INVALID_FOREIGN_KEY: Final[str]
CANNOT_COERCE: Final[str]
UNDEFINED_FUNCTION: Final[str]
GENERATED_ALWAYS: Final[str]
RESERVED_NAME: Final[str]
UNDEFINED_TABLE: Final[str]
UNDEFINED_PARAMETER: Final[str]
DUPLICATE_CURSOR: Final[str]
DUPLICATE_DATABASE: Final[str]
DUPLICATE_PREPARED_STATEMENT: Final[str]
DUPLICATE_SCHEMA: Final[str]
DUPLICATE_TABLE: Final[str]
AMBIGUOUS_PARAMETER: Final[str]
AMBIGUOUS_ALIAS: Final[str]
INVALID_COLUMN_REFERENCE: Final[str]
INVALID_CURSOR_DEFINITION: Final[str]
INVALID_DATABASE_DEFINITION: Final[str]
INVALID_FUNCTION_DEFINITION: Final[str]
INVALID_PREPARED_STATEMENT_DEFINITION: Final[str]
INVALID_SCHEMA_DEFINITION: Final[str]
INVALID_TABLE_DEFINITION: Final[str]
INVALID_OBJECT_DEFINITION: Final[str]
INDETERMINATE_DATATYPE: Final[str]
INVALID_RECURSION: Final[str]
WINDOWING_ERROR: Final[str]
COLLATION_MISMATCH: Final[str]
INDETERMINATE_COLLATION: Final[str]
WITH_CHECK_OPTION_VIOLATION: Final[str]
INSUFFICIENT_RESOURCES: Final[str]
DISK_FULL: Final[str]
OUT_OF_MEMORY: Final[str]
TOO_MANY_CONNECTIONS: Final[str]
CONFIGURATION_LIMIT_EXCEEDED: Final[str]
PROGRAM_LIMIT_EXCEEDED: Final[str]
STATEMENT_TOO_COMPLEX: Final[str]
TOO_MANY_COLUMNS: Final[str]
TOO_MANY_ARGUMENTS: Final[str]
OBJECT_NOT_IN_PREREQUISITE_STATE: Final[str]
OBJECT_IN_USE: Final[str]
CANT_CHANGE_RUNTIME_PARAM: Final[str]
LOCK_NOT_AVAILABLE: Final[str]
UNSAFE_NEW_ENUM_VALUE_USAGE: Final[str]
OPERATOR_INTERVENTION: Final[str]
QUERY_CANCELED: Final[str]
ADMIN_SHUTDOWN: Final[str]
CRASH_SHUTDOWN: Final[str]
CANNOT_CONNECT_NOW: Final[str]
DATABASE_DROPPED: Final[str]
SYSTEM_ERROR: Final[str]
IO_ERROR: Final[str]
UNDEFINED_FILE: Final[str]
DUPLICATE_FILE: Final[str]
SNAPSHOT_TOO_OLD: Final[str]
CONFIG_FILE_ERROR: Final[str]
LOCK_FILE_EXISTS: Final[str]
FDW_ERROR: Final[str]
FDW_OUT_OF_MEMORY: Final[str]
FDW_DYNAMIC_PARAMETER_VALUE_NEEDED: Final[str]
FDW_INVALID_DATA_TYPE: Final[str]
FDW_COLUMN_NAME_NOT_FOUND: Final[str]
FDW_INVALID_DATA_TYPE_DESCRIPTORS: Final[str]
FDW_INVALID_COLUMN_NAME: Final[str]
FDW_INVALID_COLUMN_NUMBER: Final[str]
FDW_INVALID_USE_OF_NULL_POINTER: Final[str]
FDW_INVALID_STRING_FORMAT: Final[str]
FDW_INVALID_HANDLE: Final[str]
FDW_INVALID_OPTION_INDEX: Final[str]
FDW_INVALID_OPTION_NAME: Final[str]
FDW_OPTION_NAME_NOT_FOUND: Final[str]
FDW_REPLY_HANDLE: Final[str]
FDW_UNABLE_TO_CREATE_EXECUTION: Final[str]
FDW_UNABLE_TO_CREATE_REPLY: Final[str]
FDW_UNABLE_TO_ESTABLISH_CONNECTION: Final[str]
FDW_NO_SCHEMAS: Final[str]
FDW_SCHEMA_NOT_FOUND: Final[str]
FDW_TABLE_NOT_FOUND: Final[str]
FDW_FUNCTION_SEQUENCE_ERROR: Final[str]
FDW_TOO_MANY_HANDLES: Final[str]
FDW_INCONSISTENT_DESCRIPTOR_INFORMATION: Final[str]
FDW_INVALID_ATTRIBUTE_VALUE: Final[str]
FDW_INVALID_STRING_LENGTH_OR_BUFFER_LENGTH: Final[str]
FDW_INVALID_DESCRIPTOR_FIELD_IDENTIFIER: Final[str]
PLPGSQL_ERROR: Final[str]
RAISE_EXCEPTION: Final[str]
NO_DATA_FOUND: Final[str]
TOO_MANY_ROWS: Final[str]
ASSERT_FAILURE: Final[str]
INTERNAL_ERROR: Final[str]
DATA_CORRUPTED: Final[str]
INDEX_CORRUPTED: Final[str]
IDLE_SESSION_TIMEOUT: Final[str]
SQL_JSON_ITEM_CANNOT_BE_CAST_TO_TARGET_TYPE: Final[str]
TRANSACTION_TIMEOUT: Final[str]
