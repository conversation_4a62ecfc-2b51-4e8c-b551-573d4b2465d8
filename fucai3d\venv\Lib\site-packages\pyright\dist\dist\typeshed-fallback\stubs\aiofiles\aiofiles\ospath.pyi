from _typeshed import FileD<PERSON><PERSON><PERSON><PERSON>r<PERSON>ath
from asyncio.events import Abstract<PERSON><PERSON><PERSON>oop
from collections.abc import Awaitable, Callable
from concurrent.futures import Executor
from os import Path<PERSON><PERSON>
from typing import AnyStr, TypeVar

_R = TypeVar("_R")

def wrap(func: Callable[..., _R]) -> Callable[..., Awaitable[_R]]: ...
async def exists(
    path: FileDescriptorOrPath, *, loop: AbstractEventLoop | None = ..., executor: Executor | None = ...
) -> bool: ...
async def isfile(
    path: FileDescriptorOrPath, *, loop: AbstractEventLoop | None = ..., executor: Executor | None = ...
) -> bool: ...
async def isdir(s: FileDescriptorOrPath, *, loop: AbstractEventLoop | None = ..., executor: Executor | None = ...) -> bool: ...
async def islink(path: FileDescriptorOrPath) -> bool: ...
async def ismount(path: FileDescriptorOrPath) -> bool: ...
async def getsize(
    filename: FileDescriptorOrPath, *, loop: AbstractEventLoop | None = ..., executor: Executor | None = ...
) -> int: ...
async def getmtime(
    filename: FileDescriptorOrPath, *, loop: AbstractEventLoop | None = ..., executor: Executor | None = ...
) -> float: ...
async def getatime(
    filename: FileDescriptorOrPath, *, loop: AbstractEventLoop | None = ..., executor: Executor | None = ...
) -> float: ...
async def getctime(
    filename: FileDescriptorOrPath, *, loop: AbstractEventLoop | None = ..., executor: Executor | None = ...
) -> float: ...
async def samefile(
    f1: FileDescriptorOrPath, f2: FileDescriptorOrPath, *, loop: AbstractEventLoop | None = ..., executor: Executor | None = ...
) -> bool: ...
async def sameopenfile(fp1: int, fp2: int, *, loop: AbstractEventLoop | None = ..., executor: Executor | None = ...) -> bool: ...
async def abspath(path: PathLike[AnyStr] | AnyStr) -> AnyStr: ...
