# This is actually a C-extension module.
# Not all types defined in C are exported to Python.
# For example: `<PERSON>tern` and `Match` are not exported
# and are redefined in `regex.regex module.

from typing import Any, AnyStr, Generic, final
from typing_extensions import Self

from .regex import Match, Pattern

@final
class Splitter(Generic[AnyStr]):
    @property
    def pattern(self) -> Pattern[AnyStr]: ...
    def __iter__(self) -> Self: ...
    def __next__(self) -> AnyStr | Any: ...
    def split(self) -> AnyStr | Any: ...

@final
class Scanner(Generic[AnyStr]):
    @property
    def pattern(self) -> Pattern[AnyStr]: ...
    def __iter__(self) -> Self: ...
    def __next__(self) -> Match[AnyStr]: ...
    def match(self) -> Match[AnyStr] | None: ...
    def search(self) -> Match[AnyStr] | None: ...
