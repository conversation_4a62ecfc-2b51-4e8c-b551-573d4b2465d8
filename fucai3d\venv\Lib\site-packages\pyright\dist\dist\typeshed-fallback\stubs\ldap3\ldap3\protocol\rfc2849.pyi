from typing import Any

conf_ldif_line_length: Any

def safe_ldif_string(bytes_value): ...
def add_controls(controls, all_base64): ...
def add_attributes(attributes, all_base64): ...
def sort_ldif_lines(lines, sort_order): ...
def search_response_to_ldif(entries, all_base64, sort_order=None): ...
def add_request_to_ldif(entry, all_base64, sort_order=None): ...
def delete_request_to_ldif(entry, all_base64, sort_order=None): ...
def modify_request_to_ldif(entry, all_base64, sort_order=None): ...
def modify_dn_request_to_ldif(entry, all_base64, sort_order=None): ...
def operation_to_ldif(operation_type, entries, all_base64: bool = False, sort_order=None): ...
def add_ldif_header(ldif_lines): ...
def ldif_sort(line, sort_order): ...
def decode_persistent_search_control(change): ...
def persistent_search_response_to_ldif(change): ...
