from _typeshed import Incomplete

__all__ = ["DOCUMENT", "DOCTYP<PERSON>", "TEXT", "ELEMENT", "COMMENT", "ENTITY", "UNKNOWN", "TreeWalker", "NonRecursiveTreeWalker"]

DOCUMENT: int
DOCTYPE: int
TEXT: int
ELEMENT: int
COMMENT: int
ENTITY: int
UNKNOWN: str
spaceCharacters: str

class TreeWalker:
    tree: Incomplete
    def __init__(self, tree) -> None: ...
    def __iter__(self): ...
    def error(self, msg): ...
    def emptyTag(self, namespace, name, attrs, hasChildren: bool = False) -> None: ...
    def startTag(self, namespace, name, attrs): ...
    def endTag(self, namespace, name): ...
    def text(self, data) -> None: ...
    def comment(self, data): ...
    def doctype(self, name, publicId=None, systemId=None): ...
    def entity(self, name): ...
    def unknown(self, nodeType): ...

class NonRecursiveTreeWalker(TreeWalker):
    def getNodeDetails(self, node) -> None: ...
    def getFirstChild(self, node) -> None: ...
    def getNextSibling(self, node) -> None: ...
    def getParentNode(self, node) -> None: ...
    def __iter__(self): ...
