from typing import Any

class EndOfText(RuntimeError): ...

class Scanner:
    data: Any
    data_length: Any
    start_pos: int
    pos: int
    flags: Any
    last: Any
    match: Any
    def __init__(self, text, flags: int = 0) -> None: ...
    @property
    def eos(self): ...
    def check(self, pattern): ...
    def test(self, pattern): ...
    def scan(self, pattern): ...
    def get_char(self) -> None: ...
