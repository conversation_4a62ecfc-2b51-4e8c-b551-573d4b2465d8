from _typeshed import Incomplete

class TestingGateway:
    gateway: Incomplete
    config: Incomplete
    def __init__(self, gateway) -> None: ...
    def make_past_due(self, subscription_id, number_of_days_past_due: int = 1) -> None: ...
    def settle_transaction(self, transaction_id): ...
    def settlement_confirm_transaction(self, transaction_id): ...
    def settlement_decline_transaction(self, transaction_id): ...
    def settlement_pending_transaction(self, transaction_id): ...
    def create_3ds_verification(self, merchant_account_id, params): ...
