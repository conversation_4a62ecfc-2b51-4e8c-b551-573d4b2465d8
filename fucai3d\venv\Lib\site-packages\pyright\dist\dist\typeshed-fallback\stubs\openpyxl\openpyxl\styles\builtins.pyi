from _typeshed import Incomplete

normal: str
comma: str
comma_0: str
currency: str
currency_0: str
percent: str
hyperlink: str
followed_hyperlink: str
title: str
headline_1: str
headline_2: str
headline_3: str
headline_4: str
good: str
bad: str
neutral: str
input: str
output: str
calculation: str
linked_cell: str
check_cell: str
warning: str
note: str
explanatory: str
total: str
accent_1: str
accent_1_20: str
accent_1_40: str
accent_1_60: str
accent_2: str
accent_2_20: str
accent_2_40: str
accent_2_60: str
accent_3: str
accent_3_20: str
accent_3_40: str
accent_3_60: str
accent_4: str
accent_4_20: str
accent_4_40: str
accent_4_60: str
accent_5: str
accent_5_20: str
accent_5_40: str
accent_5_60: str
accent_6: str
accent_6_20: str
accent_6_40: str
accent_6_60: str
pandas_highlight: str
styles: Incomplete
