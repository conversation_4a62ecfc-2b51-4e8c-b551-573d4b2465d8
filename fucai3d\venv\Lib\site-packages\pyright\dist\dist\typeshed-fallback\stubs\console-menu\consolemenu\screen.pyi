from typing import Any

class Screen:
    def __init__(self) -> None: ...
    @property
    def screen_height(self) -> int: ...
    @property
    def screen_width(self) -> int: ...
    @staticmethod
    def clear() -> None: ...
    @staticmethod
    def flush() -> None: ...
    def input(self, prompt: str = "") -> str: ...
    @staticmethod
    def printf(*args: Any) -> None: ...
    @staticmethod
    def println(*args: Any) -> None: ...
