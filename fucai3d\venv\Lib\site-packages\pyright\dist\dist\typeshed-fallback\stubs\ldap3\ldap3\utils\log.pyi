from logging import <PERSON>ull<PERSON><PERSON><PERSON> as <PERSON>ull<PERSON><PERSON><PERSON>
from typing import Any

OFF: int
ERROR: int
BASIC: int
PROTOCOL: int
NETWORK: int
EXTENDED: int
DETAIL_LEVELS: Any

def get_detail_level_name(level_name): ...
def log(detail, message, *args) -> None: ...
def log_enabled(detail): ...
def set_library_log_hide_sensitive_data(hide: bool = True) -> None: ...
def get_library_log_hide_sensitive_data(): ...
def set_library_log_activation_level(logging_level) -> None: ...
def get_library_log_activation_lavel(): ...
def set_library_log_max_line_length(length) -> None: ...
def get_library_log_max_line_length(): ...
def set_library_log_detail_level(detail) -> None: ...
def get_library_log_detail_level(): ...
def format_ldap_message(message, prefix): ...

logger: Any
