from _typeshed import Incomplete, SupportsKeysAndGetItem, SupportsNext, SupportsRichComparison
from collections.abc import Generator, Iterable, Iterator, Mapping, MutableMapping
from typing import Literal, TypeVar, overload

_T = TypeVar("_T")

class URIDict(MutableMapping[str, str]):
    def normalize(self, uri: str) -> str: ...
    store: dict[str, str]
    def __init__(self, m: SupportsKeysAndGetItem[str, str] | Iterable[tuple[str, str]], /, **kwargs: str) -> None: ...
    def __getitem__(self, uri: str) -> str: ...
    def __setitem__(self, uri: str, value: str) -> None: ...
    def __delitem__(self, uri: str) -> None: ...
    def __iter__(self) -> Iterator[str]: ...
    def __len__(self) -> int: ...

class Unset: ...

def format_as_index(container: str, indices: Iterable[Incomplete] | None) -> str: ...
def find_additional_properties(instance: Iterable[str], schema: Mapping[str, Iterable[str]]) -> Generator[str]: ...
def extras_msg(extras: Iterable[object]) -> tuple[str, Literal["was", "were"]]: ...  # elements are passed to the repr() function
@overload
def ensure_list(thing: str) -> list[str]: ...
@overload
def ensure_list(thing: _T) -> _T: ...
def equal(one, two) -> bool: ...
def unbool(element, true=..., false=...): ...
def uniq(container: Iterable[SupportsRichComparison]) -> bool: ...
def find_evaluated_item_indexes_by_schema(validator, instance, schema) -> list[Incomplete]: ...
def find_evaluated_property_keys_by_schema(validator, instance, schema) -> list[Incomplete]: ...
def is_valid(errs_it: SupportsNext[object]) -> bool: ...
