def to_str_or_normalized_unicode(val): ...
def attribute_to_dict(attribute): ...
def attributes_to_dict(attributes): ...
def referrals_to_list(referrals): ...
def search_refs_to_list(search_refs): ...
def search_refs_to_list_fast(search_refs): ...
def sasl_to_dict(sasl): ...
def authentication_choice_to_dict(authentication_choice): ...
def partial_attribute_to_dict(modification): ...
def change_to_dict(change): ...
def changes_to_list(changes): ...
def attributes_to_list(attributes): ...
def ava_to_dict(ava): ...
def substring_to_dict(substring): ...
def prepare_changes_for_request(changes): ...
def build_controls_list(controls): ...
def validate_assertion_value(schema, name, value, auto_escape, auto_encode, validator, check_names): ...
def validate_attribute_value(schema, name, value, auto_encode, validator=None, check_names: bool = False): ...
def prepare_filter_for_sending(raw_string): ...
def prepare_for_sending(raw_string): ...
