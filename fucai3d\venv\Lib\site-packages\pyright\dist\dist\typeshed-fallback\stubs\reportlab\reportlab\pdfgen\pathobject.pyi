from typing import Final

__version__: Final[str]

class PDFPathObject:
    def __init__(self, code=None) -> None: ...
    def getCode(self): ...
    def moveTo(self, x, y) -> None: ...
    def lineTo(self, x, y) -> None: ...
    def curveTo(self, x1, y1, x2, y2, x3, y3) -> None: ...
    def arc(self, x1, y1, x2, y2, startAng: int = 0, extent: int = 90) -> None: ...
    def arcTo(self, x1, y1, x2, y2, startAng: int = 0, extent: int = 90) -> None: ...
    def rect(self, x, y, width, height) -> None: ...
    def ellipse(self, x, y, width, height) -> None: ...
    def circle(self, x_cen, y_cen, r) -> None: ...
    def roundRect(self, x, y, width, height, radius) -> None: ...
    def close(self) -> None: ...
