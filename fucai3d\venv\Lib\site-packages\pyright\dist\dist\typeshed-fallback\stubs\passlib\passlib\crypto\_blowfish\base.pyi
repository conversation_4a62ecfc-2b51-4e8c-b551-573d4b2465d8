from typing import Any

class BlowfishEngine:
    P: Any
    S: Any
    def __init__(self) -> None: ...
    @staticmethod
    def key_to_words(data, size: int = 18): ...
    def encipher(self, l, r): ...
    def expand(self, key_words) -> None: ...
    def eks_salted_expand(self, key_words, salt_words) -> None: ...
    def eks_repeated_expand(self, key_words, salt_words, rounds) -> None: ...
    def repeat_encipher(self, l, r, count): ...

__all__ = ["BlowfishEngine"]
