from _typeshed import Incomplete

import _win32typing

def CryptProtectData(
    DataIn,
    DataDescr: str | None = ...,
    OptionalEntropy: Incomplete | None = ...,
    Reserved: Incomplete | None = ...,
    PromptStruct: _win32typing.PyCRYPTPROTECT_PROMPTSTRUCT | None = ...,
    Flags: int = ...,
): ...
def CryptUnprotectData(
    DataIn,
    OptionalEntropy: Incomplete | None = ...,
    Reserved: Incomplete | None = ...,
    PromptStruct: _win32typing.PyCRYPTPROTECT_PROMPTSTRUCT | None = ...,
    Flags: int = ...,
) -> tuple[Incomplete, Incomplete]: ...
def CryptEnumProviders() -> list[tuple[str, Incomplete]]: ...
def CryptEnumProviderTypes() -> list[tuple[str, Incomplete]]: ...
def CryptGetDefaultProvider(ProvType, Flags) -> str: ...
def CryptSetProviderEx(ProvName: str, ProvType, Flags) -> None: ...
def CryptAcquireContext(Container: str, Provider: str, ProvType, Flags) -> _win32typing.PyCRYPTPROV: ...
def CryptFindLocalizedName(CryptName: str) -> str: ...
def CertEnumSystemStore(Flags, SystemStoreLocationPara: Incomplete | None = ...) -> list[Incomplete]: ...
def CertEnumSystemStoreLocation(Flags: int = ...) -> list[Incomplete]: ...
def CertEnumPhysicalStore(SystemStore: str, Flags) -> list[Incomplete]: ...
def CertRegisterSystemStore(SystemStore: str, Flags) -> None: ...
def CertUnregisterSystemStore(SystemStore: str, Flags) -> None: ...
def CertOpenStore(
    StoreProvider, MsgAndCertEncodingType, CryptProv: _win32typing.PyCRYPTPROV, Flags, Para: Incomplete | None
) -> _win32typing.PyCERTSTORE: ...
def CertOpenSystemStore(SubsystemProtocol: str, Prov: _win32typing.PyCRYPTPROV | None = ...) -> _win32typing.PyCERTSTORE: ...
def CryptFindOIDInfo(KeyType, Key, GroupId: int = ...): ...
def CertAlgIdToOID(AlgId) -> str: ...
def CertOIDToAlgId(ObjId: str): ...
def CryptGetKeyIdentifierProperty(KeyIdentifier: str, PropId=..., Flags: int = ..., ComputerName: str | None = ...): ...
def CryptEnumKeyIdentifierProperties(
    KeyIdentifier: str | None = ..., PropId: int = ..., Flags: int = ..., ComputerName: str | None = ...
): ...
def CryptEnumOIDInfo(GroupId: int = ...): ...
def CertAddSerializedElementToStore(
    CertStore: _win32typing.PyCERTSTORE, Element, AddDisposition, ContextTypeFlags=..., Flags: int = ...
) -> _win32typing.PyCERT_CONTEXT: ...
def CryptQueryObject(ObjectType, Object, ExpectedContentTypeFlags=..., ExpectedFormatTypeFlags=..., Flags: int = ...): ...
def CryptDecodeMessage(
    EncodedBlob,
    DecryptPara,
    VerifyPara: Incomplete | None = ...,
    MsgTypeFlags=...,
    SignerIndex: int = ...,
    PrevInnerContentType: int = ...,
    ReturnData: bool = ...,
): ...
def CryptEncryptMessage(
    EncryptPara: _win32typing.PyCRYPT_ENCRYPT_MESSAGE_PARA, RecipientCert: tuple[_win32typing.PyCERT_CONTEXT, ...], ToBeEncrypted
): ...
def CryptDecryptMessage(
    DecryptPara: _win32typing.PyCRYPT_DECRYPT_MESSAGE_PARA, EncryptedBlob
) -> tuple[Incomplete, _win32typing.PyCERT_CONTEXT]: ...
def CryptSignAndEncryptMessage(
    SignPara: _win32typing.PyCRYPT_SIGN_MESSAGE_PARA,
    EncryptPara: _win32typing.PyCRYPT_ENCRYPT_MESSAGE_PARA,
    RecipientCert: tuple[_win32typing.PyCERT_CONTEXT, ...],
    ToBeSignedAndEncrypted,
): ...
def CryptVerifyMessageSignature(
    SignedBlob, SignerIndex: int = ..., VerifyPara: _win32typing.PyCRYPT_VERIFY_MESSAGE_PARA | None = ..., ReturnData: bool = ...
) -> tuple[_win32typing.PyCERT_CONTEXT, Incomplete]: ...
def CryptGetMessageCertificates(
    SignedBlob, MsgAndCertEncodingType=..., CryptProv: _win32typing.PyCRYPTPROV | None = ..., Flags: int = ...
) -> _win32typing.PyCERTSTORE: ...
def CryptGetMessageSignerCount(SignedBlob, MsgEncodingType=...): ...
def CryptSignMessage(
    SignPara: _win32typing.PyCRYPT_SIGN_MESSAGE_PARA, ToBeSigned: tuple[Incomplete, ...], DetachedSignature: bool = ...
): ...
def CryptVerifyDetachedMessageSignature(
    SignerIndex,
    DetachedSignBlob,
    ToBeSigned: tuple[Incomplete, ...],
    VerifyPara: _win32typing.PyCRYPT_VERIFY_MESSAGE_PARA | None = ...,
) -> _win32typing.PyCERT_CONTEXT: ...
def CryptDecryptAndVerifyMessageSignature(
    EncryptedBlob,
    DecryptPara: _win32typing.PyCRYPT_DECRYPT_MESSAGE_PARA,
    VerifyPara: _win32typing.PyCRYPT_VERIFY_MESSAGE_PARA | None = ...,
    SignerIndex: int = ...,
): ...
def CryptEncodeObjectEx(
    StructType, StructInfo=..., Flags: int = ..., CertEncodingType=..., EncodePara: Incomplete | None = ...
): ...
def CryptDecodeObjectEx(StructType, Encoded, Flags: int = ..., CertEncodingType=..., DecodePara: Incomplete | None = ...): ...
def CertNameToStr(Name, StrType, CertEncodingType): ...
def CryptFormatObject(
    StructType,
    Encoded,
    FormatStrType: int = ...,
    CertEncodingType=...,
    FormatType: int = ...,
    FormatStruct: Incomplete | None = ...,
): ...
def PFXImportCertStore(PFX, Password, Flags) -> _win32typing.PyCERTSTORE: ...
def PFXVerifyPassword(PFX, Password, Flags): ...
def PFXIsPFXBlob(PFX): ...
def CryptBinaryToString(Binary, Flags): ...
def CryptStringToBinary(String, Flags) -> tuple[Incomplete, Incomplete, Incomplete]: ...
