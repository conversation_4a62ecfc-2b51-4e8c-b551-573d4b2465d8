from collections.abc import Generator

from consolemenu.console_menu import MenuItem
from consolemenu.format import MenuBorderStyle, MenuMargins, MenuPadding, MenuStyle as MenuStyle

def ansilen(s: str) -> int: ...

class Dimension:
    width: int
    height: int
    def __init__(self, width: int = 0, height: int = 0, dimension: Dimension | None = None) -> None: ...

class MenuComponent:
    def __init__(self, menu_style: MenuStyle, max_dimension: Dimension | None = None) -> None: ...
    @property
    def max_dimension(self) -> Dimension: ...
    @property
    def style(self) -> MenuStyle: ...
    @property
    def margins(self) -> MenuMargins: ...
    @property
    def padding(self) -> MenuPadding: ...
    @property
    def border_style(self) -> MenuBorderStyle: ...
    def calculate_border_width(self) -> int: ...
    def calculate_content_width(self) -> int: ...
    def generate(self) -> Generator[str, None, None]: ...
    def inner_horizontals(self) -> str: ...
    def inner_horizontal_border(self) -> str: ...
    def outer_horizontals(self) -> str: ...
    def outer_horizontal_border_bottom(self) -> str: ...
    def outer_horizontal_border_top(self) -> str: ...
    def row(self, content: str = "", align: str = "left", indent_len: int = 0) -> str: ...

class MenuHeader(MenuComponent):
    title: str
    title_align: str
    subtitle: str
    subtitle_align: str
    show_bottom_border: bool
    def __init__(
        self,
        menu_style: MenuStyle,
        max_dimension: Dimension | None = None,
        title: str | None = None,
        title_align: str = "left",
        subtitle: str | None = None,
        subtitle_align: str = "left",
        show_bottom_border: bool = False,
    ) -> None: ...
    def generate(self) -> Generator[str, None, None]: ...

class MenuTextSection(MenuComponent):
    text: str
    text_align: str
    show_top_border: bool
    show_bottom_border: bool
    def __init__(
        self,
        menu_style: MenuStyle,
        max_dimension: Dimension | None = None,
        text: str | None = None,
        text_align: str = "left",
        show_top_border: bool = False,
        show_bottom_border: bool = False,
    ) -> None: ...
    def generate(self) -> Generator[str, None, None]: ...

class MenuItemsSection(MenuComponent):
    items_align: str
    def __init__(
        self,
        menu_style: MenuStyle,
        max_dimension: Dimension | None = None,
        items: list[MenuItem] | None = None,
        items_align: str = "left",
    ) -> None: ...
    @property
    def items(self) -> list[MenuItem]: ...
    @items.setter
    def items(self, items: list[MenuItem]) -> None: ...
    @property
    def items_with_bottom_border(self) -> list[str]: ...
    @property
    def items_with_top_border(self) -> list[str]: ...
    def show_item_bottom_border(self, item_text: str, flag: bool) -> None: ...
    def show_item_top_border(self, item_text: str, flag: bool) -> None: ...
    def generate(self) -> Generator[str, None, None]: ...

class MenuFooter(MenuComponent):
    def generate(self) -> Generator[str, None, None]: ...

class MenuPrompt(MenuComponent):
    def __init__(self, menu_style: MenuStyle, max_dimension: Dimension | None = None, prompt_string: str = ">>") -> None: ...
    @property
    def prompt(self) -> str: ...
    @prompt.setter
    def prompt(self, prompt: str) -> None: ...
    def generate(self) -> Generator[str, None, None]: ...
