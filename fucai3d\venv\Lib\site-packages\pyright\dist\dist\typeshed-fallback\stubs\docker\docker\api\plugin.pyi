class PluginApiMixin:
    def configure_plugin(self, name, options): ...
    def create_plugin(self, name, plugin_data_dir, gzip: bool = False): ...
    def disable_plugin(self, name, force: bool = False): ...
    def enable_plugin(self, name, timeout: int = 0): ...
    def inspect_plugin(self, name): ...
    def pull_plugin(self, remote, privileges, name=None): ...
    def plugins(self): ...
    def plugin_privileges(self, name): ...
    def push_plugin(self, name): ...
    def remove_plugin(self, name, force: bool = False): ...
    def upgrade_plugin(self, name, remote, privileges): ...
