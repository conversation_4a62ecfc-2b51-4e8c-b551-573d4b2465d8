from _typeshed import Incomplete

__all__ = [
    "no_filter",
    "hide_nodes",
    "hide_edges",
    "hide_multiedges",
    "hide_diedges",
    "hide_multidiedges",
    "show_nodes",
    "show_edges",
    "show_multiedges",
    "show_diedges",
    "show_multidiedges",
]

def no_filter(*items): ...
def hide_nodes(nodes): ...
def hide_diedges(edges): ...
def hide_edges(edges): ...
def hide_multidiedges(edges): ...
def hide_multiedges(edges): ...

class show_nodes:
    nodes: Incomplete
    def __init__(self, nodes) -> None: ...
    def __call__(self, node): ...

def show_diedges(edges): ...
def show_edges(edges): ...
def show_multidiedges(edges): ...
def show_multiedges(edges): ...
