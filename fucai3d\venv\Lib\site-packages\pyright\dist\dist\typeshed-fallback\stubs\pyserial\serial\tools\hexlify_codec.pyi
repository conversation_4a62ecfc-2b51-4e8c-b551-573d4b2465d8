import codecs
from _typeshed import ReadableBuffer

HEXDIGITS: str

def hex_encode(data: str, errors: str = "strict") -> tuple[bytes, int]: ...
def hex_decode(data: bytes, errors: str = "strict") -> tuple[str, int]: ...

class Codec(codecs.Codec):
    def encode(self, data: str, errors: str = "strict") -> tuple[bytes, int]: ...
    def decode(self, data: bytes, errors: str = "strict") -> tuple[str, int]: ...

class IncrementalEncoder(codecs.IncrementalEncoder):
    state: int
    def encode(self, data: str, final: bool = False) -> bytes: ...

class IncrementalDecoder(codecs.IncrementalDecoder):
    def decode(self, data: ReadableBuffer, final: bool = False) -> str: ...

class StreamWriter(Codec, codecs.StreamWriter): ...
class StreamReader(Codec, codecs.StreamReader): ...

def getregentry() -> codecs.CodecInfo: ...
