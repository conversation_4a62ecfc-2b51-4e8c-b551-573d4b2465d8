from threading import Event
from typing import AnyStr, Generic

class PipeTimeout(OSError): ...

class BufferedPipe(Generic[AnyStr]):
    def __init__(self) -> None: ...
    def set_event(self, event: Event) -> None: ...
    def feed(self, data: AnyStr) -> None: ...
    def read_ready(self) -> bool: ...
    def read(self, nbytes: int, timeout: float | None = None) -> AnyStr: ...
    def empty(self) -> AnyStr: ...
    def close(self) -> None: ...
    def __len__(self) -> int: ...
