from collections.abc import Iterable

from docutils.utils.math.mathml_elements import MathElement, mover, msub, msubsup, msup, mtd, munder, munderover

letters: dict[str, str]
ordinary: dict[str, str]
greek_capitals: dict[str, str]
functions: dict[str, str | None]
modulo_functions: dict[str, tuple[bool, bool, bool, str]]
math_alphabets: dict[str, str]
stretchables: dict[str, str]
operators: dict[str, str]
thick_operators: dict[str, str]
small_operators: dict[str, str]
movablelimits: tuple[str, ...]
spaces: dict[str, str]
accents: dict[str, str]
over: dict[str, tuple[str, float]]
under: dict[str, tuple[str, float]]
anomalous_chars: dict[str, str]
mathbb: dict[str, str]
matrices: dict[str, tuple[str, str]]
layout_styles: dict[str, dict[str, bool | int]]
fractions: dict[str, dict[str, bool | int | str] | dict[str, bool | int] | dict[str, int]]
delimiter_sizes: list[str]
bigdelimiters: dict[str, int]

def tex_cmdname(string: str) -> tuple[str, str]: ...
def tex_number(string: str) -> tuple[str, str]: ...
def tex_token(string: str) -> tuple[str, str]: ...
def tex_group(string: str) -> tuple[str, str]: ...
def tex_token_or_group(string: str) -> tuple[str, str]: ...
def tex_optarg(string: str) -> tuple[str, str]: ...
def parse_latex_math(root: MathElement, source: str) -> MathElement: ...
def handle_cmd(name: str, node: MathElement, string: str) -> tuple[MathElement, str]: ...
def handle_math_alphabet(name: str, node: MathElement, string: str) -> tuple[MathElement, str]: ...
def handle_script_or_limit(
    node: MathElement, c: str, limits: str = ""
) -> munderover | msubsup | munder | msub | mover | msup: ...
def begin_environment(node: MathElement, string: str) -> tuple[mtd, str]: ...
def end_environment(node: MathElement, string: str) -> tuple[MathElement, str]: ...
def tex_equation_columns(rows: Iterable[str]) -> int: ...
def align_attributes(rows: Iterable[str]) -> dict[str, str | bool]: ...
def tex2mathml(tex_math: str, as_block: bool = False) -> str: ...
