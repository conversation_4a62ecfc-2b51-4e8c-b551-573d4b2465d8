import gdb

cont: gdb.EventRegistry[gdb.ContinueEvent]
exited: gdb.EventRegistry[gdb.ExitedEvent]
thread_exited: gdb.EventRegistry[gdb.ThreadExitedEvent]
stop: gdb.EventRegistry[gdb.StopEvent]
new_objfile: gdb.EventRegistry[gdb.NewObjFileEvent]
free_objfile: gdb.EventRegistry[gdb.FreeObjFileEvent]
clear_objfiles: gdb.EventRegistry[gdb.ClearObjFilesEvent]
new_progspace: gdb.EventRegistry[gdb.NewProgspaceEvent]
free_progspace: gdb.EventRegistry[gdb.FreeProgspaceEvent]
inferior_call: gdb.EventRegistry[gdb._InferiorCallEvent]
memory_changed: gdb.EventRegistry[gdb.MemoryChangedEvent]
register_changed: gdb.EventRegistry[gdb.RegisterChangedEvent]
breakpoint_created: gdb.EventRegistry[gdb.Breakpoint]
breakpoint_modified: gdb.EventRegistry[gdb.Breakpoint]
breakpoint_deleted: gdb.EventRegistry[gdb.Breakpoint]
before_prompt: gdb.EventRegistry[None]
new_inferior: gdb.EventRegistry[gdb.NewInferiorEvent]
inferior_deleted: gdb.EventRegistry[gdb.InferiorDeletedEvent]
new_thread: gdb.EventRegistry[gdb.NewThreadEvent]
gdb_exiting: gdb.EventRegistry[gdb.GdbExitingEvent]
connection_removed: gdb.EventRegistry[gdb.ConnectionEvent]
executable_changed: gdb.EventRegistry[gdb.ExecutableChangedEvent]
