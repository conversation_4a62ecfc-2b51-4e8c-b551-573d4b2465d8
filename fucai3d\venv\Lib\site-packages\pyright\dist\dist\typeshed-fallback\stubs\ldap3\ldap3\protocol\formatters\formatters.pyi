from typing import Any

def format_unicode(raw_value): ...
def format_integer(raw_value): ...
def format_binary(raw_value): ...
def format_uuid(raw_value): ...
def format_uuid_le(raw_value): ...
def format_boolean(raw_value): ...
def format_ad_timestamp(raw_value): ...

time_format: Any

def format_time(raw_value): ...
def format_ad_timedelta(raw_value): ...
def format_time_with_0_year(raw_value): ...
def format_sid(raw_value): ...
