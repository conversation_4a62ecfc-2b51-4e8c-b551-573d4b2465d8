from _typeshed import Incomplete

from braintree.customer import Customer
from braintree.error_result import Error<PERSON><PERSON>ult
from braintree.resource_collection import ResourceCollection
from braintree.successful_result import SuccessfulResult

class CustomerGateway:
    gateway: Incomplete
    config: Incomplete
    def __init__(self, gateway) -> None: ...
    def all(self) -> ResourceCollection: ...
    def create(self, params: dict[str, Incomplete] | None = None) -> SuccessfulResult | ErrorResult | None: ...
    def delete(self, customer_id: str) -> SuccessfulResult: ...
    def find(self, customer_id: str, association_filter_id: str | None = None) -> Customer: ...
    def search(self, *query) -> ResourceCollection: ...
    def update(self, customer_id: str, params: dict[str, Incomplete] | None = None) -> SuccessfulResult | ErrorResult | None: ...
