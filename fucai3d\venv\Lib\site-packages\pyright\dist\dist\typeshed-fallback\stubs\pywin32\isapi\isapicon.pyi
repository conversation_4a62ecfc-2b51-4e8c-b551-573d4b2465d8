from typing import Final

HTTP_CONTINUE: Final = 100
HTTP_SWITCHING_PROTOCOLS: Final = 101
HTTP_PROCESSING: Final = 102
HTTP_OK: Final = 200
HTTP_CREATED: Final = 201
HTTP_ACCEPTED: Final = 202
HTTP_NON_AUTHORITATIVE: Final = 203
HTTP_NO_CONTENT: Final = 204
HTTP_RESET_CONTENT: Final = 205
HTTP_PARTIAL_CONTENT: Final = 206
HTTP_MULTI_STATUS: Final = 207
HTTP_MULTIPLE_CHOICES: Final = 300
HTTP_MOVED_PERMANENTLY: Final = 301
HTTP_MOVED_TEMPORARILY: Final = 302
HTTP_SEE_OTHER: Final = 303
HTTP_NOT_MODIFIED: Final = 304
HTTP_USE_PROXY: Final = 305
HTTP_TEMPORARY_REDIRECT: Final = 307
HTTP_BAD_REQUEST: Final = 400
HTTP_UNAUTHORIZED: Final = 401
HTTP_PAYMENT_REQUIRED: Final = 402
HTTP_FORBIDDEN: Final = 403
HTTP_NOT_FOUND: Final = 404
HTTP_METHOD_NOT_ALLOWED: Final = 405
HTTP_NOT_ACCEPTABLE: Final = 406
HTTP_PROXY_AUTHENTICATION_REQUIRED: Final = 407
HTTP_REQUEST_TIME_OUT: Final = 408
HTTP_CONFLICT: Final = 409
HTTP_GONE: Final = 410
HTTP_LENGTH_REQUIRED: Final = 411
HTTP_PRECONDITION_FAILED: Final = 412
HTTP_REQUEST_ENTITY_TOO_LARGE: Final = 413
HTTP_REQUEST_URI_TOO_LARGE: Final = 414
HTTP_UNSUPPORTED_MEDIA_TYPE: Final = 415
HTTP_RANGE_NOT_SATISFIABLE: Final = 416
HTTP_EXPECTATION_FAILED: Final = 417
HTTP_UNPROCESSABLE_ENTITY: Final = 422
HTTP_INTERNAL_SERVER_ERROR: Final = 500
HTTP_NOT_IMPLEMENTED: Final = 501
HTTP_BAD_GATEWAY: Final = 502
HTTP_SERVICE_UNAVAILABLE: Final = 503
HTTP_GATEWAY_TIME_OUT: Final = 504
HTTP_VERSION_NOT_SUPPORTED: Final = 505
HTTP_VARIANT_ALSO_VARIES: Final = 506
HSE_STATUS_SUCCESS: Final = 1
HSE_STATUS_SUCCESS_AND_KEEP_CONN: Final = 2
HSE_STATUS_PENDING: Final = 3
HSE_STATUS_ERROR: Final = 4
SF_NOTIFY_SECURE_PORT: Final = 0x00000001
SF_NOTIFY_NONSECURE_PORT: Final = 0x00000002
SF_NOTIFY_READ_RAW_DATA: Final = 0x00008000
SF_NOTIFY_PREPROC_HEADERS: Final = 0x00004000
SF_NOTIFY_AUTHENTICATION: Final = 0x00002000
SF_NOTIFY_URL_MAP: Final = 0x00001000
SF_NOTIFY_ACCESS_DENIED: Final = 0x00000800
SF_NOTIFY_SEND_RESPONSE: Final = 0x00000040
SF_NOTIFY_SEND_RAW_DATA: Final = 0x00000400
SF_NOTIFY_LOG: Final = 0x00000200
SF_NOTIFY_END_OF_REQUEST: Final = 0x00000080
SF_NOTIFY_END_OF_NET_SESSION: Final = 0x00000100
SF_NOTIFY_ORDER_HIGH: Final = 0x00080000
SF_NOTIFY_ORDER_MEDIUM: Final = 0x00040000
SF_NOTIFY_ORDER_LOW: Final = 0x00020000
SF_NOTIFY_ORDER_DEFAULT: Final = SF_NOTIFY_ORDER_LOW
SF_NOTIFY_ORDER_MASK: Final = 917504
SF_STATUS_REQ_FINISHED: Final = 134217728
SF_STATUS_REQ_FINISHED_KEEP_CONN: Final = 134217729
SF_STATUS_REQ_NEXT_NOTIFICATION: Final = 134217730
SF_STATUS_REQ_HANDLED_NOTIFICATION: Final = 134217731
SF_STATUS_REQ_ERROR: Final = 134217732
SF_STATUS_REQ_READ_NEXT: Final = 134217733
HSE_IO_SYNC: Final = 0x00000001
HSE_IO_ASYNC: Final = 0x00000002
HSE_IO_DISCONNECT_AFTER_SEND: Final = 0x00000004
HSE_IO_SEND_HEADERS: Final = 0x00000008
HSE_IO_NODELAY: Final = 0x00001000
HSE_IO_FINAL_SEND: Final = 0x00000010
HSE_IO_CACHE_RESPONSE: Final = 0x00000020
HSE_EXEC_URL_NO_HEADERS: Final = 0x02
HSE_EXEC_URL_IGNORE_CURRENT_INTERCEPTOR: Final = 0x04
HSE_EXEC_URL_IGNORE_VALIDATION_AND_RANGE: Final = 0x10
HSE_EXEC_URL_DISABLE_CUSTOM_ERROR: Final = 0x20
HSE_EXEC_URL_SSI_CMD: Final = 0x40
HSE_EXEC_URL_HTTP_CACHE_ELIGIBLE: Final = 0x80
