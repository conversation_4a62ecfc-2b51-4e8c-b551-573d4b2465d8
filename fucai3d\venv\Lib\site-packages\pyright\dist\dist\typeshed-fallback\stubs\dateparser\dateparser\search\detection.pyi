from typing import Any

class BaseLanguageDetector:
    languages: Any
    def __init__(self, languages) -> None: ...
    def iterate_applicable_languages(self, date_string, modify: bool = False, settings=None) -> None: ...

class AutoDetectLanguage(BaseLanguageDetector):
    language_pool: Any
    allow_redetection: Any
    def __init__(self, languages, allow_redetection: bool = False) -> None: ...
    languages: Any
    def iterate_applicable_languages(self, date_string, modify: bool = False, settings=None) -> None: ...

class ExactLanguages(BaseLanguageDetector):
    def __init__(self, languages) -> None: ...
    def iterate_applicable_languages(self, date_string, modify: bool = False, settings=None) -> None: ...
