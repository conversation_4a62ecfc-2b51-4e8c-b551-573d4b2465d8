from typing import Final

ERROR_INSTALL_SERVICE: Final = 1601
ERROR_BAD_DATABASE_VERSION: Final = 1613
win16_E_NOTIMPL: Final = -2147483647
win16_E_OUTOFMEMORY: Final = -2147483646
win16_E_INVALIDARG: Final = -2147483645
win16_E_NOINTERFACE: Final = -2147483644
win16_E_POINTER: Final = -2147483643
win16_E_HANDLE: Final = -2147483642
win16_E_ABORT: Final = -2147483641
win16_E_FAIL: Final = -2147483640
win16_E_ACCESSDENIED: Final = -2147483639
CERTDB_E_JET_ERROR: Final = -2146873344

FACILITY_NULL: Final = 0
FACILITY_RPC: Final = 1
FACILITY_DISPATCH: Final = 2
FACILITY_STORAGE: Final = 3
FACILITY_ITF: Final = 4
FACILITY_WIN32: Final = 7
FACILITY_WINDOWS: Final = 8
FACILITY_SSPI: Final = 9
FACILITY_SECURITY: Final = 9
FACILITY_CONTROL: Final = 10
FACILITY_CERT: Final = 11
FACILITY_INTERNET: Final = 12
FACILITY_MEDIASERVER: Final = 13
FACILITY_MSMQ: Final = 14
FACILITY_SETUPAPI: Final = 15
FACILITY_SCARD: Final = 16
FACILITY_COMPLUS: Final = 17
FACILITY_AAF: Final = 18
FACILITY_URT: Final = 19
FACILITY_ACS: Final = 20
FACILITY_DPLAY: Final = 21
FACILITY_UMI: Final = 22
FACILITY_SXS: Final = 23
FACILITY_WINDOWS_CE: Final = 24
FACILITY_HTTP: Final = 25
FACILITY_USERMODE_COMMONLOG: Final = 26
FACILITY_WER: Final = 27
FACILITY_USERMODE_FILTER_MANAGER: Final = 31
FACILITY_BACKGROUNDCOPY: Final = 32
FACILITY_CONFIGURATION: Final = 33
FACILITY_WIA: Final = 33
FACILITY_STATE_MANAGEMENT: Final = 34
FACILITY_METADIRECTORY: Final = 35
FACILITY_WINDOWSUPDATE: Final = 36
FACILITY_DIRECTORYSERVICE: Final = 37
FACILITY_GRAPHICS: Final = 38
FACILITY_SHELL: Final = 39
FACILITY_NAP: Final = 39
FACILITY_TPM_SERVICES: Final = 40
FACILITY_TPM_SOFTWARE: Final = 41
FACILITY_UI: Final = 42
FACILITY_XAML: Final = 43
FACILITY_ACTION_QUEUE: Final = 44
FACILITY_PLA: Final = 48
FACILITY_WINDOWS_SETUP: Final = 48
FACILITY_FVE: Final = 49
FACILITY_FWP: Final = 50
FACILITY_WINRM: Final = 51
FACILITY_NDIS: Final = 52
FACILITY_USERMODE_HYPERVISOR: Final = 53
FACILITY_CMI: Final = 54
FACILITY_USERMODE_VIRTUALIZATION: Final = 55
FACILITY_USERMODE_VOLMGR: Final = 56
FACILITY_BCD: Final = 57
FACILITY_USERMODE_VHD: Final = 58
FACILITY_USERMODE_HNS: Final = 59
FACILITY_SDIAG: Final = 60
FACILITY_WEBSERVICES: Final = 61
FACILITY_WINPE: Final = 61
FACILITY_WPN: Final = 62
FACILITY_WINDOWS_STORE: Final = 63
FACILITY_INPUT: Final = 64
FACILITY_QUIC: Final = 65
FACILITY_EAP: Final = 66
FACILITY_IORING: Final = 70
FACILITY_WINDOWS_DEFENDER: Final = 80
FACILITY_OPC: Final = 81
FACILITY_XPS: Final = 82
FACILITY_MBN: Final = 84
FACILITY_POWERSHELL: Final = 84
FACILITY_RAS: Final = 83
FACILITY_P2P_INT: Final = 98
FACILITY_P2P: Final = 99
FACILITY_DAF: Final = 100
FACILITY_BLUETOOTH_ATT: Final = 101
FACILITY_AUDIO: Final = 102
FACILITY_STATEREPOSITORY: Final = 103
FACILITY_VISUALCPP: Final = 109
FACILITY_SCRIPT: Final = 112
FACILITY_PARSE: Final = 113
FACILITY_BLB: Final = 120
FACILITY_BLB_CLI: Final = 121
FACILITY_WSBAPP: Final = 122
FACILITY_BLBUI: Final = 128
FACILITY_USN: Final = 129
FACILITY_USERMODE_VOLSNAP: Final = 130
FACILITY_TIERING: Final = 131
FACILITY_WSB_ONLINE: Final = 133
FACILITY_ONLINE_ID: Final = 134
FACILITY_DEVICE_UPDATE_AGENT: Final = 135
FACILITY_DRVSERVICING: Final = 136
FACILITY_DLS: Final = 153
FACILITY_DELIVERY_OPTIMIZATION: Final = 208
FACILITY_USERMODE_SPACES: Final = 231
FACILITY_USER_MODE_SECURITY_CORE: Final = 232
FACILITY_USERMODE_LICENSING: Final = 234
FACILITY_SOS: Final = 160
FACILITY_OCP_UPDATE_AGENT: Final = 173
FACILITY_DEBUGGERS: Final = 176
FACILITY_SPP: Final = 256
FACILITY_RESTORE: Final = 256
FACILITY_DMSERVER: Final = 256
FACILITY_DEPLOYMENT_SERVICES_SERVER: Final = 257
FACILITY_DEPLOYMENT_SERVICES_IMAGING: Final = 258
FACILITY_DEPLOYMENT_SERVICES_MANAGEMENT: Final = 259
FACILITY_DEPLOYMENT_SERVICES_UTIL: Final = 260
FACILITY_DEPLOYMENT_SERVICES_BINLSVC: Final = 261
FACILITY_DEPLOYMENT_SERVICES_PXE: Final = 263
FACILITY_DEPLOYMENT_SERVICES_TFTP: Final = 264
FACILITY_DEPLOYMENT_SERVICES_TRANSPORT_MANAGEMENT: Final = 272
FACILITY_DEPLOYMENT_SERVICES_DRIVER_PROVISIONING: Final = 278
FACILITY_DEPLOYMENT_SERVICES_MULTICAST_SERVER: Final = 289
FACILITY_DEPLOYMENT_SERVICES_MULTICAST_CLIENT: Final = 290
FACILITY_DEPLOYMENT_SERVICES_CONTENT_PROVIDER: Final = 293
FACILITY_HSP_SERVICES: Final = 296
FACILITY_HSP_SOFTWARE: Final = 297
FACILITY_LINGUISTIC_SERVICES: Final = 305
FACILITY_AUDIOSTREAMING: Final = 1094
FACILITY_TTD: Final = 1490
FACILITY_ACCELERATOR: Final = 1536
FACILITY_WMAAECMA: Final = 1996
FACILITY_DIRECTMUSIC: Final = 2168
FACILITY_DIRECT3D10: Final = 2169
FACILITY_DXGI: Final = 2170
FACILITY_DXGI_DDI: Final = 2171
FACILITY_DIRECT3D11: Final = 2172
FACILITY_DIRECT3D11_DEBUG: Final = 2173
FACILITY_DIRECT3D12: Final = 2174
FACILITY_DIRECT3D12_DEBUG: Final = 2175
FACILITY_DXCORE: Final = 2176
FACILITY_PRESENTATION: Final = 2177
FACILITY_LEAP: Final = 2184
FACILITY_AUDCLNT: Final = 2185
FACILITY_WINCODEC_DWRITE_DWM: Final = 2200
FACILITY_WINML: Final = 2192
FACILITY_DIRECT2D: Final = 2201
FACILITY_DEFRAG: Final = 2304
FACILITY_USERMODE_SDBUS: Final = 2305
FACILITY_JSCRIPT: Final = 2306
FACILITY_PIDGENX: Final = 2561
FACILITY_EAS: Final = 85
FACILITY_WEB: Final = 885
FACILITY_WEB_SOCKET: Final = 886
FACILITY_MOBILE: Final = 1793
FACILITY_SQLITE: Final = 1967
FACILITY_SERVICE_FABRIC: Final = 1968
FACILITY_UTC: Final = 1989
FACILITY_WEP: Final = 2049
FACILITY_SYNCENGINE: Final = 2050
FACILITY_XBOX: Final = 2339
FACILITY_GAME: Final = 2340
FACILITY_PIX: Final = 2748
ERROR_SUCCESS: Final = 0
NO_ERROR: Final = 0
SEC_E_OK: Final = 0x00000000
ERROR_INVALID_FUNCTION: Final = 1
ERROR_FILE_NOT_FOUND: Final = 2
ERROR_PATH_NOT_FOUND: Final = 3
ERROR_TOO_MANY_OPEN_FILES: Final = 4
ERROR_ACCESS_DENIED: Final = 5
ERROR_INVALID_HANDLE: Final = 6
ERROR_ARENA_TRASHED: Final = 7
ERROR_NOT_ENOUGH_MEMORY: Final = 8
ERROR_INVALID_BLOCK: Final = 9
ERROR_BAD_ENVIRONMENT: Final = 10
ERROR_BAD_FORMAT: Final = 11
ERROR_INVALID_ACCESS: Final = 12
ERROR_INVALID_DATA: Final = 13
ERROR_OUTOFMEMORY: Final = 14
ERROR_INVALID_DRIVE: Final = 15
ERROR_CURRENT_DIRECTORY: Final = 16
ERROR_NOT_SAME_DEVICE: Final = 17
ERROR_NO_MORE_FILES: Final = 18
ERROR_WRITE_PROTECT: Final = 19
ERROR_BAD_UNIT: Final = 20
ERROR_NOT_READY: Final = 21
ERROR_BAD_COMMAND: Final = 22
ERROR_CRC: Final = 23
ERROR_BAD_LENGTH: Final = 24
ERROR_SEEK: Final = 25
ERROR_NOT_DOS_DISK: Final = 26
ERROR_SECTOR_NOT_FOUND: Final = 27
ERROR_OUT_OF_PAPER: Final = 28
ERROR_WRITE_FAULT: Final = 29
ERROR_READ_FAULT: Final = 30
ERROR_GEN_FAILURE: Final = 31
ERROR_SHARING_VIOLATION: Final = 32
ERROR_LOCK_VIOLATION: Final = 33
ERROR_WRONG_DISK: Final = 34
ERROR_SHARING_BUFFER_EXCEEDED: Final = 36
ERROR_HANDLE_EOF: Final = 38
ERROR_HANDLE_DISK_FULL: Final = 39
ERROR_NOT_SUPPORTED: Final = 50
ERROR_REM_NOT_LIST: Final = 51
ERROR_DUP_NAME: Final = 52
ERROR_BAD_NETPATH: Final = 53
ERROR_NETWORK_BUSY: Final = 54
ERROR_DEV_NOT_EXIST: Final = 55
ERROR_TOO_MANY_CMDS: Final = 56
ERROR_ADAP_HDW_ERR: Final = 57
ERROR_BAD_NET_RESP: Final = 58
ERROR_UNEXP_NET_ERR: Final = 59
ERROR_BAD_REM_ADAP: Final = 60
ERROR_PRINTQ_FULL: Final = 61
ERROR_NO_SPOOL_SPACE: Final = 62
ERROR_PRINT_CANCELLED: Final = 63
ERROR_NETNAME_DELETED: Final = 64
ERROR_NETWORK_ACCESS_DENIED: Final = 65
ERROR_BAD_DEV_TYPE: Final = 66
ERROR_BAD_NET_NAME: Final = 67
ERROR_TOO_MANY_NAMES: Final = 68
ERROR_TOO_MANY_SESS: Final = 69
ERROR_SHARING_PAUSED: Final = 70
ERROR_REQ_NOT_ACCEP: Final = 71
ERROR_REDIR_PAUSED: Final = 72
ERROR_FILE_EXISTS: Final = 80
ERROR_CANNOT_MAKE: Final = 82
ERROR_FAIL_I24: Final = 83
ERROR_OUT_OF_STRUCTURES: Final = 84
ERROR_ALREADY_ASSIGNED: Final = 85
ERROR_INVALID_PASSWORD: Final = 86
ERROR_INVALID_PARAMETER: Final = 87
ERROR_NET_WRITE_FAULT: Final = 88
ERROR_NO_PROC_SLOTS: Final = 89
ERROR_TOO_MANY_SEMAPHORES: Final = 100
ERROR_EXCL_SEM_ALREADY_OWNED: Final = 101
ERROR_SEM_IS_SET: Final = 102
ERROR_TOO_MANY_SEM_REQUESTS: Final = 103
ERROR_INVALID_AT_INTERRUPT_TIME: Final = 104
ERROR_SEM_OWNER_DIED: Final = 105
ERROR_SEM_USER_LIMIT: Final = 106
ERROR_DISK_CHANGE: Final = 107
ERROR_DRIVE_LOCKED: Final = 108
ERROR_BROKEN_PIPE: Final = 109
ERROR_OPEN_FAILED: Final = 110
ERROR_BUFFER_OVERFLOW: Final = 111
ERROR_DISK_FULL: Final = 112
ERROR_NO_MORE_SEARCH_HANDLES: Final = 113
ERROR_INVALID_TARGET_HANDLE: Final = 114
ERROR_INVALID_CATEGORY: Final = 117
ERROR_INVALID_VERIFY_SWITCH: Final = 118
ERROR_BAD_DRIVER_LEVEL: Final = 119
ERROR_CALL_NOT_IMPLEMENTED: Final = 120
ERROR_SEM_TIMEOUT: Final = 121
ERROR_INSUFFICIENT_BUFFER: Final = 122
ERROR_INVALID_NAME: Final = 123
ERROR_INVALID_LEVEL: Final = 124
ERROR_NO_VOLUME_LABEL: Final = 125
ERROR_MOD_NOT_FOUND: Final = 126
ERROR_PROC_NOT_FOUND: Final = 127
ERROR_WAIT_NO_CHILDREN: Final = 128
ERROR_CHILD_NOT_COMPLETE: Final = 129
ERROR_DIRECT_ACCESS_HANDLE: Final = 130
ERROR_NEGATIVE_SEEK: Final = 131
ERROR_SEEK_ON_DEVICE: Final = 132
ERROR_IS_JOIN_TARGET: Final = 133
ERROR_IS_JOINED: Final = 134
ERROR_IS_SUBSTED: Final = 135
ERROR_NOT_JOINED: Final = 136
ERROR_NOT_SUBSTED: Final = 137
ERROR_JOIN_TO_JOIN: Final = 138
ERROR_SUBST_TO_SUBST: Final = 139
ERROR_JOIN_TO_SUBST: Final = 140
ERROR_SUBST_TO_JOIN: Final = 141
ERROR_BUSY_DRIVE: Final = 142
ERROR_SAME_DRIVE: Final = 143
ERROR_DIR_NOT_ROOT: Final = 144
ERROR_DIR_NOT_EMPTY: Final = 145
ERROR_IS_SUBST_PATH: Final = 146
ERROR_IS_JOIN_PATH: Final = 147
ERROR_PATH_BUSY: Final = 148
ERROR_IS_SUBST_TARGET: Final = 149
ERROR_SYSTEM_TRACE: Final = 150
ERROR_INVALID_EVENT_COUNT: Final = 151
ERROR_TOO_MANY_MUXWAITERS: Final = 152
ERROR_INVALID_LIST_FORMAT: Final = 153
ERROR_LABEL_TOO_LONG: Final = 154
ERROR_TOO_MANY_TCBS: Final = 155
ERROR_SIGNAL_REFUSED: Final = 156
ERROR_DISCARDED: Final = 157
ERROR_NOT_LOCKED: Final = 158
ERROR_BAD_THREADID_ADDR: Final = 159
ERROR_BAD_ARGUMENTS: Final = 160
ERROR_BAD_PATHNAME: Final = 161
ERROR_SIGNAL_PENDING: Final = 162
ERROR_MAX_THRDS_REACHED: Final = 164
ERROR_LOCK_FAILED: Final = 167
ERROR_BUSY: Final = 170
ERROR_DEVICE_SUPPORT_IN_PROGRESS: Final = 171
ERROR_CANCEL_VIOLATION: Final = 173
ERROR_ATOMIC_LOCKS_NOT_SUPPORTED: Final = 174
ERROR_INVALID_SEGMENT_NUMBER: Final = 180
ERROR_INVALID_ORDINAL: Final = 182
ERROR_ALREADY_EXISTS: Final = 183
ERROR_INVALID_FLAG_NUMBER: Final = 186
ERROR_SEM_NOT_FOUND: Final = 187
ERROR_INVALID_STARTING_CODESEG: Final = 188
ERROR_INVALID_STACKSEG: Final = 189
ERROR_INVALID_MODULETYPE: Final = 190
ERROR_INVALID_EXE_SIGNATURE: Final = 191
ERROR_EXE_MARKED_INVALID: Final = 192
ERROR_BAD_EXE_FORMAT: Final = 193
ERROR_ITERATED_DATA_EXCEEDS_64k: Final = 194
ERROR_INVALID_MINALLOCSIZE: Final = 195
ERROR_DYNLINK_FROM_INVALID_RING: Final = 196
ERROR_IOPL_NOT_ENABLED: Final = 197
ERROR_INVALID_SEGDPL: Final = 198
ERROR_AUTODATASEG_EXCEEDS_64k: Final = 199
ERROR_RING2SEG_MUST_BE_MOVABLE: Final = 200
ERROR_RELOC_CHAIN_XEEDS_SEGLIM: Final = 201
ERROR_INFLOOP_IN_RELOC_CHAIN: Final = 202
ERROR_ENVVAR_NOT_FOUND: Final = 203
ERROR_NO_SIGNAL_SENT: Final = 205
ERROR_FILENAME_EXCED_RANGE: Final = 206
ERROR_RING2_STACK_IN_USE: Final = 207
ERROR_META_EXPANSION_TOO_LONG: Final = 208
ERROR_INVALID_SIGNAL_NUMBER: Final = 209
ERROR_THREAD_1_INACTIVE: Final = 210
ERROR_LOCKED: Final = 212
ERROR_TOO_MANY_MODULES: Final = 214
ERROR_NESTING_NOT_ALLOWED: Final = 215
ERROR_EXE_MACHINE_TYPE_MISMATCH: Final = 216
ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY: Final = 217
ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY: Final = 218
ERROR_FILE_CHECKED_OUT: Final = 220
ERROR_CHECKOUT_REQUIRED: Final = 221
ERROR_BAD_FILE_TYPE: Final = 222
ERROR_FILE_TOO_LARGE: Final = 223
ERROR_FORMS_AUTH_REQUIRED: Final = 224
ERROR_VIRUS_INFECTED: Final = 225
ERROR_VIRUS_DELETED: Final = 226
ERROR_PIPE_LOCAL: Final = 229
ERROR_BAD_PIPE: Final = 230
ERROR_PIPE_BUSY: Final = 231
ERROR_NO_DATA: Final = 232
ERROR_PIPE_NOT_CONNECTED: Final = 233
ERROR_MORE_DATA: Final = 234
ERROR_NO_WORK_DONE: Final = 235
ERROR_VC_DISCONNECTED: Final = 240
ERROR_INVALID_EA_NAME: Final = 254
ERROR_EA_LIST_INCONSISTENT: Final = 255
WAIT_TIMEOUT: Final = 258
ERROR_NO_MORE_ITEMS: Final = 259
ERROR_CANNOT_COPY: Final = 266
ERROR_DIRECTORY: Final = 267
ERROR_EAS_DIDNT_FIT: Final = 275
ERROR_EA_FILE_CORRUPT: Final = 276
ERROR_EA_TABLE_FULL: Final = 277
ERROR_INVALID_EA_HANDLE: Final = 278
ERROR_EAS_NOT_SUPPORTED: Final = 282
ERROR_NOT_OWNER: Final = 288
ERROR_TOO_MANY_POSTS: Final = 298
ERROR_PARTIAL_COPY: Final = 299
ERROR_OPLOCK_NOT_GRANTED: Final = 300
ERROR_INVALID_OPLOCK_PROTOCOL: Final = 301
ERROR_DISK_TOO_FRAGMENTED: Final = 302
ERROR_DELETE_PENDING: Final = 303
ERROR_INCOMPATIBLE_WITH_GLOBAL_SHORT_NAME_REGISTRY_SETTING: Final = 304
ERROR_SHORT_NAMES_NOT_ENABLED_ON_VOLUME: Final = 305
ERROR_SECURITY_STREAM_IS_INCONSISTENT: Final = 306
ERROR_INVALID_LOCK_RANGE: Final = 307
ERROR_IMAGE_SUBSYSTEM_NOT_PRESENT: Final = 308
ERROR_NOTIFICATION_GUID_ALREADY_DEFINED: Final = 309
ERROR_INVALID_EXCEPTION_HANDLER: Final = 310
ERROR_DUPLICATE_PRIVILEGES: Final = 311
ERROR_NO_RANGES_PROCESSED: Final = 312
ERROR_NOT_ALLOWED_ON_SYSTEM_FILE: Final = 313
ERROR_DISK_RESOURCES_EXHAUSTED: Final = 314
ERROR_INVALID_TOKEN: Final = 315
ERROR_DEVICE_FEATURE_NOT_SUPPORTED: Final = 316
ERROR_MR_MID_NOT_FOUND: Final = 317
ERROR_SCOPE_NOT_FOUND: Final = 318
ERROR_UNDEFINED_SCOPE: Final = 319
ERROR_INVALID_CAP: Final = 320
ERROR_DEVICE_UNREACHABLE: Final = 321
ERROR_DEVICE_NO_RESOURCES: Final = 322
ERROR_DATA_CHECKSUM_ERROR: Final = 323
ERROR_INTERMIXED_KERNEL_EA_OPERATION: Final = 324
ERROR_FILE_LEVEL_TRIM_NOT_SUPPORTED: Final = 326
ERROR_OFFSET_ALIGNMENT_VIOLATION: Final = 327
ERROR_INVALID_FIELD_IN_PARAMETER_LIST: Final = 328
ERROR_OPERATION_IN_PROGRESS: Final = 329
ERROR_BAD_DEVICE_PATH: Final = 330
ERROR_TOO_MANY_DESCRIPTORS: Final = 331
ERROR_SCRUB_DATA_DISABLED: Final = 332
ERROR_NOT_REDUNDANT_STORAGE: Final = 333
ERROR_RESIDENT_FILE_NOT_SUPPORTED: Final = 334
ERROR_COMPRESSED_FILE_NOT_SUPPORTED: Final = 335
ERROR_DIRECTORY_NOT_SUPPORTED: Final = 336
ERROR_NOT_READ_FROM_COPY: Final = 337
ERROR_FT_WRITE_FAILURE: Final = 338
ERROR_FT_DI_SCAN_REQUIRED: Final = 339
ERROR_INVALID_KERNEL_INFO_VERSION: Final = 340
ERROR_INVALID_PEP_INFO_VERSION: Final = 341
ERROR_OBJECT_NOT_EXTERNALLY_BACKED: Final = 342
ERROR_EXTERNAL_BACKING_PROVIDER_UNKNOWN: Final = 343
ERROR_COMPRESSION_NOT_BENEFICIAL: Final = 344
ERROR_STORAGE_TOPOLOGY_ID_MISMATCH: Final = 345
ERROR_BLOCKED_BY_PARENTAL_CONTROLS: Final = 346
ERROR_BLOCK_TOO_MANY_REFERENCES: Final = 347
ERROR_MARKED_TO_DISALLOW_WRITES: Final = 348
ERROR_ENCLAVE_FAILURE: Final = 349
ERROR_FAIL_NOACTION_REBOOT: Final = 350
ERROR_FAIL_SHUTDOWN: Final = 351
ERROR_FAIL_RESTART: Final = 352
ERROR_MAX_SESSIONS_REACHED: Final = 353
ERROR_NETWORK_ACCESS_DENIED_EDP: Final = 354
ERROR_DEVICE_HINT_NAME_BUFFER_TOO_SMALL: Final = 355
ERROR_EDP_POLICY_DENIES_OPERATION: Final = 356
ERROR_EDP_DPL_POLICY_CANT_BE_SATISFIED: Final = 357
ERROR_CLOUD_FILE_SYNC_ROOT_METADATA_CORRUPT: Final = 358
ERROR_DEVICE_IN_MAINTENANCE: Final = 359
ERROR_NOT_SUPPORTED_ON_DAX: Final = 360
ERROR_DAX_MAPPING_EXISTS: Final = 361
ERROR_CLOUD_FILE_PROVIDER_NOT_RUNNING: Final = 362
ERROR_CLOUD_FILE_METADATA_CORRUPT: Final = 363
ERROR_CLOUD_FILE_METADATA_TOO_LARGE: Final = 364
ERROR_CLOUD_FILE_PROPERTY_BLOB_TOO_LARGE: Final = 365
ERROR_CLOUD_FILE_PROPERTY_BLOB_CHECKSUM_MISMATCH: Final = 366
ERROR_CHILD_PROCESS_BLOCKED: Final = 367
ERROR_STORAGE_LOST_DATA_PERSISTENCE: Final = 368
ERROR_FILE_SYSTEM_VIRTUALIZATION_UNAVAILABLE: Final = 369
ERROR_FILE_SYSTEM_VIRTUALIZATION_METADATA_CORRUPT: Final = 370
ERROR_FILE_SYSTEM_VIRTUALIZATION_BUSY: Final = 371
ERROR_FILE_SYSTEM_VIRTUALIZATION_PROVIDER_UNKNOWN: Final = 372
ERROR_GDI_HANDLE_LEAK: Final = 373
ERROR_CLOUD_FILE_TOO_MANY_PROPERTY_BLOBS: Final = 374
ERROR_CLOUD_FILE_PROPERTY_VERSION_NOT_SUPPORTED: Final = 375
ERROR_NOT_A_CLOUD_FILE: Final = 376
ERROR_CLOUD_FILE_NOT_IN_SYNC: Final = 377
ERROR_CLOUD_FILE_ALREADY_CONNECTED: Final = 378
ERROR_CLOUD_FILE_NOT_SUPPORTED: Final = 379
ERROR_CLOUD_FILE_INVALID_REQUEST: Final = 380
ERROR_CLOUD_FILE_READ_ONLY_VOLUME: Final = 381
ERROR_CLOUD_FILE_CONNECTED_PROVIDER_ONLY: Final = 382
ERROR_CLOUD_FILE_VALIDATION_FAILED: Final = 383
ERROR_SMB1_NOT_AVAILABLE: Final = 384
ERROR_FILE_SYSTEM_VIRTUALIZATION_INVALID_OPERATION: Final = 385
ERROR_CLOUD_FILE_AUTHENTICATION_FAILED: Final = 386
ERROR_CLOUD_FILE_INSUFFICIENT_RESOURCES: Final = 387
ERROR_CLOUD_FILE_NETWORK_UNAVAILABLE: Final = 388
ERROR_CLOUD_FILE_UNSUCCESSFUL: Final = 389
ERROR_CLOUD_FILE_NOT_UNDER_SYNC_ROOT: Final = 390
ERROR_CLOUD_FILE_IN_USE: Final = 391
ERROR_CLOUD_FILE_PINNED: Final = 392
ERROR_CLOUD_FILE_REQUEST_ABORTED: Final = 393
ERROR_CLOUD_FILE_PROPERTY_CORRUPT: Final = 394
ERROR_CLOUD_FILE_ACCESS_DENIED: Final = 395
ERROR_CLOUD_FILE_INCOMPATIBLE_HARDLINKS: Final = 396
ERROR_CLOUD_FILE_PROPERTY_LOCK_CONFLICT: Final = 397
ERROR_CLOUD_FILE_REQUEST_CANCELED: Final = 398
ERROR_EXTERNAL_SYSKEY_NOT_SUPPORTED: Final = 399
ERROR_THREAD_MODE_ALREADY_BACKGROUND: Final = 400
ERROR_THREAD_MODE_NOT_BACKGROUND: Final = 401
ERROR_PROCESS_MODE_ALREADY_BACKGROUND: Final = 402
ERROR_PROCESS_MODE_NOT_BACKGROUND: Final = 403
ERROR_CLOUD_FILE_PROVIDER_TERMINATED: Final = 404
ERROR_NOT_A_CLOUD_SYNC_ROOT: Final = 405
ERROR_FILE_PROTECTED_UNDER_DPL: Final = 406
ERROR_VOLUME_NOT_CLUSTER_ALIGNED: Final = 407
ERROR_NO_PHYSICALLY_ALIGNED_FREE_SPACE_FOUND: Final = 408
ERROR_APPX_FILE_NOT_ENCRYPTED: Final = 409
ERROR_RWRAW_ENCRYPTED_FILE_NOT_ENCRYPTED: Final = 410
ERROR_RWRAW_ENCRYPTED_INVALID_EDATAINFO_FILEOFFSET: Final = 411
ERROR_RWRAW_ENCRYPTED_INVALID_EDATAINFO_FILERANGE: Final = 412
ERROR_RWRAW_ENCRYPTED_INVALID_EDATAINFO_PARAMETER: Final = 413
ERROR_LINUX_SUBSYSTEM_NOT_PRESENT: Final = 414
ERROR_FT_READ_FAILURE: Final = 415
ERROR_STORAGE_RESERVE_ID_INVALID: Final = 416
ERROR_STORAGE_RESERVE_DOES_NOT_EXIST: Final = 417
ERROR_STORAGE_RESERVE_ALREADY_EXISTS: Final = 418
ERROR_STORAGE_RESERVE_NOT_EMPTY: Final = 419
ERROR_NOT_A_DAX_VOLUME: Final = 420
ERROR_NOT_DAX_MAPPABLE: Final = 421
ERROR_TIME_SENSITIVE_THREAD: Final = 422
ERROR_DPL_NOT_SUPPORTED_FOR_USER: Final = 423
ERROR_CASE_DIFFERING_NAMES_IN_DIR: Final = 424
ERROR_FILE_NOT_SUPPORTED: Final = 425
ERROR_CLOUD_FILE_REQUEST_TIMEOUT: Final = 426
ERROR_NO_TASK_QUEUE: Final = 427
ERROR_SRC_SRV_DLL_LOAD_FAILED: Final = 428
ERROR_NOT_SUPPORTED_WITH_BTT: Final = 429
ERROR_ENCRYPTION_DISABLED: Final = 430
ERROR_ENCRYPTING_METADATA_DISALLOWED: Final = 431
ERROR_CANT_CLEAR_ENCRYPTION_FLAG: Final = 432
ERROR_NO_SUCH_DEVICE: Final = 433
ERROR_CLOUD_FILE_DEHYDRATION_DISALLOWED: Final = 434
ERROR_FILE_SNAP_IN_PROGRESS: Final = 435
ERROR_FILE_SNAP_USER_SECTION_NOT_SUPPORTED: Final = 436
ERROR_FILE_SNAP_MODIFY_NOT_SUPPORTED: Final = 437
ERROR_FILE_SNAP_IO_NOT_COORDINATED: Final = 438
ERROR_FILE_SNAP_UNEXPECTED_ERROR: Final = 439
ERROR_FILE_SNAP_INVALID_PARAMETER: Final = 440
ERROR_UNSATISFIED_DEPENDENCIES: Final = 441
ERROR_CASE_SENSITIVE_PATH: Final = 442
ERROR_UNEXPECTED_NTCACHEMANAGER_ERROR: Final = 443
ERROR_LINUX_SUBSYSTEM_UPDATE_REQUIRED: Final = 444
ERROR_DLP_POLICY_WARNS_AGAINST_OPERATION: Final = 445
ERROR_DLP_POLICY_DENIES_OPERATION: Final = 446
ERROR_SECURITY_DENIES_OPERATION: Final = 447
ERROR_UNTRUSTED_MOUNT_POINT: Final = 448
ERROR_DLP_POLICY_SILENTLY_FAIL: Final = 449
ERROR_CAPAUTHZ_NOT_DEVUNLOCKED: Final = 450
ERROR_CAPAUTHZ_CHANGE_TYPE: Final = 451
ERROR_CAPAUTHZ_NOT_PROVISIONED: Final = 452
ERROR_CAPAUTHZ_NOT_AUTHORIZED: Final = 453
ERROR_CAPAUTHZ_NO_POLICY: Final = 454
ERROR_CAPAUTHZ_DB_CORRUPTED: Final = 455
ERROR_CAPAUTHZ_SCCD_INVALID_CATALOG: Final = 456
ERROR_CAPAUTHZ_SCCD_NO_AUTH_ENTITY: Final = 457
ERROR_CAPAUTHZ_SCCD_PARSE_ERROR: Final = 458
ERROR_CAPAUTHZ_SCCD_DEV_MODE_REQUIRED: Final = 459
ERROR_CAPAUTHZ_SCCD_NO_CAPABILITY_MATCH: Final = 460
ERROR_CIMFS_IMAGE_CORRUPT: Final = 470
ERROR_CIMFS_IMAGE_VERSION_NOT_SUPPORTED: Final = 471
ERROR_STORAGE_STACK_ACCESS_DENIED: Final = 472
ERROR_INSUFFICIENT_VIRTUAL_ADDR_RESOURCES: Final = 473
ERROR_INDEX_OUT_OF_BOUNDS: Final = 474
ERROR_CLOUD_FILE_US_MESSAGE_TIMEOUT: Final = 475
ERROR_NOT_A_DEV_VOLUME: Final = 476
ERROR_FS_GUID_MISMATCH: Final = 477
ERROR_CANT_ATTACH_TO_DEV_VOLUME: Final = 478
ERROR_INVALID_CONFIG_VALUE: Final = 479
ERROR_PNP_QUERY_REMOVE_DEVICE_TIMEOUT: Final = 480
ERROR_PNP_QUERY_REMOVE_RELATED_DEVICE_TIMEOUT: Final = 481
ERROR_PNP_QUERY_REMOVE_UNRELATED_DEVICE_TIMEOUT: Final = 482
ERROR_DEVICE_HARDWARE_ERROR: Final = 483
ERROR_INVALID_ADDRESS: Final = 487
ERROR_HAS_SYSTEM_CRITICAL_FILES: Final = 488
ERROR_ENCRYPTED_FILE_NOT_SUPPORTED: Final = 489
ERROR_SPARSE_FILE_NOT_SUPPORTED: Final = 490
ERROR_PAGEFILE_NOT_SUPPORTED: Final = 491
ERROR_VOLUME_NOT_SUPPORTED: Final = 492
ERROR_NOT_SUPPORTED_WITH_BYPASSIO: Final = 493
ERROR_NO_BYPASSIO_DRIVER_SUPPORT: Final = 494
ERROR_NOT_SUPPORTED_WITH_ENCRYPTION: Final = 495
ERROR_NOT_SUPPORTED_WITH_COMPRESSION: Final = 496
ERROR_NOT_SUPPORTED_WITH_REPLICATION: Final = 497
ERROR_NOT_SUPPORTED_WITH_DEDUPLICATION: Final = 498
ERROR_NOT_SUPPORTED_WITH_AUDITING: Final = 499
ERROR_USER_PROFILE_LOAD: Final = 500
ERROR_SESSION_KEY_TOO_SHORT: Final = 501
ERROR_ACCESS_DENIED_APPDATA: Final = 502
ERROR_NOT_SUPPORTED_WITH_MONITORING: Final = 503
ERROR_NOT_SUPPORTED_WITH_SNAPSHOT: Final = 504
ERROR_NOT_SUPPORTED_WITH_VIRTUALIZATION: Final = 505
ERROR_BYPASSIO_FLT_NOT_SUPPORTED: Final = 506
ERROR_DEVICE_RESET_REQUIRED: Final = 507
ERROR_VOLUME_WRITE_ACCESS_DENIED: Final = 508
ERROR_NOT_SUPPORTED_WITH_CACHED_HANDLE: Final = 509
ERROR_FS_METADATA_INCONSISTENT: Final = 510
ERROR_BLOCK_WEAK_REFERENCE_INVALID: Final = 511
ERROR_BLOCK_SOURCE_WEAK_REFERENCE_INVALID: Final = 512
ERROR_BLOCK_TARGET_WEAK_REFERENCE_INVALID: Final = 513
ERROR_BLOCK_SHARED: Final = 514
ERROR_VOLUME_UPGRADE_NOT_NEEDED: Final = 515
ERROR_VOLUME_UPGRADE_PENDING: Final = 516
ERROR_VOLUME_UPGRADE_DISABLED: Final = 517
ERROR_VOLUME_UPGRADE_DISABLED_TILL_OS_DOWNGRADE_EXPIRED: Final = 518
ERROR_ARITHMETIC_OVERFLOW: Final = 534
ERROR_PIPE_CONNECTED: Final = 535
ERROR_PIPE_LISTENING: Final = 536
ERROR_VERIFIER_STOP: Final = 537
ERROR_ABIOS_ERROR: Final = 538
ERROR_WX86_WARNING: Final = 539
ERROR_WX86_ERROR: Final = 540
ERROR_TIMER_NOT_CANCELED: Final = 541
ERROR_UNWIND: Final = 542
ERROR_BAD_STACK: Final = 543
ERROR_INVALID_UNWIND_TARGET: Final = 544
ERROR_INVALID_PORT_ATTRIBUTES: Final = 545
ERROR_PORT_MESSAGE_TOO_LONG: Final = 546
ERROR_INVALID_QUOTA_LOWER: Final = 547
ERROR_DEVICE_ALREADY_ATTACHED: Final = 548
ERROR_INSTRUCTION_MISALIGNMENT: Final = 549
ERROR_PROFILING_NOT_STARTED: Final = 550
ERROR_PROFILING_NOT_STOPPED: Final = 551
ERROR_COULD_NOT_INTERPRET: Final = 552
ERROR_PROFILING_AT_LIMIT: Final = 553
ERROR_CANT_WAIT: Final = 554
ERROR_CANT_TERMINATE_SELF: Final = 555
ERROR_UNEXPECTED_MM_CREATE_ERR: Final = 556
ERROR_UNEXPECTED_MM_MAP_ERROR: Final = 557
ERROR_UNEXPECTED_MM_EXTEND_ERR: Final = 558
ERROR_BAD_FUNCTION_TABLE: Final = 559
ERROR_NO_GUID_TRANSLATION: Final = 560
ERROR_INVALID_LDT_SIZE: Final = 561
ERROR_INVALID_LDT_OFFSET: Final = 563
ERROR_INVALID_LDT_DESCRIPTOR: Final = 564
ERROR_TOO_MANY_THREADS: Final = 565
ERROR_THREAD_NOT_IN_PROCESS: Final = 566
ERROR_PAGEFILE_QUOTA_EXCEEDED: Final = 567
ERROR_LOGON_SERVER_CONFLICT: Final = 568
ERROR_SYNCHRONIZATION_REQUIRED: Final = 569
ERROR_NET_OPEN_FAILED: Final = 570
ERROR_IO_PRIVILEGE_FAILED: Final = 571
ERROR_CONTROL_C_EXIT: Final = 572
ERROR_MISSING_SYSTEMFILE: Final = 573
ERROR_UNHANDLED_EXCEPTION: Final = 574
ERROR_APP_INIT_FAILURE: Final = 575
ERROR_PAGEFILE_CREATE_FAILED: Final = 576
ERROR_INVALID_IMAGE_HASH: Final = 577
ERROR_NO_PAGEFILE: Final = 578
ERROR_ILLEGAL_FLOAT_CONTEXT: Final = 579
ERROR_NO_EVENT_PAIR: Final = 580
ERROR_DOMAIN_CTRLR_CONFIG_ERROR: Final = 581
ERROR_ILLEGAL_CHARACTER: Final = 582
ERROR_UNDEFINED_CHARACTER: Final = 583
ERROR_FLOPPY_VOLUME: Final = 584
ERROR_BIOS_FAILED_TO_CONNECT_INTERRUPT: Final = 585
ERROR_BACKUP_CONTROLLER: Final = 586
ERROR_MUTANT_LIMIT_EXCEEDED: Final = 587
ERROR_FS_DRIVER_REQUIRED: Final = 588
ERROR_CANNOT_LOAD_REGISTRY_FILE: Final = 589
ERROR_DEBUG_ATTACH_FAILED: Final = 590
ERROR_SYSTEM_PROCESS_TERMINATED: Final = 591
ERROR_DATA_NOT_ACCEPTED: Final = 592
ERROR_VDM_HARD_ERROR: Final = 593
ERROR_DRIVER_CANCEL_TIMEOUT: Final = 594
ERROR_REPLY_MESSAGE_MISMATCH: Final = 595
ERROR_LOST_WRITEBEHIND_DATA: Final = 596
ERROR_CLIENT_SERVER_PARAMETERS_INVALID: Final = 597
ERROR_NOT_TINY_STREAM: Final = 598
ERROR_STACK_OVERFLOW_READ: Final = 599
ERROR_CONVERT_TO_LARGE: Final = 600
ERROR_FOUND_OUT_OF_SCOPE: Final = 601
ERROR_ALLOCATE_BUCKET: Final = 602
ERROR_MARSHALL_OVERFLOW: Final = 603
ERROR_INVALID_VARIANT: Final = 604
ERROR_BAD_COMPRESSION_BUFFER: Final = 605
ERROR_AUDIT_FAILED: Final = 606
ERROR_TIMER_RESOLUTION_NOT_SET: Final = 607
ERROR_INSUFFICIENT_LOGON_INFO: Final = 608
ERROR_BAD_DLL_ENTRYPOINT: Final = 609
ERROR_BAD_SERVICE_ENTRYPOINT: Final = 610
ERROR_IP_ADDRESS_CONFLICT1: Final = 611
ERROR_IP_ADDRESS_CONFLICT2: Final = 612
ERROR_REGISTRY_QUOTA_LIMIT: Final = 613
ERROR_NO_CALLBACK_ACTIVE: Final = 614
ERROR_PWD_TOO_SHORT: Final = 615
ERROR_PWD_TOO_RECENT: Final = 616
ERROR_PWD_HISTORY_CONFLICT: Final = 617
ERROR_UNSUPPORTED_COMPRESSION: Final = 618
ERROR_INVALID_HW_PROFILE: Final = 619
ERROR_INVALID_PLUGPLAY_DEVICE_PATH: Final = 620
ERROR_QUOTA_LIST_INCONSISTENT: Final = 621
ERROR_EVALUATION_EXPIRATION: Final = 622
ERROR_ILLEGAL_DLL_RELOCATION: Final = 623
ERROR_DLL_INIT_FAILED_LOGOFF: Final = 624
ERROR_VALIDATE_CONTINUE: Final = 625
ERROR_NO_MORE_MATCHES: Final = 626
ERROR_RANGE_LIST_CONFLICT: Final = 627
ERROR_SERVER_SID_MISMATCH: Final = 628
ERROR_CANT_ENABLE_DENY_ONLY: Final = 629
ERROR_FLOAT_MULTIPLE_FAULTS: Final = 630
ERROR_FLOAT_MULTIPLE_TRAPS: Final = 631
ERROR_NOINTERFACE: Final = 632
ERROR_DRIVER_FAILED_SLEEP: Final = 633
ERROR_CORRUPT_SYSTEM_FILE: Final = 634
ERROR_COMMITMENT_MINIMUM: Final = 635
ERROR_PNP_RESTART_ENUMERATION: Final = 636
ERROR_SYSTEM_IMAGE_BAD_SIGNATURE: Final = 637
ERROR_PNP_REBOOT_REQUIRED: Final = 638
ERROR_INSUFFICIENT_POWER: Final = 639
ERROR_MULTIPLE_FAULT_VIOLATION: Final = 640
ERROR_SYSTEM_SHUTDOWN: Final = 641
ERROR_PORT_NOT_SET: Final = 642
ERROR_DS_VERSION_CHECK_FAILURE: Final = 643
ERROR_RANGE_NOT_FOUND: Final = 644
ERROR_NOT_SAFE_MODE_DRIVER: Final = 646
ERROR_FAILED_DRIVER_ENTRY: Final = 647
ERROR_DEVICE_ENUMERATION_ERROR: Final = 648
ERROR_MOUNT_POINT_NOT_RESOLVED: Final = 649
ERROR_INVALID_DEVICE_OBJECT_PARAMETER: Final = 650
ERROR_MCA_OCCURED: Final = 651
ERROR_DRIVER_DATABASE_ERROR: Final = 652
ERROR_SYSTEM_HIVE_TOO_LARGE: Final = 653
ERROR_DRIVER_FAILED_PRIOR_UNLOAD: Final = 654
ERROR_VOLSNAP_PREPARE_HIBERNATE: Final = 655
ERROR_HIBERNATION_FAILURE: Final = 656
ERROR_PWD_TOO_LONG: Final = 657
ERROR_FILE_SYSTEM_LIMITATION: Final = 665
ERROR_ASSERTION_FAILURE: Final = 668
ERROR_ACPI_ERROR: Final = 669
ERROR_WOW_ASSERTION: Final = 670
ERROR_PNP_BAD_MPS_TABLE: Final = 671
ERROR_PNP_TRANSLATION_FAILED: Final = 672
ERROR_PNP_IRQ_TRANSLATION_FAILED: Final = 673
ERROR_PNP_INVALID_ID: Final = 674
ERROR_WAKE_SYSTEM_DEBUGGER: Final = 675
ERROR_HANDLES_CLOSED: Final = 676
ERROR_EXTRANEOUS_INFORMATION: Final = 677
ERROR_RXACT_COMMIT_NECESSARY: Final = 678
ERROR_MEDIA_CHECK: Final = 679
ERROR_GUID_SUBSTITUTION_MADE: Final = 680
ERROR_STOPPED_ON_SYMLINK: Final = 681
ERROR_LONGJUMP: Final = 682
ERROR_PLUGPLAY_QUERY_VETOED: Final = 683
ERROR_UNWIND_CONSOLIDATE: Final = 684
ERROR_REGISTRY_HIVE_RECOVERED: Final = 685
ERROR_DLL_MIGHT_BE_INSECURE: Final = 686
ERROR_DLL_MIGHT_BE_INCOMPATIBLE: Final = 687
ERROR_DBG_EXCEPTION_NOT_HANDLED: Final = 688
ERROR_DBG_REPLY_LATER: Final = 689
ERROR_DBG_UNABLE_TO_PROVIDE_HANDLE: Final = 690
ERROR_DBG_TERMINATE_THREAD: Final = 691
ERROR_DBG_TERMINATE_PROCESS: Final = 692
ERROR_DBG_CONTROL_C: Final = 693
ERROR_DBG_PRINTEXCEPTION_C: Final = 694
ERROR_DBG_RIPEXCEPTION: Final = 695
ERROR_DBG_CONTROL_BREAK: Final = 696
ERROR_DBG_COMMAND_EXCEPTION: Final = 697
ERROR_OBJECT_NAME_EXISTS: Final = 698
ERROR_THREAD_WAS_SUSPENDED: Final = 699
ERROR_IMAGE_NOT_AT_BASE: Final = 700
ERROR_RXACT_STATE_CREATED: Final = 701
ERROR_SEGMENT_NOTIFICATION: Final = 702
ERROR_BAD_CURRENT_DIRECTORY: Final = 703
ERROR_FT_READ_RECOVERY_FROM_BACKUP: Final = 704
ERROR_FT_WRITE_RECOVERY: Final = 705
ERROR_IMAGE_MACHINE_TYPE_MISMATCH: Final = 706
ERROR_RECEIVE_PARTIAL: Final = 707
ERROR_RECEIVE_EXPEDITED: Final = 708
ERROR_RECEIVE_PARTIAL_EXPEDITED: Final = 709
ERROR_EVENT_DONE: Final = 710
ERROR_EVENT_PENDING: Final = 711
ERROR_CHECKING_FILE_SYSTEM: Final = 712
ERROR_FATAL_APP_EXIT: Final = 713
ERROR_PREDEFINED_HANDLE: Final = 714
ERROR_WAS_UNLOCKED: Final = 715
ERROR_SERVICE_NOTIFICATION: Final = 716
ERROR_WAS_LOCKED: Final = 717
ERROR_LOG_HARD_ERROR: Final = 718
ERROR_ALREADY_WIN32: Final = 719
ERROR_IMAGE_MACHINE_TYPE_MISMATCH_EXE: Final = 720
ERROR_NO_YIELD_PERFORMED: Final = 721
ERROR_TIMER_RESUME_IGNORED: Final = 722
ERROR_ARBITRATION_UNHANDLED: Final = 723
ERROR_CARDBUS_NOT_SUPPORTED: Final = 724
ERROR_MP_PROCESSOR_MISMATCH: Final = 725
ERROR_HIBERNATED: Final = 726
ERROR_RESUME_HIBERNATION: Final = 727
ERROR_FIRMWARE_UPDATED: Final = 728
ERROR_DRIVERS_LEAKING_LOCKED_PAGES: Final = 729
ERROR_WAKE_SYSTEM: Final = 730
ERROR_WAIT_1: Final = 731
ERROR_WAIT_2: Final = 732
ERROR_WAIT_3: Final = 733
ERROR_WAIT_63: Final = 734
ERROR_ABANDONED_WAIT_0: Final = 735
ERROR_ABANDONED_WAIT_63: Final = 736
ERROR_USER_APC: Final = 737
ERROR_KERNEL_APC: Final = 738
ERROR_ALERTED: Final = 739
ERROR_ELEVATION_REQUIRED: Final = 740
ERROR_REPARSE: Final = 741
ERROR_OPLOCK_BREAK_IN_PROGRESS: Final = 742
ERROR_VOLUME_MOUNTED: Final = 743
ERROR_RXACT_COMMITTED: Final = 744
ERROR_NOTIFY_CLEANUP: Final = 745
ERROR_PRIMARY_TRANSPORT_CONNECT_FAILED: Final = 746
ERROR_PAGE_FAULT_TRANSITION: Final = 747
ERROR_PAGE_FAULT_DEMAND_ZERO: Final = 748
ERROR_PAGE_FAULT_COPY_ON_WRITE: Final = 749
ERROR_PAGE_FAULT_GUARD_PAGE: Final = 750
ERROR_PAGE_FAULT_PAGING_FILE: Final = 751
ERROR_CACHE_PAGE_LOCKED: Final = 752
ERROR_CRASH_DUMP: Final = 753
ERROR_BUFFER_ALL_ZEROS: Final = 754
ERROR_REPARSE_OBJECT: Final = 755
ERROR_RESOURCE_REQUIREMENTS_CHANGED: Final = 756
ERROR_TRANSLATION_COMPLETE: Final = 757
ERROR_NOTHING_TO_TERMINATE: Final = 758
ERROR_PROCESS_NOT_IN_JOB: Final = 759
ERROR_PROCESS_IN_JOB: Final = 760
ERROR_VOLSNAP_HIBERNATE_READY: Final = 761
ERROR_FSFILTER_OP_COMPLETED_SUCCESSFULLY: Final = 762
ERROR_INTERRUPT_VECTOR_ALREADY_CONNECTED: Final = 763
ERROR_INTERRUPT_STILL_CONNECTED: Final = 764
ERROR_WAIT_FOR_OPLOCK: Final = 765
ERROR_DBG_EXCEPTION_HANDLED: Final = 766
ERROR_DBG_CONTINUE: Final = 767
ERROR_CALLBACK_POP_STACK: Final = 768
ERROR_COMPRESSION_DISABLED: Final = 769
ERROR_CANTFETCHBACKWARDS: Final = 770
ERROR_CANTSCROLLBACKWARDS: Final = 771
ERROR_ROWSNOTRELEASED: Final = 772
ERROR_BAD_ACCESSOR_FLAGS: Final = 773
ERROR_ERRORS_ENCOUNTERED: Final = 774
ERROR_NOT_CAPABLE: Final = 775
ERROR_REQUEST_OUT_OF_SEQUENCE: Final = 776
ERROR_VERSION_PARSE_ERROR: Final = 777
ERROR_BADSTARTPOSITION: Final = 778
ERROR_MEMORY_HARDWARE: Final = 779
ERROR_DISK_REPAIR_DISABLED: Final = 780
ERROR_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE: Final = 781
ERROR_SYSTEM_POWERSTATE_TRANSITION: Final = 782
ERROR_SYSTEM_POWERSTATE_COMPLEX_TRANSITION: Final = 783
ERROR_MCA_EXCEPTION: Final = 784
ERROR_ACCESS_AUDIT_BY_POLICY: Final = 785
ERROR_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY: Final = 786
ERROR_ABANDON_HIBERFILE: Final = 787
ERROR_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED: Final = 788
ERROR_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR: Final = 789
ERROR_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR: Final = 790
ERROR_BAD_MCFG_TABLE: Final = 791
ERROR_DISK_REPAIR_REDIRECTED: Final = 792
ERROR_DISK_REPAIR_UNSUCCESSFUL: Final = 793
ERROR_CORRUPT_LOG_OVERFULL: Final = 794
ERROR_CORRUPT_LOG_CORRUPTED: Final = 795
ERROR_CORRUPT_LOG_UNAVAILABLE: Final = 796
ERROR_CORRUPT_LOG_DELETED_FULL: Final = 797
ERROR_CORRUPT_LOG_CLEARED: Final = 798
ERROR_ORPHAN_NAME_EXHAUSTED: Final = 799
ERROR_OPLOCK_SWITCHED_TO_NEW_HANDLE: Final = 800
ERROR_CANNOT_GRANT_REQUESTED_OPLOCK: Final = 801
ERROR_CANNOT_BREAK_OPLOCK: Final = 802
ERROR_OPLOCK_HANDLE_CLOSED: Final = 803
ERROR_NO_ACE_CONDITION: Final = 804
ERROR_INVALID_ACE_CONDITION: Final = 805
ERROR_FILE_HANDLE_REVOKED: Final = 806
ERROR_IMAGE_AT_DIFFERENT_BASE: Final = 807
ERROR_ENCRYPTED_IO_NOT_POSSIBLE: Final = 808
ERROR_FILE_METADATA_OPTIMIZATION_IN_PROGRESS: Final = 809
ERROR_QUOTA_ACTIVITY: Final = 810
ERROR_HANDLE_REVOKED: Final = 811
ERROR_CALLBACK_INVOKE_INLINE: Final = 812
ERROR_CPU_SET_INVALID: Final = 813
ERROR_ENCLAVE_NOT_TERMINATED: Final = 814
ERROR_ENCLAVE_VIOLATION: Final = 815
ERROR_SERVER_TRANSPORT_CONFLICT: Final = 816
ERROR_CERTIFICATE_VALIDATION_PREFERENCE_CONFLICT: Final = 817
ERROR_FT_READ_FROM_COPY_FAILURE: Final = 818
ERROR_SECTION_DIRECT_MAP_ONLY: Final = 819
ERROR_EA_ACCESS_DENIED: Final = 994
ERROR_OPERATION_ABORTED: Final = 995
ERROR_IO_INCOMPLETE: Final = 996
ERROR_IO_PENDING: Final = 997
ERROR_NOACCESS: Final = 998
ERROR_SWAPERROR: Final = 999
ERROR_STACK_OVERFLOW: Final = 1001
ERROR_INVALID_MESSAGE: Final = 1002
ERROR_CAN_NOT_COMPLETE: Final = 1003
ERROR_INVALID_FLAGS: Final = 1004
ERROR_UNRECOGNIZED_VOLUME: Final = 1005
ERROR_FILE_INVALID: Final = 1006
ERROR_FULLSCREEN_MODE: Final = 1007
ERROR_NO_TOKEN: Final = 1008
ERROR_BADDB: Final = 1009
ERROR_BADKEY: Final = 1010
ERROR_CANTOPEN: Final = 1011
ERROR_CANTREAD: Final = 1012
ERROR_CANTWRITE: Final = 1013
ERROR_REGISTRY_RECOVERED: Final = 1014
ERROR_REGISTRY_CORRUPT: Final = 1015
ERROR_REGISTRY_IO_FAILED: Final = 1016
ERROR_NOT_REGISTRY_FILE: Final = 1017
ERROR_KEY_DELETED: Final = 1018
ERROR_NO_LOG_SPACE: Final = 1019
ERROR_KEY_HAS_CHILDREN: Final = 1020
ERROR_CHILD_MUST_BE_VOLATILE: Final = 1021
ERROR_NOTIFY_ENUM_DIR: Final = 1022
ERROR_DEPENDENT_SERVICES_RUNNING: Final = 1051
ERROR_INVALID_SERVICE_CONTROL: Final = 1052
ERROR_SERVICE_REQUEST_TIMEOUT: Final = 1053
ERROR_SERVICE_NO_THREAD: Final = 1054
ERROR_SERVICE_DATABASE_LOCKED: Final = 1055
ERROR_SERVICE_ALREADY_RUNNING: Final = 1056
ERROR_INVALID_SERVICE_ACCOUNT: Final = 1057
ERROR_SERVICE_DISABLED: Final = 1058
ERROR_CIRCULAR_DEPENDENCY: Final = 1059
ERROR_SERVICE_DOES_NOT_EXIST: Final = 1060
ERROR_SERVICE_CANNOT_ACCEPT_CTRL: Final = 1061
ERROR_SERVICE_NOT_ACTIVE: Final = 1062
ERROR_FAILED_SERVICE_CONTROLLER_CONNECT: Final = 1063
ERROR_EXCEPTION_IN_SERVICE: Final = 1064
ERROR_DATABASE_DOES_NOT_EXIST: Final = 1065
ERROR_SERVICE_SPECIFIC_ERROR: Final = 1066
ERROR_PROCESS_ABORTED: Final = 1067
ERROR_SERVICE_DEPENDENCY_FAIL: Final = 1068
ERROR_SERVICE_LOGON_FAILED: Final = 1069
ERROR_SERVICE_START_HANG: Final = 1070
ERROR_INVALID_SERVICE_LOCK: Final = 1071
ERROR_SERVICE_MARKED_FOR_DELETE: Final = 1072
ERROR_SERVICE_EXISTS: Final = 1073
ERROR_ALREADY_RUNNING_LKG: Final = 1074
ERROR_SERVICE_DEPENDENCY_DELETED: Final = 1075
ERROR_BOOT_ALREADY_ACCEPTED: Final = 1076
ERROR_SERVICE_NEVER_STARTED: Final = 1077
ERROR_DUPLICATE_SERVICE_NAME: Final = 1078
ERROR_DIFFERENT_SERVICE_ACCOUNT: Final = 1079
ERROR_CANNOT_DETECT_DRIVER_FAILURE: Final = 1080
ERROR_CANNOT_DETECT_PROCESS_ABORT: Final = 1081
ERROR_NO_RECOVERY_PROGRAM: Final = 1082
ERROR_SERVICE_NOT_IN_EXE: Final = 1083
ERROR_NOT_SAFEBOOT_SERVICE: Final = 1084
ERROR_END_OF_MEDIA: Final = 1100
ERROR_FILEMARK_DETECTED: Final = 1101
ERROR_BEGINNING_OF_MEDIA: Final = 1102
ERROR_SETMARK_DETECTED: Final = 1103
ERROR_NO_DATA_DETECTED: Final = 1104
ERROR_PARTITION_FAILURE: Final = 1105
ERROR_INVALID_BLOCK_LENGTH: Final = 1106
ERROR_DEVICE_NOT_PARTITIONED: Final = 1107
ERROR_UNABLE_TO_LOCK_MEDIA: Final = 1108
ERROR_UNABLE_TO_UNLOAD_MEDIA: Final = 1109
ERROR_MEDIA_CHANGED: Final = 1110
ERROR_BUS_RESET: Final = 1111
ERROR_NO_MEDIA_IN_DRIVE: Final = 1112
ERROR_NO_UNICODE_TRANSLATION: Final = 1113
ERROR_DLL_INIT_FAILED: Final = 1114
ERROR_SHUTDOWN_IN_PROGRESS: Final = 1115
ERROR_NO_SHUTDOWN_IN_PROGRESS: Final = 1116
ERROR_IO_DEVICE: Final = 1117
ERROR_SERIAL_NO_DEVICE: Final = 1118
ERROR_IRQ_BUSY: Final = 1119
ERROR_MORE_WRITES: Final = 1120
ERROR_COUNTER_TIMEOUT: Final = 1121
ERROR_FLOPPY_ID_MARK_NOT_FOUND: Final = 1122
ERROR_FLOPPY_WRONG_CYLINDER: Final = 1123
ERROR_FLOPPY_UNKNOWN_ERROR: Final = 1124
ERROR_FLOPPY_BAD_REGISTERS: Final = 1125
ERROR_DISK_RECALIBRATE_FAILED: Final = 1126
ERROR_DISK_OPERATION_FAILED: Final = 1127
ERROR_DISK_RESET_FAILED: Final = 1128
ERROR_EOM_OVERFLOW: Final = 1129
ERROR_NOT_ENOUGH_SERVER_MEMORY: Final = 1130
ERROR_POSSIBLE_DEADLOCK: Final = 1131
ERROR_MAPPED_ALIGNMENT: Final = 1132
ERROR_SET_POWER_STATE_VETOED: Final = 1140
ERROR_SET_POWER_STATE_FAILED: Final = 1141
ERROR_TOO_MANY_LINKS: Final = 1142
ERROR_OLD_WIN_VERSION: Final = 1150
ERROR_APP_WRONG_OS: Final = 1151
ERROR_SINGLE_INSTANCE_APP: Final = 1152
ERROR_RMODE_APP: Final = 1153
ERROR_INVALID_DLL: Final = 1154
ERROR_NO_ASSOCIATION: Final = 1155
ERROR_DDE_FAIL: Final = 1156
ERROR_DLL_NOT_FOUND: Final = 1157
ERROR_NO_MORE_USER_HANDLES: Final = 1158
ERROR_MESSAGE_SYNC_ONLY: Final = 1159
ERROR_SOURCE_ELEMENT_EMPTY: Final = 1160
ERROR_DESTINATION_ELEMENT_FULL: Final = 1161
ERROR_ILLEGAL_ELEMENT_ADDRESS: Final = 1162
ERROR_MAGAZINE_NOT_PRESENT: Final = 1163
ERROR_DEVICE_REINITIALIZATION_NEEDED: Final = 1164
ERROR_DEVICE_REQUIRES_CLEANING: Final = 1165
ERROR_DEVICE_DOOR_OPEN: Final = 1166
ERROR_DEVICE_NOT_CONNECTED: Final = 1167
ERROR_NOT_FOUND: Final = 1168
ERROR_NO_MATCH: Final = 1169
ERROR_SET_NOT_FOUND: Final = 1170
ERROR_POINT_NOT_FOUND: Final = 1171
ERROR_NO_TRACKING_SERVICE: Final = 1172
ERROR_NO_VOLUME_ID: Final = 1173
ERROR_UNABLE_TO_REMOVE_REPLACED: Final = 1175
ERROR_UNABLE_TO_MOVE_REPLACEMENT: Final = 1176
ERROR_UNABLE_TO_MOVE_REPLACEMENT_2: Final = 1177
ERROR_JOURNAL_DELETE_IN_PROGRESS: Final = 1178
ERROR_JOURNAL_NOT_ACTIVE: Final = 1179
ERROR_POTENTIAL_FILE_FOUND: Final = 1180
ERROR_JOURNAL_ENTRY_DELETED: Final = 1181
ERROR_PARTITION_TERMINATING: Final = 1184
ERROR_SHUTDOWN_IS_SCHEDULED: Final = 1190
ERROR_SHUTDOWN_USERS_LOGGED_ON: Final = 1191
ERROR_SHUTDOWN_DISKS_NOT_IN_MAINTENANCE_MODE: Final = 1192
ERROR_BAD_DEVICE: Final = 1200
ERROR_CONNECTION_UNAVAIL: Final = 1201
ERROR_DEVICE_ALREADY_REMEMBERED: Final = 1202
ERROR_NO_NET_OR_BAD_PATH: Final = 1203
ERROR_BAD_PROVIDER: Final = 1204
ERROR_CANNOT_OPEN_PROFILE: Final = 1205
ERROR_BAD_PROFILE: Final = 1206
ERROR_NOT_CONTAINER: Final = 1207
ERROR_EXTENDED_ERROR: Final = 1208
ERROR_INVALID_GROUPNAME: Final = 1209
ERROR_INVALID_COMPUTERNAME: Final = 1210
ERROR_INVALID_EVENTNAME: Final = 1211
ERROR_INVALID_DOMAINNAME: Final = 1212
ERROR_INVALID_SERVICENAME: Final = 1213
ERROR_INVALID_NETNAME: Final = 1214
ERROR_INVALID_SHARENAME: Final = 1215
ERROR_INVALID_PASSWORDNAME: Final = 1216
ERROR_INVALID_MESSAGENAME: Final = 1217
ERROR_INVALID_MESSAGEDEST: Final = 1218
ERROR_SESSION_CREDENTIAL_CONFLICT: Final = 1219
ERROR_REMOTE_SESSION_LIMIT_EXCEEDED: Final = 1220
ERROR_DUP_DOMAINNAME: Final = 1221
ERROR_NO_NETWORK: Final = 1222
ERROR_CANCELLED: Final = 1223
ERROR_USER_MAPPED_FILE: Final = 1224
ERROR_CONNECTION_REFUSED: Final = 1225
ERROR_GRACEFUL_DISCONNECT: Final = 1226
ERROR_ADDRESS_ALREADY_ASSOCIATED: Final = 1227
ERROR_ADDRESS_NOT_ASSOCIATED: Final = 1228
ERROR_CONNECTION_INVALID: Final = 1229
ERROR_CONNECTION_ACTIVE: Final = 1230
ERROR_NETWORK_UNREACHABLE: Final = 1231
ERROR_HOST_UNREACHABLE: Final = 1232
ERROR_PROTOCOL_UNREACHABLE: Final = 1233
ERROR_PORT_UNREACHABLE: Final = 1234
ERROR_REQUEST_ABORTED: Final = 1235
ERROR_CONNECTION_ABORTED: Final = 1236
ERROR_RETRY: Final = 1237
ERROR_CONNECTION_COUNT_LIMIT: Final = 1238
ERROR_LOGIN_TIME_RESTRICTION: Final = 1239
ERROR_LOGIN_WKSTA_RESTRICTION: Final = 1240
ERROR_INCORRECT_ADDRESS: Final = 1241
ERROR_ALREADY_REGISTERED: Final = 1242
ERROR_SERVICE_NOT_FOUND: Final = 1243
ERROR_NOT_AUTHENTICATED: Final = 1244
ERROR_NOT_LOGGED_ON: Final = 1245
ERROR_CONTINUE: Final = 1246
ERROR_ALREADY_INITIALIZED: Final = 1247
ERROR_NO_MORE_DEVICES: Final = 1248
ERROR_NO_SUCH_SITE: Final = 1249
ERROR_DOMAIN_CONTROLLER_EXISTS: Final = 1250
ERROR_ONLY_IF_CONNECTED: Final = 1251
ERROR_OVERRIDE_NOCHANGES: Final = 1252
ERROR_BAD_USER_PROFILE: Final = 1253
ERROR_NOT_SUPPORTED_ON_SBS: Final = 1254
ERROR_SERVER_SHUTDOWN_IN_PROGRESS: Final = 1255
ERROR_HOST_DOWN: Final = 1256
ERROR_NON_ACCOUNT_SID: Final = 1257
ERROR_NON_DOMAIN_SID: Final = 1258
ERROR_APPHELP_BLOCK: Final = 1259
ERROR_ACCESS_DISABLED_BY_POLICY: Final = 1260
ERROR_REG_NAT_CONSUMPTION: Final = 1261
ERROR_CSCSHARE_OFFLINE: Final = 1262
ERROR_PKINIT_FAILURE: Final = 1263
ERROR_SMARTCARD_SUBSYSTEM_FAILURE: Final = 1264
ERROR_DOWNGRADE_DETECTED: Final = 1265
ERROR_MACHINE_LOCKED: Final = 1271
ERROR_SMB_GUEST_LOGON_BLOCKED: Final = 1272
ERROR_CALLBACK_SUPPLIED_INVALID_DATA: Final = 1273
ERROR_SYNC_FOREGROUND_REFRESH_REQUIRED: Final = 1274
ERROR_DRIVER_BLOCKED: Final = 1275
ERROR_INVALID_IMPORT_OF_NON_DLL: Final = 1276
ERROR_ACCESS_DISABLED_WEBBLADE: Final = 1277
ERROR_ACCESS_DISABLED_WEBBLADE_TAMPER: Final = 1278
ERROR_RECOVERY_FAILURE: Final = 1279
ERROR_ALREADY_FIBER: Final = 1280
ERROR_ALREADY_THREAD: Final = 1281
ERROR_STACK_BUFFER_OVERRUN: Final = 1282
ERROR_PARAMETER_QUOTA_EXCEEDED: Final = 1283
ERROR_DEBUGGER_INACTIVE: Final = 1284
ERROR_DELAY_LOAD_FAILED: Final = 1285
ERROR_VDM_DISALLOWED: Final = 1286
ERROR_UNIDENTIFIED_ERROR: Final = 1287
ERROR_INVALID_CRUNTIME_PARAMETER: Final = 1288
ERROR_BEYOND_VDL: Final = 1289
ERROR_INCOMPATIBLE_SERVICE_SID_TYPE: Final = 1290
ERROR_DRIVER_PROCESS_TERMINATED: Final = 1291
ERROR_IMPLEMENTATION_LIMIT: Final = 1292
ERROR_PROCESS_IS_PROTECTED: Final = 1293
ERROR_SERVICE_NOTIFY_CLIENT_LAGGING: Final = 1294
ERROR_DISK_QUOTA_EXCEEDED: Final = 1295
ERROR_CONTENT_BLOCKED: Final = 1296
ERROR_INCOMPATIBLE_SERVICE_PRIVILEGE: Final = 1297
ERROR_APP_HANG: Final = 1298
ERROR_INVALID_LABEL: Final = 1299
ERROR_NOT_ALL_ASSIGNED: Final = 1300
ERROR_SOME_NOT_MAPPED: Final = 1301
ERROR_NO_QUOTAS_FOR_ACCOUNT: Final = 1302
ERROR_LOCAL_USER_SESSION_KEY: Final = 1303
ERROR_NULL_LM_PASSWORD: Final = 1304
ERROR_UNKNOWN_REVISION: Final = 1305
ERROR_REVISION_MISMATCH: Final = 1306
ERROR_INVALID_OWNER: Final = 1307
ERROR_INVALID_PRIMARY_GROUP: Final = 1308
ERROR_NO_IMPERSONATION_TOKEN: Final = 1309
ERROR_CANT_DISABLE_MANDATORY: Final = 1310
ERROR_NO_LOGON_SERVERS: Final = 1311
ERROR_NO_SUCH_LOGON_SESSION: Final = 1312
ERROR_NO_SUCH_PRIVILEGE: Final = 1313
ERROR_PRIVILEGE_NOT_HELD: Final = 1314
ERROR_INVALID_ACCOUNT_NAME: Final = 1315
ERROR_USER_EXISTS: Final = 1316
ERROR_NO_SUCH_USER: Final = 1317
ERROR_GROUP_EXISTS: Final = 1318
ERROR_NO_SUCH_GROUP: Final = 1319
ERROR_MEMBER_IN_GROUP: Final = 1320
ERROR_MEMBER_NOT_IN_GROUP: Final = 1321
ERROR_LAST_ADMIN: Final = 1322
ERROR_WRONG_PASSWORD: Final = 1323
ERROR_ILL_FORMED_PASSWORD: Final = 1324
ERROR_PASSWORD_RESTRICTION: Final = 1325
ERROR_LOGON_FAILURE: Final = 1326
ERROR_ACCOUNT_RESTRICTION: Final = 1327
ERROR_INVALID_LOGON_HOURS: Final = 1328
ERROR_INVALID_WORKSTATION: Final = 1329
ERROR_PASSWORD_EXPIRED: Final = 1330
ERROR_ACCOUNT_DISABLED: Final = 1331
ERROR_NONE_MAPPED: Final = 1332
ERROR_TOO_MANY_LUIDS_REQUESTED: Final = 1333
ERROR_LUIDS_EXHAUSTED: Final = 1334
ERROR_INVALID_SUB_AUTHORITY: Final = 1335
ERROR_INVALID_ACL: Final = 1336
ERROR_INVALID_SID: Final = 1337
ERROR_INVALID_SECURITY_DESCR: Final = 1338
ERROR_BAD_INHERITANCE_ACL: Final = 1340
ERROR_SERVER_DISABLED: Final = 1341
ERROR_SERVER_NOT_DISABLED: Final = 1342
ERROR_INVALID_ID_AUTHORITY: Final = 1343
ERROR_ALLOTTED_SPACE_EXCEEDED: Final = 1344
ERROR_INVALID_GROUP_ATTRIBUTES: Final = 1345
ERROR_BAD_IMPERSONATION_LEVEL: Final = 1346
ERROR_CANT_OPEN_ANONYMOUS: Final = 1347
ERROR_BAD_VALIDATION_CLASS: Final = 1348
ERROR_BAD_TOKEN_TYPE: Final = 1349
ERROR_NO_SECURITY_ON_OBJECT: Final = 1350
ERROR_CANT_ACCESS_DOMAIN_INFO: Final = 1351
ERROR_INVALID_SERVER_STATE: Final = 1352
ERROR_INVALID_DOMAIN_STATE: Final = 1353
ERROR_INVALID_DOMAIN_ROLE: Final = 1354
ERROR_NO_SUCH_DOMAIN: Final = 1355
ERROR_DOMAIN_EXISTS: Final = 1356
ERROR_DOMAIN_LIMIT_EXCEEDED: Final = 1357
ERROR_INTERNAL_DB_CORRUPTION: Final = 1358
ERROR_INTERNAL_ERROR: Final = 1359
ERROR_GENERIC_NOT_MAPPED: Final = 1360
ERROR_BAD_DESCRIPTOR_FORMAT: Final = 1361
ERROR_NOT_LOGON_PROCESS: Final = 1362
ERROR_LOGON_SESSION_EXISTS: Final = 1363
ERROR_NO_SUCH_PACKAGE: Final = 1364
ERROR_BAD_LOGON_SESSION_STATE: Final = 1365
ERROR_LOGON_SESSION_COLLISION: Final = 1366
ERROR_INVALID_LOGON_TYPE: Final = 1367
ERROR_CANNOT_IMPERSONATE: Final = 1368
ERROR_RXACT_INVALID_STATE: Final = 1369
ERROR_RXACT_COMMIT_FAILURE: Final = 1370
ERROR_SPECIAL_ACCOUNT: Final = 1371
ERROR_SPECIAL_GROUP: Final = 1372
ERROR_SPECIAL_USER: Final = 1373
ERROR_MEMBERS_PRIMARY_GROUP: Final = 1374
ERROR_TOKEN_ALREADY_IN_USE: Final = 1375
ERROR_NO_SUCH_ALIAS: Final = 1376
ERROR_MEMBER_NOT_IN_ALIAS: Final = 1377
ERROR_MEMBER_IN_ALIAS: Final = 1378
ERROR_ALIAS_EXISTS: Final = 1379
ERROR_LOGON_NOT_GRANTED: Final = 1380
ERROR_TOO_MANY_SECRETS: Final = 1381
ERROR_SECRET_TOO_LONG: Final = 1382
ERROR_INTERNAL_DB_ERROR: Final = 1383
ERROR_TOO_MANY_CONTEXT_IDS: Final = 1384
ERROR_LOGON_TYPE_NOT_GRANTED: Final = 1385
ERROR_NT_CROSS_ENCRYPTION_REQUIRED: Final = 1386
ERROR_NO_SUCH_MEMBER: Final = 1387
ERROR_INVALID_MEMBER: Final = 1388
ERROR_TOO_MANY_SIDS: Final = 1389
ERROR_LM_CROSS_ENCRYPTION_REQUIRED: Final = 1390
ERROR_NO_INHERITANCE: Final = 1391
ERROR_FILE_CORRUPT: Final = 1392
ERROR_DISK_CORRUPT: Final = 1393
ERROR_NO_USER_SESSION_KEY: Final = 1394
ERROR_LICENSE_QUOTA_EXCEEDED: Final = 1395
ERROR_WRONG_TARGET_NAME: Final = 1396
ERROR_MUTUAL_AUTH_FAILED: Final = 1397
ERROR_TIME_SKEW: Final = 1398
ERROR_CURRENT_DOMAIN_NOT_ALLOWED: Final = 1399
ERROR_INVALID_WINDOW_HANDLE: Final = 1400
ERROR_INVALID_MENU_HANDLE: Final = 1401
ERROR_INVALID_CURSOR_HANDLE: Final = 1402
ERROR_INVALID_ACCEL_HANDLE: Final = 1403
ERROR_INVALID_HOOK_HANDLE: Final = 1404
ERROR_INVALID_DWP_HANDLE: Final = 1405
ERROR_TLW_WITH_WSCHILD: Final = 1406
ERROR_CANNOT_FIND_WND_CLASS: Final = 1407
ERROR_WINDOW_OF_OTHER_THREAD: Final = 1408
ERROR_HOTKEY_ALREADY_REGISTERED: Final = 1409
ERROR_CLASS_ALREADY_EXISTS: Final = 1410
ERROR_CLASS_DOES_NOT_EXIST: Final = 1411
ERROR_CLASS_HAS_WINDOWS: Final = 1412
ERROR_INVALID_INDEX: Final = 1413
ERROR_INVALID_ICON_HANDLE: Final = 1414
ERROR_PRIVATE_DIALOG_INDEX: Final = 1415
ERROR_LISTBOX_ID_NOT_FOUND: Final = 1416
ERROR_NO_WILDCARD_CHARACTERS: Final = 1417
ERROR_CLIPBOARD_NOT_OPEN: Final = 1418
ERROR_HOTKEY_NOT_REGISTERED: Final = 1419
ERROR_WINDOW_NOT_DIALOG: Final = 1420
ERROR_CONTROL_ID_NOT_FOUND: Final = 1421
ERROR_INVALID_COMBOBOX_MESSAGE: Final = 1422
ERROR_WINDOW_NOT_COMBOBOX: Final = 1423
ERROR_INVALID_EDIT_HEIGHT: Final = 1424
ERROR_DC_NOT_FOUND: Final = 1425
ERROR_INVALID_HOOK_FILTER: Final = 1426
ERROR_INVALID_FILTER_PROC: Final = 1427
ERROR_HOOK_NEEDS_HMOD: Final = 1428
ERROR_GLOBAL_ONLY_HOOK: Final = 1429
ERROR_JOURNAL_HOOK_SET: Final = 1430
ERROR_HOOK_NOT_INSTALLED: Final = 1431
ERROR_INVALID_LB_MESSAGE: Final = 1432
ERROR_SETCOUNT_ON_BAD_LB: Final = 1433
ERROR_LB_WITHOUT_TABSTOPS: Final = 1434
ERROR_DESTROY_OBJECT_OF_OTHER_THREAD: Final = 1435
ERROR_CHILD_WINDOW_MENU: Final = 1436
ERROR_NO_SYSTEM_MENU: Final = 1437
ERROR_INVALID_MSGBOX_STYLE: Final = 1438
ERROR_INVALID_SPI_VALUE: Final = 1439
ERROR_SCREEN_ALREADY_LOCKED: Final = 1440
ERROR_HWNDS_HAVE_DIFF_PARENT: Final = 1441
ERROR_NOT_CHILD_WINDOW: Final = 1442
ERROR_INVALID_GW_COMMAND: Final = 1443
ERROR_INVALID_THREAD_ID: Final = 1444
ERROR_NON_MDICHILD_WINDOW: Final = 1445
ERROR_POPUP_ALREADY_ACTIVE: Final = 1446
ERROR_NO_SCROLLBARS: Final = 1447
ERROR_INVALID_SCROLLBAR_RANGE: Final = 1448
ERROR_INVALID_SHOWWIN_COMMAND: Final = 1449
ERROR_NO_SYSTEM_RESOURCES: Final = 1450
ERROR_NONPAGED_SYSTEM_RESOURCES: Final = 1451
ERROR_PAGED_SYSTEM_RESOURCES: Final = 1452
ERROR_WORKING_SET_QUOTA: Final = 1453
ERROR_PAGEFILE_QUOTA: Final = 1454
ERROR_COMMITMENT_LIMIT: Final = 1455
ERROR_MENU_ITEM_NOT_FOUND: Final = 1456
ERROR_INVALID_KEYBOARD_HANDLE: Final = 1457
ERROR_HOOK_TYPE_NOT_ALLOWED: Final = 1458
ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION: Final = 1459
ERROR_TIMEOUT: Final = 1460
ERROR_INVALID_MONITOR_HANDLE: Final = 1461
ERROR_INCORRECT_SIZE: Final = 1462
ERROR_SYMLINK_CLASS_DISABLED: Final = 1463
ERROR_SYMLINK_NOT_SUPPORTED: Final = 1464
ERROR_XML_PARSE_ERROR: Final = 1465
ERROR_XMLDSIG_ERROR: Final = 1466
ERROR_RESTART_APPLICATION: Final = 1467
ERROR_WRONG_COMPARTMENT: Final = 1468
ERROR_AUTHIP_FAILURE: Final = 1469
ERROR_NO_NVRAM_RESOURCES: Final = 1470
ERROR_NOT_GUI_PROCESS: Final = 1471
ERROR_EVENTLOG_FILE_CORRUPT: Final = 1500
ERROR_EVENTLOG_CANT_START: Final = 1501
ERROR_LOG_FILE_FULL: Final = 1502
ERROR_EVENTLOG_FILE_CHANGED: Final = 1503
ERROR_CONTAINER_ASSIGNED: Final = 1504
ERROR_JOB_NO_CONTAINER: Final = 1505
ERROR_INVALID_TASK_NAME: Final = 1550
ERROR_INVALID_TASK_INDEX: Final = 1551
ERROR_THREAD_ALREADY_IN_TASK: Final = 1552
ERROR_INSTALL_SERVICE_FAILURE: Final = 1601
ERROR_INSTALL_USEREXIT: Final = 1602
ERROR_INSTALL_FAILURE: Final = 1603
ERROR_INSTALL_SUSPEND: Final = 1604
ERROR_UNKNOWN_PRODUCT: Final = 1605
ERROR_UNKNOWN_FEATURE: Final = 1606
ERROR_UNKNOWN_COMPONENT: Final = 1607
ERROR_UNKNOWN_PROPERTY: Final = 1608
ERROR_INVALID_HANDLE_STATE: Final = 1609
ERROR_BAD_CONFIGURATION: Final = 1610
ERROR_INDEX_ABSENT: Final = 1611
ERROR_INSTALL_SOURCE_ABSENT: Final = 1612
ERROR_INSTALL_PACKAGE_VERSION: Final = 1613
ERROR_PRODUCT_UNINSTALLED: Final = 1614
ERROR_BAD_QUERY_SYNTAX: Final = 1615
ERROR_INVALID_FIELD: Final = 1616
ERROR_DEVICE_REMOVED: Final = 1617
ERROR_INSTALL_ALREADY_RUNNING: Final = 1618
ERROR_INSTALL_PACKAGE_OPEN_FAILED: Final = 1619
ERROR_INSTALL_PACKAGE_INVALID: Final = 1620
ERROR_INSTALL_UI_FAILURE: Final = 1621
ERROR_INSTALL_LOG_FAILURE: Final = 1622
ERROR_INSTALL_LANGUAGE_UNSUPPORTED: Final = 1623
ERROR_INSTALL_TRANSFORM_FAILURE: Final = 1624
ERROR_INSTALL_PACKAGE_REJECTED: Final = 1625
ERROR_FUNCTION_NOT_CALLED: Final = 1626
ERROR_FUNCTION_FAILED: Final = 1627
ERROR_INVALID_TABLE: Final = 1628
ERROR_DATATYPE_MISMATCH: Final = 1629
ERROR_UNSUPPORTED_TYPE: Final = 1630
ERROR_CREATE_FAILED: Final = 1631
ERROR_INSTALL_TEMP_UNWRITABLE: Final = 1632
ERROR_INSTALL_PLATFORM_UNSUPPORTED: Final = 1633
ERROR_INSTALL_NOTUSED: Final = 1634
ERROR_PATCH_PACKAGE_OPEN_FAILED: Final = 1635
ERROR_PATCH_PACKAGE_INVALID: Final = 1636
ERROR_PATCH_PACKAGE_UNSUPPORTED: Final = 1637
ERROR_PRODUCT_VERSION: Final = 1638
ERROR_INVALID_COMMAND_LINE: Final = 1639
ERROR_INSTALL_REMOTE_DISALLOWED: Final = 1640
ERROR_SUCCESS_REBOOT_INITIATED: Final = 1641
ERROR_PATCH_TARGET_NOT_FOUND: Final = 1642
ERROR_PATCH_PACKAGE_REJECTED: Final = 1643
ERROR_INSTALL_TRANSFORM_REJECTED: Final = 1644
ERROR_INSTALL_REMOTE_PROHIBITED: Final = 1645
ERROR_PATCH_REMOVAL_UNSUPPORTED: Final = 1646
ERROR_UNKNOWN_PATCH: Final = 1647
ERROR_PATCH_NO_SEQUENCE: Final = 1648
ERROR_PATCH_REMOVAL_DISALLOWED: Final = 1649
ERROR_INVALID_PATCH_XML: Final = 1650
ERROR_PATCH_MANAGED_ADVERTISED_PRODUCT: Final = 1651
ERROR_INSTALL_SERVICE_SAFEBOOT: Final = 1652
ERROR_FAIL_FAST_EXCEPTION: Final = 1653
ERROR_INSTALL_REJECTED: Final = 1654
ERROR_DYNAMIC_CODE_BLOCKED: Final = 1655
ERROR_NOT_SAME_OBJECT: Final = 1656
ERROR_STRICT_CFG_VIOLATION: Final = 1657
ERROR_SET_CONTEXT_DENIED: Final = 1660
ERROR_CROSS_PARTITION_VIOLATION: Final = 1661
ERROR_RETURN_ADDRESS_HIJACK_ATTEMPT: Final = 1662
RPC_S_INVALID_STRING_BINDING: Final = 1700
RPC_S_WRONG_KIND_OF_BINDING: Final = 1701
RPC_S_INVALID_BINDING: Final = 1702
RPC_S_PROTSEQ_NOT_SUPPORTED: Final = 1703
RPC_S_INVALID_RPC_PROTSEQ: Final = 1704
RPC_S_INVALID_STRING_UUID: Final = 1705
RPC_S_INVALID_ENDPOINT_FORMAT: Final = 1706
RPC_S_INVALID_NET_ADDR: Final = 1707
RPC_S_NO_ENDPOINT_FOUND: Final = 1708
RPC_S_INVALID_TIMEOUT: Final = 1709
RPC_S_OBJECT_NOT_FOUND: Final = 1710
RPC_S_ALREADY_REGISTERED: Final = 1711
RPC_S_TYPE_ALREADY_REGISTERED: Final = 1712
RPC_S_ALREADY_LISTENING: Final = 1713
RPC_S_NO_PROTSEQS_REGISTERED: Final = 1714
RPC_S_NOT_LISTENING: Final = 1715
RPC_S_UNKNOWN_MGR_TYPE: Final = 1716
RPC_S_UNKNOWN_IF: Final = 1717
RPC_S_NO_BINDINGS: Final = 1718
RPC_S_NO_PROTSEQS: Final = 1719
RPC_S_CANT_CREATE_ENDPOINT: Final = 1720
RPC_S_OUT_OF_RESOURCES: Final = 1721
RPC_S_SERVER_UNAVAILABLE: Final = 1722
RPC_S_SERVER_TOO_BUSY: Final = 1723
RPC_S_INVALID_NETWORK_OPTIONS: Final = 1724
RPC_S_NO_CALL_ACTIVE: Final = 1725
RPC_S_CALL_FAILED: Final = 1726
RPC_S_CALL_FAILED_DNE: Final = 1727
RPC_S_PROTOCOL_ERROR: Final = 1728
RPC_S_PROXY_ACCESS_DENIED: Final = 1729
RPC_S_UNSUPPORTED_TRANS_SYN: Final = 1730
RPC_S_UNSUPPORTED_TYPE: Final = 1732
RPC_S_INVALID_TAG: Final = 1733
RPC_S_INVALID_BOUND: Final = 1734
RPC_S_NO_ENTRY_NAME: Final = 1735
RPC_S_INVALID_NAME_SYNTAX: Final = 1736
RPC_S_UNSUPPORTED_NAME_SYNTAX: Final = 1737
RPC_S_UUID_NO_ADDRESS: Final = 1739
RPC_S_DUPLICATE_ENDPOINT: Final = 1740
RPC_S_UNKNOWN_AUTHN_TYPE: Final = 1741
RPC_S_MAX_CALLS_TOO_SMALL: Final = 1742
RPC_S_STRING_TOO_LONG: Final = 1743
RPC_S_PROTSEQ_NOT_FOUND: Final = 1744
RPC_S_PROCNUM_OUT_OF_RANGE: Final = 1745
RPC_S_BINDING_HAS_NO_AUTH: Final = 1746
RPC_S_UNKNOWN_AUTHN_SERVICE: Final = 1747
RPC_S_UNKNOWN_AUTHN_LEVEL: Final = 1748
RPC_S_INVALID_AUTH_IDENTITY: Final = 1749
RPC_S_UNKNOWN_AUTHZ_SERVICE: Final = 1750
EPT_S_INVALID_ENTRY: Final = 1751
EPT_S_CANT_PERFORM_OP: Final = 1752
EPT_S_NOT_REGISTERED: Final = 1753
RPC_S_NOTHING_TO_EXPORT: Final = 1754
RPC_S_INCOMPLETE_NAME: Final = 1755
RPC_S_INVALID_VERS_OPTION: Final = 1756
RPC_S_NO_MORE_MEMBERS: Final = 1757
RPC_S_NOT_ALL_OBJS_UNEXPORTED: Final = 1758
RPC_S_INTERFACE_NOT_FOUND: Final = 1759
RPC_S_ENTRY_ALREADY_EXISTS: Final = 1760
RPC_S_ENTRY_NOT_FOUND: Final = 1761
RPC_S_NAME_SERVICE_UNAVAILABLE: Final = 1762
RPC_S_INVALID_NAF_ID: Final = 1763
RPC_S_CANNOT_SUPPORT: Final = 1764
RPC_S_NO_CONTEXT_AVAILABLE: Final = 1765
RPC_S_INTERNAL_ERROR: Final = 1766
RPC_S_ZERO_DIVIDE: Final = 1767
RPC_S_ADDRESS_ERROR: Final = 1768
RPC_S_FP_DIV_ZERO: Final = 1769
RPC_S_FP_UNDERFLOW: Final = 1770
RPC_S_FP_OVERFLOW: Final = 1771
RPC_X_NO_MORE_ENTRIES: Final = 1772
RPC_X_SS_CHAR_TRANS_OPEN_FAIL: Final = 1773
RPC_X_SS_CHAR_TRANS_SHORT_FILE: Final = 1774
RPC_X_SS_IN_NULL_CONTEXT: Final = 1775
RPC_X_SS_CONTEXT_DAMAGED: Final = 1777
RPC_X_SS_HANDLES_MISMATCH: Final = 1778
RPC_X_SS_CANNOT_GET_CALL_HANDLE: Final = 1779
RPC_X_NULL_REF_POINTER: Final = 1780
RPC_X_ENUM_VALUE_OUT_OF_RANGE: Final = 1781
RPC_X_BYTE_COUNT_TOO_SMALL: Final = 1782
RPC_X_BAD_STUB_DATA: Final = 1783
ERROR_INVALID_USER_BUFFER: Final = 1784
ERROR_UNRECOGNIZED_MEDIA: Final = 1785
ERROR_NO_TRUST_LSA_SECRET: Final = 1786
ERROR_NO_TRUST_SAM_ACCOUNT: Final = 1787
ERROR_TRUSTED_DOMAIN_FAILURE: Final = 1788
ERROR_TRUSTED_RELATIONSHIP_FAILURE: Final = 1789
ERROR_TRUST_FAILURE: Final = 1790
RPC_S_CALL_IN_PROGRESS: Final = 1791
ERROR_NETLOGON_NOT_STARTED: Final = 1792
ERROR_ACCOUNT_EXPIRED: Final = 1793
ERROR_REDIRECTOR_HAS_OPEN_HANDLES: Final = 1794
ERROR_PRINTER_DRIVER_ALREADY_INSTALLED: Final = 1795
ERROR_UNKNOWN_PORT: Final = 1796
ERROR_UNKNOWN_PRINTER_DRIVER: Final = 1797
ERROR_UNKNOWN_PRINTPROCESSOR: Final = 1798
ERROR_INVALID_SEPARATOR_FILE: Final = 1799
ERROR_INVALID_PRIORITY: Final = 1800
ERROR_INVALID_PRINTER_NAME: Final = 1801
ERROR_PRINTER_ALREADY_EXISTS: Final = 1802
ERROR_INVALID_PRINTER_COMMAND: Final = 1803
ERROR_INVALID_DATATYPE: Final = 1804
ERROR_INVALID_ENVIRONMENT: Final = 1805
RPC_S_NO_MORE_BINDINGS: Final = 1806
ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT: Final = 1807
ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT: Final = 1808
ERROR_NOLOGON_SERVER_TRUST_ACCOUNT: Final = 1809
ERROR_DOMAIN_TRUST_INCONSISTENT: Final = 1810
ERROR_SERVER_HAS_OPEN_HANDLES: Final = 1811
ERROR_RESOURCE_DATA_NOT_FOUND: Final = 1812
ERROR_RESOURCE_TYPE_NOT_FOUND: Final = 1813
ERROR_RESOURCE_NAME_NOT_FOUND: Final = 1814
ERROR_RESOURCE_LANG_NOT_FOUND: Final = 1815
ERROR_NOT_ENOUGH_QUOTA: Final = 1816
RPC_S_NO_INTERFACES: Final = 1817
RPC_S_CALL_CANCELLED: Final = 1818
RPC_S_BINDING_INCOMPLETE: Final = 1819
RPC_S_COMM_FAILURE: Final = 1820
RPC_S_UNSUPPORTED_AUTHN_LEVEL: Final = 1821
RPC_S_NO_PRINC_NAME: Final = 1822
RPC_S_NOT_RPC_ERROR: Final = 1823
RPC_S_UUID_LOCAL_ONLY: Final = 1824
RPC_S_SEC_PKG_ERROR: Final = 1825
RPC_S_NOT_CANCELLED: Final = 1826
RPC_X_INVALID_ES_ACTION: Final = 1827
RPC_X_WRONG_ES_VERSION: Final = 1828
RPC_X_WRONG_STUB_VERSION: Final = 1829
RPC_X_INVALID_PIPE_OBJECT: Final = 1830
RPC_X_WRONG_PIPE_ORDER: Final = 1831
RPC_X_WRONG_PIPE_VERSION: Final = 1832
RPC_S_COOKIE_AUTH_FAILED: Final = 1833
RPC_S_DO_NOT_DISTURB: Final = 1834
RPC_S_SYSTEM_HANDLE_COUNT_EXCEEDED: Final = 1835
RPC_S_SYSTEM_HANDLE_TYPE_MISMATCH: Final = 1836
RPC_S_GROUP_MEMBER_NOT_FOUND: Final = 1898
EPT_S_CANT_CREATE: Final = 1899
RPC_S_INVALID_OBJECT: Final = 1900
ERROR_INVALID_TIME: Final = 1901
ERROR_INVALID_FORM_NAME: Final = 1902
ERROR_INVALID_FORM_SIZE: Final = 1903
ERROR_ALREADY_WAITING: Final = 1904
ERROR_PRINTER_DELETED: Final = 1905
ERROR_INVALID_PRINTER_STATE: Final = 1906
ERROR_PASSWORD_MUST_CHANGE: Final = 1907
ERROR_DOMAIN_CONTROLLER_NOT_FOUND: Final = 1908
ERROR_ACCOUNT_LOCKED_OUT: Final = 1909
OR_INVALID_OXID: Final = 1910
OR_INVALID_OID: Final = 1911
OR_INVALID_SET: Final = 1912
RPC_S_SEND_INCOMPLETE: Final = 1913
RPC_S_INVALID_ASYNC_HANDLE: Final = 1914
RPC_S_INVALID_ASYNC_CALL: Final = 1915
RPC_X_PIPE_CLOSED: Final = 1916
RPC_X_PIPE_DISCIPLINE_ERROR: Final = 1917
RPC_X_PIPE_EMPTY: Final = 1918
ERROR_NO_SITENAME: Final = 1919
ERROR_CANT_ACCESS_FILE: Final = 1920
ERROR_CANT_RESOLVE_FILENAME: Final = 1921
RPC_S_ENTRY_TYPE_MISMATCH: Final = 1922
RPC_S_NOT_ALL_OBJS_EXPORTED: Final = 1923
RPC_S_INTERFACE_NOT_EXPORTED: Final = 1924
RPC_S_PROFILE_NOT_ADDED: Final = 1925
RPC_S_PRF_ELT_NOT_ADDED: Final = 1926
RPC_S_PRF_ELT_NOT_REMOVED: Final = 1927
RPC_S_GRP_ELT_NOT_ADDED: Final = 1928
RPC_S_GRP_ELT_NOT_REMOVED: Final = 1929
ERROR_KM_DRIVER_BLOCKED: Final = 1930
ERROR_CONTEXT_EXPIRED: Final = 1931
ERROR_PER_USER_TRUST_QUOTA_EXCEEDED: Final = 1932
ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED: Final = 1933
ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED: Final = 1934
ERROR_AUTHENTICATION_FIREWALL_FAILED: Final = 1935
ERROR_REMOTE_PRINT_CONNECTIONS_BLOCKED: Final = 1936
ERROR_NTLM_BLOCKED: Final = 1937
ERROR_PASSWORD_CHANGE_REQUIRED: Final = 1938
ERROR_LOST_MODE_LOGON_RESTRICTION: Final = 1939
ERROR_INVALID_PIXEL_FORMAT: Final = 2000
ERROR_BAD_DRIVER: Final = 2001
ERROR_INVALID_WINDOW_STYLE: Final = 2002
ERROR_METAFILE_NOT_SUPPORTED: Final = 2003
ERROR_TRANSFORM_NOT_SUPPORTED: Final = 2004
ERROR_CLIPPING_NOT_SUPPORTED: Final = 2005
ERROR_INVALID_CMM: Final = 2010
ERROR_INVALID_PROFILE: Final = 2011
ERROR_TAG_NOT_FOUND: Final = 2012
ERROR_TAG_NOT_PRESENT: Final = 2013
ERROR_DUPLICATE_TAG: Final = 2014
ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE: Final = 2015
ERROR_PROFILE_NOT_FOUND: Final = 2016
ERROR_INVALID_COLORSPACE: Final = 2017
ERROR_ICM_NOT_ENABLED: Final = 2018
ERROR_DELETING_ICM_XFORM: Final = 2019
ERROR_INVALID_TRANSFORM: Final = 2020
ERROR_COLORSPACE_MISMATCH: Final = 2021
ERROR_INVALID_COLORINDEX: Final = 2022
ERROR_PROFILE_DOES_NOT_MATCH_DEVICE: Final = 2023
ERROR_CONNECTED_OTHER_PASSWORD: Final = 2108
ERROR_CONNECTED_OTHER_PASSWORD_DEFAULT: Final = 2109
ERROR_BAD_USERNAME: Final = 2202
ERROR_NOT_CONNECTED: Final = 2250
ERROR_OPEN_FILES: Final = 2401
ERROR_ACTIVE_CONNECTIONS: Final = 2402
ERROR_DEVICE_IN_USE: Final = 2404
ERROR_UNKNOWN_PRINT_MONITOR: Final = 3000
ERROR_PRINTER_DRIVER_IN_USE: Final = 3001
ERROR_SPOOL_FILE_NOT_FOUND: Final = 3002
ERROR_SPL_NO_STARTDOC: Final = 3003
ERROR_SPL_NO_ADDJOB: Final = 3004
ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED: Final = 3005
ERROR_PRINT_MONITOR_ALREADY_INSTALLED: Final = 3006
ERROR_INVALID_PRINT_MONITOR: Final = 3007
ERROR_PRINT_MONITOR_IN_USE: Final = 3008
ERROR_PRINTER_HAS_JOBS_QUEUED: Final = 3009
ERROR_SUCCESS_REBOOT_REQUIRED: Final = 3010
ERROR_SUCCESS_RESTART_REQUIRED: Final = 3011
ERROR_PRINTER_NOT_FOUND: Final = 3012
ERROR_PRINTER_DRIVER_WARNED: Final = 3013
ERROR_PRINTER_DRIVER_BLOCKED: Final = 3014
ERROR_PRINTER_DRIVER_PACKAGE_IN_USE: Final = 3015
ERROR_CORE_DRIVER_PACKAGE_NOT_FOUND: Final = 3016
ERROR_FAIL_REBOOT_REQUIRED: Final = 3017
ERROR_FAIL_REBOOT_INITIATED: Final = 3018
ERROR_PRINTER_DRIVER_DOWNLOAD_NEEDED: Final = 3019
ERROR_PRINT_JOB_RESTART_REQUIRED: Final = 3020
ERROR_INVALID_PRINTER_DRIVER_MANIFEST: Final = 3021
ERROR_PRINTER_NOT_SHAREABLE: Final = 3022
ERROR_SERVER_SERVICE_CALL_REQUIRES_SMB1: Final = 3023
ERROR_NETWORK_AUTHENTICATION_PROMPT_CANCELED: Final = 3024
ERROR_REQUEST_PAUSED: Final = 3050
ERROR_APPEXEC_CONDITION_NOT_SATISFIED: Final = 3060
ERROR_APPEXEC_HANDLE_INVALIDATED: Final = 3061
ERROR_APPEXEC_INVALID_HOST_GENERATION: Final = 3062
ERROR_APPEXEC_UNEXPECTED_PROCESS_REGISTRATION: Final = 3063
ERROR_APPEXEC_INVALID_HOST_STATE: Final = 3064
ERROR_APPEXEC_NO_DONOR: Final = 3065
ERROR_APPEXEC_HOST_ID_MISMATCH: Final = 3066
ERROR_APPEXEC_UNKNOWN_USER: Final = 3067
ERROR_APPEXEC_APP_COMPAT_BLOCK: Final = 3068
ERROR_APPEXEC_CALLER_WAIT_TIMEOUT: Final = 3069
ERROR_APPEXEC_CALLER_WAIT_TIMEOUT_TERMINATION: Final = 3070
ERROR_APPEXEC_CALLER_WAIT_TIMEOUT_LICENSING: Final = 3071
ERROR_APPEXEC_CALLER_WAIT_TIMEOUT_RESOURCES: Final = 3072
ERROR_VRF_VOLATILE_CFG_AND_IO_ENABLED: Final = 3080
ERROR_VRF_VOLATILE_NOT_STOPPABLE: Final = 3081
ERROR_VRF_VOLATILE_SAFE_MODE: Final = 3082
ERROR_VRF_VOLATILE_NOT_RUNNABLE_SYSTEM: Final = 3083
ERROR_VRF_VOLATILE_NOT_SUPPORTED_RULECLASS: Final = 3084
ERROR_VRF_VOLATILE_PROTECTED_DRIVER: Final = 3085
ERROR_VRF_VOLATILE_NMI_REGISTERED: Final = 3086
ERROR_VRF_VOLATILE_SETTINGS_CONFLICT: Final = 3087
ERROR_DIF_IOCALLBACK_NOT_REPLACED: Final = 3190
ERROR_DIF_LIVEDUMP_LIMIT_EXCEEDED: Final = 3191
ERROR_DIF_VOLATILE_SECTION_NOT_LOCKED: Final = 3192
ERROR_DIF_VOLATILE_DRIVER_HOTPATCHED: Final = 3193
ERROR_DIF_VOLATILE_INVALID_INFO: Final = 3194
ERROR_DIF_VOLATILE_DRIVER_IS_NOT_RUNNING: Final = 3195
ERROR_DIF_VOLATILE_PLUGIN_IS_NOT_RUNNING: Final = 3196
ERROR_DIF_VOLATILE_PLUGIN_CHANGE_NOT_ALLOWED: Final = 3197
ERROR_DIF_VOLATILE_NOT_ALLOWED: Final = 3198
ERROR_DIF_BINDING_API_NOT_FOUND: Final = 3199
ERROR_IO_REISSUE_AS_CACHED: Final = 3950
ERROR_WINS_INTERNAL: Final = 4000
ERROR_CAN_NOT_DEL_LOCAL_WINS: Final = 4001
ERROR_STATIC_INIT: Final = 4002
ERROR_INC_BACKUP: Final = 4003
ERROR_FULL_BACKUP: Final = 4004
ERROR_REC_NON_EXISTENT: Final = 4005
ERROR_RPL_NOT_ALLOWED: Final = 4006
PEERDIST_ERROR_CONTENTINFO_VERSION_UNSUPPORTED: Final = 4050
PEERDIST_ERROR_CANNOT_PARSE_CONTENTINFO: Final = 4051
PEERDIST_ERROR_MISSING_DATA: Final = 4052
PEERDIST_ERROR_NO_MORE: Final = 4053
PEERDIST_ERROR_NOT_INITIALIZED: Final = 4054
PEERDIST_ERROR_ALREADY_INITIALIZED: Final = 4055
PEERDIST_ERROR_SHUTDOWN_IN_PROGRESS: Final = 4056
PEERDIST_ERROR_INVALIDATED: Final = 4057
PEERDIST_ERROR_ALREADY_EXISTS: Final = 4058
PEERDIST_ERROR_OPERATION_NOTFOUND: Final = 4059
PEERDIST_ERROR_ALREADY_COMPLETED: Final = 4060
PEERDIST_ERROR_OUT_OF_BOUNDS: Final = 4061
PEERDIST_ERROR_VERSION_UNSUPPORTED: Final = 4062
PEERDIST_ERROR_INVALID_CONFIGURATION: Final = 4063
PEERDIST_ERROR_NOT_LICENSED: Final = 4064
PEERDIST_ERROR_SERVICE_UNAVAILABLE: Final = 4065
PEERDIST_ERROR_TRUST_FAILURE: Final = 4066
ERROR_DHCP_ADDRESS_CONFLICT: Final = 4100
ERROR_WMI_GUID_NOT_FOUND: Final = 4200
ERROR_WMI_INSTANCE_NOT_FOUND: Final = 4201
ERROR_WMI_ITEMID_NOT_FOUND: Final = 4202
ERROR_WMI_TRY_AGAIN: Final = 4203
ERROR_WMI_DP_NOT_FOUND: Final = 4204
ERROR_WMI_UNRESOLVED_INSTANCE_REF: Final = 4205
ERROR_WMI_ALREADY_ENABLED: Final = 4206
ERROR_WMI_GUID_DISCONNECTED: Final = 4207
ERROR_WMI_SERVER_UNAVAILABLE: Final = 4208
ERROR_WMI_DP_FAILED: Final = 4209
ERROR_WMI_INVALID_MOF: Final = 4210
ERROR_WMI_INVALID_REGINFO: Final = 4211
ERROR_WMI_ALREADY_DISABLED: Final = 4212
ERROR_WMI_READ_ONLY: Final = 4213
ERROR_WMI_SET_FAILURE: Final = 4214
ERROR_NOT_APPCONTAINER: Final = 4250
ERROR_APPCONTAINER_REQUIRED: Final = 4251
ERROR_NOT_SUPPORTED_IN_APPCONTAINER: Final = 4252
ERROR_INVALID_PACKAGE_SID_LENGTH: Final = 4253
ERROR_INVALID_MEDIA: Final = 4300
ERROR_INVALID_LIBRARY: Final = 4301
ERROR_INVALID_MEDIA_POOL: Final = 4302
ERROR_DRIVE_MEDIA_MISMATCH: Final = 4303
ERROR_MEDIA_OFFLINE: Final = 4304
ERROR_LIBRARY_OFFLINE: Final = 4305
ERROR_EMPTY: Final = 4306
ERROR_NOT_EMPTY: Final = 4307
ERROR_MEDIA_UNAVAILABLE: Final = 4308
ERROR_RESOURCE_DISABLED: Final = 4309
ERROR_INVALID_CLEANER: Final = 4310
ERROR_UNABLE_TO_CLEAN: Final = 4311
ERROR_OBJECT_NOT_FOUND: Final = 4312
ERROR_DATABASE_FAILURE: Final = 4313
ERROR_DATABASE_FULL: Final = 4314
ERROR_MEDIA_INCOMPATIBLE: Final = 4315
ERROR_RESOURCE_NOT_PRESENT: Final = 4316
ERROR_INVALID_OPERATION: Final = 4317
ERROR_MEDIA_NOT_AVAILABLE: Final = 4318
ERROR_DEVICE_NOT_AVAILABLE: Final = 4319
ERROR_REQUEST_REFUSED: Final = 4320
ERROR_INVALID_DRIVE_OBJECT: Final = 4321
ERROR_LIBRARY_FULL: Final = 4322
ERROR_MEDIUM_NOT_ACCESSIBLE: Final = 4323
ERROR_UNABLE_TO_LOAD_MEDIUM: Final = 4324
ERROR_UNABLE_TO_INVENTORY_DRIVE: Final = 4325
ERROR_UNABLE_TO_INVENTORY_SLOT: Final = 4326
ERROR_UNABLE_TO_INVENTORY_TRANSPORT: Final = 4327
ERROR_TRANSPORT_FULL: Final = 4328
ERROR_CONTROLLING_IEPORT: Final = 4329
ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA: Final = 4330
ERROR_CLEANER_SLOT_SET: Final = 4331
ERROR_CLEANER_SLOT_NOT_SET: Final = 4332
ERROR_CLEANER_CARTRIDGE_SPENT: Final = 4333
ERROR_UNEXPECTED_OMID: Final = 4334
ERROR_CANT_DELETE_LAST_ITEM: Final = 4335
ERROR_MESSAGE_EXCEEDS_MAX_SIZE: Final = 4336
ERROR_VOLUME_CONTAINS_SYS_FILES: Final = 4337
ERROR_INDIGENOUS_TYPE: Final = 4338
ERROR_NO_SUPPORTING_DRIVES: Final = 4339
ERROR_CLEANER_CARTRIDGE_INSTALLED: Final = 4340
ERROR_IEPORT_FULL: Final = 4341
ERROR_FILE_OFFLINE: Final = 4350
ERROR_REMOTE_STORAGE_NOT_ACTIVE: Final = 4351
ERROR_REMOTE_STORAGE_MEDIA_ERROR: Final = 4352
ERROR_NOT_A_REPARSE_POINT: Final = 4390
ERROR_REPARSE_ATTRIBUTE_CONFLICT: Final = 4391
ERROR_INVALID_REPARSE_DATA: Final = 4392
ERROR_REPARSE_TAG_INVALID: Final = 4393
ERROR_REPARSE_TAG_MISMATCH: Final = 4394
ERROR_REPARSE_POINT_ENCOUNTERED: Final = 4395
ERROR_APP_DATA_NOT_FOUND: Final = 4400
ERROR_APP_DATA_EXPIRED: Final = 4401
ERROR_APP_DATA_CORRUPT: Final = 4402
ERROR_APP_DATA_LIMIT_EXCEEDED: Final = 4403
ERROR_APP_DATA_REBOOT_REQUIRED: Final = 4404
ERROR_SECUREBOOT_ROLLBACK_DETECTED: Final = 4420
ERROR_SECUREBOOT_POLICY_VIOLATION: Final = 4421
ERROR_SECUREBOOT_INVALID_POLICY: Final = 4422
ERROR_SECUREBOOT_POLICY_PUBLISHER_NOT_FOUND: Final = 4423
ERROR_SECUREBOOT_POLICY_NOT_SIGNED: Final = 4424
ERROR_SECUREBOOT_NOT_ENABLED: Final = 4425
ERROR_SECUREBOOT_FILE_REPLACED: Final = 4426
ERROR_SECUREBOOT_POLICY_NOT_AUTHORIZED: Final = 4427
ERROR_SECUREBOOT_POLICY_UNKNOWN: Final = 4428
ERROR_SECUREBOOT_POLICY_MISSING_ANTIROLLBACKVERSION: Final = 4429
ERROR_SECUREBOOT_PLATFORM_ID_MISMATCH: Final = 4430
ERROR_SECUREBOOT_POLICY_ROLLBACK_DETECTED: Final = 4431
ERROR_SECUREBOOT_POLICY_UPGRADE_MISMATCH: Final = 4432
ERROR_SECUREBOOT_REQUIRED_POLICY_FILE_MISSING: Final = 4433
ERROR_SECUREBOOT_NOT_BASE_POLICY: Final = 4434
ERROR_SECUREBOOT_NOT_SUPPLEMENTAL_POLICY: Final = 4435
ERROR_OFFLOAD_READ_FLT_NOT_SUPPORTED: Final = 4440
ERROR_OFFLOAD_WRITE_FLT_NOT_SUPPORTED: Final = 4441
ERROR_OFFLOAD_READ_FILE_NOT_SUPPORTED: Final = 4442
ERROR_OFFLOAD_WRITE_FILE_NOT_SUPPORTED: Final = 4443
ERROR_ALREADY_HAS_STREAM_ID: Final = 4444
ERROR_SMR_GARBAGE_COLLECTION_REQUIRED: Final = 4445
ERROR_WOF_WIM_HEADER_CORRUPT: Final = 4446
ERROR_WOF_WIM_RESOURCE_TABLE_CORRUPT: Final = 4447
ERROR_WOF_FILE_RESOURCE_TABLE_CORRUPT: Final = 4448
ERROR_OBJECT_IS_IMMUTABLE: Final = 4449
ERROR_VOLUME_NOT_SIS_ENABLED: Final = 4500
ERROR_SYSTEM_INTEGRITY_ROLLBACK_DETECTED: Final = 4550
ERROR_SYSTEM_INTEGRITY_POLICY_VIOLATION: Final = 4551
ERROR_SYSTEM_INTEGRITY_INVALID_POLICY: Final = 4552
ERROR_SYSTEM_INTEGRITY_POLICY_NOT_SIGNED: Final = 4553
ERROR_SYSTEM_INTEGRITY_TOO_MANY_POLICIES: Final = 4554
ERROR_SYSTEM_INTEGRITY_SUPPLEMENTAL_POLICY_NOT_AUTHORIZED: Final = 4555
ERROR_SYSTEM_INTEGRITY_REPUTATION_MALICIOUS: Final = 4556
ERROR_SYSTEM_INTEGRITY_REPUTATION_PUA: Final = 4557
ERROR_SYSTEM_INTEGRITY_REPUTATION_DANGEROUS_EXT: Final = 4558
ERROR_SYSTEM_INTEGRITY_REPUTATION_OFFLINE: Final = 4559
ERROR_VSM_NOT_INITIALIZED: Final = 4560
ERROR_VSM_DMA_PROTECTION_NOT_IN_USE: Final = 4561
ERROR_PLATFORM_MANIFEST_NOT_AUTHORIZED: Final = 4570
ERROR_PLATFORM_MANIFEST_INVALID: Final = 4571
ERROR_PLATFORM_MANIFEST_FILE_NOT_AUTHORIZED: Final = 4572
ERROR_PLATFORM_MANIFEST_CATALOG_NOT_AUTHORIZED: Final = 4573
ERROR_PLATFORM_MANIFEST_BINARY_ID_NOT_FOUND: Final = 4574
ERROR_PLATFORM_MANIFEST_NOT_ACTIVE: Final = 4575
ERROR_PLATFORM_MANIFEST_NOT_SIGNED: Final = 4576
ERROR_SYSTEM_INTEGRITY_REPUTATION_UNFRIENDLY_FILE: Final = 4580
ERROR_SYSTEM_INTEGRITY_REPUTATION_UNATTAINABLE: Final = 4581
ERROR_SYSTEM_INTEGRITY_REPUTATION_EXPLICIT_DENY_FILE: Final = 4582
ERROR_DEPENDENT_RESOURCE_EXISTS: Final = 5001
ERROR_DEPENDENCY_NOT_FOUND: Final = 5002
ERROR_DEPENDENCY_ALREADY_EXISTS: Final = 5003
ERROR_RESOURCE_NOT_ONLINE: Final = 5004
ERROR_HOST_NODE_NOT_AVAILABLE: Final = 5005
ERROR_RESOURCE_NOT_AVAILABLE: Final = 5006
ERROR_RESOURCE_NOT_FOUND: Final = 5007
ERROR_SHUTDOWN_CLUSTER: Final = 5008
ERROR_CANT_EVICT_ACTIVE_NODE: Final = 5009
ERROR_OBJECT_ALREADY_EXISTS: Final = 5010
ERROR_OBJECT_IN_LIST: Final = 5011
ERROR_GROUP_NOT_AVAILABLE: Final = 5012
ERROR_GROUP_NOT_FOUND: Final = 5013
ERROR_GROUP_NOT_ONLINE: Final = 5014
ERROR_HOST_NODE_NOT_RESOURCE_OWNER: Final = 5015
ERROR_HOST_NODE_NOT_GROUP_OWNER: Final = 5016
ERROR_RESMON_CREATE_FAILED: Final = 5017
ERROR_RESMON_ONLINE_FAILED: Final = 5018
ERROR_RESOURCE_ONLINE: Final = 5019
ERROR_QUORUM_RESOURCE: Final = 5020
ERROR_NOT_QUORUM_CAPABLE: Final = 5021
ERROR_CLUSTER_SHUTTING_DOWN: Final = 5022
ERROR_INVALID_STATE: Final = 5023
ERROR_RESOURCE_PROPERTIES_STORED: Final = 5024
ERROR_NOT_QUORUM_CLASS: Final = 5025
ERROR_CORE_RESOURCE: Final = 5026
ERROR_QUORUM_RESOURCE_ONLINE_FAILED: Final = 5027
ERROR_QUORUMLOG_OPEN_FAILED: Final = 5028
ERROR_CLUSTERLOG_CORRUPT: Final = 5029
ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE: Final = 5030
ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE: Final = 5031
ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND: Final = 5032
ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE: Final = 5033
ERROR_QUORUM_OWNER_ALIVE: Final = 5034
ERROR_NETWORK_NOT_AVAILABLE: Final = 5035
ERROR_NODE_NOT_AVAILABLE: Final = 5036
ERROR_ALL_NODES_NOT_AVAILABLE: Final = 5037
ERROR_RESOURCE_FAILED: Final = 5038
ERROR_CLUSTER_INVALID_NODE: Final = 5039
ERROR_CLUSTER_NODE_EXISTS: Final = 5040
ERROR_CLUSTER_JOIN_IN_PROGRESS: Final = 5041
ERROR_CLUSTER_NODE_NOT_FOUND: Final = 5042
ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND: Final = 5043
ERROR_CLUSTER_NETWORK_EXISTS: Final = 5044
ERROR_CLUSTER_NETWORK_NOT_FOUND: Final = 5045
ERROR_CLUSTER_NETINTERFACE_EXISTS: Final = 5046
ERROR_CLUSTER_NETINTERFACE_NOT_FOUND: Final = 5047
ERROR_CLUSTER_INVALID_REQUEST: Final = 5048
ERROR_CLUSTER_INVALID_NETWORK_PROVIDER: Final = 5049
ERROR_CLUSTER_NODE_DOWN: Final = 5050
ERROR_CLUSTER_NODE_UNREACHABLE: Final = 5051
ERROR_CLUSTER_NODE_NOT_MEMBER: Final = 5052
ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS: Final = 5053
ERROR_CLUSTER_INVALID_NETWORK: Final = 5054
ERROR_CLUSTER_NODE_UP: Final = 5056
ERROR_CLUSTER_IPADDR_IN_USE: Final = 5057
ERROR_CLUSTER_NODE_NOT_PAUSED: Final = 5058
ERROR_CLUSTER_NO_SECURITY_CONTEXT: Final = 5059
ERROR_CLUSTER_NETWORK_NOT_INTERNAL: Final = 5060
ERROR_CLUSTER_NODE_ALREADY_UP: Final = 5061
ERROR_CLUSTER_NODE_ALREADY_DOWN: Final = 5062
ERROR_CLUSTER_NETWORK_ALREADY_ONLINE: Final = 5063
ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE: Final = 5064
ERROR_CLUSTER_NODE_ALREADY_MEMBER: Final = 5065
ERROR_CLUSTER_LAST_INTERNAL_NETWORK: Final = 5066
ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS: Final = 5067
ERROR_INVALID_OPERATION_ON_QUORUM: Final = 5068
ERROR_DEPENDENCY_NOT_ALLOWED: Final = 5069
ERROR_CLUSTER_NODE_PAUSED: Final = 5070
ERROR_NODE_CANT_HOST_RESOURCE: Final = 5071
ERROR_CLUSTER_NODE_NOT_READY: Final = 5072
ERROR_CLUSTER_NODE_SHUTTING_DOWN: Final = 5073
ERROR_CLUSTER_JOIN_ABORTED: Final = 5074
ERROR_CLUSTER_INCOMPATIBLE_VERSIONS: Final = 5075
ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED: Final = 5076
ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED: Final = 5077
ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND: Final = 5078
ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED: Final = 5079
ERROR_CLUSTER_RESNAME_NOT_FOUND: Final = 5080
ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED: Final = 5081
ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST: Final = 5082
ERROR_CLUSTER_DATABASE_SEQMISMATCH: Final = 5083
ERROR_RESMON_INVALID_STATE: Final = 5084
ERROR_CLUSTER_GUM_NOT_LOCKER: Final = 5085
ERROR_QUORUM_DISK_NOT_FOUND: Final = 5086
ERROR_DATABASE_BACKUP_CORRUPT: Final = 5087
ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT: Final = 5088
ERROR_RESOURCE_PROPERTY_UNCHANGEABLE: Final = 5089
ERROR_NO_ADMIN_ACCESS_POINT: Final = 5090
ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE: Final = 5890
ERROR_CLUSTER_QUORUMLOG_NOT_FOUND: Final = 5891
ERROR_CLUSTER_MEMBERSHIP_HALT: Final = 5892
ERROR_CLUSTER_INSTANCE_ID_MISMATCH: Final = 5893
ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP: Final = 5894
ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH: Final = 5895
ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP: Final = 5896
ERROR_CLUSTER_PARAMETER_MISMATCH: Final = 5897
ERROR_NODE_CANNOT_BE_CLUSTERED: Final = 5898
ERROR_CLUSTER_WRONG_OS_VERSION: Final = 5899
ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME: Final = 5900
ERROR_CLUSCFG_ALREADY_COMMITTED: Final = 5901
ERROR_CLUSCFG_ROLLBACK_FAILED: Final = 5902
ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT: Final = 5903
ERROR_CLUSTER_OLD_VERSION: Final = 5904
ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME: Final = 5905
ERROR_CLUSTER_NO_NET_ADAPTERS: Final = 5906
ERROR_CLUSTER_POISONED: Final = 5907
ERROR_CLUSTER_GROUP_MOVING: Final = 5908
ERROR_CLUSTER_RESOURCE_TYPE_BUSY: Final = 5909
ERROR_RESOURCE_CALL_TIMED_OUT: Final = 5910
ERROR_INVALID_CLUSTER_IPV6_ADDRESS: Final = 5911
ERROR_CLUSTER_INTERNAL_INVALID_FUNCTION: Final = 5912
ERROR_CLUSTER_PARAMETER_OUT_OF_BOUNDS: Final = 5913
ERROR_CLUSTER_PARTIAL_SEND: Final = 5914
ERROR_CLUSTER_REGISTRY_INVALID_FUNCTION: Final = 5915
ERROR_CLUSTER_INVALID_STRING_TERMINATION: Final = 5916
ERROR_CLUSTER_INVALID_STRING_FORMAT: Final = 5917
ERROR_CLUSTER_DATABASE_TRANSACTION_IN_PROGRESS: Final = 5918
ERROR_CLUSTER_DATABASE_TRANSACTION_NOT_IN_PROGRESS: Final = 5919
ERROR_CLUSTER_NULL_DATA: Final = 5920
ERROR_CLUSTER_PARTIAL_READ: Final = 5921
ERROR_CLUSTER_PARTIAL_WRITE: Final = 5922
ERROR_CLUSTER_CANT_DESERIALIZE_DATA: Final = 5923
ERROR_DEPENDENT_RESOURCE_PROPERTY_CONFLICT: Final = 5924
ERROR_CLUSTER_NO_QUORUM: Final = 5925
ERROR_CLUSTER_INVALID_IPV6_NETWORK: Final = 5926
ERROR_CLUSTER_INVALID_IPV6_TUNNEL_NETWORK: Final = 5927
ERROR_QUORUM_NOT_ALLOWED_IN_THIS_GROUP: Final = 5928
ERROR_DEPENDENCY_TREE_TOO_COMPLEX: Final = 5929
ERROR_EXCEPTION_IN_RESOURCE_CALL: Final = 5930
ERROR_CLUSTER_RHS_FAILED_INITIALIZATION: Final = 5931
ERROR_CLUSTER_NOT_INSTALLED: Final = 5932
ERROR_CLUSTER_RESOURCES_MUST_BE_ONLINE_ON_THE_SAME_NODE: Final = 5933
ERROR_CLUSTER_MAX_NODES_IN_CLUSTER: Final = 5934
ERROR_CLUSTER_TOO_MANY_NODES: Final = 5935
ERROR_CLUSTER_OBJECT_ALREADY_USED: Final = 5936
ERROR_NONCORE_GROUPS_FOUND: Final = 5937
ERROR_FILE_SHARE_RESOURCE_CONFLICT: Final = 5938
ERROR_CLUSTER_EVICT_INVALID_REQUEST: Final = 5939
ERROR_CLUSTER_SINGLETON_RESOURCE: Final = 5940
ERROR_CLUSTER_GROUP_SINGLETON_RESOURCE: Final = 5941
ERROR_CLUSTER_RESOURCE_PROVIDER_FAILED: Final = 5942
ERROR_CLUSTER_RESOURCE_CONFIGURATION_ERROR: Final = 5943
ERROR_CLUSTER_GROUP_BUSY: Final = 5944
ERROR_CLUSTER_NOT_SHARED_VOLUME: Final = 5945
ERROR_CLUSTER_INVALID_SECURITY_DESCRIPTOR: Final = 5946
ERROR_CLUSTER_SHARED_VOLUMES_IN_USE: Final = 5947
ERROR_CLUSTER_USE_SHARED_VOLUMES_API: Final = 5948
ERROR_CLUSTER_BACKUP_IN_PROGRESS: Final = 5949
ERROR_NON_CSV_PATH: Final = 5950
ERROR_CSV_VOLUME_NOT_LOCAL: Final = 5951
ERROR_CLUSTER_WATCHDOG_TERMINATING: Final = 5952
ERROR_CLUSTER_RESOURCE_VETOED_MOVE_INCOMPATIBLE_NODES: Final = 5953
ERROR_CLUSTER_INVALID_NODE_WEIGHT: Final = 5954
ERROR_CLUSTER_RESOURCE_VETOED_CALL: Final = 5955
ERROR_RESMON_SYSTEM_RESOURCES_LACKING: Final = 5956
ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_DESTINATION: Final = 5957
ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_SOURCE: Final = 5958
ERROR_CLUSTER_GROUP_QUEUED: Final = 5959
ERROR_CLUSTER_RESOURCE_LOCKED_STATUS: Final = 5960
ERROR_CLUSTER_SHARED_VOLUME_FAILOVER_NOT_ALLOWED: Final = 5961
ERROR_CLUSTER_NODE_DRAIN_IN_PROGRESS: Final = 5962
ERROR_CLUSTER_DISK_NOT_CONNECTED: Final = 5963
ERROR_DISK_NOT_CSV_CAPABLE: Final = 5964
ERROR_RESOURCE_NOT_IN_AVAILABLE_STORAGE: Final = 5965
ERROR_CLUSTER_SHARED_VOLUME_REDIRECTED: Final = 5966
ERROR_CLUSTER_SHARED_VOLUME_NOT_REDIRECTED: Final = 5967
ERROR_CLUSTER_CANNOT_RETURN_PROPERTIES: Final = 5968
ERROR_CLUSTER_RESOURCE_CONTAINS_UNSUPPORTED_DIFF_AREA_FOR_SHARED_VOLUMES: Final = 5969
ERROR_CLUSTER_RESOURCE_IS_IN_MAINTENANCE_MODE: Final = 5970
ERROR_CLUSTER_AFFINITY_CONFLICT: Final = 5971
ERROR_CLUSTER_RESOURCE_IS_REPLICA_VIRTUAL_MACHINE: Final = 5972
ERROR_CLUSTER_UPGRADE_INCOMPATIBLE_VERSIONS: Final = 5973
ERROR_CLUSTER_UPGRADE_FIX_QUORUM_NOT_SUPPORTED: Final = 5974
ERROR_CLUSTER_UPGRADE_RESTART_REQUIRED: Final = 5975
ERROR_CLUSTER_UPGRADE_IN_PROGRESS: Final = 5976
ERROR_CLUSTER_UPGRADE_INCOMPLETE: Final = 5977
ERROR_CLUSTER_NODE_IN_GRACE_PERIOD: Final = 5978
ERROR_CLUSTER_CSV_IO_PAUSE_TIMEOUT: Final = 5979
ERROR_NODE_NOT_ACTIVE_CLUSTER_MEMBER: Final = 5980
ERROR_CLUSTER_RESOURCE_NOT_MONITORED: Final = 5981
ERROR_CLUSTER_RESOURCE_DOES_NOT_SUPPORT_UNMONITORED: Final = 5982
ERROR_CLUSTER_RESOURCE_IS_REPLICATED: Final = 5983
ERROR_CLUSTER_NODE_ISOLATED: Final = 5984
ERROR_CLUSTER_NODE_QUARANTINED: Final = 5985
ERROR_CLUSTER_DATABASE_UPDATE_CONDITION_FAILED: Final = 5986
ERROR_CLUSTER_SPACE_DEGRADED: Final = 5987
ERROR_CLUSTER_TOKEN_DELEGATION_NOT_SUPPORTED: Final = 5988
ERROR_CLUSTER_CSV_INVALID_HANDLE: Final = 5989
ERROR_CLUSTER_CSV_SUPPORTED_ONLY_ON_COORDINATOR: Final = 5990
ERROR_GROUPSET_NOT_AVAILABLE: Final = 5991
ERROR_GROUPSET_NOT_FOUND: Final = 5992
ERROR_GROUPSET_CANT_PROVIDE: Final = 5993
ERROR_CLUSTER_FAULT_DOMAIN_PARENT_NOT_FOUND: Final = 5994
ERROR_CLUSTER_FAULT_DOMAIN_INVALID_HIERARCHY: Final = 5995
ERROR_CLUSTER_FAULT_DOMAIN_FAILED_S2D_VALIDATION: Final = 5996
ERROR_CLUSTER_FAULT_DOMAIN_S2D_CONNECTIVITY_LOSS: Final = 5997
ERROR_CLUSTER_INVALID_INFRASTRUCTURE_FILESERVER_NAME: Final = 5998
ERROR_CLUSTERSET_MANAGEMENT_CLUSTER_UNREACHABLE: Final = 5999
ERROR_ENCRYPTION_FAILED: Final = 6000
ERROR_DECRYPTION_FAILED: Final = 6001
ERROR_FILE_ENCRYPTED: Final = 6002
ERROR_NO_RECOVERY_POLICY: Final = 6003
ERROR_NO_EFS: Final = 6004
ERROR_WRONG_EFS: Final = 6005
ERROR_NO_USER_KEYS: Final = 6006
ERROR_FILE_NOT_ENCRYPTED: Final = 6007
ERROR_NOT_EXPORT_FORMAT: Final = 6008
ERROR_FILE_READ_ONLY: Final = 6009
ERROR_DIR_EFS_DISALLOWED: Final = 6010
ERROR_EFS_SERVER_NOT_TRUSTED: Final = 6011
ERROR_BAD_RECOVERY_POLICY: Final = 6012
ERROR_EFS_ALG_BLOB_TOO_BIG: Final = 6013
ERROR_VOLUME_NOT_SUPPORT_EFS: Final = 6014
ERROR_EFS_DISABLED: Final = 6015
ERROR_EFS_VERSION_NOT_SUPPORT: Final = 6016
ERROR_CS_ENCRYPTION_INVALID_SERVER_RESPONSE: Final = 6017
ERROR_CS_ENCRYPTION_UNSUPPORTED_SERVER: Final = 6018
ERROR_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE: Final = 6019
ERROR_CS_ENCRYPTION_NEW_ENCRYPTED_FILE: Final = 6020
ERROR_CS_ENCRYPTION_FILE_NOT_CSE: Final = 6021
ERROR_ENCRYPTION_POLICY_DENIES_OPERATION: Final = 6022
ERROR_WIP_ENCRYPTION_FAILED: Final = 6023
ERROR_NO_BROWSER_SERVERS_FOUND: Final = 6118
SCHED_E_SERVICE_NOT_LOCALSYSTEM: Final = 6200
ERROR_CLUSTER_OBJECT_IS_CLUSTER_SET_VM: Final = 6250
ERROR_LOG_SECTOR_INVALID: Final = 6600
ERROR_LOG_SECTOR_PARITY_INVALID: Final = 6601
ERROR_LOG_SECTOR_REMAPPED: Final = 6602
ERROR_LOG_BLOCK_INCOMPLETE: Final = 6603
ERROR_LOG_INVALID_RANGE: Final = 6604
ERROR_LOG_BLOCKS_EXHAUSTED: Final = 6605
ERROR_LOG_READ_CONTEXT_INVALID: Final = 6606
ERROR_LOG_RESTART_INVALID: Final = 6607
ERROR_LOG_BLOCK_VERSION: Final = 6608
ERROR_LOG_BLOCK_INVALID: Final = 6609
ERROR_LOG_READ_MODE_INVALID: Final = 6610
ERROR_LOG_NO_RESTART: Final = 6611
ERROR_LOG_METADATA_CORRUPT: Final = 6612
ERROR_LOG_METADATA_INVALID: Final = 6613
ERROR_LOG_METADATA_INCONSISTENT: Final = 6614
ERROR_LOG_RESERVATION_INVALID: Final = 6615
ERROR_LOG_CANT_DELETE: Final = 6616
ERROR_LOG_CONTAINER_LIMIT_EXCEEDED: Final = 6617
ERROR_LOG_START_OF_LOG: Final = 6618
ERROR_LOG_POLICY_ALREADY_INSTALLED: Final = 6619
ERROR_LOG_POLICY_NOT_INSTALLED: Final = 6620
ERROR_LOG_POLICY_INVALID: Final = 6621
ERROR_LOG_POLICY_CONFLICT: Final = 6622
ERROR_LOG_PINNED_ARCHIVE_TAIL: Final = 6623
ERROR_LOG_RECORD_NONEXISTENT: Final = 6624
ERROR_LOG_RECORDS_RESERVED_INVALID: Final = 6625
ERROR_LOG_SPACE_RESERVED_INVALID: Final = 6626
ERROR_LOG_TAIL_INVALID: Final = 6627
ERROR_LOG_FULL: Final = 6628
ERROR_COULD_NOT_RESIZE_LOG: Final = 6629
ERROR_LOG_MULTIPLEXED: Final = 6630
ERROR_LOG_DEDICATED: Final = 6631
ERROR_LOG_ARCHIVE_NOT_IN_PROGRESS: Final = 6632
ERROR_LOG_ARCHIVE_IN_PROGRESS: Final = 6633
ERROR_LOG_EPHEMERAL: Final = 6634
ERROR_LOG_NOT_ENOUGH_CONTAINERS: Final = 6635
ERROR_LOG_CLIENT_ALREADY_REGISTERED: Final = 6636
ERROR_LOG_CLIENT_NOT_REGISTERED: Final = 6637
ERROR_LOG_FULL_HANDLER_IN_PROGRESS: Final = 6638
ERROR_LOG_CONTAINER_READ_FAILED: Final = 6639
ERROR_LOG_CONTAINER_WRITE_FAILED: Final = 6640
ERROR_LOG_CONTAINER_OPEN_FAILED: Final = 6641
ERROR_LOG_CONTAINER_STATE_INVALID: Final = 6642
ERROR_LOG_STATE_INVALID: Final = 6643
ERROR_LOG_PINNED: Final = 6644
ERROR_LOG_METADATA_FLUSH_FAILED: Final = 6645
ERROR_LOG_INCONSISTENT_SECURITY: Final = 6646
ERROR_LOG_APPENDED_FLUSH_FAILED: Final = 6647
ERROR_LOG_PINNED_RESERVATION: Final = 6648
ERROR_INVALID_TRANSACTION: Final = 6700
ERROR_TRANSACTION_NOT_ACTIVE: Final = 6701
ERROR_TRANSACTION_REQUEST_NOT_VALID: Final = 6702
ERROR_TRANSACTION_NOT_REQUESTED: Final = 6703
ERROR_TRANSACTION_ALREADY_ABORTED: Final = 6704
ERROR_TRANSACTION_ALREADY_COMMITTED: Final = 6705
ERROR_TM_INITIALIZATION_FAILED: Final = 6706
ERROR_RESOURCEMANAGER_READ_ONLY: Final = 6707
ERROR_TRANSACTION_NOT_JOINED: Final = 6708
ERROR_TRANSACTION_SUPERIOR_EXISTS: Final = 6709
ERROR_CRM_PROTOCOL_ALREADY_EXISTS: Final = 6710
ERROR_TRANSACTION_PROPAGATION_FAILED: Final = 6711
ERROR_CRM_PROTOCOL_NOT_FOUND: Final = 6712
ERROR_TRANSACTION_INVALID_MARSHALL_BUFFER: Final = 6713
ERROR_CURRENT_TRANSACTION_NOT_VALID: Final = 6714
ERROR_TRANSACTION_NOT_FOUND: Final = 6715
ERROR_RESOURCEMANAGER_NOT_FOUND: Final = 6716
ERROR_ENLISTMENT_NOT_FOUND: Final = 6717
ERROR_TRANSACTIONMANAGER_NOT_FOUND: Final = 6718
ERROR_TRANSACTIONMANAGER_NOT_ONLINE: Final = 6719
ERROR_TRANSACTIONMANAGER_RECOVERY_NAME_COLLISION: Final = 6720
ERROR_TRANSACTION_NOT_ROOT: Final = 6721
ERROR_TRANSACTION_OBJECT_EXPIRED: Final = 6722
ERROR_TRANSACTION_RESPONSE_NOT_ENLISTED: Final = 6723
ERROR_TRANSACTION_RECORD_TOO_LONG: Final = 6724
ERROR_IMPLICIT_TRANSACTION_NOT_SUPPORTED: Final = 6725
ERROR_TRANSACTION_INTEGRITY_VIOLATED: Final = 6726
ERROR_TRANSACTIONMANAGER_IDENTITY_MISMATCH: Final = 6727
ERROR_RM_CANNOT_BE_FROZEN_FOR_SNAPSHOT: Final = 6728
ERROR_TRANSACTION_MUST_WRITETHROUGH: Final = 6729
ERROR_TRANSACTION_NO_SUPERIOR: Final = 6730
ERROR_HEURISTIC_DAMAGE_POSSIBLE: Final = 6731
ERROR_TRANSACTIONAL_CONFLICT: Final = 6800
ERROR_RM_NOT_ACTIVE: Final = 6801
ERROR_RM_METADATA_CORRUPT: Final = 6802
ERROR_DIRECTORY_NOT_RM: Final = 6803
ERROR_TRANSACTIONS_UNSUPPORTED_REMOTE: Final = 6805
ERROR_LOG_RESIZE_INVALID_SIZE: Final = 6806
ERROR_OBJECT_NO_LONGER_EXISTS: Final = 6807
ERROR_STREAM_MINIVERSION_NOT_FOUND: Final = 6808
ERROR_STREAM_MINIVERSION_NOT_VALID: Final = 6809
ERROR_MINIVERSION_INACCESSIBLE_FROM_SPECIFIED_TRANSACTION: Final = 6810
ERROR_CANT_OPEN_MINIVERSION_WITH_MODIFY_INTENT: Final = 6811
ERROR_CANT_CREATE_MORE_STREAM_MINIVERSIONS: Final = 6812
ERROR_REMOTE_FILE_VERSION_MISMATCH: Final = 6814
ERROR_HANDLE_NO_LONGER_VALID: Final = 6815
ERROR_NO_TXF_METADATA: Final = 6816
ERROR_LOG_CORRUPTION_DETECTED: Final = 6817
ERROR_CANT_RECOVER_WITH_HANDLE_OPEN: Final = 6818
ERROR_RM_DISCONNECTED: Final = 6819
ERROR_ENLISTMENT_NOT_SUPERIOR: Final = 6820
ERROR_RECOVERY_NOT_NEEDED: Final = 6821
ERROR_RM_ALREADY_STARTED: Final = 6822
ERROR_FILE_IDENTITY_NOT_PERSISTENT: Final = 6823
ERROR_CANT_BREAK_TRANSACTIONAL_DEPENDENCY: Final = 6824
ERROR_CANT_CROSS_RM_BOUNDARY: Final = 6825
ERROR_TXF_DIR_NOT_EMPTY: Final = 6826
ERROR_INDOUBT_TRANSACTIONS_EXIST: Final = 6827
ERROR_TM_VOLATILE: Final = 6828
ERROR_ROLLBACK_TIMER_EXPIRED: Final = 6829
ERROR_TXF_ATTRIBUTE_CORRUPT: Final = 6830
ERROR_EFS_NOT_ALLOWED_IN_TRANSACTION: Final = 6831
ERROR_TRANSACTIONAL_OPEN_NOT_ALLOWED: Final = 6832
ERROR_LOG_GROWTH_FAILED: Final = 6833
ERROR_TRANSACTED_MAPPING_UNSUPPORTED_REMOTE: Final = 6834
ERROR_TXF_METADATA_ALREADY_PRESENT: Final = 6835
ERROR_TRANSACTION_SCOPE_CALLBACKS_NOT_SET: Final = 6836
ERROR_TRANSACTION_REQUIRED_PROMOTION: Final = 6837
ERROR_CANNOT_EXECUTE_FILE_IN_TRANSACTION: Final = 6838
ERROR_TRANSACTIONS_NOT_FROZEN: Final = 6839
ERROR_TRANSACTION_FREEZE_IN_PROGRESS: Final = 6840
ERROR_NOT_SNAPSHOT_VOLUME: Final = 6841
ERROR_NO_SAVEPOINT_WITH_OPEN_FILES: Final = 6842
ERROR_DATA_LOST_REPAIR: Final = 6843
ERROR_SPARSE_NOT_ALLOWED_IN_TRANSACTION: Final = 6844
ERROR_TM_IDENTITY_MISMATCH: Final = 6845
ERROR_FLOATED_SECTION: Final = 6846
ERROR_CANNOT_ACCEPT_TRANSACTED_WORK: Final = 6847
ERROR_CANNOT_ABORT_TRANSACTIONS: Final = 6848
ERROR_BAD_CLUSTERS: Final = 6849
ERROR_COMPRESSION_NOT_ALLOWED_IN_TRANSACTION: Final = 6850
ERROR_VOLUME_DIRTY: Final = 6851
ERROR_NO_LINK_TRACKING_IN_TRANSACTION: Final = 6852
ERROR_OPERATION_NOT_SUPPORTED_IN_TRANSACTION: Final = 6853
ERROR_EXPIRED_HANDLE: Final = 6854
ERROR_TRANSACTION_NOT_ENLISTED: Final = 6855
ERROR_CTX_WINSTATION_NAME_INVALID: Final = 7001
ERROR_CTX_INVALID_PD: Final = 7002
ERROR_CTX_PD_NOT_FOUND: Final = 7003
ERROR_CTX_WD_NOT_FOUND: Final = 7004
ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY: Final = 7005
ERROR_CTX_SERVICE_NAME_COLLISION: Final = 7006
ERROR_CTX_CLOSE_PENDING: Final = 7007
ERROR_CTX_NO_OUTBUF: Final = 7008
ERROR_CTX_MODEM_INF_NOT_FOUND: Final = 7009
ERROR_CTX_INVALID_MODEMNAME: Final = 7010
ERROR_CTX_MODEM_RESPONSE_ERROR: Final = 7011
ERROR_CTX_MODEM_RESPONSE_TIMEOUT: Final = 7012
ERROR_CTX_MODEM_RESPONSE_NO_CARRIER: Final = 7013
ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE: Final = 7014
ERROR_CTX_MODEM_RESPONSE_BUSY: Final = 7015
ERROR_CTX_MODEM_RESPONSE_VOICE: Final = 7016
ERROR_CTX_TD_ERROR: Final = 7017
ERROR_CTX_WINSTATION_NOT_FOUND: Final = 7022
ERROR_CTX_WINSTATION_ALREADY_EXISTS: Final = 7023
ERROR_CTX_WINSTATION_BUSY: Final = 7024
ERROR_CTX_BAD_VIDEO_MODE: Final = 7025
ERROR_CTX_GRAPHICS_INVALID: Final = 7035
ERROR_CTX_LOGON_DISABLED: Final = 7037
ERROR_CTX_NOT_CONSOLE: Final = 7038
ERROR_CTX_CLIENT_QUERY_TIMEOUT: Final = 7040
ERROR_CTX_CONSOLE_DISCONNECT: Final = 7041
ERROR_CTX_CONSOLE_CONNECT: Final = 7042
ERROR_CTX_SHADOW_DENIED: Final = 7044
ERROR_CTX_WINSTATION_ACCESS_DENIED: Final = 7045
ERROR_CTX_INVALID_WD: Final = 7049
ERROR_CTX_SHADOW_INVALID: Final = 7050
ERROR_CTX_SHADOW_DISABLED: Final = 7051
ERROR_CTX_CLIENT_LICENSE_IN_USE: Final = 7052
ERROR_CTX_CLIENT_LICENSE_NOT_SET: Final = 7053
ERROR_CTX_LICENSE_NOT_AVAILABLE: Final = 7054
ERROR_CTX_LICENSE_CLIENT_INVALID: Final = 7055
ERROR_CTX_LICENSE_EXPIRED: Final = 7056
ERROR_CTX_SHADOW_NOT_RUNNING: Final = 7057
ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE: Final = 7058
ERROR_ACTIVATION_COUNT_EXCEEDED: Final = 7059
ERROR_CTX_WINSTATIONS_DISABLED: Final = 7060
ERROR_CTX_ENCRYPTION_LEVEL_REQUIRED: Final = 7061
ERROR_CTX_SESSION_IN_USE: Final = 7062
ERROR_CTX_NO_FORCE_LOGOFF: Final = 7063
ERROR_CTX_ACCOUNT_RESTRICTION: Final = 7064
ERROR_RDP_PROTOCOL_ERROR: Final = 7065
ERROR_CTX_CDM_CONNECT: Final = 7066
ERROR_CTX_CDM_DISCONNECT: Final = 7067
ERROR_CTX_SECURITY_LAYER_ERROR: Final = 7068
ERROR_TS_INCOMPATIBLE_SESSIONS: Final = 7069
ERROR_TS_VIDEO_SUBSYSTEM_ERROR: Final = 7070
FRS_ERR_INVALID_API_SEQUENCE: Final = 8001
FRS_ERR_STARTING_SERVICE: Final = 8002
FRS_ERR_STOPPING_SERVICE: Final = 8003
FRS_ERR_INTERNAL_API: Final = 8004
FRS_ERR_INTERNAL: Final = 8005
FRS_ERR_SERVICE_COMM: Final = 8006
FRS_ERR_INSUFFICIENT_PRIV: Final = 8007
FRS_ERR_AUTHENTICATION: Final = 8008
FRS_ERR_PARENT_INSUFFICIENT_PRIV: Final = 8009
FRS_ERR_PARENT_AUTHENTICATION: Final = 8010
FRS_ERR_CHILD_TO_PARENT_COMM: Final = 8011
FRS_ERR_PARENT_TO_CHILD_COMM: Final = 8012
FRS_ERR_SYSVOL_POPULATE: Final = 8013
FRS_ERR_SYSVOL_POPULATE_TIMEOUT: Final = 8014
FRS_ERR_SYSVOL_IS_BUSY: Final = 8015
FRS_ERR_SYSVOL_DEMOTE: Final = 8016
FRS_ERR_INVALID_SERVICE_PARAMETER: Final = 8017
DS_S_SUCCESS: Final = NO_ERROR
ERROR_DS_NOT_INSTALLED: Final = 8200
ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY: Final = 8201
ERROR_DS_NO_ATTRIBUTE_OR_VALUE: Final = 8202
ERROR_DS_INVALID_ATTRIBUTE_SYNTAX: Final = 8203
ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED: Final = 8204
ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS: Final = 8205
ERROR_DS_BUSY: Final = 8206
ERROR_DS_UNAVAILABLE: Final = 8207
ERROR_DS_NO_RIDS_ALLOCATED: Final = 8208
ERROR_DS_NO_MORE_RIDS: Final = 8209
ERROR_DS_INCORRECT_ROLE_OWNER: Final = 8210
ERROR_DS_RIDMGR_INIT_ERROR: Final = 8211
ERROR_DS_OBJ_CLASS_VIOLATION: Final = 8212
ERROR_DS_CANT_ON_NON_LEAF: Final = 8213
ERROR_DS_CANT_ON_RDN: Final = 8214
ERROR_DS_CANT_MOD_OBJ_CLASS: Final = 8215
ERROR_DS_CROSS_DOM_MOVE_ERROR: Final = 8216
ERROR_DS_GC_NOT_AVAILABLE: Final = 8217
ERROR_SHARED_POLICY: Final = 8218
ERROR_POLICY_OBJECT_NOT_FOUND: Final = 8219
ERROR_POLICY_ONLY_IN_DS: Final = 8220
ERROR_PROMOTION_ACTIVE: Final = 8221
ERROR_NO_PROMOTION_ACTIVE: Final = 8222
ERROR_DS_OPERATIONS_ERROR: Final = 8224
ERROR_DS_PROTOCOL_ERROR: Final = 8225
ERROR_DS_TIMELIMIT_EXCEEDED: Final = 8226
ERROR_DS_SIZELIMIT_EXCEEDED: Final = 8227
ERROR_DS_ADMIN_LIMIT_EXCEEDED: Final = 8228
ERROR_DS_COMPARE_FALSE: Final = 8229
ERROR_DS_COMPARE_TRUE: Final = 8230
ERROR_DS_AUTH_METHOD_NOT_SUPPORTED: Final = 8231
ERROR_DS_STRONG_AUTH_REQUIRED: Final = 8232
ERROR_DS_INAPPROPRIATE_AUTH: Final = 8233
ERROR_DS_AUTH_UNKNOWN: Final = 8234
ERROR_DS_REFERRAL: Final = 8235
ERROR_DS_UNAVAILABLE_CRIT_EXTENSION: Final = 8236
ERROR_DS_CONFIDENTIALITY_REQUIRED: Final = 8237
ERROR_DS_INAPPROPRIATE_MATCHING: Final = 8238
ERROR_DS_CONSTRAINT_VIOLATION: Final = 8239
ERROR_DS_NO_SUCH_OBJECT: Final = 8240
ERROR_DS_ALIAS_PROBLEM: Final = 8241
ERROR_DS_INVALID_DN_SYNTAX: Final = 8242
ERROR_DS_IS_LEAF: Final = 8243
ERROR_DS_ALIAS_DEREF_PROBLEM: Final = 8244
ERROR_DS_UNWILLING_TO_PERFORM: Final = 8245
ERROR_DS_LOOP_DETECT: Final = 8246
ERROR_DS_NAMING_VIOLATION: Final = 8247
ERROR_DS_OBJECT_RESULTS_TOO_LARGE: Final = 8248
ERROR_DS_AFFECTS_MULTIPLE_DSAS: Final = 8249
ERROR_DS_SERVER_DOWN: Final = 8250
ERROR_DS_LOCAL_ERROR: Final = 8251
ERROR_DS_ENCODING_ERROR: Final = 8252
ERROR_DS_DECODING_ERROR: Final = 8253
ERROR_DS_FILTER_UNKNOWN: Final = 8254
ERROR_DS_PARAM_ERROR: Final = 8255
ERROR_DS_NOT_SUPPORTED: Final = 8256
ERROR_DS_NO_RESULTS_RETURNED: Final = 8257
ERROR_DS_CONTROL_NOT_FOUND: Final = 8258
ERROR_DS_CLIENT_LOOP: Final = 8259
ERROR_DS_REFERRAL_LIMIT_EXCEEDED: Final = 8260
ERROR_DS_SORT_CONTROL_MISSING: Final = 8261
ERROR_DS_OFFSET_RANGE_ERROR: Final = 8262
ERROR_DS_RIDMGR_DISABLED: Final = 8263
ERROR_DS_ROOT_MUST_BE_NC: Final = 8301
ERROR_DS_ADD_REPLICA_INHIBITED: Final = 8302
ERROR_DS_ATT_NOT_DEF_IN_SCHEMA: Final = 8303
ERROR_DS_MAX_OBJ_SIZE_EXCEEDED: Final = 8304
ERROR_DS_OBJ_STRING_NAME_EXISTS: Final = 8305
ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA: Final = 8306
ERROR_DS_RDN_DOESNT_MATCH_SCHEMA: Final = 8307
ERROR_DS_NO_REQUESTED_ATTS_FOUND: Final = 8308
ERROR_DS_USER_BUFFER_TO_SMALL: Final = 8309
ERROR_DS_ATT_IS_NOT_ON_OBJ: Final = 8310
ERROR_DS_ILLEGAL_MOD_OPERATION: Final = 8311
ERROR_DS_OBJ_TOO_LARGE: Final = 8312
ERROR_DS_BAD_INSTANCE_TYPE: Final = 8313
ERROR_DS_MASTERDSA_REQUIRED: Final = 8314
ERROR_DS_OBJECT_CLASS_REQUIRED: Final = 8315
ERROR_DS_MISSING_REQUIRED_ATT: Final = 8316
ERROR_DS_ATT_NOT_DEF_FOR_CLASS: Final = 8317
ERROR_DS_ATT_ALREADY_EXISTS: Final = 8318
ERROR_DS_CANT_ADD_ATT_VALUES: Final = 8320
ERROR_DS_SINGLE_VALUE_CONSTRAINT: Final = 8321
ERROR_DS_RANGE_CONSTRAINT: Final = 8322
ERROR_DS_ATT_VAL_ALREADY_EXISTS: Final = 8323
ERROR_DS_CANT_REM_MISSING_ATT: Final = 8324
ERROR_DS_CANT_REM_MISSING_ATT_VAL: Final = 8325
ERROR_DS_ROOT_CANT_BE_SUBREF: Final = 8326
ERROR_DS_NO_CHAINING: Final = 8327
ERROR_DS_NO_CHAINED_EVAL: Final = 8328
ERROR_DS_NO_PARENT_OBJECT: Final = 8329
ERROR_DS_PARENT_IS_AN_ALIAS: Final = 8330
ERROR_DS_CANT_MIX_MASTER_AND_REPS: Final = 8331
ERROR_DS_CHILDREN_EXIST: Final = 8332
ERROR_DS_OBJ_NOT_FOUND: Final = 8333
ERROR_DS_ALIASED_OBJ_MISSING: Final = 8334
ERROR_DS_BAD_NAME_SYNTAX: Final = 8335
ERROR_DS_ALIAS_POINTS_TO_ALIAS: Final = 8336
ERROR_DS_CANT_DEREF_ALIAS: Final = 8337
ERROR_DS_OUT_OF_SCOPE: Final = 8338
ERROR_DS_OBJECT_BEING_REMOVED: Final = 8339
ERROR_DS_CANT_DELETE_DSA_OBJ: Final = 8340
ERROR_DS_GENERIC_ERROR: Final = 8341
ERROR_DS_DSA_MUST_BE_INT_MASTER: Final = 8342
ERROR_DS_CLASS_NOT_DSA: Final = 8343
ERROR_DS_INSUFF_ACCESS_RIGHTS: Final = 8344
ERROR_DS_ILLEGAL_SUPERIOR: Final = 8345
ERROR_DS_ATTRIBUTE_OWNED_BY_SAM: Final = 8346
ERROR_DS_NAME_TOO_MANY_PARTS: Final = 8347
ERROR_DS_NAME_TOO_LONG: Final = 8348
ERROR_DS_NAME_VALUE_TOO_LONG: Final = 8349
ERROR_DS_NAME_UNPARSEABLE: Final = 8350
ERROR_DS_NAME_TYPE_UNKNOWN: Final = 8351
ERROR_DS_NOT_AN_OBJECT: Final = 8352
ERROR_DS_SEC_DESC_TOO_SHORT: Final = 8353
ERROR_DS_SEC_DESC_INVALID: Final = 8354
ERROR_DS_NO_DELETED_NAME: Final = 8355
ERROR_DS_SUBREF_MUST_HAVE_PARENT: Final = 8356
ERROR_DS_NCNAME_MUST_BE_NC: Final = 8357
ERROR_DS_CANT_ADD_SYSTEM_ONLY: Final = 8358
ERROR_DS_CLASS_MUST_BE_CONCRETE: Final = 8359
ERROR_DS_INVALID_DMD: Final = 8360
ERROR_DS_OBJ_GUID_EXISTS: Final = 8361
ERROR_DS_NOT_ON_BACKLINK: Final = 8362
ERROR_DS_NO_CROSSREF_FOR_NC: Final = 8363
ERROR_DS_SHUTTING_DOWN: Final = 8364
ERROR_DS_UNKNOWN_OPERATION: Final = 8365
ERROR_DS_INVALID_ROLE_OWNER: Final = 8366
ERROR_DS_COULDNT_CONTACT_FSMO: Final = 8367
ERROR_DS_CROSS_NC_DN_RENAME: Final = 8368
ERROR_DS_CANT_MOD_SYSTEM_ONLY: Final = 8369
ERROR_DS_REPLICATOR_ONLY: Final = 8370
ERROR_DS_OBJ_CLASS_NOT_DEFINED: Final = 8371
ERROR_DS_OBJ_CLASS_NOT_SUBCLASS: Final = 8372
ERROR_DS_NAME_REFERENCE_INVALID: Final = 8373
ERROR_DS_CROSS_REF_EXISTS: Final = 8374
ERROR_DS_CANT_DEL_MASTER_CROSSREF: Final = 8375
ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD: Final = 8376
ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX: Final = 8377
ERROR_DS_DUP_RDN: Final = 8378
ERROR_DS_DUP_OID: Final = 8379
ERROR_DS_DUP_MAPI_ID: Final = 8380
ERROR_DS_DUP_SCHEMA_ID_GUID: Final = 8381
ERROR_DS_DUP_LDAP_DISPLAY_NAME: Final = 8382
ERROR_DS_SEMANTIC_ATT_TEST: Final = 8383
ERROR_DS_SYNTAX_MISMATCH: Final = 8384
ERROR_DS_EXISTS_IN_MUST_HAVE: Final = 8385
ERROR_DS_EXISTS_IN_MAY_HAVE: Final = 8386
ERROR_DS_NONEXISTENT_MAY_HAVE: Final = 8387
ERROR_DS_NONEXISTENT_MUST_HAVE: Final = 8388
ERROR_DS_AUX_CLS_TEST_FAIL: Final = 8389
ERROR_DS_NONEXISTENT_POSS_SUP: Final = 8390
ERROR_DS_SUB_CLS_TEST_FAIL: Final = 8391
ERROR_DS_BAD_RDN_ATT_ID_SYNTAX: Final = 8392
ERROR_DS_EXISTS_IN_AUX_CLS: Final = 8393
ERROR_DS_EXISTS_IN_SUB_CLS: Final = 8394
ERROR_DS_EXISTS_IN_POSS_SUP: Final = 8395
ERROR_DS_RECALCSCHEMA_FAILED: Final = 8396
ERROR_DS_TREE_DELETE_NOT_FINISHED: Final = 8397
ERROR_DS_CANT_DELETE: Final = 8398
ERROR_DS_ATT_SCHEMA_REQ_ID: Final = 8399
ERROR_DS_BAD_ATT_SCHEMA_SYNTAX: Final = 8400
ERROR_DS_CANT_CACHE_ATT: Final = 8401
ERROR_DS_CANT_CACHE_CLASS: Final = 8402
ERROR_DS_CANT_REMOVE_ATT_CACHE: Final = 8403
ERROR_DS_CANT_REMOVE_CLASS_CACHE: Final = 8404
ERROR_DS_CANT_RETRIEVE_DN: Final = 8405
ERROR_DS_MISSING_SUPREF: Final = 8406
ERROR_DS_CANT_RETRIEVE_INSTANCE: Final = 8407
ERROR_DS_CODE_INCONSISTENCY: Final = 8408
ERROR_DS_DATABASE_ERROR: Final = 8409
ERROR_DS_GOVERNSID_MISSING: Final = 8410
ERROR_DS_MISSING_EXPECTED_ATT: Final = 8411
ERROR_DS_NCNAME_MISSING_CR_REF: Final = 8412
ERROR_DS_SECURITY_CHECKING_ERROR: Final = 8413
ERROR_DS_SCHEMA_NOT_LOADED: Final = 8414
ERROR_DS_SCHEMA_ALLOC_FAILED: Final = 8415
ERROR_DS_ATT_SCHEMA_REQ_SYNTAX: Final = 8416
ERROR_DS_GCVERIFY_ERROR: Final = 8417
ERROR_DS_DRA_SCHEMA_MISMATCH: Final = 8418
ERROR_DS_CANT_FIND_DSA_OBJ: Final = 8419
ERROR_DS_CANT_FIND_EXPECTED_NC: Final = 8420
ERROR_DS_CANT_FIND_NC_IN_CACHE: Final = 8421
ERROR_DS_CANT_RETRIEVE_CHILD: Final = 8422
ERROR_DS_SECURITY_ILLEGAL_MODIFY: Final = 8423
ERROR_DS_CANT_REPLACE_HIDDEN_REC: Final = 8424
ERROR_DS_BAD_HIERARCHY_FILE: Final = 8425
ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED: Final = 8426
ERROR_DS_CONFIG_PARAM_MISSING: Final = 8427
ERROR_DS_COUNTING_AB_INDICES_FAILED: Final = 8428
ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED: Final = 8429
ERROR_DS_INTERNAL_FAILURE: Final = 8430
ERROR_DS_UNKNOWN_ERROR: Final = 8431
ERROR_DS_ROOT_REQUIRES_CLASS_TOP: Final = 8432
ERROR_DS_REFUSING_FSMO_ROLES: Final = 8433
ERROR_DS_MISSING_FSMO_SETTINGS: Final = 8434
ERROR_DS_UNABLE_TO_SURRENDER_ROLES: Final = 8435
ERROR_DS_DRA_GENERIC: Final = 8436
ERROR_DS_DRA_INVALID_PARAMETER: Final = 8437
ERROR_DS_DRA_BUSY: Final = 8438
ERROR_DS_DRA_BAD_DN: Final = 8439
ERROR_DS_DRA_BAD_NC: Final = 8440
ERROR_DS_DRA_DN_EXISTS: Final = 8441
ERROR_DS_DRA_INTERNAL_ERROR: Final = 8442
ERROR_DS_DRA_INCONSISTENT_DIT: Final = 8443
ERROR_DS_DRA_CONNECTION_FAILED: Final = 8444
ERROR_DS_DRA_BAD_INSTANCE_TYPE: Final = 8445
ERROR_DS_DRA_OUT_OF_MEM: Final = 8446
ERROR_DS_DRA_MAIL_PROBLEM: Final = 8447
ERROR_DS_DRA_REF_ALREADY_EXISTS: Final = 8448
ERROR_DS_DRA_REF_NOT_FOUND: Final = 8449
ERROR_DS_DRA_OBJ_IS_REP_SOURCE: Final = 8450
ERROR_DS_DRA_DB_ERROR: Final = 8451
ERROR_DS_DRA_NO_REPLICA: Final = 8452
ERROR_DS_DRA_ACCESS_DENIED: Final = 8453
ERROR_DS_DRA_NOT_SUPPORTED: Final = 8454
ERROR_DS_DRA_RPC_CANCELLED: Final = 8455
ERROR_DS_DRA_SOURCE_DISABLED: Final = 8456
ERROR_DS_DRA_SINK_DISABLED: Final = 8457
ERROR_DS_DRA_NAME_COLLISION: Final = 8458
ERROR_DS_DRA_SOURCE_REINSTALLED: Final = 8459
ERROR_DS_DRA_MISSING_PARENT: Final = 8460
ERROR_DS_DRA_PREEMPTED: Final = 8461
ERROR_DS_DRA_ABANDON_SYNC: Final = 8462
ERROR_DS_DRA_SHUTDOWN: Final = 8463
ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET: Final = 8464
ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA: Final = 8465
ERROR_DS_DRA_EXTN_CONNECTION_FAILED: Final = 8466
ERROR_DS_INSTALL_SCHEMA_MISMATCH: Final = 8467
ERROR_DS_DUP_LINK_ID: Final = 8468
ERROR_DS_NAME_ERROR_RESOLVING: Final = 8469
ERROR_DS_NAME_ERROR_NOT_FOUND: Final = 8470
ERROR_DS_NAME_ERROR_NOT_UNIQUE: Final = 8471
ERROR_DS_NAME_ERROR_NO_MAPPING: Final = 8472
ERROR_DS_NAME_ERROR_DOMAIN_ONLY: Final = 8473
ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING: Final = 8474
ERROR_DS_CONSTRUCTED_ATT_MOD: Final = 8475
ERROR_DS_WRONG_OM_OBJ_CLASS: Final = 8476
ERROR_DS_DRA_REPL_PENDING: Final = 8477
ERROR_DS_DS_REQUIRED: Final = 8478
ERROR_DS_INVALID_LDAP_DISPLAY_NAME: Final = 8479
ERROR_DS_NON_BASE_SEARCH: Final = 8480
ERROR_DS_CANT_RETRIEVE_ATTS: Final = 8481
ERROR_DS_BACKLINK_WITHOUT_LINK: Final = 8482
ERROR_DS_EPOCH_MISMATCH: Final = 8483
ERROR_DS_SRC_NAME_MISMATCH: Final = 8484
ERROR_DS_SRC_AND_DST_NC_IDENTICAL: Final = 8485
ERROR_DS_DST_NC_MISMATCH: Final = 8486
ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC: Final = 8487
ERROR_DS_SRC_GUID_MISMATCH: Final = 8488
ERROR_DS_CANT_MOVE_DELETED_OBJECT: Final = 8489
ERROR_DS_PDC_OPERATION_IN_PROGRESS: Final = 8490
ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD: Final = 8491
ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION: Final = 8492
ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS: Final = 8493
ERROR_DS_NC_MUST_HAVE_NC_PARENT: Final = 8494
ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE: Final = 8495
ERROR_DS_DST_DOMAIN_NOT_NATIVE: Final = 8496
ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER: Final = 8497
ERROR_DS_CANT_MOVE_ACCOUNT_GROUP: Final = 8498
ERROR_DS_CANT_MOVE_RESOURCE_GROUP: Final = 8499
ERROR_DS_INVALID_SEARCH_FLAG: Final = 8500
ERROR_DS_NO_TREE_DELETE_ABOVE_NC: Final = 8501
ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE: Final = 8502
ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE: Final = 8503
ERROR_DS_SAM_INIT_FAILURE: Final = 8504
ERROR_DS_SENSITIVE_GROUP_VIOLATION: Final = 8505
ERROR_DS_CANT_MOD_PRIMARYGROUPID: Final = 8506
ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD: Final = 8507
ERROR_DS_NONSAFE_SCHEMA_CHANGE: Final = 8508
ERROR_DS_SCHEMA_UPDATE_DISALLOWED: Final = 8509
ERROR_DS_CANT_CREATE_UNDER_SCHEMA: Final = 8510
ERROR_DS_INSTALL_NO_SRC_SCH_VERSION: Final = 8511
ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE: Final = 8512
ERROR_DS_INVALID_GROUP_TYPE: Final = 8513
ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN: Final = 8514
ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN: Final = 8515
ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER: Final = 8516
ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER: Final = 8517
ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER: Final = 8518
ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER: Final = 8519
ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER: Final = 8520
ERROR_DS_HAVE_PRIMARY_MEMBERS: Final = 8521
ERROR_DS_STRING_SD_CONVERSION_FAILED: Final = 8522
ERROR_DS_NAMING_MASTER_GC: Final = 8523
ERROR_DS_DNS_LOOKUP_FAILURE: Final = 8524
ERROR_DS_COULDNT_UPDATE_SPNS: Final = 8525
ERROR_DS_CANT_RETRIEVE_SD: Final = 8526
ERROR_DS_KEY_NOT_UNIQUE: Final = 8527
ERROR_DS_WRONG_LINKED_ATT_SYNTAX: Final = 8528
ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD: Final = 8529
ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY: Final = 8530
ERROR_DS_CANT_START: Final = 8531
ERROR_DS_INIT_FAILURE: Final = 8532
ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION: Final = 8533
ERROR_DS_SOURCE_DOMAIN_IN_FOREST: Final = 8534
ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST: Final = 8535
ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED: Final = 8536
ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN: Final = 8537
ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER: Final = 8538
ERROR_DS_SRC_SID_EXISTS_IN_FOREST: Final = 8539
ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH: Final = 8540
ERROR_SAM_INIT_FAILURE: Final = 8541
ERROR_DS_DRA_SCHEMA_INFO_SHIP: Final = 8542
ERROR_DS_DRA_SCHEMA_CONFLICT: Final = 8543
ERROR_DS_DRA_EARLIER_SCHEMA_CONFLICT: Final = 8544
ERROR_DS_DRA_OBJ_NC_MISMATCH: Final = 8545
ERROR_DS_NC_STILL_HAS_DSAS: Final = 8546
ERROR_DS_GC_REQUIRED: Final = 8547
ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY: Final = 8548
ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS: Final = 8549
ERROR_DS_CANT_ADD_TO_GC: Final = 8550
ERROR_DS_NO_CHECKPOINT_WITH_PDC: Final = 8551
ERROR_DS_SOURCE_AUDITING_NOT_ENABLED: Final = 8552
ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC: Final = 8553
ERROR_DS_INVALID_NAME_FOR_SPN: Final = 8554
ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS: Final = 8555
ERROR_DS_UNICODEPWD_NOT_IN_QUOTES: Final = 8556
ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED: Final = 8557
ERROR_DS_MUST_BE_RUN_ON_DST_DC: Final = 8558
ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER: Final = 8559
ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ: Final = 8560
ERROR_DS_INIT_FAILURE_CONSOLE: Final = 8561
ERROR_DS_SAM_INIT_FAILURE_CONSOLE: Final = 8562
ERROR_DS_FOREST_VERSION_TOO_HIGH: Final = 8563
ERROR_DS_DOMAIN_VERSION_TOO_HIGH: Final = 8564
ERROR_DS_FOREST_VERSION_TOO_LOW: Final = 8565
ERROR_DS_DOMAIN_VERSION_TOO_LOW: Final = 8566
ERROR_DS_INCOMPATIBLE_VERSION: Final = 8567
ERROR_DS_LOW_DSA_VERSION: Final = 8568
ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN: Final = 8569
ERROR_DS_NOT_SUPPORTED_SORT_ORDER: Final = 8570
ERROR_DS_NAME_NOT_UNIQUE: Final = 8571
ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4: Final = 8572
ERROR_DS_OUT_OF_VERSION_STORE: Final = 8573
ERROR_DS_INCOMPATIBLE_CONTROLS_USED: Final = 8574
ERROR_DS_NO_REF_DOMAIN: Final = 8575
ERROR_DS_RESERVED_LINK_ID: Final = 8576
ERROR_DS_LINK_ID_NOT_AVAILABLE: Final = 8577
ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER: Final = 8578
ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE: Final = 8579
ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC: Final = 8580
ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG: Final = 8581
ERROR_DS_MODIFYDN_WRONG_GRANDPARENT: Final = 8582
ERROR_DS_NAME_ERROR_TRUST_REFERRAL: Final = 8583
ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER: Final = 8584
ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD: Final = 8585
ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2: Final = 8586
ERROR_DS_THREAD_LIMIT_EXCEEDED: Final = 8587
ERROR_DS_NOT_CLOSEST: Final = 8588
ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF: Final = 8589
ERROR_DS_SINGLE_USER_MODE_FAILED: Final = 8590
ERROR_DS_NTDSCRIPT_SYNTAX_ERROR: Final = 8591
ERROR_DS_NTDSCRIPT_PROCESS_ERROR: Final = 8592
ERROR_DS_DIFFERENT_REPL_EPOCHS: Final = 8593
ERROR_DS_DRS_EXTENSIONS_CHANGED: Final = 8594
ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR: Final = 8595
ERROR_DS_NO_MSDS_INTID: Final = 8596
ERROR_DS_DUP_MSDS_INTID: Final = 8597
ERROR_DS_EXISTS_IN_RDNATTID: Final = 8598
ERROR_DS_AUTHORIZATION_FAILED: Final = 8599
ERROR_DS_INVALID_SCRIPT: Final = 8600
ERROR_DS_REMOTE_CROSSREF_OP_FAILED: Final = 8601
ERROR_DS_CROSS_REF_BUSY: Final = 8602
ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN: Final = 8603
ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC: Final = 8604
ERROR_DS_DUPLICATE_ID_FOUND: Final = 8605
ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT: Final = 8606
ERROR_DS_GROUP_CONVERSION_ERROR: Final = 8607
ERROR_DS_CANT_MOVE_APP_BASIC_GROUP: Final = 8608
ERROR_DS_CANT_MOVE_APP_QUERY_GROUP: Final = 8609
ERROR_DS_ROLE_NOT_VERIFIED: Final = 8610
ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL: Final = 8611
ERROR_DS_DOMAIN_RENAME_IN_PROGRESS: Final = 8612
ERROR_DS_EXISTING_AD_CHILD_NC: Final = 8613
ERROR_DS_REPL_LIFETIME_EXCEEDED: Final = 8614
ERROR_DS_DISALLOWED_IN_SYSTEM_CONTAINER: Final = 8615
ERROR_DS_LDAP_SEND_QUEUE_FULL: Final = 8616
ERROR_DS_DRA_OUT_SCHEDULE_WINDOW: Final = 8617
ERROR_DS_POLICY_NOT_KNOWN: Final = 8618
ERROR_NO_SITE_SETTINGS_OBJECT: Final = 8619
ERROR_NO_SECRETS: Final = 8620
ERROR_NO_WRITABLE_DC_FOUND: Final = 8621
ERROR_DS_NO_SERVER_OBJECT: Final = 8622
ERROR_DS_NO_NTDSA_OBJECT: Final = 8623
ERROR_DS_NON_ASQ_SEARCH: Final = 8624
ERROR_DS_AUDIT_FAILURE: Final = 8625
ERROR_DS_INVALID_SEARCH_FLAG_SUBTREE: Final = 8626
ERROR_DS_INVALID_SEARCH_FLAG_TUPLE: Final = 8627
ERROR_DS_HIERARCHY_TABLE_TOO_DEEP: Final = 8628
ERROR_DS_DRA_CORRUPT_UTD_VECTOR: Final = 8629
ERROR_DS_DRA_SECRETS_DENIED: Final = 8630
ERROR_DS_RESERVED_MAPI_ID: Final = 8631
ERROR_DS_MAPI_ID_NOT_AVAILABLE: Final = 8632
ERROR_DS_DRA_MISSING_KRBTGT_SECRET: Final = 8633
ERROR_DS_DOMAIN_NAME_EXISTS_IN_FOREST: Final = 8634
ERROR_DS_FLAT_NAME_EXISTS_IN_FOREST: Final = 8635
ERROR_INVALID_USER_PRINCIPAL_NAME: Final = 8636
ERROR_DS_OID_MAPPED_GROUP_CANT_HAVE_MEMBERS: Final = 8637
ERROR_DS_OID_NOT_FOUND: Final = 8638
ERROR_DS_DRA_RECYCLED_TARGET: Final = 8639
ERROR_DS_DISALLOWED_NC_REDIRECT: Final = 8640
ERROR_DS_HIGH_ADLDS_FFL: Final = 8641
ERROR_DS_HIGH_DSA_VERSION: Final = 8642
ERROR_DS_LOW_ADLDS_FFL: Final = 8643
ERROR_DOMAIN_SID_SAME_AS_LOCAL_WORKSTATION: Final = 8644
ERROR_DS_UNDELETE_SAM_VALIDATION_FAILED: Final = 8645
ERROR_INCORRECT_ACCOUNT_TYPE: Final = 8646
ERROR_DS_SPN_VALUE_NOT_UNIQUE_IN_FOREST: Final = 8647
ERROR_DS_UPN_VALUE_NOT_UNIQUE_IN_FOREST: Final = 8648
ERROR_DS_MISSING_FOREST_TRUST: Final = 8649
ERROR_DS_VALUE_KEY_NOT_UNIQUE: Final = 8650
ERROR_WEAK_WHFBKEY_BLOCKED: Final = 8651
ERROR_DS_PER_ATTRIBUTE_AUTHZ_FAILED_DURING_ADD: Final = 8652
ERROR_LOCAL_POLICY_MODIFICATION_NOT_SUPPORTED: Final = 8653
ERROR_POLICY_CONTROLLED_ACCOUNT: Final = 8654
ERROR_LAPS_LEGACY_SCHEMA_MISSING: Final = 8655
ERROR_LAPS_SCHEMA_MISSING: Final = 8656
ERROR_LAPS_ENCRYPTION_REQUIRES_2016_DFL: Final = 8657
DNS_ERROR_RESPONSE_CODES_BASE: Final = 9000
DNS_ERROR_RCODE_NO_ERROR: Final = NO_ERROR
DNS_ERROR_MASK: Final = 0x00002328
DNS_ERROR_RCODE_FORMAT_ERROR: Final = 9001
DNS_ERROR_RCODE_SERVER_FAILURE: Final = 9002
DNS_ERROR_RCODE_NAME_ERROR: Final = 9003
DNS_ERROR_RCODE_NOT_IMPLEMENTED: Final = 9004
DNS_ERROR_RCODE_REFUSED: Final = 9005
DNS_ERROR_RCODE_YXDOMAIN: Final = 9006
DNS_ERROR_RCODE_YXRRSET: Final = 9007
DNS_ERROR_RCODE_NXRRSET: Final = 9008
DNS_ERROR_RCODE_NOTAUTH: Final = 9009
DNS_ERROR_RCODE_NOTZONE: Final = 9010
DNS_ERROR_RCODE_BADSIG: Final = 9016
DNS_ERROR_RCODE_BADKEY: Final = 9017
DNS_ERROR_RCODE_BADTIME: Final = 9018
DNS_ERROR_RCODE_LAST: Final = DNS_ERROR_RCODE_BADTIME
DNS_ERROR_DNSSEC_BASE: Final = 9100
DNS_ERROR_KEYMASTER_REQUIRED: Final = 9101
DNS_ERROR_NOT_ALLOWED_ON_SIGNED_ZONE: Final = 9102
DNS_ERROR_NSEC3_INCOMPATIBLE_WITH_RSA_SHA1: Final = 9103
DNS_ERROR_NOT_ENOUGH_SIGNING_KEY_DESCRIPTORS: Final = 9104
DNS_ERROR_UNSUPPORTED_ALGORITHM: Final = 9105
DNS_ERROR_INVALID_KEY_SIZE: Final = 9106
DNS_ERROR_SIGNING_KEY_NOT_ACCESSIBLE: Final = 9107
DNS_ERROR_KSP_DOES_NOT_SUPPORT_PROTECTION: Final = 9108
DNS_ERROR_UNEXPECTED_DATA_PROTECTION_ERROR: Final = 9109
DNS_ERROR_UNEXPECTED_CNG_ERROR: Final = 9110
DNS_ERROR_UNKNOWN_SIGNING_PARAMETER_VERSION: Final = 9111
DNS_ERROR_KSP_NOT_ACCESSIBLE: Final = 9112
DNS_ERROR_TOO_MANY_SKDS: Final = 9113
DNS_ERROR_INVALID_ROLLOVER_PERIOD: Final = 9114
DNS_ERROR_INVALID_INITIAL_ROLLOVER_OFFSET: Final = 9115
DNS_ERROR_ROLLOVER_IN_PROGRESS: Final = 9116
DNS_ERROR_STANDBY_KEY_NOT_PRESENT: Final = 9117
DNS_ERROR_NOT_ALLOWED_ON_ZSK: Final = 9118
DNS_ERROR_NOT_ALLOWED_ON_ACTIVE_SKD: Final = 9119
DNS_ERROR_ROLLOVER_ALREADY_QUEUED: Final = 9120
DNS_ERROR_NOT_ALLOWED_ON_UNSIGNED_ZONE: Final = 9121
DNS_ERROR_BAD_KEYMASTER: Final = 9122
DNS_ERROR_INVALID_SIGNATURE_VALIDITY_PERIOD: Final = 9123
DNS_ERROR_INVALID_NSEC3_ITERATION_COUNT: Final = 9124
DNS_ERROR_DNSSEC_IS_DISABLED: Final = 9125
DNS_ERROR_INVALID_XML: Final = 9126
DNS_ERROR_NO_VALID_TRUST_ANCHORS: Final = 9127
DNS_ERROR_ROLLOVER_NOT_POKEABLE: Final = 9128
DNS_ERROR_NSEC3_NAME_COLLISION: Final = 9129
DNS_ERROR_NSEC_INCOMPATIBLE_WITH_NSEC3_RSA_SHA1: Final = 9130
DNS_ERROR_PACKET_FMT_BASE: Final = 9500
DNS_INFO_NO_RECORDS: Final = 9501
DNS_ERROR_BAD_PACKET: Final = 9502
DNS_ERROR_NO_PACKET: Final = 9503
DNS_ERROR_RCODE: Final = 9504
DNS_ERROR_UNSECURE_PACKET: Final = 9505
DNS_STATUS_PACKET_UNSECURE: Final = DNS_ERROR_UNSECURE_PACKET
DNS_REQUEST_PENDING: Final = 9506
DNS_ERROR_NO_MEMORY: Final = ERROR_OUTOFMEMORY
DNS_ERROR_INVALID_NAME: Final = ERROR_INVALID_NAME
DNS_ERROR_INVALID_DATA: Final = ERROR_INVALID_DATA
DNS_ERROR_GENERAL_API_BASE: Final = 9550
DNS_ERROR_INVALID_TYPE: Final = 9551
DNS_ERROR_INVALID_IP_ADDRESS: Final = 9552
DNS_ERROR_INVALID_PROPERTY: Final = 9553
DNS_ERROR_TRY_AGAIN_LATER: Final = 9554
DNS_ERROR_NOT_UNIQUE: Final = 9555
DNS_ERROR_NON_RFC_NAME: Final = 9556
DNS_STATUS_FQDN: Final = 9557
DNS_STATUS_DOTTED_NAME: Final = 9558
DNS_STATUS_SINGLE_PART_NAME: Final = 9559
DNS_ERROR_INVALID_NAME_CHAR: Final = 9560
DNS_ERROR_NUMERIC_NAME: Final = 9561
DNS_ERROR_NOT_ALLOWED_ON_ROOT_SERVER: Final = 9562
DNS_ERROR_NOT_ALLOWED_UNDER_DELEGATION: Final = 9563
DNS_ERROR_CANNOT_FIND_ROOT_HINTS: Final = 9564
DNS_ERROR_INCONSISTENT_ROOT_HINTS: Final = 9565
DNS_ERROR_DWORD_VALUE_TOO_SMALL: Final = 9566
DNS_ERROR_DWORD_VALUE_TOO_LARGE: Final = 9567
DNS_ERROR_BACKGROUND_LOADING: Final = 9568
DNS_ERROR_NOT_ALLOWED_ON_RODC: Final = 9569
DNS_ERROR_NOT_ALLOWED_UNDER_DNAME: Final = 9570
DNS_ERROR_DELEGATION_REQUIRED: Final = 9571
DNS_ERROR_INVALID_POLICY_TABLE: Final = 9572
DNS_ERROR_ADDRESS_REQUIRED: Final = 9573
DNS_ERROR_ZONE_BASE: Final = 9600
DNS_ERROR_ZONE_DOES_NOT_EXIST: Final = 9601
DNS_ERROR_NO_ZONE_INFO: Final = 9602
DNS_ERROR_INVALID_ZONE_OPERATION: Final = 9603
DNS_ERROR_ZONE_CONFIGURATION_ERROR: Final = 9604
DNS_ERROR_ZONE_HAS_NO_SOA_RECORD: Final = 9605
DNS_ERROR_ZONE_HAS_NO_NS_RECORDS: Final = 9606
DNS_ERROR_ZONE_LOCKED: Final = 9607
DNS_ERROR_ZONE_CREATION_FAILED: Final = 9608
DNS_ERROR_ZONE_ALREADY_EXISTS: Final = 9609
DNS_ERROR_AUTOZONE_ALREADY_EXISTS: Final = 9610
DNS_ERROR_INVALID_ZONE_TYPE: Final = 9611
DNS_ERROR_SECONDARY_REQUIRES_MASTER_IP: Final = 9612
DNS_ERROR_ZONE_NOT_SECONDARY: Final = 9613
DNS_ERROR_NEED_SECONDARY_ADDRESSES: Final = 9614
DNS_ERROR_WINS_INIT_FAILED: Final = 9615
DNS_ERROR_NEED_WINS_SERVERS: Final = 9616
DNS_ERROR_NBSTAT_INIT_FAILED: Final = 9617
DNS_ERROR_SOA_DELETE_INVALID: Final = 9618
DNS_ERROR_FORWARDER_ALREADY_EXISTS: Final = 9619
DNS_ERROR_ZONE_REQUIRES_MASTER_IP: Final = 9620
DNS_ERROR_ZONE_IS_SHUTDOWN: Final = 9621
DNS_ERROR_ZONE_LOCKED_FOR_SIGNING: Final = 9622
DNS_ERROR_DATAFILE_BASE: Final = 9650
DNS_ERROR_PRIMARY_REQUIRES_DATAFILE: Final = 9651
DNS_ERROR_INVALID_DATAFILE_NAME: Final = 9652
DNS_ERROR_DATAFILE_OPEN_FAILURE: Final = 9653
DNS_ERROR_FILE_WRITEBACK_FAILED: Final = 9654
DNS_ERROR_DATAFILE_PARSING: Final = 9655
DNS_ERROR_DATABASE_BASE: Final = 9700
DNS_ERROR_RECORD_DOES_NOT_EXIST: Final = 9701
DNS_ERROR_RECORD_FORMAT: Final = 9702
DNS_ERROR_NODE_CREATION_FAILED: Final = 9703
DNS_ERROR_UNKNOWN_RECORD_TYPE: Final = 9704
DNS_ERROR_RECORD_TIMED_OUT: Final = 9705
DNS_ERROR_NAME_NOT_IN_ZONE: Final = 9706
DNS_ERROR_CNAME_LOOP: Final = 9707
DNS_ERROR_NODE_IS_CNAME: Final = 9708
DNS_ERROR_CNAME_COLLISION: Final = 9709
DNS_ERROR_RECORD_ONLY_AT_ZONE_ROOT: Final = 9710
DNS_ERROR_RECORD_ALREADY_EXISTS: Final = 9711
DNS_ERROR_SECONDARY_DATA: Final = 9712
DNS_ERROR_NO_CREATE_CACHE_DATA: Final = 9713
DNS_ERROR_NAME_DOES_NOT_EXIST: Final = 9714
DNS_WARNING_PTR_CREATE_FAILED: Final = 9715
DNS_WARNING_DOMAIN_UNDELETED: Final = 9716
DNS_ERROR_DS_UNAVAILABLE: Final = 9717
DNS_ERROR_DS_ZONE_ALREADY_EXISTS: Final = 9718
DNS_ERROR_NO_BOOTFILE_IF_DS_ZONE: Final = 9719
DNS_ERROR_NODE_IS_DNAME: Final = 9720
DNS_ERROR_DNAME_COLLISION: Final = 9721
DNS_ERROR_ALIAS_LOOP: Final = 9722
DNS_ERROR_OPERATION_BASE: Final = 9750
DNS_INFO_AXFR_COMPLETE: Final = 9751
DNS_ERROR_AXFR: Final = 9752
DNS_INFO_ADDED_LOCAL_WINS: Final = 9753
DNS_ERROR_SECURE_BASE: Final = 9800
DNS_STATUS_CONTINUE_NEEDED: Final = 9801
DNS_ERROR_SETUP_BASE: Final = 9850
DNS_ERROR_NO_TCPIP: Final = 9851
DNS_ERROR_NO_DNS_SERVERS: Final = 9852
DNS_ERROR_DP_BASE: Final = 9900
DNS_ERROR_DP_DOES_NOT_EXIST: Final = 9901
DNS_ERROR_DP_ALREADY_EXISTS: Final = 9902
DNS_ERROR_DP_NOT_ENLISTED: Final = 9903
DNS_ERROR_DP_ALREADY_ENLISTED: Final = 9904
DNS_ERROR_DP_NOT_AVAILABLE: Final = 9905
DNS_ERROR_DP_FSMO_ERROR: Final = 9906
DNS_ERROR_RRL_NOT_ENABLED: Final = 9911
DNS_ERROR_RRL_INVALID_WINDOW_SIZE: Final = 9912
DNS_ERROR_RRL_INVALID_IPV4_PREFIX: Final = 9913
DNS_ERROR_RRL_INVALID_IPV6_PREFIX: Final = 9914
DNS_ERROR_RRL_INVALID_TC_RATE: Final = 9915
DNS_ERROR_RRL_INVALID_LEAK_RATE: Final = 9916
DNS_ERROR_RRL_LEAK_RATE_LESSTHAN_TC_RATE: Final = 9917
DNS_ERROR_VIRTUALIZATION_INSTANCE_ALREADY_EXISTS: Final = 9921
DNS_ERROR_VIRTUALIZATION_INSTANCE_DOES_NOT_EXIST: Final = 9922
DNS_ERROR_VIRTUALIZATION_TREE_LOCKED: Final = 9923
DNS_ERROR_INVAILD_VIRTUALIZATION_INSTANCE_NAME: Final = 9924
DNS_ERROR_DEFAULT_VIRTUALIZATION_INSTANCE: Final = 9925
DNS_ERROR_ZONESCOPE_ALREADY_EXISTS: Final = 9951
DNS_ERROR_ZONESCOPE_DOES_NOT_EXIST: Final = 9952
DNS_ERROR_DEFAULT_ZONESCOPE: Final = 9953
DNS_ERROR_INVALID_ZONESCOPE_NAME: Final = 9954
DNS_ERROR_NOT_ALLOWED_WITH_ZONESCOPES: Final = 9955
DNS_ERROR_LOAD_ZONESCOPE_FAILED: Final = 9956
DNS_ERROR_ZONESCOPE_FILE_WRITEBACK_FAILED: Final = 9957
DNS_ERROR_INVALID_SCOPE_NAME: Final = 9958
DNS_ERROR_SCOPE_DOES_NOT_EXIST: Final = 9959
DNS_ERROR_DEFAULT_SCOPE: Final = 9960
DNS_ERROR_INVALID_SCOPE_OPERATION: Final = 9961
DNS_ERROR_SCOPE_LOCKED: Final = 9962
DNS_ERROR_SCOPE_ALREADY_EXISTS: Final = 9963
DNS_ERROR_POLICY_ALREADY_EXISTS: Final = 9971
DNS_ERROR_POLICY_DOES_NOT_EXIST: Final = 9972
DNS_ERROR_POLICY_INVALID_CRITERIA: Final = 9973
DNS_ERROR_POLICY_INVALID_SETTINGS: Final = 9974
DNS_ERROR_CLIENT_SUBNET_IS_ACCESSED: Final = 9975
DNS_ERROR_CLIENT_SUBNET_DOES_NOT_EXIST: Final = 9976
DNS_ERROR_CLIENT_SUBNET_ALREADY_EXISTS: Final = 9977
DNS_ERROR_SUBNET_DOES_NOT_EXIST: Final = 9978
DNS_ERROR_SUBNET_ALREADY_EXISTS: Final = 9979
DNS_ERROR_POLICY_LOCKED: Final = 9980
DNS_ERROR_POLICY_INVALID_WEIGHT: Final = 9981
DNS_ERROR_POLICY_INVALID_NAME: Final = 9982
DNS_ERROR_POLICY_MISSING_CRITERIA: Final = 9983
DNS_ERROR_INVALID_CLIENT_SUBNET_NAME: Final = 9984
DNS_ERROR_POLICY_PROCESSING_ORDER_INVALID: Final = 9985
DNS_ERROR_POLICY_SCOPE_MISSING: Final = 9986
DNS_ERROR_POLICY_SCOPE_NOT_ALLOWED: Final = 9987
DNS_ERROR_SERVERSCOPE_IS_REFERENCED: Final = 9988
DNS_ERROR_ZONESCOPE_IS_REFERENCED: Final = 9989
DNS_ERROR_POLICY_INVALID_CRITERIA_CLIENT_SUBNET: Final = 9990
DNS_ERROR_POLICY_INVALID_CRITERIA_TRANSPORT_PROTOCOL: Final = 9991
DNS_ERROR_POLICY_INVALID_CRITERIA_NETWORK_PROTOCOL: Final = 9992
DNS_ERROR_POLICY_INVALID_CRITERIA_INTERFACE: Final = 9993
DNS_ERROR_POLICY_INVALID_CRITERIA_FQDN: Final = 9994
DNS_ERROR_POLICY_INVALID_CRITERIA_QUERY_TYPE: Final = 9995
DNS_ERROR_POLICY_INVALID_CRITERIA_TIME_OF_DAY: Final = 9996
WSABASEERR: Final = 10000
WSAEINTR: Final = 10004
WSAEBADF: Final = 10009
WSAEACCES: Final = 10013
WSAEFAULT: Final = 10014
WSAEINVAL: Final = 10022
WSAEMFILE: Final = 10024
WSAEWOULDBLOCK: Final = 10035
WSAEINPROGRESS: Final = 10036
WSAEALREADY: Final = 10037
WSAENOTSOCK: Final = 10038
WSAEDESTADDRREQ: Final = 10039
WSAEMSGSIZE: Final = 10040
WSAEPROTOTYPE: Final = 10041
WSAENOPROTOOPT: Final = 10042
WSAEPROTONOSUPPORT: Final = 10043
WSAESOCKTNOSUPPORT: Final = 10044
WSAEOPNOTSUPP: Final = 10045
WSAEPFNOSUPPORT: Final = 10046
WSAEAFNOSUPPORT: Final = 10047
WSAEADDRINUSE: Final = 10048
WSAEADDRNOTAVAIL: Final = 10049
WSAENETDOWN: Final = 10050
WSAENETUNREACH: Final = 10051
WSAENETRESET: Final = 10052
WSAECONNABORTED: Final = 10053
WSAECONNRESET: Final = 10054
WSAENOBUFS: Final = 10055
WSAEISCONN: Final = 10056
WSAENOTCONN: Final = 10057
WSAESHUTDOWN: Final = 10058
WSAETOOMANYREFS: Final = 10059
WSAETIMEDOUT: Final = 10060
WSAECONNREFUSED: Final = 10061
WSAELOOP: Final = 10062
WSAENAMETOOLONG: Final = 10063
WSAEHOSTDOWN: Final = 10064
WSAEHOSTUNREACH: Final = 10065
WSAENOTEMPTY: Final = 10066
WSAEPROCLIM: Final = 10067
WSAEUSERS: Final = 10068
WSAEDQUOT: Final = 10069
WSAESTALE: Final = 10070
WSAEREMOTE: Final = 10071
WSASYSNOTREADY: Final = 10091
WSAVERNOTSUPPORTED: Final = 10092
WSANOTINITIALISED: Final = 10093
WSAEDISCON: Final = 10101
WSAENOMORE: Final = 10102
WSAECANCELLED: Final = 10103
WSAEINVALIDPROCTABLE: Final = 10104
WSAEINVALIDPROVIDER: Final = 10105
WSAEPROVIDERFAILEDINIT: Final = 10106
WSASYSCALLFAILURE: Final = 10107
WSASERVICE_NOT_FOUND: Final = 10108
WSATYPE_NOT_FOUND: Final = 10109
WSA_E_NO_MORE: Final = 10110
WSA_E_CANCELLED: Final = 10111
WSAEREFUSED: Final = 10112
WSAHOST_NOT_FOUND: Final = 11001
WSATRY_AGAIN: Final = 11002
WSANO_RECOVERY: Final = 11003
WSANO_DATA: Final = 11004
WSA_QOS_RECEIVERS: Final = 11005
WSA_QOS_SENDERS: Final = 11006
WSA_QOS_NO_SENDERS: Final = 11007
WSA_QOS_NO_RECEIVERS: Final = 11008
WSA_QOS_REQUEST_CONFIRMED: Final = 11009
WSA_QOS_ADMISSION_FAILURE: Final = 11010
WSA_QOS_POLICY_FAILURE: Final = 11011
WSA_QOS_BAD_STYLE: Final = 11012
WSA_QOS_BAD_OBJECT: Final = 11013
WSA_QOS_TRAFFIC_CTRL_ERROR: Final = 11014
WSA_QOS_GENERIC_ERROR: Final = 11015
WSA_QOS_ESERVICETYPE: Final = 11016
WSA_QOS_EFLOWSPEC: Final = 11017
WSA_QOS_EPROVSPECBUF: Final = 11018
WSA_QOS_EFILTERSTYLE: Final = 11019
WSA_QOS_EFILTERTYPE: Final = 11020
WSA_QOS_EFILTERCOUNT: Final = 11021
WSA_QOS_EOBJLENGTH: Final = 11022
WSA_QOS_EFLOWCOUNT: Final = 11023
WSA_QOS_EUNKOWNPSOBJ: Final = 11024
WSA_QOS_EPOLICYOBJ: Final = 11025
WSA_QOS_EFLOWDESC: Final = 11026
WSA_QOS_EPSFLOWSPEC: Final = 11027
WSA_QOS_EPSFILTERSPEC: Final = 11028
WSA_QOS_ESDMODEOBJ: Final = 11029
WSA_QOS_ESHAPERATEOBJ: Final = 11030
WSA_QOS_RESERVED_PETYPE: Final = 11031
WSA_SECURE_HOST_NOT_FOUND: Final = 11032
WSA_IPSEC_NAME_POLICY_ERROR: Final = 11033
ERROR_IPSEC_QM_POLICY_EXISTS: Final = 13000
ERROR_IPSEC_QM_POLICY_NOT_FOUND: Final = 13001
ERROR_IPSEC_QM_POLICY_IN_USE: Final = 13002
ERROR_IPSEC_MM_POLICY_EXISTS: Final = 13003
ERROR_IPSEC_MM_POLICY_NOT_FOUND: Final = 13004
ERROR_IPSEC_MM_POLICY_IN_USE: Final = 13005
ERROR_IPSEC_MM_FILTER_EXISTS: Final = 13006
ERROR_IPSEC_MM_FILTER_NOT_FOUND: Final = 13007
ERROR_IPSEC_TRANSPORT_FILTER_EXISTS: Final = 13008
ERROR_IPSEC_TRANSPORT_FILTER_NOT_FOUND: Final = 13009
ERROR_IPSEC_MM_AUTH_EXISTS: Final = 13010
ERROR_IPSEC_MM_AUTH_NOT_FOUND: Final = 13011
ERROR_IPSEC_MM_AUTH_IN_USE: Final = 13012
ERROR_IPSEC_DEFAULT_MM_POLICY_NOT_FOUND: Final = 13013
ERROR_IPSEC_DEFAULT_MM_AUTH_NOT_FOUND: Final = 13014
ERROR_IPSEC_DEFAULT_QM_POLICY_NOT_FOUND: Final = 13015
ERROR_IPSEC_TUNNEL_FILTER_EXISTS: Final = 13016
ERROR_IPSEC_TUNNEL_FILTER_NOT_FOUND: Final = 13017
ERROR_IPSEC_MM_FILTER_PENDING_DELETION: Final = 13018
ERROR_IPSEC_TRANSPORT_FILTER_PENDING_DELETION: Final = 13019
ERROR_IPSEC_TUNNEL_FILTER_PENDING_DELETION: Final = 13020
ERROR_IPSEC_MM_POLICY_PENDING_DELETION: Final = 13021
ERROR_IPSEC_MM_AUTH_PENDING_DELETION: Final = 13022
ERROR_IPSEC_QM_POLICY_PENDING_DELETION: Final = 13023
WARNING_IPSEC_MM_POLICY_PRUNED: Final = 13024
WARNING_IPSEC_QM_POLICY_PRUNED: Final = 13025
ERROR_IPSEC_IKE_NEG_STATUS_BEGIN: Final = 13800
ERROR_IPSEC_IKE_AUTH_FAIL: Final = 13801
ERROR_IPSEC_IKE_ATTRIB_FAIL: Final = 13802
ERROR_IPSEC_IKE_NEGOTIATION_PENDING: Final = 13803
ERROR_IPSEC_IKE_GENERAL_PROCESSING_ERROR: Final = 13804
ERROR_IPSEC_IKE_TIMED_OUT: Final = 13805
ERROR_IPSEC_IKE_NO_CERT: Final = 13806
ERROR_IPSEC_IKE_SA_DELETED: Final = 13807
ERROR_IPSEC_IKE_SA_REAPED: Final = 13808
ERROR_IPSEC_IKE_MM_ACQUIRE_DROP: Final = 13809
ERROR_IPSEC_IKE_QM_ACQUIRE_DROP: Final = 13810
ERROR_IPSEC_IKE_QUEUE_DROP_MM: Final = 13811
ERROR_IPSEC_IKE_QUEUE_DROP_NO_MM: Final = 13812
ERROR_IPSEC_IKE_DROP_NO_RESPONSE: Final = 13813
ERROR_IPSEC_IKE_MM_DELAY_DROP: Final = 13814
ERROR_IPSEC_IKE_QM_DELAY_DROP: Final = 13815
ERROR_IPSEC_IKE_ERROR: Final = 13816
ERROR_IPSEC_IKE_CRL_FAILED: Final = 13817
ERROR_IPSEC_IKE_INVALID_KEY_USAGE: Final = 13818
ERROR_IPSEC_IKE_INVALID_CERT_TYPE: Final = 13819
ERROR_IPSEC_IKE_NO_PRIVATE_KEY: Final = 13820
ERROR_IPSEC_IKE_SIMULTANEOUS_REKEY: Final = 13821
ERROR_IPSEC_IKE_DH_FAIL: Final = 13822
ERROR_IPSEC_IKE_CRITICAL_PAYLOAD_NOT_RECOGNIZED: Final = 13823
ERROR_IPSEC_IKE_INVALID_HEADER: Final = 13824
ERROR_IPSEC_IKE_NO_POLICY: Final = 13825
ERROR_IPSEC_IKE_INVALID_SIGNATURE: Final = 13826
ERROR_IPSEC_IKE_KERBEROS_ERROR: Final = 13827
ERROR_IPSEC_IKE_NO_PUBLIC_KEY: Final = 13828
ERROR_IPSEC_IKE_PROCESS_ERR: Final = 13829
ERROR_IPSEC_IKE_PROCESS_ERR_SA: Final = 13830
ERROR_IPSEC_IKE_PROCESS_ERR_PROP: Final = 13831
ERROR_IPSEC_IKE_PROCESS_ERR_TRANS: Final = 13832
ERROR_IPSEC_IKE_PROCESS_ERR_KE: Final = 13833
ERROR_IPSEC_IKE_PROCESS_ERR_ID: Final = 13834
ERROR_IPSEC_IKE_PROCESS_ERR_CERT: Final = 13835
ERROR_IPSEC_IKE_PROCESS_ERR_CERT_REQ: Final = 13836
ERROR_IPSEC_IKE_PROCESS_ERR_HASH: Final = 13837
ERROR_IPSEC_IKE_PROCESS_ERR_SIG: Final = 13838
ERROR_IPSEC_IKE_PROCESS_ERR_NONCE: Final = 13839
ERROR_IPSEC_IKE_PROCESS_ERR_NOTIFY: Final = 13840
ERROR_IPSEC_IKE_PROCESS_ERR_DELETE: Final = 13841
ERROR_IPSEC_IKE_PROCESS_ERR_VENDOR: Final = 13842
ERROR_IPSEC_IKE_INVALID_PAYLOAD: Final = 13843
ERROR_IPSEC_IKE_LOAD_SOFT_SA: Final = 13844
ERROR_IPSEC_IKE_SOFT_SA_TORN_DOWN: Final = 13845
ERROR_IPSEC_IKE_INVALID_COOKIE: Final = 13846
ERROR_IPSEC_IKE_NO_PEER_CERT: Final = 13847
ERROR_IPSEC_IKE_PEER_CRL_FAILED: Final = 13848
ERROR_IPSEC_IKE_POLICY_CHANGE: Final = 13849
ERROR_IPSEC_IKE_NO_MM_POLICY: Final = 13850
ERROR_IPSEC_IKE_NOTCBPRIV: Final = 13851
ERROR_IPSEC_IKE_SECLOADFAIL: Final = 13852
ERROR_IPSEC_IKE_FAILSSPINIT: Final = 13853
ERROR_IPSEC_IKE_FAILQUERYSSP: Final = 13854
ERROR_IPSEC_IKE_SRVACQFAIL: Final = 13855
ERROR_IPSEC_IKE_SRVQUERYCRED: Final = 13856
ERROR_IPSEC_IKE_GETSPIFAIL: Final = 13857
ERROR_IPSEC_IKE_INVALID_FILTER: Final = 13858
ERROR_IPSEC_IKE_OUT_OF_MEMORY: Final = 13859
ERROR_IPSEC_IKE_ADD_UPDATE_KEY_FAILED: Final = 13860
ERROR_IPSEC_IKE_INVALID_POLICY: Final = 13861
ERROR_IPSEC_IKE_UNKNOWN_DOI: Final = 13862
ERROR_IPSEC_IKE_INVALID_SITUATION: Final = 13863
ERROR_IPSEC_IKE_DH_FAILURE: Final = 13864
ERROR_IPSEC_IKE_INVALID_GROUP: Final = 13865
ERROR_IPSEC_IKE_ENCRYPT: Final = 13866
ERROR_IPSEC_IKE_DECRYPT: Final = 13867
ERROR_IPSEC_IKE_POLICY_MATCH: Final = 13868
ERROR_IPSEC_IKE_UNSUPPORTED_ID: Final = 13869
ERROR_IPSEC_IKE_INVALID_HASH: Final = 13870
ERROR_IPSEC_IKE_INVALID_HASH_ALG: Final = 13871
ERROR_IPSEC_IKE_INVALID_HASH_SIZE: Final = 13872
ERROR_IPSEC_IKE_INVALID_ENCRYPT_ALG: Final = 13873
ERROR_IPSEC_IKE_INVALID_AUTH_ALG: Final = 13874
ERROR_IPSEC_IKE_INVALID_SIG: Final = 13875
ERROR_IPSEC_IKE_LOAD_FAILED: Final = 13876
ERROR_IPSEC_IKE_RPC_DELETE: Final = 13877
ERROR_IPSEC_IKE_BENIGN_REINIT: Final = 13878
ERROR_IPSEC_IKE_INVALID_RESPONDER_LIFETIME_NOTIFY: Final = 13879
ERROR_IPSEC_IKE_INVALID_MAJOR_VERSION: Final = 13880
ERROR_IPSEC_IKE_INVALID_CERT_KEYLEN: Final = 13881
ERROR_IPSEC_IKE_MM_LIMIT: Final = 13882
ERROR_IPSEC_IKE_NEGOTIATION_DISABLED: Final = 13883
ERROR_IPSEC_IKE_QM_LIMIT: Final = 13884
ERROR_IPSEC_IKE_MM_EXPIRED: Final = 13885
ERROR_IPSEC_IKE_PEER_MM_ASSUMED_INVALID: Final = 13886
ERROR_IPSEC_IKE_CERT_CHAIN_POLICY_MISMATCH: Final = 13887
ERROR_IPSEC_IKE_UNEXPECTED_MESSAGE_ID: Final = 13888
ERROR_IPSEC_IKE_INVALID_AUTH_PAYLOAD: Final = 13889
ERROR_IPSEC_IKE_DOS_COOKIE_SENT: Final = 13890
ERROR_IPSEC_IKE_SHUTTING_DOWN: Final = 13891
ERROR_IPSEC_IKE_CGA_AUTH_FAILED: Final = 13892
ERROR_IPSEC_IKE_PROCESS_ERR_NATOA: Final = 13893
ERROR_IPSEC_IKE_INVALID_MM_FOR_QM: Final = 13894
ERROR_IPSEC_IKE_QM_EXPIRED: Final = 13895
ERROR_IPSEC_IKE_TOO_MANY_FILTERS: Final = 13896
ERROR_IPSEC_IKE_NEG_STATUS_END: Final = 13897
ERROR_IPSEC_IKE_KILL_DUMMY_NAP_TUNNEL: Final = 13898
ERROR_IPSEC_IKE_INNER_IP_ASSIGNMENT_FAILURE: Final = 13899
ERROR_IPSEC_IKE_REQUIRE_CP_PAYLOAD_MISSING: Final = 13900
ERROR_IPSEC_KEY_MODULE_IMPERSONATION_NEGOTIATION_PENDING: Final = 13901
ERROR_IPSEC_IKE_COEXISTENCE_SUPPRESS: Final = 13902
ERROR_IPSEC_IKE_RATELIMIT_DROP: Final = 13903
ERROR_IPSEC_IKE_PEER_DOESNT_SUPPORT_MOBIKE: Final = 13904
ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE: Final = 13905
ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_FAILURE: Final = 13906
ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE_WITH_OPTIONAL_RETRY: Final = 13907
ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_AND_CERTMAP_FAILURE: Final = 13908
ERROR_IPSEC_IKE_NEG_STATUS_EXTENDED_END: Final = 13909
ERROR_IPSEC_BAD_SPI: Final = 13910
ERROR_IPSEC_SA_LIFETIME_EXPIRED: Final = 13911
ERROR_IPSEC_WRONG_SA: Final = 13912
ERROR_IPSEC_REPLAY_CHECK_FAILED: Final = 13913
ERROR_IPSEC_INVALID_PACKET: Final = 13914
ERROR_IPSEC_INTEGRITY_CHECK_FAILED: Final = 13915
ERROR_IPSEC_CLEAR_TEXT_DROP: Final = 13916
ERROR_IPSEC_AUTH_FIREWALL_DROP: Final = 13917
ERROR_IPSEC_THROTTLE_DROP: Final = 13918
ERROR_IPSEC_DOSP_BLOCK: Final = 13925
ERROR_IPSEC_DOSP_RECEIVED_MULTICAST: Final = 13926
ERROR_IPSEC_DOSP_INVALID_PACKET: Final = 13927
ERROR_IPSEC_DOSP_STATE_LOOKUP_FAILED: Final = 13928
ERROR_IPSEC_DOSP_MAX_ENTRIES: Final = 13929
ERROR_IPSEC_DOSP_KEYMOD_NOT_ALLOWED: Final = 13930
ERROR_IPSEC_DOSP_NOT_INSTALLED: Final = 13931
ERROR_IPSEC_DOSP_MAX_PER_IP_RATELIMIT_QUEUES: Final = 13932
ERROR_SXS_SECTION_NOT_FOUND: Final = 14000
ERROR_SXS_CANT_GEN_ACTCTX: Final = 14001
ERROR_SXS_INVALID_ACTCTXDATA_FORMAT: Final = 14002
ERROR_SXS_ASSEMBLY_NOT_FOUND: Final = 14003
ERROR_SXS_MANIFEST_FORMAT_ERROR: Final = 14004
ERROR_SXS_MANIFEST_PARSE_ERROR: Final = 14005
ERROR_SXS_ACTIVATION_CONTEXT_DISABLED: Final = 14006
ERROR_SXS_KEY_NOT_FOUND: Final = 14007
ERROR_SXS_VERSION_CONFLICT: Final = 14008
ERROR_SXS_WRONG_SECTION_TYPE: Final = 14009
ERROR_SXS_THREAD_QUERIES_DISABLED: Final = 14010
ERROR_SXS_PROCESS_DEFAULT_ALREADY_SET: Final = 14011
ERROR_SXS_UNKNOWN_ENCODING_GROUP: Final = 14012
ERROR_SXS_UNKNOWN_ENCODING: Final = 14013
ERROR_SXS_INVALID_XML_NAMESPACE_URI: Final = 14014
ERROR_SXS_ROOT_MANIFEST_DEPENDENCY_NOT_INSTALLED: Final = 14015
ERROR_SXS_LEAF_MANIFEST_DEPENDENCY_NOT_INSTALLED: Final = 14016
ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE: Final = 14017
ERROR_SXS_MANIFEST_MISSING_REQUIRED_DEFAULT_NAMESPACE: Final = 14018
ERROR_SXS_MANIFEST_INVALID_REQUIRED_DEFAULT_NAMESPACE: Final = 14019
ERROR_SXS_PRIVATE_MANIFEST_CROSS_PATH_WITH_REPARSE_POINT: Final = 14020
ERROR_SXS_DUPLICATE_DLL_NAME: Final = 14021
ERROR_SXS_DUPLICATE_WINDOWCLASS_NAME: Final = 14022
ERROR_SXS_DUPLICATE_CLSID: Final = 14023
ERROR_SXS_DUPLICATE_IID: Final = 14024
ERROR_SXS_DUPLICATE_TLBID: Final = 14025
ERROR_SXS_DUPLICATE_PROGID: Final = 14026
ERROR_SXS_DUPLICATE_ASSEMBLY_NAME: Final = 14027
ERROR_SXS_FILE_HASH_MISMATCH: Final = 14028
ERROR_SXS_POLICY_PARSE_ERROR: Final = 14029
ERROR_SXS_XML_E_MISSINGQUOTE: Final = 14030
ERROR_SXS_XML_E_COMMENTSYNTAX: Final = 14031
ERROR_SXS_XML_E_BADSTARTNAMECHAR: Final = 14032
ERROR_SXS_XML_E_BADNAMECHAR: Final = 14033
ERROR_SXS_XML_E_BADCHARINSTRING: Final = 14034
ERROR_SXS_XML_E_XMLDECLSYNTAX: Final = 14035
ERROR_SXS_XML_E_BADCHARDATA: Final = 14036
ERROR_SXS_XML_E_MISSINGWHITESPACE: Final = 14037
ERROR_SXS_XML_E_EXPECTINGTAGEND: Final = 14038
ERROR_SXS_XML_E_MISSINGSEMICOLON: Final = 14039
ERROR_SXS_XML_E_UNBALANCEDPAREN: Final = 14040
ERROR_SXS_XML_E_INTERNALERROR: Final = 14041
ERROR_SXS_XML_E_UNEXPECTED_WHITESPACE: Final = 14042
ERROR_SXS_XML_E_INCOMPLETE_ENCODING: Final = 14043
ERROR_SXS_XML_E_MISSING_PAREN: Final = 14044
ERROR_SXS_XML_E_EXPECTINGCLOSEQUOTE: Final = 14045
ERROR_SXS_XML_E_MULTIPLE_COLONS: Final = 14046
ERROR_SXS_XML_E_INVALID_DECIMAL: Final = 14047
ERROR_SXS_XML_E_INVALID_HEXIDECIMAL: Final = 14048
ERROR_SXS_XML_E_INVALID_UNICODE: Final = 14049
ERROR_SXS_XML_E_WHITESPACEORQUESTIONMARK: Final = 14050
ERROR_SXS_XML_E_UNEXPECTEDENDTAG: Final = 14051
ERROR_SXS_XML_E_UNCLOSEDTAG: Final = 14052
ERROR_SXS_XML_E_DUPLICATEATTRIBUTE: Final = 14053
ERROR_SXS_XML_E_MULTIPLEROOTS: Final = 14054
ERROR_SXS_XML_E_INVALIDATROOTLEVEL: Final = 14055
ERROR_SXS_XML_E_BADXMLDECL: Final = 14056
ERROR_SXS_XML_E_MISSINGROOT: Final = 14057
ERROR_SXS_XML_E_UNEXPECTEDEOF: Final = 14058
ERROR_SXS_XML_E_BADPEREFINSUBSET: Final = 14059
ERROR_SXS_XML_E_UNCLOSEDSTARTTAG: Final = 14060
ERROR_SXS_XML_E_UNCLOSEDENDTAG: Final = 14061
ERROR_SXS_XML_E_UNCLOSEDSTRING: Final = 14062
ERROR_SXS_XML_E_UNCLOSEDCOMMENT: Final = 14063
ERROR_SXS_XML_E_UNCLOSEDDECL: Final = 14064
ERROR_SXS_XML_E_UNCLOSEDCDATA: Final = 14065
ERROR_SXS_XML_E_RESERVEDNAMESPACE: Final = 14066
ERROR_SXS_XML_E_INVALIDENCODING: Final = 14067
ERROR_SXS_XML_E_INVALIDSWITCH: Final = 14068
ERROR_SXS_XML_E_BADXMLCASE: Final = 14069
ERROR_SXS_XML_E_INVALID_STANDALONE: Final = 14070
ERROR_SXS_XML_E_UNEXPECTED_STANDALONE: Final = 14071
ERROR_SXS_XML_E_INVALID_VERSION: Final = 14072
ERROR_SXS_XML_E_MISSINGEQUALS: Final = 14073
ERROR_SXS_PROTECTION_RECOVERY_FAILED: Final = 14074
ERROR_SXS_PROTECTION_PUBLIC_KEY_TOO_SHORT: Final = 14075
ERROR_SXS_PROTECTION_CATALOG_NOT_VALID: Final = 14076
ERROR_SXS_UNTRANSLATABLE_HRESULT: Final = 14077
ERROR_SXS_PROTECTION_CATALOG_FILE_MISSING: Final = 14078
ERROR_SXS_MISSING_ASSEMBLY_IDENTITY_ATTRIBUTE: Final = 14079
ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_NAME: Final = 14080
ERROR_SXS_ASSEMBLY_MISSING: Final = 14081
ERROR_SXS_CORRUPT_ACTIVATION_STACK: Final = 14082
ERROR_SXS_CORRUPTION: Final = 14083
ERROR_SXS_EARLY_DEACTIVATION: Final = 14084
ERROR_SXS_INVALID_DEACTIVATION: Final = 14085
ERROR_SXS_MULTIPLE_DEACTIVATION: Final = 14086
ERROR_SXS_PROCESS_TERMINATION_REQUESTED: Final = 14087
ERROR_SXS_RELEASE_ACTIVATION_CONTEXT: Final = 14088
ERROR_SXS_SYSTEM_DEFAULT_ACTIVATION_CONTEXT_EMPTY: Final = 14089
ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_VALUE: Final = 14090
ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_NAME: Final = 14091
ERROR_SXS_IDENTITY_DUPLICATE_ATTRIBUTE: Final = 14092
ERROR_SXS_IDENTITY_PARSE_ERROR: Final = 14093
ERROR_MALFORMED_SUBSTITUTION_STRING: Final = 14094
ERROR_SXS_INCORRECT_PUBLIC_KEY_TOKEN: Final = 14095
ERROR_UNMAPPED_SUBSTITUTION_STRING: Final = 14096
ERROR_SXS_ASSEMBLY_NOT_LOCKED: Final = 14097
ERROR_SXS_COMPONENT_STORE_CORRUPT: Final = 14098
ERROR_ADVANCED_INSTALLER_FAILED: Final = 14099
ERROR_XML_ENCODING_MISMATCH: Final = 14100
ERROR_SXS_MANIFEST_IDENTITY_SAME_BUT_CONTENTS_DIFFERENT: Final = 14101
ERROR_SXS_IDENTITIES_DIFFERENT: Final = 14102
ERROR_SXS_ASSEMBLY_IS_NOT_A_DEPLOYMENT: Final = 14103
ERROR_SXS_FILE_NOT_PART_OF_ASSEMBLY: Final = 14104
ERROR_SXS_MANIFEST_TOO_BIG: Final = 14105
ERROR_SXS_SETTING_NOT_REGISTERED: Final = 14106
ERROR_SXS_TRANSACTION_CLOSURE_INCOMPLETE: Final = 14107
ERROR_SMI_PRIMITIVE_INSTALLER_FAILED: Final = 14108
ERROR_GENERIC_COMMAND_FAILED: Final = 14109
ERROR_SXS_FILE_HASH_MISSING: Final = 14110
ERROR_SXS_DUPLICATE_ACTIVATABLE_CLASS: Final = 14111
ERROR_EVT_INVALID_CHANNEL_PATH: Final = 15000
ERROR_EVT_INVALID_QUERY: Final = 15001
ERROR_EVT_PUBLISHER_METADATA_NOT_FOUND: Final = 15002
ERROR_EVT_EVENT_TEMPLATE_NOT_FOUND: Final = 15003
ERROR_EVT_INVALID_PUBLISHER_NAME: Final = 15004
ERROR_EVT_INVALID_EVENT_DATA: Final = 15005
ERROR_EVT_CHANNEL_NOT_FOUND: Final = 15007
ERROR_EVT_MALFORMED_XML_TEXT: Final = 15008
ERROR_EVT_SUBSCRIPTION_TO_DIRECT_CHANNEL: Final = 15009
ERROR_EVT_CONFIGURATION_ERROR: Final = 15010
ERROR_EVT_QUERY_RESULT_STALE: Final = 15011
ERROR_EVT_QUERY_RESULT_INVALID_POSITION: Final = 15012
ERROR_EVT_NON_VALIDATING_MSXML: Final = 15013
ERROR_EVT_FILTER_ALREADYSCOPED: Final = 15014
ERROR_EVT_FILTER_NOTELTSET: Final = 15015
ERROR_EVT_FILTER_INVARG: Final = 15016
ERROR_EVT_FILTER_INVTEST: Final = 15017
ERROR_EVT_FILTER_INVTYPE: Final = 15018
ERROR_EVT_FILTER_PARSEERR: Final = 15019
ERROR_EVT_FILTER_UNSUPPORTEDOP: Final = 15020
ERROR_EVT_FILTER_UNEXPECTEDTOKEN: Final = 15021
ERROR_EVT_INVALID_OPERATION_OVER_ENABLED_DIRECT_CHANNEL: Final = 15022
ERROR_EVT_INVALID_CHANNEL_PROPERTY_VALUE: Final = 15023
ERROR_EVT_INVALID_PUBLISHER_PROPERTY_VALUE: Final = 15024
ERROR_EVT_CHANNEL_CANNOT_ACTIVATE: Final = 15025
ERROR_EVT_FILTER_TOO_COMPLEX: Final = 15026
ERROR_EVT_MESSAGE_NOT_FOUND: Final = 15027
ERROR_EVT_MESSAGE_ID_NOT_FOUND: Final = 15028
ERROR_EVT_UNRESOLVED_VALUE_INSERT: Final = 15029
ERROR_EVT_UNRESOLVED_PARAMETER_INSERT: Final = 15030
ERROR_EVT_MAX_INSERTS_REACHED: Final = 15031
ERROR_EVT_EVENT_DEFINITION_NOT_FOUND: Final = 15032
ERROR_EVT_MESSAGE_LOCALE_NOT_FOUND: Final = 15033
ERROR_EVT_VERSION_TOO_OLD: Final = 15034
ERROR_EVT_VERSION_TOO_NEW: Final = 15035
ERROR_EVT_CANNOT_OPEN_CHANNEL_OF_QUERY: Final = 15036
ERROR_EVT_PUBLISHER_DISABLED: Final = 15037
ERROR_EVT_FILTER_OUT_OF_RANGE: Final = 15038
ERROR_EC_SUBSCRIPTION_CANNOT_ACTIVATE: Final = 15080
ERROR_EC_LOG_DISABLED: Final = 15081
ERROR_EC_CIRCULAR_FORWARDING: Final = 15082
ERROR_EC_CREDSTORE_FULL: Final = 15083
ERROR_EC_CRED_NOT_FOUND: Final = 15084
ERROR_EC_NO_ACTIVE_CHANNEL: Final = 15085
ERROR_MUI_FILE_NOT_FOUND: Final = 15100
ERROR_MUI_INVALID_FILE: Final = 15101
ERROR_MUI_INVALID_RC_CONFIG: Final = 15102
ERROR_MUI_INVALID_LOCALE_NAME: Final = 15103
ERROR_MUI_INVALID_ULTIMATEFALLBACK_NAME: Final = 15104
ERROR_MUI_FILE_NOT_LOADED: Final = 15105
ERROR_RESOURCE_ENUM_USER_STOP: Final = 15106
ERROR_MUI_INTLSETTINGS_UILANG_NOT_INSTALLED: Final = 15107
ERROR_MUI_INTLSETTINGS_INVALID_LOCALE_NAME: Final = 15108
ERROR_MRM_RUNTIME_NO_DEFAULT_OR_NEUTRAL_RESOURCE: Final = 15110
ERROR_MRM_INVALID_PRICONFIG: Final = 15111
ERROR_MRM_INVALID_FILE_TYPE: Final = 15112
ERROR_MRM_UNKNOWN_QUALIFIER: Final = 15113
ERROR_MRM_INVALID_QUALIFIER_VALUE: Final = 15114
ERROR_MRM_NO_CANDIDATE: Final = 15115
ERROR_MRM_NO_MATCH_OR_DEFAULT_CANDIDATE: Final = 15116
ERROR_MRM_RESOURCE_TYPE_MISMATCH: Final = 15117
ERROR_MRM_DUPLICATE_MAP_NAME: Final = 15118
ERROR_MRM_DUPLICATE_ENTRY: Final = 15119
ERROR_MRM_INVALID_RESOURCE_IDENTIFIER: Final = 15120
ERROR_MRM_FILEPATH_TOO_LONG: Final = 15121
ERROR_MRM_UNSUPPORTED_DIRECTORY_TYPE: Final = 15122
ERROR_MRM_INVALID_PRI_FILE: Final = 15126
ERROR_MRM_NAMED_RESOURCE_NOT_FOUND: Final = 15127
ERROR_MRM_MAP_NOT_FOUND: Final = 15135
ERROR_MRM_UNSUPPORTED_PROFILE_TYPE: Final = 15136
ERROR_MRM_INVALID_QUALIFIER_OPERATOR: Final = 15137
ERROR_MRM_INDETERMINATE_QUALIFIER_VALUE: Final = 15138
ERROR_MRM_AUTOMERGE_ENABLED: Final = 15139
ERROR_MRM_TOO_MANY_RESOURCES: Final = 15140
ERROR_MRM_UNSUPPORTED_FILE_TYPE_FOR_MERGE: Final = 15141
ERROR_MRM_UNSUPPORTED_FILE_TYPE_FOR_LOAD_UNLOAD_PRI_FILE: Final = 15142
ERROR_MRM_NO_CURRENT_VIEW_ON_THREAD: Final = 15143
ERROR_DIFFERENT_PROFILE_RESOURCE_MANAGER_EXIST: Final = 15144
ERROR_OPERATION_NOT_ALLOWED_FROM_SYSTEM_COMPONENT: Final = 15145
ERROR_MRM_DIRECT_REF_TO_NON_DEFAULT_RESOURCE: Final = 15146
ERROR_MRM_GENERATION_COUNT_MISMATCH: Final = 15147
ERROR_PRI_MERGE_VERSION_MISMATCH: Final = 15148
ERROR_PRI_MERGE_MISSING_SCHEMA: Final = 15149
ERROR_PRI_MERGE_LOAD_FILE_FAILED: Final = 15150
ERROR_PRI_MERGE_ADD_FILE_FAILED: Final = 15151
ERROR_PRI_MERGE_WRITE_FILE_FAILED: Final = 15152
ERROR_PRI_MERGE_MULTIPLE_PACKAGE_FAMILIES_NOT_ALLOWED: Final = 15153
ERROR_PRI_MERGE_MULTIPLE_MAIN_PACKAGES_NOT_ALLOWED: Final = 15154
ERROR_PRI_MERGE_BUNDLE_PACKAGES_NOT_ALLOWED: Final = 15155
ERROR_PRI_MERGE_MAIN_PACKAGE_REQUIRED: Final = 15156
ERROR_PRI_MERGE_RESOURCE_PACKAGE_REQUIRED: Final = 15157
ERROR_PRI_MERGE_INVALID_FILE_NAME: Final = 15158
ERROR_MRM_PACKAGE_NOT_FOUND: Final = 15159
ERROR_MRM_MISSING_DEFAULT_LANGUAGE: Final = 15160
ERROR_MRM_SCOPE_ITEM_CONFLICT: Final = 15161
ERROR_MCA_INVALID_CAPABILITIES_STRING: Final = 15200
ERROR_MCA_INVALID_VCP_VERSION: Final = 15201
ERROR_MCA_MONITOR_VIOLATES_MCCS_SPECIFICATION: Final = 15202
ERROR_MCA_MCCS_VERSION_MISMATCH: Final = 15203
ERROR_MCA_UNSUPPORTED_MCCS_VERSION: Final = 15204
ERROR_MCA_INTERNAL_ERROR: Final = 15205
ERROR_MCA_INVALID_TECHNOLOGY_TYPE_RETURNED: Final = 15206
ERROR_MCA_UNSUPPORTED_COLOR_TEMPERATURE: Final = 15207
ERROR_AMBIGUOUS_SYSTEM_DEVICE: Final = 15250
ERROR_SYSTEM_DEVICE_NOT_FOUND: Final = 15299
ERROR_HASH_NOT_SUPPORTED: Final = 15300
ERROR_HASH_NOT_PRESENT: Final = 15301
ERROR_SECONDARY_IC_PROVIDER_NOT_REGISTERED: Final = 15321
ERROR_GPIO_CLIENT_INFORMATION_INVALID: Final = 15322
ERROR_GPIO_VERSION_NOT_SUPPORTED: Final = 15323
ERROR_GPIO_INVALID_REGISTRATION_PACKET: Final = 15324
ERROR_GPIO_OPERATION_DENIED: Final = 15325
ERROR_GPIO_INCOMPATIBLE_CONNECT_MODE: Final = 15326
ERROR_GPIO_INTERRUPT_ALREADY_UNMASKED: Final = 15327
ERROR_CANNOT_SWITCH_RUNLEVEL: Final = 15400
ERROR_INVALID_RUNLEVEL_SETTING: Final = 15401
ERROR_RUNLEVEL_SWITCH_TIMEOUT: Final = 15402
ERROR_RUNLEVEL_SWITCH_AGENT_TIMEOUT: Final = 15403
ERROR_RUNLEVEL_SWITCH_IN_PROGRESS: Final = 15404
ERROR_SERVICES_FAILED_AUTOSTART: Final = 15405
ERROR_COM_TASK_STOP_PENDING: Final = 15501
ERROR_INSTALL_OPEN_PACKAGE_FAILED: Final = 15600
ERROR_INSTALL_PACKAGE_NOT_FOUND: Final = 15601
ERROR_INSTALL_INVALID_PACKAGE: Final = 15602
ERROR_INSTALL_RESOLVE_DEPENDENCY_FAILED: Final = 15603
ERROR_INSTALL_OUT_OF_DISK_SPACE: Final = 15604
ERROR_INSTALL_NETWORK_FAILURE: Final = 15605
ERROR_INSTALL_REGISTRATION_FAILURE: Final = 15606
ERROR_INSTALL_DEREGISTRATION_FAILURE: Final = 15607
ERROR_INSTALL_CANCEL: Final = 15608
ERROR_INSTALL_FAILED: Final = 15609
ERROR_REMOVE_FAILED: Final = 15610
ERROR_PACKAGE_ALREADY_EXISTS: Final = 15611
ERROR_NEEDS_REMEDIATION: Final = 15612
ERROR_INSTALL_PREREQUISITE_FAILED: Final = 15613
ERROR_PACKAGE_REPOSITORY_CORRUPTED: Final = 15614
ERROR_INSTALL_POLICY_FAILURE: Final = 15615
ERROR_PACKAGE_UPDATING: Final = 15616
ERROR_DEPLOYMENT_BLOCKED_BY_POLICY: Final = 15617
ERROR_PACKAGES_IN_USE: Final = 15618
ERROR_RECOVERY_FILE_CORRUPT: Final = 15619
ERROR_INVALID_STAGED_SIGNATURE: Final = 15620
ERROR_DELETING_EXISTING_APPLICATIONDATA_STORE_FAILED: Final = 15621
ERROR_INSTALL_PACKAGE_DOWNGRADE: Final = 15622
ERROR_SYSTEM_NEEDS_REMEDIATION: Final = 15623
ERROR_APPX_INTEGRITY_FAILURE_CLR_NGEN: Final = 15624
ERROR_RESILIENCY_FILE_CORRUPT: Final = 15625
ERROR_INSTALL_FIREWALL_SERVICE_NOT_RUNNING: Final = 15626
ERROR_PACKAGE_MOVE_FAILED: Final = 15627
ERROR_INSTALL_VOLUME_NOT_EMPTY: Final = 15628
ERROR_INSTALL_VOLUME_OFFLINE: Final = 15629
ERROR_INSTALL_VOLUME_CORRUPT: Final = 15630
ERROR_NEEDS_REGISTRATION: Final = 15631
ERROR_INSTALL_WRONG_PROCESSOR_ARCHITECTURE: Final = 15632
ERROR_DEV_SIDELOAD_LIMIT_EXCEEDED: Final = 15633
ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE: Final = 15634
ERROR_PACKAGE_NOT_SUPPORTED_ON_FILESYSTEM: Final = 15635
ERROR_PACKAGE_MOVE_BLOCKED_BY_STREAMING: Final = 15636
ERROR_INSTALL_OPTIONAL_PACKAGE_APPLICATIONID_NOT_UNIQUE: Final = 15637
ERROR_PACKAGE_STAGING_ONHOLD: Final = 15638
ERROR_INSTALL_INVALID_RELATED_SET_UPDATE: Final = 15639
ERROR_INSTALL_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_FULLTRUST_CAPABILITY: Final = 15640
ERROR_DEPLOYMENT_BLOCKED_BY_USER_LOG_OFF: Final = 15641
ERROR_PROVISION_OPTIONAL_PACKAGE_REQUIRES_MAIN_PACKAGE_PROVISIONED: Final = 15642
ERROR_PACKAGES_REPUTATION_CHECK_FAILED: Final = 15643
ERROR_PACKAGES_REPUTATION_CHECK_TIMEDOUT: Final = 15644
ERROR_DEPLOYMENT_OPTION_NOT_SUPPORTED: Final = 15645
ERROR_APPINSTALLER_ACTIVATION_BLOCKED: Final = 15646
ERROR_REGISTRATION_FROM_REMOTE_DRIVE_NOT_SUPPORTED: Final = 15647
ERROR_APPX_RAW_DATA_WRITE_FAILED: Final = 15648
ERROR_DEPLOYMENT_BLOCKED_BY_VOLUME_POLICY_PACKAGE: Final = 15649
ERROR_DEPLOYMENT_BLOCKED_BY_VOLUME_POLICY_MACHINE: Final = 15650
ERROR_DEPLOYMENT_BLOCKED_BY_PROFILE_POLICY: Final = 15651
ERROR_DEPLOYMENT_FAILED_CONFLICTING_MUTABLE_PACKAGE_DIRECTORY: Final = 15652
ERROR_SINGLETON_RESOURCE_INSTALLED_IN_ACTIVE_USER: Final = 15653
ERROR_DIFFERENT_VERSION_OF_PACKAGED_SERVICE_INSTALLED: Final = 15654
ERROR_SERVICE_EXISTS_AS_NON_PACKAGED_SERVICE: Final = 15655
ERROR_PACKAGED_SERVICE_REQUIRES_ADMIN_PRIVILEGES: Final = 15656
ERROR_REDIRECTION_TO_DEFAULT_ACCOUNT_NOT_ALLOWED: Final = 15657
ERROR_PACKAGE_LACKS_CAPABILITY_TO_DEPLOY_ON_HOST: Final = 15658
ERROR_UNSIGNED_PACKAGE_INVALID_CONTENT: Final = 15659
ERROR_UNSIGNED_PACKAGE_INVALID_PUBLISHER_NAMESPACE: Final = 15660
ERROR_SIGNED_PACKAGE_INVALID_PUBLISHER_NAMESPACE: Final = 15661
ERROR_PACKAGE_EXTERNAL_LOCATION_NOT_ALLOWED: Final = 15662
ERROR_INSTALL_FULLTRUST_HOSTRUNTIME_REQUIRES_MAIN_PACKAGE_FULLTRUST_CAPABILITY: Final = 15663
ERROR_PACKAGE_LACKS_CAPABILITY_FOR_MANDATORY_STARTUPTASKS: Final = 15664
ERROR_INSTALL_RESOLVE_HOSTRUNTIME_DEPENDENCY_FAILED: Final = 15665
ERROR_MACHINE_SCOPE_NOT_ALLOWED: Final = 15666
ERROR_CLASSIC_COMPAT_MODE_NOT_ALLOWED: Final = 15667
ERROR_STAGEFROMUPDATEAGENT_PACKAGE_NOT_APPLICABLE: Final = 15668
ERROR_PACKAGE_NOT_REGISTERED_FOR_USER: Final = 15669
ERROR_PACKAGE_NAME_MISMATCH: Final = 15670
ERROR_APPINSTALLER_URI_IN_USE: Final = 15671
ERROR_APPINSTALLER_IS_MANAGED_BY_SYSTEM: Final = 15672
APPMODEL_ERROR_NO_PACKAGE: Final = 15700
APPMODEL_ERROR_PACKAGE_RUNTIME_CORRUPT: Final = 15701
APPMODEL_ERROR_PACKAGE_IDENTITY_CORRUPT: Final = 15702
APPMODEL_ERROR_NO_APPLICATION: Final = 15703
APPMODEL_ERROR_DYNAMIC_PROPERTY_READ_FAILED: Final = 15704
APPMODEL_ERROR_DYNAMIC_PROPERTY_INVALID: Final = 15705
APPMODEL_ERROR_PACKAGE_NOT_AVAILABLE: Final = 15706
APPMODEL_ERROR_NO_MUTABLE_DIRECTORY: Final = 15707
ERROR_STATE_LOAD_STORE_FAILED: Final = 15800
ERROR_STATE_GET_VERSION_FAILED: Final = 15801
ERROR_STATE_SET_VERSION_FAILED: Final = 15802
ERROR_STATE_STRUCTURED_RESET_FAILED: Final = 15803
ERROR_STATE_OPEN_CONTAINER_FAILED: Final = 15804
ERROR_STATE_CREATE_CONTAINER_FAILED: Final = 15805
ERROR_STATE_DELETE_CONTAINER_FAILED: Final = 15806
ERROR_STATE_READ_SETTING_FAILED: Final = 15807
ERROR_STATE_WRITE_SETTING_FAILED: Final = 15808
ERROR_STATE_DELETE_SETTING_FAILED: Final = 15809
ERROR_STATE_QUERY_SETTING_FAILED: Final = 15810
ERROR_STATE_READ_COMPOSITE_SETTING_FAILED: Final = 15811
ERROR_STATE_WRITE_COMPOSITE_SETTING_FAILED: Final = 15812
ERROR_STATE_ENUMERATE_CONTAINER_FAILED: Final = 15813
ERROR_STATE_ENUMERATE_SETTINGS_FAILED: Final = 15814
ERROR_STATE_COMPOSITE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED: Final = 15815
ERROR_STATE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED: Final = 15816
ERROR_STATE_SETTING_NAME_SIZE_LIMIT_EXCEEDED: Final = 15817
ERROR_STATE_CONTAINER_NAME_SIZE_LIMIT_EXCEEDED: Final = 15818
ERROR_API_UNAVAILABLE: Final = 15841
STORE_ERROR_UNLICENSED: Final = 15861
STORE_ERROR_UNLICENSED_USER: Final = 15862
STORE_ERROR_PENDING_COM_TRANSACTION: Final = 15863
STORE_ERROR_LICENSE_REVOKED: Final = 15864
SEVERITY_SUCCESS: Final = 0
SEVERITY_ERROR: Final = 1

def SUCCEEDED(hr): ...
def FAILED(hr): ...
def HRESULT_CODE(hr): ...
def SCODE_CODE(sc): ...
def HRESULT_FACILITY(hr): ...
def SCODE_FACILITY(sc): ...
def HRESULT_SEVERITY(hr): ...
def SCODE_SEVERITY(sc): ...

FACILITY_NT_BIT: Final = 0x10000000

def HRESULT_FROM_WIN32(x): ...
def HRESULT_FROM_NT(x): ...
def GetScode(hr): ...
def ResultFromScode(sc): ...

NOERROR: Final = 0
E_UNEXPECTED: Final = -**********
E_NOTIMPL: Final = -**********
E_OUTOFMEMORY: Final = -**********
E_INVALIDARG: Final = -**********
E_NOINTERFACE: Final = -**********
E_POINTER: Final = -**********
E_HANDLE: Final = -**********
E_ABORT: Final = -**********
E_FAIL: Final = -**********
E_ACCESSDENIED: Final = -**********
E_PENDING: Final = -**********
E_BOUNDS: Final = -**********
E_CHANGED_STATE: Final = -**********
E_ILLEGAL_STATE_CHANGE: Final = -**********
E_ILLEGAL_METHOD_CALL: Final = -**********
RO_E_METADATA_NAME_NOT_FOUND: Final = -2147483633
RO_E_METADATA_NAME_IS_NAMESPACE: Final = -2147483632
RO_E_METADATA_INVALID_TYPE_FORMAT: Final = -2147483631
RO_E_INVALID_METADATA_FILE: Final = -2147483630
RO_E_CLOSED: Final = -2147483629
RO_E_EXCLUSIVE_WRITE: Final = -2147483628
RO_E_CHANGE_NOTIFICATION_IN_PROGRESS: Final = -2147483627
RO_E_ERROR_STRING_NOT_FOUND: Final = -2147483626
E_STRING_NOT_NULL_TERMINATED: Final = -2147483625
E_ILLEGAL_DELEGATE_ASSIGNMENT: Final = -2147483624
E_ASYNC_OPERATION_NOT_STARTED: Final = -2147483623
E_APPLICATION_EXITING: Final = -2147483622
E_APPLICATION_VIEW_EXITING: Final = -2147483621
RO_E_MUST_BE_AGILE: Final = -2147483620
RO_E_UNSUPPORTED_FROM_MTA: Final = -2147483619
RO_E_COMMITTED: Final = -2147483618
RO_E_BLOCKED_CROSS_ASTA_CALL: Final = -2147483617
RO_E_CANNOT_ACTIVATE_FULL_TRUST_SERVER: Final = -2147483616
RO_E_CANNOT_ACTIVATE_UNIVERSAL_APPLICATION_SERVER: Final = -2147483615
CO_E_INIT_TLS: Final = -2147467258
CO_E_INIT_SHARED_ALLOCATOR: Final = -2147467257
CO_E_INIT_MEMORY_ALLOCATOR: Final = -2147467256
CO_E_INIT_CLASS_CACHE: Final = -2147467255
CO_E_INIT_RPC_CHANNEL: Final = -2147467254
CO_E_INIT_TLS_SET_CHANNEL_CONTROL: Final = -2147467253
CO_E_INIT_TLS_CHANNEL_CONTROL: Final = -2147467252
CO_E_INIT_UNACCEPTED_USER_ALLOCATOR: Final = -2147467251
CO_E_INIT_SCM_MUTEX_EXISTS: Final = -2147467250
CO_E_INIT_SCM_FILE_MAPPING_EXISTS: Final = -2147467249
CO_E_INIT_SCM_MAP_VIEW_OF_FILE: Final = -2147467248
CO_E_INIT_SCM_EXEC_FAILURE: Final = -2147467247
CO_E_INIT_ONLY_SINGLE_THREADED: Final = -2147467246
CO_E_CANT_REMOTE: Final = -2147467245
CO_E_BAD_SERVER_NAME: Final = -2147467244
CO_E_WRONG_SERVER_IDENTITY: Final = -2147467243
CO_E_OLE1DDE_DISABLED: Final = -2147467242
CO_E_RUNAS_SYNTAX: Final = -2147467241
CO_E_CREATEPROCESS_FAILURE: Final = -2147467240
CO_E_RUNAS_CREATEPROCESS_FAILURE: Final = -2147467239
CO_E_RUNAS_LOGON_FAILURE: Final = -2147467238
CO_E_LAUNCH_PERMSSION_DENIED: Final = -2147467237
CO_E_START_SERVICE_FAILURE: Final = -2147467236
CO_E_REMOTE_COMMUNICATION_FAILURE: Final = -2147467235
CO_E_SERVER_START_TIMEOUT: Final = -2147467234
CO_E_CLSREG_INCONSISTENT: Final = -2147467233
CO_E_IIDREG_INCONSISTENT: Final = -2147467232
CO_E_NOT_SUPPORTED: Final = -2147467231
CO_E_RELOAD_DLL: Final = -2147467230
CO_E_MSI_ERROR: Final = -2147467229
CO_E_ATTEMPT_TO_CREATE_OUTSIDE_CLIENT_CONTEXT: Final = -2147467228
CO_E_SERVER_PAUSED: Final = -2147467227
CO_E_SERVER_NOT_PAUSED: Final = -2147467226
CO_E_CLASS_DISABLED: Final = -2147467225
CO_E_CLRNOTAVAILABLE: Final = -2147467224
CO_E_ASYNC_WORK_REJECTED: Final = -2147467223
CO_E_SERVER_INIT_TIMEOUT: Final = -2147467222
CO_E_NO_SECCTX_IN_ACTIVATE: Final = -2147467221
CO_E_TRACKER_CONFIG: Final = -2147467216
CO_E_THREADPOOL_CONFIG: Final = -2147467215
CO_E_SXS_CONFIG: Final = -2147467214
CO_E_MALFORMED_SPN: Final = -2147467213
CO_E_UNREVOKED_REGISTRATION_ON_APARTMENT_SHUTDOWN: Final = -2147467212
CO_E_PREMATURE_STUB_RUNDOWN: Final = -2147467211
S_OK: Final = 0
S_FALSE: Final = 1
OLE_E_FIRST: Final = -2147221504
OLE_E_LAST: Final = -2147221249
OLE_S_FIRST: Final = 0x00040000
OLE_S_LAST: Final = 0x000400FF
OLE_E_OLEVERB: Final = -2147221504
OLE_E_ADVF: Final = -2147221503
OLE_E_ENUM_NOMORE: Final = -2147221502
OLE_E_ADVISENOTSUPPORTED: Final = -2147221501
OLE_E_NOCONNECTION: Final = -2147221500
OLE_E_NOTRUNNING: Final = -2147221499
OLE_E_NOCACHE: Final = -2147221498
OLE_E_BLANK: Final = -2147221497
OLE_E_CLASSDIFF: Final = -2147221496
OLE_E_CANT_GETMONIKER: Final = -2147221495
OLE_E_CANT_BINDTOSOURCE: Final = -2147221494
OLE_E_STATIC: Final = -2147221493
OLE_E_PROMPTSAVECANCELLED: Final = -2147221492
OLE_E_INVALIDRECT: Final = -2147221491
OLE_E_WRONGCOMPOBJ: Final = -2147221490
OLE_E_INVALIDHWND: Final = -2147221489
OLE_E_NOT_INPLACEACTIVE: Final = -2147221488
OLE_E_CANTCONVERT: Final = -2147221487
OLE_E_NOSTORAGE: Final = -2147221486
DV_E_FORMATETC: Final = -2147221404
DV_E_DVTARGETDEVICE: Final = -2147221403
DV_E_STGMEDIUM: Final = -2147221402
DV_E_STATDATA: Final = -2147221401
DV_E_LINDEX: Final = -2147221400
DV_E_TYMED: Final = -2147221399
DV_E_CLIPFORMAT: Final = -2147221398
DV_E_DVASPECT: Final = -2147221397
DV_E_DVTARGETDEVICE_SIZE: Final = -2147221396
DV_E_NOIVIEWOBJECT: Final = -2147221395
DRAGDROP_E_FIRST: Final = -**********
DRAGDROP_E_LAST: Final = -2147221233
DRAGDROP_S_FIRST: Final = 0x00040100
DRAGDROP_S_LAST: Final = 0x0004010F
DRAGDROP_E_NOTREGISTERED: Final = -**********
DRAGDROP_E_ALREADYREGISTERED: Final = -**********
DRAGDROP_E_INVALIDHWND: Final = -**********
DRAGDROP_E_CONCURRENT_DRAG_ATTEMPTED: Final = -**********
CLASSFACTORY_E_FIRST: Final = -**********
CLASSFACTORY_E_LAST: Final = -**********
CLASSFACTORY_S_FIRST: Final = 0x00040110
CLASSFACTORY_S_LAST: Final = 0x0004011F
CLASS_E_NOAGGREGATION: Final = -**********
CLASS_E_CLASSNOTAVAILABLE: Final = -**********
CLASS_E_NOTLICENSED: Final = -**********
MARSHAL_E_FIRST: Final = -**********
MARSHAL_E_LAST: Final = -**********
MARSHAL_S_FIRST: Final = 0x00040120
MARSHAL_S_LAST: Final = 0x0004012F
DATA_E_FIRST: Final = -**********
DATA_E_LAST: Final = -**********
DATA_S_FIRST: Final = 0x00040130
DATA_S_LAST: Final = 0x0004013F
VIEW_E_FIRST: Final = -**********
VIEW_E_LAST: Final = -**********
VIEW_S_FIRST: Final = 0x00040140
VIEW_S_LAST: Final = 0x0004014F
VIEW_E_DRAW: Final = -**********
REGDB_E_FIRST: Final = -**********
REGDB_E_LAST: Final = -**********
REGDB_S_FIRST: Final = 0x00040150
REGDB_S_LAST: Final = 0x0004015F
REGDB_E_READREGDB: Final = -**********
REGDB_E_WRITEREGDB: Final = -**********
REGDB_E_KEYMISSING: Final = -**********
REGDB_E_INVALIDVALUE: Final = -**********
REGDB_E_CLASSNOTREG: Final = -**********
REGDB_E_IIDNOTREG: Final = -**********
REGDB_E_BADTHREADINGMODEL: Final = -**********
REGDB_E_PACKAGEPOLICYVIOLATION: Final = -**********
CAT_E_FIRST: Final = -**********
CAT_E_LAST: Final = -2147221151
CAT_E_CATIDNOEXIST: Final = -**********
CAT_E_NODESCRIPTION: Final = -2147221151
CS_E_FIRST: Final = -2147221148
CS_E_LAST: Final = -2147221137
CS_E_PACKAGE_NOTFOUND: Final = -2147221148
CS_E_NOT_DELETABLE: Final = -2147221147
CS_E_CLASS_NOTFOUND: Final = -2147221146
CS_E_INVALID_VERSION: Final = -2147221145
CS_E_NO_CLASSSTORE: Final = -2147221144
CS_E_OBJECT_NOTFOUND: Final = -2147221143
CS_E_OBJECT_ALREADY_EXISTS: Final = -2147221142
CS_E_INVALID_PATH: Final = -2147221141
CS_E_NETWORK_ERROR: Final = -2147221140
CS_E_ADMIN_LIMIT_EXCEEDED: Final = -2147221139
CS_E_SCHEMA_MISMATCH: Final = -2147221138
CS_E_INTERNAL_ERROR: Final = -2147221137
CACHE_E_FIRST: Final = -2147221136
CACHE_E_LAST: Final = -2147221121
CACHE_S_FIRST: Final = 0x00040170
CACHE_S_LAST: Final = 0x0004017F
CACHE_E_NOCACHE_UPDATED: Final = -2147221136
OLEOBJ_E_FIRST: Final = -2147221120
OLEOBJ_E_LAST: Final = -2147221105
OLEOBJ_S_FIRST: Final = 0x00040180
OLEOBJ_S_LAST: Final = 0x0004018F
OLEOBJ_E_NOVERBS: Final = -2147221120
OLEOBJ_E_INVALIDVERB: Final = -2147221119
CLIENTSITE_E_FIRST: Final = -2147221104
CLIENTSITE_E_LAST: Final = -2147221089
CLIENTSITE_S_FIRST: Final = 0x00040190
CLIENTSITE_S_LAST: Final = 0x0004019F
INPLACE_E_NOTUNDOABLE: Final = -2147221088
INPLACE_E_NOTOOLSPACE: Final = -2147221087
INPLACE_E_FIRST: Final = -2147221088
INPLACE_E_LAST: Final = -2147221073
INPLACE_S_FIRST: Final = 0x000401A0
INPLACE_S_LAST: Final = 0x000401AF
ENUM_E_FIRST: Final = -2147221072
ENUM_E_LAST: Final = -2147221057
ENUM_S_FIRST: Final = 0x000401B0
ENUM_S_LAST: Final = 0x000401BF
CONVERT10_E_FIRST: Final = -2147221056
CONVERT10_E_LAST: Final = -2147221041
CONVERT10_S_FIRST: Final = 0x000401C0
CONVERT10_S_LAST: Final = 0x000401CF
CONVERT10_E_OLESTREAM_GET: Final = -2147221056
CONVERT10_E_OLESTREAM_PUT: Final = -2147221055
CONVERT10_E_OLESTREAM_FMT: Final = -2147221054
CONVERT10_E_OLESTREAM_BITMAP_TO_DIB: Final = -2147221053
CONVERT10_E_STG_FMT: Final = -2147221052
CONVERT10_E_STG_NO_STD_STREAM: Final = -2147221051
CONVERT10_E_STG_DIB_TO_BITMAP: Final = -2147221050
CONVERT10_E_OLELINK_DISABLED: Final = -2147221049
CLIPBRD_E_FIRST: Final = -2147221040
CLIPBRD_E_LAST: Final = -2147221025
CLIPBRD_S_FIRST: Final = 0x000401D0
CLIPBRD_S_LAST: Final = 0x000401DF
CLIPBRD_E_CANT_OPEN: Final = -2147221040
CLIPBRD_E_CANT_EMPTY: Final = -2147221039
CLIPBRD_E_CANT_SET: Final = -2147221038
CLIPBRD_E_BAD_DATA: Final = -2147221037
CLIPBRD_E_CANT_CLOSE: Final = -2147221036
MK_E_FIRST: Final = -2147221024
MK_E_LAST: Final = -2147221009
MK_S_FIRST: Final = 0x000401E0
MK_S_LAST: Final = 0x000401EF
MK_E_CONNECTMANUALLY: Final = -2147221024
MK_E_EXCEEDEDDEADLINE: Final = -2147221023
MK_E_NEEDGENERIC: Final = -2147221022
MK_E_UNAVAILABLE: Final = -2147221021
MK_E_SYNTAX: Final = -2147221020
MK_E_NOOBJECT: Final = -2147221019
MK_E_INVALIDEXTENSION: Final = -2147221018
MK_E_INTERMEDIATEINTERFACENOTSUPPORTED: Final = -2147221017
MK_E_NOTBINDABLE: Final = -2147221016
MK_E_NOTBOUND: Final = -2147221015
MK_E_CANTOPENFILE: Final = -2147221014
MK_E_MUSTBOTHERUSER: Final = -2147221013
MK_E_NOINVERSE: Final = -2147221012
MK_E_NOSTORAGE: Final = -2147221011
MK_E_NOPREFIX: Final = -2147221010
MK_E_ENUMERATION_FAILED: Final = -2147221009
CO_E_FIRST: Final = -2147221008
CO_E_LAST: Final = -2147220993
CO_S_FIRST: Final = 0x000401F0
CO_S_LAST: Final = 0x000401FF
CO_E_NOTINITIALIZED: Final = -2147221008
CO_E_ALREADYINITIALIZED: Final = -2147221007
CO_E_CANTDETERMINECLASS: Final = -2147221006
CO_E_CLASSSTRING: Final = -2147221005
CO_E_IIDSTRING: Final = -2147221004
CO_E_APPNOTFOUND: Final = -2147221003
CO_E_APPSINGLEUSE: Final = -2147221002
CO_E_ERRORINAPP: Final = -2147221001
CO_E_DLLNOTFOUND: Final = -2147221000
CO_E_ERRORINDLL: Final = -2147220999
CO_E_WRONGOSFORAPP: Final = -2147220998
CO_E_OBJNOTREG: Final = -2147220997
CO_E_OBJISREG: Final = -2147220996
CO_E_OBJNOTCONNECTED: Final = -2147220995
CO_E_APPDIDNTREG: Final = -2147220994
CO_E_RELEASED: Final = -2147220993
EVENT_E_FIRST: Final = -2147220992
EVENT_E_LAST: Final = -2147220961
EVENT_S_FIRST: Final = 0x00040200
EVENT_S_LAST: Final = 0x0004021F
EVENT_S_SOME_SUBSCRIBERS_FAILED: Final = 0x00040200
EVENT_E_ALL_SUBSCRIBERS_FAILED: Final = -2147220991
EVENT_S_NOSUBSCRIBERS: Final = 0x00040202
EVENT_E_QUERYSYNTAX: Final = -2147220989
EVENT_E_QUERYFIELD: Final = -2147220988
EVENT_E_INTERNALEXCEPTION: Final = -2147220987
EVENT_E_INTERNALERROR: Final = -2147220986
EVENT_E_INVALID_PER_USER_SID: Final = -2147220985
EVENT_E_USER_EXCEPTION: Final = -2147220984
EVENT_E_TOO_MANY_METHODS: Final = -2147220983
EVENT_E_MISSING_EVENTCLASS: Final = -2147220982
EVENT_E_NOT_ALL_REMOVED: Final = -2147220981
EVENT_E_COMPLUS_NOT_INSTALLED: Final = -2147220980
EVENT_E_CANT_MODIFY_OR_DELETE_UNCONFIGURED_OBJECT: Final = -2147220979
EVENT_E_CANT_MODIFY_OR_DELETE_CONFIGURED_OBJECT: Final = -2147220978
EVENT_E_INVALID_EVENT_CLASS_PARTITION: Final = -2147220977
EVENT_E_PER_USER_SID_NOT_LOGGED_ON: Final = -2147220976
TPC_E_INVALID_PROPERTY: Final = -2147220927
TPC_E_NO_DEFAULT_TABLET: Final = -2147220974
TPC_E_UNKNOWN_PROPERTY: Final = -2147220965
TPC_E_INVALID_INPUT_RECT: Final = -2147220967
TPC_E_INVALID_STROKE: Final = -2147220958
TPC_E_INITIALIZE_FAIL: Final = -2147220957
TPC_E_NOT_RELEVANT: Final = -2147220942
TPC_E_INVALID_PACKET_DESCRIPTION: Final = -2147220941
TPC_E_RECOGNIZER_NOT_REGISTERED: Final = -2147220939
TPC_E_INVALID_RIGHTS: Final = -2147220938
TPC_E_OUT_OF_ORDER_CALL: Final = -2147220937
TPC_E_QUEUE_FULL: Final = -2147220936
TPC_E_INVALID_CONFIGURATION: Final = -2147220935
TPC_E_INVALID_DATA_FROM_RECOGNIZER: Final = -2147220934
TPC_S_TRUNCATED: Final = 0x00040252
TPC_S_INTERRUPTED: Final = 0x00040253
TPC_S_NO_DATA_TO_PROCESS: Final = 0x00040254
XACT_E_FIRST: Final = -2147168256
XACT_E_LAST: Final = -2147168213
XACT_S_FIRST: Final = 0x0004D000
XACT_S_LAST: Final = 0x0004D010
XACT_E_ALREADYOTHERSINGLEPHASE: Final = -2147168256
XACT_E_CANTRETAIN: Final = -2147168255
XACT_E_COMMITFAILED: Final = -2147168254
XACT_E_COMMITPREVENTED: Final = -2147168253
XACT_E_HEURISTICABORT: Final = -2147168252
XACT_E_HEURISTICCOMMIT: Final = -2147168251
XACT_E_HEURISTICDAMAGE: Final = -2147168250
XACT_E_HEURISTICDANGER: Final = -2147168249
XACT_E_ISOLATIONLEVEL: Final = -2147168248
XACT_E_NOASYNC: Final = -2147168247
XACT_E_NOENLIST: Final = -2147168246
XACT_E_NOISORETAIN: Final = -2147168245
XACT_E_NORESOURCE: Final = -2147168244
XACT_E_NOTCURRENT: Final = -2147168243
XACT_E_NOTRANSACTION: Final = -2147168242
XACT_E_NOTSUPPORTED: Final = -2147168241
XACT_E_UNKNOWNRMGRID: Final = -2147168240
XACT_E_WRONGSTATE: Final = -2147168239
XACT_E_WRONGUOW: Final = -2147168238
XACT_E_XTIONEXISTS: Final = -2147168237
XACT_E_NOIMPORTOBJECT: Final = -2147168236
XACT_E_INVALIDCOOKIE: Final = -2147168235
XACT_E_INDOUBT: Final = -2147168234
XACT_E_NOTIMEOUT: Final = -2147168233
XACT_E_ALREADYINPROGRESS: Final = -2147168232
XACT_E_ABORTED: Final = -2147168231
XACT_E_LOGFULL: Final = -2147168230
XACT_E_TMNOTAVAILABLE: Final = -2147168229
XACT_E_CONNECTION_DOWN: Final = -2147168228
XACT_E_CONNECTION_DENIED: Final = -2147168227
XACT_E_REENLISTTIMEOUT: Final = -2147168226
XACT_E_TIP_CONNECT_FAILED: Final = -2147168225
XACT_E_TIP_PROTOCOL_ERROR: Final = -2147168224
XACT_E_TIP_PULL_FAILED: Final = -2147168223
XACT_E_DEST_TMNOTAVAILABLE: Final = -2147168222
XACT_E_TIP_DISABLED: Final = -2147168221
XACT_E_NETWORK_TX_DISABLED: Final = -2147168220
XACT_E_PARTNER_NETWORK_TX_DISABLED: Final = -2147168219
XACT_E_XA_TX_DISABLED: Final = -2147168218
XACT_E_UNABLE_TO_READ_DTC_CONFIG: Final = -2147168217
XACT_E_UNABLE_TO_LOAD_DTC_PROXY: Final = -2147168216
XACT_E_ABORTING: Final = -2147168215
XACT_E_PUSH_COMM_FAILURE: Final = -2147168214
XACT_E_PULL_COMM_FAILURE: Final = -2147168213
XACT_E_LU_TX_DISABLED: Final = -2147168212
XACT_E_CLERKNOTFOUND: Final = -2147168128
XACT_E_CLERKEXISTS: Final = -2147168127
XACT_E_RECOVERYINPROGRESS: Final = -2147168126
XACT_E_TRANSACTIONCLOSED: Final = -2147168125
XACT_E_INVALIDLSN: Final = -2147168124
XACT_E_REPLAYREQUEST: Final = -2147168123
XACT_S_ASYNC: Final = 0x0004D000
XACT_S_DEFECT: Final = 0x0004D001
XACT_S_READONLY: Final = 0x0004D002
XACT_S_SOMENORETAIN: Final = 0x0004D003
XACT_S_OKINFORM: Final = 0x0004D004
XACT_S_MADECHANGESCONTENT: Final = 0x0004D005
XACT_S_MADECHANGESINFORM: Final = 0x0004D006
XACT_S_ALLNORETAIN: Final = 0x0004D007
XACT_S_ABORTING: Final = 0x0004D008
XACT_S_SINGLEPHASE: Final = 0x0004D009
XACT_S_LOCALLY_OK: Final = 0x0004D00A
XACT_S_LASTRESOURCEMANAGER: Final = 0x0004D010
CONTEXT_E_FIRST: Final = -2147164160
CONTEXT_E_LAST: Final = -2147164113
CONTEXT_S_FIRST: Final = 0x0004E000
CONTEXT_S_LAST: Final = 0x0004E02F
CONTEXT_E_ABORTED: Final = -2147164158
CONTEXT_E_ABORTING: Final = -2147164157
CONTEXT_E_NOCONTEXT: Final = -2147164156
CONTEXT_E_WOULD_DEADLOCK: Final = -2147164155
CONTEXT_E_SYNCH_TIMEOUT: Final = -2147164154
CONTEXT_E_OLDREF: Final = -2147164153
CONTEXT_E_ROLENOTFOUND: Final = -2147164148
CONTEXT_E_TMNOTAVAILABLE: Final = -2147164145
CO_E_ACTIVATIONFAILED: Final = -2147164127
CO_E_ACTIVATIONFAILED_EVENTLOGGED: Final = -2147164126
CO_E_ACTIVATIONFAILED_CATALOGERROR: Final = -2147164125
CO_E_ACTIVATIONFAILED_TIMEOUT: Final = -2147164124
CO_E_INITIALIZATIONFAILED: Final = -2147164123
CONTEXT_E_NOJIT: Final = -2147164122
CONTEXT_E_NOTRANSACTION: Final = -2147164121
CO_E_THREADINGMODEL_CHANGED: Final = -2147164120
CO_E_NOIISINTRINSICS: Final = -2147164119
CO_E_NOCOOKIES: Final = -2147164118
CO_E_DBERROR: Final = -2147164117
CO_E_NOTPOOLED: Final = -2147164116
CO_E_NOTCONSTRUCTED: Final = -2147164115
CO_E_NOSYNCHRONIZATION: Final = -2147164114
CO_E_ISOLEVELMISMATCH: Final = -2147164113
CO_E_CALL_OUT_OF_TX_SCOPE_NOT_ALLOWED: Final = -2147164112
CO_E_EXIT_TRANSACTION_SCOPE_NOT_CALLED: Final = -2147164111
OLE_S_USEREG: Final = 0x00040000
OLE_S_STATIC: Final = 0x00040001
OLE_S_MAC_CLIPFORMAT: Final = 0x00040002
DRAGDROP_S_DROP: Final = 0x00040100
DRAGDROP_S_CANCEL: Final = 0x00040101
DRAGDROP_S_USEDEFAULTCURSORS: Final = 0x00040102
DATA_S_SAMEFORMATETC: Final = 0x00040130
VIEW_S_ALREADY_FROZEN: Final = 0x00040140
CACHE_S_FORMATETC_NOTSUPPORTED: Final = 0x00040170
CACHE_S_SAMECACHE: Final = 0x00040171
CACHE_S_SOMECACHES_NOTUPDATED: Final = 0x00040172
OLEOBJ_S_INVALIDVERB: Final = 0x00040180
OLEOBJ_S_CANNOT_DOVERB_NOW: Final = 0x00040181
OLEOBJ_S_INVALIDHWND: Final = 0x00040182
INPLACE_S_TRUNCATED: Final = 0x000401A0
CONVERT10_S_NO_PRESENTATION: Final = 0x000401C0
MK_S_REDUCED_TO_SELF: Final = 0x000401E2
MK_S_ME: Final = 0x000401E4
MK_S_HIM: Final = 0x000401E5
MK_S_US: Final = 0x000401E6
MK_S_MONIKERALREADYREGISTERED: Final = 0x000401E7
SCHED_S_TASK_READY: Final = 0x00041300
SCHED_S_TASK_RUNNING: Final = 0x00041301
SCHED_S_TASK_DISABLED: Final = 0x00041302
SCHED_S_TASK_HAS_NOT_RUN: Final = 0x00041303
SCHED_S_TASK_NO_MORE_RUNS: Final = 0x00041304
SCHED_S_TASK_NOT_SCHEDULED: Final = 0x00041305
SCHED_S_TASK_TERMINATED: Final = 0x00041306
SCHED_S_TASK_NO_VALID_TRIGGERS: Final = 0x00041307
SCHED_S_EVENT_TRIGGER: Final = 0x00041308
SCHED_E_TRIGGER_NOT_FOUND: Final = -**********
SCHED_E_TASK_NOT_READY: Final = -**********
SCHED_E_TASK_NOT_RUNNING: Final = -**********
SCHED_E_SERVICE_NOT_INSTALLED: Final = -**********
SCHED_E_CANNOT_OPEN_TASK: Final = -**********
SCHED_E_INVALID_TASK: Final = -**********
SCHED_E_ACCOUNT_INFORMATION_NOT_SET: Final = -**********
SCHED_E_ACCOUNT_NAME_NOT_FOUND: Final = -**********
SCHED_E_ACCOUNT_DBASE_CORRUPT: Final = -**********
SCHED_E_NO_SECURITY_SERVICES: Final = -**********
SCHED_E_UNKNOWN_OBJECT_VERSION: Final = -**********
SCHED_E_UNSUPPORTED_ACCOUNT_OPTION: Final = -**********
SCHED_E_SERVICE_NOT_RUNNING: Final = -**********
SCHED_E_UNEXPECTEDNODE: Final = -**********
SCHED_E_NAMESPACE: Final = -**********
SCHED_E_INVALIDVALUE: Final = -**********
SCHED_E_MISSINGNODE: Final = -**********
SCHED_E_MALFORMEDXML: Final = -**********
SCHED_S_SOME_TRIGGERS_FAILED: Final = 0x0004131B
SCHED_S_BATCH_LOGON_PROBLEM: Final = 0x0004131C
SCHED_E_TOO_MANY_NODES: Final = -**********
SCHED_E_PAST_END_BOUNDARY: Final = -**********
SCHED_E_ALREADY_RUNNING: Final = -**********
SCHED_E_USER_NOT_LOGGED_ON: Final = -**********
SCHED_E_INVALID_TASK_HASH: Final = -**********
SCHED_E_SERVICE_NOT_AVAILABLE: Final = -**********
SCHED_E_SERVICE_TOO_BUSY: Final = -**********
SCHED_E_TASK_ATTEMPTED: Final = -**********
SCHED_S_TASK_QUEUED: Final = 0x00041325
SCHED_E_TASK_DISABLED: Final = -**********
SCHED_E_TASK_NOT_V1_COMPAT: Final = -**********
SCHED_E_START_ON_DEMAND: Final = -**********
SCHED_E_TASK_NOT_UBPM_COMPAT: Final = -**********
SCHED_E_DEPRECATED_FEATURE_USED: Final = -2147216592
CO_E_CLASS_CREATE_FAILED: Final = -2146959359
CO_E_SCM_ERROR: Final = -2146959358
CO_E_SCM_RPC_FAILURE: Final = -2146959357
CO_E_BAD_PATH: Final = -2146959356
CO_E_SERVER_EXEC_FAILURE: Final = -2146959355
CO_E_OBJSRV_RPC_FAILURE: Final = -2146959354
MK_E_NO_NORMALIZED: Final = -2146959353
CO_E_SERVER_STOPPING: Final = -2146959352
MEM_E_INVALID_ROOT: Final = -2146959351
MEM_E_INVALID_LINK: Final = -2146959344
MEM_E_INVALID_SIZE: Final = -2146959343
CO_S_NOTALLINTERFACES: Final = 0x00080012
CO_S_MACHINENAMENOTFOUND: Final = 0x00080013
CO_E_MISSING_DISPLAYNAME: Final = -2146959339
CO_E_RUNAS_VALUE_MUST_BE_AAA: Final = -2146959338
CO_E_ELEVATION_DISABLED: Final = -2146959337
APPX_E_PACKAGING_INTERNAL: Final = -2146958848
APPX_E_INTERLEAVING_NOT_ALLOWED: Final = -2146958847
APPX_E_RELATIONSHIPS_NOT_ALLOWED: Final = -2146958846
APPX_E_MISSING_REQUIRED_FILE: Final = -2146958845
APPX_E_INVALID_MANIFEST: Final = -2146958844
APPX_E_INVALID_BLOCKMAP: Final = -2146958843
APPX_E_CORRUPT_CONTENT: Final = -2146958842
APPX_E_BLOCK_HASH_INVALID: Final = -2146958841
APPX_E_REQUESTED_RANGE_TOO_LARGE: Final = -2146958840
APPX_E_INVALID_SIP_CLIENT_DATA: Final = -2146958839
APPX_E_INVALID_KEY_INFO: Final = -2146958838
APPX_E_INVALID_CONTENTGROUPMAP: Final = -2146958837
APPX_E_INVALID_APPINSTALLER: Final = -2146958836
APPX_E_DELTA_BASELINE_VERSION_MISMATCH: Final = -2146958835
APPX_E_DELTA_PACKAGE_MISSING_FILE: Final = -2146958834
APPX_E_INVALID_DELTA_PACKAGE: Final = -2146958833
APPX_E_DELTA_APPENDED_PACKAGE_NOT_ALLOWED: Final = -2146958832
APPX_E_INVALID_PACKAGING_LAYOUT: Final = -2146958831
APPX_E_INVALID_PACKAGESIGNCONFIG: Final = -2146958830
APPX_E_RESOURCESPRI_NOT_ALLOWED: Final = -2146958829
APPX_E_FILE_COMPRESSION_MISMATCH: Final = -2146958828
APPX_E_INVALID_PAYLOAD_PACKAGE_EXTENSION: Final = -2146958827
APPX_E_INVALID_ENCRYPTION_EXCLUSION_FILE_LIST: Final = -2146958826
APPX_E_INVALID_PACKAGE_FOLDER_ACLS: Final = -2146958825
APPX_E_INVALID_PUBLISHER_BRIDGING: Final = -2146958824
APPX_E_DIGEST_MISMATCH: Final = -2146958823
BT_E_SPURIOUS_ACTIVATION: Final = -2146958592
DISP_E_UNKNOWNINTERFACE: Final = -2147352575
DISP_E_MEMBERNOTFOUND: Final = -2147352573
DISP_E_PARAMNOTFOUND: Final = -2147352572
DISP_E_TYPEMISMATCH: Final = -2147352571
DISP_E_UNKNOWNNAME: Final = -2147352570
DISP_E_NONAMEDARGS: Final = -2147352569
DISP_E_BADVARTYPE: Final = -2147352568
DISP_E_EXCEPTION: Final = -2147352567
DISP_E_OVERFLOW: Final = -2147352566
DISP_E_BADINDEX: Final = -2147352565
DISP_E_UNKNOWNLCID: Final = -2147352564
DISP_E_ARRAYISLOCKED: Final = -2147352563
DISP_E_BADPARAMCOUNT: Final = -2147352562
DISP_E_PARAMNOTOPTIONAL: Final = -2147352561
DISP_E_BADCALLEE: Final = -2147352560
DISP_E_NOTACOLLECTION: Final = -2147352559
DISP_E_DIVBYZERO: Final = -2147352558
DISP_E_BUFFERTOOSMALL: Final = -2147352557
TYPE_E_BUFFERTOOSMALL: Final = -2147319786
TYPE_E_FIELDNOTFOUND: Final = -2147319785
TYPE_E_INVDATAREAD: Final = -2147319784
TYPE_E_UNSUPFORMAT: Final = -2147319783
TYPE_E_REGISTRYACCESS: Final = -2147319780
TYPE_E_LIBNOTREGISTERED: Final = -2147319779
TYPE_E_UNDEFINEDTYPE: Final = -2147319769
TYPE_E_QUALIFIEDNAMEDISALLOWED: Final = -2147319768
TYPE_E_INVALIDSTATE: Final = -2147319767
TYPE_E_WRONGTYPEKIND: Final = -2147319766
TYPE_E_ELEMENTNOTFOUND: Final = -2147319765
TYPE_E_AMBIGUOUSNAME: Final = -2147319764
TYPE_E_NAMECONFLICT: Final = -2147319763
TYPE_E_UNKNOWNLCID: Final = -2147319762
TYPE_E_DLLFUNCTIONNOTFOUND: Final = -2147319761
TYPE_E_BADMODULEKIND: Final = -2147317571
TYPE_E_SIZETOOBIG: Final = -2147317563
TYPE_E_DUPLICATEID: Final = -2147317562
TYPE_E_INVALIDID: Final = -2147317553
TYPE_E_TYPEMISMATCH: Final = -2147316576
TYPE_E_OUTOFBOUNDS: Final = -2147316575
TYPE_E_IOERROR: Final = -2147316574
TYPE_E_CANTCREATETMPFILE: Final = -2147316573
TYPE_E_CANTLOADLIBRARY: Final = -2147312566
TYPE_E_INCONSISTENTPROPFUNCS: Final = -2147312509
TYPE_E_CIRCULARTYPE: Final = -2147312508
STG_E_INVALIDFUNCTION: Final = -2147287039
STG_E_FILENOTFOUND: Final = -2147287038
STG_E_PATHNOTFOUND: Final = -2147287037
STG_E_TOOMANYOPENFILES: Final = -2147287036
STG_E_ACCESSDENIED: Final = -2147287035
STG_E_INVALIDHANDLE: Final = -2147287034
STG_E_INSUFFICIENTMEMORY: Final = -2147287032
STG_E_INVALIDPOINTER: Final = -2147287031
STG_E_NOMOREFILES: Final = -2147287022
STG_E_DISKISWRITEPROTECTED: Final = -2147287021
STG_E_SEEKERROR: Final = -2147287015
STG_E_WRITEFAULT: Final = -2147287011
STG_E_READFAULT: Final = -2147287010
STG_E_SHAREVIOLATION: Final = -2147287008
STG_E_LOCKVIOLATION: Final = -2147287007
STG_E_FILEALREADYEXISTS: Final = -2147286960
STG_E_INVALIDPARAMETER: Final = -2147286953
STG_E_MEDIUMFULL: Final = -2147286928
STG_E_PROPSETMISMATCHED: Final = -2147286800
STG_E_ABNORMALAPIEXIT: Final = -2147286790
STG_E_INVALIDHEADER: Final = -2147286789
STG_E_INVALIDNAME: Final = -2147286788
STG_E_UNKNOWN: Final = -2147286787
STG_E_UNIMPLEMENTEDFUNCTION: Final = -2147286786
STG_E_INVALIDFLAG: Final = -2147286785
STG_E_INUSE: Final = -2147286784
STG_E_NOTCURRENT: Final = -2147286783
STG_E_REVERTED: Final = -2147286782
STG_E_CANTSAVE: Final = -2147286781
STG_E_OLDFORMAT: Final = -2147286780
STG_E_OLDDLL: Final = -2147286779
STG_E_SHAREREQUIRED: Final = -2147286778
STG_E_NOTFILEBASEDSTORAGE: Final = -2147286777
STG_E_EXTANTMARSHALLINGS: Final = -2147286776
STG_E_DOCFILECORRUPT: Final = -2147286775
STG_E_BADBASEADDRESS: Final = -2147286768
STG_E_DOCFILETOOLARGE: Final = -2147286767
STG_E_NOTSIMPLEFORMAT: Final = -2147286766
STG_E_INCOMPLETE: Final = -2147286527
STG_E_TERMINATED: Final = -2147286526
STG_S_CONVERTED: Final = 0x00030200
STG_S_BLOCK: Final = 0x00030201
STG_S_RETRYNOW: Final = 0x00030202
STG_S_MONITORING: Final = 0x00030203
STG_S_MULTIPLEOPENS: Final = 0x00030204
STG_S_CONSOLIDATIONFAILED: Final = 0x00030205
STG_S_CANNOTCONSOLIDATE: Final = 0x00030206
STG_S_POWER_CYCLE_REQUIRED: Final = 0x00030207
STG_E_FIRMWARE_SLOT_INVALID: Final = -2147286520
STG_E_FIRMWARE_IMAGE_INVALID: Final = -2147286519
STG_E_DEVICE_UNRESPONSIVE: Final = -2147286518
STG_E_STATUS_COPY_PROTECTION_FAILURE: Final = -2147286267
STG_E_CSS_AUTHENTICATION_FAILURE: Final = -2147286266
STG_E_CSS_KEY_NOT_PRESENT: Final = -2147286265
STG_E_CSS_KEY_NOT_ESTABLISHED: Final = -2147286264
STG_E_CSS_SCRAMBLED_SECTOR: Final = -2147286263
STG_E_CSS_REGION_MISMATCH: Final = -2147286262
STG_E_RESETS_EXHAUSTED: Final = -2147286261
RPC_E_CALL_REJECTED: Final = -2147418111
RPC_E_CALL_CANCELED: Final = -2147418110
RPC_E_CANTPOST_INSENDCALL: Final = -2147418109
RPC_E_CANTCALLOUT_INASYNCCALL: Final = -2147418108
RPC_E_CANTCALLOUT_INEXTERNALCALL: Final = -2147418107
RPC_E_CONNECTION_TERMINATED: Final = -2147418106
RPC_E_SERVER_DIED: Final = -2147418105
RPC_E_CLIENT_DIED: Final = -2147418104
RPC_E_INVALID_DATAPACKET: Final = -2147418103
RPC_E_CANTTRANSMIT_CALL: Final = -2147418102
RPC_E_CLIENT_CANTMARSHAL_DATA: Final = -2147418101
RPC_E_CLIENT_CANTUNMARSHAL_DATA: Final = -2147418100
RPC_E_SERVER_CANTMARSHAL_DATA: Final = -2147418099
RPC_E_SERVER_CANTUNMARSHAL_DATA: Final = -2147418098
RPC_E_INVALID_DATA: Final = -2147418097
RPC_E_INVALID_PARAMETER: Final = -2147418096
RPC_E_CANTCALLOUT_AGAIN: Final = -2147418095
RPC_E_SERVER_DIED_DNE: Final = -2147418094
RPC_E_SYS_CALL_FAILED: Final = -2147417856
RPC_E_OUT_OF_RESOURCES: Final = -2147417855
RPC_E_ATTEMPTED_MULTITHREAD: Final = -2147417854
RPC_E_NOT_REGISTERED: Final = -2147417853
RPC_E_FAULT: Final = -2147417852
RPC_E_SERVERFAULT: Final = -2147417851
RPC_E_CHANGED_MODE: Final = -2147417850
RPC_E_INVALIDMETHOD: Final = -2147417849
RPC_E_DISCONNECTED: Final = -2147417848
RPC_E_RETRY: Final = -2147417847
RPC_E_SERVERCALL_RETRYLATER: Final = -2147417846
RPC_E_SERVERCALL_REJECTED: Final = -2147417845
RPC_E_INVALID_CALLDATA: Final = -2147417844
RPC_E_CANTCALLOUT_ININPUTSYNCCALL: Final = -2147417843
RPC_E_WRONG_THREAD: Final = -2147417842
RPC_E_THREAD_NOT_INIT: Final = -2147417841
RPC_E_VERSION_MISMATCH: Final = -2147417840
RPC_E_INVALID_HEADER: Final = -2147417839
RPC_E_INVALID_EXTENSION: Final = -2147417838
RPC_E_INVALID_IPID: Final = -2147417837
RPC_E_INVALID_OBJECT: Final = -2147417836
RPC_S_CALLPENDING: Final = -2147417835
RPC_S_WAITONTIMER: Final = -2147417834
RPC_E_CALL_COMPLETE: Final = -2147417833
RPC_E_UNSECURE_CALL: Final = -2147417832
RPC_E_TOO_LATE: Final = -2147417831
RPC_E_NO_GOOD_SECURITY_PACKAGES: Final = -2147417830
RPC_E_ACCESS_DENIED: Final = -2147417829
RPC_E_REMOTE_DISABLED: Final = -2147417828
RPC_E_INVALID_OBJREF: Final = -2147417827
RPC_E_NO_CONTEXT: Final = -2147417826
RPC_E_TIMEOUT: Final = -2147417825
RPC_E_NO_SYNC: Final = -2147417824
RPC_E_FULLSIC_REQUIRED: Final = -2147417823
RPC_E_INVALID_STD_NAME: Final = -2147417822
CO_E_FAILEDTOIMPERSONATE: Final = -2147417821
CO_E_FAILEDTOGETSECCTX: Final = -2147417820
CO_E_FAILEDTOOPENTHREADTOKEN: Final = -2147417819
CO_E_FAILEDTOGETTOKENINFO: Final = -2147417818
CO_E_TRUSTEEDOESNTMATCHCLIENT: Final = -2147417817
CO_E_FAILEDTOQUERYCLIENTBLANKET: Final = -2147417816
CO_E_FAILEDTOSETDACL: Final = -2147417815
CO_E_ACCESSCHECKFAILED: Final = -2147417814
CO_E_NETACCESSAPIFAILED: Final = -2147417813
CO_E_WRONGTRUSTEENAMESYNTAX: Final = -2147417812
CO_E_INVALIDSID: Final = -2147417811
CO_E_CONVERSIONFAILED: Final = -2147417810
CO_E_NOMATCHINGSIDFOUND: Final = -2147417809
CO_E_LOOKUPACCSIDFAILED: Final = -2147417808
CO_E_NOMATCHINGNAMEFOUND: Final = -2147417807
CO_E_LOOKUPACCNAMEFAILED: Final = -2147417806
CO_E_SETSERLHNDLFAILED: Final = -2147417805
CO_E_FAILEDTOGETWINDIR: Final = -2147417804
CO_E_PATHTOOLONG: Final = -2147417803
CO_E_FAILEDTOGENUUID: Final = -2147417802
CO_E_FAILEDTOCREATEFILE: Final = -2147417801
CO_E_FAILEDTOCLOSEHANDLE: Final = -2147417800
CO_E_EXCEEDSYSACLLIMIT: Final = -2147417799
CO_E_ACESINWRONGORDER: Final = -2147417798
CO_E_INCOMPATIBLESTREAMVERSION: Final = -2147417797
CO_E_FAILEDTOOPENPROCESSTOKEN: Final = -2147417796
CO_E_DECODEFAILED: Final = -2147417795
CO_E_ACNOTINITIALIZED: Final = -2147417793
CO_E_CANCEL_DISABLED: Final = -2147417792
RPC_E_UNEXPECTED: Final = -2147352577
ERROR_AUDITING_DISABLED: Final = -1073151999
ERROR_ALL_SIDS_FILTERED: Final = -1073151998
ERROR_BIZRULES_NOT_ENABLED: Final = -1073151997
NTE_BAD_UID: Final = -2146893823
NTE_BAD_HASH: Final = -2146893822
NTE_BAD_KEY: Final = -2146893821
NTE_BAD_LEN: Final = -**********
NTE_BAD_DATA: Final = -**********
NTE_BAD_SIGNATURE: Final = -**********
NTE_BAD_VER: Final = -**********
NTE_BAD_ALGID: Final = -**********
NTE_BAD_FLAGS: Final = -**********
NTE_BAD_TYPE: Final = -**********
NTE_BAD_KEY_STATE: Final = -**********
NTE_BAD_HASH_STATE: Final = -**********
NTE_NO_KEY: Final = -**********
NTE_NO_MEMORY: Final = -**********
NTE_EXISTS: Final = -**********
NTE_PERM: Final = -**********
NTE_NOT_FOUND: Final = -**********
NTE_DOUBLE_ENCRYPT: Final = -**********
NTE_BAD_PROVIDER: Final = -**********
NTE_BAD_PROV_TYPE: Final = -**********
NTE_BAD_PUBLIC_KEY: Final = -**********
NTE_BAD_KEYSET: Final = -**********
NTE_PROV_TYPE_NOT_DEF: Final = -**********
NTE_PROV_TYPE_ENTRY_BAD: Final = -**********
NTE_KEYSET_NOT_DEF: Final = -**********
NTE_KEYSET_ENTRY_BAD: Final = -**********
NTE_PROV_TYPE_NO_MATCH: Final = -**********
NTE_SIGNATURE_FILE_BAD: Final = -**********
NTE_PROVIDER_DLL_FAIL: Final = -**********
NTE_PROV_DLL_NOT_FOUND: Final = -**********
NTE_BAD_KEYSET_PARAM: Final = -**********
NTE_FAIL: Final = -**********
NTE_SYS_ERR: Final = -**********
NTE_SILENT_CONTEXT: Final = -**********
NTE_TOKEN_KEYSET_STORAGE_FULL: Final = -**********
NTE_TEMPORARY_PROFILE: Final = -**********
NTE_FIXEDPARAMETER: Final = -**********
NTE_INVALID_HANDLE: Final = -**********
NTE_INVALID_PARAMETER: Final = -**********
NTE_BUFFER_TOO_SMALL: Final = -**********
NTE_NOT_SUPPORTED: Final = -**********
NTE_NO_MORE_ITEMS: Final = -**********
NTE_BUFFERS_OVERLAP: Final = -**********
NTE_DECRYPTION_FAILURE: Final = -**********
NTE_INTERNAL_ERROR: Final = -**********
NTE_UI_REQUIRED: Final = -**********
NTE_HMAC_NOT_SUPPORTED: Final = -**********
NTE_DEVICE_NOT_READY: Final = -**********
NTE_AUTHENTICATION_IGNORED: Final = -**********
NTE_VALIDATION_FAILED: Final = -**********
NTE_INCORRECT_PASSWORD: Final = -**********
NTE_ENCRYPTION_FAILURE: Final = -**********
NTE_DEVICE_NOT_FOUND: Final = -2146893771
NTE_USER_CANCELLED: Final = -2146893770
NTE_PASSWORD_CHANGE_REQUIRED: Final = -2146893769
NTE_NOT_ACTIVE_CONSOLE: Final = -2146893768
SEC_E_INSUFFICIENT_MEMORY: Final = -2146893056
SEC_E_INVALID_HANDLE: Final = -2146893055
SEC_E_UNSUPPORTED_FUNCTION: Final = -2146893054
SEC_E_TARGET_UNKNOWN: Final = -2146893053
SEC_E_INTERNAL_ERROR: Final = -2146893052
SEC_E_SECPKG_NOT_FOUND: Final = -2146893051
SEC_E_NOT_OWNER: Final = -2146893050
SEC_E_CANNOT_INSTALL: Final = -2146893049
SEC_E_INVALID_TOKEN: Final = -2146893048
SEC_E_CANNOT_PACK: Final = -2146893047
SEC_E_QOP_NOT_SUPPORTED: Final = -2146893046
SEC_E_NO_IMPERSONATION: Final = -2146893045
SEC_E_LOGON_DENIED: Final = -2146893044
SEC_E_UNKNOWN_CREDENTIALS: Final = -2146893043
SEC_E_NO_CREDENTIALS: Final = -2146893042
SEC_E_MESSAGE_ALTERED: Final = -2146893041
SEC_E_OUT_OF_SEQUENCE: Final = -2146893040
SEC_E_NO_AUTHENTICATING_AUTHORITY: Final = -2146893039
SEC_I_CONTINUE_NEEDED: Final = 0x00090312
SEC_I_COMPLETE_NEEDED: Final = 0x00090313
SEC_I_COMPLETE_AND_CONTINUE: Final = 0x00090314
SEC_I_LOCAL_LOGON: Final = 0x00090315
SEC_I_GENERIC_EXTENSION_RECEIVED: Final = 0x00090316
SEC_E_BAD_PKGID: Final = -2146893034
SEC_E_CONTEXT_EXPIRED: Final = -2146893033
SEC_I_CONTEXT_EXPIRED: Final = 0x00090317
SEC_E_INCOMPLETE_MESSAGE: Final = -2146893032
SEC_E_INCOMPLETE_CREDENTIALS: Final = -2146893024
SEC_E_BUFFER_TOO_SMALL: Final = -2146893023
SEC_I_INCOMPLETE_CREDENTIALS: Final = 0x00090320
SEC_I_RENEGOTIATE: Final = 0x00090321
SEC_E_WRONG_PRINCIPAL: Final = -2146893022
SEC_I_NO_LSA_CONTEXT: Final = 0x00090323
SEC_E_TIME_SKEW: Final = -2146893020
SEC_E_UNTRUSTED_ROOT: Final = -2146893019
SEC_E_ILLEGAL_MESSAGE: Final = -2146893018
SEC_E_CERT_UNKNOWN: Final = -2146893017
SEC_E_CERT_EXPIRED: Final = -2146893016
SEC_E_ENCRYPT_FAILURE: Final = -2146893015
SEC_E_DECRYPT_FAILURE: Final = -2146893008
SEC_E_ALGORITHM_MISMATCH: Final = -2146893007
SEC_E_SECURITY_QOS_FAILED: Final = -2146893006
SEC_E_UNFINISHED_CONTEXT_DELETED: Final = -2146893005
SEC_E_NO_TGT_REPLY: Final = -2146893004
SEC_E_NO_IP_ADDRESSES: Final = -2146893003
SEC_E_WRONG_CREDENTIAL_HANDLE: Final = -2146893002
SEC_E_CRYPTO_SYSTEM_INVALID: Final = -2146893001
SEC_E_MAX_REFERRALS_EXCEEDED: Final = -2146893000
SEC_E_MUST_BE_KDC: Final = -2146892999
SEC_E_STRONG_CRYPTO_NOT_SUPPORTED: Final = -2146892998
SEC_E_TOO_MANY_PRINCIPALS: Final = -**********
SEC_E_NO_PA_DATA: Final = -**********
SEC_E_PKINIT_NAME_MISMATCH: Final = -**********
SEC_E_SMARTCARD_LOGON_REQUIRED: Final = -**********
SEC_E_SHUTDOWN_IN_PROGRESS: Final = -**********
SEC_E_KDC_INVALID_REQUEST: Final = -**********
SEC_E_KDC_UNABLE_TO_REFER: Final = -**********
SEC_E_KDC_UNKNOWN_ETYPE: Final = -**********
SEC_E_UNSUPPORTED_PREAUTH: Final = -**********
SEC_E_DELEGATION_REQUIRED: Final = -**********
SEC_E_BAD_BINDINGS: Final = -**********
SEC_E_MULTIPLE_ACCOUNTS: Final = -**********
SEC_E_NO_KERB_KEY: Final = -**********
SEC_E_CERT_WRONG_USAGE: Final = -**********
SEC_E_DOWNGRADE_DETECTED: Final = -**********
SEC_E_SMARTCARD_CERT_REVOKED: Final = -**********
SEC_E_ISSUING_CA_UNTRUSTED: Final = -**********
SEC_E_REVOCATION_OFFLINE_C: Final = -**********
SEC_E_PKINIT_CLIENT_FAILURE: Final = -**********
SEC_E_SMARTCARD_CERT_EXPIRED: Final = -**********
SEC_E_NO_S4U_PROT_SUPPORT: Final = -**********
SEC_E_CROSSREALM_DELEGATION_FAILURE: Final = -**********
SEC_E_REVOCATION_OFFLINE_KDC: Final = -**********
SEC_E_ISSUING_CA_UNTRUSTED_KDC: Final = -**********
SEC_E_KDC_CERT_EXPIRED: Final = -**********
SEC_E_KDC_CERT_REVOKED: Final = -**********
SEC_I_SIGNATURE_NEEDED: Final = 0x0009035C
SEC_E_INVALID_PARAMETER: Final = -**********
SEC_E_DELEGATION_POLICY: Final = -**********
SEC_E_POLICY_NLTM_ONLY: Final = -**********
SEC_I_NO_RENEGOTIATION: Final = 0x00090360
SEC_E_NO_CONTEXT: Final = -**********
SEC_E_PKU2U_CERT_FAILURE: Final = -*********8
SEC_E_MUTUAL_AUTH_FAILED: Final = -*********7
SEC_I_MESSAGE_FRAGMENT: Final = 0x00090364
SEC_E_ONLY_HTTPS_ALLOWED: Final = -*********5
SEC_I_CONTINUE_NEEDED_MESSAGE_OK: Final = 0x00090366
SEC_E_APPLICATION_PROTOCOL_MISMATCH: Final = -*********3
SEC_I_ASYNC_CALL_PENDING: Final = 0x00090368
SEC_E_INVALID_UPN_NAME: Final = -*********1
SEC_E_EXT_BUFFER_TOO_SMALL: Final = -*********0
SEC_E_INSUFFICIENT_BUFFERS: Final = -2146892949
SEC_E_NO_SPM: Final = SEC_E_INTERNAL_ERROR
SEC_E_NOT_SUPPORTED: Final = SEC_E_UNSUPPORTED_FUNCTION
CRYPT_E_MSG_ERROR: Final = -2146889727
CRYPT_E_UNKNOWN_ALGO: Final = -2146889726
CRYPT_E_OID_FORMAT: Final = -2146889725
CRYPT_E_INVALID_MSG_TYPE: Final = -2146889724
CRYPT_E_UNEXPECTED_ENCODING: Final = -2146889723
CRYPT_E_AUTH_ATTR_MISSING: Final = -2146889722
CRYPT_E_HASH_VALUE: Final = -2146889721
CRYPT_E_INVALID_INDEX: Final = -2146889720
CRYPT_E_ALREADY_DECRYPTED: Final = -2146889719
CRYPT_E_NOT_DECRYPTED: Final = -2146889718
CRYPT_E_RECIPIENT_NOT_FOUND: Final = -2146889717
CRYPT_E_CONTROL_TYPE: Final = -2146889716
CRYPT_E_ISSUER_SERIALNUMBER: Final = -**********
CRYPT_E_SIGNER_NOT_FOUND: Final = -**********
CRYPT_E_ATTRIBUTES_MISSING: Final = -**********
CRYPT_E_STREAM_MSG_NOT_READY: Final = -**********
CRYPT_E_STREAM_INSUFFICIENT_DATA: Final = -**********
CRYPT_I_NEW_PROTECTION_REQUIRED: Final = 0x00091012
CRYPT_E_BAD_LEN: Final = -**********
CRYPT_E_BAD_ENCODE: Final = -**********
CRYPT_E_FILE_ERROR: Final = -**********
CRYPT_E_NOT_FOUND: Final = -**********
CRYPT_E_EXISTS: Final = -**********
CRYPT_E_NO_PROVIDER: Final = -**********
CRYPT_E_SELF_SIGNED: Final = -**********
CRYPT_E_DELETED_PREV: Final = -**********
CRYPT_E_NO_MATCH: Final = -**********
CRYPT_E_UNEXPECTED_MSG_TYPE: Final = -**********
CRYPT_E_NO_KEY_PROPERTY: Final = -**********
CRYPT_E_NO_DECRYPT_CERT: Final = -**********
CRYPT_E_BAD_MSG: Final = -**********
CRYPT_E_NO_SIGNER: Final = -**********
CRYPT_E_PENDING_CLOSE: Final = -**********
CRYPT_E_REVOKED: Final = -**********
CRYPT_E_NO_REVOCATION_DLL: Final = -**********
CRYPT_E_NO_REVOCATION_CHECK: Final = -**********
CRYPT_E_REVOCATION_OFFLINE: Final = -**********
CRYPT_E_NOT_IN_REVOCATION_DATABASE: Final = -**********
CRYPT_E_INVALID_NUMERIC_STRING: Final = -**********
CRYPT_E_INVALID_PRINTABLE_STRING: Final = -**********
CRYPT_E_INVALID_IA5_STRING: Final = -**********
CRYPT_E_INVALID_X500_STRING: Final = -**********
CRYPT_E_NOT_CHAR_STRING: Final = -**********
CRYPT_E_FILERESIZED: Final = -**********
CRYPT_E_SECURITY_SETTINGS: Final = -**********
CRYPT_E_NO_VERIFY_USAGE_DLL: Final = -2146885593
CRYPT_E_NO_VERIFY_USAGE_CHECK: Final = -2146885592
CRYPT_E_VERIFY_USAGE_OFFLINE: Final = -2146885591
CRYPT_E_NOT_IN_CTL: Final = -2146885590
CRYPT_E_NO_TRUSTED_SIGNER: Final = -2146885589
CRYPT_E_MISSING_PUBKEY_PARA: Final = -2146885588
CRYPT_E_OBJECT_LOCATOR_OBJECT_NOT_FOUND: Final = -2146885587
CRYPT_E_OSS_ERROR: Final = -2146881536
OSS_MORE_BUF: Final = -2146881535
OSS_NEGATIVE_UINTEGER: Final = -2146881534
OSS_PDU_RANGE: Final = -2146881533
OSS_MORE_INPUT: Final = -2146881532
OSS_DATA_ERROR: Final = -2146881531
OSS_BAD_ARG: Final = -2146881530
OSS_BAD_VERSION: Final = -2146881529
OSS_OUT_MEMORY: Final = -2146881528
OSS_PDU_MISMATCH: Final = -2146881527
OSS_LIMITED: Final = -2146881526
OSS_BAD_PTR: Final = -2146881525
OSS_BAD_TIME: Final = -2146881524
OSS_INDEFINITE_NOT_SUPPORTED: Final = -2146881523
OSS_MEM_ERROR: Final = -2146881522
OSS_BAD_TABLE: Final = -2146881521
OSS_TOO_LONG: Final = -2146881520
OSS_CONSTRAINT_VIOLATED: Final = -2146881519
OSS_FATAL_ERROR: Final = -2146881518
OSS_ACCESS_SERIALIZATION_ERROR: Final = -2146881517
OSS_NULL_TBL: Final = -2146881516
OSS_NULL_FCN: Final = -2146881515
OSS_BAD_ENCRULES: Final = -2146881514
OSS_UNAVAIL_ENCRULES: Final = -2146881513
OSS_CANT_OPEN_TRACE_WINDOW: Final = -2146881512
OSS_UNIMPLEMENTED: Final = -2146881511
OSS_OID_DLL_NOT_LINKED: Final = -2146881510
OSS_CANT_OPEN_TRACE_FILE: Final = -2146881509
OSS_TRACE_FILE_ALREADY_OPEN: Final = -2146881508
OSS_TABLE_MISMATCH: Final = -2146881507
OSS_TYPE_NOT_SUPPORTED: Final = -2146881506
OSS_REAL_DLL_NOT_LINKED: Final = -2146881505
OSS_REAL_CODE_NOT_LINKED: Final = -2146881504
OSS_OUT_OF_RANGE: Final = -2146881503
OSS_COPIER_DLL_NOT_LINKED: Final = -2146881502
OSS_CONSTRAINT_DLL_NOT_LINKED: Final = -2146881501
OSS_COMPARATOR_DLL_NOT_LINKED: Final = -2146881500
OSS_COMPARATOR_CODE_NOT_LINKED: Final = -2146881499
OSS_MEM_MGR_DLL_NOT_LINKED: Final = -2146881498
OSS_PDV_DLL_NOT_LINKED: Final = -2146881497
OSS_PDV_CODE_NOT_LINKED: Final = -2146881496
OSS_API_DLL_NOT_LINKED: Final = -2146881495
OSS_BERDER_DLL_NOT_LINKED: Final = -2146881494
OSS_PER_DLL_NOT_LINKED: Final = -2146881493
OSS_OPEN_TYPE_ERROR: Final = -2146881492
OSS_MUTEX_NOT_CREATED: Final = -2146881491
OSS_CANT_CLOSE_TRACE_FILE: Final = -2146881490
CRYPT_E_ASN1_ERROR: Final = -2146881280
CRYPT_E_ASN1_INTERNAL: Final = -2146881279
CRYPT_E_ASN1_EOD: Final = -2146881278
CRYPT_E_ASN1_CORRUPT: Final = -2146881277
CRYPT_E_ASN1_LARGE: Final = -2146881276
CRYPT_E_ASN1_CONSTRAINT: Final = -2146881275
CRYPT_E_ASN1_MEMORY: Final = -2146881274
CRYPT_E_ASN1_OVERFLOW: Final = -2146881273
CRYPT_E_ASN1_BADPDU: Final = -2146881272
CRYPT_E_ASN1_BADARGS: Final = -2146881271
CRYPT_E_ASN1_BADREAL: Final = -2146881270
CRYPT_E_ASN1_BADTAG: Final = -2146881269
CRYPT_E_ASN1_CHOICE: Final = -2146881268
CRYPT_E_ASN1_RULE: Final = -2146881267
CRYPT_E_ASN1_UTF8: Final = -2146881266
CRYPT_E_ASN1_PDU_TYPE: Final = -2146881229
CRYPT_E_ASN1_NYI: Final = -2146881228
CRYPT_E_ASN1_EXTENDED: Final = -2146881023
CRYPT_E_ASN1_NOEOD: Final = -2146881022
CERTSRV_E_BAD_REQUESTSUBJECT: Final = -2146877439
CERTSRV_E_NO_REQUEST: Final = -2146877438
CERTSRV_E_BAD_REQUESTSTATUS: Final = -2146877437
CERTSRV_E_PROPERTY_EMPTY: Final = -2146877436
CERTSRV_E_INVALID_CA_CERTIFICATE: Final = -2146877435
CERTSRV_E_SERVER_SUSPENDED: Final = -2146877434
CERTSRV_E_ENCODING_LENGTH: Final = -2146877433
CERTSRV_E_ROLECONFLICT: Final = -2146877432
CERTSRV_E_RESTRICTEDOFFICER: Final = -2146877431
CERTSRV_E_KEY_ARCHIVAL_NOT_CONFIGURED: Final = -2146877430
CERTSRV_E_NO_VALID_KRA: Final = -2146877429
CERTSRV_E_BAD_REQUEST_KEY_ARCHIVAL: Final = -2146877428
CERTSRV_E_NO_CAADMIN_DEFINED: Final = -2146877427
CERTSRV_E_BAD_RENEWAL_CERT_ATTRIBUTE: Final = -2146877426
CERTSRV_E_NO_DB_SESSIONS: Final = -2146877425
CERTSRV_E_ALIGNMENT_FAULT: Final = -2146877424
CERTSRV_E_ENROLL_DENIED: Final = -2146877423
CERTSRV_E_TEMPLATE_DENIED: Final = -2146877422
CERTSRV_E_DOWNLEVEL_DC_SSL_OR_UPGRADE: Final = -2146877421
CERTSRV_E_ADMIN_DENIED_REQUEST: Final = -2146877420
CERTSRV_E_NO_POLICY_SERVER: Final = -2146877419
CERTSRV_E_WEAK_SIGNATURE_OR_KEY: Final = -2146877418
CERTSRV_E_KEY_ATTESTATION_NOT_SUPPORTED: Final = -2146877417
CERTSRV_E_ENCRYPTION_CERT_REQUIRED: Final = -2146877416
CERTSRV_E_UNSUPPORTED_CERT_TYPE: Final = -2146875392
CERTSRV_E_NO_CERT_TYPE: Final = -2146875391
CERTSRV_E_TEMPLATE_CONFLICT: Final = -2146875390
CERTSRV_E_SUBJECT_ALT_NAME_REQUIRED: Final = -2146875389
CERTSRV_E_ARCHIVED_KEY_REQUIRED: Final = -2146875388
CERTSRV_E_SMIME_REQUIRED: Final = -2146875387
CERTSRV_E_BAD_RENEWAL_SUBJECT: Final = -2146875386
CERTSRV_E_BAD_TEMPLATE_VERSION: Final = -2146875385
CERTSRV_E_TEMPLATE_POLICY_REQUIRED: Final = -2146875384
CERTSRV_E_SIGNATURE_POLICY_REQUIRED: Final = -2146875383
CERTSRV_E_SIGNATURE_COUNT: Final = -2146875382
CERTSRV_E_SIGNATURE_REJECTED: Final = -2146875381
CERTSRV_E_ISSUANCE_POLICY_REQUIRED: Final = -2146875380
CERTSRV_E_SUBJECT_UPN_REQUIRED: Final = -2146875379
CERTSRV_E_SUBJECT_DIRECTORY_GUID_REQUIRED: Final = -2146875378
CERTSRV_E_SUBJECT_DNS_REQUIRED: Final = -2146875377
CERTSRV_E_ARCHIVED_KEY_UNEXPECTED: Final = -2146875376
CERTSRV_E_KEY_LENGTH: Final = -2146875375
CERTSRV_E_SUBJECT_EMAIL_REQUIRED: Final = -2146875374
CERTSRV_E_UNKNOWN_CERT_TYPE: Final = -2146875373
CERTSRV_E_CERT_TYPE_OVERLAP: Final = -2146875372
CERTSRV_E_TOO_MANY_SIGNATURES: Final = -2146875371
CERTSRV_E_RENEWAL_BAD_PUBLIC_KEY: Final = -2146875370
CERTSRV_E_INVALID_EK: Final = -2146875369
CERTSRV_E_INVALID_IDBINDING: Final = -2146875368
CERTSRV_E_INVALID_ATTESTATION: Final = -2146875367
CERTSRV_E_KEY_ATTESTATION: Final = -2146875366
CERTSRV_E_CORRUPT_KEY_ATTESTATION: Final = -2146875365
CERTSRV_E_EXPIRED_CHALLENGE: Final = -2146875364
CERTSRV_E_INVALID_RESPONSE: Final = -2146875363
CERTSRV_E_INVALID_REQUESTID: Final = -2146875362
CERTSRV_E_REQUEST_PRECERTIFICATE_MISMATCH: Final = -2146875361
CERTSRV_E_PENDING_CLIENT_RESPONSE: Final = -2146875360
CERTSRV_E_SEC_EXT_DIRECTORY_SID_REQUIRED: Final = -2146875359
XENROLL_E_KEY_NOT_EXPORTABLE: Final = -2146873344
XENROLL_E_CANNOT_ADD_ROOT_CERT: Final = -2146873343
XENROLL_E_RESPONSE_KA_HASH_NOT_FOUND: Final = -2146873342
XENROLL_E_RESPONSE_UNEXPECTED_KA_HASH: Final = -2146873341
XENROLL_E_RESPONSE_KA_HASH_MISMATCH: Final = -2146873340
XENROLL_E_KEYSPEC_SMIME_MISMATCH: Final = -2146873339
TRUST_E_SYSTEM_ERROR: Final = -2146869247
TRUST_E_NO_SIGNER_CERT: Final = -2146869246
TRUST_E_COUNTER_SIGNER: Final = -2146869245
TRUST_E_CERT_SIGNATURE: Final = -2146869244
TRUST_E_TIME_STAMP: Final = -2146869243
TRUST_E_BAD_DIGEST: Final = -2146869232
TRUST_E_MALFORMED_SIGNATURE: Final = -2146869231
TRUST_E_BASIC_CONSTRAINTS: Final = -2146869223
TRUST_E_FINANCIAL_CRITERIA: Final = -2146869218
MSSIPOTF_E_OUTOFMEMRANGE: Final = -2146865151
MSSIPOTF_E_CANTGETOBJECT: Final = -2146865150
MSSIPOTF_E_NOHEADTABLE: Final = -2146865149
MSSIPOTF_E_BAD_MAGICNUMBER: Final = -2146865148
MSSIPOTF_E_BAD_OFFSET_TABLE: Final = -2146865147
MSSIPOTF_E_TABLE_TAGORDER: Final = -2146865146
MSSIPOTF_E_TABLE_LONGWORD: Final = -2146865145
MSSIPOTF_E_BAD_FIRST_TABLE_PLACEMENT: Final = -2146865144
MSSIPOTF_E_TABLES_OVERLAP: Final = -2146865143
MSSIPOTF_E_TABLE_PADBYTES: Final = -2146865142
MSSIPOTF_E_FILETOOSMALL: Final = -2146865141
MSSIPOTF_E_TABLE_CHECKSUM: Final = -2146865140
MSSIPOTF_E_FILE_CHECKSUM: Final = -**********
MSSIPOTF_E_FAILED_POLICY: Final = -**********
MSSIPOTF_E_FAILED_HINTS_CHECK: Final = -**********
MSSIPOTF_E_NOT_OPENTYPE: Final = -**********
MSSIPOTF_E_FILE: Final = -**********
MSSIPOTF_E_CRYPT: Final = -**********
MSSIPOTF_E_BADVERSION: Final = -**********
MSSIPOTF_E_DSIG_STRUCTURE: Final = -**********
MSSIPOTF_E_PCONST_CHECK: Final = -**********
MSSIPOTF_E_STRUCTURE: Final = -**********
ERROR_CRED_REQUIRES_CONFIRMATION: Final = -**********
NTE_OP_OK: Final = 0
TRUST_E_PROVIDER_UNKNOWN: Final = -**********
TRUST_E_ACTION_UNKNOWN: Final = -**********
TRUST_E_SUBJECT_FORM_UNKNOWN: Final = -**********
TRUST_E_SUBJECT_NOT_TRUSTED: Final = -**********
DIGSIG_E_ENCODE: Final = -**********
DIGSIG_E_DECODE: Final = -**********
DIGSIG_E_EXTENSIBILITY: Final = -**********
DIGSIG_E_CRYPTO: Final = -**********
PERSIST_E_SIZEDEFINITE: Final = -**********
PERSIST_E_SIZEINDEFINITE: Final = -**********
PERSIST_E_NOTSELFSIZING: Final = -**********
TRUST_E_NOSIGNATURE: Final = -**********
CERT_E_EXPIRED: Final = -**********
CERT_E_VALIDITYPERIODNESTING: Final = -**********
CERT_E_ROLE: Final = -**********
CERT_E_PATHLENCONST: Final = -**********
CERT_E_CRITICAL: Final = -**********
CERT_E_PURPOSE: Final = -**********
CERT_E_ISSUERCHAINING: Final = -**********
CERT_E_MALFORMED: Final = -**********
CERT_E_UNTRUSTEDROOT: Final = -**********
CERT_E_CHAINING: Final = -**********
TRUST_E_FAIL: Final = -**********
CERT_E_REVOKED: Final = -**********
CERT_E_UNTRUSTEDTESTROOT: Final = -2146762483
CERT_E_REVOCATION_FAILURE: Final = -2146762482
CERT_E_CN_NO_MATCH: Final = -2146762481
CERT_E_WRONG_USAGE: Final = -2146762480
TRUST_E_EXPLICIT_DISTRUST: Final = -2146762479
CERT_E_UNTRUSTEDCA: Final = -2146762478
CERT_E_INVALID_POLICY: Final = -2146762477
CERT_E_INVALID_NAME: Final = -2146762476

def HRESULT_FROM_SETUPAPI(x): ...

SPAPI_E_EXPECTED_SECTION_NAME: Final = -2146500608
SPAPI_E_BAD_SECTION_NAME_LINE: Final = -2146500607
SPAPI_E_SECTION_NAME_TOO_LONG: Final = -2146500606
SPAPI_E_GENERAL_SYNTAX: Final = -2146500605
SPAPI_E_WRONG_INF_STYLE: Final = -2146500352
SPAPI_E_SECTION_NOT_FOUND: Final = -2146500351
SPAPI_E_LINE_NOT_FOUND: Final = -2146500350
SPAPI_E_NO_BACKUP: Final = -2146500349
SPAPI_E_NO_ASSOCIATED_CLASS: Final = -2146500096
SPAPI_E_CLASS_MISMATCH: Final = -2146500095
SPAPI_E_DUPLICATE_FOUND: Final = -2146500094
SPAPI_E_NO_DRIVER_SELECTED: Final = -2146500093
SPAPI_E_KEY_DOES_NOT_EXIST: Final = -2146500092
SPAPI_E_INVALID_DEVINST_NAME: Final = -2146500091
SPAPI_E_INVALID_CLASS: Final = -2146500090
SPAPI_E_DEVINST_ALREADY_EXISTS: Final = -2146500089
SPAPI_E_DEVINFO_NOT_REGISTERED: Final = -2146500088
SPAPI_E_INVALID_REG_PROPERTY: Final = -2146500087
SPAPI_E_NO_INF: Final = -2146500086
SPAPI_E_NO_SUCH_DEVINST: Final = -2146500085
SPAPI_E_CANT_LOAD_CLASS_ICON: Final = -2146500084
SPAPI_E_INVALID_CLASS_INSTALLER: Final = -2146500083
SPAPI_E_DI_DO_DEFAULT: Final = -2146500082
SPAPI_E_DI_NOFILECOPY: Final = -2146500081
SPAPI_E_INVALID_HWPROFILE: Final = -2146500080
SPAPI_E_NO_DEVICE_SELECTED: Final = -2146500079
SPAPI_E_DEVINFO_LIST_LOCKED: Final = -2146500078
SPAPI_E_DEVINFO_DATA_LOCKED: Final = -2146500077
SPAPI_E_DI_BAD_PATH: Final = -2146500076
SPAPI_E_NO_CLASSINSTALL_PARAMS: Final = -2146500075
SPAPI_E_FILEQUEUE_LOCKED: Final = -2146500074
SPAPI_E_BAD_SERVICE_INSTALLSECT: Final = -2146500073
SPAPI_E_NO_CLASS_DRIVER_LIST: Final = -2146500072
SPAPI_E_NO_ASSOCIATED_SERVICE: Final = -2146500071
SPAPI_E_NO_DEFAULT_DEVICE_INTERFACE: Final = -**********
SPAPI_E_DEVICE_INTERFACE_ACTIVE: Final = -**********
SPAPI_E_DEVICE_INTERFACE_REMOVED: Final = -**********
SPAPI_E_BAD_INTERFACE_INSTALLSECT: Final = -**********
SPAPI_E_NO_SUCH_INTERFACE_CLASS: Final = -**********
SPAPI_E_INVALID_REFERENCE_STRING: Final = -**********
SPAPI_E_INVALID_MACHINENAME: Final = -**********
SPAPI_E_REMOTE_COMM_FAILURE: Final = -**********
SPAPI_E_MACHINE_UNAVAILABLE: Final = -**********
SPAPI_E_NO_CONFIGMGR_SERVICES: Final = -**********
SPAPI_E_INVALID_PROPPAGE_PROVIDER: Final = -**********
SPAPI_E_NO_SUCH_DEVICE_INTERFACE: Final = -**********
SPAPI_E_DI_POSTPROCESSING_REQUIRED: Final = -**********
SPAPI_E_INVALID_COINSTALLER: Final = -**********
SPAPI_E_NO_COMPAT_DRIVERS: Final = -**********
SPAPI_E_NO_DEVICE_ICON: Final = -**********
SPAPI_E_INVALID_INF_LOGCONFIG: Final = -**********
SPAPI_E_DI_DONT_INSTALL: Final = -**********
SPAPI_E_INVALID_FILTER_DRIVER: Final = -**********
SPAPI_E_NON_WINDOWS_NT_DRIVER: Final = -**********
SPAPI_E_NON_WINDOWS_DRIVER: Final = -**********
SPAPI_E_NO_CATALOG_FOR_OEM_INF: Final = -**********
SPAPI_E_DEVINSTALL_QUEUE_NONNATIVE: Final = -**********
SPAPI_E_NOT_DISABLEABLE: Final = -**********
SPAPI_E_CANT_REMOVE_DEVINST: Final = -**********
SPAPI_E_INVALID_TARGET: Final = -**********
SPAPI_E_DRIVER_NONNATIVE: Final = -**********
SPAPI_E_IN_WOW64: Final = -**********
SPAPI_E_SET_SYSTEM_RESTORE_POINT: Final = -**********
SPAPI_E_INCORRECTLY_COPIED_INF: Final = -**********
SPAPI_E_SCE_DISABLED: Final = -2146500040
SPAPI_E_UNKNOWN_EXCEPTION: Final = -2146500039
SPAPI_E_PNP_REGISTRY_ERROR: Final = -2146500038
SPAPI_E_REMOTE_REQUEST_UNSUPPORTED: Final = -2146500037
SPAPI_E_NOT_AN_INSTALLED_OEM_INF: Final = -2146500036
SPAPI_E_INF_IN_USE_BY_DEVICES: Final = -2146500035
SPAPI_E_DI_FUNCTION_OBSOLETE: Final = -2146500034
SPAPI_E_NO_AUTHENTICODE_CATALOG: Final = -2146500033
SPAPI_E_AUTHENTICODE_DISALLOWED: Final = -2146500032
SPAPI_E_AUTHENTICODE_TRUSTED_PUBLISHER: Final = -2146500031
SPAPI_E_AUTHENTICODE_TRUST_NOT_ESTABLISHED: Final = -2146500030
SPAPI_E_AUTHENTICODE_PUBLISHER_NOT_TRUSTED: Final = -2146500029
SPAPI_E_SIGNATURE_OSATTRIBUTE_MISMATCH: Final = -2146500028
SPAPI_E_ONLY_VALIDATE_VIA_AUTHENTICODE: Final = -2146500027
SPAPI_E_DEVICE_INSTALLER_NOT_READY: Final = -2146500026
SPAPI_E_DRIVER_STORE_ADD_FAILED: Final = -2146500025
SPAPI_E_DEVICE_INSTALL_BLOCKED: Final = -2146500024
SPAPI_E_DRIVER_INSTALL_BLOCKED: Final = -2146500023
SPAPI_E_WRONG_INF_TYPE: Final = -2146500022
SPAPI_E_FILE_HASH_NOT_IN_CATALOG: Final = -2146500021
SPAPI_E_DRIVER_STORE_DELETE_FAILED: Final = -2146500020
SPAPI_E_UNRECOVERABLE_STACK_OVERFLOW: Final = -2146499840
SPAPI_E_ERROR_NOT_INSTALLED: Final = -2146496512
SCARD_S_SUCCESS: Final = NO_ERROR
SCARD_F_INTERNAL_ERROR: Final = -2146435071
SCARD_E_CANCELLED: Final = -2146435070
SCARD_E_INVALID_HANDLE: Final = -2146435069
SCARD_E_INVALID_PARAMETER: Final = -2146435068
SCARD_E_INVALID_TARGET: Final = -2146435067
SCARD_E_NO_MEMORY: Final = -2146435066
SCARD_F_WAITED_TOO_LONG: Final = -2146435065
SCARD_E_INSUFFICIENT_BUFFER: Final = -2146435064
SCARD_E_UNKNOWN_READER: Final = -2146435063
SCARD_E_TIMEOUT: Final = -2146435062
SCARD_E_SHARING_VIOLATION: Final = -2146435061
SCARD_E_NO_SMARTCARD: Final = -2146435060
SCARD_E_UNKNOWN_CARD: Final = -2146435059
SCARD_E_CANT_DISPOSE: Final = -2146435058
SCARD_E_PROTO_MISMATCH: Final = -2146435057
SCARD_E_NOT_READY: Final = -2146435056
SCARD_E_INVALID_VALUE: Final = -2146435055
SCARD_E_SYSTEM_CANCELLED: Final = -2146435054
SCARD_F_COMM_ERROR: Final = -2146435053
SCARD_F_UNKNOWN_ERROR: Final = -2146435052
SCARD_E_INVALID_ATR: Final = -2146435051
SCARD_E_NOT_TRANSACTED: Final = -2146435050
SCARD_E_READER_UNAVAILABLE: Final = -2146435049
SCARD_P_SHUTDOWN: Final = -2146435048
SCARD_E_PCI_TOO_SMALL: Final = -2146435047
SCARD_E_READER_UNSUPPORTED: Final = -2146435046
SCARD_E_DUPLICATE_READER: Final = -2146435045
SCARD_E_CARD_UNSUPPORTED: Final = -2146435044
SCARD_E_NO_SERVICE: Final = -2146435043
SCARD_E_SERVICE_STOPPED: Final = -2146435042
SCARD_E_UNEXPECTED: Final = -2146435041
SCARD_E_ICC_INSTALLATION: Final = -2146435040
SCARD_E_ICC_CREATEORDER: Final = -2146435039
SCARD_E_UNSUPPORTED_FEATURE: Final = -2146435038
SCARD_E_DIR_NOT_FOUND: Final = -2146435037
SCARD_E_FILE_NOT_FOUND: Final = -2146435036
SCARD_E_NO_DIR: Final = -2146435035
SCARD_E_NO_FILE: Final = -2146435034
SCARD_E_NO_ACCESS: Final = -2146435033
SCARD_E_WRITE_TOO_MANY: Final = -2146435032
SCARD_E_BAD_SEEK: Final = -2146435031
SCARD_E_INVALID_CHV: Final = -2146435030
SCARD_E_UNKNOWN_RES_MNG: Final = -2146435029
SCARD_E_NO_SUCH_CERTIFICATE: Final = -2146435028
SCARD_E_CERTIFICATE_UNAVAILABLE: Final = -2146435027
SCARD_E_NO_READERS_AVAILABLE: Final = -2146435026
SCARD_E_COMM_DATA_LOST: Final = -2146435025
SCARD_E_NO_KEY_CONTAINER: Final = -2146435024
SCARD_E_SERVER_TOO_BUSY: Final = -2146435023
SCARD_E_PIN_CACHE_EXPIRED: Final = -2146435022
SCARD_E_NO_PIN_CACHE: Final = -2146435021
SCARD_E_READ_ONLY_CARD: Final = -2146435020
SCARD_W_UNSUPPORTED_CARD: Final = -2146434971
SCARD_W_UNRESPONSIVE_CARD: Final = -2146434970
SCARD_W_UNPOWERED_CARD: Final = -2146434969
SCARD_W_RESET_CARD: Final = -2146434968
SCARD_W_REMOVED_CARD: Final = -2146434967
SCARD_W_SECURITY_VIOLATION: Final = -2146434966
SCARD_W_WRONG_CHV: Final = -2146434965
SCARD_W_CHV_BLOCKED: Final = -2146434964
SCARD_W_EOF: Final = -2146434963
SCARD_W_CANCELLED_BY_USER: Final = -2146434962
SCARD_W_CARD_NOT_AUTHENTICATED: Final = -2146434961
SCARD_W_CACHE_ITEM_NOT_FOUND: Final = -2146434960
SCARD_W_CACHE_ITEM_STALE: Final = -2146434959
SCARD_W_CACHE_ITEM_TOO_BIG: Final = -2146434958
COMADMIN_E_OBJECTERRORS: Final = -2146368511
COMADMIN_E_OBJECTINVALID: Final = -2146368510
COMADMIN_E_KEYMISSING: Final = -2146368509
COMADMIN_E_ALREADYINSTALLED: Final = -2146368508
COMADMIN_E_APP_FILE_WRITEFAIL: Final = -2146368505
COMADMIN_E_APP_FILE_READFAIL: Final = -2146368504
COMADMIN_E_APP_FILE_VERSION: Final = -2146368503
COMADMIN_E_BADPATH: Final = -2146368502
COMADMIN_E_APPLICATIONEXISTS: Final = -2146368501
COMADMIN_E_ROLEEXISTS: Final = -2146368500
COMADMIN_E_CANTCOPYFILE: Final = -2146368499
COMADMIN_E_NOUSER: Final = -2146368497
COMADMIN_E_INVALIDUSERIDS: Final = -2146368496
COMADMIN_E_NOREGISTRYCLSID: Final = -2146368495
COMADMIN_E_BADREGISTRYPROGID: Final = -2146368494
COMADMIN_E_AUTHENTICATIONLEVEL: Final = -2146368493
COMADMIN_E_USERPASSWDNOTVALID: Final = -2146368492
COMADMIN_E_CLSIDORIIDMISMATCH: Final = -2146368488
COMADMIN_E_REMOTEINTERFACE: Final = -2146368487
COMADMIN_E_DLLREGISTERSERVER: Final = -2146368486
COMADMIN_E_NOSERVERSHARE: Final = -2146368485
COMADMIN_E_DLLLOADFAILED: Final = -2146368483
COMADMIN_E_BADREGISTRYLIBID: Final = -2146368482
COMADMIN_E_APPDIRNOTFOUND: Final = -2146368481
COMADMIN_E_REGISTRARFAILED: Final = -2146368477
COMADMIN_E_COMPFILE_DOESNOTEXIST: Final = -2146368476
COMADMIN_E_COMPFILE_LOADDLLFAIL: Final = -2146368475
COMADMIN_E_COMPFILE_GETCLASSOBJ: Final = -2146368474
COMADMIN_E_COMPFILE_CLASSNOTAVAIL: Final = -2146368473
COMADMIN_E_COMPFILE_BADTLB: Final = -2146368472
COMADMIN_E_COMPFILE_NOTINSTALLABLE: Final = -2146368471
COMADMIN_E_NOTCHANGEABLE: Final = -2146368470
COMADMIN_E_NOTDELETEABLE: Final = -2146368469
COMADMIN_E_SESSION: Final = -2146368468
COMADMIN_E_COMP_MOVE_LOCKED: Final = -2146368467
COMADMIN_E_COMP_MOVE_BAD_DEST: Final = -2146368466
COMADMIN_E_REGISTERTLB: Final = -2146368464
COMADMIN_E_SYSTEMAPP: Final = -2146368461
COMADMIN_E_COMPFILE_NOREGISTRAR: Final = -2146368460
COMADMIN_E_COREQCOMPINSTALLED: Final = -2146368459
COMADMIN_E_SERVICENOTINSTALLED: Final = -2146368458
COMADMIN_E_PROPERTYSAVEFAILED: Final = -2146368457
COMADMIN_E_OBJECTEXISTS: Final = -2146368456
COMADMIN_E_COMPONENTEXISTS: Final = -2146368455
COMADMIN_E_REGFILE_CORRUPT: Final = -2146368453
COMADMIN_E_PROPERTY_OVERFLOW: Final = -2146368452
COMADMIN_E_NOTINREGISTRY: Final = -2146368450
COMADMIN_E_OBJECTNOTPOOLABLE: Final = -2146368449
COMADMIN_E_APPLID_MATCHES_CLSID: Final = -2146368442
COMADMIN_E_ROLE_DOES_NOT_EXIST: Final = -2146368441
COMADMIN_E_START_APP_NEEDS_COMPONENTS: Final = -2146368440
COMADMIN_E_REQUIRES_DIFFERENT_PLATFORM: Final = -2146368439
COMADMIN_E_CAN_NOT_EXPORT_APP_PROXY: Final = -2146368438
COMADMIN_E_CAN_NOT_START_APP: Final = -2146368437
COMADMIN_E_CAN_NOT_EXPORT_SYS_APP: Final = -2146368436
COMADMIN_E_CANT_SUBSCRIBE_TO_COMPONENT: Final = -2146368435
COMADMIN_E_EVENTCLASS_CANT_BE_SUBSCRIBER: Final = -2146368434
COMADMIN_E_LIB_APP_PROXY_INCOMPATIBLE: Final = -2146368433
COMADMIN_E_BASE_PARTITION_ONLY: Final = -2146368432
COMADMIN_E_START_APP_DISABLED: Final = -2146368431
COMADMIN_E_CAT_DUPLICATE_PARTITION_NAME: Final = -2146368425
COMADMIN_E_CAT_INVALID_PARTITION_NAME: Final = -2146368424
COMADMIN_E_CAT_PARTITION_IN_USE: Final = -2146368423
COMADMIN_E_FILE_PARTITION_DUPLICATE_FILES: Final = -2146368422
COMADMIN_E_CAT_IMPORTED_COMPONENTS_NOT_ALLOWED: Final = -2146368421
COMADMIN_E_AMBIGUOUS_APPLICATION_NAME: Final = -2146368420
COMADMIN_E_AMBIGUOUS_PARTITION_NAME: Final = -2146368419
COMADMIN_E_REGDB_NOTINITIALIZED: Final = -2146368398
COMADMIN_E_REGDB_NOTOPEN: Final = -2146368397
COMADMIN_E_REGDB_SYSTEMERR: Final = -2146368396
COMADMIN_E_REGDB_ALREADYRUNNING: Final = -2146368395
COMADMIN_E_MIG_VERSIONNOTSUPPORTED: Final = -2146368384
COMADMIN_E_MIG_SCHEMANOTFOUND: Final = -2146368383
COMADMIN_E_CAT_BITNESSMISMATCH: Final = -2146368382
COMADMIN_E_CAT_UNACCEPTABLEBITNESS: Final = -2146368381
COMADMIN_E_CAT_WRONGAPPBITNESS: Final = -2146368380
COMADMIN_E_CAT_PAUSE_RESUME_NOT_SUPPORTED: Final = -2146368379
COMADMIN_E_CAT_SERVERFAULT: Final = -2146368378
COMQC_E_APPLICATION_NOT_QUEUED: Final = -2146368000
COMQC_E_NO_QUEUEABLE_INTERFACES: Final = -2146367999
COMQC_E_QUEUING_SERVICE_NOT_AVAILABLE: Final = -2146367998
COMQC_E_NO_IPERSISTSTREAM: Final = -2146367997
COMQC_E_BAD_MESSAGE: Final = -2146367996
COMQC_E_UNAUTHENTICATED: Final = -2146367995
COMQC_E_UNTRUSTED_ENQUEUER: Final = -2146367994
MSDTC_E_DUPLICATE_RESOURCE: Final = -2146367743
COMADMIN_E_OBJECT_PARENT_MISSING: Final = -2146367480
COMADMIN_E_OBJECT_DOES_NOT_EXIST: Final = -2146367479
COMADMIN_E_APP_NOT_RUNNING: Final = -2146367478
COMADMIN_E_INVALID_PARTITION: Final = -2146367477
COMADMIN_E_SVCAPP_NOT_POOLABLE_OR_RECYCLABLE: Final = -2146367475
COMADMIN_E_USER_IN_SET: Final = -2146367474
COMADMIN_E_CANTRECYCLELIBRARYAPPS: Final = -2146367473
COMADMIN_E_CANTRECYCLESERVICEAPPS: Final = -2146367471
COMADMIN_E_PROCESSALREADYRECYCLED: Final = -2146367470
COMADMIN_E_PAUSEDPROCESSMAYNOTBERECYCLED: Final = -2146367469
COMADMIN_E_CANTMAKEINPROCSERVICE: Final = -2146367468
COMADMIN_E_PROGIDINUSEBYCLSID: Final = -2146367467
COMADMIN_E_DEFAULT_PARTITION_NOT_IN_SET: Final = -2146367466
COMADMIN_E_RECYCLEDPROCESSMAYNOTBEPAUSED: Final = -2146367465
COMADMIN_E_PARTITION_ACCESSDENIED: Final = -2146367464
COMADMIN_E_PARTITION_MSI_ONLY: Final = -2146367463
COMADMIN_E_LEGACYCOMPS_NOT_ALLOWED_IN_1_0_FORMAT: Final = -2146367462
COMADMIN_E_LEGACYCOMPS_NOT_ALLOWED_IN_NONBASE_PARTITIONS: Final = -2146367461
COMADMIN_E_COMP_MOVE_SOURCE: Final = -2146367460
COMADMIN_E_COMP_MOVE_DEST: Final = -2146367459
COMADMIN_E_COMP_MOVE_PRIVATE: Final = -2146367458
COMADMIN_E_BASEPARTITION_REQUIRED_IN_SET: Final = -2146367457
COMADMIN_E_CANNOT_ALIAS_EVENTCLASS: Final = -2146367456
COMADMIN_E_PRIVATE_ACCESSDENIED: Final = -2146367455
COMADMIN_E_SAFERINVALID: Final = -2146367454
COMADMIN_E_REGISTRY_ACCESSDENIED: Final = -2146367453
COMADMIN_E_PARTITIONS_DISABLED: Final = -2146367452
MENROLL_E_DEVICE_MESSAGE_FORMAT_ERROR: Final = -2145910783
MENROLL_E_DEVICE_AUTHENTICATION_ERROR: Final = -2145910782
MENROLL_E_DEVICE_AUTHORIZATION_ERROR: Final = -2145910781
MENROLL_E_DEVICE_CERTIFICATEREQUEST_ERROR: Final = -2145910780
MENROLL_E_DEVICE_CONFIGMGRSERVER_ERROR: Final = -2145910779
MENROLL_E_DEVICE_INTERNALSERVICE_ERROR: Final = -2145910778
MENROLL_E_DEVICE_INVALIDSECURITY_ERROR: Final = -2145910777
MENROLL_E_DEVICE_UNKNOWN_ERROR: Final = -2145910776
MENROLL_E_ENROLLMENT_IN_PROGRESS: Final = -2145910775
MENROLL_E_DEVICE_ALREADY_ENROLLED: Final = -2145910774
MENROLL_E_DISCOVERY_SEC_CERT_DATE_INVALID: Final = -**********
MENROLL_E_PASSWORD_NEEDED: Final = -**********
MENROLL_E_WAB_ERROR: Final = -**********
MENROLL_E_CONNECTIVITY: Final = -**********
MENROLL_S_ENROLLMENT_SUSPENDED: Final = 0x00180011
MENROLL_E_INVALIDSSLCERT: Final = -**********
MENROLL_E_DEVICECAPREACHED: Final = -**********
MENROLL_E_DEVICENOTSUPPORTED: Final = -**********
MENROLL_E_NOT_SUPPORTED: Final = -**********
MENROLL_E_NOTELIGIBLETORENEW: Final = -**********
MENROLL_E_INMAINTENANCE: Final = -**********
MENROLL_E_USER_LICENSE: Final = -**********
MENROLL_E_ENROLLMENTDATAINVALID: Final = -**********
MENROLL_E_INSECUREREDIRECT: Final = -**********
MENROLL_E_PLATFORM_WRONG_STATE: Final = -**********
MENROLL_E_PLATFORM_LICENSE_ERROR: Final = -**********
MENROLL_E_PLATFORM_UNKNOWN_ERROR: Final = -**********
MENROLL_E_PROV_CSP_CERTSTORE: Final = -**********
MENROLL_E_PROV_CSP_W7: Final = -**********
MENROLL_E_PROV_CSP_DMCLIENT: Final = -**********
MENROLL_E_PROV_CSP_PFW: Final = -**********
MENROLL_E_PROV_CSP_MISC: Final = -**********
MENROLL_E_PROV_UNKNOWN: Final = -**********
MENROLL_E_PROV_SSLCERTNOTFOUND: Final = -**********
MENROLL_E_PROV_CSP_APPMGMT: Final = -**********
MENROLL_E_DEVICE_MANAGEMENT_BLOCKED: Final = -**********
MENROLL_E_CERTPOLICY_PRIVATEKEYCREATION_FAILED: Final = -**********
MENROLL_E_CERTAUTH_FAILED_TO_FIND_CERT: Final = -**********
MENROLL_E_EMPTY_MESSAGE: Final = -**********
MENROLL_E_USER_CANCELLED: Final = -**********
MENROLL_E_MDM_NOT_CONFIGURED: Final = -**********
MENROLL_E_CUSTOMSERVERERROR: Final = -**********
WER_S_REPORT_DEBUG: Final = 0x001B0000
WER_S_REPORT_UPLOADED: Final = 0x001B0001
WER_S_REPORT_QUEUED: Final = 0x001B0002
WER_S_DISABLED: Final = 0x001B0003
WER_S_SUSPENDED_UPLOAD: Final = 0x001B0004
WER_S_DISABLED_QUEUE: Final = 0x001B0005
WER_S_DISABLED_ARCHIVE: Final = 0x001B0006
WER_S_REPORT_ASYNC: Final = 0x001B0007
WER_S_IGNORE_ASSERT_INSTANCE: Final = 0x001B0008
WER_S_IGNORE_ALL_ASSERTS: Final = 0x001B0009
WER_S_ASSERT_CONTINUE: Final = 0x001B000A
WER_S_THROTTLED: Final = 0x001B000B
WER_S_REPORT_UPLOADED_CAB: Final = 0x001B000C
WER_E_CRASH_FAILURE: Final = -2145681408
WER_E_CANCELED: Final = -2145681407
WER_E_NETWORK_FAILURE: Final = -2145681406
WER_E_NOT_INITIALIZED: Final = -2145681405
WER_E_ALREADY_REPORTING: Final = -2145681404
WER_E_DUMP_THROTTLED: Final = -2145681403
WER_E_INSUFFICIENT_CONSENT: Final = -2145681402
WER_E_TOO_HEAVY: Final = -2145681401

def FILTER_HRESULT_FROM_FLT_NTSTATUS(x: int) -> int: ...

ERROR_FLT_IO_COMPLETE: Final = 0x001F0001
ERROR_FLT_NO_HANDLER_DEFINED: Final = -2145452031
ERROR_FLT_CONTEXT_ALREADY_DEFINED: Final = -2145452030
ERROR_FLT_INVALID_ASYNCHRONOUS_REQUEST: Final = -2145452029
ERROR_FLT_DISALLOW_FAST_IO: Final = -2145452028
ERROR_FLT_INVALID_NAME_REQUEST: Final = -2145452027
ERROR_FLT_NOT_SAFE_TO_POST_OPERATION: Final = -2145452026
ERROR_FLT_NOT_INITIALIZED: Final = -2145452025
ERROR_FLT_FILTER_NOT_READY: Final = -2145452024
ERROR_FLT_POST_OPERATION_CLEANUP: Final = -2145452023
ERROR_FLT_INTERNAL_ERROR: Final = -2145452022
ERROR_FLT_DELETING_OBJECT: Final = -2145452021
ERROR_FLT_MUST_BE_NONPAGED_POOL: Final = -2145452020
ERROR_FLT_DUPLICATE_ENTRY: Final = -2145452019
ERROR_FLT_CBDQ_DISABLED: Final = -2145452018
ERROR_FLT_DO_NOT_ATTACH: Final = -2145452017
ERROR_FLT_DO_NOT_DETACH: Final = -2145452016
ERROR_FLT_INSTANCE_ALTITUDE_COLLISION: Final = -2145452015
ERROR_FLT_INSTANCE_NAME_COLLISION: Final = -2145452014
ERROR_FLT_FILTER_NOT_FOUND: Final = -2145452013
ERROR_FLT_VOLUME_NOT_FOUND: Final = -2145452012
ERROR_FLT_INSTANCE_NOT_FOUND: Final = -2145452011
ERROR_FLT_CONTEXT_ALLOCATION_NOT_FOUND: Final = -2145452010
ERROR_FLT_INVALID_CONTEXT_REGISTRATION: Final = -2145452009
ERROR_FLT_NAME_CACHE_MISS: Final = -2145452008
ERROR_FLT_NO_DEVICE_OBJECT: Final = -2145452007
ERROR_FLT_VOLUME_ALREADY_MOUNTED: Final = -2145452006
ERROR_FLT_ALREADY_ENLISTED: Final = -2145452005
ERROR_FLT_CONTEXT_ALREADY_LINKED: Final = -2145452004
ERROR_FLT_NO_WAITER_FOR_REPLY: Final = -2145452000
ERROR_FLT_REGISTRATION_BUSY: Final = -2145451997
ERROR_FLT_WCOS_NOT_SUPPORTED: Final = -2145451996
ERROR_HUNG_DISPLAY_DRIVER_THREAD: Final = -2144993279
DWM_E_COMPOSITIONDISABLED: Final = -2144980991
DWM_E_REMOTING_NOT_SUPPORTED: Final = -2144980990
DWM_E_NO_REDIRECTION_SURFACE_AVAILABLE: Final = -2144980989
DWM_E_NOT_QUEUING_PRESENTS: Final = -2144980988
DWM_E_ADAPTER_NOT_FOUND: Final = -2144980987
DWM_S_GDI_REDIRECTION_SURFACE: Final = 0x00263005
DWM_E_TEXTURE_TOO_LARGE: Final = -2144980985
DWM_S_GDI_REDIRECTION_SURFACE_BLT_VIA_GDI: Final = 0x00263008
ERROR_MONITOR_NO_DESCRIPTOR: Final = 0x00261001
ERROR_MONITOR_UNKNOWN_DESCRIPTOR_FORMAT: Final = 0x00261002
ERROR_MONITOR_INVALID_DESCRIPTOR_CHECKSUM: Final = -1071247357
ERROR_MONITOR_INVALID_STANDARD_TIMING_BLOCK: Final = -1071247356
ERROR_MONITOR_WMI_DATABLOCK_REGISTRATION_FAILED: Final = -1071247355
ERROR_MONITOR_INVALID_SERIAL_NUMBER_MONDSC_BLOCK: Final = -1071247354
ERROR_MONITOR_INVALID_USER_FRIENDLY_MONDSC_BLOCK: Final = -1071247353
ERROR_MONITOR_NO_MORE_DESCRIPTOR_DATA: Final = -1071247352
ERROR_MONITOR_INVALID_DETAILED_TIMING_BLOCK: Final = -1071247351
ERROR_MONITOR_INVALID_MANUFACTURE_DATE: Final = -1071247350
ERROR_GRAPHICS_NOT_EXCLUSIVE_MODE_OWNER: Final = -1071243264
ERROR_GRAPHICS_INSUFFICIENT_DMA_BUFFER: Final = -1071243263
ERROR_GRAPHICS_INVALID_DISPLAY_ADAPTER: Final = -1071243262
ERROR_GRAPHICS_ADAPTER_WAS_RESET: Final = -1071243261
ERROR_GRAPHICS_INVALID_DRIVER_MODEL: Final = -1071243260
ERROR_GRAPHICS_PRESENT_MODE_CHANGED: Final = -1071243259
ERROR_GRAPHICS_PRESENT_OCCLUDED: Final = -1071243258
ERROR_GRAPHICS_PRESENT_DENIED: Final = -1071243257
ERROR_GRAPHICS_CANNOTCOLORCONVERT: Final = -1071243256
ERROR_GRAPHICS_DRIVER_MISMATCH: Final = -1071243255
ERROR_GRAPHICS_PARTIAL_DATA_POPULATED: Final = 0x4026200A
ERROR_GRAPHICS_PRESENT_REDIRECTION_DISABLED: Final = -1071243253
ERROR_GRAPHICS_PRESENT_UNOCCLUDED: Final = -1071243252
ERROR_GRAPHICS_WINDOWDC_NOT_AVAILABLE: Final = -1071243251
ERROR_GRAPHICS_WINDOWLESS_PRESENT_DISABLED: Final = -1071243250
ERROR_GRAPHICS_PRESENT_INVALID_WINDOW: Final = -1071243249
ERROR_GRAPHICS_PRESENT_BUFFER_NOT_BOUND: Final = -1071243248
ERROR_GRAPHICS_VAIL_STATE_CHANGED: Final = -**********
ERROR_GRAPHICS_INDIRECT_DISPLAY_ABANDON_SWAPCHAIN: Final = -**********
ERROR_GRAPHICS_INDIRECT_DISPLAY_DEVICE_STOPPED: Final = -**********
ERROR_GRAPHICS_VAIL_FAILED_TO_SEND_CREATE_SUPERWETINK_MESSAGE: Final = -**********
ERROR_GRAPHICS_VAIL_FAILED_TO_SEND_DESTROY_SUPERWETINK_MESSAGE: Final = -**********
ERROR_GRAPHICS_VAIL_FAILED_TO_SEND_COMPOSITION_WINDOW_DPI_MESSAGE: Final = -**********
ERROR_GRAPHICS_LINK_CONFIGURATION_IN_PROGRESS: Final = -**********
ERROR_GRAPHICS_MPO_ALLOCATION_UNPINNED: Final = -**********
ERROR_GRAPHICS_NO_VIDEO_MEMORY: Final = -**********
ERROR_GRAPHICS_CANT_LOCK_MEMORY: Final = -**********
ERROR_GRAPHICS_ALLOCATION_BUSY: Final = -**********
ERROR_GRAPHICS_TOO_MANY_REFERENCES: Final = -**********
ERROR_GRAPHICS_TRY_AGAIN_LATER: Final = -**********
ERROR_GRAPHICS_TRY_AGAIN_NOW: Final = -**********
ERROR_GRAPHICS_ALLOCATION_INVALID: Final = -**********
ERROR_GRAPHICS_UNSWIZZLING_APERTURE_UNAVAILABLE: Final = -**********
ERROR_GRAPHICS_UNSWIZZLING_APERTURE_UNSUPPORTED: Final = -**********
ERROR_GRAPHICS_CANT_EVICT_PINNED_ALLOCATION: Final = -**********
ERROR_GRAPHICS_INVALID_ALLOCATION_USAGE: Final = -**********
ERROR_GRAPHICS_CANT_RENDER_LOCKED_ALLOCATION: Final = -**********
ERROR_GRAPHICS_ALLOCATION_CLOSED: Final = -**********
ERROR_GRAPHICS_INVALID_ALLOCATION_INSTANCE: Final = -**********
ERROR_GRAPHICS_INVALID_ALLOCATION_HANDLE: Final = -**********
ERROR_GRAPHICS_WRONG_ALLOCATION_DEVICE: Final = -**********
ERROR_GRAPHICS_ALLOCATION_CONTENT_LOST: Final = -1071242986
ERROR_GRAPHICS_GPU_EXCEPTION_ON_DEVICE: Final = -1071242752
ERROR_GRAPHICS_SKIP_ALLOCATION_PREPARATION: Final = 0x40262201
ERROR_GRAPHICS_INVALID_VIDPN_TOPOLOGY: Final = -1071242496
ERROR_GRAPHICS_VIDPN_TOPOLOGY_NOT_SUPPORTED: Final = -1071242495
ERROR_GRAPHICS_VIDPN_TOPOLOGY_CURRENTLY_NOT_SUPPORTED: Final = -1071242494
ERROR_GRAPHICS_INVALID_VIDPN: Final = -1071242493
ERROR_GRAPHICS_INVALID_VIDEO_PRESENT_SOURCE: Final = -1071242492
ERROR_GRAPHICS_INVALID_VIDEO_PRESENT_TARGET: Final = -1071242491
ERROR_GRAPHICS_VIDPN_MODALITY_NOT_SUPPORTED: Final = -1071242490
ERROR_GRAPHICS_MODE_NOT_PINNED: Final = 0x00262307
ERROR_GRAPHICS_INVALID_VIDPN_SOURCEMODESET: Final = -1071242488
ERROR_GRAPHICS_INVALID_VIDPN_TARGETMODESET: Final = -1071242487
ERROR_GRAPHICS_INVALID_FREQUENCY: Final = -1071242486
ERROR_GRAPHICS_INVALID_ACTIVE_REGION: Final = -1071242485
ERROR_GRAPHICS_INVALID_TOTAL_REGION: Final = -1071242484
ERROR_GRAPHICS_INVALID_VIDEO_PRESENT_SOURCE_MODE: Final = -1071242480
ERROR_GRAPHICS_INVALID_VIDEO_PRESENT_TARGET_MODE: Final = -1071242479
ERROR_GRAPHICS_PINNED_MODE_MUST_REMAIN_IN_SET: Final = -1071242478
ERROR_GRAPHICS_PATH_ALREADY_IN_TOPOLOGY: Final = -1071242477
ERROR_GRAPHICS_MODE_ALREADY_IN_MODESET: Final = -1071242476
ERROR_GRAPHICS_INVALID_VIDEOPRESENTSOURCESET: Final = -1071242475
ERROR_GRAPHICS_INVALID_VIDEOPRESENTTARGETSET: Final = -1071242474
ERROR_GRAPHICS_SOURCE_ALREADY_IN_SET: Final = -1071242473
ERROR_GRAPHICS_TARGET_ALREADY_IN_SET: Final = -1071242472
ERROR_GRAPHICS_INVALID_VIDPN_PRESENT_PATH: Final = -1071242471
ERROR_GRAPHICS_NO_RECOMMENDED_VIDPN_TOPOLOGY: Final = -1071242470
ERROR_GRAPHICS_INVALID_MONITOR_FREQUENCYRANGESET: Final = -1071242469
ERROR_GRAPHICS_INVALID_MONITOR_FREQUENCYRANGE: Final = -1071242468
ERROR_GRAPHICS_FREQUENCYRANGE_NOT_IN_SET: Final = -1071242467
ERROR_GRAPHICS_NO_PREFERRED_MODE: Final = 0x0026231E
ERROR_GRAPHICS_FREQUENCYRANGE_ALREADY_IN_SET: Final = -1071242465
ERROR_GRAPHICS_STALE_MODESET: Final = -1071242464
ERROR_GRAPHICS_INVALID_MONITOR_SOURCEMODESET: Final = -1071242463
ERROR_GRAPHICS_INVALID_MONITOR_SOURCE_MODE: Final = -1071242462
ERROR_GRAPHICS_NO_RECOMMENDED_FUNCTIONAL_VIDPN: Final = -1071242461
ERROR_GRAPHICS_MODE_ID_MUST_BE_UNIQUE: Final = -1071242460
ERROR_GRAPHICS_EMPTY_ADAPTER_MONITOR_MODE_SUPPORT_INTERSECTION: Final = -1071242459
ERROR_GRAPHICS_VIDEO_PRESENT_TARGETS_LESS_THAN_SOURCES: Final = -1071242458
ERROR_GRAPHICS_PATH_NOT_IN_TOPOLOGY: Final = -1071242457
ERROR_GRAPHICS_ADAPTER_MUST_HAVE_AT_LEAST_ONE_SOURCE: Final = -1071242456
ERROR_GRAPHICS_ADAPTER_MUST_HAVE_AT_LEAST_ONE_TARGET: Final = -1071242455
ERROR_GRAPHICS_INVALID_MONITORDESCRIPTORSET: Final = -1071242454
ERROR_GRAPHICS_INVALID_MONITORDESCRIPTOR: Final = -1071242453
ERROR_GRAPHICS_MONITORDESCRIPTOR_NOT_IN_SET: Final = -1071242452
ERROR_GRAPHICS_MONITORDESCRIPTOR_ALREADY_IN_SET: Final = -1071242451
ERROR_GRAPHICS_MONITORDESCRIPTOR_ID_MUST_BE_UNIQUE: Final = -1071242450
ERROR_GRAPHICS_INVALID_VIDPN_TARGET_SUBSET_TYPE: Final = -1071242449
ERROR_GRAPHICS_RESOURCES_NOT_RELATED: Final = -1071242448
ERROR_GRAPHICS_SOURCE_ID_MUST_BE_UNIQUE: Final = -1071242447
ERROR_GRAPHICS_TARGET_ID_MUST_BE_UNIQUE: Final = -1071242446
ERROR_GRAPHICS_NO_AVAILABLE_VIDPN_TARGET: Final = -1071242445
ERROR_GRAPHICS_MONITOR_COULD_NOT_BE_ASSOCIATED_WITH_ADAPTER: Final = -1071242444
ERROR_GRAPHICS_NO_VIDPNMGR: Final = -1071242443
ERROR_GRAPHICS_NO_ACTIVE_VIDPN: Final = -1071242442
ERROR_GRAPHICS_STALE_VIDPN_TOPOLOGY: Final = -1071242441
ERROR_GRAPHICS_MONITOR_NOT_CONNECTED: Final = -1071242440
ERROR_GRAPHICS_SOURCE_NOT_IN_TOPOLOGY: Final = -1071242439
ERROR_GRAPHICS_INVALID_PRIMARYSURFACE_SIZE: Final = -1071242438
ERROR_GRAPHICS_INVALID_VISIBLEREGION_SIZE: Final = -1071242437
ERROR_GRAPHICS_INVALID_STRIDE: Final = -1071242436
ERROR_GRAPHICS_INVALID_PIXELFORMAT: Final = -1071242435
ERROR_GRAPHICS_INVALID_COLORBASIS: Final = -1071242434
ERROR_GRAPHICS_INVALID_PIXELVALUEACCESSMODE: Final = -1071242433
ERROR_GRAPHICS_TARGET_NOT_IN_TOPOLOGY: Final = -1071242432
ERROR_GRAPHICS_NO_DISPLAY_MODE_MANAGEMENT_SUPPORT: Final = -1071242431
ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE: Final = -1071242430
ERROR_GRAPHICS_CANT_ACCESS_ACTIVE_VIDPN: Final = -1071242429
ERROR_GRAPHICS_INVALID_PATH_IMPORTANCE_ORDINAL: Final = -1071242428
ERROR_GRAPHICS_INVALID_PATH_CONTENT_GEOMETRY_TRANSFORMATION: Final = -1071242427
ERROR_GRAPHICS_PATH_CONTENT_GEOMETRY_TRANSFORMATION_NOT_SUPPORTED: Final = -1071242426
ERROR_GRAPHICS_INVALID_GAMMA_RAMP: Final = -1071242425
ERROR_GRAPHICS_GAMMA_RAMP_NOT_SUPPORTED: Final = -1071242424
ERROR_GRAPHICS_MULTISAMPLING_NOT_SUPPORTED: Final = -1071242423
ERROR_GRAPHICS_MODE_NOT_IN_MODESET: Final = -1071242422
ERROR_GRAPHICS_DATASET_IS_EMPTY: Final = 0x0026234B
ERROR_GRAPHICS_NO_MORE_ELEMENTS_IN_DATASET: Final = 0x0026234C
ERROR_GRAPHICS_INVALID_VIDPN_TOPOLOGY_RECOMMENDATION_REASON: Final = -1071242419
ERROR_GRAPHICS_INVALID_PATH_CONTENT_TYPE: Final = -1071242418
ERROR_GRAPHICS_INVALID_COPYPROTECTION_TYPE: Final = -1071242417
ERROR_GRAPHICS_UNASSIGNED_MODESET_ALREADY_EXISTS: Final = -1071242416
ERROR_GRAPHICS_PATH_CONTENT_GEOMETRY_TRANSFORMATION_NOT_PINNED: Final = 0x00262351
ERROR_GRAPHICS_INVALID_SCANLINE_ORDERING: Final = -1071242414
ERROR_GRAPHICS_TOPOLOGY_CHANGES_NOT_ALLOWED: Final = -1071242413
ERROR_GRAPHICS_NO_AVAILABLE_IMPORTANCE_ORDINALS: Final = -1071242412
ERROR_GRAPHICS_INCOMPATIBLE_PRIVATE_FORMAT: Final = -1071242411
ERROR_GRAPHICS_INVALID_MODE_PRUNING_ALGORITHM: Final = -1071242410
ERROR_GRAPHICS_INVALID_MONITOR_CAPABILITY_ORIGIN: Final = -1071242409
ERROR_GRAPHICS_INVALID_MONITOR_FREQUENCYRANGE_CONSTRAINT: Final = -1071242408
ERROR_GRAPHICS_MAX_NUM_PATHS_REACHED: Final = -1071242407
ERROR_GRAPHICS_CANCEL_VIDPN_TOPOLOGY_AUGMENTATION: Final = -1071242406
ERROR_GRAPHICS_INVALID_CLIENT_TYPE: Final = -1071242405
ERROR_GRAPHICS_CLIENTVIDPN_NOT_SET: Final = -1071242404
ERROR_GRAPHICS_SPECIFIED_CHILD_ALREADY_CONNECTED: Final = -1071242240
ERROR_GRAPHICS_CHILD_DESCRIPTOR_NOT_SUPPORTED: Final = -1071242239
ERROR_GRAPHICS_UNKNOWN_CHILD_STATUS: Final = 0x4026242F
ERROR_GRAPHICS_NOT_A_LINKED_ADAPTER: Final = -1071242192
ERROR_GRAPHICS_LEADLINK_NOT_ENUMERATED: Final = -1071242191
ERROR_GRAPHICS_CHAINLINKS_NOT_ENUMERATED: Final = -1071242190
ERROR_GRAPHICS_ADAPTER_CHAIN_NOT_READY: Final = -1071242189
ERROR_GRAPHICS_CHAINLINKS_NOT_STARTED: Final = -1071242188
ERROR_GRAPHICS_CHAINLINKS_NOT_POWERED_ON: Final = -1071242187
ERROR_GRAPHICS_INCONSISTENT_DEVICE_LINK_STATE: Final = -1071242186
ERROR_GRAPHICS_LEADLINK_START_DEFERRED: Final = 0x40262437
ERROR_GRAPHICS_NOT_POST_DEVICE_DRIVER: Final = -1071242184
ERROR_GRAPHICS_POLLING_TOO_FREQUENTLY: Final = 0x40262439
ERROR_GRAPHICS_START_DEFERRED: Final = 0x4026243A
ERROR_GRAPHICS_ADAPTER_ACCESS_NOT_EXCLUDED: Final = -1071242181
ERROR_GRAPHICS_DEPENDABLE_CHILD_STATUS: Final = 0x4026243C
ERROR_GRAPHICS_OPM_NOT_SUPPORTED: Final = -1071241984
ERROR_GRAPHICS_COPP_NOT_SUPPORTED: Final = -1071241983
ERROR_GRAPHICS_UAB_NOT_SUPPORTED: Final = -1071241982
ERROR_GRAPHICS_OPM_INVALID_ENCRYPTED_PARAMETERS: Final = -1071241981
ERROR_GRAPHICS_OPM_NO_VIDEO_OUTPUTS_EXIST: Final = -1071241979
ERROR_GRAPHICS_OPM_INTERNAL_ERROR: Final = -1071241973
ERROR_GRAPHICS_OPM_INVALID_HANDLE: Final = -1071241972
ERROR_GRAPHICS_PVP_INVALID_CERTIFICATE_LENGTH: Final = -1071241970
ERROR_GRAPHICS_OPM_SPANNING_MODE_ENABLED: Final = -1071241969
ERROR_GRAPHICS_OPM_THEATER_MODE_ENABLED: Final = -1071241968
ERROR_GRAPHICS_PVP_HFS_FAILED: Final = -1071241967
ERROR_GRAPHICS_OPM_INVALID_SRM: Final = -1071241966
ERROR_GRAPHICS_OPM_OUTPUT_DOES_NOT_SUPPORT_HDCP: Final = -1071241965
ERROR_GRAPHICS_OPM_OUTPUT_DOES_NOT_SUPPORT_ACP: Final = -1071241964
ERROR_GRAPHICS_OPM_OUTPUT_DOES_NOT_SUPPORT_CGMSA: Final = -1071241963
ERROR_GRAPHICS_OPM_HDCP_SRM_NEVER_SET: Final = -1071241962
ERROR_GRAPHICS_OPM_RESOLUTION_TOO_HIGH: Final = -1071241961
ERROR_GRAPHICS_OPM_ALL_HDCP_HARDWARE_ALREADY_IN_USE: Final = -1071241960
ERROR_GRAPHICS_OPM_VIDEO_OUTPUT_NO_LONGER_EXISTS: Final = -1071241958
ERROR_GRAPHICS_OPM_SESSION_TYPE_CHANGE_IN_PROGRESS: Final = -1071241957
ERROR_GRAPHICS_OPM_VIDEO_OUTPUT_DOES_NOT_HAVE_COPP_SEMANTICS: Final = -1071241956
ERROR_GRAPHICS_OPM_INVALID_INFORMATION_REQUEST: Final = -1071241955
ERROR_GRAPHICS_OPM_DRIVER_INTERNAL_ERROR: Final = -1071241954
ERROR_GRAPHICS_OPM_VIDEO_OUTPUT_DOES_NOT_HAVE_OPM_SEMANTICS: Final = -1071241953
ERROR_GRAPHICS_OPM_SIGNALING_NOT_SUPPORTED: Final = -1071241952
ERROR_GRAPHICS_OPM_INVALID_CONFIGURATION_REQUEST: Final = -1071241951
ERROR_GRAPHICS_I2C_NOT_SUPPORTED: Final = -1071241856
ERROR_GRAPHICS_I2C_DEVICE_DOES_NOT_EXIST: Final = -1071241855
ERROR_GRAPHICS_I2C_ERROR_TRANSMITTING_DATA: Final = -1071241854
ERROR_GRAPHICS_I2C_ERROR_RECEIVING_DATA: Final = -1071241853
ERROR_GRAPHICS_DDCCI_VCP_NOT_SUPPORTED: Final = -1071241852
ERROR_GRAPHICS_DDCCI_INVALID_DATA: Final = -1071241851
ERROR_GRAPHICS_DDCCI_MONITOR_RETURNED_INVALID_TIMING_STATUS_BYTE: Final = -1071241850
ERROR_GRAPHICS_MCA_INVALID_CAPABILITIES_STRING: Final = -1071241849
ERROR_GRAPHICS_MCA_INTERNAL_ERROR: Final = -1071241848
ERROR_GRAPHICS_DDCCI_INVALID_MESSAGE_COMMAND: Final = -1071241847
ERROR_GRAPHICS_DDCCI_INVALID_MESSAGE_LENGTH: Final = -1071241846
ERROR_GRAPHICS_DDCCI_INVALID_MESSAGE_CHECKSUM: Final = -1071241845
ERROR_GRAPHICS_INVALID_PHYSICAL_MONITOR_HANDLE: Final = -1071241844
ERROR_GRAPHICS_MONITOR_NO_LONGER_EXISTS: Final = -1071241843
ERROR_GRAPHICS_DDCCI_CURRENT_CURRENT_VALUE_GREATER_THAN_MAXIMUM_VALUE: Final = -1071241768
ERROR_GRAPHICS_MCA_INVALID_VCP_VERSION: Final = -1071241767
ERROR_GRAPHICS_MCA_MONITOR_VIOLATES_MCCS_SPECIFICATION: Final = -1071241766
ERROR_GRAPHICS_MCA_MCCS_VERSION_MISMATCH: Final = -1071241765
ERROR_GRAPHICS_MCA_UNSUPPORTED_MCCS_VERSION: Final = -1071241764
ERROR_GRAPHICS_MCA_INVALID_TECHNOLOGY_TYPE_RETURNED: Final = -1071241762
ERROR_GRAPHICS_MCA_UNSUPPORTED_COLOR_TEMPERATURE: Final = -1071241761
ERROR_GRAPHICS_ONLY_CONSOLE_SESSION_SUPPORTED: Final = -1071241760
ERROR_GRAPHICS_NO_DISPLAY_DEVICE_CORRESPONDS_TO_NAME: Final = -1071241759
ERROR_GRAPHICS_DISPLAY_DEVICE_NOT_ATTACHED_TO_DESKTOP: Final = -1071241758
ERROR_GRAPHICS_MIRRORING_DEVICES_NOT_SUPPORTED: Final = -1071241757
ERROR_GRAPHICS_INVALID_POINTER: Final = -1071241756
ERROR_GRAPHICS_NO_MONITORS_CORRESPOND_TO_DISPLAY_DEVICE: Final = -1071241755
ERROR_GRAPHICS_PARAMETER_ARRAY_TOO_SMALL: Final = -1071241754
ERROR_GRAPHICS_INTERNAL_ERROR: Final = -1071241753
ERROR_GRAPHICS_SESSION_TYPE_CHANGE_IN_PROGRESS: Final = -1071249944
NAP_E_INVALID_PACKET: Final = -2144927743
NAP_E_MISSING_SOH: Final = -2144927742
NAP_E_CONFLICTING_ID: Final = -2144927741
NAP_E_NO_CACHED_SOH: Final = -2144927740
NAP_E_STILL_BOUND: Final = -2144927739
NAP_E_NOT_REGISTERED: Final = -2144927738
NAP_E_NOT_INITIALIZED: Final = -2144927737
NAP_E_MISMATCHED_ID: Final = -2144927736
NAP_E_NOT_PENDING: Final = -2144927735
NAP_E_ID_NOT_FOUND: Final = -2144927734
NAP_E_MAXSIZE_TOO_SMALL: Final = -2144927733
NAP_E_SERVICE_NOT_RUNNING: Final = -2144927732
NAP_S_CERT_ALREADY_PRESENT: Final = 0x0027000D
NAP_E_ENTITY_DISABLED: Final = -2144927730
NAP_E_NETSH_GROUPPOLICY_ERROR: Final = -2144927729
NAP_E_TOO_MANY_CALLS: Final = -2144927728
NAP_E_SHV_CONFIG_EXISTED: Final = -2144927727
NAP_E_SHV_CONFIG_NOT_FOUND: Final = -2144927726
NAP_E_SHV_TIMEOUT: Final = -2144927725
TPM_E_ERROR_MASK: Final = -2144862208
TPM_E_AUTHFAIL: Final = -2144862207
TPM_E_BADINDEX: Final = -2144862206
TPM_E_BAD_PARAMETER: Final = -2144862205
TPM_E_AUDITFAILURE: Final = -2144862204
TPM_E_CLEAR_DISABLED: Final = -2144862203
TPM_E_DEACTIVATED: Final = -2144862202
TPM_E_DISABLED: Final = -2144862201
TPM_E_DISABLED_CMD: Final = -2144862200
TPM_E_FAIL: Final = -2144862199
TPM_E_BAD_ORDINAL: Final = -2144862198
TPM_E_INSTALL_DISABLED: Final = -2144862197
TPM_E_INVALID_KEYHANDLE: Final = -2144862196
TPM_E_KEYNOTFOUND: Final = -2144862195
TPM_E_INAPPROPRIATE_ENC: Final = -2144862194
TPM_E_MIGRATEFAIL: Final = -2144862193
TPM_E_INVALID_PCR_INFO: Final = -2144862192
TPM_E_NOSPACE: Final = -2144862191
TPM_E_NOSRK: Final = -2144862190
TPM_E_NOTSEALED_BLOB: Final = -2144862189
TPM_E_OWNER_SET: Final = -2144862188
TPM_E_RESOURCES: Final = -2144862187
TPM_E_SHORTRANDOM: Final = -2144862186
TPM_E_SIZE: Final = -2144862185
TPM_E_WRONGPCRVAL: Final = -2144862184
TPM_E_BAD_PARAM_SIZE: Final = -2144862183
TPM_E_SHA_THREAD: Final = -2144862182
TPM_E_SHA_ERROR: Final = -2144862181
TPM_E_FAILEDSELFTEST: Final = -2144862180
TPM_E_AUTH2FAIL: Final = -2144862179
TPM_E_BADTAG: Final = -2144862178
TPM_E_IOERROR: Final = -2144862177
TPM_E_ENCRYPT_ERROR: Final = -2144862176
TPM_E_DECRYPT_ERROR: Final = -2144862175
TPM_E_INVALID_AUTHHANDLE: Final = -2144862174
TPM_E_NO_ENDORSEMENT: Final = -2144862173
TPM_E_INVALID_KEYUSAGE: Final = -2144862172
TPM_E_WRONG_ENTITYTYPE: Final = -2144862171
TPM_E_INVALID_POSTINIT: Final = -2144862170
TPM_E_INAPPROPRIATE_SIG: Final = -2144862169
TPM_E_BAD_KEY_PROPERTY: Final = -2144862168
TPM_E_BAD_MIGRATION: Final = -2144862167
TPM_E_BAD_SCHEME: Final = -2144862166
TPM_E_BAD_DATASIZE: Final = -2144862165
TPM_E_BAD_MODE: Final = -2144862164
TPM_E_BAD_PRESENCE: Final = -2144862163
TPM_E_BAD_VERSION: Final = -2144862162
TPM_E_NO_WRAP_TRANSPORT: Final = -2144862161
TPM_E_AUDITFAIL_UNSUCCESSFUL: Final = -2144862160
TPM_E_AUDITFAIL_SUCCESSFUL: Final = -2144862159
TPM_E_NOTRESETABLE: Final = -2144862158
TPM_E_NOTLOCAL: Final = -2144862157
TPM_E_BAD_TYPE: Final = -2144862156
TPM_E_INVALID_RESOURCE: Final = -2144862155
TPM_E_NOTFIPS: Final = -2144862154
TPM_E_INVALID_FAMILY: Final = -2144862153
TPM_E_NO_NV_PERMISSION: Final = -2144862152
TPM_E_REQUIRES_SIGN: Final = -2144862151
TPM_E_KEY_NOTSUPPORTED: Final = -2144862150
TPM_E_AUTH_CONFLICT: Final = -2144862149
TPM_E_AREA_LOCKED: Final = -2144862148
TPM_E_BAD_LOCALITY: Final = -2144862147
TPM_E_READ_ONLY: Final = -2144862146
TPM_E_PER_NOWRITE: Final = -2144862145
TPM_E_FAMILYCOUNT: Final = -2144862144
TPM_E_WRITE_LOCKED: Final = -2144862143
TPM_E_BAD_ATTRIBUTES: Final = -2144862142
TPM_E_INVALID_STRUCTURE: Final = -2144862141
TPM_E_KEY_OWNER_CONTROL: Final = -2144862140
TPM_E_BAD_COUNTER: Final = -2144862139
TPM_E_NOT_FULLWRITE: Final = -2144862138
TPM_E_CONTEXT_GAP: Final = -2144862137
TPM_E_MAXNVWRITES: Final = -2144862136
TPM_E_NOOPERATOR: Final = -2144862135
TPM_E_RESOURCEMISSING: Final = -2144862134
TPM_E_DELEGATE_LOCK: Final = -2144862133
TPM_E_DELEGATE_FAMILY: Final = -2144862132
TPM_E_DELEGATE_ADMIN: Final = -2144862131
TPM_E_TRANSPORT_NOTEXCLUSIVE: Final = -2144862130
TPM_E_OWNER_CONTROL: Final = -2144862129
TPM_E_DAA_RESOURCES: Final = -2144862128
TPM_E_DAA_INPUT_DATA0: Final = -2144862127
TPM_E_DAA_INPUT_DATA1: Final = -2144862126
TPM_E_DAA_ISSUER_SETTINGS: Final = -2144862125
TPM_E_DAA_TPM_SETTINGS: Final = -2144862124
TPM_E_DAA_STAGE: Final = -2144862123
TPM_E_DAA_ISSUER_VALIDITY: Final = -2144862122
TPM_E_DAA_WRONG_W: Final = -2144862121
TPM_E_BAD_HANDLE: Final = -2144862120
TPM_E_BAD_DELEGATE: Final = -2144862119
TPM_E_BADCONTEXT: Final = -2144862118
TPM_E_TOOMANYCONTEXTS: Final = -2144862117
TPM_E_MA_TICKET_SIGNATURE: Final = -2144862116
TPM_E_MA_DESTINATION: Final = -2144862115
TPM_E_MA_SOURCE: Final = -2144862114
TPM_E_MA_AUTHORITY: Final = -2144862113
TPM_E_PERMANENTEK: Final = -2144862111
TPM_E_BAD_SIGNATURE: Final = -2144862110
TPM_E_NOCONTEXTSPACE: Final = -2144862109
TPM_20_E_ASYMMETRIC: Final = -2144862079
TPM_20_E_ATTRIBUTES: Final = -2144862078
TPM_20_E_HASH: Final = -2144862077
TPM_20_E_VALUE: Final = -2144862076
TPM_20_E_HIERARCHY: Final = -2144862075
TPM_20_E_KEY_SIZE: Final = -2144862073
TPM_20_E_MGF: Final = -2144862072
TPM_20_E_MODE: Final = -2144862071
TPM_20_E_TYPE: Final = -2144862070
TPM_20_E_HANDLE: Final = -2144862069
TPM_20_E_KDF: Final = -2144862068
TPM_20_E_RANGE: Final = -2144862067
TPM_20_E_AUTH_FAIL: Final = -2144862066
TPM_20_E_NONCE: Final = -2144862065
TPM_20_E_PP: Final = -2144862064
TPM_20_E_SCHEME: Final = -2144862062
TPM_20_E_SIZE: Final = -2144862059
TPM_20_E_SYMMETRIC: Final = -2144862058
TPM_20_E_TAG: Final = -2144862057
TPM_20_E_SELECTOR: Final = -2144862056
TPM_20_E_INSUFFICIENT: Final = -2144862054
TPM_20_E_SIGNATURE: Final = -2144862053
TPM_20_E_KEY: Final = -2144862052
TPM_20_E_POLICY_FAIL: Final = -2144862051
TPM_20_E_INTEGRITY: Final = -2144862049
TPM_20_E_TICKET: Final = -2144862048
TPM_20_E_RESERVED_BITS: Final = -2144862047
TPM_20_E_BAD_AUTH: Final = -2144862046
TPM_20_E_EXPIRED: Final = -2144862045
TPM_20_E_POLICY_CC: Final = -2144862044
TPM_20_E_BINDING: Final = -2144862043
TPM_20_E_CURVE: Final = -2144862042
TPM_20_E_ECC_POINT: Final = -2144862041
TPM_20_E_INITIALIZE: Final = -2144861952
TPM_20_E_FAILURE: Final = -2144861951
TPM_20_E_SEQUENCE: Final = -2144861949
TPM_20_E_PRIVATE: Final = -2144861941
TPM_20_E_HMAC: Final = -2144861927
TPM_20_E_DISABLED: Final = -2144861920
TPM_20_E_EXCLUSIVE: Final = -2144861919
TPM_20_E_ECC_CURVE: Final = -2144861917
TPM_20_E_AUTH_TYPE: Final = -2144861916
TPM_20_E_AUTH_MISSING: Final = -2144861915
TPM_20_E_POLICY: Final = -2144861914
TPM_20_E_PCR: Final = -2144861913
TPM_20_E_PCR_CHANGED: Final = -2144861912
TPM_20_E_UPGRADE: Final = -2144861907
TPM_20_E_TOO_MANY_CONTEXTS: Final = -2144861906
TPM_20_E_AUTH_UNAVAILABLE: Final = -2144861905
TPM_20_E_REBOOT: Final = -2144861904
TPM_20_E_UNBALANCED: Final = -2144861903
TPM_20_E_COMMAND_SIZE: Final = -2144861886
TPM_20_E_COMMAND_CODE: Final = -2144861885
TPM_20_E_AUTHSIZE: Final = -2144861884
TPM_20_E_AUTH_CONTEXT: Final = -2144861883
TPM_20_E_NV_RANGE: Final = -2144861882
TPM_20_E_NV_SIZE: Final = -2144861881
TPM_20_E_NV_LOCKED: Final = -2144861880
TPM_20_E_NV_AUTHORIZATION: Final = -2144861879
TPM_20_E_NV_UNINITIALIZED: Final = -2144861878
TPM_20_E_NV_SPACE: Final = -2144861877
TPM_20_E_NV_DEFINED: Final = -2144861876
TPM_20_E_BAD_CONTEXT: Final = -2144861872
TPM_20_E_CPHASH: Final = -2144861871
TPM_20_E_PARENT: Final = -2144861870
TPM_20_E_NEEDS_TEST: Final = -2144861869
TPM_20_E_NO_RESULT: Final = -2144861868
TPM_20_E_SENSITIVE: Final = -2144861867
TPM_E_COMMAND_BLOCKED: Final = -2144861184
TPM_E_INVALID_HANDLE: Final = -2144861183
TPM_E_DUPLICATE_VHANDLE: Final = -2144861182
TPM_E_EMBEDDED_COMMAND_BLOCKED: Final = -2144861181
TPM_E_EMBEDDED_COMMAND_UNSUPPORTED: Final = -2144861180
TPM_E_RETRY: Final = -2144860160
TPM_E_NEEDS_SELFTEST: Final = -2144860159
TPM_E_DOING_SELFTEST: Final = -2144860158
TPM_E_DEFEND_LOCK_RUNNING: Final = -2144860157
TPM_20_E_CONTEXT_GAP: Final = -2144859903
TPM_20_E_OBJECT_MEMORY: Final = -2144859902
TPM_20_E_SESSION_MEMORY: Final = -2144859901
TPM_20_E_MEMORY: Final = -2144859900
TPM_20_E_SESSION_HANDLES: Final = -2144859899
TPM_20_E_OBJECT_HANDLES: Final = -2144859898
TPM_20_E_LOCALITY: Final = -2144859897
TPM_20_E_YIELDED: Final = -2144859896
TPM_20_E_CANCELED: Final = -2144859895
TPM_20_E_TESTING: Final = -2144859894
TPM_20_E_NV_RATE: Final = -2144859872
TPM_20_E_LOCKOUT: Final = -2144859871
TPM_20_E_RETRY: Final = -2144859870
TPM_20_E_NV_UNAVAILABLE: Final = -2144859869
TBS_E_INTERNAL_ERROR: Final = -2144845823
TBS_E_BAD_PARAMETER: Final = -2144845822
TBS_E_INVALID_OUTPUT_POINTER: Final = -2144845821
TBS_E_INVALID_CONTEXT: Final = -2144845820
TBS_E_INSUFFICIENT_BUFFER: Final = -2144845819
TBS_E_IOERROR: Final = -2144845818
TBS_E_INVALID_CONTEXT_PARAM: Final = -2144845817
TBS_E_SERVICE_NOT_RUNNING: Final = -2144845816
TBS_E_TOO_MANY_TBS_CONTEXTS: Final = -2144845815
TBS_E_TOO_MANY_RESOURCES: Final = -2144845814
TBS_E_SERVICE_START_PENDING: Final = -2144845813
TBS_E_PPI_NOT_SUPPORTED: Final = -2144845812
TBS_E_COMMAND_CANCELED: Final = -2144845811
TBS_E_BUFFER_TOO_LARGE: Final = -2144845810
TBS_E_TPM_NOT_FOUND: Final = -2144845809
TBS_E_SERVICE_DISABLED: Final = -2144845808
TBS_E_NO_EVENT_LOG: Final = -2144845807
TBS_E_ACCESS_DENIED: Final = -2144845806
TBS_E_PROVISIONING_NOT_ALLOWED: Final = -2144845805
TBS_E_PPI_FUNCTION_UNSUPPORTED: Final = -2144845804
TBS_E_OWNERAUTH_NOT_FOUND: Final = -2144845803
TBS_E_PROVISIONING_INCOMPLETE: Final = -2144845802
TPMAPI_E_INVALID_STATE: Final = -2144796416
TPMAPI_E_NOT_ENOUGH_DATA: Final = -2144796415
TPMAPI_E_TOO_MUCH_DATA: Final = -2144796414
TPMAPI_E_INVALID_OUTPUT_POINTER: Final = -2144796413
TPMAPI_E_INVALID_PARAMETER: Final = -2144796412
TPMAPI_E_OUT_OF_MEMORY: Final = -2144796411
TPMAPI_E_BUFFER_TOO_SMALL: Final = -2144796410
TPMAPI_E_INTERNAL_ERROR: Final = -2144796409
TPMAPI_E_ACCESS_DENIED: Final = -2144796408
TPMAPI_E_AUTHORIZATION_FAILED: Final = -2144796407
TPMAPI_E_INVALID_CONTEXT_HANDLE: Final = -2144796406
TPMAPI_E_TBS_COMMUNICATION_ERROR: Final = -2144796405
TPMAPI_E_TPM_COMMAND_ERROR: Final = -2144796404
TPMAPI_E_MESSAGE_TOO_LARGE: Final = -2144796403
TPMAPI_E_INVALID_ENCODING: Final = -2144796402
TPMAPI_E_INVALID_KEY_SIZE: Final = -2144796401
TPMAPI_E_ENCRYPTION_FAILED: Final = -2144796400
TPMAPI_E_INVALID_KEY_PARAMS: Final = -2144796399
TPMAPI_E_INVALID_MIGRATION_AUTHORIZATION_BLOB: Final = -2144796398
TPMAPI_E_INVALID_PCR_INDEX: Final = -2144796397
TPMAPI_E_INVALID_DELEGATE_BLOB: Final = -2144796396
TPMAPI_E_INVALID_CONTEXT_PARAMS: Final = -2144796395
TPMAPI_E_INVALID_KEY_BLOB: Final = -2144796394
TPMAPI_E_INVALID_PCR_DATA: Final = -2144796393
TPMAPI_E_INVALID_OWNER_AUTH: Final = -2144796392
TPMAPI_E_FIPS_RNG_CHECK_FAILED: Final = -2144796391
TPMAPI_E_EMPTY_TCG_LOG: Final = -2144796390
TPMAPI_E_INVALID_TCG_LOG_ENTRY: Final = -2144796389
TPMAPI_E_TCG_SEPARATOR_ABSENT: Final = -2144796388
TPMAPI_E_TCG_INVALID_DIGEST_ENTRY: Final = -2144796387
TPMAPI_E_POLICY_DENIES_OPERATION: Final = -2144796386
TPMAPI_E_NV_BITS_NOT_DEFINED: Final = -2144796385
TPMAPI_E_NV_BITS_NOT_READY: Final = -2144796384
TPMAPI_E_SEALING_KEY_NOT_AVAILABLE: Final = -2144796383
TPMAPI_E_NO_AUTHORIZATION_CHAIN_FOUND: Final = -2144796382
TPMAPI_E_SVN_COUNTER_NOT_AVAILABLE: Final = -2144796381
TPMAPI_E_OWNER_AUTH_NOT_NULL: Final = -2144796380
TPMAPI_E_ENDORSEMENT_AUTH_NOT_NULL: Final = -2144796379
TPMAPI_E_AUTHORIZATION_REVOKED: Final = -2144796378
TPMAPI_E_MALFORMED_AUTHORIZATION_KEY: Final = -2144796377
TPMAPI_E_AUTHORIZING_KEY_NOT_SUPPORTED: Final = -2144796376
TPMAPI_E_INVALID_AUTHORIZATION_SIGNATURE: Final = -2144796375
TPMAPI_E_MALFORMED_AUTHORIZATION_POLICY: Final = -2144796374
TPMAPI_E_MALFORMED_AUTHORIZATION_OTHER: Final = -2144796373
TPMAPI_E_SEALING_KEY_CHANGED: Final = -2144796372
TPMAPI_E_INVALID_TPM_VERSION: Final = -2144796371
TPMAPI_E_INVALID_POLICYAUTH_BLOB_TYPE: Final = -2144796370
TBSIMP_E_BUFFER_TOO_SMALL: Final = -2144796160
TBSIMP_E_CLEANUP_FAILED: Final = -2144796159
TBSIMP_E_INVALID_CONTEXT_HANDLE: Final = -2144796158
TBSIMP_E_INVALID_CONTEXT_PARAM: Final = -2144796157
TBSIMP_E_TPM_ERROR: Final = -2144796156
TBSIMP_E_HASH_BAD_KEY: Final = -2144796155
TBSIMP_E_DUPLICATE_VHANDLE: Final = -2144796154
TBSIMP_E_INVALID_OUTPUT_POINTER: Final = -2144796153
TBSIMP_E_INVALID_PARAMETER: Final = -2144796152
TBSIMP_E_RPC_INIT_FAILED: Final = -2144796151
TBSIMP_E_SCHEDULER_NOT_RUNNING: Final = -2144796150
TBSIMP_E_COMMAND_CANCELED: Final = -2144796149
TBSIMP_E_OUT_OF_MEMORY: Final = -2144796148
TBSIMP_E_LIST_NO_MORE_ITEMS: Final = -2144796147
TBSIMP_E_LIST_NOT_FOUND: Final = -2144796146
TBSIMP_E_NOT_ENOUGH_SPACE: Final = -2144796145
TBSIMP_E_NOT_ENOUGH_TPM_CONTEXTS: Final = -2144796144
TBSIMP_E_COMMAND_FAILED: Final = -2144796143
TBSIMP_E_UNKNOWN_ORDINAL: Final = -2144796142
TBSIMP_E_RESOURCE_EXPIRED: Final = -2144796141
TBSIMP_E_INVALID_RESOURCE: Final = -2144796140
TBSIMP_E_NOTHING_TO_UNLOAD: Final = -2144796139
TBSIMP_E_HASH_TABLE_FULL: Final = -2144796138
TBSIMP_E_TOO_MANY_TBS_CONTEXTS: Final = -2144796137
TBSIMP_E_TOO_MANY_RESOURCES: Final = -2144796136
TBSIMP_E_PPI_NOT_SUPPORTED: Final = -2144796135
TBSIMP_E_TPM_INCOMPATIBLE: Final = -2144796134
TBSIMP_E_NO_EVENT_LOG: Final = -2144796133
TPM_E_PPI_ACPI_FAILURE: Final = -2144795904
TPM_E_PPI_USER_ABORT: Final = -2144795903
TPM_E_PPI_BIOS_FAILURE: Final = -2144795902
TPM_E_PPI_NOT_SUPPORTED: Final = -2144795901
TPM_E_PPI_BLOCKED_IN_BIOS: Final = -2144795900
TPM_E_PCP_ERROR_MASK: Final = -2144795648
TPM_E_PCP_DEVICE_NOT_READY: Final = -2144795647
TPM_E_PCP_INVALID_HANDLE: Final = -2144795646
TPM_E_PCP_INVALID_PARAMETER: Final = -2144795645
TPM_E_PCP_FLAG_NOT_SUPPORTED: Final = -2144795644
TPM_E_PCP_NOT_SUPPORTED: Final = -2144795643
TPM_E_PCP_BUFFER_TOO_SMALL: Final = -2144795642
TPM_E_PCP_INTERNAL_ERROR: Final = -2144795641
TPM_E_PCP_AUTHENTICATION_FAILED: Final = -2144795640
TPM_E_PCP_AUTHENTICATION_IGNORED: Final = -2144795639
TPM_E_PCP_POLICY_NOT_FOUND: Final = -2144795638
TPM_E_PCP_PROFILE_NOT_FOUND: Final = -2144795637
TPM_E_PCP_VALIDATION_FAILED: Final = -2144795636
TPM_E_PCP_WRONG_PARENT: Final = -2144795634
TPM_E_KEY_NOT_LOADED: Final = -2144795633
TPM_E_NO_KEY_CERTIFICATION: Final = -2144795632
TPM_E_KEY_NOT_FINALIZED: Final = -2144795631
TPM_E_ATTESTATION_CHALLENGE_NOT_SET: Final = -2144795630
TPM_E_NOT_PCR_BOUND: Final = -2144795629
TPM_E_KEY_ALREADY_FINALIZED: Final = -2144795628
TPM_E_KEY_USAGE_POLICY_NOT_SUPPORTED: Final = -2144795627
TPM_E_KEY_USAGE_POLICY_INVALID: Final = -2144795626
TPM_E_SOFT_KEY_ERROR: Final = -2144795625
TPM_E_KEY_NOT_AUTHENTICATED: Final = -2144795624
TPM_E_PCP_KEY_NOT_AIK: Final = -2144795623
TPM_E_KEY_NOT_SIGNING_KEY: Final = -2144795622
TPM_E_LOCKED_OUT: Final = -2144795621
TPM_E_CLAIM_TYPE_NOT_SUPPORTED: Final = -2144795620
TPM_E_VERSION_NOT_SUPPORTED: Final = -2144795619
TPM_E_BUFFER_LENGTH_MISMATCH: Final = -2144795618
TPM_E_PCP_IFX_RSA_KEY_CREATION_BLOCKED: Final = -2144795617
TPM_E_PCP_TICKET_MISSING: Final = -2144795616
TPM_E_PCP_RAW_POLICY_NOT_SUPPORTED: Final = -2144795615
TPM_E_PCP_KEY_HANDLE_INVALIDATED: Final = -2144795614
TPM_E_PCP_UNSUPPORTED_PSS_SALT: Final = 0x40290423
TPM_E_PCP_PLATFORM_CLAIM_MAY_BE_OUTDATED: Final = 0x40290424
TPM_E_PCP_PLATFORM_CLAIM_OUTDATED: Final = 0x40290425
TPM_E_PCP_PLATFORM_CLAIM_REBOOT: Final = 0x40290426
TPM_E_ZERO_EXHAUST_ENABLED: Final = -2144795392
TPM_E_PROVISIONING_INCOMPLETE: Final = -2144795136
TPM_E_INVALID_OWNER_AUTH: Final = -2144795135
TPM_E_TOO_MUCH_DATA: Final = -2144795134
TPM_E_TPM_GENERATED_EPS: Final = -2144795133
PLA_E_DCS_NOT_FOUND: Final = -2144337918
PLA_E_DCS_IN_USE: Final = -2144337750
PLA_E_TOO_MANY_FOLDERS: Final = -2144337851
PLA_E_NO_MIN_DISK: Final = -2144337808
PLA_E_DCS_ALREADY_EXISTS: Final = -2144337737
PLA_S_PROPERTY_IGNORED: Final = 0x00300100
PLA_E_PROPERTY_CONFLICT: Final = -2144337663
PLA_E_DCS_SINGLETON_REQUIRED: Final = -2144337662
PLA_E_CREDENTIALS_REQUIRED: Final = -2144337661
PLA_E_DCS_NOT_RUNNING: Final = -2144337660
PLA_E_CONFLICT_INCL_EXCL_API: Final = -2144337659
PLA_E_NETWORK_EXE_NOT_VALID: Final = -2144337658
PLA_E_EXE_ALREADY_CONFIGURED: Final = -2144337657
PLA_E_EXE_PATH_NOT_VALID: Final = -2144337656
PLA_E_DC_ALREADY_EXISTS: Final = -2144337655
PLA_E_DCS_START_WAIT_TIMEOUT: Final = -2144337654
PLA_E_DC_START_WAIT_TIMEOUT: Final = -2144337653
PLA_E_REPORT_WAIT_TIMEOUT: Final = -2144337652
PLA_E_NO_DUPLICATES: Final = -2144337651
PLA_E_EXE_FULL_PATH_REQUIRED: Final = -2144337650
PLA_E_INVALID_SESSION_NAME: Final = -2144337649
PLA_E_PLA_CHANNEL_NOT_ENABLED: Final = -2144337648
PLA_E_TASKSCHED_CHANNEL_NOT_ENABLED: Final = -2144337647
PLA_E_RULES_MANAGER_FAILED: Final = -2144337646
PLA_E_CABAPI_FAILURE: Final = -2144337645
FVE_E_LOCKED_VOLUME: Final = -2144272384
FVE_E_NOT_ENCRYPTED: Final = -2144272383
FVE_E_NO_TPM_BIOS: Final = -2144272382
FVE_E_NO_MBR_METRIC: Final = -2144272381
FVE_E_NO_BOOTSECTOR_METRIC: Final = -2144272380
FVE_E_NO_BOOTMGR_METRIC: Final = -2144272379
FVE_E_WRONG_BOOTMGR: Final = -2144272378
FVE_E_SECURE_KEY_REQUIRED: Final = -2144272377
FVE_E_NOT_ACTIVATED: Final = -2144272376
FVE_E_ACTION_NOT_ALLOWED: Final = -2144272375
FVE_E_AD_SCHEMA_NOT_INSTALLED: Final = -2144272374
FVE_E_AD_INVALID_DATATYPE: Final = -2144272373
FVE_E_AD_INVALID_DATASIZE: Final = -2144272372
FVE_E_AD_NO_VALUES: Final = -2144272371
FVE_E_AD_ATTR_NOT_SET: Final = -2144272370
FVE_E_AD_GUID_NOT_FOUND: Final = -2144272369
FVE_E_BAD_INFORMATION: Final = -2144272368
FVE_E_TOO_SMALL: Final = -2144272367
FVE_E_SYSTEM_VOLUME: Final = -2144272366
FVE_E_FAILED_WRONG_FS: Final = -2144272365
FVE_E_BAD_PARTITION_SIZE: Final = -2144272364
FVE_E_NOT_SUPPORTED: Final = -2144272363
FVE_E_BAD_DATA: Final = -2144272362
FVE_E_VOLUME_NOT_BOUND: Final = -2144272361
FVE_E_TPM_NOT_OWNED: Final = -2144272360
FVE_E_NOT_DATA_VOLUME: Final = -2144272359
FVE_E_AD_INSUFFICIENT_BUFFER: Final = -2144272358
FVE_E_CONV_READ: Final = -2144272357
FVE_E_CONV_WRITE: Final = -2144272356
FVE_E_KEY_REQUIRED: Final = -2144272355
FVE_E_CLUSTERING_NOT_SUPPORTED: Final = -2144272354
FVE_E_VOLUME_BOUND_ALREADY: Final = -2144272353
FVE_E_OS_NOT_PROTECTED: Final = -2144272352
FVE_E_PROTECTION_DISABLED: Final = -2144272351
FVE_E_RECOVERY_KEY_REQUIRED: Final = -2144272350
FVE_E_FOREIGN_VOLUME: Final = -2144272349
FVE_E_OVERLAPPED_UPDATE: Final = -2144272348
FVE_E_TPM_SRK_AUTH_NOT_ZERO: Final = -2144272347
FVE_E_FAILED_SECTOR_SIZE: Final = -2144272346
FVE_E_FAILED_AUTHENTICATION: Final = -2144272345
FVE_E_NOT_OS_VOLUME: Final = -2144272344
FVE_E_AUTOUNLOCK_ENABLED: Final = -2144272343
FVE_E_WRONG_BOOTSECTOR: Final = -2144272342
FVE_E_WRONG_SYSTEM_FS: Final = -2144272341
FVE_E_POLICY_PASSWORD_REQUIRED: Final = -2144272340
FVE_E_CANNOT_SET_FVEK_ENCRYPTED: Final = -2144272339
FVE_E_CANNOT_ENCRYPT_NO_KEY: Final = -2144272338
FVE_E_BOOTABLE_CDDVD: Final = -2144272336
FVE_E_PROTECTOR_EXISTS: Final = -2144272335
FVE_E_RELATIVE_PATH: Final = -2144272334
FVE_E_PROTECTOR_NOT_FOUND: Final = -2144272333
FVE_E_INVALID_KEY_FORMAT: Final = -2144272332
FVE_E_INVALID_PASSWORD_FORMAT: Final = -2144272331
FVE_E_FIPS_RNG_CHECK_FAILED: Final = -2144272330
FVE_E_FIPS_PREVENTS_RECOVERY_PASSWORD: Final = -2144272329
FVE_E_FIPS_PREVENTS_EXTERNAL_KEY_EXPORT: Final = -2144272328
FVE_E_NOT_DECRYPTED: Final = -2144272327
FVE_E_INVALID_PROTECTOR_TYPE: Final = -2144272326
FVE_E_NO_PROTECTORS_TO_TEST: Final = -2144272325
FVE_E_KEYFILE_NOT_FOUND: Final = -2144272324
FVE_E_KEYFILE_INVALID: Final = -2144272323
FVE_E_KEYFILE_NO_VMK: Final = -**********
FVE_E_TPM_DISABLED: Final = -**********
FVE_E_NOT_ALLOWED_IN_SAFE_MODE: Final = -**********
FVE_E_TPM_INVALID_PCR: Final = -**********
FVE_E_TPM_NO_VMK: Final = -**********
FVE_E_PIN_INVALID: Final = -**********
FVE_E_AUTH_INVALID_APPLICATION: Final = -**********
FVE_E_AUTH_INVALID_CONFIG: Final = -**********
FVE_E_FIPS_DISABLE_PROTECTION_NOT_ALLOWED: Final = -**********
FVE_E_FS_NOT_EXTENDED: Final = -**********
FVE_E_FIRMWARE_TYPE_NOT_SUPPORTED: Final = -**********
FVE_E_NO_LICENSE: Final = -**********
FVE_E_NOT_ON_STACK: Final = -**********
FVE_E_FS_MOUNTED: Final = -**********
FVE_E_TOKEN_NOT_IMPERSONATED: Final = -**********
FVE_E_DRY_RUN_FAILED: Final = -**********
FVE_E_REBOOT_REQUIRED: Final = -**********
FVE_E_DEBUGGER_ENABLED: Final = -**********
FVE_E_RAW_ACCESS: Final = -**********
FVE_E_RAW_BLOCKED: Final = -**********
FVE_E_BCD_APPLICATIONS_PATH_INCORRECT: Final = -**********
FVE_E_NOT_ALLOWED_IN_VERSION: Final = -**********
FVE_E_NO_AUTOUNLOCK_MASTER_KEY: Final = -**********
FVE_E_MOR_FAILED: Final = -**********
FVE_E_HIDDEN_VOLUME: Final = -**********
FVE_E_TRANSIENT_STATE: Final = -**********
FVE_E_PUBKEY_NOT_ALLOWED: Final = -**********
FVE_E_VOLUME_HANDLE_OPEN: Final = -**********
FVE_E_NO_FEATURE_LICENSE: Final = -**********
FVE_E_INVALID_STARTUP_OPTIONS: Final = -**********
FVE_E_POLICY_RECOVERY_PASSWORD_NOT_ALLOWED: Final = -**********
FVE_E_POLICY_RECOVERY_PASSWORD_REQUIRED: Final = -**********
FVE_E_POLICY_RECOVERY_KEY_NOT_ALLOWED: Final = -**********
FVE_E_POLICY_RECOVERY_KEY_REQUIRED: Final = -**********
FVE_E_POLICY_STARTUP_PIN_NOT_ALLOWED: Final = -**********
FVE_E_POLICY_STARTUP_PIN_REQUIRED: Final = -**********
FVE_E_POLICY_STARTUP_KEY_NOT_ALLOWED: Final = -**********
FVE_E_POLICY_STARTUP_KEY_REQUIRED: Final = -**********
FVE_E_POLICY_STARTUP_PIN_KEY_NOT_ALLOWED: Final = -**********
FVE_E_POLICY_STARTUP_PIN_KEY_REQUIRED: Final = -**********
FVE_E_POLICY_STARTUP_TPM_NOT_ALLOWED: Final = -**********
FVE_E_POLICY_STARTUP_TPM_REQUIRED: Final = -**********
FVE_E_POLICY_INVALID_PIN_LENGTH: Final = -**********
FVE_E_KEY_PROTECTOR_NOT_SUPPORTED: Final = -**********
FVE_E_POLICY_PASSPHRASE_NOT_ALLOWED: Final = -**********
FVE_E_POLICY_PASSPHRASE_REQUIRED: Final = -**********
FVE_E_FIPS_PREVENTS_PASSPHRASE: Final = -2144272276
FVE_E_OS_VOLUME_PASSPHRASE_NOT_ALLOWED: Final = -2144272275
FVE_E_INVALID_BITLOCKER_OID: Final = -2144272274
FVE_E_VOLUME_TOO_SMALL: Final = -2144272273
FVE_E_DV_NOT_SUPPORTED_ON_FS: Final = -2144272272
FVE_E_DV_NOT_ALLOWED_BY_GP: Final = -2144272271
FVE_E_POLICY_USER_CERTIFICATE_NOT_ALLOWED: Final = -2144272270
FVE_E_POLICY_USER_CERTIFICATE_REQUIRED: Final = -2144272269
FVE_E_POLICY_USER_CERT_MUST_BE_HW: Final = -2144272268
FVE_E_POLICY_USER_CONFIGURE_FDV_AUTOUNLOCK_NOT_ALLOWED: Final = -2144272267
FVE_E_POLICY_USER_CONFIGURE_RDV_AUTOUNLOCK_NOT_ALLOWED: Final = -2144272266
FVE_E_POLICY_USER_CONFIGURE_RDV_NOT_ALLOWED: Final = -2144272265
FVE_E_POLICY_USER_ENABLE_RDV_NOT_ALLOWED: Final = -2144272264
FVE_E_POLICY_USER_DISABLE_RDV_NOT_ALLOWED: Final = -2144272263
FVE_E_POLICY_INVALID_PASSPHRASE_LENGTH: Final = -2144272256
FVE_E_POLICY_PASSPHRASE_TOO_SIMPLE: Final = -2144272255
FVE_E_RECOVERY_PARTITION: Final = -2144272254
FVE_E_POLICY_CONFLICT_FDV_RK_OFF_AUK_ON: Final = -2144272253
FVE_E_POLICY_CONFLICT_RDV_RK_OFF_AUK_ON: Final = -2144272252
FVE_E_NON_BITLOCKER_OID: Final = -2144272251
FVE_E_POLICY_PROHIBITS_SELFSIGNED: Final = -2144272250
FVE_E_POLICY_CONFLICT_RO_AND_STARTUP_KEY_REQUIRED: Final = -2144272249
FVE_E_CONV_RECOVERY_FAILED: Final = -2144272248
FVE_E_VIRTUALIZED_SPACE_TOO_BIG: Final = -2144272247
FVE_E_POLICY_CONFLICT_OSV_RP_OFF_ADB_ON: Final = -2144272240
FVE_E_POLICY_CONFLICT_FDV_RP_OFF_ADB_ON: Final = -2144272239
FVE_E_POLICY_CONFLICT_RDV_RP_OFF_ADB_ON: Final = -2144272238
FVE_E_NON_BITLOCKER_KU: Final = -2144272237
FVE_E_PRIVATEKEY_AUTH_FAILED: Final = -2144272236
FVE_E_REMOVAL_OF_DRA_FAILED: Final = -2144272235
FVE_E_OPERATION_NOT_SUPPORTED_ON_VISTA_VOLUME: Final = -2144272234
FVE_E_CANT_LOCK_AUTOUNLOCK_ENABLED_VOLUME: Final = -2144272233
FVE_E_FIPS_HASH_KDF_NOT_ALLOWED: Final = -2144272232
FVE_E_ENH_PIN_INVALID: Final = -2144272231
FVE_E_INVALID_PIN_CHARS: Final = -2144272230
FVE_E_INVALID_DATUM_TYPE: Final = -2144272229
FVE_E_EFI_ONLY: Final = -2144272228
FVE_E_MULTIPLE_NKP_CERTS: Final = -2144272227
FVE_E_REMOVAL_OF_NKP_FAILED: Final = -2144272226
FVE_E_INVALID_NKP_CERT: Final = -2144272225
FVE_E_NO_EXISTING_PIN: Final = -2144272224
FVE_E_PROTECTOR_CHANGE_PIN_MISMATCH: Final = -2144272223
FVE_E_PIN_PROTECTOR_CHANGE_BY_STD_USER_DISALLOWED: Final = -2144272222
FVE_E_PROTECTOR_CHANGE_MAX_PIN_CHANGE_ATTEMPTS_REACHED: Final = -2144272221
FVE_E_POLICY_PASSPHRASE_REQUIRES_ASCII: Final = -2144272220
FVE_E_FULL_ENCRYPTION_NOT_ALLOWED_ON_TP_STORAGE: Final = -2144272219
FVE_E_WIPE_NOT_ALLOWED_ON_TP_STORAGE: Final = -2144272218
FVE_E_KEY_LENGTH_NOT_SUPPORTED_BY_EDRIVE: Final = -2144272217
FVE_E_NO_EXISTING_PASSPHRASE: Final = -2144272216
FVE_E_PROTECTOR_CHANGE_PASSPHRASE_MISMATCH: Final = -2144272215
FVE_E_PASSPHRASE_TOO_LONG: Final = -2144272214
FVE_E_NO_PASSPHRASE_WITH_TPM: Final = -2144272213
FVE_E_NO_TPM_WITH_PASSPHRASE: Final = -2144272212
FVE_E_NOT_ALLOWED_ON_CSV_STACK: Final = -2144272211
FVE_E_NOT_ALLOWED_ON_CLUSTER: Final = -2144272210
FVE_E_EDRIVE_NO_FAILOVER_TO_SW: Final = -2144272209
FVE_E_EDRIVE_BAND_IN_USE: Final = -2144272208
FVE_E_EDRIVE_DISALLOWED_BY_GP: Final = -2144272207
FVE_E_EDRIVE_INCOMPATIBLE_VOLUME: Final = -2144272206
FVE_E_NOT_ALLOWED_TO_UPGRADE_WHILE_CONVERTING: Final = -2144272205
FVE_E_EDRIVE_DV_NOT_SUPPORTED: Final = -2144272204
FVE_E_NO_PREBOOT_KEYBOARD_DETECTED: Final = -2144272203
FVE_E_NO_PREBOOT_KEYBOARD_OR_WINRE_DETECTED: Final = -2144272202
FVE_E_POLICY_REQUIRES_STARTUP_PIN_ON_TOUCH_DEVICE: Final = -2144272201
FVE_E_POLICY_REQUIRES_RECOVERY_PASSWORD_ON_TOUCH_DEVICE: Final = -2144272200
FVE_E_WIPE_CANCEL_NOT_APPLICABLE: Final = -**********
FVE_E_SECUREBOOT_DISABLED: Final = -**********
FVE_E_SECUREBOOT_CONFIGURATION_INVALID: Final = -**********
FVE_E_EDRIVE_DRY_RUN_FAILED: Final = -**********
FVE_E_SHADOW_COPY_PRESENT: Final = -**********
FVE_E_POLICY_INVALID_ENHANCED_BCD_SETTINGS: Final = -**********
FVE_E_EDRIVE_INCOMPATIBLE_FIRMWARE: Final = -**********
FVE_E_PROTECTOR_CHANGE_MAX_PASSPHRASE_CHANGE_ATTEMPTS_REACHED: Final = -**********
FVE_E_PASSPHRASE_PROTECTOR_CHANGE_BY_STD_USER_DISALLOWED: Final = -**********
FVE_E_LIVEID_ACCOUNT_SUSPENDED: Final = -**********
FVE_E_LIVEID_ACCOUNT_BLOCKED: Final = -**********
FVE_E_NOT_PROVISIONED_ON_ALL_VOLUMES: Final = -**********
FVE_E_DE_FIXED_DATA_NOT_SUPPORTED: Final = -**********
FVE_E_DE_HARDWARE_NOT_COMPLIANT: Final = -**********
FVE_E_DE_WINRE_NOT_CONFIGURED: Final = -**********
FVE_E_DE_PROTECTION_SUSPENDED: Final = -**********
FVE_E_DE_OS_VOLUME_NOT_PROTECTED: Final = -**********
FVE_E_DE_DEVICE_LOCKEDOUT: Final = -**********
FVE_E_DE_PROTECTION_NOT_YET_ENABLED: Final = -**********
FVE_E_INVALID_PIN_CHARS_DETAILED: Final = -**********
FVE_E_DEVICE_LOCKOUT_COUNTER_UNAVAILABLE: Final = -**********
FVE_E_DEVICELOCKOUT_COUNTER_MISMATCH: Final = -**********
FVE_E_BUFFER_TOO_LARGE: Final = -**********
FVE_E_NO_SUCH_CAPABILITY_ON_TARGET: Final = -**********
FVE_E_DE_PREVENTED_FOR_OS: Final = -**********
FVE_E_DE_VOLUME_OPTED_OUT: Final = -**********
FVE_E_DE_VOLUME_NOT_SUPPORTED: Final = -**********
FVE_E_EOW_NOT_SUPPORTED_IN_VERSION: Final = -**********
FVE_E_ADBACKUP_NOT_ENABLED: Final = -**********
FVE_E_VOLUME_EXTEND_PREVENTS_EOW_DECRYPT: Final = -2144272170
FVE_E_NOT_DE_VOLUME: Final = -2144272169
FVE_E_PROTECTION_CANNOT_BE_DISABLED: Final = -2144272168
FVE_E_OSV_KSR_NOT_ALLOWED: Final = -2144272167
FVE_E_AD_BACKUP_REQUIRED_POLICY_NOT_SET_OS_DRIVE: Final = -2144272166
FVE_E_AD_BACKUP_REQUIRED_POLICY_NOT_SET_FIXED_DRIVE: Final = -2144272165
FVE_E_AD_BACKUP_REQUIRED_POLICY_NOT_SET_REMOVABLE_DRIVE: Final = -2144272164
FVE_E_KEY_ROTATION_NOT_SUPPORTED: Final = -2144272163
FVE_E_EXECUTE_REQUEST_SENT_TOO_SOON: Final = -2144272162
FVE_E_KEY_ROTATION_NOT_ENABLED: Final = -2144272161
FVE_E_DEVICE_NOT_JOINED: Final = -2144272160
FVE_E_AAD_ENDPOINT_BUSY: Final = -2144272159
FVE_E_INVALID_NBP_CERT: Final = -2144272158
FVE_E_EDRIVE_BAND_ENUMERATION_FAILED: Final = -2144272157
FVE_E_POLICY_ON_RDV_EXCLUSION_LIST: Final = -2144272156
FVE_E_PREDICTED_TPM_PROTECTOR_NOT_SUPPORTED: Final = -2144272155
FVE_E_SETUP_TPM_CALLBACK_NOT_SUPPORTED: Final = -**********
FVE_E_TPM_CONTEXT_SETUP_NOT_SUPPORTED: Final = -**********
FVE_E_UPDATE_INVALID_CONFIG: Final = -**********
FVE_E_AAD_SERVER_FAIL_RETRY_AFTER: Final = -**********
FVE_E_AAD_SERVER_FAIL_BACKOFF: Final = -**********
FVE_E_DATASET_FULL: Final = -**********
FVE_E_METADATA_FULL: Final = -**********
FWP_E_CALLOUT_NOT_FOUND: Final = -**********
FWP_E_CONDITION_NOT_FOUND: Final = -**********
FWP_E_FILTER_NOT_FOUND: Final = -**********
FWP_E_LAYER_NOT_FOUND: Final = -**********
FWP_E_PROVIDER_NOT_FOUND: Final = -**********
FWP_E_PROVIDER_CONTEXT_NOT_FOUND: Final = -**********
FWP_E_SUBLAYER_NOT_FOUND: Final = -**********
FWP_E_NOT_FOUND: Final = -**********
FWP_E_ALREADY_EXISTS: Final = -**********
FWP_E_IN_USE: Final = -**********
FWP_E_DYNAMIC_SESSION_IN_PROGRESS: Final = -**********
FWP_E_WRONG_SESSION: Final = -**********
FWP_E_NO_TXN_IN_PROGRESS: Final = -**********
FWP_E_TXN_IN_PROGRESS: Final = -**********
FWP_E_TXN_ABORTED: Final = -**********
FWP_E_SESSION_ABORTED: Final = -**********
FWP_E_INCOMPATIBLE_TXN: Final = -**********
FWP_E_TIMEOUT: Final = -**********
FWP_E_NET_EVENTS_DISABLED: Final = -**********
FWP_E_INCOMPATIBLE_LAYER: Final = -**********
FWP_E_KM_CLIENTS_ONLY: Final = -**********
FWP_E_LIFETIME_MISMATCH: Final = -**********
FWP_E_BUILTIN_OBJECT: Final = -**********
FWP_E_TOO_MANY_CALLOUTS: Final = -**********
FWP_E_NOTIFICATION_DROPPED: Final = -**********
FWP_E_TRAFFIC_MISMATCH: Final = -**********
FWP_E_INCOMPATIBLE_SA_STATE: Final = -**********
FWP_E_NULL_POINTER: Final = -2144206820
FWP_E_INVALID_ENUMERATOR: Final = -2144206819
FWP_E_INVALID_FLAGS: Final = -2144206818
FWP_E_INVALID_NET_MASK: Final = -2144206817
FWP_E_INVALID_RANGE: Final = -2144206816
FWP_E_INVALID_INTERVAL: Final = -2144206815
FWP_E_ZERO_LENGTH_ARRAY: Final = -2144206814
FWP_E_NULL_DISPLAY_NAME: Final = -2144206813
FWP_E_INVALID_ACTION_TYPE: Final = -2144206812
FWP_E_INVALID_WEIGHT: Final = -2144206811
FWP_E_MATCH_TYPE_MISMATCH: Final = -2144206810
FWP_E_TYPE_MISMATCH: Final = -2144206809
FWP_E_OUT_OF_BOUNDS: Final = -2144206808
FWP_E_RESERVED: Final = -2144206807
FWP_E_DUPLICATE_CONDITION: Final = -**********
FWP_E_DUPLICATE_KEYMOD: Final = -**********
FWP_E_ACTION_INCOMPATIBLE_WITH_LAYER: Final = -**********
FWP_E_ACTION_INCOMPATIBLE_WITH_SUBLAYER: Final = -**********
FWP_E_CONTEXT_INCOMPATIBLE_WITH_LAYER: Final = -**********
FWP_E_CONTEXT_INCOMPATIBLE_WITH_CALLOUT: Final = -**********
FWP_E_INCOMPATIBLE_AUTH_METHOD: Final = -**********
FWP_E_INCOMPATIBLE_DH_GROUP: Final = -**********
FWP_E_EM_NOT_SUPPORTED: Final = -**********
FWP_E_NEVER_MATCH: Final = -**********
FWP_E_PROVIDER_CONTEXT_MISMATCH: Final = -**********
FWP_E_INVALID_PARAMETER: Final = -**********
FWP_E_TOO_MANY_SUBLAYERS: Final = -**********
FWP_E_CALLOUT_NOTIFICATION_FAILED: Final = -**********
FWP_E_INVALID_AUTH_TRANSFORM: Final = -**********
FWP_E_INVALID_CIPHER_TRANSFORM: Final = -**********
FWP_E_INCOMPATIBLE_CIPHER_TRANSFORM: Final = -**********
FWP_E_INVALID_TRANSFORM_COMBINATION: Final = -**********
FWP_E_DUPLICATE_AUTH_METHOD: Final = -**********
FWP_E_INVALID_TUNNEL_ENDPOINT: Final = -**********
FWP_E_L2_DRIVER_NOT_READY: Final = -**********
FWP_E_KEY_DICTATOR_ALREADY_REGISTERED: Final = -**********
FWP_E_KEY_DICTATION_INVALID_KEYING_MATERIAL: Final = -**********
FWP_E_CONNECTIONS_DISABLED: Final = -**********
FWP_E_INVALID_DNS_NAME: Final = -**********
FWP_E_STILL_ON: Final = -**********
FWP_E_IKEEXT_NOT_RUNNING: Final = -**********
FWP_E_DROP_NOICMP: Final = -**********
WS_S_ASYNC: Final = 0x003D0000
WS_S_END: Final = 0x003D0001
WS_E_INVALID_FORMAT: Final = -**********
WS_E_OBJECT_FAULTED: Final = -2143485951
WS_E_NUMERIC_OVERFLOW: Final = -2143485950
WS_E_INVALID_OPERATION: Final = -2143485949
WS_E_OPERATION_ABORTED: Final = -2143485948
WS_E_ENDPOINT_ACCESS_DENIED: Final = -2143485947
WS_E_OPERATION_TIMED_OUT: Final = -2143485946
WS_E_OPERATION_ABANDONED: Final = -2143485945
WS_E_QUOTA_EXCEEDED: Final = -2143485944
WS_E_NO_TRANSLATION_AVAILABLE: Final = -2143485943
WS_E_SECURITY_VERIFICATION_FAILURE: Final = -2143485942
WS_E_ADDRESS_IN_USE: Final = -2143485941
WS_E_ADDRESS_NOT_AVAILABLE: Final = -2143485940
WS_E_ENDPOINT_NOT_FOUND: Final = -2143485939
WS_E_ENDPOINT_NOT_AVAILABLE: Final = -2143485938
WS_E_ENDPOINT_FAILURE: Final = -2143485937
WS_E_ENDPOINT_UNREACHABLE: Final = -2143485936
WS_E_ENDPOINT_ACTION_NOT_SUPPORTED: Final = -2143485935
WS_E_ENDPOINT_TOO_BUSY: Final = -2143485934
WS_E_ENDPOINT_FAULT_RECEIVED: Final = -2143485933
WS_E_ENDPOINT_DISCONNECTED: Final = -2143485932
WS_E_PROXY_FAILURE: Final = -2143485931
WS_E_PROXY_ACCESS_DENIED: Final = -2143485930
WS_E_NOT_SUPPORTED: Final = -2143485929
WS_E_PROXY_REQUIRES_BASIC_AUTH: Final = -2143485928
WS_E_PROXY_REQUIRES_DIGEST_AUTH: Final = -2143485927
WS_E_PROXY_REQUIRES_NTLM_AUTH: Final = -2143485926
WS_E_PROXY_REQUIRES_NEGOTIATE_AUTH: Final = -2143485925
WS_E_SERVER_REQUIRES_BASIC_AUTH: Final = -2143485924
WS_E_SERVER_REQUIRES_DIGEST_AUTH: Final = -2143485923
WS_E_SERVER_REQUIRES_NTLM_AUTH: Final = -2143485922
WS_E_SERVER_REQUIRES_NEGOTIATE_AUTH: Final = -2143485921
WS_E_INVALID_ENDPOINT_URL: Final = -2143485920
WS_E_OTHER: Final = -2143485919
WS_E_SECURITY_TOKEN_EXPIRED: Final = -2143485918
WS_E_SECURITY_SYSTEM_FAILURE: Final = -2143485917

ERROR_NDIS_INTERFACE_CLOSING: Final = -2144075774
ERROR_NDIS_BAD_VERSION: Final = -2144075772
ERROR_NDIS_BAD_CHARACTERISTICS: Final = -2144075771
ERROR_NDIS_ADAPTER_NOT_FOUND: Final = -2144075770
ERROR_NDIS_OPEN_FAILED: Final = -2144075769
ERROR_NDIS_DEVICE_FAILED: Final = -2144075768
ERROR_NDIS_MULTICAST_FULL: Final = -2144075767
ERROR_NDIS_MULTICAST_EXISTS: Final = -2144075766
ERROR_NDIS_MULTICAST_NOT_FOUND: Final = -2144075765
ERROR_NDIS_REQUEST_ABORTED: Final = -2144075764
ERROR_NDIS_RESET_IN_PROGRESS: Final = -2144075763
ERROR_NDIS_NOT_SUPPORTED: Final = -2144075589
ERROR_NDIS_INVALID_PACKET: Final = -2144075761
ERROR_NDIS_ADAPTER_NOT_READY: Final = -2144075759
ERROR_NDIS_INVALID_LENGTH: Final = -2144075756
ERROR_NDIS_INVALID_DATA: Final = -2144075755
ERROR_NDIS_BUFFER_TOO_SHORT: Final = -2144075754
ERROR_NDIS_INVALID_OID: Final = -2144075753
ERROR_NDIS_ADAPTER_REMOVED: Final = -2144075752
ERROR_NDIS_UNSUPPORTED_MEDIA: Final = -2144075751
ERROR_NDIS_GROUP_ADDRESS_IN_USE: Final = -2144075750
ERROR_NDIS_FILE_NOT_FOUND: Final = -2144075749
ERROR_NDIS_ERROR_READING_FILE: Final = -2144075748
ERROR_NDIS_ALREADY_MAPPED: Final = -2144075747
ERROR_NDIS_RESOURCE_CONFLICT: Final = -2144075746
ERROR_NDIS_MEDIA_DISCONNECTED: Final = -2144075745
ERROR_NDIS_INVALID_ADDRESS: Final = -2144075742
ERROR_NDIS_INVALID_DEVICE_REQUEST: Final = -2144075760
ERROR_NDIS_PAUSED: Final = -2144075734
ERROR_NDIS_INTERFACE_NOT_FOUND: Final = -2144075733
ERROR_NDIS_UNSUPPORTED_REVISION: Final = -2144075732
ERROR_NDIS_INVALID_PORT: Final = -2144075731
ERROR_NDIS_INVALID_PORT_STATE: Final = -2144075730
ERROR_NDIS_LOW_POWER_STATE: Final = -2144075729
ERROR_NDIS_REINIT_REQUIRED: Final = -2144075728
ERROR_NDIS_NO_QUEUES: Final = -2144075727
ERROR_NDIS_DOT11_AUTO_CONFIG_ENABLED: Final = -2144067584
ERROR_NDIS_DOT11_MEDIA_IN_USE: Final = -2144067583
ERROR_NDIS_DOT11_POWER_STATE_INVALID: Final = -2144067582
ERROR_NDIS_PM_WOL_PATTERN_LIST_FULL: Final = -2144067581
ERROR_NDIS_PM_PROTOCOL_OFFLOAD_LIST_FULL: Final = -2144067580
ERROR_NDIS_DOT11_AP_CHANNEL_CURRENTLY_NOT_AVAILABLE: Final = -2144067579
ERROR_NDIS_DOT11_AP_BAND_CURRENTLY_NOT_AVAILABLE: Final = -2144067578
ERROR_NDIS_DOT11_AP_CHANNEL_NOT_ALLOWED: Final = -2144067577
ERROR_NDIS_DOT11_AP_BAND_NOT_ALLOWED: Final = -2144067576
ERROR_NDIS_INDICATION_REQUIRED: Final = 0x00340001
ERROR_NDIS_OFFLOAD_POLICY: Final = -1070329841
ERROR_NDIS_OFFLOAD_CONNECTION_REJECTED: Final = -1070329838
ERROR_NDIS_OFFLOAD_PATH_REJECTED: Final = -1070329837
ERROR_HV_INVALID_HYPERCALL_CODE: Final = -1070268414
ERROR_HV_INVALID_HYPERCALL_INPUT: Final = -1070268413
ERROR_HV_INVALID_ALIGNMENT: Final = -1070268412
ERROR_HV_INVALID_PARAMETER: Final = -1070268411
ERROR_HV_ACCESS_DENIED: Final = -1070268410
ERROR_HV_INVALID_PARTITION_STATE: Final = -1070268409
ERROR_HV_OPERATION_DENIED: Final = -1070268408
ERROR_HV_UNKNOWN_PROPERTY: Final = -1070268407
ERROR_HV_PROPERTY_VALUE_OUT_OF_RANGE: Final = -1070268406
ERROR_HV_INSUFFICIENT_MEMORY: Final = -1070268405
ERROR_HV_PARTITION_TOO_DEEP: Final = -1070268404
ERROR_HV_INVALID_PARTITION_ID: Final = -1070268403
ERROR_HV_INVALID_VP_INDEX: Final = -1070268402
ERROR_HV_INVALID_PORT_ID: Final = -1070268399
ERROR_HV_INVALID_CONNECTION_ID: Final = -1070268398
ERROR_HV_INSUFFICIENT_BUFFERS: Final = -1070268397
ERROR_HV_NOT_ACKNOWLEDGED: Final = -1070268396
ERROR_HV_INVALID_VP_STATE: Final = -1070268395
ERROR_HV_ACKNOWLEDGED: Final = -1070268394
ERROR_HV_INVALID_SAVE_RESTORE_STATE: Final = -1070268393
ERROR_HV_INVALID_SYNIC_STATE: Final = -1070268392
ERROR_HV_OBJECT_IN_USE: Final = -1070268391
ERROR_HV_INVALID_PROXIMITY_DOMAIN_INFO: Final = -1070268390
ERROR_HV_NO_DATA: Final = -1070268389
ERROR_HV_INACTIVE: Final = -1070268388
ERROR_HV_NO_RESOURCES: Final = -1070268387
ERROR_HV_FEATURE_UNAVAILABLE: Final = -1070268386
ERROR_HV_INSUFFICIENT_BUFFER: Final = -1070268365
ERROR_HV_INSUFFICIENT_DEVICE_DOMAINS: Final = -1070268360
ERROR_HV_CPUID_FEATURE_VALIDATION: Final = -1070268356
ERROR_HV_CPUID_XSAVE_FEATURE_VALIDATION: Final = -1070268355
ERROR_HV_PROCESSOR_STARTUP_TIMEOUT: Final = -1070268354
ERROR_HV_SMX_ENABLED: Final = -1070268353
ERROR_HV_INVALID_LP_INDEX: Final = -1070268351
ERROR_HV_INVALID_REGISTER_VALUE: Final = -1070268336
ERROR_HV_INVALID_VTL_STATE: Final = -1070268335
ERROR_HV_NX_NOT_DETECTED: Final = -1070268331
ERROR_HV_INVALID_DEVICE_ID: Final = -1070268329
ERROR_HV_INVALID_DEVICE_STATE: Final = -1070268328
ERROR_HV_PENDING_PAGE_REQUESTS: Final = 0x00350059
ERROR_HV_PAGE_REQUEST_INVALID: Final = -1070268320
ERROR_HV_INVALID_CPU_GROUP_ID: Final = -1070268305
ERROR_HV_INVALID_CPU_GROUP_STATE: Final = -1070268304
ERROR_HV_OPERATION_FAILED: Final = -1070268303
ERROR_HV_NOT_ALLOWED_WITH_NESTED_VIRT_ACTIVE: Final = -1070268302
ERROR_HV_INSUFFICIENT_ROOT_MEMORY: Final = -1070268301
ERROR_HV_EVENT_BUFFER_ALREADY_FREED: Final = -1070268300
ERROR_HV_INSUFFICIENT_CONTIGUOUS_MEMORY: Final = -1070268299
ERROR_HV_DEVICE_NOT_IN_DOMAIN: Final = -1070268298
ERROR_HV_NESTED_VM_EXIT: Final = -1070268297
ERROR_HV_MSR_ACCESS_FAILED: Final = -1070268288
ERROR_HV_INSUFFICIENT_MEMORY_MIRRORING: Final = -1070268287
ERROR_HV_INSUFFICIENT_CONTIGUOUS_MEMORY_MIRRORING: Final = -1070268286
ERROR_HV_INSUFFICIENT_CONTIGUOUS_ROOT_MEMORY: Final = -1070268285
ERROR_HV_INSUFFICIENT_ROOT_MEMORY_MIRRORING: Final = -1070268284
ERROR_HV_INSUFFICIENT_CONTIGUOUS_ROOT_MEMORY_MIRRORING: Final = -1070268283
ERROR_HV_NOT_PRESENT: Final = -1070264320
ERROR_VID_DUPLICATE_HANDLER: Final = -1070137343
ERROR_VID_TOO_MANY_HANDLERS: Final = -1070137342
ERROR_VID_QUEUE_FULL: Final = -1070137341
ERROR_VID_HANDLER_NOT_PRESENT: Final = -1070137340
ERROR_VID_INVALID_OBJECT_NAME: Final = -1070137339
ERROR_VID_PARTITION_NAME_TOO_LONG: Final = -1070137338
ERROR_VID_MESSAGE_QUEUE_NAME_TOO_LONG: Final = -1070137337
ERROR_VID_PARTITION_ALREADY_EXISTS: Final = -1070137336
ERROR_VID_PARTITION_DOES_NOT_EXIST: Final = -1070137335
ERROR_VID_PARTITION_NAME_NOT_FOUND: Final = -1070137334
ERROR_VID_MESSAGE_QUEUE_ALREADY_EXISTS: Final = -1070137333
ERROR_VID_EXCEEDED_MBP_ENTRY_MAP_LIMIT: Final = -1070137332
ERROR_VID_MB_STILL_REFERENCED: Final = -1070137331
ERROR_VID_CHILD_GPA_PAGE_SET_CORRUPTED: Final = -1070137330
ERROR_VID_INVALID_NUMA_SETTINGS: Final = -1070137329
ERROR_VID_INVALID_NUMA_NODE_INDEX: Final = -1070137328
ERROR_VID_NOTIFICATION_QUEUE_ALREADY_ASSOCIATED: Final = -1070137327
ERROR_VID_INVALID_MEMORY_BLOCK_HANDLE: Final = -1070137326
ERROR_VID_PAGE_RANGE_OVERFLOW: Final = -1070137325
ERROR_VID_INVALID_MESSAGE_QUEUE_HANDLE: Final = -1070137324
ERROR_VID_INVALID_GPA_RANGE_HANDLE: Final = -1070137323
ERROR_VID_NO_MEMORY_BLOCK_NOTIFICATION_QUEUE: Final = -1070137322
ERROR_VID_MEMORY_BLOCK_LOCK_COUNT_EXCEEDED: Final = -1070137321
ERROR_VID_INVALID_PPM_HANDLE: Final = -1070137320
ERROR_VID_MBPS_ARE_LOCKED: Final = -1070137319
ERROR_VID_MESSAGE_QUEUE_CLOSED: Final = -1070137318
ERROR_VID_VIRTUAL_PROCESSOR_LIMIT_EXCEEDED: Final = -1070137317
ERROR_VID_STOP_PENDING: Final = -1070137316
ERROR_VID_INVALID_PROCESSOR_STATE: Final = -1070137315
ERROR_VID_EXCEEDED_KM_CONTEXT_COUNT_LIMIT: Final = -1070137314
ERROR_VID_KM_INTERFACE_ALREADY_INITIALIZED: Final = -1070137313
ERROR_VID_MB_PROPERTY_ALREADY_SET_RESET: Final = -1070137312
ERROR_VID_MMIO_RANGE_DESTROYED: Final = -1070137311
ERROR_VID_INVALID_CHILD_GPA_PAGE_SET: Final = -1070137310
ERROR_VID_RESERVE_PAGE_SET_IS_BEING_USED: Final = -1070137309
ERROR_VID_RESERVE_PAGE_SET_TOO_SMALL: Final = -1070137308
ERROR_VID_MBP_ALREADY_LOCKED_USING_RESERVED_PAGE: Final = -1070137307
ERROR_VID_MBP_COUNT_EXCEEDED_LIMIT: Final = -1070137306
ERROR_VID_SAVED_STATE_CORRUPT: Final = -1070137305
ERROR_VID_SAVED_STATE_UNRECOGNIZED_ITEM: Final = -1070137304
ERROR_VID_SAVED_STATE_INCOMPATIBLE: Final = -1070137303
ERROR_VID_VTL_ACCESS_DENIED: Final = -1070137302
ERROR_VID_INSUFFICIENT_RESOURCES_RESERVE: Final = -1070137301
ERROR_VID_INSUFFICIENT_RESOURCES_PHYSICAL_BUFFER: Final = -1070137300
ERROR_VID_INSUFFICIENT_RESOURCES_HV_DEPOSIT: Final = -1070137299
ERROR_VID_MEMORY_TYPE_NOT_SUPPORTED: Final = -1070137298
ERROR_VID_INSUFFICIENT_RESOURCES_WITHDRAW: Final = -1070137297
ERROR_VID_PROCESS_ALREADY_SET: Final = -1070137296
ERROR_VMCOMPUTE_TERMINATED_DURING_START: Final = -1070137088
ERROR_VMCOMPUTE_IMAGE_MISMATCH: Final = -1070137087
ERROR_VMCOMPUTE_HYPERV_NOT_INSTALLED: Final = -1070137086
ERROR_VMCOMPUTE_OPERATION_PENDING: Final = -1070137085
ERROR_VMCOMPUTE_TOO_MANY_NOTIFICATIONS: Final = -1070137084
ERROR_VMCOMPUTE_INVALID_STATE: Final = -1070137083
ERROR_VMCOMPUTE_UNEXPECTED_EXIT: Final = -1070137082
ERROR_VMCOMPUTE_TERMINATED: Final = -1070137081
ERROR_VMCOMPUTE_CONNECT_FAILED: Final = -1070137080
ERROR_VMCOMPUTE_TIMEOUT: Final = -1070137079
ERROR_VMCOMPUTE_CONNECTION_CLOSED: Final = -1070137078
ERROR_VMCOMPUTE_UNKNOWN_MESSAGE: Final = -1070137077
ERROR_VMCOMPUTE_UNSUPPORTED_PROTOCOL_VERSION: Final = -1070137076
ERROR_VMCOMPUTE_INVALID_JSON: Final = -1070137075
ERROR_VMCOMPUTE_SYSTEM_NOT_FOUND: Final = -1070137074
ERROR_VMCOMPUTE_SYSTEM_ALREADY_EXISTS: Final = -1070137073
ERROR_VMCOMPUTE_SYSTEM_ALREADY_STOPPED: Final = -1070137072
ERROR_VMCOMPUTE_PROTOCOL_ERROR: Final = -1070137071
ERROR_VMCOMPUTE_INVALID_LAYER: Final = -1070137070
ERROR_VMCOMPUTE_WINDOWS_INSIDER_REQUIRED: Final = -1070137069
HCS_E_TERMINATED_DURING_START: Final = -2143878912
HCS_E_IMAGE_MISMATCH: Final = -2143878911
HCS_E_HYPERV_NOT_INSTALLED: Final = -2143878910
HCS_E_INVALID_STATE: Final = -2143878907
HCS_E_UNEXPECTED_EXIT: Final = -2143878906
HCS_E_TERMINATED: Final = -2143878905
HCS_E_CONNECT_FAILED: Final = -2143878904
HCS_E_CONNECTION_TIMEOUT: Final = -2143878903
HCS_E_CONNECTION_CLOSED: Final = -2143878902
HCS_E_UNKNOWN_MESSAGE: Final = -2143878901
HCS_E_UNSUPPORTED_PROTOCOL_VERSION: Final = -2143878900
HCS_E_INVALID_JSON: Final = -2143878899
HCS_E_SYSTEM_NOT_FOUND: Final = -2143878898
HCS_E_SYSTEM_ALREADY_EXISTS: Final = -2143878897
HCS_E_SYSTEM_ALREADY_STOPPED: Final = -2143878896
HCS_E_PROTOCOL_ERROR: Final = -2143878895
HCS_E_INVALID_LAYER: Final = -2143878894
HCS_E_WINDOWS_INSIDER_REQUIRED: Final = -2143878893
HCS_E_SERVICE_NOT_AVAILABLE: Final = -2143878892
HCS_E_OPERATION_NOT_STARTED: Final = -2143878891
HCS_E_OPERATION_ALREADY_STARTED: Final = -2143878890
HCS_E_OPERATION_PENDING: Final = -2143878889
HCS_E_OPERATION_TIMEOUT: Final = -2143878888
HCS_E_OPERATION_SYSTEM_CALLBACK_ALREADY_SET: Final = -2143878887
HCS_E_OPERATION_RESULT_ALLOCATION_FAILED: Final = -2143878886
HCS_E_ACCESS_DENIED: Final = -2143878885
HCS_E_GUEST_CRITICAL_ERROR: Final = -2143878884
HCS_E_PROCESS_INFO_NOT_AVAILABLE: Final = -2143878883
HCS_E_SERVICE_DISCONNECT: Final = -2143878882
HCS_E_PROCESS_ALREADY_STOPPED: Final = -2143878881
HCS_E_SYSTEM_NOT_CONFIGURED_FOR_OPERATION: Final = -2143878880
HCS_E_OPERATION_ALREADY_CANCELLED: Final = -2143878879
ERROR_VNET_VIRTUAL_SWITCH_NAME_NOT_FOUND: Final = -1070136832
ERROR_VID_REMOTE_NODE_PARENT_GPA_PAGES_USED: Final = -2143879167
WHV_E_UNKNOWN_CAPABILITY: Final = -2143878400
WHV_E_INSUFFICIENT_BUFFER: Final = -2143878399
WHV_E_UNKNOWN_PROPERTY: Final = -2143878398
WHV_E_UNSUPPORTED_HYPERVISOR_CONFIG: Final = -2143878397
WHV_E_INVALID_PARTITION_CONFIG: Final = -2143878396
WHV_E_GPA_RANGE_NOT_FOUND: Final = -2143878395
WHV_E_VP_ALREADY_EXISTS: Final = -2143878394
WHV_E_VP_DOES_NOT_EXIST: Final = -2143878393
WHV_E_INVALID_VP_STATE: Final = -2143878392
WHV_E_INVALID_VP_REGISTER_NAME: Final = -2143878391
WHV_E_UNSUPPORTED_PROCESSOR_CONFIG: Final = -2143878384
ERROR_VSMB_SAVED_STATE_FILE_NOT_FOUND: Final = -1070136320
ERROR_VSMB_SAVED_STATE_CORRUPT: Final = -1070136319
VM_SAVED_STATE_DUMP_E_PARTITION_STATE_NOT_FOUND: Final = -1070136064
VM_SAVED_STATE_DUMP_E_GUEST_MEMORY_NOT_FOUND: Final = -1070136063
VM_SAVED_STATE_DUMP_E_NO_VP_FOUND_IN_PARTITION_STATE: Final = -1070136062
VM_SAVED_STATE_DUMP_E_NESTED_VIRTUALIZATION_NOT_SUPPORTED: Final = -1070136061
VM_SAVED_STATE_DUMP_E_WINDOWS_KERNEL_IMAGE_NOT_FOUND: Final = -1070136060
VM_SAVED_STATE_DUMP_E_VA_NOT_MAPPED: Final = -1070136059
VM_SAVED_STATE_DUMP_E_INVALID_VP_STATE: Final = -1070136058
VM_SAVED_STATE_DUMP_E_VP_VTL_NOT_ENABLED: Final = -1070136055
ERROR_DM_OPERATION_LIMIT_EXCEEDED: Final = -1070135808
ERROR_VOLMGR_INCOMPLETE_REGENERATION: Final = -2143813631
ERROR_VOLMGR_INCOMPLETE_DISK_MIGRATION: Final = -2143813630
ERROR_VOLMGR_DATABASE_FULL: Final = -1070071807
ERROR_VOLMGR_DISK_CONFIGURATION_CORRUPTED: Final = -1070071806
ERROR_VOLMGR_DISK_CONFIGURATION_NOT_IN_SYNC: Final = -1070071805
ERROR_VOLMGR_PACK_CONFIG_UPDATE_FAILED: Final = -1070071804
ERROR_VOLMGR_DISK_CONTAINS_NON_SIMPLE_VOLUME: Final = -1070071803
ERROR_VOLMGR_DISK_DUPLICATE: Final = -1070071802
ERROR_VOLMGR_DISK_DYNAMIC: Final = -1070071801
ERROR_VOLMGR_DISK_ID_INVALID: Final = -1070071800
ERROR_VOLMGR_DISK_INVALID: Final = -1070071799
ERROR_VOLMGR_DISK_LAST_VOTER: Final = -1070071798
ERROR_VOLMGR_DISK_LAYOUT_INVALID: Final = -1070071797
ERROR_VOLMGR_DISK_LAYOUT_NON_BASIC_BETWEEN_BASIC_PARTITIONS: Final = -1070071796
ERROR_VOLMGR_DISK_LAYOUT_NOT_CYLINDER_ALIGNED: Final = -1070071795
ERROR_VOLMGR_DISK_LAYOUT_PARTITIONS_TOO_SMALL: Final = -1070071794
ERROR_VOLMGR_DISK_LAYOUT_PRIMARY_BETWEEN_LOGICAL_PARTITIONS: Final = -1070071793
ERROR_VOLMGR_DISK_LAYOUT_TOO_MANY_PARTITIONS: Final = -1070071792
ERROR_VOLMGR_DISK_MISSING: Final = -1070071791
ERROR_VOLMGR_DISK_NOT_EMPTY: Final = -1070071790
ERROR_VOLMGR_DISK_NOT_ENOUGH_SPACE: Final = -1070071789
ERROR_VOLMGR_DISK_REVECTORING_FAILED: Final = -1070071788
ERROR_VOLMGR_DISK_SECTOR_SIZE_INVALID: Final = -1070071787
ERROR_VOLMGR_DISK_SET_NOT_CONTAINED: Final = -1070071786
ERROR_VOLMGR_DISK_USED_BY_MULTIPLE_MEMBERS: Final = -1070071785
ERROR_VOLMGR_DISK_USED_BY_MULTIPLE_PLEXES: Final = -1070071784
ERROR_VOLMGR_DYNAMIC_DISK_NOT_SUPPORTED: Final = -1070071783
ERROR_VOLMGR_EXTENT_ALREADY_USED: Final = -1070071782
ERROR_VOLMGR_EXTENT_NOT_CONTIGUOUS: Final = -1070071781
ERROR_VOLMGR_EXTENT_NOT_IN_PUBLIC_REGION: Final = -1070071780
ERROR_VOLMGR_EXTENT_NOT_SECTOR_ALIGNED: Final = -1070071779
ERROR_VOLMGR_EXTENT_OVERLAPS_EBR_PARTITION: Final = -1070071778
ERROR_VOLMGR_EXTENT_VOLUME_LENGTHS_DO_NOT_MATCH: Final = -1070071777
ERROR_VOLMGR_FAULT_TOLERANT_NOT_SUPPORTED: Final = -1070071776
ERROR_VOLMGR_INTERLEAVE_LENGTH_INVALID: Final = -1070071775
ERROR_VOLMGR_MAXIMUM_REGISTERED_USERS: Final = -1070071774
ERROR_VOLMGR_MEMBER_IN_SYNC: Final = -1070071773
ERROR_VOLMGR_MEMBER_INDEX_DUPLICATE: Final = -1070071772
ERROR_VOLMGR_MEMBER_INDEX_INVALID: Final = -1070071771
ERROR_VOLMGR_MEMBER_MISSING: Final = -1070071770
ERROR_VOLMGR_MEMBER_NOT_DETACHED: Final = -1070071769
ERROR_VOLMGR_MEMBER_REGENERATING: Final = -1070071768
ERROR_VOLMGR_ALL_DISKS_FAILED: Final = -1070071767
ERROR_VOLMGR_NO_REGISTERED_USERS: Final = -1070071766
ERROR_VOLMGR_NO_SUCH_USER: Final = -1070071765
ERROR_VOLMGR_NOTIFICATION_RESET: Final = -1070071764
ERROR_VOLMGR_NUMBER_OF_MEMBERS_INVALID: Final = -1070071763
ERROR_VOLMGR_NUMBER_OF_PLEXES_INVALID: Final = -1070071762
ERROR_VOLMGR_PACK_DUPLICATE: Final = -1070071761
ERROR_VOLMGR_PACK_ID_INVALID: Final = -1070071760
ERROR_VOLMGR_PACK_INVALID: Final = -1070071759
ERROR_VOLMGR_PACK_NAME_INVALID: Final = -1070071758
ERROR_VOLMGR_PACK_OFFLINE: Final = -1070071757
ERROR_VOLMGR_PACK_HAS_QUORUM: Final = -1070071756
ERROR_VOLMGR_PACK_WITHOUT_QUORUM: Final = -1070071755
ERROR_VOLMGR_PARTITION_STYLE_INVALID: Final = -1070071754
ERROR_VOLMGR_PARTITION_UPDATE_FAILED: Final = -1070071753
ERROR_VOLMGR_PLEX_IN_SYNC: Final = -1070071752
ERROR_VOLMGR_PLEX_INDEX_DUPLICATE: Final = -1070071751
ERROR_VOLMGR_PLEX_INDEX_INVALID: Final = -1070071750
ERROR_VOLMGR_PLEX_LAST_ACTIVE: Final = -1070071749
ERROR_VOLMGR_PLEX_MISSING: Final = -1070071748
ERROR_VOLMGR_PLEX_REGENERATING: Final = -1070071747
ERROR_VOLMGR_PLEX_TYPE_INVALID: Final = -1070071746
ERROR_VOLMGR_PLEX_NOT_RAID5: Final = -1070071745
ERROR_VOLMGR_PLEX_NOT_SIMPLE: Final = -1070071744
ERROR_VOLMGR_STRUCTURE_SIZE_INVALID: Final = -1070071743
ERROR_VOLMGR_TOO_MANY_NOTIFICATION_REQUESTS: Final = -1070071742
ERROR_VOLMGR_TRANSACTION_IN_PROGRESS: Final = -1070071741
ERROR_VOLMGR_UNEXPECTED_DISK_LAYOUT_CHANGE: Final = -1070071740
ERROR_VOLMGR_VOLUME_CONTAINS_MISSING_DISK: Final = -1070071739
ERROR_VOLMGR_VOLUME_ID_INVALID: Final = -1070071738
ERROR_VOLMGR_VOLUME_LENGTH_INVALID: Final = -1070071737
ERROR_VOLMGR_VOLUME_LENGTH_NOT_SECTOR_SIZE_MULTIPLE: Final = -1070071736
ERROR_VOLMGR_VOLUME_NOT_MIRRORED: Final = -1070071735
ERROR_VOLMGR_VOLUME_NOT_RETAINED: Final = -1070071734
ERROR_VOLMGR_VOLUME_OFFLINE: Final = -1070071733
ERROR_VOLMGR_VOLUME_RETAINED: Final = -1070071732
ERROR_VOLMGR_NUMBER_OF_EXTENTS_INVALID: Final = -1070071731
ERROR_VOLMGR_DIFFERENT_SECTOR_SIZE: Final = -1070071730
ERROR_VOLMGR_BAD_BOOT_DISK: Final = -1070071729
ERROR_VOLMGR_PACK_CONFIG_OFFLINE: Final = -1070071728
ERROR_VOLMGR_PACK_CONFIG_ONLINE: Final = -1070071727
ERROR_VOLMGR_NOT_PRIMARY_PACK: Final = -1070071726
ERROR_VOLMGR_PACK_LOG_UPDATE_FAILED: Final = -1070071725
ERROR_VOLMGR_NUMBER_OF_DISKS_IN_PLEX_INVALID: Final = -1070071724
ERROR_VOLMGR_NUMBER_OF_DISKS_IN_MEMBER_INVALID: Final = -1070071723
ERROR_VOLMGR_VOLUME_MIRRORED: Final = -1070071722
ERROR_VOLMGR_PLEX_NOT_SIMPLE_SPANNED: Final = -1070071721
ERROR_VOLMGR_NO_VALID_LOG_COPIES: Final = -1070071720
ERROR_VOLMGR_PRIMARY_PACK_PRESENT: Final = -1070071719
ERROR_VOLMGR_NUMBER_OF_DISKS_INVALID: Final = -1070071718
ERROR_VOLMGR_MIRROR_NOT_SUPPORTED: Final = -1070071717
ERROR_VOLMGR_RAID5_NOT_SUPPORTED: Final = -1070071716
ERROR_BCD_NOT_ALL_ENTRIES_IMPORTED: Final = -2143748095
ERROR_BCD_TOO_MANY_ELEMENTS: Final = -1070006270
ERROR_BCD_NOT_ALL_ENTRIES_SYNCHRONIZED: Final = -2143748093
ERROR_VHD_DRIVE_FOOTER_MISSING: Final = -1069940735
ERROR_VHD_DRIVE_FOOTER_CHECKSUM_MISMATCH: Final = -1069940734
ERROR_VHD_DRIVE_FOOTER_CORRUPT: Final = -1069940733
ERROR_VHD_FORMAT_UNKNOWN: Final = -1069940732
ERROR_VHD_FORMAT_UNSUPPORTED_VERSION: Final = -1069940731
ERROR_VHD_SPARSE_HEADER_CHECKSUM_MISMATCH: Final = -1069940730
ERROR_VHD_SPARSE_HEADER_UNSUPPORTED_VERSION: Final = -1069940729
ERROR_VHD_SPARSE_HEADER_CORRUPT: Final = -1069940728
ERROR_VHD_BLOCK_ALLOCATION_FAILURE: Final = -1069940727
ERROR_VHD_BLOCK_ALLOCATION_TABLE_CORRUPT: Final = -**********
ERROR_VHD_INVALID_BLOCK_SIZE: Final = -**********
ERROR_VHD_BITMAP_MISMATCH: Final = -**********
ERROR_VHD_PARENT_VHD_NOT_FOUND: Final = -**********
ERROR_VHD_CHILD_PARENT_ID_MISMATCH: Final = -**********
ERROR_VHD_CHILD_PARENT_TIMESTAMP_MISMATCH: Final = -**********
ERROR_VHD_METADATA_READ_FAILURE: Final = -**********
ERROR_VHD_METADATA_WRITE_FAILURE: Final = -**********
ERROR_VHD_INVALID_SIZE: Final = -**********
ERROR_VHD_INVALID_FILE_SIZE: Final = -**********
ERROR_VIRTDISK_PROVIDER_NOT_FOUND: Final = -**********
ERROR_VIRTDISK_NOT_VIRTUAL_DISK: Final = -**********
ERROR_VHD_PARENT_VHD_ACCESS_DENIED: Final = -**********
ERROR_VHD_CHILD_PARENT_SIZE_MISMATCH: Final = -**********
ERROR_VHD_DIFFERENCING_CHAIN_CYCLE_DETECTED: Final = -**********
ERROR_VHD_DIFFERENCING_CHAIN_ERROR_IN_PARENT: Final = -**********
ERROR_VIRTUAL_DISK_LIMITATION: Final = -**********
ERROR_VHD_INVALID_TYPE: Final = -**********
ERROR_VHD_INVALID_STATE: Final = -**********
ERROR_VIRTDISK_UNSUPPORTED_DISK_SECTOR_SIZE: Final = -**********
ERROR_VIRTDISK_DISK_ALREADY_OWNED: Final = -**********
ERROR_VIRTDISK_DISK_ONLINE_AND_WRITABLE: Final = -**********
ERROR_CTLOG_TRACKING_NOT_INITIALIZED: Final = -**********
ERROR_CTLOG_LOGFILE_SIZE_EXCEEDED_MAXSIZE: Final = -**********
ERROR_CTLOG_VHD_CHANGED_OFFLINE: Final = -**********
ERROR_CTLOG_INVALID_TRACKING_STATE: Final = -**********
ERROR_CTLOG_INCONSISTENT_TRACKING_FILE: Final = -**********
ERROR_VHD_RESIZE_WOULD_TRUNCATE_DATA: Final = -1069940699
ERROR_VHD_COULD_NOT_COMPUTE_MINIMUM_VIRTUAL_SIZE: Final = -1069940698
ERROR_VHD_ALREADY_AT_OR_BELOW_MINIMUM_VIRTUAL_SIZE: Final = -1069940697
ERROR_VHD_METADATA_FULL: Final = -1069940696
ERROR_VHD_INVALID_CHANGE_TRACKING_ID: Final = -1069940695
ERROR_VHD_CHANGE_TRACKING_DISABLED: Final = -1069940694
ERROR_VHD_MISSING_CHANGE_TRACKING_INFORMATION: Final = -1069940688
ERROR_VHD_UNEXPECTED_ID: Final = -1069940684
ERROR_QUERY_STORAGE_ERROR: Final = -2143682559
HCN_E_NETWORK_NOT_FOUND: Final = -2143617023
HCN_E_ENDPOINT_NOT_FOUND: Final = -2143617022
HCN_E_LAYER_NOT_FOUND: Final = -2143617021
HCN_E_SWITCH_NOT_FOUND: Final = -2143617020
HCN_E_SUBNET_NOT_FOUND: Final = -2143617019
HCN_E_ADAPTER_NOT_FOUND: Final = -2143617018
HCN_E_PORT_NOT_FOUND: Final = -2143617017
HCN_E_POLICY_NOT_FOUND: Final = -2143617016
HCN_E_VFP_PORTSETTING_NOT_FOUND: Final = -2143617015
HCN_E_INVALID_NETWORK: Final = -2143617014
HCN_E_INVALID_NETWORK_TYPE: Final = -2143617013
HCN_E_INVALID_ENDPOINT: Final = -2143617012
HCN_E_INVALID_POLICY: Final = -2143617011
HCN_E_INVALID_POLICY_TYPE: Final = -2143617010
HCN_E_INVALID_REMOTE_ENDPOINT_OPERATION: Final = -2143617009
HCN_E_NETWORK_ALREADY_EXISTS: Final = -2143617008
HCN_E_LAYER_ALREADY_EXISTS: Final = -2143617007
HCN_E_POLICY_ALREADY_EXISTS: Final = -2143617006
HCN_E_PORT_ALREADY_EXISTS: Final = -2143617005
HCN_E_ENDPOINT_ALREADY_ATTACHED: Final = -2143617004
HCN_E_REQUEST_UNSUPPORTED: Final = -2143617003
HCN_E_MAPPING_NOT_SUPPORTED: Final = -2143617002
HCN_E_DEGRADED_OPERATION: Final = -2143617001
HCN_E_SHARED_SWITCH_MODIFICATION: Final = -2143617000
HCN_E_GUID_CONVERSION_FAILURE: Final = -2143616999
HCN_E_REGKEY_FAILURE: Final = -2143616998
HCN_E_INVALID_JSON: Final = -2143616997
HCN_E_INVALID_JSON_REFERENCE: Final = -2143616996
HCN_E_ENDPOINT_SHARING_DISABLED: Final = -2143616995
HCN_E_INVALID_IP: Final = -2143616994
HCN_E_SWITCH_EXTENSION_NOT_FOUND: Final = -2143616993
HCN_E_MANAGER_STOPPED: Final = -2143616992
GCN_E_MODULE_NOT_FOUND: Final = -2143616991
GCN_E_NO_REQUEST_HANDLERS: Final = -2143616990
GCN_E_REQUEST_UNSUPPORTED: Final = -2143616989
GCN_E_RUNTIMEKEYS_FAILED: Final = -2143616988
GCN_E_NETADAPTER_TIMEOUT: Final = -2143616987
GCN_E_NETADAPTER_NOT_FOUND: Final = -2143616986
GCN_E_NETCOMPARTMENT_NOT_FOUND: Final = -2143616985
GCN_E_NETINTERFACE_NOT_FOUND: Final = -2143616984
GCN_E_DEFAULTNAMESPACE_EXISTS: Final = -2143616983
HCN_E_ICS_DISABLED: Final = -2143616982
HCN_E_ENDPOINT_NAMESPACE_ALREADY_EXISTS: Final = -2143616981
HCN_E_ENTITY_HAS_REFERENCES: Final = -2143616980
HCN_E_INVALID_INTERNAL_PORT: Final = -2143616979
HCN_E_NAMESPACE_ATTACH_FAILED: Final = -2143616978
HCN_E_ADDR_INVALID_OR_RESERVED: Final = -2143616977
HCN_E_INVALID_PREFIX: Final = -2143616976
HCN_E_OBJECT_USED_AFTER_UNLOAD: Final = -2143616975
HCN_E_INVALID_SUBNET: Final = -2143616974
HCN_E_INVALID_IP_SUBNET: Final = -2143616973
HCN_E_ENDPOINT_NOT_ATTACHED: Final = -2143616972
HCN_E_ENDPOINT_NOT_LOCAL: Final = -2143616971
HCN_INTERFACEPARAMETERS_ALREADY_APPLIED: Final = -2143616970
HCN_E_VFP_NOT_ALLOWED: Final = -2143616969
SDIAG_E_CANCELLED: Final = -2143551232
SDIAG_E_SCRIPT: Final = -2143551231
SDIAG_E_POWERSHELL: Final = -2143551230
SDIAG_E_MANAGEDHOST: Final = -2143551229
SDIAG_E_NOVERIFIER: Final = -2143551228
SDIAG_S_CANNOTRUN: Final = 0x003C0105
SDIAG_E_DISABLED: Final = -2143551226
SDIAG_E_TRUST: Final = -2143551225
SDIAG_E_CANNOTRUN: Final = -2143551224
SDIAG_E_VERSION: Final = -2143551223
SDIAG_E_RESOURCE: Final = -2143551222
SDIAG_E_ROOTCAUSE: Final = -2143551221
WPN_E_CHANNEL_CLOSED: Final = -2143420160
WPN_E_CHANNEL_REQUEST_NOT_COMPLETE: Final = -2143420159
WPN_E_INVALID_APP: Final = -2143420158
WPN_E_OUTSTANDING_CHANNEL_REQUEST: Final = -2143420157
WPN_E_DUPLICATE_CHANNEL: Final = -2143420156
WPN_E_PLATFORM_UNAVAILABLE: Final = -2143420155
WPN_E_NOTIFICATION_POSTED: Final = -2143420154
WPN_E_NOTIFICATION_HIDDEN: Final = -2143420153
WPN_E_NOTIFICATION_NOT_POSTED: Final = -2143420152
WPN_E_CLOUD_DISABLED: Final = -2143420151
WPN_E_CLOUD_INCAPABLE: Final = -2143420144
WPN_E_CLOUD_AUTH_UNAVAILABLE: Final = -2143420134
WPN_E_CLOUD_SERVICE_UNAVAILABLE: Final = -2143420133
WPN_E_FAILED_LOCK_SCREEN_UPDATE_INTIALIZATION: Final = -2143420132
WPN_E_NOTIFICATION_DISABLED: Final = -2143420143
WPN_E_NOTIFICATION_INCAPABLE: Final = -2143420142
WPN_E_INTERNET_INCAPABLE: Final = -2143420141
WPN_E_NOTIFICATION_TYPE_DISABLED: Final = -2143420140
WPN_E_NOTIFICATION_SIZE: Final = -2143420139
WPN_E_TAG_SIZE: Final = -2143420138
WPN_E_ACCESS_DENIED: Final = -2143420137
WPN_E_DUPLICATE_REGISTRATION: Final = -2143420136
WPN_E_PUSH_NOTIFICATION_INCAPABLE: Final = -2143420135
WPN_E_DEV_ID_SIZE: Final = -2143420128
WPN_E_TAG_ALPHANUMERIC: Final = -2143420118
WPN_E_INVALID_HTTP_STATUS_CODE: Final = -2143420117
WPN_E_OUT_OF_SESSION: Final = -2143419904
WPN_E_POWER_SAVE: Final = -2143419903
WPN_E_IMAGE_NOT_FOUND_IN_CACHE: Final = -2143419902
WPN_E_ALL_URL_NOT_COMPLETED: Final = -2143419901
WPN_E_INVALID_CLOUD_IMAGE: Final = -2143419900
WPN_E_NOTIFICATION_ID_MATCHED: Final = -2143419899
WPN_E_CALLBACK_ALREADY_REGISTERED: Final = -2143419898
WPN_E_TOAST_NOTIFICATION_DROPPED: Final = -**********
WPN_E_STORAGE_LOCKED: Final = -**********
WPN_E_GROUP_SIZE: Final = -**********
WPN_E_GROUP_ALPHANUMERIC: Final = -**********
WPN_E_CLOUD_DISABLED_FOR_APP: Final = -**********
E_MBN_CONTEXT_NOT_ACTIVATED: Final = -**********
E_MBN_BAD_SIM: Final = -**********
E_MBN_DATA_CLASS_NOT_AVAILABLE: Final = -**********
E_MBN_INVALID_ACCESS_STRING: Final = -**********
E_MBN_MAX_ACTIVATED_CONTEXTS: Final = -**********
E_MBN_PACKET_SVC_DETACHED: Final = -**********
E_MBN_PROVIDER_NOT_VISIBLE: Final = -**********
E_MBN_RADIO_POWER_OFF: Final = -**********
E_MBN_SERVICE_NOT_ACTIVATED: Final = -**********
E_MBN_SIM_NOT_INSERTED: Final = -**********
E_MBN_VOICE_CALL_IN_PROGRESS: Final = -**********
E_MBN_INVALID_CACHE: Final = -**********
E_MBN_NOT_REGISTERED: Final = -**********
E_MBN_PROVIDERS_NOT_FOUND: Final = -**********
E_MBN_PIN_NOT_SUPPORTED: Final = -**********
E_MBN_PIN_REQUIRED: Final = -**********
E_MBN_PIN_DISABLED: Final = -**********
E_MBN_FAILURE: Final = -**********
E_MBN_INVALID_PROFILE: Final = -**********
E_MBN_DEFAULT_PROFILE_EXIST: Final = -**********
E_MBN_SMS_ENCODING_NOT_SUPPORTED: Final = -**********
E_MBN_SMS_FILTER_NOT_SUPPORTED: Final = -**********
E_MBN_SMS_INVALID_MEMORY_INDEX: Final = -**********
E_MBN_SMS_LANG_NOT_SUPPORTED: Final = -**********
E_MBN_SMS_MEMORY_FAILURE: Final = -**********
E_MBN_SMS_NETWORK_TIMEOUT: Final = -**********
E_MBN_SMS_UNKNOWN_SMSC_ADDRESS: Final = -**********
E_MBN_SMS_FORMAT_NOT_SUPPORTED: Final = -**********
E_MBN_SMS_OPERATION_NOT_ALLOWED: Final = -**********
E_MBN_SMS_MEMORY_FULL: Final = -**********
PEER_E_IPV6_NOT_INSTALLED: Final = -**********
PEER_E_NOT_INITIALIZED: Final = -**********
PEER_E_CANNOT_START_SERVICE: Final = -**********
PEER_E_NOT_LICENSED: Final = -**********
PEER_E_INVALID_GRAPH: Final = -**********
PEER_E_DBNAME_CHANGED: Final = -**********
PEER_E_DUPLICATE_GRAPH: Final = -**********
PEER_E_GRAPH_NOT_READY: Final = -**********
PEER_E_GRAPH_SHUTTING_DOWN: Final = -**********
PEER_E_GRAPH_IN_USE: Final = -**********
PEER_E_INVALID_DATABASE: Final = -**********
PEER_E_TOO_MANY_ATTRIBUTES: Final = -**********
PEER_E_CONNECTION_NOT_FOUND: Final = -**********
PEER_E_CONNECT_SELF: Final = -**********
PEER_E_ALREADY_LISTENING: Final = -**********
PEER_E_NODE_NOT_FOUND: Final = -**********
PEER_E_CONNECTION_FAILED: Final = -**********
PEER_E_CONNECTION_NOT_AUTHENTICATED: Final = -**********
PEER_E_CONNECTION_REFUSED: Final = -**********
PEER_E_CLASSIFIER_TOO_LONG: Final = -**********
PEER_E_TOO_MANY_IDENTITIES: Final = -**********
PEER_E_NO_KEY_ACCESS: Final = -**********
PEER_E_GROUPS_EXIST: Final = -**********
PEER_E_RECORD_NOT_FOUND: Final = -**********
PEER_E_DATABASE_ACCESSDENIED: Final = -**********
PEER_E_DBINITIALIZATION_FAILED: Final = -2140994813
PEER_E_MAX_RECORD_SIZE_EXCEEDED: Final = -2140994812
PEER_E_DATABASE_ALREADY_PRESENT: Final = -2140994811
PEER_E_DATABASE_NOT_PRESENT: Final = -2140994810
PEER_E_IDENTITY_NOT_FOUND: Final = -2140994559
PEER_E_EVENT_HANDLE_NOT_FOUND: Final = -2140994303
PEER_E_INVALID_SEARCH: Final = -2140994047
PEER_E_INVALID_ATTRIBUTES: Final = -2140994046
PEER_E_INVITATION_NOT_TRUSTED: Final = -2140993791
PEER_E_CHAIN_TOO_LONG: Final = -2140993789
PEER_E_INVALID_TIME_PERIOD: Final = -2140993787
PEER_E_CIRCULAR_CHAIN_DETECTED: Final = -2140993786
PEER_E_CERT_STORE_CORRUPTED: Final = -2140993535
PEER_E_NO_CLOUD: Final = -2140991487
PEER_E_CLOUD_NAME_AMBIGUOUS: Final = -2140991483
PEER_E_INVALID_RECORD: Final = -2140987376
PEER_E_NOT_AUTHORIZED: Final = -2140987360
PEER_E_PASSWORD_DOES_NOT_MEET_POLICY: Final = -2140987359
PEER_E_DEFERRED_VALIDATION: Final = -2140987344
PEER_E_INVALID_GROUP_PROPERTIES: Final = -2140987328
PEER_E_INVALID_PEER_NAME: Final = -2140987312
PEER_E_INVALID_CLASSIFIER: Final = -2140987296
PEER_E_INVALID_FRIENDLY_NAME: Final = -2140987280
PEER_E_INVALID_ROLE_PROPERTY: Final = -2140987279
PEER_E_INVALID_CLASSIFIER_PROPERTY: Final = -2140987278
PEER_E_INVALID_RECORD_EXPIRATION: Final = -2140987264
PEER_E_INVALID_CREDENTIAL_INFO: Final = -2140987263
PEER_E_INVALID_CREDENTIAL: Final = -2140987262
PEER_E_INVALID_RECORD_SIZE: Final = -2140987261
PEER_E_UNSUPPORTED_VERSION: Final = -2140987248
PEER_E_GROUP_NOT_READY: Final = -2140987247
PEER_E_GROUP_IN_USE: Final = -2140987246
PEER_E_INVALID_GROUP: Final = -2140987245
PEER_E_NO_MEMBERS_FOUND: Final = -2140987244
PEER_E_NO_MEMBER_CONNECTIONS: Final = -2140987243
PEER_E_UNABLE_TO_LISTEN: Final = -2140987242
PEER_E_IDENTITY_DELETED: Final = -2140987232
PEER_E_SERVICE_NOT_AVAILABLE: Final = -2140987231
PEER_E_CONTACT_NOT_FOUND: Final = -2140971007
PEER_S_GRAPH_DATA_CREATED: Final = 0x00630001
PEER_S_NO_EVENT_DATA: Final = 0x00630002
PEER_S_ALREADY_CONNECTED: Final = 0x00632000
PEER_S_SUBSCRIPTION_EXISTS: Final = 0x00636000
PEER_S_NO_CONNECTIVITY: Final = 0x00630005
PEER_S_ALREADY_A_MEMBER: Final = 0x00630006
PEER_E_CANNOT_CONVERT_PEER_NAME: Final = -2140979199
PEER_E_INVALID_PEER_HOST_NAME: Final = -2140979198
PEER_E_NO_MORE: Final = -2140979197
PEER_E_PNRP_DUPLICATE_PEER_NAME: Final = -2140979195
PEER_E_INVITE_CANCELLED: Final = -2140966912
PEER_E_INVITE_RESPONSE_NOT_AVAILABLE: Final = -2140966911
PEER_E_NOT_SIGNED_IN: Final = -2140966909
PEER_E_PRIVACY_DECLINED: Final = -2140966908
PEER_E_TIMEOUT: Final = -2140966907
PEER_E_INVALID_ADDRESS: Final = -2140966905
PEER_E_FW_EXCEPTION_DISABLED: Final = -2140966904
PEER_E_FW_BLOCKED_BY_POLICY: Final = -2140966903
PEER_E_FW_BLOCKED_BY_SHIELDS_UP: Final = -2140966902
PEER_E_FW_DECLINED: Final = -2140966901
UI_E_CREATE_FAILED: Final = -2144731135
UI_E_SHUTDOWN_CALLED: Final = -2144731134
UI_E_ILLEGAL_REENTRANCY: Final = -2144731133
UI_E_OBJECT_SEALED: Final = -2144731132
UI_E_VALUE_NOT_SET: Final = -2144731131
UI_E_VALUE_NOT_DETERMINED: Final = -2144731130
UI_E_INVALID_OUTPUT: Final = -2144731129
UI_E_BOOLEAN_EXPECTED: Final = -2144731128
UI_E_DIFFERENT_OWNER: Final = -2144731127
UI_E_AMBIGUOUS_MATCH: Final = -2144731126
UI_E_FP_OVERFLOW: Final = -2144731125
UI_E_WRONG_THREAD: Final = -2144731124
UI_E_STORYBOARD_ACTIVE: Final = -2144730879
UI_E_STORYBOARD_NOT_PLAYING: Final = -2144730878
UI_E_START_KEYFRAME_AFTER_END: Final = -2144730877
UI_E_END_KEYFRAME_NOT_DETERMINED: Final = -2144730876
UI_E_LOOPS_OVERLAP: Final = -2144730875
UI_E_TRANSITION_ALREADY_USED: Final = -2144730874
UI_E_TRANSITION_NOT_IN_STORYBOARD: Final = -2144730873
UI_E_TRANSITION_ECLIPSED: Final = -2144730872
UI_E_TIME_BEFORE_LAST_UPDATE: Final = -2144730871
UI_E_TIMER_CLIENT_ALREADY_CONNECTED: Final = -2144730870
UI_E_INVALID_DIMENSION: Final = -2144730869
UI_E_PRIMITIVE_OUT_OF_BOUNDS: Final = -2144730868
UI_E_WINDOW_CLOSED: Final = -2144730623
E_BLUETOOTH_ATT_INVALID_HANDLE: Final = -2140864511
E_BLUETOOTH_ATT_READ_NOT_PERMITTED: Final = -2140864510
E_BLUETOOTH_ATT_WRITE_NOT_PERMITTED: Final = -2140864509
E_BLUETOOTH_ATT_INVALID_PDU: Final = -2140864508
E_BLUETOOTH_ATT_INSUFFICIENT_AUTHENTICATION: Final = -2140864507
E_BLUETOOTH_ATT_REQUEST_NOT_SUPPORTED: Final = -2140864506
E_BLUETOOTH_ATT_INVALID_OFFSET: Final = -2140864505
E_BLUETOOTH_ATT_INSUFFICIENT_AUTHORIZATION: Final = -2140864504
E_BLUETOOTH_ATT_PREPARE_QUEUE_FULL: Final = -2140864503
E_BLUETOOTH_ATT_ATTRIBUTE_NOT_FOUND: Final = -2140864502
E_BLUETOOTH_ATT_ATTRIBUTE_NOT_LONG: Final = -2140864501
E_BLUETOOTH_ATT_INSUFFICIENT_ENCRYPTION_KEY_SIZE: Final = -2140864500
E_BLUETOOTH_ATT_INVALID_ATTRIBUTE_VALUE_LENGTH: Final = -2140864499
E_BLUETOOTH_ATT_UNLIKELY: Final = -2140864498
E_BLUETOOTH_ATT_INSUFFICIENT_ENCRYPTION: Final = -2140864497
E_BLUETOOTH_ATT_UNSUPPORTED_GROUP_TYPE: Final = -2140864496
E_BLUETOOTH_ATT_INSUFFICIENT_RESOURCES: Final = -2140864495
E_BLUETOOTH_ATT_UNKNOWN_ERROR: Final = -2140860416
E_AUDIO_ENGINE_NODE_NOT_FOUND: Final = -2140798975
E_HDAUDIO_EMPTY_CONNECTION_LIST: Final = -2140798974
E_HDAUDIO_CONNECTION_LIST_NOT_SUPPORTED: Final = -2140798973
E_HDAUDIO_NO_LOGICAL_DEVICES_CREATED: Final = -2140798972
E_HDAUDIO_NULL_LINKED_LIST_ENTRY: Final = -2140798971
STATEREPOSITORY_E_CONCURRENCY_LOCKING_FAILURE: Final = -2140733439
STATEREPOSITORY_E_STATEMENT_INPROGRESS: Final = -2140733438
STATEREPOSITORY_E_CONFIGURATION_INVALID: Final = -2140733437
STATEREPOSITORY_E_UNKNOWN_SCHEMA_VERSION: Final = -2140733436
STATEREPOSITORY_ERROR_DICTIONARY_CORRUPTED: Final = -2140733435
STATEREPOSITORY_E_BLOCKED: Final = -2140733434
STATEREPOSITORY_E_BUSY_RETRY: Final = -2140733433
STATEREPOSITORY_E_BUSY_RECOVERY_RETRY: Final = -2140733432
STATEREPOSITORY_E_LOCKED_RETRY: Final = -2140733431
STATEREPOSITORY_E_LOCKED_SHAREDCACHE_RETRY: Final = -2140733430
STATEREPOSITORY_E_TRANSACTION_REQUIRED: Final = -2140733429
STATEREPOSITORY_E_BUSY_TIMEOUT_EXCEEDED: Final = -2140733428
STATEREPOSITORY_E_BUSY_RECOVERY_TIMEOUT_EXCEEDED: Final = -2140733427
STATEREPOSITORY_E_LOCKED_TIMEOUT_EXCEEDED: Final = -2140733426
STATEREPOSITORY_E_LOCKED_SHAREDCACHE_TIMEOUT_EXCEEDED: Final = -2140733425
STATEREPOSITORY_E_SERVICE_STOP_IN_PROGRESS: Final = -2140733424
STATEREPOSTORY_E_NESTED_TRANSACTION_NOT_SUPPORTED: Final = -2140733423
STATEREPOSITORY_ERROR_CACHE_CORRUPTED: Final = -2140733422
STATEREPOSITORY_TRANSACTION_CALLER_ID_CHANGED: Final = 0x00670013
STATEREPOSITORY_TRANSACTION_IN_PROGRESS: Final = -2140733420
STATEREPOSITORY_E_CACHE_NOT_INIITALIZED: Final = -2140733419
STATEREPOSITORY_E_DEPENDENCY_NOT_RESOLVED: Final = -2140733418
ERROR_SPACES_POOL_WAS_DELETED: Final = 0x00E70001
ERROR_SPACES_FAULT_DOMAIN_TYPE_INVALID: Final = -2132344831
ERROR_SPACES_INTERNAL_ERROR: Final = -2132344830
ERROR_SPACES_RESILIENCY_TYPE_INVALID: Final = -2132344829
ERROR_SPACES_DRIVE_SECTOR_SIZE_INVALID: Final = -2132344828
ERROR_SPACES_DRIVE_REDUNDANCY_INVALID: Final = -2132344826
ERROR_SPACES_NUMBER_OF_DATA_COPIES_INVALID: Final = -2132344825
ERROR_SPACES_PARITY_LAYOUT_INVALID: Final = -2132344824
ERROR_SPACES_INTERLEAVE_LENGTH_INVALID: Final = -2132344823
ERROR_SPACES_NUMBER_OF_COLUMNS_INVALID: Final = -2132344822
ERROR_SPACES_NOT_ENOUGH_DRIVES: Final = -2132344821
ERROR_SPACES_EXTENDED_ERROR: Final = -2132344820
ERROR_SPACES_PROVISIONING_TYPE_INVALID: Final = -2132344819
ERROR_SPACES_ALLOCATION_SIZE_INVALID: Final = -2132344818
ERROR_SPACES_ENCLOSURE_AWARE_INVALID: Final = -2132344817
ERROR_SPACES_WRITE_CACHE_SIZE_INVALID: Final = -2132344816
ERROR_SPACES_NUMBER_OF_GROUPS_INVALID: Final = -2132344815
ERROR_SPACES_DRIVE_OPERATIONAL_STATE_INVALID: Final = -2132344814
ERROR_SPACES_ENTRY_INCOMPLETE: Final = -2132344813
ERROR_SPACES_ENTRY_INVALID: Final = -2132344812
ERROR_SPACES_UPDATE_COLUMN_STATE: Final = -2132344811
ERROR_SPACES_MAP_REQUIRED: Final = -2132344810
ERROR_SPACES_UNSUPPORTED_VERSION: Final = -2132344809
ERROR_SPACES_CORRUPT_METADATA: Final = -2132344808
ERROR_SPACES_DRT_FULL: Final = -2132344807
ERROR_SPACES_INCONSISTENCY: Final = -2132344806
ERROR_SPACES_LOG_NOT_READY: Final = -2132344805
ERROR_SPACES_NO_REDUNDANCY: Final = -2132344804
ERROR_SPACES_DRIVE_NOT_READY: Final = -2132344803
ERROR_SPACES_DRIVE_SPLIT: Final = -2132344802
ERROR_SPACES_DRIVE_LOST_DATA: Final = -2132344801
ERROR_SPACES_MARK_DIRTY: Final = -2132344800
ERROR_SPACES_FLUSH_METADATA: Final = -2132344795
ERROR_SPACES_CACHE_FULL: Final = -2132344794
ERROR_SPACES_REPAIR_IN_PROGRESS: Final = -2132344793
ERROR_VOLSNAP_BOOTFILE_NOT_VALID: Final = -2138963967
ERROR_VOLSNAP_ACTIVATION_TIMEOUT: Final = -2138963966
ERROR_VOLSNAP_NO_BYPASSIO_WITH_SNAPSHOT: Final = -2138963965
ERROR_TIERING_NOT_SUPPORTED_ON_VOLUME: Final = -2138898431
ERROR_TIERING_VOLUME_DISMOUNT_IN_PROGRESS: Final = -**********
ERROR_TIERING_STORAGE_TIER_NOT_FOUND: Final = -**********
ERROR_TIERING_INVALID_FILE_ID: Final = -**********
ERROR_TIERING_WRONG_CLUSTER_NODE: Final = -**********
ERROR_TIERING_ALREADY_PROCESSING: Final = -**********
ERROR_TIERING_CANNOT_PIN_OBJECT: Final = -**********
ERROR_TIERING_FILE_IS_NOT_PINNED: Final = -**********
ERROR_NOT_A_TIERED_VOLUME: Final = -**********
ERROR_ATTRIBUTE_NOT_PRESENT: Final = -**********
ERROR_SECCORE_INVALID_COMMAND: Final = -**********
ERROR_NO_APPLICABLE_APP_LICENSES_FOUND: Final = -**********
ERROR_CLIP_LICENSE_NOT_FOUND: Final = -**********
ERROR_CLIP_DEVICE_LICENSE_MISSING: Final = -**********
ERROR_CLIP_LICENSE_INVALID_SIGNATURE: Final = -**********
ERROR_CLIP_KEYHOLDER_LICENSE_MISSING_OR_INVALID: Final = -**********
ERROR_CLIP_LICENSE_EXPIRED: Final = -**********
ERROR_CLIP_LICENSE_SIGNED_BY_UNKNOWN_SOURCE: Final = -**********
ERROR_CLIP_LICENSE_NOT_SIGNED: Final = -**********
ERROR_CLIP_LICENSE_HARDWARE_ID_OUT_OF_TOLERANCE: Final = -**********
ERROR_CLIP_LICENSE_DEVICE_ID_MISMATCH: Final = -**********
DXGI_STATUS_OCCLUDED: Final = 0x087A0001
DXGI_STATUS_CLIPPED: Final = 0x087A0002
DXGI_STATUS_NO_REDIRECTION: Final = 0x087A0004
DXGI_STATUS_NO_DESKTOP_ACCESS: Final = 0x087A0005
DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE: Final = 0x087A0006
DXGI_STATUS_MODE_CHANGED: Final = 0x087A0007
DXGI_STATUS_MODE_CHANGE_IN_PROGRESS: Final = 0x087A0008
DXGI_ERROR_INVALID_CALL: Final = -**********
DXGI_ERROR_NOT_FOUND: Final = -**********
DXGI_ERROR_MORE_DATA: Final = -**********
DXGI_ERROR_UNSUPPORTED: Final = -**********
DXGI_ERROR_DEVICE_REMOVED: Final = -**********
DXGI_ERROR_DEVICE_HUNG: Final = -**********
DXGI_ERROR_DEVICE_RESET: Final = -**********
DXGI_ERROR_WAS_STILL_DRAWING: Final = -**********
DXGI_ERROR_FRAME_STATISTICS_DISJOINT: Final = -**********
DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE: Final = -**********
DXGI_ERROR_DRIVER_INTERNAL_ERROR: Final = -**********
DXGI_ERROR_NONEXCLUSIVE: Final = -**********
DXGI_ERROR_NOT_CURRENTLY_AVAILABLE: Final = -2005270494
DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED: Final = -2005270493
DXGI_ERROR_REMOTE_OUTOFMEMORY: Final = -2005270492
DXGI_ERROR_ACCESS_LOST: Final = -2005270490
DXGI_ERROR_WAIT_TIMEOUT: Final = -2005270489
DXGI_ERROR_SESSION_DISCONNECTED: Final = -2005270488
DXGI_ERROR_RESTRICT_TO_OUTPUT_STALE: Final = -2005270487
DXGI_ERROR_CANNOT_PROTECT_CONTENT: Final = -2005270486
DXGI_ERROR_ACCESS_DENIED: Final = -2005270485
DXGI_ERROR_NAME_ALREADY_EXISTS: Final = -2005270484
DXGI_ERROR_SDK_COMPONENT_MISSING: Final = -2005270483
DXGI_ERROR_NOT_CURRENT: Final = -2005270482
DXGI_ERROR_HW_PROTECTION_OUTOFMEMORY: Final = -2005270480
DXGI_ERROR_DYNAMIC_CODE_POLICY_VIOLATION: Final = -2005270479
DXGI_ERROR_NON_COMPOSITED_UI: Final = -2005270478
DXCORE_ERROR_EVENT_NOT_UNREGISTERED: Final = -**********
PRESENTATION_ERROR_LOST: Final = -**********
DXGI_STATUS_UNOCCLUDED: Final = 0x087A0009
DXGI_STATUS_DDA_WAS_STILL_DRAWING: Final = 0x087A000A
DXGI_ERROR_MODE_CHANGE_IN_PROGRESS: Final = -**********
DXGI_STATUS_PRESENT_REQUIRED: Final = 0x087A002F
DXGI_ERROR_CACHE_CORRUPT: Final = -**********
DXGI_ERROR_CACHE_FULL: Final = -**********
DXGI_ERROR_CACHE_HASH_COLLISION: Final = -**********
DXGI_ERROR_ALREADY_EXISTS: Final = -**********
DXGI_ERROR_MPO_UNPINNED: Final = -**********
DXGI_DDI_ERR_WASSTILLDRAWING: Final = -**********
DXGI_DDI_ERR_UNSUPPORTED: Final = -**********
DXGI_DDI_ERR_NONEXCLUSIVE: Final = -**********
D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS: Final = -**********
D3D10_ERROR_FILE_NOT_FOUND: Final = -**********
D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS: Final = -**********
D3D11_ERROR_FILE_NOT_FOUND: Final = -**********
D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS: Final = -**********
D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD: Final = -**********
D3D12_ERROR_ADAPTER_NOT_FOUND: Final = -**********
D3D12_ERROR_DRIVER_VERSION_MISMATCH: Final = -**********
D3D12_ERROR_INVALID_REDIST: Final = -**********
D2DERR_WRONG_STATE: Final = -**********
D2DERR_NOT_INITIALIZED: Final = -**********
D2DERR_UNSUPPORTED_OPERATION: Final = -**********
D2DERR_SCANNER_FAILED: Final = -**********
D2DERR_SCREEN_ACCESS_DENIED: Final = -**********
D2DERR_DISPLAY_STATE_INVALID: Final = -**********
D2DERR_ZERO_VECTOR: Final = -2003238905
D2DERR_INTERNAL_ERROR: Final = -2003238904
D2DERR_DISPLAY_FORMAT_NOT_SUPPORTED: Final = -2003238903
D2DERR_INVALID_CALL: Final = -2003238902
D2DERR_NO_HARDWARE_DEVICE: Final = -2003238901
D2DERR_RECREATE_TARGET: Final = -2003238900
D2DERR_TOO_MANY_SHADER_ELEMENTS: Final = -2003238899
D2DERR_SHADER_COMPILE_FAILED: Final = -2003238898
D2DERR_MAX_TEXTURE_SIZE_EXCEEDED: Final = -2003238897
D2DERR_UNSUPPORTED_VERSION: Final = -2003238896
D2DERR_BAD_NUMBER: Final = -2003238895
D2DERR_WRONG_FACTORY: Final = -2003238894
D2DERR_LAYER_ALREADY_IN_USE: Final = -2003238893
D2DERR_POP_CALL_DID_NOT_MATCH_PUSH: Final = -2003238892
D2DERR_WRONG_RESOURCE_DOMAIN: Final = -2003238891
D2DERR_PUSH_POP_UNBALANCED: Final = -2003238890
D2DERR_RENDER_TARGET_HAS_LAYER_OR_CLIPRECT: Final = -2003238889
D2DERR_INCOMPATIBLE_BRUSH_TYPES: Final = -2003238888
D2DERR_WIN32_ERROR: Final = -2003238887
D2DERR_TARGET_NOT_GDI_COMPATIBLE: Final = -2003238886
D2DERR_TEXT_EFFECT_IS_WRONG_TYPE: Final = -2003238885
D2DERR_TEXT_RENDERER_NOT_RELEASED: Final = -2003238884
D2DERR_EXCEEDS_MAX_BITMAP_SIZE: Final = -2003238883
D2DERR_INVALID_GRAPH_CONFIGURATION: Final = -2003238882
D2DERR_INVALID_INTERNAL_GRAPH_CONFIGURATION: Final = -2003238881
D2DERR_CYCLIC_GRAPH: Final = -2003238880
D2DERR_BITMAP_CANNOT_DRAW: Final = -2003238879
D2DERR_OUTSTANDING_BITMAP_REFERENCES: Final = -2003238878
D2DERR_ORIGINAL_TARGET_NOT_BOUND: Final = -2003238877
D2DERR_INVALID_TARGET: Final = -2003238876
D2DERR_BITMAP_BOUND_AS_TARGET: Final = -2003238875
D2DERR_INSUFFICIENT_DEVICE_CAPABILITIES: Final = -2003238874
D2DERR_INTERMEDIATE_TOO_LARGE: Final = -2003238873
D2DERR_EFFECT_IS_NOT_REGISTERED: Final = -2003238872
D2DERR_INVALID_PROPERTY: Final = -2003238871
D2DERR_NO_SUBPROPERTIES: Final = -2003238870
D2DERR_PRINT_JOB_CLOSED: Final = -2003238869
D2DERR_PRINT_FORMAT_NOT_SUPPORTED: Final = -2003238868
D2DERR_TOO_MANY_TRANSFORM_INPUTS: Final = -2003238867
D2DERR_INVALID_GLYPH_IMAGE: Final = -2003238866
DWRITE_E_FILEFORMAT: Final = -2003283968
DWRITE_E_UNEXPECTED: Final = -2003283967
DWRITE_E_NOFONT: Final = -2003283966
DWRITE_E_FILENOTFOUND: Final = -2003283965
DWRITE_E_FILEACCESS: Final = -2003283964
DWRITE_E_FONTCOLLECTIONOBSOLETE: Final = -2003283963
DWRITE_E_ALREADYREGISTERED: Final = -2003283962
DWRITE_E_CACHEFORMAT: Final = -2003283961
DWRITE_E_CACHEVERSION: Final = -2003283960
DWRITE_E_UNSUPPORTEDOPERATION: Final = -2003283959
DWRITE_E_TEXTRENDERERINCOMPATIBLE: Final = -2003283958
DWRITE_E_FLOWDIRECTIONCONFLICTS: Final = -2003283957
DWRITE_E_NOCOLOR: Final = -2003283956
DWRITE_E_REMOTEFONT: Final = -2003283955
DWRITE_E_DOWNLOADCANCELLED: Final = -2003283954
DWRITE_E_DOWNLOADFAILED: Final = -2003283953
DWRITE_E_TOOMANYDOWNLOADS: Final = -2003283952
WINCODEC_ERR_WRONGSTATE: Final = -2003292412
WINCODEC_ERR_VALUEOUTOFRANGE: Final = -2003292411
WINCODEC_ERR_UNKNOWNIMAGEFORMAT: Final = -2003292409
WINCODEC_ERR_UNSUPPORTEDVERSION: Final = -2003292405
WINCODEC_ERR_NOTINITIALIZED: Final = -2003292404
WINCODEC_ERR_ALREADYLOCKED: Final = -2003292403
WINCODEC_ERR_PROPERTYNOTFOUND: Final = -2003292352
WINCODEC_ERR_PROPERTYNOTSUPPORTED: Final = -2003292351
WINCODEC_ERR_PROPERTYSIZE: Final = -2003292350
WINCODEC_ERR_CODECPRESENT: Final = -2003292349
WINCODEC_ERR_CODECNOTHUMBNAIL: Final = -2003292348
WINCODEC_ERR_PALETTEUNAVAILABLE: Final = -2003292347
WINCODEC_ERR_CODECTOOMANYSCANLINES: Final = -2003292346
WINCODEC_ERR_INTERNALERROR: Final = -2003292344
WINCODEC_ERR_SOURCERECTDOESNOTMATCHDIMENSIONS: Final = -2003292343
WINCODEC_ERR_COMPONENTNOTFOUND: Final = -2003292336
WINCODEC_ERR_IMAGESIZEOUTOFRANGE: Final = -2003292335
WINCODEC_ERR_TOOMUCHMETADATA: Final = -2003292334
WINCODEC_ERR_BADIMAGE: Final = -2003292320
WINCODEC_ERR_BADHEADER: Final = -2003292319
WINCODEC_ERR_FRAMEMISSING: Final = -2003292318
WINCODEC_ERR_BADMETADATAHEADER: Final = -2003292317
WINCODEC_ERR_BADSTREAMDATA: Final = -2003292304
WINCODEC_ERR_STREAMWRITE: Final = -2003292303
WINCODEC_ERR_STREAMREAD: Final = -2003292302
WINCODEC_ERR_STREAMNOTAVAILABLE: Final = -2003292301
WINCODEC_ERR_UNSUPPORTEDPIXELFORMAT: Final = -2003292288
WINCODEC_ERR_UNSUPPORTEDOPERATION: Final = -2003292287
WINCODEC_ERR_INVALIDREGISTRATION: Final = -2003292278
WINCODEC_ERR_COMPONENTINITIALIZEFAILURE: Final = -2003292277
WINCODEC_ERR_INSUFFICIENTBUFFER: Final = -2003292276
WINCODEC_ERR_DUPLICATEMETADATAPRESENT: Final = -2003292275
WINCODEC_ERR_PROPERTYUNEXPECTEDTYPE: Final = -2003292274
WINCODEC_ERR_UNEXPECTEDSIZE: Final = -2003292273
WINCODEC_ERR_INVALIDQUERYREQUEST: Final = -2003292272
WINCODEC_ERR_UNEXPECTEDMETADATATYPE: Final = -2003292271
WINCODEC_ERR_REQUESTONLYVALIDATMETADATAROOT: Final = -2003292270
WINCODEC_ERR_INVALIDQUERYCHARACTER: Final = -2003292269
WINCODEC_ERR_WIN32ERROR: Final = -2003292268
WINCODEC_ERR_INVALIDPROGRESSIVELEVEL: Final = -2003292267
WINCODEC_ERR_INVALIDJPEGSCANINDEX: Final = -2003292266
MILERR_OBJECTBUSY: Final = -2003304447
MILERR_INSUFFICIENTBUFFER: Final = -2003304446
MILERR_WIN32ERROR: Final = -2003304445
MILERR_SCANNER_FAILED: Final = -2003304444
MILERR_SCREENACCESSDENIED: Final = -2003304443
MILERR_DISPLAYSTATEINVALID: Final = -2003304442
MILERR_NONINVERTIBLEMATRIX: Final = -2003304441
MILERR_ZEROVECTOR: Final = -2003304440
MILERR_TERMINATED: Final = -2003304439
MILERR_BADNUMBER: Final = -2003304438
MILERR_INTERNALERROR: Final = -2003304320
MILERR_DISPLAYFORMATNOTSUPPORTED: Final = -2003304316
MILERR_INVALIDCALL: Final = -2003304315
MILERR_ALREADYLOCKED: Final = -2003304314
MILERR_NOTLOCKED: Final = -2003304313
MILERR_DEVICECANNOTRENDERTEXT: Final = -2003304312
MILERR_GLYPHBITMAPMISSED: Final = -2003304311
MILERR_MALFORMEDGLYPHCACHE: Final = -2003304310
MILERR_GENERIC_IGNORE: Final = -2003304309
MILERR_MALFORMED_GUIDELINE_DATA: Final = -2003304308
MILERR_NO_HARDWARE_DEVICE: Final = -2003304307
MILERR_NEED_RECREATE_AND_PRESENT: Final = -2003304306
MILERR_ALREADY_INITIALIZED: Final = -2003304305
MILERR_MISMATCHED_SIZE: Final = -2003304304
MILERR_NO_REDIRECTION_SURFACE_AVAILABLE: Final = -2003304303
MILERR_REMOTING_NOT_SUPPORTED: Final = -2003304302
MILERR_QUEUED_PRESENT_NOT_SUPPORTED: Final = -2003304301
MILERR_NOT_QUEUING_PRESENTS: Final = -2003304300
MILERR_NO_REDIRECTION_SURFACE_RETRY_LATER: Final = -2003304299
MILERR_TOOMANYSHADERELEMNTS: Final = -2003304298
MILERR_MROW_READLOCK_FAILED: Final = -2003304297
MILERR_MROW_UPDATE_FAILED: Final = -2003304296
MILERR_SHADER_COMPILE_FAILED: Final = -2003304295
MILERR_MAX_TEXTURE_SIZE_EXCEEDED: Final = -2003304294
MILERR_QPC_TIME_WENT_BACKWARD: Final = -2003304293
MILERR_DXGI_ENUMERATION_OUT_OF_SYNC: Final = -2003304291
MILERR_ADAPTER_NOT_FOUND: Final = -2003304290
MILERR_COLORSPACE_NOT_SUPPORTED: Final = -2003304289
MILERR_PREFILTER_NOT_SUPPORTED: Final = -2003304288
MILERR_DISPLAYID_ACCESS_DENIED: Final = -2003304287
UCEERR_INVALIDPACKETHEADER: Final = -2003303424
UCEERR_UNKNOWNPACKET: Final = -2003303423
UCEERR_ILLEGALPACKET: Final = -2003303422
UCEERR_MALFORMEDPACKET: Final = -2003303421
UCEERR_ILLEGALHANDLE: Final = -2003303420
UCEERR_HANDLELOOKUPFAILED: Final = -2003303419
UCEERR_RENDERTHREADFAILURE: Final = -2003303418
UCEERR_CTXSTACKFRSTTARGETNULL: Final = -2003303417
UCEERR_CONNECTIONIDLOOKUPFAILED: Final = -2003303416
UCEERR_BLOCKSFULL: Final = -2003303415
UCEERR_MEMORYFAILURE: Final = -2003303414
UCEERR_PACKETRECORDOUTOFRANGE: Final = -2003303413
UCEERR_ILLEGALRECORDTYPE: Final = -2003303412
UCEERR_OUTOFHANDLES: Final = -2003303411
UCEERR_UNCHANGABLE_UPDATE_ATTEMPTED: Final = -2003303410
UCEERR_NO_MULTIPLE_WORKER_THREADS: Final = -2003303409
UCEERR_REMOTINGNOTSUPPORTED: Final = -2003303408
UCEERR_MISSINGENDCOMMAND: Final = -2003303407
UCEERR_MISSINGBEGINCOMMAND: Final = -2003303406
UCEERR_CHANNELSYNCTIMEDOUT: Final = -2003303405
UCEERR_CHANNELSYNCABANDONED: Final = -2003303404
UCEERR_UNSUPPORTEDTRANSPORTVERSION: Final = -2003303403
UCEERR_TRANSPORTUNAVAILABLE: Final = -2003303402
UCEERR_FEEDBACK_UNSUPPORTED: Final = -2003303401
UCEERR_COMMANDTRANSPORTDENIED: Final = -2003303400
UCEERR_GRAPHICSSTREAMUNAVAILABLE: Final = -2003303399
UCEERR_GRAPHICSSTREAMALREADYOPEN: Final = -2003303392
UCEERR_TRANSPORTDISCONNECTED: Final = -2003303391
UCEERR_TRANSPORTOVERLOADED: Final = -2003303390
UCEERR_PARTITION_ZOMBIED: Final = -2003303389
MILAVERR_NOCLOCK: Final = -2003303168
MILAVERR_NOMEDIATYPE: Final = -2003303167
MILAVERR_NOVIDEOMIXER: Final = -2003303166
MILAVERR_NOVIDEOPRESENTER: Final = -2003303165
MILAVERR_NOREADYFRAMES: Final = -2003303164
MILAVERR_MODULENOTLOADED: Final = -2003303163
MILAVERR_WMPFACTORYNOTREGISTERED: Final = -2003303162
MILAVERR_INVALIDWMPVERSION: Final = -2003303161
MILAVERR_INSUFFICIENTVIDEORESOURCES: Final = -2003303160
MILAVERR_VIDEOACCELERATIONNOTAVAILABLE: Final = -2003303159
MILAVERR_REQUESTEDTEXTURETOOBIG: Final = -2003303158
MILAVERR_SEEKFAILED: Final = -2003303157
MILAVERR_UNEXPECTEDWMPFAILURE: Final = -2003303156
MILAVERR_MEDIAPLAYERCLOSED: Final = -2003303155
MILAVERR_UNKNOWNHARDWAREERROR: Final = -2003303154
MILEFFECTSERR_UNKNOWNPROPERTY: Final = -2003302898
MILEFFECTSERR_EFFECTNOTPARTOFGROUP: Final = -2003302897
MILEFFECTSERR_NOINPUTSOURCEATTACHED: Final = -2003302896
MILEFFECTSERR_CONNECTORNOTCONNECTED: Final = -2003302895
MILEFFECTSERR_CONNECTORNOTASSOCIATEDWITHEFFECT: Final = -2003302894
MILEFFECTSERR_RESERVED: Final = -2003302893
MILEFFECTSERR_CYCLEDETECTED: Final = -2003302892
MILEFFECTSERR_EFFECTINMORETHANONEGRAPH: Final = -2003302891
MILEFFECTSERR_EFFECTALREADYINAGRAPH: Final = -2003302890
MILEFFECTSERR_EFFECTHASNOCHILDREN: Final = -2003302889
MILEFFECTSERR_ALREADYATTACHEDTOLISTENER: Final = -2003302888
MILEFFECTSERR_NOTAFFINETRANSFORM: Final = -2003302887
MILEFFECTSERR_EMPTYBOUNDS: Final = -2003302886
MILEFFECTSERR_OUTPUTSIZETOOLARGE: Final = -2003302885
DWMERR_STATE_TRANSITION_FAILED: Final = -**********
DWMERR_THEME_FAILED: Final = -**********
DWMERR_CATASTROPHIC_FAILURE: Final = -**********
DCOMPOSITION_ERROR_WINDOW_ALREADY_COMPOSED: Final = -**********
DCOMPOSITION_ERROR_SURFACE_BEING_RENDERED: Final = -**********
DCOMPOSITION_ERROR_SURFACE_NOT_BEING_RENDERED: Final = -**********
ONL_E_INVALID_AUTHENTICATION_TARGET: Final = -**********
ONL_E_ACCESS_DENIED_BY_TOU: Final = -**********
ONL_E_INVALID_APPLICATION: Final = -**********
ONL_E_PASSWORD_UPDATE_REQUIRED: Final = -**********
ONL_E_ACCOUNT_UPDATE_REQUIRED: Final = -**********
ONL_E_FORCESIGNIN: Final = -**********
ONL_E_ACCOUNT_LOCKED: Final = -**********
ONL_E_PARENTAL_CONSENT_REQUIRED: Final = -**********
ONL_E_EMAIL_VERIFICATION_REQUIRED: Final = -**********
ONL_E_ACCOUNT_SUSPENDED_COMPROIMISE: Final = -**********
ONL_E_ACCOUNT_SUSPENDED_ABUSE: Final = -**********
ONL_E_ACTION_REQUIRED: Final = -**********
ONL_CONNECTION_COUNT_LIMIT: Final = -**********
ONL_E_CONNECTED_ACCOUNT_CAN_NOT_SIGNOUT: Final = -**********
ONL_E_USER_AUTHENTICATION_REQUIRED: Final = -**********
ONL_E_REQUEST_THROTTLED: Final = -**********
FA_E_MAX_PERSISTED_ITEMS_REACHED: Final = -**********
FA_E_HOMEGROUP_NOT_AVAILABLE: Final = -**********
E_MONITOR_RESOLUTION_TOO_LOW: Final = -**********
E_ELEVATED_ACTIVATION_NOT_SUPPORTED: Final = -**********
E_UAC_DISABLED: Final = -**********
E_FULL_ADMIN_NOT_SUPPORTED: Final = -**********
E_APPLICATION_NOT_REGISTERED: Final = -**********
E_MULTIPLE_EXTENSIONS_FOR_APPLICATION: Final = -**********
E_MULTIPLE_PACKAGES_FOR_FAMILY: Final = -**********
E_APPLICATION_MANAGER_NOT_RUNNING: Final = -**********
S_STORE_LAUNCHED_FOR_REMEDIATION: Final = 0x00270258
S_APPLICATION_ACTIVATION_ERROR_HANDLED_BY_DIALOG: Final = 0x00270259
E_APPLICATION_ACTIVATION_TIMED_OUT: Final = -**********
E_APPLICATION_ACTIVATION_EXEC_FAILURE: Final = -**********
E_APPLICATION_TEMPORARY_LICENSE_ERROR: Final = -**********
E_APPLICATION_TRIAL_LICENSE_EXPIRED: Final = -**********
E_SKYDRIVE_ROOT_TARGET_FILE_SYSTEM_NOT_SUPPORTED: Final = -**********
E_SKYDRIVE_ROOT_TARGET_OVERLAP: Final = -**********
E_SKYDRIVE_ROOT_TARGET_CANNOT_INDEX: Final = -**********
E_SKYDRIVE_FILE_NOT_UPLOADED: Final = -**********
E_SKYDRIVE_UPDATE_AVAILABILITY_FAIL: Final = -**********
E_SKYDRIVE_ROOT_TARGET_VOLUME_ROOT_NOT_SUPPORTED: Final = -**********
E_SYNCENGINE_FILE_SIZE_OVER_LIMIT: Final = -**********
E_SYNCENGINE_FILE_SIZE_EXCEEDS_REMAINING_QUOTA: Final = -**********
E_SYNCENGINE_UNSUPPORTED_FILE_NAME: Final = -**********
E_SYNCENGINE_FOLDER_ITEM_COUNT_LIMIT_EXCEEDED: Final = -**********
E_SYNCENGINE_FILE_SYNC_PARTNER_ERROR: Final = -**********
E_SYNCENGINE_SYNC_PAUSED_BY_SERVICE: Final = -**********
E_SYNCENGINE_FILE_IDENTIFIER_UNKNOWN: Final = -**********
E_SYNCENGINE_SERVICE_AUTHENTICATION_FAILED: Final = -**********
E_SYNCENGINE_UNKNOWN_SERVICE_ERROR: Final = -**********
E_SYNCENGINE_SERVICE_RETURNED_UNEXPECTED_SIZE: Final = -**********
E_SYNCENGINE_REQUEST_BLOCKED_BY_SERVICE: Final = -2013085690
E_SYNCENGINE_REQUEST_BLOCKED_DUE_TO_CLIENT_ERROR: Final = -2013085689
E_SYNCENGINE_FOLDER_INACCESSIBLE: Final = -2013081599
E_SYNCENGINE_UNSUPPORTED_FOLDER_NAME: Final = -2013081598
E_SYNCENGINE_UNSUPPORTED_MARKET: Final = -2013081597
E_SYNCENGINE_PATH_LENGTH_LIMIT_EXCEEDED: Final = -2013081596
E_SYNCENGINE_REMOTE_PATH_LENGTH_LIMIT_EXCEEDED: Final = -2013081595
E_SYNCENGINE_CLIENT_UPDATE_NEEDED: Final = -2013081594
E_SYNCENGINE_PROXY_AUTHENTICATION_REQUIRED: Final = -2013081593
E_SYNCENGINE_STORAGE_SERVICE_PROVISIONING_FAILED: Final = -2013081592
E_SYNCENGINE_UNSUPPORTED_REPARSE_POINT: Final = -2013081591
E_SYNCENGINE_STORAGE_SERVICE_BLOCKED: Final = -2013081590
E_SYNCENGINE_FOLDER_IN_REDIRECTION: Final = -2013081589
EAS_E_POLICY_NOT_MANAGED_BY_OS: Final = -2141913087
EAS_E_POLICY_COMPLIANT_WITH_ACTIONS: Final = -2141913086
EAS_E_REQUESTED_POLICY_NOT_ENFORCEABLE: Final = -2141913085
EAS_E_CURRENT_USER_HAS_BLANK_PASSWORD: Final = -2141913084
EAS_E_REQUESTED_POLICY_PASSWORD_EXPIRATION_INCOMPATIBLE: Final = -2141913083
EAS_E_USER_CANNOT_CHANGE_PASSWORD: Final = -2141913082
EAS_E_ADMINS_HAVE_BLANK_PASSWORD: Final = -2141913081
EAS_E_ADMINS_CANNOT_CHANGE_PASSWORD: Final = -2141913080
EAS_E_LOCAL_CONTROLLED_USERS_CANNOT_CHANGE_PASSWORD: Final = -2141913079
EAS_E_PASSWORD_POLICY_NOT_ENFORCEABLE_FOR_CONNECTED_ADMINS: Final = -2141913078
EAS_E_CONNECTED_ADMINS_NEED_TO_CHANGE_PASSWORD: Final = -2141913077
EAS_E_PASSWORD_POLICY_NOT_ENFORCEABLE_FOR_CURRENT_CONNECTED_USER: Final = -2141913076
EAS_E_CURRENT_CONNECTED_USER_NEED_TO_CHANGE_PASSWORD: Final = -2141913075
WEB_E_UNSUPPORTED_FORMAT: Final = -2089484287
WEB_E_INVALID_XML: Final = -2089484286
WEB_E_MISSING_REQUIRED_ELEMENT: Final = -2089484285
WEB_E_MISSING_REQUIRED_ATTRIBUTE: Final = -2089484284
WEB_E_UNEXPECTED_CONTENT: Final = -2089484283
WEB_E_RESOURCE_TOO_LARGE: Final = -2089484282
WEB_E_INVALID_JSON_STRING: Final = -2089484281
WEB_E_INVALID_JSON_NUMBER: Final = -2089484280
WEB_E_JSON_VALUE_NOT_FOUND: Final = -2089484279
HTTP_E_STATUS_UNEXPECTED: Final = -2145845247
HTTP_E_STATUS_UNEXPECTED_REDIRECTION: Final = -2145845245
HTTP_E_STATUS_UNEXPECTED_CLIENT_ERROR: Final = -2145845244
HTTP_E_STATUS_UNEXPECTED_SERVER_ERROR: Final = -2145845243
HTTP_E_STATUS_AMBIGUOUS: Final = -2145844948
HTTP_E_STATUS_MOVED: Final = -2145844947
HTTP_E_STATUS_REDIRECT: Final = -2145844946
HTTP_E_STATUS_REDIRECT_METHOD: Final = -2145844945
HTTP_E_STATUS_NOT_MODIFIED: Final = -2145844944
HTTP_E_STATUS_USE_PROXY: Final = -2145844943
HTTP_E_STATUS_REDIRECT_KEEP_VERB: Final = -2145844941
HTTP_E_STATUS_BAD_REQUEST: Final = -2145844848
HTTP_E_STATUS_DENIED: Final = -2145844847
HTTP_E_STATUS_PAYMENT_REQ: Final = -2145844846
HTTP_E_STATUS_FORBIDDEN: Final = -2145844845
HTTP_E_STATUS_NOT_FOUND: Final = -2145844844
HTTP_E_STATUS_BAD_METHOD: Final = -2145844843
HTTP_E_STATUS_NONE_ACCEPTABLE: Final = -2145844842
HTTP_E_STATUS_PROXY_AUTH_REQ: Final = -2145844841
HTTP_E_STATUS_REQUEST_TIMEOUT: Final = -2145844840
HTTP_E_STATUS_CONFLICT: Final = -2145844839
HTTP_E_STATUS_GONE: Final = -2145844838
HTTP_E_STATUS_LENGTH_REQUIRED: Final = -2145844837
HTTP_E_STATUS_PRECOND_FAILED: Final = -2145844836
HTTP_E_STATUS_REQUEST_TOO_LARGE: Final = -2145844835
HTTP_E_STATUS_URI_TOO_LONG: Final = -2145844834
HTTP_E_STATUS_UNSUPPORTED_MEDIA: Final = -2145844833
HTTP_E_STATUS_RANGE_NOT_SATISFIABLE: Final = -2145844832
HTTP_E_STATUS_EXPECTATION_FAILED: Final = -2145844831
HTTP_E_STATUS_SERVER_ERROR: Final = -2145844748
HTTP_E_STATUS_NOT_SUPPORTED: Final = -2145844747
HTTP_E_STATUS_BAD_GATEWAY: Final = -2145844746
HTTP_E_STATUS_SERVICE_UNAVAIL: Final = -2145844745
HTTP_E_STATUS_GATEWAY_TIMEOUT: Final = -2145844744
HTTP_E_STATUS_VERSION_NOT_SUP: Final = -2145844743
E_INVALID_PROTOCOL_OPERATION: Final = -2089418751
E_INVALID_PROTOCOL_FORMAT: Final = -2089418750
E_PROTOCOL_EXTENSIONS_NOT_SUPPORTED: Final = -2089418749
E_SUBPROTOCOL_NOT_SUPPORTED: Final = -2089418748
E_PROTOCOL_VERSION_NOT_SUPPORTED: Final = -2089418747
INPUT_E_OUT_OF_ORDER: Final = -2143289344
INPUT_E_REENTRANCY: Final = -2143289343
INPUT_E_MULTIMODAL: Final = -2143289342
INPUT_E_PACKET: Final = -2143289341
INPUT_E_FRAME: Final = -2143289340
INPUT_E_HISTORY: Final = -2143289339
INPUT_E_DEVICE_INFO: Final = -2143289338
INPUT_E_TRANSFORM: Final = -2143289337
INPUT_E_DEVICE_PROPERTY: Final = -2143289336
INET_E_INVALID_URL: Final = -2146697214
INET_E_NO_SESSION: Final = -2146697213
INET_E_CANNOT_CONNECT: Final = -2146697212
INET_E_RESOURCE_NOT_FOUND: Final = -2146697211
INET_E_OBJECT_NOT_FOUND: Final = -2146697210
INET_E_DATA_NOT_AVAILABLE: Final = -2146697209
INET_E_DOWNLOAD_FAILURE: Final = -2146697208
INET_E_AUTHENTICATION_REQUIRED: Final = -2146697207
INET_E_NO_VALID_MEDIA: Final = -2146697206
INET_E_CONNECTION_TIMEOUT: Final = -2146697205
INET_E_INVALID_REQUEST: Final = -2146697204
INET_E_UNKNOWN_PROTOCOL: Final = -2146697203
INET_E_SECURITY_PROBLEM: Final = -2146697202
INET_E_CANNOT_LOAD_DATA: Final = -2146697201
INET_E_CANNOT_INSTANTIATE_OBJECT: Final = -2146697200
INET_E_INVALID_CERTIFICATE: Final = -2146697191
INET_E_REDIRECT_FAILED: Final = -2146697196
INET_E_REDIRECT_TO_DIR: Final = -2146697195
ERROR_DBG_CREATE_PROCESS_FAILURE_LOCKDOWN: Final = -2135949311
ERROR_DBG_ATTACH_PROCESS_FAILURE_LOCKDOWN: Final = -2135949310
ERROR_DBG_CONNECT_SERVER_FAILURE_LOCKDOWN: Final = -**********
ERROR_DBG_START_SERVER_FAILURE_LOCKDOWN: Final = -**********
HSP_E_ERROR_MASK: Final = -**********
HSP_E_INTERNAL_ERROR: Final = -**********
HSP_BS_ERROR_MASK: Final = -**********
HSP_BS_INTERNAL_ERROR: Final = -**********
HSP_DRV_ERROR_MASK: Final = -**********
HSP_DRV_INTERNAL_ERROR: Final = -**********
HSP_BASE_ERROR_MASK: Final = -**********
HSP_BASE_INTERNAL_ERROR: Final = -**********
HSP_KSP_ERROR_MASK: Final = -**********
HSP_KSP_DEVICE_NOT_READY: Final = -**********
HSP_KSP_INVALID_PROVIDER_HANDLE: Final = -**********
HSP_KSP_INVALID_KEY_HANDLE: Final = -**********
HSP_KSP_INVALID_PARAMETER: Final = -**********
HSP_KSP_BUFFER_TOO_SMALL: Final = -**********
HSP_KSP_NOT_SUPPORTED: Final = -**********
HSP_KSP_INVALID_DATA: Final = -**********
HSP_KSP_INVALID_FLAGS: Final = -**********
HSP_KSP_ALGORITHM_NOT_SUPPORTED: Final = -**********
HSP_KSP_KEY_ALREADY_FINALIZED: Final = -**********
HSP_KSP_KEY_NOT_FINALIZED: Final = -**********
HSP_KSP_INVALID_KEY_TYPE: Final = -**********
HSP_KSP_NO_MEMORY: Final = -**********
HSP_KSP_PARAMETER_NOT_SET: Final = -**********
HSP_KSP_KEY_EXISTS: Final = -**********
HSP_KSP_KEY_MISSING: Final = -**********
HSP_KSP_KEY_LOAD_FAIL: Final = -**********
HSP_KSP_NO_MORE_ITEMS: Final = -**********
HSP_KSP_INTERNAL_ERROR: Final = -**********
ERROR_IO_PREEMPTED: Final = -**********
JSCRIPT_E_CANTEXECUTE: Final = -**********
WEP_E_NOT_PROVISIONED_ON_ALL_VOLUMES: Final = -**********
WEP_E_FIXED_DATA_NOT_SUPPORTED: Final = -**********
WEP_E_HARDWARE_NOT_COMPLIANT: Final = -**********
WEP_E_LOCK_NOT_CONFIGURED: Final = -**********
WEP_E_PROTECTION_SUSPENDED: Final = -**********
WEP_E_NO_LICENSE: Final = -**********
WEP_E_OS_NOT_PROTECTED: Final = -**********
WEP_E_UNEXPECTED_FAIL: Final = -**********
WEP_E_BUFFER_TOO_LARGE: Final = -**********
ERROR_SVHDX_ERROR_STORED: Final = -**********
ERROR_SVHDX_ERROR_NOT_AVAILABLE: Final = -**********
ERROR_SVHDX_UNIT_ATTENTION_AVAILABLE: Final = -**********
ERROR_SVHDX_UNIT_ATTENTION_CAPACITY_DATA_CHANGED: Final = -**********
ERROR_SVHDX_UNIT_ATTENTION_RESERVATIONS_PREEMPTED: Final = -**********
ERROR_SVHDX_UNIT_ATTENTION_RESERVATIONS_RELEASED: Final = -**********
ERROR_SVHDX_UNIT_ATTENTION_REGISTRATIONS_PREEMPTED: Final = -**********
ERROR_SVHDX_UNIT_ATTENTION_OPERATING_DEFINITION_CHANGED: Final = -**********
ERROR_SVHDX_RESERVATION_CONFLICT: Final = -**********
ERROR_SVHDX_WRONG_FILE_TYPE: Final = -**********
ERROR_SVHDX_VERSION_MISMATCH: Final = -**********
ERROR_VHD_SHARED: Final = -**********
ERROR_SVHDX_NO_INITIATOR: Final = -**********
ERROR_VHDSET_BACKING_STORAGE_NOT_FOUND: Final = -**********
ERROR_SMB_NO_PREAUTH_INTEGRITY_HASH_OVERLAP: Final = -1067646976
ERROR_SMB_BAD_CLUSTER_DIALECT: Final = -1067646975
ERROR_SMB_NO_SIGNING_ALGORITHM_OVERLAP: Final = -1067646974
WININET_E_OUT_OF_HANDLES: Final = -2147012895
WININET_E_TIMEOUT: Final = -2147012894
WININET_E_EXTENDED_ERROR: Final = -2147012893
WININET_E_INTERNAL_ERROR: Final = -2147012892
WININET_E_INVALID_URL: Final = -2147012891
WININET_E_UNRECOGNIZED_SCHEME: Final = -2147012890
WININET_E_NAME_NOT_RESOLVED: Final = -2147012889
WININET_E_PROTOCOL_NOT_FOUND: Final = -2147012888
WININET_E_INVALID_OPTION: Final = -2147012887
WININET_E_BAD_OPTION_LENGTH: Final = -2147012886
WININET_E_OPTION_NOT_SETTABLE: Final = -2147012885
WININET_E_SHUTDOWN: Final = -2147012884
WININET_E_INCORRECT_USER_NAME: Final = -2147012883
WININET_E_INCORRECT_PASSWORD: Final = -2147012882
WININET_E_LOGIN_FAILURE: Final = -2147012881
WININET_E_INVALID_OPERATION: Final = -2147012880
WININET_E_OPERATION_CANCELLED: Final = -2147012879
WININET_E_INCORRECT_HANDLE_TYPE: Final = -2147012878
WININET_E_INCORRECT_HANDLE_STATE: Final = -2147012877
WININET_E_NOT_PROXY_REQUEST: Final = -2147012876
WININET_E_REGISTRY_VALUE_NOT_FOUND: Final = -2147012875
WININET_E_BAD_REGISTRY_PARAMETER: Final = -2147012874
WININET_E_NO_DIRECT_ACCESS: Final = -2147012873
WININET_E_NO_CONTEXT: Final = -2147012872
WININET_E_NO_CALLBACK: Final = -2147012871
WININET_E_REQUEST_PENDING: Final = -2147012870
WININET_E_INCORRECT_FORMAT: Final = -2147012869
WININET_E_ITEM_NOT_FOUND: Final = -2147012868
WININET_E_CANNOT_CONNECT: Final = -2147012867
WININET_E_CONNECTION_ABORTED: Final = -2147012866
WININET_E_CONNECTION_RESET: Final = -2147012865
WININET_E_FORCE_RETRY: Final = -2147012864
WININET_E_INVALID_PROXY_REQUEST: Final = -2147012863
WININET_E_NEED_UI: Final = -2147012862
WININET_E_HANDLE_EXISTS: Final = -2147012860
WININET_E_SEC_CERT_DATE_INVALID: Final = -2147012859
WININET_E_SEC_CERT_CN_INVALID: Final = -2147012858
WININET_E_HTTP_TO_HTTPS_ON_REDIR: Final = -2147012857
WININET_E_HTTPS_TO_HTTP_ON_REDIR: Final = -2147012856
WININET_E_MIXED_SECURITY: Final = -2147012855
WININET_E_CHG_POST_IS_NON_SECURE: Final = -2147012854
WININET_E_POST_IS_NON_SECURE: Final = -2147012853
WININET_E_CLIENT_AUTH_CERT_NEEDED: Final = -2147012852
WININET_E_INVALID_CA: Final = -2147012851
WININET_E_CLIENT_AUTH_NOT_SETUP: Final = -2147012850
WININET_E_ASYNC_THREAD_FAILED: Final = -2147012849
WININET_E_REDIRECT_SCHEME_CHANGE: Final = -2147012848
WININET_E_DIALOG_PENDING: Final = -2147012847
WININET_E_RETRY_DIALOG: Final = -2147012846
WININET_E_NO_NEW_CONTAINERS: Final = -2147012845
WININET_E_HTTPS_HTTP_SUBMIT_REDIR: Final = -2147012844
WININET_E_SEC_CERT_ERRORS: Final = -2147012841
WININET_E_SEC_CERT_REV_FAILED: Final = -2147012839
WININET_E_HEADER_NOT_FOUND: Final = -2147012746
WININET_E_DOWNLEVEL_SERVER: Final = -2147012745
WININET_E_INVALID_SERVER_RESPONSE: Final = -2147012744
WININET_E_INVALID_HEADER: Final = -2147012743
WININET_E_INVALID_QUERY_REQUEST: Final = -2147012742
WININET_E_HEADER_ALREADY_EXISTS: Final = -2147012741
WININET_E_REDIRECT_FAILED: Final = -2147012740
WININET_E_SECURITY_CHANNEL_ERROR: Final = -2147012739
WININET_E_UNABLE_TO_CACHE_FILE: Final = -2147012738
WININET_E_TCPIP_NOT_INSTALLED: Final = -2147012737
WININET_E_DISCONNECTED: Final = -2147012733
WININET_E_SERVER_UNREACHABLE: Final = -2147012732
WININET_E_PROXY_SERVER_UNREACHABLE: Final = -2147012731
WININET_E_BAD_AUTO_PROXY_SCRIPT: Final = -2147012730
WININET_E_UNABLE_TO_DOWNLOAD_SCRIPT: Final = -2147012729
WININET_E_SEC_INVALID_CERT: Final = -2147012727
WININET_E_SEC_CERT_REVOKED: Final = -2147012726
WININET_E_FAILED_DUETOSECURITYCHECK: Final = -2147012725
WININET_E_NOT_INITIALIZED: Final = -2147012724
WININET_E_LOGIN_FAILURE_DISPLAY_ENTITY_BODY: Final = -2147012722
WININET_E_DECODING_FAILED: Final = -2147012721
WININET_E_NOT_REDIRECTED: Final = -2147012736
WININET_E_COOKIE_NEEDS_CONFIRMATION: Final = -2147012735
WININET_E_COOKIE_DECLINED: Final = -2147012734
WININET_E_REDIRECT_NEEDS_CONFIRMATION: Final = -2147012728
SQLITE_E_ERROR: Final = -2018574335
SQLITE_E_INTERNAL: Final = -2018574334
SQLITE_E_PERM: Final = -2018574333
SQLITE_E_ABORT: Final = -2018574332
SQLITE_E_BUSY: Final = -2018574331
SQLITE_E_LOCKED: Final = -2018574330
SQLITE_E_NOMEM: Final = -2018574329
SQLITE_E_READONLY: Final = -2018574328
SQLITE_E_INTERRUPT: Final = -2018574327
SQLITE_E_IOERR: Final = -2018574326
SQLITE_E_CORRUPT: Final = -2018574325
SQLITE_E_NOTFOUND: Final = -2018574324
SQLITE_E_FULL: Final = -2018574323
SQLITE_E_CANTOPEN: Final = -2018574322
SQLITE_E_PROTOCOL: Final = -2018574321
SQLITE_E_EMPTY: Final = -2018574320
SQLITE_E_SCHEMA: Final = -2018574319
SQLITE_E_TOOBIG: Final = -2018574318
SQLITE_E_CONSTRAINT: Final = -2018574317
SQLITE_E_MISMATCH: Final = -2018574316
SQLITE_E_MISUSE: Final = -2018574315
SQLITE_E_NOLFS: Final = -2018574314
SQLITE_E_AUTH: Final = -2018574313
SQLITE_E_FORMAT: Final = -2018574312
SQLITE_E_RANGE: Final = -2018574311
SQLITE_E_NOTADB: Final = -2018574310
SQLITE_E_NOTICE: Final = -2018574309
SQLITE_E_WARNING: Final = -2018574308
SQLITE_E_ROW: Final = -2018574236
SQLITE_E_DONE: Final = -2018574235
SQLITE_E_IOERR_READ: Final = -2018574070
SQLITE_E_IOERR_SHORT_READ: Final = -2018573814
SQLITE_E_IOERR_WRITE: Final = -2018573558
SQLITE_E_IOERR_FSYNC: Final = -2018573302
SQLITE_E_IOERR_DIR_FSYNC: Final = -2018573046
SQLITE_E_IOERR_TRUNCATE: Final = -2018572790
SQLITE_E_IOERR_FSTAT: Final = -2018572534
SQLITE_E_IOERR_UNLOCK: Final = -2018572278
SQLITE_E_IOERR_RDLOCK: Final = -2018572022
SQLITE_E_IOERR_DELETE: Final = -2018571766
SQLITE_E_IOERR_BLOCKED: Final = -2018571510
SQLITE_E_IOERR_NOMEM: Final = -2018571254
SQLITE_E_IOERR_ACCESS: Final = -2018570998
SQLITE_E_IOERR_CHECKRESERVEDLOCK: Final = -2018570742
SQLITE_E_IOERR_LOCK: Final = -2018570486
SQLITE_E_IOERR_CLOSE: Final = -2018570230
SQLITE_E_IOERR_DIR_CLOSE: Final = -2018569974
SQLITE_E_IOERR_SHMOPEN: Final = -2018569718
SQLITE_E_IOERR_SHMSIZE: Final = -2018569462
SQLITE_E_IOERR_SHMLOCK: Final = -2018569206
SQLITE_E_IOERR_SHMMAP: Final = -2018568950
SQLITE_E_IOERR_SEEK: Final = -2018568694
SQLITE_E_IOERR_DELETE_NOENT: Final = -2018568438
SQLITE_E_IOERR_MMAP: Final = -2018568182
SQLITE_E_IOERR_GETTEMPPATH: Final = -2018567926
SQLITE_E_IOERR_CONVPATH: Final = -2018567670
SQLITE_E_IOERR_VNODE: Final = -2018567678
SQLITE_E_IOERR_AUTH: Final = -2018567677
SQLITE_E_LOCKED_SHAREDCACHE: Final = -2018574074
SQLITE_E_BUSY_RECOVERY: Final = -2018574075
SQLITE_E_BUSY_SNAPSHOT: Final = -2018573819
SQLITE_E_CANTOPEN_NOTEMPDIR: Final = -2018574066
SQLITE_E_CANTOPEN_ISDIR: Final = -2018573810
SQLITE_E_CANTOPEN_FULLPATH: Final = -2018573554
SQLITE_E_CANTOPEN_CONVPATH: Final = -2018573298
SQLITE_E_CORRUPT_VTAB: Final = -2018574069
SQLITE_E_READONLY_RECOVERY: Final = -2018574072
SQLITE_E_READONLY_CANTLOCK: Final = -2018573816
SQLITE_E_READONLY_ROLLBACK: Final = -2018573560
SQLITE_E_READONLY_DBMOVED: Final = -2018573304
SQLITE_E_ABORT_ROLLBACK: Final = -2018573820
SQLITE_E_CONSTRAINT_CHECK: Final = -2018574061
SQLITE_E_CONSTRAINT_COMMITHOOK: Final = -2018573805
SQLITE_E_CONSTRAINT_FOREIGNKEY: Final = -2018573549
SQLITE_E_CONSTRAINT_FUNCTION: Final = -2018573293
SQLITE_E_CONSTRAINT_NOTNULL: Final = -2018573037
SQLITE_E_CONSTRAINT_PRIMARYKEY: Final = -2018572781
SQLITE_E_CONSTRAINT_TRIGGER: Final = -2018572525
SQLITE_E_CONSTRAINT_UNIQUE: Final = -2018572269
SQLITE_E_CONSTRAINT_VTAB: Final = -2018572013
SQLITE_E_CONSTRAINT_ROWID: Final = -2018571757
SQLITE_E_NOTICE_RECOVER_WAL: Final = -2018574053
SQLITE_E_NOTICE_RECOVER_ROLLBACK: Final = -2018573797
SQLITE_E_WARNING_AUTOINDEX: Final = -2018574052
UTC_E_TOGGLE_TRACE_STARTED: Final = -2017128447
UTC_E_ALTERNATIVE_TRACE_CANNOT_PREEMPT: Final = -2017128446
UTC_E_AOT_NOT_RUNNING: Final = -2017128445
UTC_E_SCRIPT_TYPE_INVALID: Final = -2017128444
UTC_E_SCENARIODEF_NOT_FOUND: Final = -2017128443
UTC_E_TRACEPROFILE_NOT_FOUND: Final = -2017128442
UTC_E_FORWARDER_ALREADY_ENABLED: Final = -2017128441
UTC_E_FORWARDER_ALREADY_DISABLED: Final = -2017128440
UTC_E_EVENTLOG_ENTRY_MALFORMED: Final = -2017128439
UTC_E_DIAGRULES_SCHEMAVERSION_MISMATCH: Final = -2017128438
UTC_E_SCRIPT_TERMINATED: Final = -2017128437
UTC_E_INVALID_CUSTOM_FILTER: Final = -2017128436
UTC_E_TRACE_NOT_RUNNING: Final = -2017128435
UTC_E_REESCALATED_TOO_QUICKLY: Final = -2017128434
UTC_E_ESCALATION_ALREADY_RUNNING: Final = -2017128433
UTC_E_PERFTRACK_ALREADY_TRACING: Final = -2017128432
UTC_E_REACHED_MAX_ESCALATIONS: Final = -2017128431
UTC_E_FORWARDER_PRODUCER_MISMATCH: Final = -2017128430
UTC_E_INTENTIONAL_SCRIPT_FAILURE: Final = -2017128429
UTC_E_SQM_INIT_FAILED: Final = -2017128428
UTC_E_NO_WER_LOGGER_SUPPORTED: Final = -2017128427
UTC_E_TRACERS_DONT_EXIST: Final = -2017128426
UTC_E_WINRT_INIT_FAILED: Final = -2017128425
UTC_E_SCENARIODEF_SCHEMAVERSION_MISMATCH: Final = -2017128424
UTC_E_INVALID_FILTER: Final = -2017128423
UTC_E_EXE_TERMINATED: Final = -2017128422
UTC_E_ESCALATION_NOT_AUTHORIZED: Final = -2017128421
UTC_E_SETUP_NOT_AUTHORIZED: Final = -2017128420
UTC_E_CHILD_PROCESS_FAILED: Final = -2017128419
UTC_E_COMMAND_LINE_NOT_AUTHORIZED: Final = -2017128418
UTC_E_CANNOT_LOAD_SCENARIO_EDITOR_XML: Final = -2017128417
UTC_E_ESCALATION_TIMED_OUT: Final = -2017128416
UTC_E_SETUP_TIMED_OUT: Final = -2017128415
UTC_E_TRIGGER_MISMATCH: Final = -2017128414
UTC_E_TRIGGER_NOT_FOUND: Final = -2017128413
UTC_E_SIF_NOT_SUPPORTED: Final = -2017128412
UTC_E_DELAY_TERMINATED: Final = -2017128411
UTC_E_DEVICE_TICKET_ERROR: Final = -2017128410
UTC_E_TRACE_BUFFER_LIMIT_EXCEEDED: Final = -2017128409
UTC_E_API_RESULT_UNAVAILABLE: Final = -2017128408
UTC_E_RPC_TIMEOUT: Final = -2017128407
UTC_E_RPC_WAIT_FAILED: Final = -2017128406
UTC_E_API_BUSY: Final = -2017128405
UTC_E_TRACE_MIN_DURATION_REQUIREMENT_NOT_MET: Final = -2017128404
UTC_E_EXCLUSIVITY_NOT_AVAILABLE: Final = -2017128403
UTC_E_GETFILE_FILE_PATH_NOT_APPROVED: Final = -2017128402
UTC_E_ESCALATION_DIRECTORY_ALREADY_EXISTS: Final = -2017128401
UTC_E_TIME_TRIGGER_ON_START_INVALID: Final = -2017128400
UTC_E_TIME_TRIGGER_ONLY_VALID_ON_SINGLE_TRANSITION: Final = -2017128399
UTC_E_TIME_TRIGGER_INVALID_TIME_RANGE: Final = -2017128398
UTC_E_MULTIPLE_TIME_TRIGGER_ON_SINGLE_STATE: Final = -2017128397
UTC_E_BINARY_MISSING: Final = -2017128396
UTC_E_FAILED_TO_RESOLVE_CONTAINER_ID: Final = -2017128394
UTC_E_UNABLE_TO_RESOLVE_SESSION: Final = -2017128393
UTC_E_THROTTLED: Final = -2017128392
UTC_E_UNAPPROVED_SCRIPT: Final = -2017128391
UTC_E_SCRIPT_MISSING: Final = -2017128390
UTC_E_SCENARIO_THROTTLED: Final = -2017128389
UTC_E_API_NOT_SUPPORTED: Final = -2017128388
UTC_E_GETFILE_EXTERNAL_PATH_NOT_APPROVED: Final = -2017128387
UTC_E_TRY_GET_SCENARIO_TIMEOUT_EXCEEDED: Final = -2017128386
UTC_E_CERT_REV_FAILED: Final = -2017128385
UTC_E_FAILED_TO_START_NDISCAP: Final = -2017128384
UTC_E_KERNELDUMP_LIMIT_REACHED: Final = -2017128383
UTC_E_MISSING_AGGREGATE_EVENT_TAG: Final = -2017128382
UTC_E_INVALID_AGGREGATION_STRUCT: Final = -2017128381
UTC_E_ACTION_NOT_SUPPORTED_IN_DESTINATION: Final = -2017128380
UTC_E_FILTER_MISSING_ATTRIBUTE: Final = -2017128379
UTC_E_FILTER_INVALID_TYPE: Final = -2017128378
UTC_E_FILTER_VARIABLE_NOT_FOUND: Final = -2017128377
UTC_E_FILTER_FUNCTION_RESTRICTED: Final = -2017128376
UTC_E_FILTER_VERSION_MISMATCH: Final = -2017128375
UTC_E_FILTER_INVALID_FUNCTION: Final = -2017128368
UTC_E_FILTER_INVALID_FUNCTION_PARAMS: Final = -2017128367
UTC_E_FILTER_INVALID_COMMAND: Final = -2017128366
UTC_E_FILTER_ILLEGAL_EVAL: Final = -2017128365
UTC_E_TTTRACER_RETURNED_ERROR: Final = -2017128364
UTC_E_AGENT_DIAGNOSTICS_TOO_LARGE: Final = -2017128363
UTC_E_FAILED_TO_RECEIVE_AGENT_DIAGNOSTICS: Final = -2017128362
UTC_E_SCENARIO_HAS_NO_ACTIONS: Final = -2017128361
UTC_E_TTTRACER_STORAGE_FULL: Final = -2017128360
UTC_E_INSUFFICIENT_SPACE_TO_START_TRACE: Final = -2017128359
UTC_E_ESCALATION_CANCELLED_AT_SHUTDOWN: Final = -2017128358
UTC_E_GETFILEINFOACTION_FILE_NOT_APPROVED: Final = -2017128357
UTC_E_SETREGKEYACTION_TYPE_NOT_APPROVED: Final = -2017128356
UTC_E_TRACE_THROTTLED: Final = -2017128355
WINML_ERR_INVALID_DEVICE: Final = -2003828735
WINML_ERR_INVALID_BINDING: Final = -2003828734
WINML_ERR_VALUE_NOTFOUND: Final = -2003828733
WINML_ERR_SIZE_MISMATCH: Final = -2003828732
ERROR_QUIC_HANDSHAKE_FAILURE: Final = -2143223808
ERROR_QUIC_VER_NEG_FAILURE: Final = -2143223807
ERROR_QUIC_USER_CANCELED: Final = -2143223806
ERROR_QUIC_INTERNAL_ERROR: Final = -2143223805
ERROR_QUIC_PROTOCOL_VIOLATION: Final = -2143223804
ERROR_QUIC_CONNECTION_IDLE: Final = -2143223803
ERROR_QUIC_CONNECTION_TIMEOUT: Final = -2143223802
ERROR_QUIC_ALPN_NEG_FAILURE: Final = -2143223801
IORING_E_REQUIRED_FLAG_NOT_SUPPORTED: Final = -2142896127
IORING_E_SUBMISSION_QUEUE_FULL: Final = -2142896126
IORING_E_VERSION_NOT_SUPPORTED: Final = -2142896125
IORING_E_SUBMISSION_QUEUE_TOO_BIG: Final = -2142896124
IORING_E_COMPLETION_QUEUE_TOO_BIG: Final = -2142896123
IORING_E_SUBMIT_IN_PROGRESS: Final = -2142896122
IORING_E_CORRUPT: Final = -2142896121
IORING_E_COMPLETION_QUEUE_TOO_FULL: Final = -2142896120

CDERR_DIALOGFAILURE: Final = 0xFFFF
CDERR_GENERALCODES: Final = 0x0000
CDERR_STRUCTSIZE: Final = 0x0001
CDERR_INITIALIZATION: Final = 0x0002
CDERR_NOTEMPLATE: Final = 0x0003
CDERR_NOHINSTANCE: Final = 0x0004
CDERR_LOADSTRFAILURE: Final = 0x0005
CDERR_FINDRESFAILURE: Final = 0x0006
CDERR_LOADRESFAILURE: Final = 0x0007
CDERR_LOCKRESFAILURE: Final = 0x0008
CDERR_MEMALLOCFAILURE: Final = 0x0009
CDERR_MEMLOCKFAILURE: Final = 0x000A
CDERR_NOHOOK: Final = 0x000B
CDERR_REGISTERMSGFAIL: Final = 0x000C
PDERR_PRINTERCODES: Final = 0x1000
PDERR_SETUPFAILURE: Final = 0x1001
PDERR_PARSEFAILURE: Final = 0x1002
PDERR_RETDEFFAILURE: Final = 0x1003
PDERR_LOADDRVFAILURE: Final = 0x1004
PDERR_GETDEVMODEFAIL: Final = 0x1005
PDERR_INITFAILURE: Final = 0x1006
PDERR_NODEVICES: Final = 0x1007
PDERR_NODEFAULTPRN: Final = 0x1008
PDERR_DNDMMISMATCH: Final = 0x1009
PDERR_CREATEICFAILURE: Final = 0x100A
PDERR_PRINTERNOTFOUND: Final = 0x100B
PDERR_DEFAULTDIFFERENT: Final = 0x100C
CFERR_CHOOSEFONTCODES: Final = 0x2000
CFERR_NOFONTS: Final = 0x2001
CFERR_MAXLESSTHANMIN: Final = 0x2002
FNERR_FILENAMECODES: Final = 0x3000
FNERR_SUBCLASSFAILURE: Final = 0x3001
FNERR_INVALIDFILENAME: Final = 0x3002
FNERR_BUFFERTOOSMALL: Final = 0x3003
FRERR_FINDREPLACECODES: Final = 0x4000
FRERR_BUFFERLENGTHZERO: Final = 0x4001
CCERR_CHOOSECOLORCODES: Final = 0x5000
