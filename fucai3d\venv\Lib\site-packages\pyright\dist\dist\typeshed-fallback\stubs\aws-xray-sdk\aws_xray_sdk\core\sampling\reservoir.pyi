from enum import Enum

class Reservoir:
    def __init__(self) -> None: ...
    def borrow_or_take(self, now: int, can_borrow: bool | None) -> ReservoirDecision | None: ...
    def load_quota(self, quota: int | None, TTL: int | None, interval: int | None) -> None: ...
    @property
    def quota(self) -> int | None: ...
    @property
    def TTL(self) -> int | None: ...

class ReservoirDecision(Enum):
    TAKE = "take"
    BORROW = "borrow"
    NO = "no"
