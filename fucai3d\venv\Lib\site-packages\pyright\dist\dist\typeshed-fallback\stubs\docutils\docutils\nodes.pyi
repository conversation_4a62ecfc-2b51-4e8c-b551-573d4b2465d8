import sys
import xml.dom.minidom
from abc import abstractmethod
from collections import Counter
from collections.abc import Callable, Generator, Iterable, Iterator, Mapping, Sequence
from typing import Any, ClassVar, Literal, Protocol, SupportsIndex, TypeVar, overload
from typing_extensions import Self, TypeAlias

from docutils.frontend import Values
from docutils.transforms import Transform, Transformer
from docutils.utils import Reporter

_N = TypeVar("_N", bound=Node)

class _DomModule(Protocol):
    Document: type[xml.dom.minidom.Document]

# Functional Node Base Classes

class Node:
    # children is initialized by the subclasses
    children: Sequence[Node]
    # TODO: `parent` is actually `Element | None``, but `None`` only happens rarely,
    #       i.e. for synthetic nodes (or `document`, where it is overridden).
    #       See https://github.com/python/typeshed/blob/main/CONTRIBUTING.md#the-any-trick
    parent: Element | Any
    source: str | None
    line: int | None
    @property
    def document(self) -> document | None: ...
    @document.setter
    def document(self, value: document) -> None: ...
    def __bool__(self) -> Literal[True]: ...
    def asdom(self, dom: _DomModule | None = None) -> xml.dom.minidom.Element: ...
    # While docutils documents the Node class to be abstract it does not
    # actually use the ABCMeta metaclass. We still set @abstractmethod here
    # (although it's not used in the docutils implementation) because it
    # makes Mypy reject Node() with "Cannot instantiate abstract class".
    @abstractmethod
    def copy(self) -> Self: ...
    @abstractmethod
    def deepcopy(self) -> Self: ...
    @abstractmethod
    def pformat(self, indent: str = "    ", level: int = 0) -> str: ...
    @abstractmethod
    def astext(self) -> str: ...
    def setup_child(self, child: Node) -> None: ...
    def walk(self, visitor: NodeVisitor) -> bool: ...
    def walkabout(self, visitor: NodeVisitor) -> bool: ...
    @overload
    def findall(
        self, condition: type[_N], include_self: bool = True, descend: bool = True, siblings: bool = False, ascend: bool = False
    ) -> Generator[_N, None, None]: ...
    @overload
    def findall(
        self,
        condition: Callable[[Node], bool] | None = None,
        include_self: bool = True,
        descend: bool = True,
        siblings: bool = False,
        ascend: bool = False,
    ) -> Generator[Node, None, None]: ...
    @overload
    def traverse(
        self, condition: type[_N], include_self: bool = True, descend: bool = True, siblings: bool = False, ascend: bool = False
    ) -> list[_N]: ...
    @overload
    def traverse(
        self,
        condition: Callable[[Node], bool] | None = None,
        include_self: bool = True,
        descend: bool = True,
        siblings: bool = False,
        ascend: bool = False,
    ) -> list[Node]: ...
    @overload
    def next_node(
        self, condition: type[_N], include_self: bool = False, descend: bool = True, siblings: bool = False, ascend: bool = False
    ) -> _N: ...
    @overload
    def next_node(
        self,
        condition: Callable[[Node], bool] | None = None,
        include_self: bool = False,
        descend: bool = True,
        siblings: bool = False,
        ascend: bool = False,
    ) -> Node: ...

# Left out
# - def ensure_str (deprecated)
# - def unescape (canonical import from docutils.utils)
def unescape(text: str, restore_backslashes: bool = False, respect_whitespace: bool = False) -> str: ...

class Text(Node, str):
    tagname: ClassVar[str]
    children: tuple[()]

    # we omit the rawsource parameter because it has been deprecated and is ignored
    def __new__(cls, data: str) -> Self: ...
    def __init__(self, data: str) -> None: ...
    def shortrepr(self, maxlen: int = 18) -> str: ...
    def copy(self) -> Self: ...
    def deepcopy(self) -> Self: ...
    def pformat(self, indent: str = "    ", level: int = 0) -> str: ...
    def astext(self) -> str: ...
    def rstrip(self, chars: str | None = None) -> str: ...
    def lstrip(self, chars: str | None = None) -> str: ...

_T = TypeVar("_T")

class Element(Node):
    local_attributes: ClassVar[Sequence[str]]
    basic_attributes: ClassVar[Sequence[str]]
    list_attributes: ClassVar[Sequence[str]]
    known_attributes: ClassVar[Sequence[str]]
    tagname: str
    child_text_separator: ClassVar[str]
    attributes: dict[str, Any]
    children: list[Node]
    rawsource: str
    def __init__(self, rawsource: str = "", *children: Node, **attributes: Any) -> None: ...
    def shortrepr(self) -> str: ...
    def starttag(self, quoteattr: Callable[[str], str] | None = None) -> str: ...
    def endtag(self) -> str: ...
    def emptytag(self) -> str: ...
    def __len__(self) -> int: ...
    def __contains__(self, key: str | Node) -> bool: ...
    @overload
    def __getitem__(self, key: str) -> Any: ...
    @overload
    def __getitem__(self, key: int) -> Node: ...
    @overload
    def __getitem__(self, key: slice) -> list[Node]: ...
    @overload
    def __setitem__(self, key: str, item: Any) -> None: ...
    @overload
    def __setitem__(self, key: int, item: Node) -> None: ...
    @overload
    def __setitem__(self, key: slice, item: Iterable[Node]) -> None: ...
    def __delitem__(self, key: str | int | slice) -> None: ...
    def __add__(self, other: list[Node]) -> list[Node]: ...
    def __radd__(self, other: list[Node]) -> list[Node]: ...
    def __iadd__(self, other: Node | Iterable[Node]) -> Self: ...
    def astext(self) -> str: ...
    def non_default_attributes(self) -> dict[str, Any]: ...
    def attlist(self) -> list[tuple[str, Any]]: ...
    @overload
    def get(self, key: str) -> Any: ...
    @overload
    def get(self, key: str, failobj: _T) -> _T: ...
    def hasattr(self, attr: str) -> bool: ...
    def delattr(self, attr: str) -> None: ...
    @overload
    def setdefault(self, key: str) -> Any: ...
    @overload
    def setdefault(self, key: str, failobj: _T) -> Any | _T: ...
    has_key = hasattr
    def get_language_code(self, fallback: str = "") -> str: ...
    def append(self, item: Node) -> None: ...
    def extend(self, item: Iterable[Node]) -> None: ...
    def insert(self, index: SupportsIndex, item: Node | Iterable[Node] | None) -> None: ...
    def pop(self, i: int = -1) -> Node: ...
    def remove(self, item: Node) -> None: ...
    def index(self, item: Node, start: int = 0, stop: int = sys.maxsize) -> int: ...
    def previous_sibling(self) -> Node | None: ...
    def is_not_default(self, key: str) -> bool: ...
    def update_basic_atts(self, dict_: Mapping[str, Any] | Node) -> None: ...
    def append_attr_list(self, attr: str, values: Iterable[Any]) -> None: ...
    def coerce_append_attr_list(self, attr: str, value) -> None: ...
    def replace_attr(self, attr: str, value: Any, force: bool = True) -> None: ...
    def copy_attr_convert(self, attr: str, value: Any, replace: bool = True) -> None: ...
    def copy_attr_coerce(self, attr: str, value: Any, replace: bool) -> None: ...
    def copy_attr_concatenate(self, attr: str, value: Any, replace: bool) -> None: ...
    def copy_attr_consistent(self, attr: str, value: Any, replace: bool) -> None: ...
    def update_all_atts(
        self,
        dict_: Mapping[str, Any] | Node,
        update_fun: Callable[[Element, str, Any, bool], object] = ...,
        replace: bool = True,
        and_source: bool = False,
    ) -> None: ...
    def update_all_atts_consistantly(
        self, dict_: Mapping[str, Any] | Node, replace: bool = True, and_source: bool = False
    ) -> None: ...
    def update_all_atts_concatenating(
        self, dict_: dict[str, Any] | Node, replace: bool = True, and_source: bool = False
    ) -> None: ...
    def update_all_atts_coercion(
        self, dict_: Mapping[str, Any] | Node, replace: bool = True, and_source: bool = False
    ) -> None: ...
    def update_all_atts_convert(self, dict_: Mapping[str, Any] | Node, and_source: bool = False) -> None: ...
    def clear(self) -> None: ...
    def replace(self, old: Node, new: Node | Sequence[Node]) -> None: ...
    def replace_self(self, new: Node | Sequence[Node]) -> None: ...
    def first_child_matching_class(
        self, childclass: type[Node] | tuple[type[Node], ...], start: int = 0, end: int = sys.maxsize
    ) -> int | None: ...
    def first_child_not_matching_class(
        self, childclass: type[Node] | tuple[type[Node], ...], start: int = 0, end: int = sys.maxsize
    ) -> int | None: ...
    def pformat(self, indent: str = "    ", level: int = 0) -> str: ...
    def copy(self) -> Self: ...
    def deepcopy(self) -> Self: ...
    def set_class(self, name: str) -> None: ...
    def note_referenced_by(self, name: str | None = None, id: str | None = None) -> None: ...
    @classmethod
    def is_not_list_attribute(cls, attr: str) -> bool: ...
    @classmethod
    def is_not_known_attribute(cls, attr: str) -> bool: ...

    # '__iter__' is added as workaround, since mypy doesn't support classes that are iterable via '__getitem__'
    # see https://github.com/python/typeshed/pull/10099#issuecomment-1528789395
    def __iter__(self) -> Iterator[Node]: ...

class TextElement(Element):
    def __init__(self, rawsource: str = "", text: str = "", *children: Node, **attributes) -> None: ...

class FixedTextElement(TextElement): ...

# Mixins

class Resolvable:
    resolved: int

class BackLinkable:
    def add_backref(self, refid: str) -> None: ...

# Element Categories

class Root: ...
class Titular: ...
class PreBibliographic: ...
class Bibliographic: ...
class Decorative(PreBibliographic): ...
class Structural: ...
class Body: ...
class General(Body): ...
class Sequential(Body): ...
class Admonition(Body): ...
class Special(Body): ...
class Invisible(PreBibliographic): ...
class Part: ...
class Inline: ...
class Referential(Resolvable): ...

class Targetable(Resolvable):
    referenced: int
    indirect_reference_name: str | None

class Labeled: ...

# Root Element

_Document: TypeAlias = document
_Decoration: TypeAlias = decoration

class document(Root, Structural, Element):
    current_source: str | None
    current_line: int | None
    settings: Values
    reporter: Reporter
    indirect_targets: list[target]
    substitution_defs: dict[str, substitution_definition]
    substitution_names: dict[str, str]
    refnames: dict[str, list[Element]]
    refids: dict[str, list[Element]]
    nameids: dict[str, str]
    nametypes: dict[str, bool]
    ids: dict[str, Element]
    footnote_refs: dict[str, list[footnote_reference]]
    citation_refs: dict[str, list[citation_reference]]
    autofootnotes: list[footnote]
    autofootnote_refs: list[footnote_reference]
    symbol_footnotes: list[footnote]
    symbol_footnote_refs: list[footnote_reference]
    footnotes: list[footnote]
    citations: list[citation]
    autofootnote_start: int
    symbol_footnote_start: int
    id_counter: Counter[int]
    parse_messages: list[system_message]
    transform_messages: list[system_message]
    transformer: Transformer
    decoration: decoration | None
    document: Self
    def __init__(self, settings: Values, reporter: Reporter, *args: Node, **kwargs: Any) -> None: ...
    def asdom(self, dom: Any | None = None) -> Any: ...
    def set_id(self, node: Element, msgnode: Element | None = None, suggested_prefix: str = "") -> str: ...
    def set_name_id_map(self, node: Element, id: str, msgnode: Element | None = None, explicit: bool | None = None) -> None: ...
    def set_duplicate_name_id(self, node: Element, id: str, name: str, msgnode: Element, explicit: bool) -> None: ...
    def has_name(self, name: str) -> bool: ...
    def note_implicit_target(self, target: Element, msgnode: Element | None = None) -> None: ...
    def note_explicit_target(self, target: Element, msgnode: Element | None = None) -> None: ...
    def note_refname(self, node: Element) -> None: ...
    def note_refid(self, node: Element) -> None: ...
    def note_indirect_target(self, target: target) -> None: ...
    def note_anonymous_target(self, target: target) -> None: ...
    def note_autofootnote(self, footnote: footnote) -> None: ...
    def note_autofootnote_ref(self, ref: footnote_reference) -> None: ...
    def note_symbol_footnote(self, footnote: footnote) -> None: ...
    def note_symbol_footnote_ref(self, ref: footnote_reference) -> None: ...
    def note_footnote(self, footnote: footnote) -> None: ...
    def note_footnote_ref(self, ref: footnote_reference) -> None: ...
    def note_citation(self, citation: citation) -> None: ...
    def note_citation_ref(self, ref: citation_reference) -> None: ...
    def note_substitution_def(self, subdef: substitution_definition, def_name: str, msgnode: Element | None = None) -> None: ...
    def note_substitution_ref(self, subref: substitution_reference, refname: str) -> None: ...
    def note_pending(self, pending: pending, priority: int | None = None) -> None: ...
    def note_parse_message(self, message: system_message) -> None: ...
    def note_transform_message(self, message: system_message) -> None: ...
    def note_source(self, source: str, offset: int) -> None: ...
    def copy(self) -> Self: ...
    def get_decoration(self) -> _Decoration: ...

# Title Elements

class title(Titular, PreBibliographic, TextElement): ...
class subtitle(Titular, PreBibliographic, TextElement): ...
class rubric(Titular, TextElement): ...

# Meta-Data Element

class meta(PreBibliographic, Element): ...

# Bibliographic Elements

class docinfo(Bibliographic, Element): ...
class author(Bibliographic, TextElement): ...
class authors(Bibliographic, Element): ...
class organization(Bibliographic, TextElement): ...
class address(Bibliographic, FixedTextElement): ...
class contact(Bibliographic, TextElement): ...
class version(Bibliographic, TextElement): ...
class revision(Bibliographic, TextElement): ...
class status(Bibliographic, TextElement): ...
class date(Bibliographic, TextElement): ...
class copyright(Bibliographic, TextElement): ...

# Decorative Elements

class decoration(Decorative, Element):
    def get_header(self) -> header: ...
    def get_footer(self) -> footer: ...

class header(Decorative, Element): ...
class footer(Decorative, Element): ...

# Structural Elements

class section(Structural, Element): ...
class topic(Structural, Element): ...
class sidebar(Structural, Element): ...
class transition(Structural, Element): ...

# Body Elements
# ===============

class paragraph(General, TextElement): ...
class compound(General, Element): ...
class container(General, Element): ...
class bullet_list(Sequential, Element): ...
class enumerated_list(Sequential, Element): ...
class list_item(Part, Element): ...
class definition_list(Sequential, Element): ...
class definition_list_item(Part, Element): ...
class term(Part, TextElement): ...
class classifier(Part, TextElement): ...
class definition(Part, Element): ...
class field_list(Sequential, Element): ...
class field(Part, Element): ...
class field_name(Part, TextElement): ...
class field_body(Part, Element): ...
class option(Part, Element): ...
class option_argument(Part, TextElement): ...
class option_group(Part, Element): ...
class option_list(Sequential, Element): ...
class option_list_item(Part, Element): ...
class option_string(Part, TextElement): ...
class description(Part, Element): ...
class literal_block(General, FixedTextElement): ...
class doctest_block(General, FixedTextElement): ...
class math_block(General, FixedTextElement): ...
class line_block(General, Element): ...

class line(Part, TextElement):
    indent: str | None

class block_quote(General, Element): ...
class attribution(Part, TextElement): ...
class attention(Admonition, Element): ...
class caution(Admonition, Element): ...
class danger(Admonition, Element): ...
class error(Admonition, Element): ...
class important(Admonition, Element): ...
class note(Admonition, Element): ...
class tip(Admonition, Element): ...
class hint(Admonition, Element): ...
class warning(Admonition, Element): ...
class admonition(Admonition, Element): ...
class comment(Special, Invisible, FixedTextElement): ...
class substitution_definition(Special, Invisible, TextElement): ...
class target(Special, Invisible, Inline, TextElement, Targetable): ...
class footnote(General, BackLinkable, Element, Labeled, Targetable): ...
class citation(General, BackLinkable, Element, Labeled, Targetable): ...
class label(Part, TextElement): ...
class figure(General, Element): ...
class caption(Part, TextElement): ...
class legend(Part, Element): ...
class table(General, Element): ...
class tgroup(Part, Element): ...
class colspec(Part, Element): ...
class thead(Part, Element): ...
class tbody(Part, Element): ...
class row(Part, Element): ...
class entry(Part, Element): ...

class system_message(Special, BackLinkable, PreBibliographic, Element):
    def __init__(self, message: str | None = None, *children: Node, **attributes) -> None: ...
    def astext(self) -> str: ...

class pending(Special, Invisible, Element):
    transform: type[Transform]
    details: Mapping[str, Any]
    def __init__(
        self,
        transform: type[Transform],
        details: Mapping[str, Any] | None = None,
        rawsource: str = "",
        *children: Node,
        **attributes,
    ) -> None: ...

class raw(Special, Inline, PreBibliographic, FixedTextElement): ...

# Inline Elements

class emphasis(Inline, TextElement): ...
class strong(Inline, TextElement): ...
class literal(Inline, TextElement): ...
class reference(General, Inline, Referential, TextElement): ...
class footnote_reference(Inline, Referential, TextElement): ...
class citation_reference(Inline, Referential, TextElement): ...
class substitution_reference(Inline, TextElement): ...
class title_reference(Inline, TextElement): ...
class abbreviation(Inline, TextElement): ...
class acronym(Inline, TextElement): ...
class superscript(Inline, TextElement): ...
class subscript(Inline, TextElement): ...
class math(Inline, TextElement): ...
class image(General, Inline, Element): ...
class inline(Inline, TextElement): ...
class problematic(Inline, TextElement): ...
class generated(Inline, TextElement): ...

# Auxiliary Classes, Functions, and Data

node_class_names: list[str]

class NodeVisitor:
    optional: ClassVar[tuple[str, ...]]
    document: _Document
    def __init__(self, document: _Document) -> None: ...
    def dispatch_visit(self, node: Node) -> Any: ...
    def dispatch_departure(self, node: Node) -> Any: ...
    def unknown_visit(self, node: Node) -> Any: ...
    def unknown_departure(self, node: Node) -> Any: ...

    # These methods only exist on the subclasses `GenericNodeVisitor` and `SparseNodeVisitor` at runtime.
    # If subclassing `NodeVisitor` directly, `visit_*` methods must be implemented for nodes and children that will be called
    # with `Node.walk()` and `Node.walkabout()`.
    # `depart_*` methods must also be implemented for nodes and children that will be called with `Node.walkabout()`.
    def visit_Text(self, node: Text) -> Any: ...
    def visit_abbreviation(self, node: abbreviation) -> Any: ...
    def visit_acronym(self, node: acronym) -> Any: ...
    def visit_address(self, node: address) -> Any: ...
    def visit_admonition(self, node: admonition) -> Any: ...
    def visit_attention(self, node: attention) -> Any: ...
    def visit_attribution(self, node: attribution) -> Any: ...
    def visit_author(self, node: author) -> Any: ...
    def visit_authors(self, node: authors) -> Any: ...
    def visit_block_quote(self, node: block_quote) -> Any: ...
    def visit_bullet_list(self, node: bullet_list) -> Any: ...
    def visit_caption(self, node: caption) -> Any: ...
    def visit_caution(self, node: caution) -> Any: ...
    def visit_citation(self, node: citation) -> Any: ...
    def visit_citation_reference(self, node: citation_reference) -> Any: ...
    def visit_classifier(self, node: classifier) -> Any: ...
    def visit_colspec(self, node: colspec) -> Any: ...
    def visit_comment(self, node: comment) -> Any: ...
    def visit_compound(self, node: compound) -> Any: ...
    def visit_contact(self, node: contact) -> Any: ...
    def visit_container(self, node: container) -> Any: ...
    def visit_copyright(self, node: copyright) -> Any: ...
    def visit_danger(self, node: danger) -> Any: ...
    def visit_date(self, node: date) -> Any: ...
    def visit_decoration(self, node: decoration) -> Any: ...
    def visit_definition(self, node: definition) -> Any: ...
    def visit_definition_list(self, node: definition_list) -> Any: ...
    def visit_definition_list_item(self, node: definition_list_item) -> Any: ...
    def visit_description(self, node: description) -> Any: ...
    def visit_docinfo(self, node: docinfo) -> Any: ...
    def visit_doctest_block(self, node: doctest_block) -> Any: ...
    def visit_document(self, node: _Document) -> Any: ...
    def visit_emphasis(self, node: emphasis) -> Any: ...
    def visit_entry(self, node: entry) -> Any: ...
    def visit_enumerated_list(self, node: enumerated_list) -> Any: ...
    def visit_error(self, node: error) -> Any: ...
    def visit_field(self, node: field) -> Any: ...
    def visit_field_body(self, node: field_body) -> Any: ...
    def visit_field_list(self, node: field_list) -> Any: ...
    def visit_field_name(self, node: field_name) -> Any: ...
    def visit_figure(self, node: figure) -> Any: ...
    def visit_footer(self, node: footer) -> Any: ...
    def visit_footnote(self, node: footnote) -> Any: ...
    def visit_footnote_reference(self, node: footnote_reference) -> Any: ...
    def visit_generated(self, node: generated) -> Any: ...
    def visit_header(self, node: header) -> Any: ...
    def visit_hint(self, node: hint) -> Any: ...
    def visit_image(self, node: image) -> Any: ...
    def visit_important(self, node: important) -> Any: ...
    def visit_inline(self, node: inline) -> Any: ...
    def visit_label(self, node: label) -> Any: ...
    def visit_legend(self, node: legend) -> Any: ...
    def visit_line(self, node: line) -> Any: ...
    def visit_line_block(self, node: line_block) -> Any: ...
    def visit_list_item(self, node: list_item) -> Any: ...
    def visit_literal(self, node: literal) -> Any: ...
    def visit_literal_block(self, node: literal_block) -> Any: ...
    def visit_math(self, node: math) -> Any: ...
    def visit_math_block(self, node: math_block) -> Any: ...
    def visit_meta(self, node: meta) -> Any: ...
    def visit_note(self, node: note) -> Any: ...
    def visit_option(self, node: option) -> Any: ...
    def visit_option_argument(self, node: option_argument) -> Any: ...
    def visit_option_group(self, node: option_group) -> Any: ...
    def visit_option_list(self, node: option_list) -> Any: ...
    def visit_option_list_item(self, node: option_list_item) -> Any: ...
    def visit_option_string(self, node: option_string) -> Any: ...
    def visit_organization(self, node: organization) -> Any: ...
    def visit_paragraph(self, node: paragraph) -> Any: ...
    def visit_pending(self, node: pending) -> Any: ...
    def visit_problematic(self, node: problematic) -> Any: ...
    def visit_raw(self, node: raw) -> Any: ...
    def visit_reference(self, node: reference) -> Any: ...
    def visit_revision(self, node: revision) -> Any: ...
    def visit_row(self, node: row) -> Any: ...
    def visit_rubric(self, node: rubric) -> Any: ...
    def visit_section(self, node: section) -> Any: ...
    def visit_sidebar(self, node: sidebar) -> Any: ...
    def visit_status(self, node: status) -> Any: ...
    def visit_strong(self, node: strong) -> Any: ...
    def visit_subscript(self, node: subscript) -> Any: ...
    def visit_substitution_definition(self, node: substitution_definition) -> Any: ...
    def visit_substitution_reference(self, node: substitution_reference) -> Any: ...
    def visit_subtitle(self, node: subtitle) -> Any: ...
    def visit_superscript(self, node: superscript) -> Any: ...
    def visit_system_message(self, node: system_message) -> Any: ...
    def visit_table(self, node: table) -> Any: ...
    def visit_target(self, node: target) -> Any: ...
    def visit_tbody(self, node: tbody) -> Any: ...
    def visit_term(self, node: term) -> Any: ...
    def visit_tgroup(self, node: tgroup) -> Any: ...
    def visit_thead(self, node: thead) -> Any: ...
    def visit_tip(self, node: tip) -> Any: ...
    def visit_title(self, node: title) -> Any: ...
    def visit_title_reference(self, node: title_reference) -> Any: ...
    def visit_topic(self, node: topic) -> Any: ...
    def visit_transition(self, node: transition) -> Any: ...
    def visit_version(self, node: version) -> Any: ...
    def visit_warning(self, node: warning) -> Any: ...
    def depart_Text(self, node: Text) -> Any: ...
    def depart_abbreviation(self, node: abbreviation) -> Any: ...
    def depart_acronym(self, node: acronym) -> Any: ...
    def depart_address(self, node: address) -> Any: ...
    def depart_admonition(self, node: admonition) -> Any: ...
    def depart_attention(self, node: attention) -> Any: ...
    def depart_attribution(self, node: attribution) -> Any: ...
    def depart_author(self, node: author) -> Any: ...
    def depart_authors(self, node: authors) -> Any: ...
    def depart_block_quote(self, node: block_quote) -> Any: ...
    def depart_bullet_list(self, node: bullet_list) -> Any: ...
    def depart_caption(self, node: caption) -> Any: ...
    def depart_caution(self, node: caution) -> Any: ...
    def depart_citation(self, node: citation) -> Any: ...
    def depart_citation_reference(self, node: citation_reference) -> Any: ...
    def depart_classifier(self, node: classifier) -> Any: ...
    def depart_colspec(self, node: colspec) -> Any: ...
    def depart_comment(self, node: comment) -> Any: ...
    def depart_compound(self, node: compound) -> Any: ...
    def depart_contact(self, node: contact) -> Any: ...
    def depart_container(self, node: container) -> Any: ...
    def depart_copyright(self, node: copyright) -> Any: ...
    def depart_danger(self, node: danger) -> Any: ...
    def depart_date(self, node: date) -> Any: ...
    def depart_decoration(self, node: decoration) -> Any: ...
    def depart_definition(self, node: definition) -> Any: ...
    def depart_definition_list(self, node: definition_list) -> Any: ...
    def depart_definition_list_item(self, node: definition_list_item) -> Any: ...
    def depart_description(self, node: description) -> Any: ...
    def depart_docinfo(self, node: docinfo) -> Any: ...
    def depart_doctest_block(self, node: doctest_block) -> Any: ...
    def depart_document(self, node: _Document) -> Any: ...
    def depart_emphasis(self, node: emphasis) -> Any: ...
    def depart_entry(self, node: entry) -> Any: ...
    def depart_enumerated_list(self, node: enumerated_list) -> Any: ...
    def depart_error(self, node: error) -> Any: ...
    def depart_field(self, node: field) -> Any: ...
    def depart_field_body(self, node: field_body) -> Any: ...
    def depart_field_list(self, node: field_list) -> Any: ...
    def depart_field_name(self, node: field_name) -> Any: ...
    def depart_figure(self, node: figure) -> Any: ...
    def depart_footer(self, node: footer) -> Any: ...
    def depart_footnote(self, node: footnote) -> Any: ...
    def depart_footnote_reference(self, node: footnote_reference) -> Any: ...
    def depart_generated(self, node: generated) -> Any: ...
    def depart_header(self, node: header) -> Any: ...
    def depart_hint(self, node: hint) -> Any: ...
    def depart_image(self, node: image) -> Any: ...
    def depart_important(self, node: important) -> Any: ...
    def depart_inline(self, node: inline) -> Any: ...
    def depart_label(self, node: label) -> Any: ...
    def depart_legend(self, node: legend) -> Any: ...
    def depart_line(self, node: line) -> Any: ...
    def depart_line_block(self, node: line_block) -> Any: ...
    def depart_list_item(self, node: list_item) -> Any: ...
    def depart_literal(self, node: literal) -> Any: ...
    def depart_literal_block(self, node: literal_block) -> Any: ...
    def depart_math(self, node: math) -> Any: ...
    def depart_math_block(self, node: math_block) -> Any: ...
    def depart_meta(self, node: meta) -> Any: ...
    def depart_note(self, node: note) -> Any: ...
    def depart_option(self, node: option) -> Any: ...
    def depart_option_argument(self, node: option_argument) -> Any: ...
    def depart_option_group(self, node: option_group) -> Any: ...
    def depart_option_list(self, node: option_list) -> Any: ...
    def depart_option_list_item(self, node: option_list_item) -> Any: ...
    def depart_option_string(self, node: option_string) -> Any: ...
    def depart_organization(self, node: organization) -> Any: ...
    def depart_paragraph(self, node: paragraph) -> Any: ...
    def depart_pending(self, node: pending) -> Any: ...
    def depart_problematic(self, node: problematic) -> Any: ...
    def depart_raw(self, node: raw) -> Any: ...
    def depart_reference(self, node: reference) -> Any: ...
    def depart_revision(self, node: revision) -> Any: ...
    def depart_row(self, node: row) -> Any: ...
    def depart_rubric(self, node: rubric) -> Any: ...
    def depart_section(self, node: section) -> Any: ...
    def depart_sidebar(self, node: sidebar) -> Any: ...
    def depart_status(self, node: status) -> Any: ...
    def depart_strong(self, node: strong) -> Any: ...
    def depart_subscript(self, node: subscript) -> Any: ...
    def depart_substitution_definition(self, node: substitution_definition) -> Any: ...
    def depart_substitution_reference(self, node: substitution_reference) -> Any: ...
    def depart_subtitle(self, node: subtitle) -> Any: ...
    def depart_superscript(self, node: superscript) -> Any: ...
    def depart_system_message(self, node: system_message) -> Any: ...
    def depart_table(self, node: table) -> Any: ...
    def depart_target(self, node: target) -> Any: ...
    def depart_tbody(self, node: tbody) -> Any: ...
    def depart_term(self, node: term) -> Any: ...
    def depart_tgroup(self, node: tgroup) -> Any: ...
    def depart_thead(self, node: thead) -> Any: ...
    def depart_tip(self, node: tip) -> Any: ...
    def depart_title(self, node: title) -> Any: ...
    def depart_title_reference(self, node: title_reference) -> Any: ...
    def depart_topic(self, node: topic) -> Any: ...
    def depart_transition(self, node: transition) -> Any: ...
    def depart_version(self, node: version) -> Any: ...
    def depart_warning(self, node: warning) -> Any: ...

class SparseNodeVisitor(NodeVisitor): ...

class GenericNodeVisitor(NodeVisitor):
    def default_visit(self, node: Node) -> None: ...
    def default_departure(self, node: Node) -> None: ...

class TreeCopyVisitor(GenericNodeVisitor):
    parent_stack: list[Node]
    parent: list[Node]
    def get_tree_copy(self) -> Node: ...

class TreePruningException(Exception): ...
class SkipChildren(TreePruningException): ...
class SkipSiblings(TreePruningException): ...
class SkipNode(TreePruningException): ...
class SkipDeparture(TreePruningException): ...
class NodeFound(TreePruningException): ...
class StopTraversal(TreePruningException): ...

def make_id(string: str) -> str: ...
def dupname(node: Node, name: str) -> None: ...
def fully_normalize_name(name: str) -> str: ...
def whitespace_normalize_name(name: str) -> str: ...
def serial_escape(value: str) -> str: ...
def pseudo_quoteattr(value: str) -> str: ...
