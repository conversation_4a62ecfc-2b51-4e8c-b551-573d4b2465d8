from typing import Any

POOLING_STRATEGIES: Any

class ServerState:
    server: Any
    last_checked_time: Any
    available: Any
    def __init__(self, server, last_checked_time, available) -> None: ...

class ServerPoolState:
    server_states: Any
    strategy: Any
    server_pool: Any
    last_used_server: int
    initialize_time: Any
    def __init__(self, server_pool) -> None: ...
    def refresh(self) -> None: ...
    def get_current_server(self): ...
    def get_server(self): ...
    def find_active_random_server(self): ...
    def find_active_server(self, starting): ...
    def __len__(self) -> int: ...

class ServerPool:
    servers: Any
    pool_states: Any
    active: Any
    exhaust: Any
    single: Any
    strategy: Any
    def __init__(
        self, servers=None, pool_strategy="ROUND_ROBIN", active: bool = True, exhaust: bool = False, single_state: bool = True
    ) -> None: ...
    def __len__(self) -> int: ...
    def __getitem__(self, item): ...
    def __iter__(self): ...
    def add(self, servers) -> None: ...
    def remove(self, server) -> None: ...
    def initialize(self, connection) -> None: ...
    def get_server(self, connection): ...
    def get_current_server(self, connection): ...
