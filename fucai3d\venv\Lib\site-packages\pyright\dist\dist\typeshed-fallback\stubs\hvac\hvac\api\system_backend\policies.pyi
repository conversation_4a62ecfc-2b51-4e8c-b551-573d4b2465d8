from hvac.api.system_backend.system_backend_mixin import SystemBackendMixin

class Policies(SystemBackendMixin):
    def list_acl_policies(self): ...
    def read_acl_policy(self, name): ...
    def create_or_update_acl_policy(self, name, policy, pretty_print: bool = True): ...
    def delete_acl_policy(self, name): ...
    def list_rgp_policies(self): ...
    def read_rgp_policy(self, name): ...
    def create_or_update_rgp_policy(self, name, policy, enforcement_level): ...
    def delete_rgp_policy(self, name): ...
    def list_egp_policies(self): ...
    def read_egp_policy(self, name): ...
    def create_or_update_egp_policy(self, name, policy, enforcement_level, paths): ...
    def delete_egp_policy(self, name): ...
