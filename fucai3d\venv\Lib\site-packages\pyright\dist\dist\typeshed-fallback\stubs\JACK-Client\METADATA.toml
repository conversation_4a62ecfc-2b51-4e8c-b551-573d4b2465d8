version = "0.5.*"
upstream_repository = "https://github.com/spatialaudio/jackclient-python"
# Requires a version of numpy with a `py.typed` file
requires = ["numpy>=1.20", "types-cffi"]

[tool.stubtest]
# darwin and win32 are equivalent
platforms = ["darwin", "linux"]
apt_dependencies = ["libjack-dev"]
brew_dependencies = ["jack"]
# No need to install on the CI. Leaving here as information for Windows contributors.
# choco_dependencies = ["jack"]
