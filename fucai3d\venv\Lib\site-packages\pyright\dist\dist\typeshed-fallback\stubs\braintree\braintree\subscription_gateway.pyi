from _typeshed import Incomplete

class SubscriptionGateway:
    gateway: Incomplete
    config: Incomplete
    def __init__(self, gateway) -> None: ...
    def cancel(self, subscription_id): ...
    def create(self, params=None): ...
    def find(self, subscription_id): ...
    def retry_charge(self, subscription_id, amount=None, submit_for_settlement: bool = False): ...
    def search(self, *query): ...
    def update(self, subscription_id, params=None): ...
