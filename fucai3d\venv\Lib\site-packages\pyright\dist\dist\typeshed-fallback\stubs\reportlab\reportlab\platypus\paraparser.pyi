from _typeshed import Incomplete
from html.parser import HTMLParser

from reportlab.lib.abag import ABag

__all__ = ("ParaFrag", "ParaParser")

class _PCT(float):
    def __new__(cls, v): ...
    def normalizedValue(self, normalizer): ...
    def __copy__(self): ...
    def __deepcopy__(self, mem): ...

class _ExValidate:
    tag: Incomplete
    attr: Incomplete
    def __init__(self, tag, attr) -> None: ...
    def invalid(self, s) -> None: ...
    def validate(self, parser, s): ...
    def __call__(self, parser, s): ...

class _CheckSup(_ExValidate):
    fontSize: Incomplete
    def validate(self, parser, s): ...
    def __call__(self, parser, s): ...

class _CheckUS(_ExValidate):
    def validate(self, parser, s): ...

class ParaFrag(ABag): ...

class ParaParser(HTMLParser):
    def __getattr__(self, attrName): ...
    def start_b(self, attributes) -> None: ...
    def end_b(self) -> None: ...
    def start_strong(self, attributes) -> None: ...
    def end_strong(self) -> None: ...
    def start_i(self, attributes) -> None: ...
    def end_i(self) -> None: ...
    def start_em(self, attributes) -> None: ...
    def end_em(self) -> None: ...
    def start_u(self, attributes) -> None: ...
    def end_u(self) -> None: ...
    def start_strike(self, attributes) -> None: ...
    def end_strike(self) -> None: ...
    def start_link(self, attributes) -> None: ...
    def end_link(self) -> None: ...
    def start_a(self, attributes) -> None: ...
    def end_a(self) -> None: ...
    def start_img(self, attributes) -> None: ...
    def end_img(self) -> None: ...
    def start_super(self, attributes) -> None: ...
    def end_super(self) -> None: ...
    start_sup = start_super
    end_sup = end_super
    def start_sub(self, attributes) -> None: ...
    def end_sub(self) -> None: ...
    def start_nobr(self, attrs) -> None: ...
    def end_nobr(self) -> None: ...
    def handle_charref(self, name) -> None: ...
    def syntax_error(self, lineno, message) -> None: ...
    def start_greek(self, attr) -> None: ...
    def end_greek(self) -> None: ...
    def start_unichar(self, attr) -> None: ...
    def end_unichar(self) -> None: ...
    def start_font(self, attr) -> None: ...
    def end_font(self) -> None: ...
    def start_span(self, attr) -> None: ...
    def end_span(self) -> None: ...
    def start_br(self, attr) -> None: ...
    def end_br(self) -> None: ...
    def start_para(self, attr) -> None: ...
    def end_para(self) -> None: ...
    bFragList: Incomplete
    def start_bullet(self, attr) -> None: ...
    def end_bullet(self) -> None: ...
    def start_seqdefault(self, attr) -> None: ...
    def end_seqdefault(self) -> None: ...
    def start_seqreset(self, attr) -> None: ...
    def end_seqreset(self) -> None: ...
    def start_seqchain(self, attr) -> None: ...
    end_seqchain = end_seqreset
    def start_seqformat(self, attr) -> None: ...
    end_seqformat = end_seqreset
    start_seqDefault = start_seqdefault
    end_seqDefault = end_seqdefault
    start_seqReset = start_seqreset
    end_seqReset = end_seqreset
    start_seqChain = start_seqchain
    end_seqChain = end_seqchain
    start_seqFormat = start_seqformat
    end_seqFormat = end_seqformat
    def start_seq(self, attr) -> None: ...
    def end_seq(self) -> None: ...
    def start_ondraw(self, attr) -> None: ...
    start_onDraw = start_ondraw
    end_onDraw = end_seq
    end_ondraw = end_seq
    def start_index(self, attr) -> None: ...
    end_index = end_seq
    def start_unknown(self, attr) -> None: ...
    end_unknown = end_seq
    def getAttributes(self, attr, attrMap): ...
    verbose: Incomplete
    caseSensitive: Incomplete
    ignoreUnknownTags: Incomplete
    def __init__(
        self, verbose: int = 0, caseSensitive: int = 0, ignoreUnknownTags: int = 1, crashOnError: bool = True
    ) -> None: ...
    def handle_data(self, data) -> None: ...
    def handle_cdata(self, data) -> None: ...
    def tt_parse(self, tt, style): ...
    def findSpanStyle(self, style) -> None: ...
    def parse(self, text, style): ...
    def handle_starttag(self, tag, attrs) -> None: ...
    def handle_endtag(self, tag) -> None: ...
    def handle_entityref(self, name) -> None: ...
