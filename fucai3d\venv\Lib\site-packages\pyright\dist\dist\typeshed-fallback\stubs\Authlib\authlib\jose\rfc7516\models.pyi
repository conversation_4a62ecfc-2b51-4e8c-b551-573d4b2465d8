from _typeshed import Incomplete
from abc import ABCMeta
from collections.abc import Iterable
from typing import ClassVar

class JWEAlgorithmBase(metaclass=ABCMeta):
    EXTRA_HEADERS: ClassVar[Iterable[str] | None]
    name: str | None
    description: str | None
    algorithm_type: str
    algorithm_location: str
    def prepare_key(self, raw_data) -> None: ...
    def generate_preset(self, enc_alg, key) -> None: ...

class JWEAlgorithm(JWEAlgorithmBase, metaclass=ABCMeta):
    def wrap(self, enc_alg, headers, key, preset=None) -> None: ...
    def unwrap(self, enc_alg, ek, headers, key) -> None: ...

class JWEAlgorithmWithTagAwareKeyAgreement(JWEAlgorithmBase, metaclass=ABCMeta):
    def generate_keys_and_prepare_headers(self, enc_alg, key, sender_key, preset=None) -> None: ...
    def agree_upon_key_and_wrap_cek(self, enc_alg, headers, key, sender_key, epk, cek, tag) -> None: ...
    def wrap(self, enc_alg, headers, key, sender_key, preset=None) -> None: ...
    def unwrap(self, enc_alg, ek, headers, key, sender_key, tag=None) -> None: ...

class JWEEncAlgorithm:
    name: str | None
    description: str | None
    algorithm_type: str
    algorithm_location: str
    IV_SIZE: Incomplete
    CEK_SIZE: Incomplete
    def generate_cek(self): ...
    def generate_iv(self): ...
    def check_iv(self, iv) -> None: ...
    def encrypt(self, msg, aad, iv, key) -> None: ...
    def decrypt(self, ciphertext, aad, iv, tag, key) -> None: ...

class JWEZipAlgorithm:
    name: Incomplete
    description: Incomplete
    algorithm_type: str
    algorithm_location: str
    def compress(self, s) -> None: ...
    def decompress(self, s) -> None: ...

class JWESharedHeader(dict[str, object]):
    protected: Incomplete
    unprotected: Incomplete
    def __init__(self, protected, unprotected) -> None: ...
    def update_protected(self, addition) -> None: ...
    @classmethod
    def from_dict(cls, obj): ...

class JWEHeader(dict[str, object]):
    protected: Incomplete
    unprotected: Incomplete
    header: Incomplete
    def __init__(self, protected, unprotected, header) -> None: ...
