from logging import Logger

from .context import Context

log: Logger
LAMBDA_TRACE_HEADER_KEY: str
LAMBDA_TASK_ROOT_KEY: str
TOUCH_FILE_DIR: str
TOUCH_FILE_PATH: str

def check_in_lambda(): ...

class LambdaContext(Context):
    def __init__(self) -> None: ...
    def put_segment(self, segment) -> None: ...
    def end_segment(self, end_time=None) -> None: ...
    def put_subsegment(self, subsegment) -> None: ...
    def get_trace_entity(self): ...
    @property
    def context_missing(self) -> None: ...
    @context_missing.setter
    def context_missing(self, value) -> None: ...
    def handle_context_missing(self) -> None: ...
