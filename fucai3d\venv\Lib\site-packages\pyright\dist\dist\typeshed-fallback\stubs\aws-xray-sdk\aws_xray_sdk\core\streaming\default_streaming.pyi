from _typeshed import Unused
from collections.abc import Callable

from aws_xray_sdk.core.models.entity import Entity
from aws_xray_sdk.core.models.segment import Segment

class DefaultStreaming:
    def __init__(self, streaming_threshold: int = 30) -> None: ...
    def is_eligible(self, segment: Segment) -> bool: ...
    def stream(self, entity: Entity, callback: Callable[..., Unused]) -> None: ...
    @property
    def streaming_threshold(self) -> int: ...
    @streaming_threshold.setter
    def streaming_threshold(self, value: int) -> None: ...
