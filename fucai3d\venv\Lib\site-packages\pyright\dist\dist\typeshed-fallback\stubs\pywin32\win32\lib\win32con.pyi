from typing import Final

WINVER: Final = 1280
WM_USER: Final = 1024
PY_0U: Final = 0
OFN_READONLY: Final = 1
OFN_OVERWRITEPROMPT: Final = 2
OFN_HIDEREADONLY: Final = 4
OFN_NOCHANGEDIR: Final = 8
OFN_SHOWHELP: Final = 16
OFN_ENABLEHOOK: Final = 32
OFN_ENABLETEMPLATE: Final = 64
OFN_ENABLETEMPLATEHANDLE: Final = 128
OFN_NOVALIDATE: Final = 256
OFN_ALLOWMULTISELECT: Final = 512
OFN_EXTENSIONDIFFERENT: Final = 1024
OFN_PATHMUSTEXIST: Final = 2048
OFN_FILEMUSTEXIST: Final = 4096
OFN_CREATEPROMPT: Final = 8192
OFN_SHAREAWARE: Final = 16384
OFN_NOREADONLYRETURN: Final = 32768
OFN_NOTESTFILECREATE: Final = 65536
OFN_NONETWORKBUTTON: Final = 131072
OFN_NOLONGNAMES: Final = 262144
OFN_EXPLORER: Final = 524288
OFN_NODEREFERENCELINKS: Final = 1048576
OFN_LONGNAMES: Final = 2097152
OFN_ENABLEINCLUDENOTIFY: Final = 4194304
OFN_ENABLESIZING: Final = 8388608
OFN_DONTADDTORECENT: Final = 33554432
OFN_FORCESHOWHIDDEN: Final = *********
OFN_EX_NOPLACESBAR: Final = 1
OFN_SHAREFALLTHROUGH: Final = 2
OFN_SHARENOWARN: Final = 1
OFN_SHAREWARN: Final = 0
CDN_FIRST: Final[int]
CDN_LAST: Final[int]
CDN_INITDONE: Final[int]
CDN_SELCHANGE: Final[int]
CDN_FOLDERCHANGE: Final[int]
CDN_SHAREVIOLATION: Final[int]
CDN_HELP: Final[int]
CDN_FILEOK: Final[int]
CDN_TYPECHANGE: Final[int]
CDN_INCLUDEITEM: Final[int]
CDM_FIRST: Final[int]
CDM_LAST: Final[int]
CDM_GETSPEC: Final[int]
CDM_GETFILEPATH: Final[int]
CDM_GETFOLDERPATH: Final[int]
CDM_GETFOLDERIDLIST: Final[int]
CDM_SETCONTROLTEXT: Final[int]
CDM_HIDECONTROL: Final[int]
CDM_SETDEFEXT: Final[int]
CC_RGBINIT: Final = 1
CC_FULLOPEN: Final = 2
CC_PREVENTFULLOPEN: Final = 4
CC_SHOWHELP: Final = 8
CC_ENABLEHOOK: Final = 16
CC_ENABLETEMPLATE: Final = 32
CC_ENABLETEMPLATEHANDLE: Final = 64
CC_SOLIDCOLOR: Final = 128
CC_ANYCOLOR: Final = 256
FR_DOWN: Final = 1
FR_WHOLEWORD: Final = 2
FR_MATCHCASE: Final = 4
FR_FINDNEXT: Final = 8
FR_REPLACE: Final = 16
FR_REPLACEALL: Final = 32
FR_DIALOGTERM: Final = 64
FR_SHOWHELP: Final = 128
FR_ENABLEHOOK: Final = 256
FR_ENABLETEMPLATE: Final = 512
FR_NOUPDOWN: Final = 1024
FR_NOMATCHCASE: Final = 2048
FR_NOWHOLEWORD: Final = 4096
FR_ENABLETEMPLATEHANDLE: Final = 8192
FR_HIDEUPDOWN: Final = 16384
FR_HIDEMATCHCASE: Final = 32768
FR_HIDEWHOLEWORD: Final = 65536
CF_SCREENFONTS: Final = 1
CF_PRINTERFONTS: Final = 2
CF_BOTH: Final[int]
CF_SHOWHELP: Final = 4
CF_ENABLEHOOK: Final = 8
CF_ENABLETEMPLATE: Final = 16
CF_ENABLETEMPLATEHANDLE: Final = 32
CF_INITTOLOGFONTSTRUCT: Final = 64
CF_USESTYLE: Final = 128
CF_EFFECTS: Final = 256
CF_APPLY: Final = 512
CF_ANSIONLY: Final = 1024
CF_SCRIPTSONLY: Final = CF_ANSIONLY
CF_NOVECTORFONTS: Final = 2048
CF_NOOEMFONTS: Final = CF_NOVECTORFONTS
CF_NOSIMULATIONS: Final = 4096
CF_LIMITSIZE: Final = 8192
CF_FIXEDPITCHONLY: Final = 16384
CF_WYSIWYG: Final = 32768
CF_FORCEFONTEXIST: Final = 65536
CF_SCALABLEONLY: Final = 131072
CF_TTONLY: Final = 262144
CF_NOFACESEL: Final = 524288
CF_NOSTYLESEL: Final = 1048576
CF_NOSIZESEL: Final = 2097152
CF_SELECTSCRIPT: Final = 4194304
CF_NOSCRIPTSEL: Final = 8388608
CF_NOVERTFONTS: Final = 16777216
SIMULATED_FONTTYPE: Final = 32768
PRINTER_FONTTYPE: Final = 16384
SCREEN_FONTTYPE: Final = 8192
BOLD_FONTTYPE: Final = 256
ITALIC_FONTTYPE: Final = 512
REGULAR_FONTTYPE: Final = 1024
OPENTYPE_FONTTYPE: Final = 65536
TYPE1_FONTTYPE: Final = 131072
DSIG_FONTTYPE: Final = 262144
WM_CHOOSEFONT_GETLOGFONT: Final[int]
WM_CHOOSEFONT_SETLOGFONT: Final[int]
WM_CHOOSEFONT_SETFLAGS: Final[int]
LBSELCHSTRINGA: Final = "commdlg_LBSelChangedNotify"
SHAREVISTRINGA: Final = "commdlg_ShareViolation"
FILEOKSTRINGA: Final = "commdlg_FileNameOK"
COLOROKSTRINGA: Final = "commdlg_ColorOK"
SETRGBSTRINGA: Final = "commdlg_SetRGBColor"
HELPMSGSTRINGA: Final = "commdlg_help"
FINDMSGSTRINGA: Final = "commdlg_FindReplace"
LBSELCHSTRING: Final = LBSELCHSTRINGA
SHAREVISTRING: Final = SHAREVISTRINGA
FILEOKSTRING: Final = FILEOKSTRINGA
COLOROKSTRING: Final = COLOROKSTRINGA
SETRGBSTRING: Final = SETRGBSTRINGA
HELPMSGSTRING: Final = HELPMSGSTRINGA
FINDMSGSTRING: Final = FINDMSGSTRINGA
CD_LBSELNOITEMS: Final = -1
CD_LBSELCHANGE: Final = 0
CD_LBSELSUB: Final = 1
CD_LBSELADD: Final = 2
PD_ALLPAGES: Final = 0
PD_SELECTION: Final = 1
PD_PAGENUMS: Final = 2
PD_NOSELECTION: Final = 4
PD_NOPAGENUMS: Final = 8
PD_COLLATE: Final = 16
PD_PRINTTOFILE: Final = 32
PD_PRINTSETUP: Final = 64
PD_NOWARNING: Final = 128
PD_RETURNDC: Final = 256
PD_RETURNIC: Final = 512
PD_RETURNDEFAULT: Final = 1024
PD_SHOWHELP: Final = 2048
PD_ENABLEPRINTHOOK: Final = 4096
PD_ENABLESETUPHOOK: Final = 8192
PD_ENABLEPRINTTEMPLATE: Final = 16384
PD_ENABLESETUPTEMPLATE: Final = 32768
PD_ENABLEPRINTTEMPLATEHANDLE: Final = 65536
PD_ENABLESETUPTEMPLATEHANDLE: Final = 131072
PD_USEDEVMODECOPIES: Final = 262144
PD_DISABLEPRINTTOFILE: Final = 524288
PD_HIDEPRINTTOFILE: Final = 1048576
PD_NONETWORKBUTTON: Final = 2097152
DN_DEFAULTPRN: Final = 1
WM_PSD_PAGESETUPDLG: Final = WM_USER
WM_PSD_FULLPAGERECT: Final[int]
WM_PSD_MINMARGINRECT: Final[int]
WM_PSD_MARGINRECT: Final[int]
WM_PSD_GREEKTEXTRECT: Final[int]
WM_PSD_ENVSTAMPRECT: Final[int]
WM_PSD_YAFULLPAGERECT: Final[int]
PSD_DEFAULTMINMARGINS: Final = 0
PSD_INWININIINTLMEASURE: Final = 0
PSD_MINMARGINS: Final = 1
PSD_MARGINS: Final = 2
PSD_INTHOUSANDTHSOFINCHES: Final = 4
PSD_INHUNDREDTHSOFMILLIMETERS: Final = 8
PSD_DISABLEMARGINS: Final = 16
PSD_DISABLEPRINTER: Final = 32
PSD_NOWARNING: Final = 128
PSD_DISABLEORIENTATION: Final = 256
PSD_RETURNDEFAULT: Final = 1024
PSD_DISABLEPAPER: Final = 512
PSD_SHOWHELP: Final = 2048
PSD_ENABLEPAGESETUPHOOK: Final = 8192
PSD_ENABLEPAGESETUPTEMPLATE: Final = 32768
PSD_ENABLEPAGESETUPTEMPLATEHANDLE: Final = 131072
PSD_ENABLEPAGEPAINTHOOK: Final = 262144
PSD_DISABLEPAGEPAINTING: Final = 524288
PSD_NONETWORKBUTTON: Final = 2097152

HKEY_CLASSES_ROOT: Final = -**********
HKEY_CURRENT_USER: Final = -2147483647
HKEY_LOCAL_MACHINE: Final = -2147483646
HKEY_USERS: Final = -2147483645
HKEY_PERFORMANCE_DATA: Final = -2147483644
HKEY_CURRENT_CONFIG: Final = -2147483643
HKEY_DYN_DATA: Final = -2147483642
HKEY_PERFORMANCE_TEXT: Final = -2147483568
HKEY_PERFORMANCE_NLSTEXT: Final = -2147483552

HWND_BROADCAST: Final = 65535
HWND_DESKTOP: Final = 0
HWND_TOP: Final = 0
HWND_BOTTOM: Final = 1
HWND_TOPMOST: Final = -1
HWND_NOTOPMOST: Final = -2
HWND_MESSAGE: Final = -3

SM_CXSCREEN: Final = 0
SM_CYSCREEN: Final = 1
SM_CXVSCROLL: Final = 2
SM_CYHSCROLL: Final = 3
SM_CYCAPTION: Final = 4
SM_CXBORDER: Final = 5
SM_CYBORDER: Final = 6
SM_CXDLGFRAME: Final = 7
SM_CYDLGFRAME: Final = 8
SM_CYVTHUMB: Final = 9
SM_CXHTHUMB: Final = 10
SM_CXICON: Final = 11
SM_CYICON: Final = 12
SM_CXCURSOR: Final = 13
SM_CYCURSOR: Final = 14
SM_CYMENU: Final = 15
SM_CXFULLSCREEN: Final = 16
SM_CYFULLSCREEN: Final = 17
SM_CYKANJIWINDOW: Final = 18
SM_MOUSEPRESENT: Final = 19
SM_CYVSCROLL: Final = 20
SM_CXHSCROLL: Final = 21
SM_DEBUG: Final = 22
SM_SWAPBUTTON: Final = 23
SM_RESERVED1: Final = 24
SM_RESERVED2: Final = 25
SM_RESERVED3: Final = 26
SM_RESERVED4: Final = 27
SM_CXMIN: Final = 28
SM_CYMIN: Final = 29
SM_CXSIZE: Final = 30
SM_CYSIZE: Final = 31
SM_CXFRAME: Final = 32
SM_CYFRAME: Final = 33
SM_CXMINTRACK: Final = 34
SM_CYMINTRACK: Final = 35
SM_CXDOUBLECLK: Final = 36
SM_CYDOUBLECLK: Final = 37
SM_CXICONSPACING: Final = 38
SM_CYICONSPACING: Final = 39
SM_MENUDROPALIGNMENT: Final = 40
SM_PENWINDOWS: Final = 41
SM_DBCSENABLED: Final = 42
SM_CMOUSEBUTTONS: Final = 43
SM_CXFIXEDFRAME: Final = SM_CXDLGFRAME
SM_CYFIXEDFRAME: Final = SM_CYDLGFRAME
SM_CXSIZEFRAME: Final = SM_CXFRAME
SM_CYSIZEFRAME: Final = SM_CYFRAME
SM_SECURE: Final = 44
SM_CXEDGE: Final = 45
SM_CYEDGE: Final = 46
SM_CXMINSPACING: Final = 47
SM_CYMINSPACING: Final = 48
SM_CXSMICON: Final = 49
SM_CYSMICON: Final = 50
SM_CYSMCAPTION: Final = 51
SM_CXSMSIZE: Final = 52
SM_CYSMSIZE: Final = 53
SM_CXMENUSIZE: Final = 54
SM_CYMENUSIZE: Final = 55
SM_ARRANGE: Final = 56
SM_CXMINIMIZED: Final = 57
SM_CYMINIMIZED: Final = 58
SM_CXMAXTRACK: Final = 59
SM_CYMAXTRACK: Final = 60
SM_CXMAXIMIZED: Final = 61
SM_CYMAXIMIZED: Final = 62
SM_NETWORK: Final = 63
SM_CLEANBOOT: Final = 67
SM_CXDRAG: Final = 68
SM_CYDRAG: Final = 69
SM_SHOWSOUNDS: Final = 70
SM_CXMENUCHECK: Final = 71
SM_CYMENUCHECK: Final = 72
SM_SLOWMACHINE: Final = 73
SM_MIDEASTENABLED: Final = 74
SM_MOUSEWHEELPRESENT: Final = 75
SM_XVIRTUALSCREEN: Final = 76
SM_YVIRTUALSCREEN: Final = 77
SM_CXVIRTUALSCREEN: Final = 78
SM_CYVIRTUALSCREEN: Final = 79
SM_CMONITORS: Final = 80
SM_SAMEDISPLAYFORMAT: Final = 81
SM_CMETRICS: Final = 83
MNC_IGNORE: Final = 0
MNC_CLOSE: Final = 1
MNC_EXECUTE: Final = 2
MNC_SELECT: Final = 3
MNS_NOCHECK: Final = -**********
MNS_MODELESS: Final = **********
MNS_DRAGDROP: Final = *********
MNS_AUTODISMISS: Final = *********
MNS_NOTIFYBYPOS: Final = *********
MNS_CHECKORBMP: Final = 67108864
MIM_MAXHEIGHT: Final = 1
MIM_BACKGROUND: Final = 2
MIM_HELPID: Final = 4
MIM_MENUDATA: Final = 8
MIM_STYLE: Final = 16
MIM_APPLYTOSUBMENUS: Final = -**********
MND_CONTINUE: Final = 0
MND_ENDMENU: Final = 1
MNGOF_GAP: Final = 3
MNGO_NOINTERFACE: Final = 0
MNGO_NOERROR: Final = 1
MIIM_STATE: Final = 1
MIIM_ID: Final = 2
MIIM_SUBMENU: Final = 4
MIIM_CHECKMARKS: Final = 8
MIIM_TYPE: Final = 16
MIIM_DATA: Final = 32
MIIM_STRING: Final = 64
MIIM_BITMAP: Final = 128
MIIM_FTYPE: Final = 256
HBMMENU_CALLBACK: Final = -1
HBMMENU_SYSTEM: Final = 1
HBMMENU_MBAR_RESTORE: Final = 2
HBMMENU_MBAR_MINIMIZE: Final = 3
HBMMENU_MBAR_CLOSE: Final = 5
HBMMENU_MBAR_CLOSE_D: Final = 6
HBMMENU_MBAR_MINIMIZE_D: Final = 7
HBMMENU_POPUP_CLOSE: Final = 8
HBMMENU_POPUP_RESTORE: Final = 9
HBMMENU_POPUP_MAXIMIZE: Final = 10
HBMMENU_POPUP_MINIMIZE: Final = 11
GMDI_USEDISABLED: Final = 1
GMDI_GOINTOPOPUPS: Final = 2
TPM_LEFTBUTTON: Final = 0
TPM_RIGHTBUTTON: Final = 2
TPM_LEFTALIGN: Final = 0
TPM_CENTERALIGN: Final = 4
TPM_RIGHTALIGN: Final = 8
TPM_TOPALIGN: Final = 0
TPM_VCENTERALIGN: Final = 16
TPM_BOTTOMALIGN: Final = 32
TPM_HORIZONTAL: Final = 0
TPM_VERTICAL: Final = 64
TPM_NONOTIFY: Final = 128
TPM_RETURNCMD: Final = 256
TPM_RECURSE: Final = 1
DOF_EXECUTABLE: Final = 32769
DOF_DOCUMENT: Final = 32770
DOF_DIRECTORY: Final = 32771
DOF_MULTIPLE: Final = 32772
DOF_PROGMAN: Final = 1
DOF_SHELLDATA: Final = 2
DO_DROPFILE: Final = 1162627398
DO_PRINTFILE: Final = 1414419024
DT_TOP: Final = 0
DT_LEFT: Final = 0
DT_CENTER: Final = 1
DT_RIGHT: Final = 2
DT_VCENTER: Final = 4
DT_BOTTOM: Final = 8
DT_WORDBREAK: Final = 16
DT_SINGLELINE: Final = 32
DT_EXPANDTABS: Final = 64
DT_TABSTOP: Final = 128
DT_NOCLIP: Final = 256
DT_EXTERNALLEADING: Final = 512
DT_CALCRECT: Final = 1024
DT_NOPREFIX: Final = 2048
DT_INTERNAL: Final = 4096
DT_EDITCONTROL: Final = 8192
DT_PATH_ELLIPSIS: Final = 16384
DT_END_ELLIPSIS: Final = 32768
DT_MODIFYSTRING: Final = 65536
DT_RTLREADING: Final = 131072
DT_WORD_ELLIPSIS: Final = 262144
DST_COMPLEX: Final = 0
DST_TEXT: Final = 1
DST_PREFIXTEXT: Final = 2
DST_ICON: Final = 3
DST_BITMAP: Final = 4
DSS_NORMAL: Final = 0
DSS_UNION: Final = 16
DSS_DISABLED: Final = 32
DSS_MONO: Final = 128
DSS_RIGHT: Final = 32768
DCX_WINDOW: Final = 1
DCX_CACHE: Final = 2
DCX_NORESETATTRS: Final = 4
DCX_CLIPCHILDREN: Final = 8
DCX_CLIPSIBLINGS: Final = 16
DCX_PARENTCLIP: Final = 32
DCX_EXCLUDERGN: Final = 64
DCX_INTERSECTRGN: Final = 128
DCX_EXCLUDEUPDATE: Final = 256
DCX_INTERSECTUPDATE: Final = 512
DCX_LOCKWINDOWUPDATE: Final = 1024
DCX_VALIDATE: Final = 2097152
CUDR_NORMAL: Final = 0
CUDR_NOSNAPTOGRID: Final = 1
CUDR_NORESOLVEPOSITIONS: Final = 2
CUDR_NOCLOSEGAPS: Final = 4
CUDR_NEGATIVECOORDS: Final = 8
CUDR_NOPRIMARY: Final = 16
RDW_INVALIDATE: Final = 1
RDW_INTERNALPAINT: Final = 2
RDW_ERASE: Final = 4
RDW_VALIDATE: Final = 8
RDW_NOINTERNALPAINT: Final = 16
RDW_NOERASE: Final = 32
RDW_NOCHILDREN: Final = 64
RDW_ALLCHILDREN: Final = 128
RDW_UPDATENOW: Final = 256
RDW_ERASENOW: Final = 512
RDW_FRAME: Final = 1024
RDW_NOFRAME: Final = 2048
SW_SCROLLCHILDREN: Final = 1
SW_INVALIDATE: Final = 2
SW_ERASE: Final = 4
SW_SMOOTHSCROLL: Final = 16
ESB_ENABLE_BOTH: Final = 0
ESB_DISABLE_BOTH: Final = 3
ESB_DISABLE_LEFT: Final = 1
ESB_DISABLE_RIGHT: Final = 2
ESB_DISABLE_UP: Final = 1
ESB_DISABLE_DOWN: Final = 2
ESB_DISABLE_LTUP: Final = ESB_DISABLE_LEFT
ESB_DISABLE_RTDN: Final = ESB_DISABLE_RIGHT
HELPINFO_WINDOW: Final = 1
HELPINFO_MENUITEM: Final = 2
MB_OK: Final = 0
MB_OKCANCEL: Final = 1
MB_ABORTRETRYIGNORE: Final = 2
MB_YESNOCANCEL: Final = 3
MB_YESNO: Final = 4
MB_RETRYCANCEL: Final = 5
MB_ICONHAND: Final = 16
MB_ICONQUESTION: Final = 32
MB_ICONEXCLAMATION: Final = 48
MB_ICONASTERISK: Final = 64
MB_ICONWARNING: Final = MB_ICONEXCLAMATION
MB_ICONERROR: Final = MB_ICONHAND
MB_ICONINFORMATION: Final = MB_ICONASTERISK
MB_ICONSTOP: Final = MB_ICONHAND
MB_DEFBUTTON1: Final = 0
MB_DEFBUTTON2: Final = 256
MB_DEFBUTTON3: Final = 512
MB_DEFBUTTON4: Final = 768
MB_APPLMODAL: Final = 0
MB_SYSTEMMODAL: Final = 4096
MB_TASKMODAL: Final = 8192
MB_HELP: Final = 16384
MB_NOFOCUS: Final = 32768
MB_SETFOREGROUND: Final = 65536
MB_DEFAULT_DESKTOP_ONLY: Final = 131072
MB_TOPMOST: Final = 262144
MB_RIGHT: Final = 524288
MB_RTLREADING: Final = 1048576
MB_SERVICE_NOTIFICATION: Final = 2097152
MB_TYPEMASK: Final = 15
MB_USERICON: Final = 128
MB_ICONMASK: Final = 240
MB_DEFMASK: Final = 3840
MB_MODEMASK: Final = 12288
MB_MISCMASK: Final = 49152

CWP_ALL: Final = 0
CWP_SKIPINVISIBLE: Final = 1
CWP_SKIPDISABLED: Final = 2
CWP_SKIPTRANSPARENT: Final = 4
CTLCOLOR_MSGBOX: Final = 0
CTLCOLOR_EDIT: Final = 1
CTLCOLOR_LISTBOX: Final = 2
CTLCOLOR_BTN: Final = 3
CTLCOLOR_DLG: Final = 4
CTLCOLOR_SCROLLBAR: Final = 5
CTLCOLOR_STATIC: Final = 6
CTLCOLOR_MAX: Final = 7
COLOR_SCROLLBAR: Final = 0
COLOR_BACKGROUND: Final = 1
COLOR_ACTIVECAPTION: Final = 2
COLOR_INACTIVECAPTION: Final = 3
COLOR_MENU: Final = 4
COLOR_WINDOW: Final = 5
COLOR_WINDOWFRAME: Final = 6
COLOR_MENUTEXT: Final = 7
COLOR_WINDOWTEXT: Final = 8
COLOR_CAPTIONTEXT: Final = 9
COLOR_ACTIVEBORDER: Final = 10
COLOR_INACTIVEBORDER: Final = 11
COLOR_APPWORKSPACE: Final = 12
COLOR_HIGHLIGHT: Final = 13
COLOR_HIGHLIGHTTEXT: Final = 14
COLOR_BTNFACE: Final = 15
COLOR_BTNSHADOW: Final = 16
COLOR_GRAYTEXT: Final = 17
COLOR_BTNTEXT: Final = 18
COLOR_INACTIVECAPTIONTEXT: Final = 19
COLOR_BTNHIGHLIGHT: Final = 20
COLOR_3DDKSHADOW: Final = 21
COLOR_3DLIGHT: Final = 22
COLOR_INFOTEXT: Final = 23
COLOR_INFOBK: Final = 24
COLOR_HOTLIGHT: Final = 26
COLOR_GRADIENTACTIVECAPTION: Final = 27
COLOR_GRADIENTINACTIVECAPTION: Final = 28
COLOR_DESKTOP: Final = COLOR_BACKGROUND
COLOR_3DFACE: Final = COLOR_BTNFACE
COLOR_3DSHADOW: Final = COLOR_BTNSHADOW
COLOR_3DHIGHLIGHT: Final = COLOR_BTNHIGHLIGHT
COLOR_3DHILIGHT: Final = COLOR_BTNHIGHLIGHT
COLOR_BTNHILIGHT: Final = COLOR_BTNHIGHLIGHT
GW_HWNDFIRST: Final = 0
GW_HWNDLAST: Final = 1
GW_HWNDNEXT: Final = 2
GW_HWNDPREV: Final = 3
GW_OWNER: Final = 4
GW_CHILD: Final = 5
GW_ENABLEDPOPUP: Final = 6
GW_MAX: Final = 6
MF_INSERT: Final = 0
MF_CHANGE: Final = 128
MF_APPEND: Final = 256
MF_DELETE: Final = 512
MF_REMOVE: Final = 4096
MF_BYCOMMAND: Final = 0
MF_BYPOSITION: Final = 1024
MF_SEPARATOR: Final = 2048
MF_ENABLED: Final = 0
MF_GRAYED: Final = 1
MF_DISABLED: Final = 2
MF_UNCHECKED: Final = 0
MF_CHECKED: Final = 8
MF_USECHECKBITMAPS: Final = 512
MF_STRING: Final = 0
MF_BITMAP: Final = 4
MF_OWNERDRAW: Final = 256
MF_POPUP: Final = 16
MF_MENUBARBREAK: Final = 32
MF_MENUBREAK: Final = 64
MF_UNHILITE: Final = 0
MF_HILITE: Final = 128
MF_DEFAULT: Final = 4096
MF_SYSMENU: Final = 8192
MF_HELP: Final = 16384
MF_RIGHTJUSTIFY: Final = 16384
MF_MOUSESELECT: Final = 32768
MF_END: Final = 128
MFT_STRING: Final = MF_STRING
MFT_BITMAP: Final = MF_BITMAP
MFT_MENUBARBREAK: Final = MF_MENUBARBREAK
MFT_MENUBREAK: Final = MF_MENUBREAK
MFT_OWNERDRAW: Final = MF_OWNERDRAW
MFT_RADIOCHECK: Final = 512
MFT_SEPARATOR: Final = MF_SEPARATOR
MFT_RIGHTORDER: Final = 8192
MFT_RIGHTJUSTIFY: Final = MF_RIGHTJUSTIFY
MFS_GRAYED: Final = 3
MFS_DISABLED: Final = MFS_GRAYED
MFS_CHECKED: Final = MF_CHECKED
MFS_HILITE: Final = MF_HILITE
MFS_ENABLED: Final = MF_ENABLED
MFS_UNCHECKED: Final = MF_UNCHECKED
MFS_UNHILITE: Final = MF_UNHILITE
MFS_DEFAULT: Final = MF_DEFAULT
MFS_MASK: Final = 4235
MFS_HOTTRACKDRAWN: Final = *********
MFS_CACHEDBMP: Final = *********
MFS_BOTTOMGAPDROP: Final = **********
MFS_TOPGAPDROP: Final = -**********
MFS_GAPDROP: Final = -**********
SC_SIZE: Final = 61440
SC_MOVE: Final = 61456
SC_MINIMIZE: Final = 61472
SC_MAXIMIZE: Final = 61488
SC_NEXTWINDOW: Final = 61504
SC_PREVWINDOW: Final = 61520
SC_CLOSE: Final = 61536
SC_VSCROLL: Final = 61552
SC_HSCROLL: Final = 61568
SC_MOUSEMENU: Final = 61584
SC_KEYMENU: Final = 61696
SC_ARRANGE: Final = 61712
SC_RESTORE: Final = 61728
SC_TASKLIST: Final = 61744
SC_SCREENSAVE: Final = 61760
SC_HOTKEY: Final = 61776
SC_DEFAULT: Final = 61792
SC_MONITORPOWER: Final = 61808
SC_CONTEXTHELP: Final = 61824
SC_SEPARATOR: Final = 61455
SC_ICON: Final = SC_MINIMIZE
SC_ZOOM: Final = SC_MAXIMIZE
IDC_ARROW: Final = 32512
IDC_IBEAM: Final = 32513
IDC_WAIT: Final = 32514
IDC_CROSS: Final = 32515
IDC_UPARROW: Final = 32516
IDC_SIZE: Final = 32640
IDC_ICON: Final = 32641
IDC_SIZENWSE: Final = 32642
IDC_SIZENESW: Final = 32643
IDC_SIZEWE: Final = 32644
IDC_SIZENS: Final = 32645
IDC_SIZEALL: Final = 32646
IDC_NO: Final = 32648
IDC_HAND: Final = 32649
IDC_APPSTARTING: Final = 32650
IDC_HELP: Final = 32651
IMAGE_BITMAP: Final = 0
IMAGE_ICON: Final = 1
IMAGE_CURSOR: Final = 2
IMAGE_ENHMETAFILE: Final = 3
LR_DEFAULTCOLOR: Final = 0
LR_MONOCHROME: Final = 1
LR_COLOR: Final = 2
LR_COPYRETURNORG: Final = 4
LR_COPYDELETEORG: Final = 8
LR_LOADFROMFILE: Final = 16
LR_LOADTRANSPARENT: Final = 32
LR_DEFAULTSIZE: Final = 64
LR_LOADREALSIZE: Final = 128
LR_LOADMAP3DCOLORS: Final = 4096
LR_CREATEDIBSECTION: Final = 8192
LR_COPYFROMRESOURCE: Final = 16384
LR_SHARED: Final = 32768
DI_MASK: Final = 1
DI_IMAGE: Final = 2
DI_NORMAL: Final = 3
DI_COMPAT: Final = 4
DI_DEFAULTSIZE: Final = 8
RES_ICON: Final = 1
RES_CURSOR: Final = 2
OBM_CLOSE: Final = 32754
OBM_UPARROW: Final = 32753
OBM_DNARROW: Final = 32752
OBM_RGARROW: Final = 32751
OBM_LFARROW: Final = 32750
OBM_REDUCE: Final = 32749
OBM_ZOOM: Final = 32748
OBM_RESTORE: Final = 32747
OBM_REDUCED: Final = 32746
OBM_ZOOMD: Final = 32745
OBM_RESTORED: Final = 32744
OBM_UPARROWD: Final = 32743
OBM_DNARROWD: Final = 32742
OBM_RGARROWD: Final = 32741
OBM_LFARROWD: Final = 32740
OBM_MNARROW: Final = 32739
OBM_COMBO: Final = 32738
OBM_UPARROWI: Final = 32737
OBM_DNARROWI: Final = 32736
OBM_RGARROWI: Final = 32735
OBM_LFARROWI: Final = 32734
OBM_OLD_CLOSE: Final = 32767
OBM_SIZE: Final = 32766
OBM_OLD_UPARROW: Final = 32765
OBM_OLD_DNARROW: Final = 32764
OBM_OLD_RGARROW: Final = 32763
OBM_OLD_LFARROW: Final = 32762
OBM_BTSIZE: Final = 32761
OBM_CHECK: Final = 32760
OBM_CHECKBOXES: Final = 32759
OBM_BTNCORNERS: Final = 32758
OBM_OLD_REDUCE: Final = 32757
OBM_OLD_ZOOM: Final = 32756
OBM_OLD_RESTORE: Final = 32755
OCR_NORMAL: Final = 32512
OCR_IBEAM: Final = 32513
OCR_WAIT: Final = 32514
OCR_CROSS: Final = 32515
OCR_UP: Final = 32516
OCR_SIZE: Final = 32640
OCR_ICON: Final = 32641
OCR_SIZENWSE: Final = 32642
OCR_SIZENESW: Final = 32643
OCR_SIZEWE: Final = 32644
OCR_SIZENS: Final = 32645
OCR_SIZEALL: Final = 32646
OCR_ICOCUR: Final = 32647
OCR_NO: Final = 32648
OCR_HAND: Final = 32649
OCR_APPSTARTING: Final = 32650

OIC_SAMPLE: Final = 32512
OIC_HAND: Final = 32513
OIC_QUES: Final = 32514
OIC_BANG: Final = 32515
OIC_NOTE: Final = 32516
OIC_WINLOGO: Final = 32517
OIC_WARNING: Final = OIC_BANG
OIC_ERROR: Final = OIC_HAND
OIC_INFORMATION: Final = OIC_NOTE
ORD_LANGDRIVER: Final = 1
IDI_APPLICATION: Final = 32512
IDI_HAND: Final = 32513
IDI_QUESTION: Final = 32514
IDI_EXCLAMATION: Final = 32515
IDI_ASTERISK: Final = 32516
IDI_WINLOGO: Final = 32517
IDI_WARNING: Final = IDI_EXCLAMATION
IDI_ERROR: Final = IDI_HAND
IDI_INFORMATION: Final = IDI_ASTERISK
IDOK: Final = 1
IDCANCEL: Final = 2
IDABORT: Final = 3
IDRETRY: Final = 4
IDIGNORE: Final = 5
IDYES: Final = 6
IDNO: Final = 7
IDCLOSE: Final = 8
IDHELP: Final = 9
ES_LEFT: Final = 0
ES_CENTER: Final = 1
ES_RIGHT: Final = 2
ES_MULTILINE: Final = 4
ES_UPPERCASE: Final = 8
ES_LOWERCASE: Final = 16
ES_PASSWORD: Final = 32
ES_AUTOVSCROLL: Final = 64
ES_AUTOHSCROLL: Final = 128
ES_NOHIDESEL: Final = 256
ES_OEMCONVERT: Final = 1024
ES_READONLY: Final = 2048
ES_WANTRETURN: Final = 4096
ES_NUMBER: Final = 8192
EN_SETFOCUS: Final = 256
EN_KILLFOCUS: Final = 512
EN_CHANGE: Final = 768
EN_UPDATE: Final = 1024
EN_ERRSPACE: Final = 1280
EN_MAXTEXT: Final = 1281
EN_HSCROLL: Final = 1537
EN_VSCROLL: Final = 1538
EC_LEFTMARGIN: Final = 1
EC_RIGHTMARGIN: Final = 2
EC_USEFONTINFO: Final = 65535
EMSIS_COMPOSITIONSTRING: Final = 1
EIMES_GETCOMPSTRATONCE: Final = 1
EIMES_CANCELCOMPSTRINFOCUS: Final = 2
EIMES_COMPLETECOMPSTRKILLFOCUS: Final = 4
EM_GETSEL: Final = 176
EM_SETSEL: Final = 177
EM_GETRECT: Final = 178
EM_SETRECT: Final = 179
EM_SETRECTNP: Final = 180
EM_SCROLL: Final = 181
EM_LINESCROLL: Final = 182
EM_SCROLLCARET: Final = 183
EM_GETMODIFY: Final = 184
EM_SETMODIFY: Final = 185
EM_GETLINECOUNT: Final = 186
EM_LINEINDEX: Final = 187
EM_SETHANDLE: Final = 188
EM_GETHANDLE: Final = 189
EM_GETTHUMB: Final = 190
EM_LINELENGTH: Final = 193
EM_REPLACESEL: Final = 194
EM_GETLINE: Final = 196
EM_LIMITTEXT: Final = 197
EM_CANUNDO: Final = 198
EM_UNDO: Final = 199
EM_FMTLINES: Final = 200
EM_LINEFROMCHAR: Final = 201
EM_SETTABSTOPS: Final = 203
EM_SETPASSWORDCHAR: Final = 204
EM_EMPTYUNDOBUFFER: Final = 205
EM_GETFIRSTVISIBLELINE: Final = 206
EM_SETREADONLY: Final = 207
EM_SETWORDBREAKPROC: Final = 208
EM_GETWORDBREAKPROC: Final = 209
EM_GETPASSWORDCHAR: Final = 210
EM_SETMARGINS: Final = 211
EM_GETMARGINS: Final = 212
EM_SETLIMITTEXT: Final = EM_LIMITTEXT
EM_GETLIMITTEXT: Final = 213
EM_POSFROMCHAR: Final = 214
EM_CHARFROMPOS: Final = 215
EM_SETIMESTATUS: Final = 216
EM_GETIMESTATUS: Final = 217
WB_LEFT: Final = 0
WB_RIGHT: Final = 1
WB_ISDELIMITER: Final = 2
BS_PUSHBUTTON: Final = 0
BS_DEFPUSHBUTTON: Final = 1
BS_CHECKBOX: Final = 2
BS_AUTOCHECKBOX: Final = 3
BS_RADIOBUTTON: Final = 4
BS_3STATE: Final = 5
BS_AUTO3STATE: Final = 6
BS_GROUPBOX: Final = 7
BS_USERBUTTON: Final = 8
BS_AUTORADIOBUTTON: Final = 9
BS_OWNERDRAW: Final = 11
BS_LEFTTEXT: Final = 32
BS_TEXT: Final = 0
BS_ICON: Final = 64
BS_BITMAP: Final = 128
BS_LEFT: Final = 256
BS_RIGHT: Final = 512
BS_CENTER: Final = 768
BS_TOP: Final = 1024
BS_BOTTOM: Final = 2048
BS_VCENTER: Final = 3072
BS_PUSHLIKE: Final = 4096
BS_MULTILINE: Final = 8192
BS_NOTIFY: Final = 16384
BS_FLAT: Final = 32768
BS_RIGHTBUTTON: Final = BS_LEFTTEXT
BN_CLICKED: Final = 0
BN_PAINT: Final = 1
BN_HILITE: Final = 2
BN_UNHILITE: Final = 3
BN_DISABLE: Final = 4
BN_DOUBLECLICKED: Final = 5
BN_PUSHED: Final = BN_HILITE
BN_UNPUSHED: Final = BN_UNHILITE
BN_DBLCLK: Final = BN_DOUBLECLICKED
BN_SETFOCUS: Final = 6
BN_KILLFOCUS: Final = 7
BM_GETCHECK: Final = 240
BM_SETCHECK: Final = 241
BM_GETSTATE: Final = 242
BM_SETSTATE: Final = 243
BM_SETSTYLE: Final = 244
BM_CLICK: Final = 245
BM_GETIMAGE: Final = 246
BM_SETIMAGE: Final = 247
BST_UNCHECKED: Final = 0
BST_CHECKED: Final = 1
BST_INDETERMINATE: Final = 2
BST_PUSHED: Final = 4
BST_FOCUS: Final = 8
SS_LEFT: Final = 0
SS_CENTER: Final = 1
SS_RIGHT: Final = 2
SS_ICON: Final = 3
SS_BLACKRECT: Final = 4
SS_GRAYRECT: Final = 5
SS_WHITERECT: Final = 6
SS_BLACKFRAME: Final = 7
SS_GRAYFRAME: Final = 8
SS_WHITEFRAME: Final = 9
SS_USERITEM: Final = 10
SS_SIMPLE: Final = 11
SS_LEFTNOWORDWRAP: Final = 12
SS_BITMAP: Final = 14
SS_OWNERDRAW: Final = 13
SS_ENHMETAFILE: Final = 15
SS_ETCHEDHORZ: Final = 16
SS_ETCHEDVERT: Final = 17
SS_ETCHEDFRAME: Final = 18
SS_TYPEMASK: Final = 31
SS_NOPREFIX: Final = 128
SS_NOTIFY: Final = 256
SS_CENTERIMAGE: Final = 512
SS_RIGHTJUST: Final = 1024
SS_REALSIZEIMAGE: Final = 2048
SS_SUNKEN: Final = 4096
SS_ENDELLIPSIS: Final = 16384
SS_PATHELLIPSIS: Final = 32768
SS_WORDELLIPSIS: Final = 49152
SS_ELLIPSISMASK: Final = 49152
STM_SETICON: Final = 368
STM_GETICON: Final = 369
STM_SETIMAGE: Final = 370
STM_GETIMAGE: Final = 371
STN_CLICKED: Final = 0
STN_DBLCLK: Final = 1
STN_ENABLE: Final = 2
STN_DISABLE: Final = 3
STM_MSGMAX: Final = 372
DWL_MSGRESULT: Final = 0
DWL_DLGPROC: Final = 4
DWL_USER: Final = 8
DDL_READWRITE: Final = 0
DDL_READONLY: Final = 1
DDL_HIDDEN: Final = 2
DDL_SYSTEM: Final = 4
DDL_DIRECTORY: Final = 16
DDL_ARCHIVE: Final = 32
DDL_POSTMSGS: Final = 8192
DDL_DRIVES: Final = 16384
DDL_EXCLUSIVE: Final = 32768

RT_CURSOR: Final = 1
RT_BITMAP: Final = 2
RT_ICON: Final = 3
RT_MENU: Final = 4
RT_DIALOG: Final = 5
RT_STRING: Final = 6
RT_FONTDIR: Final = 7
RT_FONT: Final = 8
RT_ACCELERATOR: Final = 9
RT_RCDATA: Final = 10
RT_MESSAGETABLE: Final = 11
DIFFERENCE: Final = 11
RT_GROUP_CURSOR: Final[int]
RT_GROUP_ICON: Final[int]
RT_VERSION: Final = 16
RT_DLGINCLUDE: Final = 17
RT_PLUGPLAY: Final = 19
RT_VXD: Final = 20
RT_ANICURSOR: Final = 21
RT_ANIICON: Final = 22
RT_HTML: Final = 23

SB_HORZ: Final = 0
SB_VERT: Final = 1
SB_CTL: Final = 2
SB_BOTH: Final = 3
SB_LINEUP: Final = 0
SB_LINELEFT: Final = 0
SB_LINEDOWN: Final = 1
SB_LINERIGHT: Final = 1
SB_PAGEUP: Final = 2
SB_PAGELEFT: Final = 2
SB_PAGEDOWN: Final = 3
SB_PAGERIGHT: Final = 3
SB_THUMBPOSITION: Final = 4
SB_THUMBTRACK: Final = 5
SB_TOP: Final = 6
SB_LEFT: Final = 6
SB_BOTTOM: Final = 7
SB_RIGHT: Final = 7
SB_ENDSCROLL: Final = 8
SW_HIDE: Final = 0
SW_SHOWNORMAL: Final = 1
SW_NORMAL: Final = 1
SW_SHOWMINIMIZED: Final = 2
SW_SHOWMAXIMIZED: Final = 3
SW_MAXIMIZE: Final = 3
SW_SHOWNOACTIVATE: Final = 4
SW_SHOW: Final = 5
SW_MINIMIZE: Final = 6
SW_SHOWMINNOACTIVE: Final = 7
SW_SHOWNA: Final = 8
SW_RESTORE: Final = 9
SW_SHOWDEFAULT: Final = 10
SW_FORCEMINIMIZE: Final = 11
SW_MAX: Final = 11
HIDE_WINDOW: Final = 0
SHOW_OPENWINDOW: Final = 1
SHOW_ICONWINDOW: Final = 2
SHOW_FULLSCREEN: Final = 3
SHOW_OPENNOACTIVATE: Final = 4
SW_PARENTCLOSING: Final = 1
SW_OTHERZOOM: Final = 2
SW_PARENTOPENING: Final = 3
SW_OTHERUNZOOM: Final = 4
AW_HOR_POSITIVE: Final = 1
AW_HOR_NEGATIVE: Final = 2
AW_VER_POSITIVE: Final = 4
AW_VER_NEGATIVE: Final = 8
AW_CENTER: Final = 16
AW_HIDE: Final = 65536
AW_ACTIVATE: Final = 131072
AW_SLIDE: Final = 262144
AW_BLEND: Final = 524288
KF_EXTENDED: Final = 256
KF_DLGMODE: Final = 2048
KF_MENUMODE: Final = 4096
KF_ALTDOWN: Final = 8192
KF_REPEAT: Final = 16384
KF_UP: Final = 32768
VK_LBUTTON: Final = 1
VK_RBUTTON: Final = 2
VK_CANCEL: Final = 3
VK_MBUTTON: Final = 4
VK_BACK: Final = 8
VK_TAB: Final = 9
VK_CLEAR: Final = 12
VK_RETURN: Final = 13
VK_SHIFT: Final = 16
VK_CONTROL: Final = 17
VK_MENU: Final = 18
VK_PAUSE: Final = 19
VK_CAPITAL: Final = 20
VK_KANA: Final = 21
VK_HANGEUL: Final = 21
VK_HANGUL: Final = 21
VK_JUNJA: Final = 23
VK_FINAL: Final = 24
VK_HANJA: Final = 25
VK_KANJI: Final = 25
VK_ESCAPE: Final = 27
VK_CONVERT: Final = 28
VK_NONCONVERT: Final = 29
VK_ACCEPT: Final = 30
VK_MODECHANGE: Final = 31
VK_SPACE: Final = 32
VK_PRIOR: Final = 33
VK_NEXT: Final = 34
VK_END: Final = 35
VK_HOME: Final = 36
VK_LEFT: Final = 37
VK_UP: Final = 38
VK_RIGHT: Final = 39
VK_DOWN: Final = 40
VK_SELECT: Final = 41
VK_PRINT: Final = 42
VK_EXECUTE: Final = 43
VK_SNAPSHOT: Final = 44
VK_INSERT: Final = 45
VK_DELETE: Final = 46
VK_HELP: Final = 47
VK_LWIN: Final = 91
VK_RWIN: Final = 92
VK_APPS: Final = 93
VK_NUMPAD0: Final = 96
VK_NUMPAD1: Final = 97
VK_NUMPAD2: Final = 98
VK_NUMPAD3: Final = 99
VK_NUMPAD4: Final = 100
VK_NUMPAD5: Final = 101
VK_NUMPAD6: Final = 102
VK_NUMPAD7: Final = 103
VK_NUMPAD8: Final = 104
VK_NUMPAD9: Final = 105
VK_MULTIPLY: Final = 106
VK_ADD: Final = 107
VK_SEPARATOR: Final = 108
VK_SUBTRACT: Final = 109
VK_DECIMAL: Final = 110
VK_DIVIDE: Final = 111
VK_F1: Final = 112
VK_F2: Final = 113
VK_F3: Final = 114
VK_F4: Final = 115
VK_F5: Final = 116
VK_F6: Final = 117
VK_F7: Final = 118
VK_F8: Final = 119
VK_F9: Final = 120
VK_F10: Final = 121
VK_F11: Final = 122
VK_F12: Final = 123
VK_F13: Final = 124
VK_F14: Final = 125
VK_F15: Final = 126
VK_F16: Final = 127
VK_F17: Final = 128
VK_F18: Final = 129
VK_F19: Final = 130
VK_F20: Final = 131
VK_F21: Final = 132
VK_F22: Final = 133
VK_F23: Final = 134
VK_F24: Final = 135
VK_NUMLOCK: Final = 144
VK_SCROLL: Final = 145
VK_LSHIFT: Final = 160
VK_RSHIFT: Final = 161
VK_LCONTROL: Final = 162
VK_RCONTROL: Final = 163
VK_LMENU: Final = 164
VK_RMENU: Final = 165
VK_PROCESSKEY: Final = 229
VK_ATTN: Final = 246
VK_CRSEL: Final = 247
VK_EXSEL: Final = 248
VK_EREOF: Final = 249
VK_PLAY: Final = 250
VK_ZOOM: Final = 251
VK_NONAME: Final = 252
VK_PA1: Final = 253
VK_OEM_CLEAR: Final = 254

VK_XBUTTON1: Final = 0x05
VK_XBUTTON2: Final = 0x06
VK_VOLUME_MUTE: Final = 0xAD
VK_VOLUME_DOWN: Final = 0xAE
VK_VOLUME_UP: Final = 0xAF
VK_MEDIA_NEXT_TRACK: Final = 0xB0
VK_MEDIA_PREV_TRACK: Final = 0xB1
VK_MEDIA_PLAY_PAUSE: Final = 0xB3
VK_BROWSER_BACK: Final = 0xA6
VK_BROWSER_FORWARD: Final = 0xA7
WH_MIN: Final = -1
WH_MSGFILTER: Final = -1
WH_JOURNALRECORD: Final = 0
WH_JOURNALPLAYBACK: Final = 1
WH_KEYBOARD: Final = 2
WH_GETMESSAGE: Final = 3
WH_CALLWNDPROC: Final = 4
WH_CBT: Final = 5
WH_SYSMSGFILTER: Final = 6
WH_MOUSE: Final = 7
WH_HARDWARE: Final = 8
WH_DEBUG: Final = 9
WH_SHELL: Final = 10
WH_FOREGROUNDIDLE: Final = 11
WH_CALLWNDPROCRET: Final = 12
WH_KEYBOARD_LL: Final = 13
WH_MOUSE_LL: Final = 14
WH_MAX: Final = 14
WH_MINHOOK: Final = WH_MIN
WH_MAXHOOK: Final = WH_MAX
HC_ACTION: Final = 0
HC_GETNEXT: Final = 1
HC_SKIP: Final = 2
HC_NOREMOVE: Final = 3
HC_NOREM: Final = HC_NOREMOVE
HC_SYSMODALON: Final = 4
HC_SYSMODALOFF: Final = 5
HCBT_MOVESIZE: Final = 0
HCBT_MINMAX: Final = 1
HCBT_QS: Final = 2
HCBT_CREATEWND: Final = 3
HCBT_DESTROYWND: Final = 4
HCBT_ACTIVATE: Final = 5
HCBT_CLICKSKIPPED: Final = 6
HCBT_KEYSKIPPED: Final = 7
HCBT_SYSCOMMAND: Final = 8
HCBT_SETFOCUS: Final = 9
MSGF_DIALOGBOX: Final = 0
MSGF_MESSAGEBOX: Final = 1
MSGF_MENU: Final = 2

MSGF_SCROLLBAR: Final = 5
MSGF_NEXTWINDOW: Final = 6

MSGF_MAX: Final = 8
MSGF_USER: Final = 4096
HSHELL_WINDOWCREATED: Final = 1
HSHELL_WINDOWDESTROYED: Final = 2
HSHELL_ACTIVATESHELLWINDOW: Final = 3
HSHELL_WINDOWACTIVATED: Final = 4
HSHELL_GETMINRECT: Final = 5
HSHELL_REDRAW: Final = 6
HSHELL_TASKMAN: Final = 7
HSHELL_LANGUAGE: Final = 8
HSHELL_ACCESSIBILITYSTATE: Final = 11
ACCESS_STICKYKEYS: Final = 1
ACCESS_FILTERKEYS: Final = 2
ACCESS_MOUSEKEYS: Final = 3

LLKHF_EXTENDED: Final = 1
LLKHF_INJECTED: Final = 16
LLKHF_ALTDOWN: Final = 32
LLKHF_UP: Final = 128
LLKHF_LOWER_IL_INJECTED: Final = 2
LLMHF_INJECTED: Final = 1
LLMHF_LOWER_IL_INJECTED: Final = 2

HKL_PREV: Final = 0
HKL_NEXT: Final = 1
KLF_ACTIVATE: Final = 1
KLF_SUBSTITUTE_OK: Final = 2
KLF_UNLOADPREVIOUS: Final = 4
KLF_REORDER: Final = 8
KLF_REPLACELANG: Final = 16
KLF_NOTELLSHELL: Final = 128
KLF_SETFORPROCESS: Final = 256
KL_NAMELENGTH: Final = 9
DESKTOP_READOBJECTS: Final = 1
DESKTOP_CREATEWINDOW: Final = 2
DESKTOP_CREATEMENU: Final = 4
DESKTOP_HOOKCONTROL: Final = 8
DESKTOP_JOURNALRECORD: Final = 16
DESKTOP_JOURNALPLAYBACK: Final = 32
DESKTOP_ENUMERATE: Final = 64
DESKTOP_WRITEOBJECTS: Final = 128
DESKTOP_SWITCHDESKTOP: Final = 256
DF_ALLOWOTHERACCOUNTHOOK: Final = 1
WINSTA_ENUMDESKTOPS: Final = 1
WINSTA_READATTRIBUTES: Final = 2
WINSTA_ACCESSCLIPBOARD: Final = 4
WINSTA_CREATEDESKTOP: Final = 8
WINSTA_WRITEATTRIBUTES: Final = 16
WINSTA_ACCESSGLOBALATOMS: Final = 32
WINSTA_EXITWINDOWS: Final = 64
WINSTA_ENUMERATE: Final = 256
WINSTA_READSCREEN: Final = 512
WSF_VISIBLE: Final = 1
UOI_FLAGS: Final = 1
UOI_NAME: Final = 2
UOI_TYPE: Final = 3
UOI_USER_SID: Final = 4
GWL_WNDPROC: Final = -4
GWL_HINSTANCE: Final = -6
GWL_HWNDPARENT: Final = -8
GWL_STYLE: Final = -16
GWL_EXSTYLE: Final = -20
GWL_USERDATA: Final = -21
GWL_ID: Final = -12
GCL_MENUNAME: Final = -8
GCL_HBRBACKGROUND: Final = -10
GCL_HCURSOR: Final = -12
GCL_HICON: Final = -14
GCL_HMODULE: Final = -16
GCL_CBWNDEXTRA: Final = -18
GCL_CBCLSEXTRA: Final = -20
GCL_WNDPROC: Final = -24
GCL_STYLE: Final = -26
GCW_ATOM: Final = -32
GCL_HICONSM: Final = -34

WM_NULL: Final = 0
WM_CREATE: Final = 1
WM_DESTROY: Final = 2
WM_MOVE: Final = 3
WM_SIZE: Final = 5
WM_ACTIVATE: Final = 6
WA_INACTIVE: Final = 0
WA_ACTIVE: Final = 1
WA_CLICKACTIVE: Final = 2
WM_SETFOCUS: Final = 7
WM_KILLFOCUS: Final = 8
WM_ENABLE: Final = 10
WM_SETREDRAW: Final = 11
WM_SETTEXT: Final = 12
WM_GETTEXT: Final = 13
WM_GETTEXTLENGTH: Final = 14
WM_PAINT: Final = 15
WM_CLOSE: Final = 16
WM_QUERYENDSESSION: Final = 17
WM_QUIT: Final = 18
WM_QUERYOPEN: Final = 19
WM_ERASEBKGND: Final = 20
WM_SYSCOLORCHANGE: Final = 21
WM_ENDSESSION: Final = 22
WM_SHOWWINDOW: Final = 24
WM_WININICHANGE: Final = 26
WM_SETTINGCHANGE: Final = WM_WININICHANGE
WM_DEVMODECHANGE: Final = 27
WM_ACTIVATEAPP: Final = 28
WM_FONTCHANGE: Final = 29
WM_TIMECHANGE: Final = 30
WM_CANCELMODE: Final = 31
WM_SETCURSOR: Final = 32
WM_MOUSEACTIVATE: Final = 33
WM_CHILDACTIVATE: Final = 34
WM_QUEUESYNC: Final = 35
WM_GETMINMAXINFO: Final = 36
WM_PAINTICON: Final = 38
WM_ICONERASEBKGND: Final = 39
WM_NEXTDLGCTL: Final = 40
WM_SPOOLERSTATUS: Final = 42
WM_DRAWITEM: Final = 43
WM_MEASUREITEM: Final = 44
WM_DELETEITEM: Final = 45
WM_VKEYTOITEM: Final = 46
WM_CHARTOITEM: Final = 47
WM_SETFONT: Final = 48
WM_GETFONT: Final = 49
WM_SETHOTKEY: Final = 50
WM_GETHOTKEY: Final = 51
WM_QUERYDRAGICON: Final = 55
WM_COMPAREITEM: Final = 57
WM_GETOBJECT: Final = 61
WM_COMPACTING: Final = 65
WM_COMMNOTIFY: Final = 68
WM_WINDOWPOSCHANGING: Final = 70
WM_WINDOWPOSCHANGED: Final = 71
WM_POWER: Final = 72
PWR_OK: Final = 1
PWR_FAIL: Final = -1
PWR_SUSPENDREQUEST: Final = 1
PWR_SUSPENDRESUME: Final = 2
PWR_CRITICALRESUME: Final = 3
WM_COPYDATA: Final = 74
WM_CANCELJOURNAL: Final = 75
WM_INPUTLANGCHANGEREQUEST: Final = 80
WM_INPUTLANGCHANGE: Final = 81
WM_TCARD: Final = 82
WM_HELP: Final = 83
WM_USERCHANGED: Final = 84
WM_NOTIFYFORMAT: Final = 85
NFR_ANSI: Final = 1
NFR_UNICODE: Final = 2
NF_QUERY: Final = 3
NF_REQUERY: Final = 4
WM_STYLECHANGING: Final = 124
WM_STYLECHANGED: Final = 125
WM_DISPLAYCHANGE: Final = 126
WM_GETICON: Final = 127
WM_SETICON: Final = 128
WM_NCCREATE: Final = 129
WM_NCDESTROY: Final = 130
WM_NCCALCSIZE: Final = 131
WM_NCHITTEST: Final = 132
WM_NCPAINT: Final = 133
WM_NCACTIVATE: Final = 134
WM_GETDLGCODE: Final = 135
WM_SYNCPAINT: Final = 136
WM_NCMOUSEMOVE: Final = 160
WM_NCLBUTTONDOWN: Final = 161
WM_NCLBUTTONUP: Final = 162
WM_NCLBUTTONDBLCLK: Final = 163
WM_NCRBUTTONDOWN: Final = 164
WM_NCRBUTTONUP: Final = 165
WM_NCRBUTTONDBLCLK: Final = 166
WM_NCMBUTTONDOWN: Final = 167
WM_NCMBUTTONUP: Final = 168
WM_NCMBUTTONDBLCLK: Final = 169
WM_KEYFIRST: Final = 256
WM_KEYDOWN: Final = 256
WM_KEYUP: Final = 257
WM_CHAR: Final = 258
WM_DEADCHAR: Final = 259
WM_SYSKEYDOWN: Final = 260
WM_SYSKEYUP: Final = 261
WM_SYSCHAR: Final = 262
WM_SYSDEADCHAR: Final = 263
WM_KEYLAST: Final = 264
WM_IME_STARTCOMPOSITION: Final = 269
WM_IME_ENDCOMPOSITION: Final = 270
WM_IME_COMPOSITION: Final = 271
WM_IME_KEYLAST: Final = 271
WM_INITDIALOG: Final = 272
WM_COMMAND: Final = 273
WM_SYSCOMMAND: Final = 274
WM_TIMER: Final = 275
WM_HSCROLL: Final = 276
WM_VSCROLL: Final = 277
WM_INITMENU: Final = 278
WM_INITMENUPOPUP: Final = 279
WM_MENUSELECT: Final = 287
WM_MENUCHAR: Final = 288
WM_ENTERIDLE: Final = 289
WM_MENURBUTTONUP: Final = 290
WM_MENUDRAG: Final = 291
WM_MENUGETOBJECT: Final = 292
WM_UNINITMENUPOPUP: Final = 293
WM_MENUCOMMAND: Final = 294
WM_CTLCOLORMSGBOX: Final = 306
WM_CTLCOLOREDIT: Final = 307
WM_CTLCOLORLISTBOX: Final = 308
WM_CTLCOLORBTN: Final = 309
WM_CTLCOLORDLG: Final = 310
WM_CTLCOLORSCROLLBAR: Final = 311
WM_CTLCOLORSTATIC: Final = 312
WM_MOUSEFIRST: Final = 512
WM_MOUSEMOVE: Final = 512
WM_LBUTTONDOWN: Final = 513
WM_LBUTTONUP: Final = 514
WM_LBUTTONDBLCLK: Final = 515
WM_RBUTTONDOWN: Final = 516
WM_RBUTTONUP: Final = 517
WM_RBUTTONDBLCLK: Final = 518
WM_MBUTTONDOWN: Final = 519
WM_MBUTTONUP: Final = 520
WM_MBUTTONDBLCLK: Final = 521
WM_MOUSEWHEEL: Final = 522
WM_MOUSELAST: Final = 522
WHEEL_DELTA: Final = 120
WHEEL_PAGESCROLL: Final = -1
WM_PARENTNOTIFY: Final = 528
MENULOOP_WINDOW: Final = 0
MENULOOP_POPUP: Final = 1
WM_ENTERMENULOOP: Final = 529
WM_EXITMENULOOP: Final = 530
WM_NEXTMENU: Final = 531
WM_SIZING: Final = 532
WM_CAPTURECHANGED: Final = 533
WM_MOVING: Final = 534
WM_POWERBROADCAST: Final = 536
PBT_APMQUERYSUSPEND: Final = 0
PBT_APMQUERYSTANDBY: Final = 1
PBT_APMQUERYSUSPENDFAILED: Final = 2
PBT_APMQUERYSTANDBYFAILED: Final = 3
PBT_APMSUSPEND: Final = 4
PBT_APMSTANDBY: Final = 5
PBT_APMRESUMECRITICAL: Final = 6
PBT_APMRESUMESUSPEND: Final = 7
PBT_APMRESUMESTANDBY: Final = 8
PBTF_APMRESUMEFROMFAILURE: Final = 1
PBT_APMBATTERYLOW: Final = 9
PBT_APMPOWERSTATUSCHANGE: Final = 10
PBT_APMOEMEVENT: Final = 11
PBT_APMRESUMEAUTOMATIC: Final = 18
WM_MDICREATE: Final = 544
WM_MDIDESTROY: Final = 545
WM_MDIACTIVATE: Final = 546
WM_MDIRESTORE: Final = 547
WM_MDINEXT: Final = 548
WM_MDIMAXIMIZE: Final = 549
WM_MDITILE: Final = 550
WM_MDICASCADE: Final = 551
WM_MDIICONARRANGE: Final = 552
WM_MDIGETACTIVE: Final = 553
WM_MDISETMENU: Final = 560
WM_ENTERSIZEMOVE: Final = 561
WM_EXITSIZEMOVE: Final = 562
WM_DROPFILES: Final = 563
WM_MDIREFRESHMENU: Final = 564
WM_IME_SETCONTEXT: Final = 641
WM_IME_NOTIFY: Final = 642
WM_IME_CONTROL: Final = 643
WM_IME_COMPOSITIONFULL: Final = 644
WM_IME_SELECT: Final = 645
WM_IME_CHAR: Final = 646
WM_IME_REQUEST: Final = 648
WM_IME_KEYDOWN: Final = 656
WM_IME_KEYUP: Final = 657
WM_MOUSEHOVER: Final = 673
WM_MOUSELEAVE: Final = 675
WM_CUT: Final = 768
WM_COPY: Final = 769
WM_PASTE: Final = 770
WM_CLEAR: Final = 771
WM_UNDO: Final = 772
WM_RENDERFORMAT: Final = 773
WM_RENDERALLFORMATS: Final = 774
WM_DESTROYCLIPBOARD: Final = 775
WM_DRAWCLIPBOARD: Final = 776
WM_PAINTCLIPBOARD: Final = 777
WM_VSCROLLCLIPBOARD: Final = 778
WM_SIZECLIPBOARD: Final = 779
WM_ASKCBFORMATNAME: Final = 780
WM_CHANGECBCHAIN: Final = 781
WM_HSCROLLCLIPBOARD: Final = 782
WM_QUERYNEWPALETTE: Final = 783
WM_PALETTEISCHANGING: Final = 784
WM_PALETTECHANGED: Final = 785
WM_HOTKEY: Final = 786
WM_PRINT: Final = 791
WM_HANDHELDFIRST: Final = 856
WM_HANDHELDLAST: Final = 863
WM_AFXFIRST: Final = 864
WM_AFXLAST: Final = 895
WM_PENWINFIRST: Final = 896
WM_PENWINLAST: Final = 911
WM_APP: Final = 32768
WMSZ_LEFT: Final = 1
WMSZ_RIGHT: Final = 2
WMSZ_TOP: Final = 3
WMSZ_TOPLEFT: Final = 4
WMSZ_TOPRIGHT: Final = 5
WMSZ_BOTTOM: Final = 6
WMSZ_BOTTOMLEFT: Final = 7
WMSZ_BOTTOMRIGHT: Final = 8

HTERROR: Final = -2
HTTRANSPARENT: Final = -1
HTNOWHERE: Final = 0
HTCLIENT: Final = 1
HTCAPTION: Final = 2
HTSYSMENU: Final = 3
HTGROWBOX: Final = 4
HTSIZE: Final = HTGROWBOX
HTMENU: Final = 5
HTHSCROLL: Final = 6
HTVSCROLL: Final = 7
HTMINBUTTON: Final = 8
HTMAXBUTTON: Final = 9
HTLEFT: Final = 10
HTRIGHT: Final = 11
HTTOP: Final = 12
HTTOPLEFT: Final = 13
HTTOPRIGHT: Final = 14
HTBOTTOM: Final = 15
HTBOTTOMLEFT: Final = 16
HTBOTTOMRIGHT: Final = 17
HTBORDER: Final = 18
HTREDUCE: Final = HTMINBUTTON
HTZOOM: Final = HTMAXBUTTON
HTSIZEFIRST: Final = HTLEFT
HTSIZELAST: Final = HTBOTTOMRIGHT
HTOBJECT: Final = 19
HTCLOSE: Final = 20
HTHELP: Final = 21
SMTO_NORMAL: Final = 0
SMTO_BLOCK: Final = 1
SMTO_ABORTIFHUNG: Final = 2
SMTO_NOTIMEOUTIFNOTHUNG: Final = 8
MA_ACTIVATE: Final = 1
MA_ACTIVATEANDEAT: Final = 2
MA_NOACTIVATE: Final = 3
MA_NOACTIVATEANDEAT: Final = 4
ICON_SMALL: Final = 0
ICON_BIG: Final = 1
SIZE_RESTORED: Final = 0
SIZE_MINIMIZED: Final = 1
SIZE_MAXIMIZED: Final = 2
SIZE_MAXSHOW: Final = 3
SIZE_MAXHIDE: Final = 4
SIZENORMAL: Final = SIZE_RESTORED
SIZEICONIC: Final = SIZE_MINIMIZED
SIZEFULLSCREEN: Final = SIZE_MAXIMIZED
SIZEZOOMSHOW: Final = SIZE_MAXSHOW
SIZEZOOMHIDE: Final = SIZE_MAXHIDE
WVR_ALIGNTOP: Final = 16
WVR_ALIGNLEFT: Final = 32
WVR_ALIGNBOTTOM: Final = 64
WVR_ALIGNRIGHT: Final = 128
WVR_HREDRAW: Final = 256
WVR_VREDRAW: Final = 512
WVR_REDRAW: Final[int]
WVR_VALIDRECTS: Final = 1024
MK_LBUTTON: Final = 1
MK_RBUTTON: Final = 2
MK_SHIFT: Final = 4
MK_CONTROL: Final = 8
MK_MBUTTON: Final = 16
TME_HOVER: Final = 1
TME_LEAVE: Final = 2
TME_QUERY: Final = **********
TME_CANCEL: Final = -**********
HOVER_DEFAULT: Final = -1
WS_OVERLAPPED: Final = 0
WS_POPUP: Final = -**********
WS_CHILD: Final = **********
WS_MINIMIZE: Final = *********
WS_VISIBLE: Final = *********
WS_DISABLED: Final = *********
WS_CLIPSIBLINGS: Final = 67108864
WS_CLIPCHILDREN: Final = 33554432
WS_MAXIMIZE: Final = 16777216
WS_CAPTION: Final = 12582912
WS_BORDER: Final = 8388608
WS_DLGFRAME: Final = 4194304
WS_VSCROLL: Final = 2097152
WS_HSCROLL: Final = 1048576
WS_SYSMENU: Final = 524288
WS_THICKFRAME: Final = 262144
WS_GROUP: Final = 131072
WS_TABSTOP: Final = 65536
WS_MINIMIZEBOX: Final = 131072
WS_MAXIMIZEBOX: Final = 65536
WS_TILED: Final = WS_OVERLAPPED
WS_ICONIC: Final = WS_MINIMIZE
WS_SIZEBOX: Final = WS_THICKFRAME
WS_OVERLAPPEDWINDOW: Final[int]
WS_POPUPWINDOW: Final[int]
WS_CHILDWINDOW: Final = WS_CHILD
WS_TILEDWINDOW: Final = WS_OVERLAPPEDWINDOW
WS_EX_DLGMODALFRAME: Final = 1
WS_EX_NOPARENTNOTIFY: Final = 4
WS_EX_TOPMOST: Final = 8
WS_EX_ACCEPTFILES: Final = 16
WS_EX_TRANSPARENT: Final = 32
WS_EX_MDICHILD: Final = 64
WS_EX_TOOLWINDOW: Final = 128
WS_EX_WINDOWEDGE: Final = 256
WS_EX_CLIENTEDGE: Final = 512
WS_EX_CONTEXTHELP: Final = 1024
WS_EX_RIGHT: Final = 4096
WS_EX_LEFT: Final = 0
WS_EX_RTLREADING: Final = 8192
WS_EX_LTRREADING: Final = 0
WS_EX_LEFTSCROLLBAR: Final = 16384
WS_EX_RIGHTSCROLLBAR: Final = 0
WS_EX_CONTROLPARENT: Final = 65536
WS_EX_STATICEDGE: Final = 131072
WS_EX_APPWINDOW: Final = 262144
WS_EX_OVERLAPPEDWINDOW: Final[int]
WS_EX_PALETTEWINDOW: Final[int]
WS_EX_LAYERED: Final = 0x00080000
WS_EX_NOINHERITLAYOUT: Final = 0x00100000
WS_EX_LAYOUTRTL: Final = 0x00400000
WS_EX_COMPOSITED: Final = 0x02000000
WS_EX_NOACTIVATE: Final = 0x08000000

CS_VREDRAW: Final = 1
CS_HREDRAW: Final = 2

CS_DBLCLKS: Final = 8
CS_OWNDC: Final = 32
CS_CLASSDC: Final = 64
CS_PARENTDC: Final = 128

CS_NOCLOSE: Final = 512
CS_SAVEBITS: Final = 2048
CS_BYTEALIGNCLIENT: Final = 4096
CS_BYTEALIGNWINDOW: Final = 8192
CS_GLOBALCLASS: Final = 16384
CS_IME: Final = 65536
PRF_CHECKVISIBLE: Final = 1
PRF_NONCLIENT: Final = 2
PRF_CLIENT: Final = 4
PRF_ERASEBKGND: Final = 8
PRF_CHILDREN: Final = 16
PRF_OWNED: Final = 32
BDR_RAISEDOUTER: Final = 1
BDR_SUNKENOUTER: Final = 2
BDR_RAISEDINNER: Final = 4
BDR_SUNKENINNER: Final = 8
BDR_OUTER: Final = 3
BDR_INNER: Final = 12

EDGE_RAISED: Final[int]
EDGE_SUNKEN: Final[int]
EDGE_ETCHED: Final[int]
EDGE_BUMP: Final[int]

ISMEX_NOSEND: Final = 0
ISMEX_SEND: Final = 1
ISMEX_NOTIFY: Final = 2
ISMEX_CALLBACK: Final = 4
ISMEX_REPLIED: Final = 8
CW_USEDEFAULT: Final = -**********
FLASHW_STOP: Final = 0
FLASHW_CAPTION: Final = 1
FLASHW_TRAY: Final = 2
FLASHW_ALL: Final[int]
FLASHW_TIMER: Final = 4
FLASHW_TIMERNOFG: Final = 12

DS_ABSALIGN: Final = 1
DS_SYSMODAL: Final = 2
DS_LOCALEDIT: Final = 32
DS_SETFONT: Final = 64
DS_MODALFRAME: Final = 128
DS_NOIDLEMSG: Final = 256
DS_SETFOREGROUND: Final = 512
DS_3DLOOK: Final = 4
DS_FIXEDSYS: Final = 8
DS_NOFAILCREATE: Final = 16
DS_CONTROL: Final = 1024
DS_CENTER: Final = 2048
DS_CENTERMOUSE: Final = 4096
DS_CONTEXTHELP: Final = 8192
DM_GETDEFID: Final[int]
DM_SETDEFID: Final[int]
DM_REPOSITION: Final[int]

DC_HASDEFID: Final = 21323
DLGC_WANTARROWS: Final = 1
DLGC_WANTTAB: Final = 2
DLGC_WANTALLKEYS: Final = 4
DLGC_WANTMESSAGE: Final = 4
DLGC_HASSETSEL: Final = 8
DLGC_DEFPUSHBUTTON: Final = 16
DLGC_UNDEFPUSHBUTTON: Final = 32
DLGC_RADIOBUTTON: Final = 64
DLGC_WANTCHARS: Final = 128
DLGC_STATIC: Final = 256
DLGC_BUTTON: Final = 8192
LB_CTLCODE: Final = 0
LB_OKAY: Final = 0
LB_ERR: Final = -1
LB_ERRSPACE: Final = -2
LBN_ERRSPACE: Final = -2
LBN_SELCHANGE: Final = 1
LBN_DBLCLK: Final = 2
LBN_SELCANCEL: Final = 3
LBN_SETFOCUS: Final = 4
LBN_KILLFOCUS: Final = 5
LB_ADDSTRING: Final = 384
LB_INSERTSTRING: Final = 385
LB_DELETESTRING: Final = 386
LB_SELITEMRANGEEX: Final = 387
LB_RESETCONTENT: Final = 388
LB_SETSEL: Final = 389
LB_SETCURSEL: Final = 390
LB_GETSEL: Final = 391
LB_GETCURSEL: Final = 392
LB_GETTEXT: Final = 393
LB_GETTEXTLEN: Final = 394
LB_GETCOUNT: Final = 395
LB_SELECTSTRING: Final = 396
LB_DIR: Final = 397
LB_GETTOPINDEX: Final = 398
LB_FINDSTRING: Final = 399
LB_GETSELCOUNT: Final = 400
LB_GETSELITEMS: Final = 401
LB_SETTABSTOPS: Final = 402
LB_GETHORIZONTALEXTENT: Final = 403
LB_SETHORIZONTALEXTENT: Final = 404
LB_SETCOLUMNWIDTH: Final = 405
LB_ADDFILE: Final = 406
LB_SETTOPINDEX: Final = 407
LB_GETITEMRECT: Final = 408
LB_GETITEMDATA: Final = 409
LB_SETITEMDATA: Final = 410
LB_SELITEMRANGE: Final = 411
LB_SETANCHORINDEX: Final = 412
LB_GETANCHORINDEX: Final = 413
LB_SETCARETINDEX: Final = 414
LB_GETCARETINDEX: Final = 415
LB_SETITEMHEIGHT: Final = 416
LB_GETITEMHEIGHT: Final = 417
LB_FINDSTRINGEXACT: Final = 418
LB_SETLOCALE: Final = 421
LB_GETLOCALE: Final = 422
LB_SETCOUNT: Final = 423
LB_INITSTORAGE: Final = 424
LB_ITEMFROMPOINT: Final = 425
LB_MSGMAX: Final = 432
LBS_NOTIFY: Final = 1
LBS_SORT: Final = 2
LBS_NOREDRAW: Final = 4
LBS_MULTIPLESEL: Final = 8
LBS_OWNERDRAWFIXED: Final = 16
LBS_OWNERDRAWVARIABLE: Final = 32
LBS_HASSTRINGS: Final = 64
LBS_USETABSTOPS: Final = 128
LBS_NOINTEGRALHEIGHT: Final = 256
LBS_MULTICOLUMN: Final = 512
LBS_WANTKEYBOARDINPUT: Final = 1024
LBS_EXTENDEDSEL: Final = 2048
LBS_DISABLENOSCROLL: Final = 4096
LBS_NODATA: Final = 8192
LBS_NOSEL: Final = 16384
LBS_STANDARD: Final[int]
CB_OKAY: Final = 0
CB_ERR: Final = -1
CB_ERRSPACE: Final = -2
CBN_ERRSPACE: Final = -1
CBN_SELCHANGE: Final = 1
CBN_DBLCLK: Final = 2
CBN_SETFOCUS: Final = 3
CBN_KILLFOCUS: Final = 4
CBN_EDITCHANGE: Final = 5
CBN_EDITUPDATE: Final = 6
CBN_DROPDOWN: Final = 7
CBN_CLOSEUP: Final = 8
CBN_SELENDOK: Final = 9
CBN_SELENDCANCEL: Final = 10
CBS_SIMPLE: Final = 1
CBS_DROPDOWN: Final = 2
CBS_DROPDOWNLIST: Final = 3
CBS_OWNERDRAWFIXED: Final = 16
CBS_OWNERDRAWVARIABLE: Final = 32
CBS_AUTOHSCROLL: Final = 64
CBS_OEMCONVERT: Final = 128
CBS_SORT: Final = 256
CBS_HASSTRINGS: Final = 512
CBS_NOINTEGRALHEIGHT: Final = 1024
CBS_DISABLENOSCROLL: Final = 2048
CBS_UPPERCASE: Final = 8192
CBS_LOWERCASE: Final = 16384
CB_GETEDITSEL: Final = 320
CB_LIMITTEXT: Final = 321
CB_SETEDITSEL: Final = 322
CB_ADDSTRING: Final = 323
CB_DELETESTRING: Final = 324
CB_DIR: Final = 325
CB_GETCOUNT: Final = 326
CB_GETCURSEL: Final = 327
CB_GETLBTEXT: Final = 328
CB_GETLBTEXTLEN: Final = 329
CB_INSERTSTRING: Final = 330
CB_RESETCONTENT: Final = 331
CB_FINDSTRING: Final = 332
CB_SELECTSTRING: Final = 333
CB_SETCURSEL: Final = 334
CB_SHOWDROPDOWN: Final = 335
CB_GETITEMDATA: Final = 336
CB_SETITEMDATA: Final = 337
CB_GETDROPPEDCONTROLRECT: Final = 338
CB_SETITEMHEIGHT: Final = 339
CB_GETITEMHEIGHT: Final = 340
CB_SETEXTENDEDUI: Final = 341
CB_GETEXTENDEDUI: Final = 342
CB_GETDROPPEDSTATE: Final = 343
CB_FINDSTRINGEXACT: Final = 344
CB_SETLOCALE: Final = 345
CB_GETLOCALE: Final = 346
CB_GETTOPINDEX: Final = 347
CB_SETTOPINDEX: Final = 348
CB_GETHORIZONTALEXTENT: Final = 349
CB_SETHORIZONTALEXTENT: Final = 350
CB_GETDROPPEDWIDTH: Final = 351
CB_SETDROPPEDWIDTH: Final = 352
CB_INITSTORAGE: Final = 353
CB_MSGMAX: Final = 354
SBS_HORZ: Final = 0
SBS_VERT: Final = 1
SBS_TOPALIGN: Final = 2
SBS_LEFTALIGN: Final = 2
SBS_BOTTOMALIGN: Final = 4
SBS_RIGHTALIGN: Final = 4
SBS_SIZEBOXTOPLEFTALIGN: Final = 2
SBS_SIZEBOXBOTTOMRIGHTALIGN: Final = 4
SBS_SIZEBOX: Final = 8
SBS_SIZEGRIP: Final = 16
SBM_SETPOS: Final = 224
SBM_GETPOS: Final = 225
SBM_SETRANGE: Final = 226
SBM_SETRANGEREDRAW: Final = 230
SBM_GETRANGE: Final = 227
SBM_ENABLE_ARROWS: Final = 228
SBM_SETSCROLLINFO: Final = 233
SBM_GETSCROLLINFO: Final = 234
SIF_RANGE: Final = 1
SIF_PAGE: Final = 2
SIF_POS: Final = 4
SIF_DISABLENOSCROLL: Final = 8
SIF_TRACKPOS: Final = 16
SIF_ALL: Final[int]
MDIS_ALLCHILDSTYLES: Final = 1
MDITILE_VERTICAL: Final = 0
MDITILE_HORIZONTAL: Final = 1
MDITILE_SKIPDISABLED: Final = 2
MDITILE_ZORDER: Final = 4

IMC_GETCANDIDATEPOS: Final = 7
IMC_SETCANDIDATEPOS: Final = 8
IMC_GETCOMPOSITIONFONT: Final = 9
IMC_SETCOMPOSITIONFONT: Final = 10
IMC_GETCOMPOSITIONWINDOW: Final = 11
IMC_SETCOMPOSITIONWINDOW: Final = 12
IMC_GETSTATUSWINDOWPOS: Final = 15
IMC_SETSTATUSWINDOWPOS: Final = 16
IMC_CLOSESTATUSWINDOW: Final = 33
IMC_OPENSTATUSWINDOW: Final = 34

DELETE: Final = 65536
READ_CONTROL: Final = 131072
WRITE_DAC: Final = 262144
WRITE_OWNER: Final = 524288
SYNCHRONIZE: Final = 1048576
STANDARD_RIGHTS_REQUIRED: Final = 983040
STANDARD_RIGHTS_READ: Final = READ_CONTROL
STANDARD_RIGHTS_WRITE: Final = READ_CONTROL
STANDARD_RIGHTS_EXECUTE: Final = READ_CONTROL
STANDARD_RIGHTS_ALL: Final = 2031616
SPECIFIC_RIGHTS_ALL: Final = 65535
ACCESS_SYSTEM_SECURITY: Final = 16777216
MAXIMUM_ALLOWED: Final = 33554432
GENERIC_READ: Final = -**********
GENERIC_WRITE: Final = **********
GENERIC_EXECUTE: Final = *********
GENERIC_ALL: Final = *********

SERVICE_KERNEL_DRIVER: Final = 1
SERVICE_FILE_SYSTEM_DRIVER: Final = 2
SERVICE_ADAPTER: Final = 4
SERVICE_RECOGNIZER_DRIVER: Final = 8
SERVICE_DRIVER: Final[int]
SERVICE_WIN32_OWN_PROCESS: Final = 16
SERVICE_WIN32_SHARE_PROCESS: Final = 32
SERVICE_WIN32: Final[int]
SERVICE_INTERACTIVE_PROCESS: Final = 256
SERVICE_TYPE_ALL: Final[int]
SERVICE_BOOT_START: Final = 0
SERVICE_SYSTEM_START: Final = 1
SERVICE_AUTO_START: Final = 2
SERVICE_DEMAND_START: Final = 3
SERVICE_DISABLED: Final = 4
SERVICE_ERROR_IGNORE: Final = 0
SERVICE_ERROR_NORMAL: Final = 1
SERVICE_ERROR_SEVERE: Final = 2
SERVICE_ERROR_CRITICAL: Final = 3
TAPE_ERASE_SHORT: Final = 0
TAPE_ERASE_LONG: Final = 1
TAPE_LOAD: Final = 0
TAPE_UNLOAD: Final = 1
TAPE_TENSION: Final = 2
TAPE_LOCK: Final = 3
TAPE_UNLOCK: Final = 4
TAPE_FORMAT: Final = 5
TAPE_SETMARKS: Final = 0
TAPE_FILEMARKS: Final = 1
TAPE_SHORT_FILEMARKS: Final = 2
TAPE_LONG_FILEMARKS: Final = 3
TAPE_ABSOLUTE_POSITION: Final = 0
TAPE_LOGICAL_POSITION: Final = 1
TAPE_PSEUDO_LOGICAL_POSITION: Final = 2
TAPE_REWIND: Final = 0
TAPE_ABSOLUTE_BLOCK: Final = 1
TAPE_LOGICAL_BLOCK: Final = 2
TAPE_PSEUDO_LOGICAL_BLOCK: Final = 3
TAPE_SPACE_END_OF_DATA: Final = 4
TAPE_SPACE_RELATIVE_BLOCKS: Final = 5
TAPE_SPACE_FILEMARKS: Final = 6
TAPE_SPACE_SEQUENTIAL_FMKS: Final = 7
TAPE_SPACE_SETMARKS: Final = 8
TAPE_SPACE_SEQUENTIAL_SMKS: Final = 9
TAPE_DRIVE_FIXED: Final = 1
TAPE_DRIVE_SELECT: Final = 2
TAPE_DRIVE_INITIATOR: Final = 4
TAPE_DRIVE_ERASE_SHORT: Final = 16
TAPE_DRIVE_ERASE_LONG: Final = 32
TAPE_DRIVE_ERASE_BOP_ONLY: Final = 64
TAPE_DRIVE_ERASE_IMMEDIATE: Final = 128
TAPE_DRIVE_TAPE_CAPACITY: Final = 256
TAPE_DRIVE_TAPE_REMAINING: Final = 512
TAPE_DRIVE_FIXED_BLOCK: Final = 1024
TAPE_DRIVE_VARIABLE_BLOCK: Final = 2048
TAPE_DRIVE_WRITE_PROTECT: Final = 4096
TAPE_DRIVE_EOT_WZ_SIZE: Final = 8192
TAPE_DRIVE_ECC: Final = 65536
TAPE_DRIVE_COMPRESSION: Final = 131072
TAPE_DRIVE_PADDING: Final = 262144
TAPE_DRIVE_REPORT_SMKS: Final = 524288
TAPE_DRIVE_GET_ABSOLUTE_BLK: Final = 1048576
TAPE_DRIVE_GET_LOGICAL_BLK: Final = 2097152
TAPE_DRIVE_SET_EOT_WZ_SIZE: Final = 4194304
TAPE_DRIVE_LOAD_UNLOAD: Final = -2147483647
TAPE_DRIVE_TENSION: Final = -2147483646
TAPE_DRIVE_LOCK_UNLOCK: Final = -2147483644
TAPE_DRIVE_REWIND_IMMEDIATE: Final = -2147483640
TAPE_DRIVE_SET_BLOCK_SIZE: Final = -2147483632
TAPE_DRIVE_LOAD_UNLD_IMMED: Final = -2147483616
TAPE_DRIVE_TENSION_IMMED: Final = -2147483584
TAPE_DRIVE_LOCK_UNLK_IMMED: Final = -2147483520
TAPE_DRIVE_SET_ECC: Final = -2147483392
TAPE_DRIVE_SET_COMPRESSION: Final = -2147483136
TAPE_DRIVE_SET_PADDING: Final = -2147482624
TAPE_DRIVE_SET_REPORT_SMKS: Final = -2147481600
TAPE_DRIVE_ABSOLUTE_BLK: Final = -2147479552
TAPE_DRIVE_ABS_BLK_IMMED: Final = -2147475456
TAPE_DRIVE_LOGICAL_BLK: Final = -2147467264
TAPE_DRIVE_LOG_BLK_IMMED: Final = -2147450880
TAPE_DRIVE_END_OF_DATA: Final = -2147418112
TAPE_DRIVE_RELATIVE_BLKS: Final = -2147352576
TAPE_DRIVE_FILEMARKS: Final = -2147221504
TAPE_DRIVE_SEQUENTIAL_FMKS: Final = -2146959360
TAPE_DRIVE_SETMARKS: Final = -2146435072
TAPE_DRIVE_SEQUENTIAL_SMKS: Final = -2145386496
TAPE_DRIVE_REVERSE_POSITION: Final = -2143289344
TAPE_DRIVE_SPACE_IMMEDIATE: Final = -2139095040
TAPE_DRIVE_WRITE_SETMARKS: Final = -2130706432
TAPE_DRIVE_WRITE_FILEMARKS: Final = -2113929216
TAPE_DRIVE_WRITE_SHORT_FMKS: Final = -2080374784
TAPE_DRIVE_WRITE_LONG_FMKS: Final = -2013265920
TAPE_DRIVE_WRITE_MARK_IMMED: Final = -1879048192
TAPE_DRIVE_FORMAT: Final = -1610612736
TAPE_DRIVE_FORMAT_IMMEDIATE: Final = -**********
TAPE_FIXED_PARTITIONS: Final = 0
TAPE_SELECT_PARTITIONS: Final = 1
TAPE_INITIATOR_PARTITIONS: Final = 2

APPLICATION_ERROR_MASK: Final = *********
ERROR_SEVERITY_SUCCESS: Final = 0
ERROR_SEVERITY_INFORMATIONAL: Final = **********
ERROR_SEVERITY_WARNING: Final = -**********
ERROR_SEVERITY_ERROR: Final = -**********
MINCHAR: Final = 128
MAXCHAR: Final = 127
MINSHORT: Final = 32768
MAXSHORT: Final = 32767
MINLONG: Final = -**********
MAXLONG: Final = 2147483647
MAXBYTE: Final = 255
MAXWORD: Final = 65535
MAXDWORD: Final = -1
LANG_NEUTRAL: Final = 0
LANG_BULGARIAN: Final = 2
LANG_CHINESE: Final = 4
LANG_CROATIAN: Final = 26
LANG_CZECH: Final = 5
LANG_DANISH: Final = 6
LANG_DUTCH: Final = 19
LANG_ENGLISH: Final = 9
LANG_FINNISH: Final = 11
LANG_FRENCH: Final = 12
LANG_GERMAN: Final = 7
LANG_GREEK: Final = 8
LANG_HUNGARIAN: Final = 14
LANG_ICELANDIC: Final = 15
LANG_ITALIAN: Final = 16
LANG_JAPANESE: Final = 17
LANG_KOREAN: Final = 18
LANG_NORWEGIAN: Final = 20
LANG_POLISH: Final = 21
LANG_PORTUGUESE: Final = 22
LANG_ROMANIAN: Final = 24
LANG_RUSSIAN: Final = 25
LANG_SLOVAK: Final = 27
LANG_SLOVENIAN: Final = 36
LANG_SPANISH: Final = 10
LANG_SWEDISH: Final = 29
LANG_TURKISH: Final = 31
SUBLANG_NEUTRAL: Final = 0
SUBLANG_DEFAULT: Final = 1
SUBLANG_SYS_DEFAULT: Final = 2
SUBLANG_CHINESE_TRADITIONAL: Final = 1
SUBLANG_CHINESE_SIMPLIFIED: Final = 2
SUBLANG_CHINESE_HONGKONG: Final = 3
SUBLANG_CHINESE_SINGAPORE: Final = 4
SUBLANG_DUTCH: Final = 1
SUBLANG_DUTCH_BELGIAN: Final = 2
SUBLANG_ENGLISH_US: Final = 1
SUBLANG_ENGLISH_UK: Final = 2
SUBLANG_ENGLISH_AUS: Final = 3
SUBLANG_ENGLISH_CAN: Final = 4
SUBLANG_ENGLISH_NZ: Final = 5
SUBLANG_ENGLISH_EIRE: Final = 6
SUBLANG_FRENCH: Final = 1
SUBLANG_FRENCH_BELGIAN: Final = 2
SUBLANG_FRENCH_CANADIAN: Final = 3
SUBLANG_FRENCH_SWISS: Final = 4
SUBLANG_GERMAN: Final = 1
SUBLANG_GERMAN_SWISS: Final = 2
SUBLANG_GERMAN_AUSTRIAN: Final = 3
SUBLANG_ITALIAN: Final = 1
SUBLANG_ITALIAN_SWISS: Final = 2
SUBLANG_NORWEGIAN_BOKMAL: Final = 1
SUBLANG_NORWEGIAN_NYNORSK: Final = 2
SUBLANG_PORTUGUESE: Final = 2
SUBLANG_PORTUGUESE_BRAZILIAN: Final = 1
SUBLANG_SPANISH: Final = 1
SUBLANG_SPANISH_MEXICAN: Final = 2
SUBLANG_SPANISH_MODERN: Final = 3
SORT_DEFAULT: Final = 0
SORT_JAPANESE_XJIS: Final = 0
SORT_JAPANESE_UNICODE: Final = 1
SORT_CHINESE_BIG5: Final = 0
SORT_CHINESE_UNICODE: Final = 1
SORT_KOREAN_KSC: Final = 0
SORT_KOREAN_UNICODE: Final = 1

def PRIMARYLANGID(lgid: int) -> int: ...
def SUBLANGID(lgid: int) -> int: ...

NLS_VALID_LOCALE_MASK: Final = 1048575
CONTEXT_PORTABLE_32BIT: Final = 1048576
CONTEXT_ALPHA: Final = 131072
SIZE_OF_80387_REGISTERS: Final = 80
CONTEXT_CONTROL: Final = 1
CONTEXT_FLOATING_POINT: Final = 2
CONTEXT_INTEGER: Final = 4
CONTEXT_FULL: Final[int]
PROCESS_TERMINATE: Final = 1
PROCESS_CREATE_THREAD: Final = 2
PROCESS_VM_OPERATION: Final = 8
PROCESS_VM_READ: Final = 16
PROCESS_VM_WRITE: Final = 32
PROCESS_DUP_HANDLE: Final = 64
PROCESS_CREATE_PROCESS: Final = 128
PROCESS_SET_QUOTA: Final = 256
PROCESS_SET_INFORMATION: Final = 512
PROCESS_QUERY_INFORMATION: Final = 1024
PROCESS_SUSPEND_RESUME: Final = 2048
PROCESS_QUERY_LIMITED_INFORMATION: Final = 4096
PROCESS_SET_LIMITED_INFORMATION: Final = 8192
PROCESS_ALL_ACCESS: Final[int]
THREAD_TERMINATE: Final = 1
THREAD_SUSPEND_RESUME: Final = 2
THREAD_GET_CONTEXT: Final = 8
THREAD_SET_CONTEXT: Final = 16
THREAD_SET_INFORMATION: Final = 32
THREAD_QUERY_INFORMATION: Final = 64
THREAD_SET_THREAD_TOKEN: Final = 128
THREAD_IMPERSONATE: Final = 256
THREAD_DIRECT_IMPERSONATION: Final = 512
THREAD_SET_LIMITED_INFORMATION: Final = 1024
THREAD_QUERY_LIMITED_INFORMATION: Final = 2048
THREAD_RESUME: Final = 4096
TLS_MINIMUM_AVAILABLE: Final = 64
EVENT_MODIFY_STATE: Final = 2
MUTANT_QUERY_STATE: Final = 1
SEMAPHORE_MODIFY_STATE: Final = 2
TIME_ZONE_ID_UNKNOWN: Final = 0
TIME_ZONE_ID_STANDARD: Final = 1
TIME_ZONE_ID_DAYLIGHT: Final = 2
PROCESSOR_INTEL_386: Final = 386
PROCESSOR_INTEL_486: Final = 486
PROCESSOR_INTEL_PENTIUM: Final = 586
PROCESSOR_INTEL_860: Final = 860
PROCESSOR_MIPS_R2000: Final = 2000
PROCESSOR_MIPS_R3000: Final = 3000
PROCESSOR_MIPS_R4000: Final = 4000
PROCESSOR_ALPHA_21064: Final = 21064
PROCESSOR_PPC_601: Final = 601
PROCESSOR_PPC_603: Final = 603
PROCESSOR_PPC_604: Final = 604
PROCESSOR_PPC_620: Final = 620
SECTION_QUERY: Final = 1
SECTION_MAP_WRITE: Final = 2
SECTION_MAP_READ: Final = 4
SECTION_MAP_EXECUTE: Final = 8
SECTION_EXTEND_SIZE: Final = 16
PAGE_NOACCESS: Final = 1
PAGE_READONLY: Final = 2
PAGE_READWRITE: Final = 4
PAGE_WRITECOPY: Final = 8
PAGE_EXECUTE: Final = 16
PAGE_EXECUTE_READ: Final = 32
PAGE_EXECUTE_READWRITE: Final = 64
PAGE_EXECUTE_WRITECOPY: Final = 128
PAGE_GUARD: Final = 256
PAGE_NOCACHE: Final = 512
MEM_COMMIT: Final = 4096
MEM_RESERVE: Final = 8192
MEM_DECOMMIT: Final = 16384
MEM_RELEASE: Final = 32768
MEM_FREE: Final = 65536
MEM_PRIVATE: Final = 131072
MEM_MAPPED: Final = 262144
MEM_TOP_DOWN: Final = 1048576

SEC_FILE: Final = 8388608
SEC_IMAGE: Final = 16777216
SEC_RESERVE: Final = 67108864
SEC_COMMIT: Final = *********
SEC_NOCACHE: Final = *********
MEM_IMAGE: Final = SEC_IMAGE
FILE_SHARE_READ: Final = 1
FILE_SHARE_WRITE: Final = 2
FILE_SHARE_DELETE: Final = 4
FILE_ATTRIBUTE_READONLY: Final = 1
FILE_ATTRIBUTE_HIDDEN: Final = 2
FILE_ATTRIBUTE_SYSTEM: Final = 4
FILE_ATTRIBUTE_DIRECTORY: Final = 16
FILE_ATTRIBUTE_ARCHIVE: Final = 32
FILE_ATTRIBUTE_DEVICE: Final = 64
FILE_ATTRIBUTE_NORMAL: Final = 128
FILE_ATTRIBUTE_TEMPORARY: Final = 256
FILE_ATTRIBUTE_SPARSE_FILE: Final = 512
FILE_ATTRIBUTE_REPARSE_POINT: Final = 1024
FILE_ATTRIBUTE_COMPRESSED: Final = 2048
FILE_ATTRIBUTE_OFFLINE: Final = 4096
FILE_ATTRIBUTE_NOT_CONTENT_INDEXED: Final = 8192
FILE_ATTRIBUTE_ENCRYPTED: Final = 16384
FILE_ATTRIBUTE_VIRTUAL: Final = 65536

FILE_ATTRIBUTE_ATOMIC_WRITE: Final = 512
FILE_ATTRIBUTE_XACTION_WRITE: Final = 1024

FILE_NOTIFY_CHANGE_FILE_NAME: Final = 1
FILE_NOTIFY_CHANGE_DIR_NAME: Final = 2
FILE_NOTIFY_CHANGE_ATTRIBUTES: Final = 4
FILE_NOTIFY_CHANGE_SIZE: Final = 8
FILE_NOTIFY_CHANGE_LAST_WRITE: Final = 16
FILE_NOTIFY_CHANGE_SECURITY: Final = 256
FILE_CASE_SENSITIVE_SEARCH: Final = 1
FILE_CASE_PRESERVED_NAMES: Final = 2
FILE_FILE_COMPRESSION: Final = 16
FILE_NAMED_STREAMS: Final = 262144
FILE_PERSISTENT_ACLS: Final = 0x00000008
FILE_READ_ONLY_VOLUME: Final = 0x00080000
FILE_SEQUENTIAL_WRITE_ONCE: Final = 0x00100000
FILE_SUPPORTS_ENCRYPTION: Final = 0x00020000
FILE_SUPPORTS_EXTENDED_ATTRIBUTES: Final = 0x00800000
FILE_SUPPORTS_HARD_LINKS: Final = 0x00400000
FILE_SUPPORTS_OBJECT_IDS: Final = 0x00010000
FILE_SUPPORTS_OPEN_BY_FILE_ID: Final = 0x01000000
FILE_SUPPORTS_REPARSE_POINTS: Final = 0x00000080
FILE_SUPPORTS_SPARSE_FILES: Final = 0x00000040
FILE_SUPPORTS_TRANSACTIONS: Final = 0x00200000
FILE_SUPPORTS_USN_JOURNAL: Final = 0x02000000
FILE_UNICODE_ON_DISK: Final = 0x00000004
FILE_VOLUME_QUOTAS: Final = 0x00000020
FILE_VOLUME_IS_COMPRESSED: Final = 32768
IO_COMPLETION_MODIFY_STATE: Final = 2
DUPLICATE_CLOSE_SOURCE: Final = 1
DUPLICATE_SAME_ACCESS: Final = 2
SID_MAX_SUB_AUTHORITIES: Final = 15
SECURITY_NULL_RID: Final = 0
SECURITY_WORLD_RID: Final = 0
SECURITY_LOCAL_RID: Final = 0x00000000
SECURITY_CREATOR_OWNER_RID: Final = 0
SECURITY_CREATOR_GROUP_RID: Final = 1
SECURITY_DIALUP_RID: Final = 1
SECURITY_NETWORK_RID: Final = 2
SECURITY_BATCH_RID: Final = 3
SECURITY_INTERACTIVE_RID: Final = 4
SECURITY_SERVICE_RID: Final = 6
SECURITY_ANONYMOUS_LOGON_RID: Final = 7
SECURITY_LOGON_IDS_RID: Final = 5
SECURITY_LOGON_IDS_RID_COUNT: Final = 3
SECURITY_LOCAL_SYSTEM_RID: Final = 18
SECURITY_NT_NON_UNIQUE: Final = 21
SECURITY_BUILTIN_DOMAIN_RID: Final = 32
DOMAIN_USER_RID_ADMIN: Final = 500
DOMAIN_USER_RID_GUEST: Final = 501
DOMAIN_GROUP_RID_ADMINS: Final = 512
DOMAIN_GROUP_RID_USERS: Final = 513
DOMAIN_GROUP_RID_GUESTS: Final = 514
DOMAIN_ALIAS_RID_ADMINS: Final = 544
DOMAIN_ALIAS_RID_USERS: Final = 545
DOMAIN_ALIAS_RID_GUESTS: Final = 546
DOMAIN_ALIAS_RID_POWER_USERS: Final = 547
DOMAIN_ALIAS_RID_ACCOUNT_OPS: Final = 548
DOMAIN_ALIAS_RID_SYSTEM_OPS: Final = 549
DOMAIN_ALIAS_RID_PRINT_OPS: Final = 550
DOMAIN_ALIAS_RID_BACKUP_OPS: Final = 551
DOMAIN_ALIAS_RID_REPLICATOR: Final = 552
SE_GROUP_MANDATORY: Final = 1
SE_GROUP_ENABLED_BY_DEFAULT: Final = 2
SE_GROUP_ENABLED: Final = 4
SE_GROUP_OWNER: Final = 8
SE_GROUP_LOGON_ID: Final = -**********
ACL_REVISION: Final = 2
ACL_REVISION1: Final = 1
ACL_REVISION2: Final = 2
ACCESS_ALLOWED_ACE_TYPE: Final = 0
ACCESS_DENIED_ACE_TYPE: Final = 1
SYSTEM_AUDIT_ACE_TYPE: Final = 2
SYSTEM_ALARM_ACE_TYPE: Final = 3
OBJECT_INHERIT_ACE: Final = 1
CONTAINER_INHERIT_ACE: Final = 2
NO_PROPAGATE_INHERIT_ACE: Final = 4
INHERIT_ONLY_ACE: Final = 8
VALID_INHERIT_FLAGS: Final = 15
SUCCESSFUL_ACCESS_ACE_FLAG: Final = 64
FAILED_ACCESS_ACE_FLAG: Final = 128
SECURITY_DESCRIPTOR_REVISION: Final = 1
SECURITY_DESCRIPTOR_REVISION1: Final = 1
SECURITY_DESCRIPTOR_MIN_LENGTH: Final = 20
SE_OWNER_DEFAULTED: Final = 1
SE_GROUP_DEFAULTED: Final = 2
SE_DACL_PRESENT: Final = 4
SE_DACL_DEFAULTED: Final = 8
SE_SACL_PRESENT: Final = 16
SE_SACL_DEFAULTED: Final = 32
SE_SELF_RELATIVE: Final = 32768
SE_PRIVILEGE_ENABLED_BY_DEFAULT: Final = 1
SE_PRIVILEGE_ENABLED: Final = 2
SE_PRIVILEGE_USED_FOR_ACCESS: Final = -**********
PRIVILEGE_SET_ALL_NECESSARY: Final = 1
SE_CREATE_TOKEN_NAME: Final = "SeCreateTokenPrivilege"
SE_ASSIGNPRIMARYTOKEN_NAME: Final = "SeAssignPrimaryTokenPrivilege"
SE_LOCK_MEMORY_NAME: Final = "SeLockMemoryPrivilege"
SE_INCREASE_QUOTA_NAME: Final = "SeIncreaseQuotaPrivilege"
SE_UNSOLICITED_INPUT_NAME: Final = "SeUnsolicitedInputPrivilege"
SE_MACHINE_ACCOUNT_NAME: Final = "SeMachineAccountPrivilege"
SE_TCB_NAME: Final = "SeTcbPrivilege"
SE_SECURITY_NAME: Final = "SeSecurityPrivilege"
SE_TAKE_OWNERSHIP_NAME: Final = "SeTakeOwnershipPrivilege"
SE_LOAD_DRIVER_NAME: Final = "SeLoadDriverPrivilege"
SE_SYSTEM_PROFILE_NAME: Final = "SeSystemProfilePrivilege"
SE_SYSTEMTIME_NAME: Final = "SeSystemtimePrivilege"
SE_PROF_SINGLE_PROCESS_NAME: Final = "SeProfileSingleProcessPrivilege"
SE_INC_BASE_PRIORITY_NAME: Final = "SeIncreaseBasePriorityPrivilege"
SE_CREATE_PAGEFILE_NAME: Final = "SeCreatePagefilePrivilege"
SE_CREATE_PERMANENT_NAME: Final = "SeCreatePermanentPrivilege"
SE_BACKUP_NAME: Final = "SeBackupPrivilege"
SE_RESTORE_NAME: Final = "SeRestorePrivilege"
SE_SHUTDOWN_NAME: Final = "SeShutdownPrivilege"
SE_DEBUG_NAME: Final = "SeDebugPrivilege"
SE_AUDIT_NAME: Final = "SeAuditPrivilege"
SE_SYSTEM_ENVIRONMENT_NAME: Final = "SeSystemEnvironmentPrivilege"
SE_CHANGE_NOTIFY_NAME: Final = "SeChangeNotifyPrivilege"
SE_REMOTE_SHUTDOWN_NAME: Final = "SeRemoteShutdownPrivilege"

TOKEN_ASSIGN_PRIMARY: Final = 1
TOKEN_DUPLICATE: Final = 2
TOKEN_IMPERSONATE: Final = 4
TOKEN_QUERY: Final = 8
TOKEN_QUERY_SOURCE: Final = 16
TOKEN_ADJUST_PRIVILEGES: Final = 32
TOKEN_ADJUST_GROUPS: Final = 64
TOKEN_ADJUST_DEFAULT: Final = 128
TOKEN_ADJUST_SESSIONID: Final = 256
TOKEN_ALL_ACCESS: Final[int]
TOKEN_READ: Final[int]
TOKEN_WRITE: Final[int]
TOKEN_EXECUTE: Final = STANDARD_RIGHTS_EXECUTE
TOKEN_SOURCE_LENGTH: Final = 8

KEY_QUERY_VALUE: Final = 1
KEY_SET_VALUE: Final = 2
KEY_CREATE_SUB_KEY: Final = 4
KEY_ENUMERATE_SUB_KEYS: Final = 8
KEY_NOTIFY: Final = 16
KEY_CREATE_LINK: Final = 32
KEY_WOW64_32KEY: Final = 512
KEY_WOW64_64KEY: Final = 256
KEY_WOW64_RES: Final = 768
KEY_READ: Final[int]
KEY_WRITE: Final[int]
KEY_EXECUTE: Final[int]
KEY_ALL_ACCESS: Final[int]
REG_NOTIFY_CHANGE_ATTRIBUTES: Final = 2
REG_NOTIFY_CHANGE_SECURITY: Final = 8
REG_NONE: Final = 0
REG_SZ: Final = 1
REG_EXPAND_SZ: Final = 2

REG_BINARY: Final = 3
REG_DWORD: Final = 4
REG_DWORD_LITTLE_ENDIAN: Final = 4
REG_DWORD_BIG_ENDIAN: Final = 5
REG_LINK: Final = 6
REG_MULTI_SZ: Final = 7
REG_RESOURCE_LIST: Final = 8
REG_FULL_RESOURCE_DESCRIPTOR: Final = 9
REG_RESOURCE_REQUIREMENTS_LIST: Final = 10
REG_QWORD: Final = 11
REG_QWORD_LITTLE_ENDIAN: Final = 11

_NLSCMPERROR: Final = 2147483647
NULL: Final = 0
HEAP_NO_SERIALIZE: Final = 1
HEAP_GROWABLE: Final = 2
HEAP_GENERATE_EXCEPTIONS: Final = 4
HEAP_ZERO_MEMORY: Final = 8
HEAP_REALLOC_IN_PLACE_ONLY: Final = 16
HEAP_TAIL_CHECKING_ENABLED: Final = 32
HEAP_FREE_CHECKING_ENABLED: Final = 64
HEAP_DISABLE_COALESCE_ON_FREE: Final = 128
IS_TEXT_UNICODE_ASCII16: Final = 1
IS_TEXT_UNICODE_REVERSE_ASCII16: Final = 16
IS_TEXT_UNICODE_STATISTICS: Final = 2
IS_TEXT_UNICODE_REVERSE_STATISTICS: Final = 32
IS_TEXT_UNICODE_CONTROLS: Final = 4
IS_TEXT_UNICODE_REVERSE_CONTROLS: Final = 64
IS_TEXT_UNICODE_SIGNATURE: Final = 8
IS_TEXT_UNICODE_REVERSE_SIGNATURE: Final = 128
IS_TEXT_UNICODE_ILLEGAL_CHARS: Final = 256
IS_TEXT_UNICODE_ODD_LENGTH: Final = 512
IS_TEXT_UNICODE_DBCS_LEADBYTE: Final = 1024
IS_TEXT_UNICODE_NULL_BYTES: Final = 4096
IS_TEXT_UNICODE_UNICODE_MASK: Final = 15
IS_TEXT_UNICODE_REVERSE_MASK: Final = 240
IS_TEXT_UNICODE_NOT_UNICODE_MASK: Final = 3840
IS_TEXT_UNICODE_NOT_ASCII_MASK: Final = 61440
COMPRESSION_FORMAT_NONE: Final = 0
COMPRESSION_FORMAT_DEFAULT: Final = 1
COMPRESSION_FORMAT_LZNT1: Final = 2
COMPRESSION_ENGINE_STANDARD: Final = 0
COMPRESSION_ENGINE_MAXIMUM: Final = 256
MESSAGE_RESOURCE_UNICODE: Final = 1
RTL_CRITSECT_TYPE: Final = 0
RTL_RESOURCE_TYPE: Final = 1
DLL_PROCESS_ATTACH: Final = 1
DLL_THREAD_ATTACH: Final = 2
DLL_THREAD_DETACH: Final = 3
DLL_PROCESS_DETACH: Final = 0
EVENTLOG_SEQUENTIAL_READ: Final = 0x0001
EVENTLOG_SEEK_READ: Final = 0x0002
EVENTLOG_FORWARDS_READ: Final = 0x0004
EVENTLOG_BACKWARDS_READ: Final = 0x0008
EVENTLOG_SUCCESS: Final = 0x0000
EVENTLOG_ERROR_TYPE: Final = 1
EVENTLOG_WARNING_TYPE: Final = 2
EVENTLOG_INFORMATION_TYPE: Final = 4
EVENTLOG_AUDIT_SUCCESS: Final = 8
EVENTLOG_AUDIT_FAILURE: Final = 16
EVENTLOG_START_PAIRED_EVENT: Final = 1
EVENTLOG_END_PAIRED_EVENT: Final = 2
EVENTLOG_END_ALL_PAIRED_EVENTS: Final = 4
EVENTLOG_PAIRED_EVENT_ACTIVE: Final = 8
EVENTLOG_PAIRED_EVENT_INACTIVE: Final = 16

OWNER_SECURITY_INFORMATION: Final = 0x00000001
GROUP_SECURITY_INFORMATION: Final = 0x00000002
DACL_SECURITY_INFORMATION: Final = 0x00000004
SACL_SECURITY_INFORMATION: Final = 0x00000008
IMAGE_SIZEOF_FILE_HEADER: Final = 20
IMAGE_FILE_MACHINE_UNKNOWN: Final = 0
IMAGE_NUMBEROF_DIRECTORY_ENTRIES: Final = 16
IMAGE_SIZEOF_ROM_OPTIONAL_HEADER: Final = 56
IMAGE_SIZEOF_STD_OPTIONAL_HEADER: Final = 28
IMAGE_SIZEOF_NT_OPTIONAL_HEADER: Final = 224
IMAGE_NT_OPTIONAL_HDR_MAGIC: Final = 267
IMAGE_ROM_OPTIONAL_HDR_MAGIC: Final = 263
IMAGE_SIZEOF_SHORT_NAME: Final = 8
IMAGE_SIZEOF_SECTION_HEADER: Final = 40
IMAGE_SIZEOF_SYMBOL: Final = 18
IMAGE_SYM_CLASS_NULL: Final = 0
IMAGE_SYM_CLASS_AUTOMATIC: Final = 1
IMAGE_SYM_CLASS_EXTERNAL: Final = 2
IMAGE_SYM_CLASS_STATIC: Final = 3
IMAGE_SYM_CLASS_REGISTER: Final = 4
IMAGE_SYM_CLASS_EXTERNAL_DEF: Final = 5
IMAGE_SYM_CLASS_LABEL: Final = 6
IMAGE_SYM_CLASS_UNDEFINED_LABEL: Final = 7
IMAGE_SYM_CLASS_MEMBER_OF_STRUCT: Final = 8
IMAGE_SYM_CLASS_ARGUMENT: Final = 9
IMAGE_SYM_CLASS_STRUCT_TAG: Final = 10
IMAGE_SYM_CLASS_MEMBER_OF_UNION: Final = 11
IMAGE_SYM_CLASS_UNION_TAG: Final = 12
IMAGE_SYM_CLASS_TYPE_DEFINITION: Final = 13
IMAGE_SYM_CLASS_UNDEFINED_STATIC: Final = 14
IMAGE_SYM_CLASS_ENUM_TAG: Final = 15
IMAGE_SYM_CLASS_MEMBER_OF_ENUM: Final = 16
IMAGE_SYM_CLASS_REGISTER_PARAM: Final = 17
IMAGE_SYM_CLASS_BIT_FIELD: Final = 18
IMAGE_SYM_CLASS_BLOCK: Final = 100
IMAGE_SYM_CLASS_FUNCTION: Final = 101
IMAGE_SYM_CLASS_END_OF_STRUCT: Final = 102
IMAGE_SYM_CLASS_FILE: Final = 103
IMAGE_SYM_CLASS_SECTION: Final = 104
IMAGE_SYM_CLASS_WEAK_EXTERNAL: Final = 105
N_BTMASK: Final = 15
N_TMASK: Final = 48
N_TMASK1: Final = 192
N_TMASK2: Final = 240
N_BTSHFT: Final = 4
N_TSHIFT: Final = 2
IMAGE_SIZEOF_AUX_SYMBOL: Final = 18
IMAGE_COMDAT_SELECT_NODUPLICATES: Final = 1
IMAGE_COMDAT_SELECT_ANY: Final = 2
IMAGE_COMDAT_SELECT_SAME_SIZE: Final = 3
IMAGE_COMDAT_SELECT_EXACT_MATCH: Final = 4
IMAGE_COMDAT_SELECT_ASSOCIATIVE: Final = 5
IMAGE_WEAK_EXTERN_SEARCH_NOLIBRARY: Final = 1
IMAGE_WEAK_EXTERN_SEARCH_LIBRARY: Final = 2
IMAGE_WEAK_EXTERN_SEARCH_ALIAS: Final = 3
IMAGE_SIZEOF_RELOCATION: Final = 10
IMAGE_REL_I386_SECTION: Final = 10
IMAGE_REL_I386_SECREL: Final = 11
IMAGE_REL_MIPS_REFHALF: Final = 1
IMAGE_REL_MIPS_REFWORD: Final = 2
IMAGE_REL_MIPS_JMPADDR: Final = 3
IMAGE_REL_MIPS_REFHI: Final = 4
IMAGE_REL_MIPS_REFLO: Final = 5
IMAGE_REL_MIPS_GPREL: Final = 6
IMAGE_REL_MIPS_LITERAL: Final = 7
IMAGE_REL_MIPS_SECTION: Final = 10
IMAGE_REL_MIPS_SECREL: Final = 11
IMAGE_REL_MIPS_REFWORDNB: Final = 34
IMAGE_REL_MIPS_PAIR: Final = 37
IMAGE_REL_ALPHA_ABSOLUTE: Final = 0
IMAGE_REL_ALPHA_REFLONG: Final = 1
IMAGE_REL_ALPHA_REFQUAD: Final = 2
IMAGE_REL_ALPHA_GPREL32: Final = 3
IMAGE_REL_ALPHA_LITERAL: Final = 4
IMAGE_REL_ALPHA_LITUSE: Final = 5
IMAGE_REL_ALPHA_GPDISP: Final = 6
IMAGE_REL_ALPHA_BRADDR: Final = 7
IMAGE_REL_ALPHA_HINT: Final = 8
IMAGE_REL_ALPHA_INLINE_REFLONG: Final = 9
IMAGE_REL_ALPHA_REFHI: Final = 10
IMAGE_REL_ALPHA_REFLO: Final = 11
IMAGE_REL_ALPHA_PAIR: Final = 12
IMAGE_REL_ALPHA_MATCH: Final = 13
IMAGE_REL_ALPHA_SECTION: Final = 14
IMAGE_REL_ALPHA_SECREL: Final = 15
IMAGE_REL_ALPHA_REFLONGNB: Final = 16
IMAGE_SIZEOF_BASE_RELOCATION: Final = 8
IMAGE_REL_BASED_ABSOLUTE: Final = 0
IMAGE_REL_BASED_HIGH: Final = 1
IMAGE_REL_BASED_LOW: Final = 2
IMAGE_REL_BASED_HIGHLOW: Final = 3
IMAGE_REL_BASED_HIGHADJ: Final = 4
IMAGE_REL_BASED_MIPS_JMPADDR: Final = 5
IMAGE_SIZEOF_LINENUMBER: Final = 6
IMAGE_ARCHIVE_START_SIZE: Final = 8
IMAGE_ARCHIVE_START: Final = "!<arch>\n"
IMAGE_ARCHIVE_END: Final = "`\n"
IMAGE_ARCHIVE_PAD: Final = "\n"
IMAGE_ARCHIVE_LINKER_MEMBER: Final = "/               "
IMAGE_ARCHIVE_LONGNAMES_MEMBER: Final = "//              "
IMAGE_SIZEOF_ARCHIVE_MEMBER_HDR: Final = 60
IMAGE_ORDINAL_FLAG: Final = -**********

def IMAGE_SNAP_BY_ORDINAL(Ordinal: int) -> bool: ...
def IMAGE_ORDINAL(Ordinal: int) -> int: ...

IMAGE_RESOURCE_NAME_IS_STRING: Final = -**********
IMAGE_RESOURCE_DATA_IS_DIRECTORY: Final = -**********
IMAGE_DEBUG_TYPE_UNKNOWN: Final = 0
IMAGE_DEBUG_TYPE_COFF: Final = 1
IMAGE_DEBUG_TYPE_CODEVIEW: Final = 2
IMAGE_DEBUG_TYPE_FPO: Final = 3
IMAGE_DEBUG_TYPE_MISC: Final = 4
IMAGE_DEBUG_TYPE_EXCEPTION: Final = 5
IMAGE_DEBUG_TYPE_FIXUP: Final = 6
IMAGE_DEBUG_TYPE_OMAP_TO_SRC: Final = 7
IMAGE_DEBUG_TYPE_OMAP_FROM_SRC: Final = 8
FRAME_FPO: Final = 0
FRAME_TRAP: Final = 1
FRAME_TSS: Final = 2
SIZEOF_RFPO_DATA: Final = 16
IMAGE_DEBUG_MISC_EXENAME: Final = 1
IMAGE_SEPARATE_DEBUG_SIGNATURE: Final = 18756

NEWFRAME: Final = 1
ABORTDOC: Final = 2
NEXTBAND: Final = 3
SETCOLORTABLE: Final = 4
GETCOLORTABLE: Final = 5
FLUSHOUTPUT: Final = 6
DRAFTMODE: Final = 7
QUERYESCSUPPORT: Final = 8
SETABORTPROC: Final = 9
STARTDOC: Final = 10
ENDDOC: Final = 11
GETPHYSPAGESIZE: Final = 12
GETPRINTINGOFFSET: Final = 13
GETSCALINGFACTOR: Final = 14
MFCOMMENT: Final = 15
GETPENWIDTH: Final = 16
SETCOPYCOUNT: Final = 17
SELECTPAPERSOURCE: Final = 18
DEVICEDATA: Final = 19
PASSTHROUGH: Final = 19
GETTECHNOLGY: Final = 20
GETTECHNOLOGY: Final = 20
SETLINECAP: Final = 21
SETLINEJOIN: Final = 22
SETMITERLIMIT: Final = 23
BANDINFO: Final = 24
DRAWPATTERNRECT: Final = 25
GETVECTORPENSIZE: Final = 26
GETVECTORBRUSHSIZE: Final = 27
ENABLEDUPLEX: Final = 28
GETSETPAPERBINS: Final = 29
GETSETPRINTORIENT: Final = 30
ENUMPAPERBINS: Final = 31
SETDIBSCALING: Final = 32
EPSPRINTING: Final = 33
ENUMPAPERMETRICS: Final = 34
GETSETPAPERMETRICS: Final = 35
POSTSCRIPT_DATA: Final = 37
POSTSCRIPT_IGNORE: Final = 38
MOUSETRAILS: Final = 39
GETDEVICEUNITS: Final = 42
GETEXTENDEDTEXTMETRICS: Final = 256
GETEXTENTTABLE: Final = 257
GETPAIRKERNTABLE: Final = 258
GETTRACKKERNTABLE: Final = 259
EXTTEXTOUT: Final = 512
GETFACENAME: Final = 513
DOWNLOADFACE: Final = 514
ENABLERELATIVEWIDTHS: Final = 768
ENABLEPAIRKERNING: Final = 769
SETKERNTRACK: Final = 770
SETALLJUSTVALUES: Final = 771
SETCHARSET: Final = 772
STRETCHBLT: Final = 2048
GETSETSCREENPARAMS: Final = 3072
BEGIN_PATH: Final = 4096
CLIP_TO_PATH: Final = 4097
END_PATH: Final = 4098
EXT_DEVICE_CAPS: Final = 4099
RESTORE_CTM: Final = 4100
SAVE_CTM: Final = 4101
SET_ARC_DIRECTION: Final = 4102
SET_BACKGROUND_COLOR: Final = 4103
SET_POLY_MODE: Final = 4104
SET_SCREEN_ANGLE: Final = 4105
SET_SPREAD: Final = 4106
TRANSFORM_CTM: Final = 4107
SET_CLIP_BOX: Final = 4108
SET_BOUNDS: Final = 4109
SET_MIRROR_MODE: Final = 4110
OPENCHANNEL: Final = 4110
DOWNLOADHEADER: Final = 4111
CLOSECHANNEL: Final = 4112
POSTSCRIPT_PASSTHROUGH: Final = 4115
ENCAPSULATED_POSTSCRIPT: Final = 4116
SP_NOTREPORTED: Final = 16384
SP_ERROR: Final = -1
SP_APPABORT: Final = -2
SP_USERABORT: Final = -3
SP_OUTOFDISK: Final = -4
SP_OUTOFMEMORY: Final = -5
PR_JOBSTATUS: Final = 0

OBJ_PEN: Final = 1
OBJ_BRUSH: Final = 2
OBJ_DC: Final = 3
OBJ_METADC: Final = 4
OBJ_PAL: Final = 5
OBJ_FONT: Final = 6
OBJ_BITMAP: Final = 7
OBJ_REGION: Final = 8
OBJ_METAFILE: Final = 9
OBJ_MEMDC: Final = 10
OBJ_EXTPEN: Final = 11
OBJ_ENHMETADC: Final = 12
OBJ_ENHMETAFILE: Final = 13
OBJ_COLORSPACE: Final = 14

MWT_IDENTITY: Final = 1
MWT_LEFTMULTIPLY: Final = 2
MWT_RIGHTMULTIPLY: Final = 3
MWT_MIN: Final = MWT_IDENTITY
MWT_MAX: Final = MWT_RIGHTMULTIPLY
BI_RGB: Final = 0
BI_RLE8: Final = 1
BI_RLE4: Final = 2
BI_BITFIELDS: Final = 3
TMPF_FIXED_PITCH: Final = 1
TMPF_VECTOR: Final = 2
TMPF_DEVICE: Final = 8
TMPF_TRUETYPE: Final = 4
NTM_REGULAR: Final = 64
NTM_BOLD: Final = 32
NTM_ITALIC: Final = 1
LF_FACESIZE: Final = 32
LF_FULLFACESIZE: Final = 64
OUT_DEFAULT_PRECIS: Final = 0
OUT_STRING_PRECIS: Final = 1
OUT_CHARACTER_PRECIS: Final = 2
OUT_STROKE_PRECIS: Final = 3
OUT_TT_PRECIS: Final = 4
OUT_DEVICE_PRECIS: Final = 5
OUT_RASTER_PRECIS: Final = 6
OUT_TT_ONLY_PRECIS: Final = 7
OUT_OUTLINE_PRECIS: Final = 8
CLIP_DEFAULT_PRECIS: Final = 0
CLIP_CHARACTER_PRECIS: Final = 1
CLIP_STROKE_PRECIS: Final = 2
CLIP_MASK: Final = 15
CLIP_LH_ANGLES: Final[int]
CLIP_TT_ALWAYS: Final[int]
CLIP_EMBEDDED: Final[int]
DEFAULT_QUALITY: Final = 0
DRAFT_QUALITY: Final = 1
PROOF_QUALITY: Final = 2
NONANTIALIASED_QUALITY: Final = 3
ANTIALIASED_QUALITY: Final = 4
CLEARTYPE_QUALITY: Final = 5
CLEARTYPE_NATURAL_QUALITY: Final = 6
DEFAULT_PITCH: Final = 0
FIXED_PITCH: Final = 1
VARIABLE_PITCH: Final = 2
ANSI_CHARSET: Final = 0
DEFAULT_CHARSET: Final = 1
SYMBOL_CHARSET: Final = 2
SHIFTJIS_CHARSET: Final = 128
HANGEUL_CHARSET: Final = 129
CHINESEBIG5_CHARSET: Final = 136
OEM_CHARSET: Final = 255
JOHAB_CHARSET: Final = 130
HEBREW_CHARSET: Final = 177
ARABIC_CHARSET: Final = 178
GREEK_CHARSET: Final = 161
TURKISH_CHARSET: Final = 162
VIETNAMESE_CHARSET: Final = 163
THAI_CHARSET: Final = 222
EASTEUROPE_CHARSET: Final = 238
RUSSIAN_CHARSET: Final = 204
MAC_CHARSET: Final = 77
BALTIC_CHARSET: Final = 186
FF_DONTCARE: Final[int]
FF_ROMAN: Final[int]
FF_SWISS: Final[int]
FF_MODERN: Final[int]
FF_SCRIPT: Final[int]
FF_DECORATIVE: Final[int]
FW_DONTCARE: Final = 0
FW_THIN: Final = 100
FW_EXTRALIGHT: Final = 200
FW_LIGHT: Final = 300
FW_NORMAL: Final = 400
FW_MEDIUM: Final = 500
FW_SEMIBOLD: Final = 600
FW_BOLD: Final = 700
FW_EXTRABOLD: Final = 800
FW_HEAVY: Final = 900
FW_ULTRALIGHT: Final = FW_EXTRALIGHT
FW_REGULAR: Final = FW_NORMAL
FW_DEMIBOLD: Final = FW_SEMIBOLD
FW_ULTRABOLD: Final = FW_EXTRABOLD
FW_BLACK: Final = FW_HEAVY

BS_SOLID: Final = 0
BS_NULL: Final = 1
BS_HOLLOW: Final = BS_NULL
BS_HATCHED: Final = 2
BS_PATTERN: Final = 3
BS_INDEXED: Final = 4
BS_DIBPATTERN: Final = 5
BS_DIBPATTERNPT: Final = 6
BS_PATTERN8X8: Final = 7
BS_DIBPATTERN8X8: Final = 8
HS_HORIZONTAL: Final = 0
HS_VERTICAL: Final = 1
HS_FDIAGONAL: Final = 2
HS_BDIAGONAL: Final = 3
HS_CROSS: Final = 4
HS_DIAGCROSS: Final = 5
HS_FDIAGONAL1: Final = 6
HS_BDIAGONAL1: Final = 7
HS_SOLID: Final = 8
HS_DENSE1: Final = 9
HS_DENSE2: Final = 10
HS_DENSE3: Final = 11
HS_DENSE4: Final = 12
HS_DENSE5: Final = 13
HS_DENSE6: Final = 14
HS_DENSE7: Final = 15
HS_DENSE8: Final = 16
HS_NOSHADE: Final = 17
HS_HALFTONE: Final = 18
HS_SOLIDCLR: Final = 19
HS_DITHEREDCLR: Final = 20
HS_SOLIDTEXTCLR: Final = 21
HS_DITHEREDTEXTCLR: Final = 22
HS_SOLIDBKCLR: Final = 23
HS_DITHEREDBKCLR: Final = 24
HS_API_MAX: Final = 25
PS_SOLID: Final = 0
PS_DASH: Final = 1
PS_DOT: Final = 2
PS_DASHDOT: Final = 3
PS_DASHDOTDOT: Final = 4
PS_NULL: Final = 5
PS_INSIDEFRAME: Final = 6
PS_USERSTYLE: Final = 7
PS_ALTERNATE: Final = 8
PS_STYLE_MASK: Final = 15
PS_ENDCAP_ROUND: Final = 0
PS_ENDCAP_SQUARE: Final = 256
PS_ENDCAP_FLAT: Final = 512
PS_ENDCAP_MASK: Final = 3840
PS_JOIN_ROUND: Final = 0
PS_JOIN_BEVEL: Final = 4096
PS_JOIN_MITER: Final = 8192
PS_JOIN_MASK: Final = 61440
PS_COSMETIC: Final = 0
PS_GEOMETRIC: Final = 65536
PS_TYPE_MASK: Final = 983040
AD_COUNTERCLOCKWISE: Final = 1
AD_CLOCKWISE: Final = 2
DRIVERVERSION: Final = 0
TECHNOLOGY: Final = 2
HORZSIZE: Final = 4
VERTSIZE: Final = 6
HORZRES: Final = 8
VERTRES: Final = 10
BITSPIXEL: Final = 12
PLANES: Final = 14
NUMBRUSHES: Final = 16
NUMPENS: Final = 18
NUMMARKERS: Final = 20
NUMFONTS: Final = 22
NUMCOLORS: Final = 24
PDEVICESIZE: Final = 26
CURVECAPS: Final = 28
LINECAPS: Final = 30
POLYGONALCAPS: Final = 32
TEXTCAPS: Final = 34
CLIPCAPS: Final = 36
RASTERCAPS: Final = 38
ASPECTX: Final = 40
ASPECTY: Final = 42
ASPECTXY: Final = 44
LOGPIXELSX: Final = 88
LOGPIXELSY: Final = 90
SIZEPALETTE: Final = 104
NUMRESERVED: Final = 106
COLORRES: Final = 108

PHYSICALWIDTH: Final = 110
PHYSICALHEIGHT: Final = 111
PHYSICALOFFSETX: Final = 112
PHYSICALOFFSETY: Final = 113
SCALINGFACTORX: Final = 114
SCALINGFACTORY: Final = 115
VREFRESH: Final = 116
DESKTOPVERTRES: Final = 117
DESKTOPHORZRES: Final = 118
BLTALIGNMENT: Final = 119
SHADEBLENDCAPS: Final = 120
COLORMGMTCAPS: Final = 121

DT_PLOTTER: Final = 0
DT_RASDISPLAY: Final = 1
DT_RASPRINTER: Final = 2
DT_RASCAMERA: Final = 3
DT_CHARSTREAM: Final = 4
DT_METAFILE: Final = 5
DT_DISPFILE: Final = 6
CC_NONE: Final = 0
CC_CIRCLES: Final = 1
CC_PIE: Final = 2
CC_CHORD: Final = 4
CC_ELLIPSES: Final = 8
CC_WIDE: Final = 16
CC_STYLED: Final = 32
CC_WIDESTYLED: Final = 64
CC_INTERIORS: Final = 128
CC_ROUNDRECT: Final = 256
LC_NONE: Final = 0
LC_POLYLINE: Final = 2
LC_MARKER: Final = 4
LC_POLYMARKER: Final = 8
LC_WIDE: Final = 16
LC_STYLED: Final = 32
LC_WIDESTYLED: Final = 64
LC_INTERIORS: Final = 128
PC_NONE: Final = 0
PC_POLYGON: Final = 1
PC_RECTANGLE: Final = 2
PC_WINDPOLYGON: Final = 4
PC_TRAPEZOID: Final = 4
PC_SCANLINE: Final = 8
PC_WIDE: Final = 16
PC_STYLED: Final = 32
PC_WIDESTYLED: Final = 64
PC_INTERIORS: Final = 128
CP_NONE: Final = 0
CP_RECTANGLE: Final = 1
CP_REGION: Final = 2
TC_OP_CHARACTER: Final = 1
TC_OP_STROKE: Final = 2
TC_CP_STROKE: Final = 4
TC_CR_90: Final = 8
TC_CR_ANY: Final = 16
TC_SF_X_YINDEP: Final = 32
TC_SA_DOUBLE: Final = 64
TC_SA_INTEGER: Final = 128
TC_SA_CONTIN: Final = 256
TC_EA_DOUBLE: Final = 512
TC_IA_ABLE: Final = 1024
TC_UA_ABLE: Final = 2048
TC_SO_ABLE: Final = 4096
TC_RA_ABLE: Final = 8192
TC_VA_ABLE: Final = 16384
TC_RESERVED: Final = 32768
TC_SCROLLBLT: Final = 65536
RC_BITBLT: Final = 1
RC_BANDING: Final = 2
RC_SCALING: Final = 4
RC_BITMAP64: Final = 8
RC_GDI20_OUTPUT: Final = 16
RC_GDI20_STATE: Final = 32
RC_SAVEBITMAP: Final = 64
RC_DI_BITMAP: Final = 128
RC_PALETTE: Final = 256
RC_DIBTODEV: Final = 512
RC_BIGFONT: Final = 1024
RC_STRETCHBLT: Final = 2048
RC_FLOODFILL: Final = 4096
RC_STRETCHDIB: Final = 8192
RC_OP_DX_OUTPUT: Final = 16384
RC_DEVBITS: Final = 32768
DIB_RGB_COLORS: Final = 0
DIB_PAL_COLORS: Final = 1
DIB_PAL_INDICES: Final = 2
DIB_PAL_PHYSINDICES: Final = 2
DIB_PAL_LOGINDICES: Final = 4
SYSPAL_ERROR: Final = 0
SYSPAL_STATIC: Final = 1
SYSPAL_NOSTATIC: Final = 2
CBM_CREATEDIB: Final = 2
CBM_INIT: Final = 4
FLOODFILLBORDER: Final = 0
FLOODFILLSURFACE: Final = 1
CCHFORMNAME: Final = 32

DM_SPECVERSION: Final = 800
DM_ORIENTATION: Final = 1
DM_PAPERSIZE: Final = 2
DM_PAPERLENGTH: Final = 4
DM_PAPERWIDTH: Final = 8
DM_SCALE: Final = 16
DM_POSITION: Final = 32
DM_NUP: Final = 64
DM_DISPLAYORIENTATION: Final = 128
DM_COPIES: Final = 256
DM_DEFAULTSOURCE: Final = 512
DM_PRINTQUALITY: Final = 1024
DM_COLOR: Final = 2048
DM_DUPLEX: Final = 4096
DM_YRESOLUTION: Final = 8192
DM_TTOPTION: Final = 16384
DM_COLLATE: Final = 32768
DM_FORMNAME: Final = 65536
DM_LOGPIXELS: Final = 131072
DM_BITSPERPEL: Final = 262144
DM_PELSWIDTH: Final = 524288
DM_PELSHEIGHT: Final = 1048576
DM_DISPLAYFLAGS: Final = 2097152
DM_DISPLAYFREQUENCY: Final = 4194304
DM_ICMMETHOD: Final = 8388608
DM_ICMINTENT: Final = 16777216
DM_MEDIATYPE: Final = 33554432
DM_DITHERTYPE: Final = 67108864
DM_PANNINGWIDTH: Final = *********
DM_PANNINGHEIGHT: Final = *********
DM_DISPLAYFIXEDOUTPUT: Final = *********

DMORIENT_PORTRAIT: Final = 1
DMORIENT_LANDSCAPE: Final = 2

DMDO_DEFAULT: Final = 0
DMDO_90: Final = 1
DMDO_180: Final = 2
DMDO_270: Final = 3

DMDFO_DEFAULT: Final = 0
DMDFO_STRETCH: Final = 1
DMDFO_CENTER: Final = 2

DMPAPER_LETTER: Final = 1
DMPAPER_LETTERSMALL: Final = 2
DMPAPER_TABLOID: Final = 3
DMPAPER_LEDGER: Final = 4
DMPAPER_LEGAL: Final = 5
DMPAPER_STATEMENT: Final = 6
DMPAPER_EXECUTIVE: Final = 7
DMPAPER_A3: Final = 8
DMPAPER_A4: Final = 9
DMPAPER_A4SMALL: Final = 10
DMPAPER_A5: Final = 11
DMPAPER_B4: Final = 12
DMPAPER_B5: Final = 13
DMPAPER_FOLIO: Final = 14
DMPAPER_QUARTO: Final = 15
DMPAPER_10X14: Final = 16
DMPAPER_11X17: Final = 17
DMPAPER_NOTE: Final = 18
DMPAPER_ENV_9: Final = 19
DMPAPER_ENV_10: Final = 20
DMPAPER_ENV_11: Final = 21
DMPAPER_ENV_12: Final = 22
DMPAPER_ENV_14: Final = 23
DMPAPER_CSHEET: Final = 24
DMPAPER_DSHEET: Final = 25
DMPAPER_ESHEET: Final = 26
DMPAPER_ENV_DL: Final = 27
DMPAPER_ENV_C5: Final = 28
DMPAPER_ENV_C3: Final = 29
DMPAPER_ENV_C4: Final = 30
DMPAPER_ENV_C6: Final = 31
DMPAPER_ENV_C65: Final = 32
DMPAPER_ENV_B4: Final = 33
DMPAPER_ENV_B5: Final = 34
DMPAPER_ENV_B6: Final = 35
DMPAPER_ENV_ITALY: Final = 36
DMPAPER_ENV_MONARCH: Final = 37
DMPAPER_ENV_PERSONAL: Final = 38
DMPAPER_FANFOLD_US: Final = 39
DMPAPER_FANFOLD_STD_GERMAN: Final = 40
DMPAPER_FANFOLD_LGL_GERMAN: Final = 41
DMPAPER_ISO_B4: Final = 42
DMPAPER_JAPANESE_POSTCARD: Final = 43
DMPAPER_9X11: Final = 44
DMPAPER_10X11: Final = 45
DMPAPER_15X11: Final = 46
DMPAPER_ENV_INVITE: Final = 47
DMPAPER_RESERVED_48: Final = 48
DMPAPER_RESERVED_49: Final = 49
DMPAPER_LETTER_EXTRA: Final = 50
DMPAPER_LEGAL_EXTRA: Final = 51
DMPAPER_TABLOID_EXTRA: Final = 52
DMPAPER_A4_EXTRA: Final = 53
DMPAPER_LETTER_TRANSVERSE: Final = 54
DMPAPER_A4_TRANSVERSE: Final = 55
DMPAPER_LETTER_EXTRA_TRANSVERSE: Final = 56
DMPAPER_A_PLUS: Final = 57
DMPAPER_B_PLUS: Final = 58
DMPAPER_LETTER_PLUS: Final = 59
DMPAPER_A4_PLUS: Final = 60
DMPAPER_A5_TRANSVERSE: Final = 61
DMPAPER_B5_TRANSVERSE: Final = 62
DMPAPER_A3_EXTRA: Final = 63
DMPAPER_A5_EXTRA: Final = 64
DMPAPER_B5_EXTRA: Final = 65
DMPAPER_A2: Final = 66
DMPAPER_A3_TRANSVERSE: Final = 67
DMPAPER_A3_EXTRA_TRANSVERSE: Final = 68
DMPAPER_DBL_JAPANESE_POSTCARD: Final = 69
DMPAPER_A6: Final = 70
DMPAPER_JENV_KAKU2: Final = 71
DMPAPER_JENV_KAKU3: Final = 72
DMPAPER_JENV_CHOU3: Final = 73
DMPAPER_JENV_CHOU4: Final = 74
DMPAPER_LETTER_ROTATED: Final = 75
DMPAPER_A3_ROTATED: Final = 76
DMPAPER_A4_ROTATED: Final = 77
DMPAPER_A5_ROTATED: Final = 78
DMPAPER_B4_JIS_ROTATED: Final = 79
DMPAPER_B5_JIS_ROTATED: Final = 80
DMPAPER_JAPANESE_POSTCARD_ROTATED: Final = 81
DMPAPER_DBL_JAPANESE_POSTCARD_ROTATED: Final = 82
DMPAPER_A6_ROTATED: Final = 83
DMPAPER_JENV_KAKU2_ROTATED: Final = 84
DMPAPER_JENV_KAKU3_ROTATED: Final = 85
DMPAPER_JENV_CHOU3_ROTATED: Final = 86
DMPAPER_JENV_CHOU4_ROTATED: Final = 87
DMPAPER_B6_JIS: Final = 88
DMPAPER_B6_JIS_ROTATED: Final = 89
DMPAPER_12X11: Final = 90
DMPAPER_JENV_YOU4: Final = 91
DMPAPER_JENV_YOU4_ROTATED: Final = 92
DMPAPER_P16K: Final = 93
DMPAPER_P32K: Final = 94
DMPAPER_P32KBIG: Final = 95
DMPAPER_PENV_1: Final = 96
DMPAPER_PENV_2: Final = 97
DMPAPER_PENV_3: Final = 98
DMPAPER_PENV_4: Final = 99
DMPAPER_PENV_5: Final = 100
DMPAPER_PENV_6: Final = 101
DMPAPER_PENV_7: Final = 102
DMPAPER_PENV_8: Final = 103
DMPAPER_PENV_9: Final = 104
DMPAPER_PENV_10: Final = 105
DMPAPER_P16K_ROTATED: Final = 106
DMPAPER_P32K_ROTATED: Final = 107
DMPAPER_P32KBIG_ROTATED: Final = 108
DMPAPER_PENV_1_ROTATED: Final = 109
DMPAPER_PENV_2_ROTATED: Final = 110
DMPAPER_PENV_3_ROTATED: Final = 111
DMPAPER_PENV_4_ROTATED: Final = 112
DMPAPER_PENV_5_ROTATED: Final = 113
DMPAPER_PENV_6_ROTATED: Final = 114
DMPAPER_PENV_7_ROTATED: Final = 115
DMPAPER_PENV_8_ROTATED: Final = 116
DMPAPER_PENV_9_ROTATED: Final = 117
DMPAPER_PENV_10_ROTATED: Final = 118
DMPAPER_LAST: Final = DMPAPER_PENV_10_ROTATED
DMPAPER_USER: Final = 256

DMBIN_UPPER: Final = 1
DMBIN_ONLYONE: Final = 1
DMBIN_LOWER: Final = 2
DMBIN_MIDDLE: Final = 3
DMBIN_MANUAL: Final = 4
DMBIN_ENVELOPE: Final = 5
DMBIN_ENVMANUAL: Final = 6
DMBIN_AUTO: Final = 7
DMBIN_TRACTOR: Final = 8
DMBIN_SMALLFMT: Final = 9
DMBIN_LARGEFMT: Final = 10
DMBIN_LARGECAPACITY: Final = 11
DMBIN_CASSETTE: Final = 14
DMBIN_FORMSOURCE: Final = 15
DMBIN_LAST: Final = DMBIN_FORMSOURCE
DMBIN_USER: Final = 256

DMRES_DRAFT: Final = -1
DMRES_LOW: Final = -2
DMRES_MEDIUM: Final = -3
DMRES_HIGH: Final = -4

DMCOLOR_MONOCHROME: Final = 1
DMCOLOR_COLOR: Final = 2

DMDUP_SIMPLEX: Final = 1
DMDUP_VERTICAL: Final = 2
DMDUP_HORIZONTAL: Final = 3

DMTT_BITMAP: Final = 1
DMTT_DOWNLOAD: Final = 2
DMTT_SUBDEV: Final = 3
DMTT_DOWNLOAD_OUTLINE: Final = 4

DMCOLLATE_FALSE: Final = 0
DMCOLLATE_TRUE: Final = 1

DM_GRAYSCALE: Final = 1
DM_INTERLACED: Final = 2

DMICMMETHOD_NONE: Final = 1
DMICMMETHOD_SYSTEM: Final = 2
DMICMMETHOD_DRIVER: Final = 3
DMICMMETHOD_DEVICE: Final = 4
DMICMMETHOD_USER: Final = 256

DMICM_SATURATE: Final = 1
DMICM_CONTRAST: Final = 2
DMICM_COLORIMETRIC: Final = 3
DMICM_ABS_COLORIMETRIC: Final = 4
DMICM_USER: Final = 256

DMMEDIA_STANDARD: Final = 1
DMMEDIA_TRANSPARENCY: Final = 2
DMMEDIA_GLOSSY: Final = 3
DMMEDIA_USER: Final = 256

DMDITHER_NONE: Final = 1
DMDITHER_COARSE: Final = 2
DMDITHER_FINE: Final = 3
DMDITHER_LINEART: Final = 4
DMDITHER_ERRORDIFFUSION: Final = 5
DMDITHER_RESERVED6: Final = 6
DMDITHER_RESERVED7: Final = 7
DMDITHER_RESERVED8: Final = 8
DMDITHER_RESERVED9: Final = 9
DMDITHER_GRAYSCALE: Final = 10
DMDITHER_USER: Final = 256

DMNUP_SYSTEM: Final = 1
DMNUP_ONEUP: Final = 2

FEATURESETTING_NUP: Final = 0
FEATURESETTING_OUTPUT: Final = 1
FEATURESETTING_PSLEVEL: Final = 2
FEATURESETTING_CUSTPAPER: Final = 3
FEATURESETTING_MIRROR: Final = 4
FEATURESETTING_NEGATIVE: Final = 5
FEATURESETTING_PROTOCOL: Final = 6
FEATURESETTING_PRIVATE_BEGIN: Final = 0x1000
FEATURESETTING_PRIVATE_END: Final = 0x1FFF

RDH_RECTANGLES: Final = 1
GGO_METRICS: Final = 0
GGO_BITMAP: Final = 1
GGO_NATIVE: Final = 2
TT_POLYGON_TYPE: Final = 24
TT_PRIM_LINE: Final = 1
TT_PRIM_QSPLINE: Final = 2
TT_AVAILABLE: Final = 1
TT_ENABLED: Final = 2
DM_UPDATE: Final = 1
DM_COPY: Final = 2
DM_PROMPT: Final = 4
DM_MODIFY: Final = 8
DM_IN_BUFFER: Final = DM_MODIFY
DM_IN_PROMPT: Final = DM_PROMPT
DM_OUT_BUFFER: Final = DM_COPY
DM_OUT_DEFAULT: Final = DM_UPDATE

DISPLAY_DEVICE_ATTACHED_TO_DESKTOP: Final = 1
DISPLAY_DEVICE_MULTI_DRIVER: Final = 2
DISPLAY_DEVICE_PRIMARY_DEVICE: Final = 4
DISPLAY_DEVICE_MIRRORING_DRIVER: Final = 8
DISPLAY_DEVICE_VGA_COMPATIBLE: Final = 16
DISPLAY_DEVICE_REMOVABLE: Final = 32
DISPLAY_DEVICE_MODESPRUNED: Final = *********
DISPLAY_DEVICE_REMOTE: Final = 67108864
DISPLAY_DEVICE_DISCONNECT: Final = 33554432

DC_FIELDS: Final = 1
DC_PAPERS: Final = 2
DC_PAPERSIZE: Final = 3
DC_MINEXTENT: Final = 4
DC_MAXEXTENT: Final = 5
DC_BINS: Final = 6
DC_DUPLEX: Final = 7
DC_SIZE: Final = 8
DC_EXTRA: Final = 9
DC_VERSION: Final = 10
DC_DRIVER: Final = 11
DC_BINNAMES: Final = 12
DC_ENUMRESOLUTIONS: Final = 13
DC_FILEDEPENDENCIES: Final = 14
DC_TRUETYPE: Final = 15
DC_PAPERNAMES: Final = 16
DC_ORIENTATION: Final = 17
DC_COPIES: Final = 18
DC_BINADJUST: Final = 19
DC_EMF_COMPLIANT: Final = 20
DC_DATATYPE_PRODUCED: Final = 21
DC_COLLATE: Final = 22
DC_MANUFACTURER: Final = 23
DC_MODEL: Final = 24
DC_PERSONALITY: Final = 25
DC_PRINTRATE: Final = 26
DC_PRINTRATEUNIT: Final = 27
DC_PRINTERMEM: Final = 28
DC_MEDIAREADY: Final = 29
DC_STAPLE: Final = 30
DC_PRINTRATEPPM: Final = 31
DC_COLORDEVICE: Final = 32
DC_NUP: Final = 33
DC_MEDIATYPENAMES: Final = 34
DC_MEDIATYPES: Final = 35

PRINTRATEUNIT_PPM: Final = 1
PRINTRATEUNIT_CPS: Final = 2
PRINTRATEUNIT_LPM: Final = 3
PRINTRATEUNIT_IPM: Final = 4

DCTT_BITMAP: Final = 1
DCTT_DOWNLOAD: Final = 2
DCTT_SUBDEV: Final = 4
DCTT_DOWNLOAD_OUTLINE: Final = 8

DCBA_FACEUPNONE: Final = 0
DCBA_FACEUPCENTER: Final = 1
DCBA_FACEUPLEFT: Final = 2
DCBA_FACEUPRIGHT: Final = 3
DCBA_FACEDOWNNONE: Final = 256
DCBA_FACEDOWNCENTER: Final = 257
DCBA_FACEDOWNLEFT: Final = 258
DCBA_FACEDOWNRIGHT: Final = 259

CA_NEGATIVE: Final = 1
CA_LOG_FILTER: Final = 2
ILLUMINANT_DEVICE_DEFAULT: Final = 0
ILLUMINANT_A: Final = 1
ILLUMINANT_B: Final = 2
ILLUMINANT_C: Final = 3
ILLUMINANT_D50: Final = 4
ILLUMINANT_D55: Final = 5
ILLUMINANT_D65: Final = 6
ILLUMINANT_D75: Final = 7
ILLUMINANT_F2: Final = 8
ILLUMINANT_MAX_INDEX: Final = ILLUMINANT_F2
ILLUMINANT_TUNGSTEN: Final = ILLUMINANT_A
ILLUMINANT_DAYLIGHT: Final = ILLUMINANT_C
ILLUMINANT_FLUORESCENT: Final = ILLUMINANT_F2
ILLUMINANT_NTSC: Final = ILLUMINANT_C

FONTMAPPER_MAX: Final = 10
ENHMETA_SIGNATURE: Final = 1179469088
ENHMETA_STOCK_OBJECT: Final = -**********
EMR_HEADER: Final = 1
EMR_POLYBEZIER: Final = 2
EMR_POLYGON: Final = 3
EMR_POLYLINE: Final = 4
EMR_POLYBEZIERTO: Final = 5
EMR_POLYLINETO: Final = 6
EMR_POLYPOLYLINE: Final = 7
EMR_POLYPOLYGON: Final = 8
EMR_SETWINDOWEXTEX: Final = 9
EMR_SETWINDOWORGEX: Final = 10
EMR_SETVIEWPORTEXTEX: Final = 11
EMR_SETVIEWPORTORGEX: Final = 12
EMR_SETBRUSHORGEX: Final = 13
EMR_EOF: Final = 14
EMR_SETPIXELV: Final = 15
EMR_SETMAPPERFLAGS: Final = 16
EMR_SETMAPMODE: Final = 17
EMR_SETBKMODE: Final = 18
EMR_SETPOLYFILLMODE: Final = 19
EMR_SETROP2: Final = 20
EMR_SETSTRETCHBLTMODE: Final = 21
EMR_SETTEXTALIGN: Final = 22
EMR_SETCOLORADJUSTMENT: Final = 23
EMR_SETTEXTCOLOR: Final = 24
EMR_SETBKCOLOR: Final = 25
EMR_OFFSETCLIPRGN: Final = 26
EMR_MOVETOEX: Final = 27
EMR_SETMETARGN: Final = 28
EMR_EXCLUDECLIPRECT: Final = 29
EMR_INTERSECTCLIPRECT: Final = 30
EMR_SCALEVIEWPORTEXTEX: Final = 31
EMR_SCALEWINDOWEXTEX: Final = 32
EMR_SAVEDC: Final = 33
EMR_RESTOREDC: Final = 34
EMR_SETWORLDTRANSFORM: Final = 35
EMR_MODIFYWORLDTRANSFORM: Final = 36
EMR_SELECTOBJECT: Final = 37
EMR_CREATEPEN: Final = 38
EMR_CREATEBRUSHINDIRECT: Final = 39
EMR_DELETEOBJECT: Final = 40
EMR_ANGLEARC: Final = 41
EMR_ELLIPSE: Final = 42
EMR_RECTANGLE: Final = 43
EMR_ROUNDRECT: Final = 44
EMR_ARC: Final = 45
EMR_CHORD: Final = 46
EMR_PIE: Final = 47
EMR_SELECTPALETTE: Final = 48
EMR_CREATEPALETTE: Final = 49
EMR_SETPALETTEENTRIES: Final = 50
EMR_RESIZEPALETTE: Final = 51
EMR_REALIZEPALETTE: Final = 52
EMR_EXTFLOODFILL: Final = 53
EMR_LINETO: Final = 54
EMR_ARCTO: Final = 55
EMR_POLYDRAW: Final = 56
EMR_SETARCDIRECTION: Final = 57
EMR_SETMITERLIMIT: Final = 58
EMR_BEGINPATH: Final = 59
EMR_ENDPATH: Final = 60
EMR_CLOSEFIGURE: Final = 61
EMR_FILLPATH: Final = 62
EMR_STROKEANDFILLPATH: Final = 63
EMR_STROKEPATH: Final = 64
EMR_FLATTENPATH: Final = 65
EMR_WIDENPATH: Final = 66
EMR_SELECTCLIPPATH: Final = 67
EMR_ABORTPATH: Final = 68
EMR_GDICOMMENT: Final = 70
EMR_FILLRGN: Final = 71
EMR_FRAMERGN: Final = 72
EMR_INVERTRGN: Final = 73
EMR_PAINTRGN: Final = 74
EMR_EXTSELECTCLIPRGN: Final = 75
EMR_BITBLT: Final = 76
EMR_STRETCHBLT: Final = 77
EMR_MASKBLT: Final = 78
EMR_PLGBLT: Final = 79
EMR_SETDIBITSTODEVICE: Final = 80
EMR_STRETCHDIBITS: Final = 81
EMR_EXTCREATEFONTINDIRECTW: Final = 82
EMR_EXTTEXTOUTA: Final = 83
EMR_EXTTEXTOUTW: Final = 84
EMR_POLYBEZIER16: Final = 85
EMR_POLYGON16: Final = 86
EMR_POLYLINE16: Final = 87
EMR_POLYBEZIERTO16: Final = 88
EMR_POLYLINETO16: Final = 89
EMR_POLYPOLYLINE16: Final = 90
EMR_POLYPOLYGON16: Final = 91
EMR_POLYDRAW16: Final = 92
EMR_CREATEMONOBRUSH: Final = 93
EMR_CREATEDIBPATTERNBRUSHPT: Final = 94
EMR_EXTCREATEPEN: Final = 95
EMR_POLYTEXTOUTA: Final = 96
EMR_POLYTEXTOUTW: Final = 97
EMR_MIN: Final = 1
EMR_MAX: Final = 97

PANOSE_COUNT: Final = 10
PAN_FAMILYTYPE_INDEX: Final = 0
PAN_SERIFSTYLE_INDEX: Final = 1
PAN_WEIGHT_INDEX: Final = 2
PAN_PROPORTION_INDEX: Final = 3
PAN_CONTRAST_INDEX: Final = 4
PAN_STROKEVARIATION_INDEX: Final = 5
PAN_ARMSTYLE_INDEX: Final = 6
PAN_LETTERFORM_INDEX: Final = 7
PAN_MIDLINE_INDEX: Final = 8
PAN_XHEIGHT_INDEX: Final = 9
PAN_CULTURE_LATIN: Final = 0
PAN_ANY: Final = 0
PAN_NO_FIT: Final = 1
PAN_FAMILY_TEXT_DISPLAY: Final = 2
PAN_FAMILY_SCRIPT: Final = 3
PAN_FAMILY_DECORATIVE: Final = 4
PAN_FAMILY_PICTORIAL: Final = 5
PAN_SERIF_COVE: Final = 2
PAN_SERIF_OBTUSE_COVE: Final = 3
PAN_SERIF_SQUARE_COVE: Final = 4
PAN_SERIF_OBTUSE_SQUARE_COVE: Final = 5
PAN_SERIF_SQUARE: Final = 6
PAN_SERIF_THIN: Final = 7
PAN_SERIF_BONE: Final = 8
PAN_SERIF_EXAGGERATED: Final = 9
PAN_SERIF_TRIANGLE: Final = 10
PAN_SERIF_NORMAL_SANS: Final = 11
PAN_SERIF_OBTUSE_SANS: Final = 12
PAN_SERIF_PERP_SANS: Final = 13
PAN_SERIF_FLARED: Final = 14
PAN_SERIF_ROUNDED: Final = 15
PAN_WEIGHT_VERY_LIGHT: Final = 2
PAN_WEIGHT_LIGHT: Final = 3
PAN_WEIGHT_THIN: Final = 4
PAN_WEIGHT_BOOK: Final = 5
PAN_WEIGHT_MEDIUM: Final = 6
PAN_WEIGHT_DEMI: Final = 7
PAN_WEIGHT_BOLD: Final = 8
PAN_WEIGHT_HEAVY: Final = 9
PAN_WEIGHT_BLACK: Final = 10
PAN_WEIGHT_NORD: Final = 11
PAN_PROP_OLD_STYLE: Final = 2
PAN_PROP_MODERN: Final = 3
PAN_PROP_EVEN_WIDTH: Final = 4
PAN_PROP_EXPANDED: Final = 5
PAN_PROP_CONDENSED: Final = 6
PAN_PROP_VERY_EXPANDED: Final = 7
PAN_PROP_VERY_CONDENSED: Final = 8
PAN_PROP_MONOSPACED: Final = 9
PAN_CONTRAST_NONE: Final = 2
PAN_CONTRAST_VERY_LOW: Final = 3
PAN_CONTRAST_LOW: Final = 4
PAN_CONTRAST_MEDIUM_LOW: Final = 5
PAN_CONTRAST_MEDIUM: Final = 6
PAN_CONTRAST_MEDIUM_HIGH: Final = 7
PAN_CONTRAST_HIGH: Final = 8
PAN_CONTRAST_VERY_HIGH: Final = 9
PAN_STROKE_GRADUAL_DIAG: Final = 2
PAN_STROKE_GRADUAL_TRAN: Final = 3
PAN_STROKE_GRADUAL_VERT: Final = 4
PAN_STROKE_GRADUAL_HORZ: Final = 5
PAN_STROKE_RAPID_VERT: Final = 6
PAN_STROKE_RAPID_HORZ: Final = 7
PAN_STROKE_INSTANT_VERT: Final = 8
PAN_STRAIGHT_ARMS_HORZ: Final = 2
PAN_STRAIGHT_ARMS_WEDGE: Final = 3
PAN_STRAIGHT_ARMS_VERT: Final = 4
PAN_STRAIGHT_ARMS_SINGLE_SERIF: Final = 5
PAN_STRAIGHT_ARMS_DOUBLE_SERIF: Final = 6
PAN_BENT_ARMS_HORZ: Final = 7
PAN_BENT_ARMS_WEDGE: Final = 8
PAN_BENT_ARMS_VERT: Final = 9
PAN_BENT_ARMS_SINGLE_SERIF: Final = 10
PAN_BENT_ARMS_DOUBLE_SERIF: Final = 11
PAN_LETT_NORMAL_CONTACT: Final = 2
PAN_LETT_NORMAL_WEIGHTED: Final = 3
PAN_LETT_NORMAL_BOXED: Final = 4
PAN_LETT_NORMAL_FLATTENED: Final = 5
PAN_LETT_NORMAL_ROUNDED: Final = 6
PAN_LETT_NORMAL_OFF_CENTER: Final = 7
PAN_LETT_NORMAL_SQUARE: Final = 8
PAN_LETT_OBLIQUE_CONTACT: Final = 9
PAN_LETT_OBLIQUE_WEIGHTED: Final = 10
PAN_LETT_OBLIQUE_BOXED: Final = 11
PAN_LETT_OBLIQUE_FLATTENED: Final = 12
PAN_LETT_OBLIQUE_ROUNDED: Final = 13
PAN_LETT_OBLIQUE_OFF_CENTER: Final = 14
PAN_LETT_OBLIQUE_SQUARE: Final = 15
PAN_MIDLINE_STANDARD_TRIMMED: Final = 2
PAN_MIDLINE_STANDARD_POINTED: Final = 3
PAN_MIDLINE_STANDARD_SERIFED: Final = 4
PAN_MIDLINE_HIGH_TRIMMED: Final = 5
PAN_MIDLINE_HIGH_POINTED: Final = 6
PAN_MIDLINE_HIGH_SERIFED: Final = 7
PAN_MIDLINE_CONSTANT_TRIMMED: Final = 8
PAN_MIDLINE_CONSTANT_POINTED: Final = 9
PAN_MIDLINE_CONSTANT_SERIFED: Final = 10
PAN_MIDLINE_LOW_TRIMMED: Final = 11
PAN_MIDLINE_LOW_POINTED: Final = 12
PAN_MIDLINE_LOW_SERIFED: Final = 13
PAN_XHEIGHT_CONSTANT_SMALL: Final = 2
PAN_XHEIGHT_CONSTANT_STD: Final = 3
PAN_XHEIGHT_CONSTANT_LARGE: Final = 4
PAN_XHEIGHT_DUCKING_SMALL: Final = 5
PAN_XHEIGHT_DUCKING_STD: Final = 6
PAN_XHEIGHT_DUCKING_LARGE: Final = 7
ELF_VENDOR_SIZE: Final = 4
ELF_VERSION: Final = 0
ELF_CULTURE_LATIN: Final = 0
RASTER_FONTTYPE: Final = 1
DEVICE_FONTTYPE: Final = 2
TRUETYPE_FONTTYPE: Final = 4

def PALETTEINDEX(i: int) -> int: ...

PC_RESERVED: Final = 1
PC_EXPLICIT: Final = 2
PC_NOCOLLAPSE: Final = 4

def GetRValue(rgb: int) -> int: ...
def GetGValue(rgb: int) -> int: ...
def GetBValue(rgb: int) -> int: ...

TRANSPARENT: Final = 1
OPAQUE: Final = 2
BKMODE_LAST: Final = 2
GM_COMPATIBLE: Final = 1
GM_ADVANCED: Final = 2
GM_LAST: Final = 2
PT_CLOSEFIGURE: Final = 1
PT_LINETO: Final = 2
PT_BEZIERTO: Final = 4
PT_MOVETO: Final = 6
MM_TEXT: Final = 1
MM_LOMETRIC: Final = 2
MM_HIMETRIC: Final = 3
MM_LOENGLISH: Final = 4
MM_HIENGLISH: Final = 5
MM_TWIPS: Final = 6
MM_ISOTROPIC: Final = 7
MM_ANISOTROPIC: Final = 8
MM_MIN: Final = MM_TEXT
MM_MAX: Final = MM_ANISOTROPIC
MM_MAX_FIXEDSCALE: Final = MM_TWIPS
ABSOLUTE: Final = 1
RELATIVE: Final = 2
WHITE_BRUSH: Final = 0
LTGRAY_BRUSH: Final = 1
GRAY_BRUSH: Final = 2
DKGRAY_BRUSH: Final = 3
BLACK_BRUSH: Final = 4
NULL_BRUSH: Final = 5
HOLLOW_BRUSH: Final = NULL_BRUSH
WHITE_PEN: Final = 6
BLACK_PEN: Final = 7
NULL_PEN: Final = 8
OEM_FIXED_FONT: Final = 10
ANSI_FIXED_FONT: Final = 11
ANSI_VAR_FONT: Final = 12
SYSTEM_FONT: Final = 13
DEVICE_DEFAULT_FONT: Final = 14
DEFAULT_PALETTE: Final = 15
SYSTEM_FIXED_FONT: Final = 16
STOCK_LAST: Final = 16
CLR_INVALID: Final = -1

DC_BRUSH: Final = 18
DC_PEN: Final = 19

STATUS_WAIT_0: Final = 0
STATUS_ABANDONED_WAIT_0: Final = 128
STATUS_USER_APC: Final = 192
STATUS_TIMEOUT: Final = 258
STATUS_PENDING: Final = 259
STATUS_SEGMENT_NOTIFICATION: Final = 1073741829
STATUS_GUARD_PAGE_VIOLATION: Final = -2147483647
STATUS_DATATYPE_MISALIGNMENT: Final = -2147483646
STATUS_BREAKPOINT: Final = -2147483645
STATUS_SINGLE_STEP: Final = -2147483644
STATUS_ACCESS_VIOLATION: Final = -1073741819
STATUS_IN_PAGE_ERROR: Final = -1073741818
STATUS_INVALID_HANDLE: Final = -1073741816
STATUS_NO_MEMORY: Final = -1073741801
STATUS_ILLEGAL_INSTRUCTION: Final = -1073741795
STATUS_NONCONTINUABLE_EXCEPTION: Final = -1073741787
STATUS_INVALID_DISPOSITION: Final = -1073741786
STATUS_ARRAY_BOUNDS_EXCEEDED: Final = -1073741684
STATUS_FLOAT_DENORMAL_OPERAND: Final = -1073741683
STATUS_FLOAT_DIVIDE_BY_ZERO: Final = -1073741682
STATUS_FLOAT_INEXACT_RESULT: Final = -1073741681
STATUS_FLOAT_INVALID_OPERATION: Final = -1073741680
STATUS_FLOAT_OVERFLOW: Final = -1073741679
STATUS_FLOAT_STACK_CHECK: Final = -1073741678
STATUS_FLOAT_UNDERFLOW: Final = -1073741677
STATUS_INTEGER_DIVIDE_BY_ZERO: Final = -1073741676
STATUS_INTEGER_OVERFLOW: Final = -1073741675
STATUS_PRIVILEGED_INSTRUCTION: Final = -1073741674
STATUS_STACK_OVERFLOW: Final = -1073741571
STATUS_CONTROL_C_EXIT: Final = -1073741510

WAIT_FAILED: Final = -1
WAIT_OBJECT_0: Final[int]

WAIT_ABANDONED: Final[int]
WAIT_ABANDONED_0: Final[int]

WAIT_TIMEOUT: Final = STATUS_TIMEOUT
WAIT_IO_COMPLETION: Final = STATUS_USER_APC
STILL_ACTIVE: Final = STATUS_PENDING
EXCEPTION_ACCESS_VIOLATION: Final = STATUS_ACCESS_VIOLATION
EXCEPTION_DATATYPE_MISALIGNMENT: Final = STATUS_DATATYPE_MISALIGNMENT
EXCEPTION_BREAKPOINT: Final = STATUS_BREAKPOINT
EXCEPTION_SINGLE_STEP: Final = STATUS_SINGLE_STEP
EXCEPTION_ARRAY_BOUNDS_EXCEEDED: Final = STATUS_ARRAY_BOUNDS_EXCEEDED
EXCEPTION_FLT_DENORMAL_OPERAND: Final = STATUS_FLOAT_DENORMAL_OPERAND
EXCEPTION_FLT_DIVIDE_BY_ZERO: Final = STATUS_FLOAT_DIVIDE_BY_ZERO
EXCEPTION_FLT_INEXACT_RESULT: Final = STATUS_FLOAT_INEXACT_RESULT
EXCEPTION_FLT_INVALID_OPERATION: Final = STATUS_FLOAT_INVALID_OPERATION
EXCEPTION_FLT_OVERFLOW: Final = STATUS_FLOAT_OVERFLOW
EXCEPTION_FLT_STACK_CHECK: Final = STATUS_FLOAT_STACK_CHECK
EXCEPTION_FLT_UNDERFLOW: Final = STATUS_FLOAT_UNDERFLOW
EXCEPTION_INT_DIVIDE_BY_ZERO: Final = STATUS_INTEGER_DIVIDE_BY_ZERO
EXCEPTION_INT_OVERFLOW: Final = STATUS_INTEGER_OVERFLOW
EXCEPTION_PRIV_INSTRUCTION: Final = STATUS_PRIVILEGED_INSTRUCTION
EXCEPTION_IN_PAGE_ERROR: Final = STATUS_IN_PAGE_ERROR
EXCEPTION_ILLEGAL_INSTRUCTION: Final = STATUS_ILLEGAL_INSTRUCTION
EXCEPTION_NONCONTINUABLE_EXCEPTION: Final = STATUS_NONCONTINUABLE_EXCEPTION
EXCEPTION_STACK_OVERFLOW: Final = STATUS_STACK_OVERFLOW
EXCEPTION_INVALID_DISPOSITION: Final = STATUS_INVALID_DISPOSITION
EXCEPTION_GUARD_PAGE: Final = STATUS_GUARD_PAGE_VIOLATION
EXCEPTION_INVALID_HANDLE: Final = STATUS_INVALID_HANDLE
CONTROL_C_EXIT: Final = STATUS_CONTROL_C_EXIT

SPI_GETBEEP: Final = 1
SPI_SETBEEP: Final = 2
SPI_GETMOUSE: Final = 3
SPI_SETMOUSE: Final = 4
SPI_GETBORDER: Final = 5
SPI_SETBORDER: Final = 6
SPI_GETKEYBOARDSPEED: Final = 10
SPI_SETKEYBOARDSPEED: Final = 11
SPI_LANGDRIVER: Final = 12
SPI_ICONHORIZONTALSPACING: Final = 13
SPI_GETSCREENSAVETIMEOUT: Final = 14
SPI_SETSCREENSAVETIMEOUT: Final = 15
SPI_GETSCREENSAVEACTIVE: Final = 16
SPI_SETSCREENSAVEACTIVE: Final = 17
SPI_GETGRIDGRANULARITY: Final = 18
SPI_SETGRIDGRANULARITY: Final = 19
SPI_SETDESKWALLPAPER: Final = 20
SPI_SETDESKPATTERN: Final = 21
SPI_GETKEYBOARDDELAY: Final = 22
SPI_SETKEYBOARDDELAY: Final = 23
SPI_ICONVERTICALSPACING: Final = 24
SPI_GETICONTITLEWRAP: Final = 25
SPI_SETICONTITLEWRAP: Final = 26
SPI_GETMENUDROPALIGNMENT: Final = 27
SPI_SETMENUDROPALIGNMENT: Final = 28
SPI_SETDOUBLECLKWIDTH: Final = 29
SPI_SETDOUBLECLKHEIGHT: Final = 30
SPI_GETICONTITLELOGFONT: Final = 31
SPI_SETDOUBLECLICKTIME: Final = 32
SPI_SETMOUSEBUTTONSWAP: Final = 33
SPI_SETICONTITLELOGFONT: Final = 34
SPI_GETFASTTASKSWITCH: Final = 35
SPI_SETFASTTASKSWITCH: Final = 36
SPI_SETDRAGFULLWINDOWS: Final = 37
SPI_GETDRAGFULLWINDOWS: Final = 38
SPI_GETNONCLIENTMETRICS: Final = 41
SPI_SETNONCLIENTMETRICS: Final = 42
SPI_GETMINIMIZEDMETRICS: Final = 43
SPI_SETMINIMIZEDMETRICS: Final = 44
SPI_GETICONMETRICS: Final = 45
SPI_SETICONMETRICS: Final = 46
SPI_SETWORKAREA: Final = 47
SPI_GETWORKAREA: Final = 48
SPI_SETPENWINDOWS: Final = 49
SPI_GETFILTERKEYS: Final = 50
SPI_SETFILTERKEYS: Final = 51
SPI_GETTOGGLEKEYS: Final = 52
SPI_SETTOGGLEKEYS: Final = 53
SPI_GETMOUSEKEYS: Final = 54
SPI_SETMOUSEKEYS: Final = 55
SPI_GETSHOWSOUNDS: Final = 56
SPI_SETSHOWSOUNDS: Final = 57
SPI_GETSTICKYKEYS: Final = 58
SPI_SETSTICKYKEYS: Final = 59
SPI_GETACCESSTIMEOUT: Final = 60
SPI_SETACCESSTIMEOUT: Final = 61
SPI_GETSERIALKEYS: Final = 62
SPI_SETSERIALKEYS: Final = 63
SPI_GETSOUNDSENTRY: Final = 64
SPI_SETSOUNDSENTRY: Final = 65
SPI_GETHIGHCONTRAST: Final = 66
SPI_SETHIGHCONTRAST: Final = 67
SPI_GETKEYBOARDPREF: Final = 68
SPI_SETKEYBOARDPREF: Final = 69
SPI_GETSCREENREADER: Final = 70
SPI_SETSCREENREADER: Final = 71
SPI_GETANIMATION: Final = 72
SPI_SETANIMATION: Final = 73
SPI_GETFONTSMOOTHING: Final = 74
SPI_SETFONTSMOOTHING: Final = 75
SPI_SETDRAGWIDTH: Final = 76
SPI_SETDRAGHEIGHT: Final = 77
SPI_SETHANDHELD: Final = 78
SPI_GETLOWPOWERTIMEOUT: Final = 79
SPI_GETPOWEROFFTIMEOUT: Final = 80
SPI_SETLOWPOWERTIMEOUT: Final = 81
SPI_SETPOWEROFFTIMEOUT: Final = 82
SPI_GETLOWPOWERACTIVE: Final = 83
SPI_GETPOWEROFFACTIVE: Final = 84
SPI_SETLOWPOWERACTIVE: Final = 85
SPI_SETPOWEROFFACTIVE: Final = 86
SPI_SETCURSORS: Final = 87
SPI_SETICONS: Final = 88
SPI_GETDEFAULTINPUTLANG: Final = 89
SPI_SETDEFAULTINPUTLANG: Final = 90
SPI_SETLANGTOGGLE: Final = 91
SPI_GETWINDOWSEXTENSION: Final = 92
SPI_SETMOUSETRAILS: Final = 93
SPI_GETMOUSETRAILS: Final = 94
SPI_GETSNAPTODEFBUTTON: Final = 95
SPI_SETSNAPTODEFBUTTON: Final = 96
SPI_SETSCREENSAVERRUNNING: Final = 97
SPI_SCREENSAVERRUNNING: Final = SPI_SETSCREENSAVERRUNNING
SPI_GETMOUSEHOVERWIDTH: Final = 98
SPI_SETMOUSEHOVERWIDTH: Final = 99
SPI_GETMOUSEHOVERHEIGHT: Final = 100
SPI_SETMOUSEHOVERHEIGHT: Final = 101
SPI_GETMOUSEHOVERTIME: Final = 102
SPI_SETMOUSEHOVERTIME: Final = 103
SPI_GETWHEELSCROLLLINES: Final = 104
SPI_SETWHEELSCROLLLINES: Final = 105
SPI_GETMENUSHOWDELAY: Final = 106
SPI_SETMENUSHOWDELAY: Final = 107

SPI_GETSHOWIMEUI: Final = 110
SPI_SETSHOWIMEUI: Final = 111
SPI_GETMOUSESPEED: Final = 112
SPI_SETMOUSESPEED: Final = 113
SPI_GETSCREENSAVERRUNNING: Final = 114
SPI_GETDESKWALLPAPER: Final = 115

SPI_GETACTIVEWINDOWTRACKING: Final = 4096
SPI_SETACTIVEWINDOWTRACKING: Final = 4097
SPI_GETMENUANIMATION: Final = 4098
SPI_SETMENUANIMATION: Final = 4099
SPI_GETCOMBOBOXANIMATION: Final = 4100
SPI_SETCOMBOBOXANIMATION: Final = 4101
SPI_GETLISTBOXSMOOTHSCROLLING: Final = 4102
SPI_SETLISTBOXSMOOTHSCROLLING: Final = 4103
SPI_GETGRADIENTCAPTIONS: Final = 4104
SPI_SETGRADIENTCAPTIONS: Final = 4105
SPI_GETKEYBOARDCUES: Final = 4106
SPI_SETKEYBOARDCUES: Final = 4107
SPI_GETMENUUNDERLINES: Final = 4106
SPI_SETMENUUNDERLINES: Final = 4107
SPI_GETACTIVEWNDTRKZORDER: Final = 4108
SPI_SETACTIVEWNDTRKZORDER: Final = 4109
SPI_GETHOTTRACKING: Final = 4110
SPI_SETHOTTRACKING: Final = 4111

SPI_GETMENUFADE: Final = 4114
SPI_SETMENUFADE: Final = 4115
SPI_GETSELECTIONFADE: Final = 4116
SPI_SETSELECTIONFADE: Final = 4117
SPI_GETTOOLTIPANIMATION: Final = 4118
SPI_SETTOOLTIPANIMATION: Final = 4119
SPI_GETTOOLTIPFADE: Final = 4120
SPI_SETTOOLTIPFADE: Final = 4121
SPI_GETCURSORSHADOW: Final = 4122
SPI_SETCURSORSHADOW: Final = 4123
SPI_GETMOUSESONAR: Final = 4124
SPI_SETMOUSESONAR: Final = 4125
SPI_GETMOUSECLICKLOCK: Final = 4126
SPI_SETMOUSECLICKLOCK: Final = 4127
SPI_GETMOUSEVANISH: Final = 4128
SPI_SETMOUSEVANISH: Final = 4129
SPI_GETFLATMENU: Final = 4130
SPI_SETFLATMENU: Final = 4131
SPI_GETDROPSHADOW: Final = 4132
SPI_SETDROPSHADOW: Final = 4133
SPI_GETBLOCKSENDINPUTRESETS: Final = 4134
SPI_SETBLOCKSENDINPUTRESETS: Final = 4135
SPI_GETUIEFFECTS: Final = 4158
SPI_SETUIEFFECTS: Final = 4159

SPI_GETFOREGROUNDLOCKTIMEOUT: Final = 8192
SPI_SETFOREGROUNDLOCKTIMEOUT: Final = 8193
SPI_GETACTIVEWNDTRKTIMEOUT: Final = 8194
SPI_SETACTIVEWNDTRKTIMEOUT: Final = 8195
SPI_GETFOREGROUNDFLASHCOUNT: Final = 8196
SPI_SETFOREGROUNDFLASHCOUNT: Final = 8197
SPI_GETCARETWIDTH: Final = 8198
SPI_SETCARETWIDTH: Final = 8199
SPI_GETMOUSECLICKLOCKTIME: Final = 8200
SPI_SETMOUSECLICKLOCKTIME: Final = 8201
SPI_GETFONTSMOOTHINGTYPE: Final = 8202
SPI_SETFONTSMOOTHINGTYPE: Final = 8203
SPI_GETFONTSMOOTHINGCONTRAST: Final = 8204
SPI_SETFONTSMOOTHINGCONTRAST: Final = 8205
SPI_GETFOCUSBORDERWIDTH: Final = 8206
SPI_SETFOCUSBORDERWIDTH: Final = 8207
SPI_GETFOCUSBORDERHEIGHT: Final = 8208
SPI_SETFOCUSBORDERHEIGHT: Final = 8209
SPI_GETFONTSMOOTHINGORIENTATION: Final = 8210
SPI_SETFONTSMOOTHINGORIENTATION: Final = 8211

SPIF_UPDATEINIFILE: Final = 1
SPIF_SENDWININICHANGE: Final = 2
SPIF_SENDCHANGE: Final = SPIF_SENDWININICHANGE

FE_FONTSMOOTHINGSTANDARD: Final = 1
FE_FONTSMOOTHINGCLEARTYPE: Final = 2
FE_FONTSMOOTHINGDOCKING: Final = 32768

METRICS_USEDEFAULT: Final = -1
ARW_BOTTOMLEFT: Final = 0
ARW_BOTTOMRIGHT: Final = 1
ARW_TOPLEFT: Final = 2
ARW_TOPRIGHT: Final = 3
ARW_STARTMASK: Final = 3
ARW_STARTRIGHT: Final = 1
ARW_STARTTOP: Final = 2
ARW_LEFT: Final = 0
ARW_RIGHT: Final = 0
ARW_UP: Final = 4
ARW_DOWN: Final = 4
ARW_HIDE: Final = 8

SERKF_SERIALKEYSON: Final = 1
SERKF_AVAILABLE: Final = 2
SERKF_INDICATOR: Final = 4
HCF_HIGHCONTRASTON: Final = 1
HCF_AVAILABLE: Final = 2
HCF_HOTKEYACTIVE: Final = 4
HCF_CONFIRMHOTKEY: Final = 8
HCF_HOTKEYSOUND: Final = 16
HCF_INDICATOR: Final = 32
HCF_HOTKEYAVAILABLE: Final = 64
CDS_UPDATEREGISTRY: Final = 1
CDS_TEST: Final = 2
CDS_FULLSCREEN: Final = 4
CDS_GLOBAL: Final = 8
CDS_SET_PRIMARY: Final = 16
CDS_RESET: Final = **********
CDS_SETRECT: Final = *********
CDS_NORESET: Final = *********

DISP_CHANGE_SUCCESSFUL: Final = 0
DISP_CHANGE_RESTART: Final = 1
DISP_CHANGE_FAILED: Final = -1
DISP_CHANGE_BADMODE: Final = -2
DISP_CHANGE_NOTUPDATED: Final = -3
DISP_CHANGE_BADFLAGS: Final = -4
DISP_CHANGE_BADPARAM: Final = -5
DISP_CHANGE_BADDUALVIEW: Final = -6

ENUM_CURRENT_SETTINGS: Final = -1
ENUM_REGISTRY_SETTINGS: Final = -2
FKF_FILTERKEYSON: Final = 1
FKF_AVAILABLE: Final = 2
FKF_HOTKEYACTIVE: Final = 4
FKF_CONFIRMHOTKEY: Final = 8
FKF_HOTKEYSOUND: Final = 16
FKF_INDICATOR: Final = 32
FKF_CLICKON: Final = 64
SKF_STICKYKEYSON: Final = 1
SKF_AVAILABLE: Final = 2
SKF_HOTKEYACTIVE: Final = 4
SKF_CONFIRMHOTKEY: Final = 8
SKF_HOTKEYSOUND: Final = 16
SKF_INDICATOR: Final = 32
SKF_AUDIBLEFEEDBACK: Final = 64
SKF_TRISTATE: Final = 128
SKF_TWOKEYSOFF: Final = 256
SKF_LALTLATCHED: Final = *********
SKF_LCTLLATCHED: Final = 67108864
SKF_LSHIFTLATCHED: Final = 16777216
SKF_RALTLATCHED: Final = *********
SKF_RCTLLATCHED: Final = *********
SKF_RSHIFTLATCHED: Final = 33554432
SKF_LWINLATCHED: Final = **********
SKF_RWINLATCHED: Final = -**********
SKF_LALTLOCKED: Final = 1048576
SKF_LCTLLOCKED: Final = 262144
SKF_LSHIFTLOCKED: Final = 65536
SKF_RALTLOCKED: Final = 2097152
SKF_RCTLLOCKED: Final = 524288
SKF_RSHIFTLOCKED: Final = 131072
SKF_LWINLOCKED: Final = 4194304
SKF_RWINLOCKED: Final = 8388608
MKF_MOUSEKEYSON: Final = 1
MKF_AVAILABLE: Final = 2
MKF_HOTKEYACTIVE: Final = 4
MKF_CONFIRMHOTKEY: Final = 8
MKF_HOTKEYSOUND: Final = 16
MKF_INDICATOR: Final = 32
MKF_MODIFIERS: Final = 64
MKF_REPLACENUMBERS: Final = 128
MKF_LEFTBUTTONSEL: Final = *********
MKF_RIGHTBUTTONSEL: Final = *********
MKF_LEFTBUTTONDOWN: Final = 16777216
MKF_RIGHTBUTTONDOWN: Final = 33554432
MKF_MOUSEMODE: Final = -**********
ATF_TIMEOUTON: Final = 1
ATF_ONOFFFEEDBACK: Final = 2
SSGF_NONE: Final = 0
SSGF_DISPLAY: Final = 3
SSTF_NONE: Final = 0
SSTF_CHARS: Final = 1
SSTF_BORDER: Final = 2
SSTF_DISPLAY: Final = 3
SSWF_NONE: Final = 0
SSWF_TITLE: Final = 1
SSWF_WINDOW: Final = 2
SSWF_DISPLAY: Final = 3
SSWF_CUSTOM: Final = 4
SSF_SOUNDSENTRYON: Final = 1
SSF_AVAILABLE: Final = 2
SSF_INDICATOR: Final = 4
TKF_TOGGLEKEYSON: Final = 1
TKF_AVAILABLE: Final = 2
TKF_HOTKEYACTIVE: Final = 4
TKF_CONFIRMHOTKEY: Final = 8
TKF_HOTKEYSOUND: Final = 16
TKF_INDICATOR: Final = 32
SLE_ERROR: Final = 1
SLE_MINORERROR: Final = 2
SLE_WARNING: Final = 3
MONITOR_DEFAULTTONULL: Final = 0
MONITOR_DEFAULTTOPRIMARY: Final = 1
MONITOR_DEFAULTTONEAREST: Final = 2
MONITORINFOF_PRIMARY: Final = 1
CCHDEVICENAME: Final = 32
CHILDID_SELF: Final = 0
INDEXID_OBJECT: Final = 0
INDEXID_CONTAINER: Final = 0
OBJID_WINDOW: Final = 0
OBJID_SYSMENU: Final = -1
OBJID_TITLEBAR: Final = -2
OBJID_MENU: Final = -3
OBJID_CLIENT: Final = -4
OBJID_VSCROLL: Final = -5
OBJID_HSCROLL: Final = -6
OBJID_SIZEGRIP: Final = -7
OBJID_CARET: Final = -8
OBJID_CURSOR: Final = -9
OBJID_ALERT: Final = -10
OBJID_SOUND: Final = -11
EVENT_MIN: Final = 1
EVENT_MAX: Final = 2147483647
EVENT_SYSTEM_SOUND: Final = 1
EVENT_SYSTEM_ALERT: Final = 2
EVENT_SYSTEM_FOREGROUND: Final = 3
EVENT_SYSTEM_MENUSTART: Final = 4
EVENT_SYSTEM_MENUEND: Final = 5
EVENT_SYSTEM_MENUPOPUPSTART: Final = 6
EVENT_SYSTEM_MENUPOPUPEND: Final = 7
EVENT_SYSTEM_CAPTURESTART: Final = 8
EVENT_SYSTEM_CAPTUREEND: Final = 9
EVENT_SYSTEM_MOVESIZESTART: Final = 10
EVENT_SYSTEM_MOVESIZEEND: Final = 11
EVENT_SYSTEM_CONTEXTHELPSTART: Final = 12
EVENT_SYSTEM_CONTEXTHELPEND: Final = 13
EVENT_SYSTEM_DRAGDROPSTART: Final = 14
EVENT_SYSTEM_DRAGDROPEND: Final = 15
EVENT_SYSTEM_DIALOGSTART: Final = 16
EVENT_SYSTEM_DIALOGEND: Final = 17
EVENT_SYSTEM_SCROLLINGSTART: Final = 18
EVENT_SYSTEM_SCROLLINGEND: Final = 19
EVENT_SYSTEM_SWITCHSTART: Final = 20
EVENT_SYSTEM_SWITCHEND: Final = 21
EVENT_SYSTEM_MINIMIZESTART: Final = 22
EVENT_SYSTEM_MINIMIZEEND: Final = 23
EVENT_OBJECT_CREATE: Final = 32768
EVENT_OBJECT_DESTROY: Final = 32769
EVENT_OBJECT_SHOW: Final = 32770
EVENT_OBJECT_HIDE: Final = 32771
EVENT_OBJECT_REORDER: Final = 32772
EVENT_OBJECT_FOCUS: Final = 32773
EVENT_OBJECT_SELECTION: Final = 32774
EVENT_OBJECT_SELECTIONADD: Final = 32775
EVENT_OBJECT_SELECTIONREMOVE: Final = 32776
EVENT_OBJECT_SELECTIONWITHIN: Final = 32777
EVENT_OBJECT_STATECHANGE: Final = 32778
EVENT_OBJECT_LOCATIONCHANGE: Final = 32779
EVENT_OBJECT_NAMECHANGE: Final = 32780
EVENT_OBJECT_DESCRIPTIONCHANGE: Final = 32781
EVENT_OBJECT_VALUECHANGE: Final = 32782
EVENT_OBJECT_PARENTCHANGE: Final = 32783
EVENT_OBJECT_HELPCHANGE: Final = 32784
EVENT_OBJECT_DEFACTIONCHANGE: Final = 32785
EVENT_OBJECT_ACCELERATORCHANGE: Final = 32786
SOUND_SYSTEM_STARTUP: Final = 1
SOUND_SYSTEM_SHUTDOWN: Final = 2
SOUND_SYSTEM_BEEP: Final = 3
SOUND_SYSTEM_ERROR: Final = 4
SOUND_SYSTEM_QUESTION: Final = 5
SOUND_SYSTEM_WARNING: Final = 6
SOUND_SYSTEM_INFORMATION: Final = 7
SOUND_SYSTEM_MAXIMIZE: Final = 8
SOUND_SYSTEM_MINIMIZE: Final = 9
SOUND_SYSTEM_RESTOREUP: Final = 10
SOUND_SYSTEM_RESTOREDOWN: Final = 11
SOUND_SYSTEM_APPSTART: Final = 12
SOUND_SYSTEM_FAULT: Final = 13
SOUND_SYSTEM_APPEND: Final = 14
SOUND_SYSTEM_MENUCOMMAND: Final = 15
SOUND_SYSTEM_MENUPOPUP: Final = 16
CSOUND_SYSTEM: Final = 16
ALERT_SYSTEM_INFORMATIONAL: Final = 1
ALERT_SYSTEM_WARNING: Final = 2
ALERT_SYSTEM_ERROR: Final = 3
ALERT_SYSTEM_QUERY: Final = 4
ALERT_SYSTEM_CRITICAL: Final = 5
CALERT_SYSTEM: Final = 6
WINEVENT_OUTOFCONTEXT: Final = 0
WINEVENT_SKIPOWNTHREAD: Final = 1
WINEVENT_SKIPOWNPROCESS: Final = 2
WINEVENT_INCONTEXT: Final = 4
GUI_CARETBLINKING: Final = 1
GUI_INMOVESIZE: Final = 2
GUI_INMENUMODE: Final = 4
GUI_SYSTEMMENUMODE: Final = 8
GUI_POPUPMENUMODE: Final = 16
STATE_SYSTEM_UNAVAILABLE: Final = 1
STATE_SYSTEM_SELECTED: Final = 2
STATE_SYSTEM_FOCUSED: Final = 4
STATE_SYSTEM_PRESSED: Final = 8
STATE_SYSTEM_CHECKED: Final = 16
STATE_SYSTEM_MIXED: Final = 32
STATE_SYSTEM_READONLY: Final = 64
STATE_SYSTEM_HOTTRACKED: Final = 128
STATE_SYSTEM_DEFAULT: Final = 256
STATE_SYSTEM_EXPANDED: Final = 512
STATE_SYSTEM_COLLAPSED: Final = 1024
STATE_SYSTEM_BUSY: Final = 2048
STATE_SYSTEM_FLOATING: Final = 4096
STATE_SYSTEM_MARQUEED: Final = 8192
STATE_SYSTEM_ANIMATED: Final = 16384
STATE_SYSTEM_INVISIBLE: Final = 32768
STATE_SYSTEM_OFFSCREEN: Final = 65536
STATE_SYSTEM_SIZEABLE: Final = 131072
STATE_SYSTEM_MOVEABLE: Final = 262144
STATE_SYSTEM_SELFVOICING: Final = 524288
STATE_SYSTEM_FOCUSABLE: Final = 1048576
STATE_SYSTEM_SELECTABLE: Final = 2097152
STATE_SYSTEM_LINKED: Final = 4194304
STATE_SYSTEM_TRAVERSED: Final = 8388608
STATE_SYSTEM_MULTISELECTABLE: Final = 16777216
STATE_SYSTEM_EXTSELECTABLE: Final = 33554432
STATE_SYSTEM_ALERT_LOW: Final = 67108864
STATE_SYSTEM_ALERT_MEDIUM: Final = *********
STATE_SYSTEM_ALERT_HIGH: Final = *********
STATE_SYSTEM_VALID: Final = 536870911
CCHILDREN_TITLEBAR: Final = 5
CCHILDREN_SCROLLBAR: Final = 5
CURSOR_SHOWING: Final = 1
WS_ACTIVECAPTION: Final = 1
GA_MIC: Final = 1
GA_PARENT: Final = 1
GA_ROOT: Final = 2
GA_ROOTOWNER: Final = 3
GA_MAC: Final = 4

BF_LEFT: Final = 1
BF_TOP: Final = 2
BF_RIGHT: Final = 4
BF_BOTTOM: Final = 8
BF_TOPLEFT: Final[int]
BF_TOPRIGHT: Final[int]
BF_BOTTOMLEFT: Final[int]
BF_BOTTOMRIGHT: Final[int]
BF_RECT: Final[int]
BF_DIAGONAL: Final = 16
BF_DIAGONAL_ENDTOPRIGHT: Final[int]
BF_DIAGONAL_ENDTOPLEFT: Final[int]
BF_DIAGONAL_ENDBOTTOMLEFT: Final[int]
BF_DIAGONAL_ENDBOTTOMRIGHT: Final[int]
BF_MIDDLE: Final = 2048
BF_SOFT: Final = 4096
BF_ADJUST: Final = 8192
BF_FLAT: Final = 16384
BF_MONO: Final = 32768
DFC_CAPTION: Final = 1
DFC_MENU: Final = 2
DFC_SCROLL: Final = 3
DFC_BUTTON: Final = 4
DFC_POPUPMENU: Final = 5
DFCS_CAPTIONCLOSE: Final = 0
DFCS_CAPTIONMIN: Final = 1
DFCS_CAPTIONMAX: Final = 2
DFCS_CAPTIONRESTORE: Final = 3
DFCS_CAPTIONHELP: Final = 4
DFCS_MENUARROW: Final = 0
DFCS_MENUCHECK: Final = 1
DFCS_MENUBULLET: Final = 2
DFCS_MENUARROWRIGHT: Final = 4
DFCS_SCROLLUP: Final = 0
DFCS_SCROLLDOWN: Final = 1
DFCS_SCROLLLEFT: Final = 2
DFCS_SCROLLRIGHT: Final = 3
DFCS_SCROLLCOMBOBOX: Final = 5
DFCS_SCROLLSIZEGRIP: Final = 8
DFCS_SCROLLSIZEGRIPRIGHT: Final = 16
DFCS_BUTTONCHECK: Final = 0
DFCS_BUTTONRADIOIMAGE: Final = 1
DFCS_BUTTONRADIOMASK: Final = 2
DFCS_BUTTONRADIO: Final = 4
DFCS_BUTTON3STATE: Final = 8
DFCS_BUTTONPUSH: Final = 16
DFCS_INACTIVE: Final = 256
DFCS_PUSHED: Final = 512
DFCS_CHECKED: Final = 1024
DFCS_TRANSPARENT: Final = 2048
DFCS_HOT: Final = 4096
DFCS_ADJUSTRECT: Final = 8192
DFCS_FLAT: Final = 16384
DFCS_MONO: Final = 32768
DC_ACTIVE: Final = 1
DC_SMALLCAP: Final = 2
DC_ICON: Final = 4
DC_TEXT: Final = 8
DC_INBUTTON: Final = 16
DC_GRADIENT: Final = 32
IDANI_OPEN: Final = 1
IDANI_CLOSE: Final = 2
IDANI_CAPTION: Final = 3
CF_TEXT: Final = 1
CF_BITMAP: Final = 2
CF_METAFILEPICT: Final = 3
CF_SYLK: Final = 4
CF_DIF: Final = 5
CF_TIFF: Final = 6
CF_OEMTEXT: Final = 7
CF_DIB: Final = 8
CF_PALETTE: Final = 9
CF_PENDATA: Final = 10
CF_RIFF: Final = 11
CF_WAVE: Final = 12
CF_UNICODETEXT: Final = 13
CF_ENHMETAFILE: Final = 14
CF_HDROP: Final = 15
CF_LOCALE: Final = 16
CF_DIBV5: Final = 17
CF_MAX: Final = 18
CF_OWNERDISPLAY: Final = 128
CF_DSPTEXT: Final = 129
CF_DSPBITMAP: Final = 130
CF_DSPMETAFILEPICT: Final = 131
CF_DSPENHMETAFILE: Final = 142
CF_PRIVATEFIRST: Final = 512
CF_PRIVATELAST: Final = 767
CF_GDIOBJFIRST: Final = 768
CF_GDIOBJLAST: Final = 1023
FVIRTKEY: Final = 1
FNOINVERT: Final = 2
FSHIFT: Final = 4
FCONTROL: Final = 8
FALT: Final = 16
WPF_SETMINPOSITION: Final = 1
WPF_RESTORETOMAXIMIZED: Final = 2
ODT_MENU: Final = 1
ODT_LISTBOX: Final = 2
ODT_COMBOBOX: Final = 3
ODT_BUTTON: Final = 4
ODT_STATIC: Final = 5
ODA_DRAWENTIRE: Final = 1
ODA_SELECT: Final = 2
ODA_FOCUS: Final = 4
ODS_SELECTED: Final = 1
ODS_GRAYED: Final = 2
ODS_DISABLED: Final = 4
ODS_CHECKED: Final = 8
ODS_FOCUS: Final = 16
ODS_DEFAULT: Final = 32
ODS_COMBOBOXEDIT: Final = 4096
ODS_HOTLIGHT: Final = 64
ODS_INACTIVE: Final = 128
PM_NOREMOVE: Final = 0
PM_REMOVE: Final = 1
PM_NOYIELD: Final = 2
MOD_ALT: Final = 1
MOD_CONTROL: Final = 2
MOD_SHIFT: Final = 4
MOD_WIN: Final = 8
MOD_NOREPEAT: Final = 16384
IDHOT_SNAPWINDOW: Final = -1
IDHOT_SNAPDESKTOP: Final = -2

ENDSESSION_LOGOFF: Final = -**********
EWX_LOGOFF: Final = 0
EWX_SHUTDOWN: Final = 1
EWX_REBOOT: Final = 2
EWX_FORCE: Final = 4
EWX_POWEROFF: Final = 8
EWX_FORCEIFHUNG: Final = 16
BSM_ALLDESKTOPS: Final = 16
BROADCAST_QUERY_DENY: Final = 1112363332

DBWF_LPARAMPOINTER: Final = 32768

SWP_NOSIZE: Final = 1
SWP_NOMOVE: Final = 2
SWP_NOZORDER: Final = 4
SWP_NOREDRAW: Final = 8
SWP_NOACTIVATE: Final = 16
SWP_FRAMECHANGED: Final = 32
SWP_SHOWWINDOW: Final = 64
SWP_HIDEWINDOW: Final = 128
SWP_NOCOPYBITS: Final = 256
SWP_NOOWNERZORDER: Final = 512
SWP_NOSENDCHANGING: Final = 1024
SWP_DRAWFRAME: Final = SWP_FRAMECHANGED
SWP_NOREPOSITION: Final = SWP_NOOWNERZORDER
SWP_DEFERERASE: Final = 8192
SWP_ASYNCWINDOWPOS: Final = 16384

DLGWINDOWEXTRA: Final = 30

KEYEVENTF_EXTENDEDKEY: Final = 1
KEYEVENTF_KEYUP: Final = 2
KEYEVENTF_UNICODE: Final = 4
KEYEVENTF_SCANCODE: Final = 8
MOUSEEVENTF_MOVE: Final = 1
MOUSEEVENTF_LEFTDOWN: Final = 2
MOUSEEVENTF_LEFTUP: Final = 4
MOUSEEVENTF_RIGHTDOWN: Final = 8
MOUSEEVENTF_RIGHTUP: Final = 16
MOUSEEVENTF_MIDDLEDOWN: Final = 32
MOUSEEVENTF_MIDDLEUP: Final = 64
MOUSEEVENTF_XDOWN: Final = 128
MOUSEEVENTF_XUP: Final = 256
MOUSEEVENTF_WHEEL: Final = 2048
MOUSEEVENTF_HWHEEL: Final = 4096
MOUSEEVENTF_MOVE_NOCOALESCE: Final = 8192
MOUSEEVENTF_VIRTUALDESK: Final = 16384
MOUSEEVENTF_ABSOLUTE: Final = 32768
INPUT_MOUSE: Final = 0
INPUT_KEYBOARD: Final = 1
INPUT_HARDWARE: Final = 2
MWMO_WAITALL: Final = 1
MWMO_ALERTABLE: Final = 2
MWMO_INPUTAVAILABLE: Final = 4
QS_KEY: Final = 1
QS_MOUSEMOVE: Final = 2
QS_MOUSEBUTTON: Final = 4
QS_POSTMESSAGE: Final = 8
QS_TIMER: Final = 16
QS_PAINT: Final = 32
QS_SENDMESSAGE: Final = 64
QS_HOTKEY: Final = 128
QS_MOUSE: Final[int]
QS_INPUT: Final[int]
QS_ALLEVENTS: Final[int]
QS_ALLINPUT: Final[int]

IMN_CLOSESTATUSWINDOW: Final = 1
IMN_OPENSTATUSWINDOW: Final = 2
IMN_CHANGECANDIDATE: Final = 3
IMN_CLOSECANDIDATE: Final = 4
IMN_OPENCANDIDATE: Final = 5
IMN_SETCONVERSIONMODE: Final = 6
IMN_SETSENTENCEMODE: Final = 7
IMN_SETOPENSTATUS: Final = 8
IMN_SETCANDIDATEPOS: Final = 9
IMN_SETCOMPOSITIONFONT: Final = 10
IMN_SETCOMPOSITIONWINDOW: Final = 11
IMN_SETSTATUSWINDOWPOS: Final = 12
IMN_GUIDELINE: Final = 13
IMN_PRIVATE: Final = 14

HELP_CONTEXT: Final = 1
HELP_QUIT: Final = 2
HELP_INDEX: Final = 3
HELP_CONTENTS: Final = 3
HELP_HELPONHELP: Final = 4
HELP_SETINDEX: Final = 5
HELP_SETCONTENTS: Final = 5
HELP_CONTEXTPOPUP: Final = 8
HELP_FORCEFILE: Final = 9
HELP_KEY: Final = 257
HELP_COMMAND: Final = 258
HELP_PARTIALKEY: Final = 261
HELP_MULTIKEY: Final = 513
HELP_SETWINPOS: Final = 515
HELP_CONTEXTMENU: Final = 10
HELP_FINDER: Final = 11
HELP_WM_HELP: Final = 12
HELP_SETPOPUP_POS: Final = 13
HELP_TCARD: Final = 32768
HELP_TCARD_DATA: Final = 16
HELP_TCARD_OTHER_CALLER: Final = 17
IDH_NO_HELP: Final = 28440
IDH_MISSING_CONTEXT: Final = 28441
IDH_GENERIC_HELP_BUTTON: Final = 28442
IDH_OK: Final = 28443
IDH_CANCEL: Final = 28444
IDH_HELP: Final = 28445
GR_GDIOBJECTS: Final = 0
GR_USEROBJECTS: Final = 1

SRCCOPY: Final = 13369376
SRCPAINT: Final = 15597702
SRCAND: Final = 8913094
SRCINVERT: Final = 6684742
SRCERASE: Final = 4457256
NOTSRCCOPY: Final = 3342344
NOTSRCERASE: Final = 1114278
MERGECOPY: Final = 12583114
MERGEPAINT: Final = 12255782
PATCOPY: Final = 15728673
PATPAINT: Final = 16452105
PATINVERT: Final = 5898313
DSTINVERT: Final = 5570569
BLACKNESS: Final = 66
WHITENESS: Final = 16711778

R2_BLACK: Final = 1
R2_NOTMERGEPEN: Final = 2
R2_MASKNOTPEN: Final = 3
R2_NOTCOPYPEN: Final = 4
R2_MASKPENNOT: Final = 5
R2_NOT: Final = 6
R2_XORPEN: Final = 7
R2_NOTMASKPEN: Final = 8
R2_MASKPEN: Final = 9
R2_NOTXORPEN: Final = 10
R2_NOP: Final = 11
R2_MERGENOTPEN: Final = 12
R2_COPYPEN: Final = 13
R2_MERGEPENNOT: Final = 14
R2_MERGEPEN: Final = 15
R2_WHITE: Final = 16
R2_LAST: Final = 16
GDI_ERROR: Final = -1
ERROR: Final = 0
NULLREGION: Final = 1
SIMPLEREGION: Final = 2
COMPLEXREGION: Final = 3
RGN_ERROR: Final = ERROR
RGN_AND: Final = 1
RGN_OR: Final = 2
RGN_XOR: Final = 3
RGN_DIFF: Final = 4
RGN_COPY: Final = 5
RGN_MIN: Final = RGN_AND
RGN_MAX: Final = RGN_COPY

BLACKONWHITE: Final = 1
WHITEONBLACK: Final = 2
COLORONCOLOR: Final = 3
HALFTONE: Final = 4
MAXSTRETCHBLTMODE: Final = 4
STRETCH_ANDSCANS: Final = BLACKONWHITE
STRETCH_ORSCANS: Final = WHITEONBLACK
STRETCH_DELETESCANS: Final = COLORONCOLOR
STRETCH_HALFTONE: Final = HALFTONE

ALTERNATE: Final = 1
WINDING: Final = 2
POLYFILL_LAST: Final = 2

LAYOUT_RTL: Final = 1
LAYOUT_BTT: Final = 2
LAYOUT_VBH: Final = 4
LAYOUT_ORIENTATIONMASK: Final[int]
LAYOUT_BITMAPORIENTATIONPRESERVED: Final = 8

TA_NOUPDATECP: Final = 0
TA_UPDATECP: Final = 1
TA_LEFT: Final = 0
TA_RIGHT: Final = 2
TA_CENTER: Final = 6
TA_TOP: Final = 0
TA_BOTTOM: Final = 8
TA_BASELINE: Final = 24
TA_MASK: Final[int]
VTA_BASELINE: Final = TA_BASELINE
VTA_LEFT: Final = TA_BOTTOM
VTA_RIGHT: Final = TA_TOP
VTA_CENTER: Final = TA_CENTER
VTA_BOTTOM: Final = TA_RIGHT
VTA_TOP: Final = TA_LEFT
ETO_GRAYED: Final = 1
ETO_OPAQUE: Final = 2
ETO_CLIPPED: Final = 4
ASPECT_FILTERING: Final = 1
DCB_RESET: Final = 1
DCB_ACCUMULATE: Final = 2
DCB_DIRTY: Final = DCB_ACCUMULATE
DCB_SET: Final[int]
DCB_ENABLE: Final = 4
DCB_DISABLE: Final = 8
META_SETBKCOLOR: Final = 513
META_SETBKMODE: Final = 258
META_SETMAPMODE: Final = 259
META_SETROP2: Final = 260
META_SETRELABS: Final = 261
META_SETPOLYFILLMODE: Final = 262
META_SETSTRETCHBLTMODE: Final = 263
META_SETTEXTCHAREXTRA: Final = 264
META_SETTEXTCOLOR: Final = 521
META_SETTEXTJUSTIFICATION: Final = 522
META_SETWINDOWORG: Final = 523
META_SETWINDOWEXT: Final = 524
META_SETVIEWPORTORG: Final = 525
META_SETVIEWPORTEXT: Final = 526
META_OFFSETWINDOWORG: Final = 527
META_SCALEWINDOWEXT: Final = 1040
META_OFFSETVIEWPORTORG: Final = 529
META_SCALEVIEWPORTEXT: Final = 1042
META_LINETO: Final = 531
META_MOVETO: Final = 532
META_EXCLUDECLIPRECT: Final = 1045
META_INTERSECTCLIPRECT: Final = 1046
META_ARC: Final = 2071
META_ELLIPSE: Final = 1048
META_FLOODFILL: Final = 1049
META_PIE: Final = 2074
META_RECTANGLE: Final = 1051
META_ROUNDRECT: Final = 1564
META_PATBLT: Final = 1565
META_SAVEDC: Final = 30
META_SETPIXEL: Final = 1055
META_OFFSETCLIPRGN: Final = 544
META_TEXTOUT: Final = 1313
META_BITBLT: Final = 2338
META_STRETCHBLT: Final = 2851
META_POLYGON: Final = 804
META_POLYLINE: Final = 805
META_ESCAPE: Final = 1574
META_RESTOREDC: Final = 295
META_FILLREGION: Final = 552
META_FRAMEREGION: Final = 1065
META_INVERTREGION: Final = 298
META_PAINTREGION: Final = 299
META_SELECTCLIPREGION: Final = 300
META_SELECTOBJECT: Final = 301
META_SETTEXTALIGN: Final = 302
META_CHORD: Final = 2096
META_SETMAPPERFLAGS: Final = 561
META_EXTTEXTOUT: Final = 2610
META_SETDIBTODEV: Final = 3379
META_SELECTPALETTE: Final = 564
META_REALIZEPALETTE: Final = 53
META_ANIMATEPALETTE: Final = 1078
META_SETPALENTRIES: Final = 55
META_POLYPOLYGON: Final = 1336
META_RESIZEPALETTE: Final = 313
META_DIBBITBLT: Final = 2368
META_DIBSTRETCHBLT: Final = 2881
META_DIBCREATEPATTERNBRUSH: Final = 322
META_STRETCHDIB: Final = 3907
META_EXTFLOODFILL: Final = 1352
META_DELETEOBJECT: Final = 496
META_CREATEPALETTE: Final = 247
META_CREATEPATTERNBRUSH: Final = 505
META_CREATEPENINDIRECT: Final = 762
META_CREATEFONTINDIRECT: Final = 763
META_CREATEBRUSHINDIRECT: Final = 764
META_CREATEREGION: Final = 1791
FILE_BEGIN: Final = 0
FILE_CURRENT: Final = 1
FILE_END: Final = 2
FILE_FLAG_WRITE_THROUGH: Final = -**********
FILE_FLAG_OVERLAPPED: Final = **********
FILE_FLAG_NO_BUFFERING: Final = *********
FILE_FLAG_RANDOM_ACCESS: Final = *********
FILE_FLAG_SEQUENTIAL_SCAN: Final = *********
FILE_FLAG_DELETE_ON_CLOSE: Final = 67108864
FILE_FLAG_BACKUP_SEMANTICS: Final = 33554432
FILE_FLAG_POSIX_SEMANTICS: Final = 16777216
CREATE_NEW: Final = 1
CREATE_ALWAYS: Final = 2
OPEN_EXISTING: Final = 3
OPEN_ALWAYS: Final = 4
TRUNCATE_EXISTING: Final = 5
PIPE_ACCESS_INBOUND: Final = 1
PIPE_ACCESS_OUTBOUND: Final = 2
PIPE_ACCESS_DUPLEX: Final = 3
PIPE_CLIENT_END: Final = 0
PIPE_SERVER_END: Final = 1
PIPE_WAIT: Final = 0
PIPE_NOWAIT: Final = 1
PIPE_READMODE_BYTE: Final = 0
PIPE_READMODE_MESSAGE: Final = 2
PIPE_TYPE_BYTE: Final = 0
PIPE_TYPE_MESSAGE: Final = 4
PIPE_UNLIMITED_INSTANCES: Final = 255
SECURITY_CONTEXT_TRACKING: Final = 262144
SECURITY_EFFECTIVE_ONLY: Final = 524288
SECURITY_SQOS_PRESENT: Final = 1048576
SECURITY_VALID_SQOS_FLAGS: Final = 2031616
DTR_CONTROL_DISABLE: Final = 0
DTR_CONTROL_ENABLE: Final = 1
DTR_CONTROL_HANDSHAKE: Final = 2
RTS_CONTROL_DISABLE: Final = 0
RTS_CONTROL_ENABLE: Final = 1
RTS_CONTROL_HANDSHAKE: Final = 2
RTS_CONTROL_TOGGLE: Final = 3
GMEM_FIXED: Final = 0
GMEM_MOVEABLE: Final = 2
GMEM_NOCOMPACT: Final = 16
GMEM_NODISCARD: Final = 32
GMEM_ZEROINIT: Final = 64
GMEM_MODIFY: Final = 128
GMEM_DISCARDABLE: Final = 256
GMEM_NOT_BANKED: Final = 4096
GMEM_SHARE: Final = 8192
GMEM_DDESHARE: Final = 8192
GMEM_NOTIFY: Final = 16384
GMEM_LOWER: Final = GMEM_NOT_BANKED
GMEM_VALID_FLAGS: Final = 32626
GMEM_INVALID_HANDLE: Final = 32768
GHND: Final[int]
GPTR: Final[int]
GMEM_DISCARDED: Final = 16384
GMEM_LOCKCOUNT: Final = 255
LMEM_FIXED: Final = 0
LMEM_MOVEABLE: Final = 2
LMEM_NOCOMPACT: Final = 16
LMEM_NODISCARD: Final = 32
LMEM_ZEROINIT: Final = 64
LMEM_MODIFY: Final = 128
LMEM_DISCARDABLE: Final = 3840
LMEM_VALID_FLAGS: Final = 3954
LMEM_INVALID_HANDLE: Final = 32768
LHND: Final[int]
LPTR: Final[int]
NONZEROLHND: Final = LMEM_MOVEABLE
NONZEROLPTR: Final = LMEM_FIXED
LMEM_DISCARDED: Final = 16384
LMEM_LOCKCOUNT: Final = 255
DEBUG_PROCESS: Final = 1
DEBUG_ONLY_THIS_PROCESS: Final = 2
CREATE_SUSPENDED: Final = 4
DETACHED_PROCESS: Final = 8
CREATE_NEW_CONSOLE: Final = 16
NORMAL_PRIORITY_CLASS: Final = 32
IDLE_PRIORITY_CLASS: Final = 64
HIGH_PRIORITY_CLASS: Final = 128
REALTIME_PRIORITY_CLASS: Final = 256
CREATE_NEW_PROCESS_GROUP: Final = 512
CREATE_UNICODE_ENVIRONMENT: Final = 1024
CREATE_SEPARATE_WOW_VDM: Final = 2048
CREATE_SHARED_WOW_VDM: Final = 4096
CREATE_DEFAULT_ERROR_MODE: Final = 67108864
CREATE_NO_WINDOW: Final = *********
PROFILE_USER: Final = *********
PROFILE_KERNEL: Final = *********
PROFILE_SERVER: Final = **********
THREAD_BASE_PRIORITY_LOWRT: Final = 15
THREAD_BASE_PRIORITY_MAX: Final = 2
THREAD_BASE_PRIORITY_MIN: Final = -2
THREAD_BASE_PRIORITY_IDLE: Final = -15
THREAD_PRIORITY_LOWEST: Final = THREAD_BASE_PRIORITY_MIN
THREAD_PRIORITY_BELOW_NORMAL: Final[int]
THREAD_PRIORITY_HIGHEST: Final = THREAD_BASE_PRIORITY_MAX
THREAD_PRIORITY_ABOVE_NORMAL: Final[int]
THREAD_PRIORITY_ERROR_RETURN: Final = MAXLONG
THREAD_PRIORITY_TIME_CRITICAL: Final = THREAD_BASE_PRIORITY_LOWRT
THREAD_PRIORITY_IDLE: Final = THREAD_BASE_PRIORITY_IDLE
THREAD_PRIORITY_NORMAL: Final = 0
THREAD_MODE_BACKGROUND_BEGIN: Final = 0x00010000
THREAD_MODE_BACKGROUND_END: Final = 0x00020000

EXCEPTION_DEBUG_EVENT: Final = 1
CREATE_THREAD_DEBUG_EVENT: Final = 2
CREATE_PROCESS_DEBUG_EVENT: Final = 3
EXIT_THREAD_DEBUG_EVENT: Final = 4
EXIT_PROCESS_DEBUG_EVENT: Final = 5
LOAD_DLL_DEBUG_EVENT: Final = 6
UNLOAD_DLL_DEBUG_EVENT: Final = 7
OUTPUT_DEBUG_STRING_EVENT: Final = 8
RIP_EVENT: Final = 9
DRIVE_UNKNOWN: Final = 0
DRIVE_NO_ROOT_DIR: Final = 1
DRIVE_REMOVABLE: Final = 2
DRIVE_FIXED: Final = 3
DRIVE_REMOTE: Final = 4
DRIVE_CDROM: Final = 5
DRIVE_RAMDISK: Final = 6
FILE_TYPE_UNKNOWN: Final = 0
FILE_TYPE_DISK: Final = 1
FILE_TYPE_CHAR: Final = 2
FILE_TYPE_PIPE: Final = 3
FILE_TYPE_REMOTE: Final = 32768
NOPARITY: Final = 0
ODDPARITY: Final = 1
EVENPARITY: Final = 2
MARKPARITY: Final = 3
SPACEPARITY: Final = 4
ONESTOPBIT: Final = 0
ONE5STOPBITS: Final = 1
TWOSTOPBITS: Final = 2
CBR_110: Final = 110
CBR_300: Final = 300
CBR_600: Final = 600
CBR_1200: Final = 1200
CBR_2400: Final = 2400
CBR_4800: Final = 4800
CBR_9600: Final = 9600
CBR_14400: Final = 14400
CBR_19200: Final = 19200
CBR_38400: Final = 38400
CBR_56000: Final = 56000
CBR_57600: Final = 57600
CBR_115200: Final = 115200
CBR_128000: Final = 128000
CBR_256000: Final = 256000
S_QUEUEEMPTY: Final = 0
S_THRESHOLD: Final = 1
S_ALLTHRESHOLD: Final = 2
S_NORMAL: Final = 0
S_LEGATO: Final = 1
S_STACCATO: Final = 2
NMPWAIT_WAIT_FOREVER: Final = -1
NMPWAIT_NOWAIT: Final = 1
NMPWAIT_USE_DEFAULT_WAIT: Final = 0
OF_READ: Final = 0
OF_WRITE: Final = 1
OF_READWRITE: Final = 2
OF_SHARE_COMPAT: Final = 0
OF_SHARE_EXCLUSIVE: Final = 16
OF_SHARE_DENY_WRITE: Final = 32
OF_SHARE_DENY_READ: Final = 48
OF_SHARE_DENY_NONE: Final = 64
OF_PARSE: Final = 256
OF_DELETE: Final = 512
OF_VERIFY: Final = 1024
OF_CANCEL: Final = 2048
OF_CREATE: Final = 4096
OF_PROMPT: Final = 8192
OF_EXIST: Final = 16384
OF_REOPEN: Final = 32768
OFS_MAXPATHNAME: Final = 128
MAXINTATOM: Final = 49152

PROCESS_HEAP_REGION: Final = 1
PROCESS_HEAP_UNCOMMITTED_RANGE: Final = 2
PROCESS_HEAP_ENTRY_BUSY: Final = 4
PROCESS_HEAP_ENTRY_MOVEABLE: Final = 16
PROCESS_HEAP_ENTRY_DDESHARE: Final = 32
SCS_32BIT_BINARY: Final = 0
SCS_DOS_BINARY: Final = 1
SCS_WOW_BINARY: Final = 2
SCS_PIF_BINARY: Final = 3
SCS_POSIX_BINARY: Final = 4
SCS_OS216_BINARY: Final = 5
SEM_FAILCRITICALERRORS: Final = 1
SEM_NOGPFAULTERRORBOX: Final = 2
SEM_NOALIGNMENTFAULTEXCEPT: Final = 4
SEM_NOOPENFILEERRORBOX: Final = 32768
LOCKFILE_FAIL_IMMEDIATELY: Final = 1
LOCKFILE_EXCLUSIVE_LOCK: Final = 2
HANDLE_FLAG_INHERIT: Final = 1
HANDLE_FLAG_PROTECT_FROM_CLOSE: Final = 2
HINSTANCE_ERROR: Final = 32
GET_TAPE_MEDIA_INFORMATION: Final = 0
GET_TAPE_DRIVE_INFORMATION: Final = 1
SET_TAPE_MEDIA_INFORMATION: Final = 0
SET_TAPE_DRIVE_INFORMATION: Final = 1
FORMAT_MESSAGE_ALLOCATE_BUFFER: Final = 256
FORMAT_MESSAGE_IGNORE_INSERTS: Final = 512
FORMAT_MESSAGE_FROM_STRING: Final = 1024
FORMAT_MESSAGE_FROM_HMODULE: Final = 2048
FORMAT_MESSAGE_FROM_SYSTEM: Final = 4096
FORMAT_MESSAGE_ARGUMENT_ARRAY: Final = 8192
FORMAT_MESSAGE_MAX_WIDTH_MASK: Final = 255
BACKUP_INVALID: Final = 0
BACKUP_DATA: Final = 1
BACKUP_EA_DATA: Final = 2
BACKUP_SECURITY_DATA: Final = 3
BACKUP_ALTERNATE_DATA: Final = 4
BACKUP_LINK: Final = 5
BACKUP_PROPERTY_DATA: Final = 6
BACKUP_OBJECT_ID: Final = 7
BACKUP_REPARSE_DATA: Final = 8
BACKUP_SPARSE_BLOCK: Final = 9

STREAM_NORMAL_ATTRIBUTE: Final = 0
STREAM_MODIFIED_WHEN_READ: Final = 1
STREAM_CONTAINS_SECURITY: Final = 2
STREAM_CONTAINS_PROPERTIES: Final = 4
STARTF_USESHOWWINDOW: Final = 1
STARTF_USESIZE: Final = 2
STARTF_USEPOSITION: Final = 4
STARTF_USECOUNTCHARS: Final = 8
STARTF_USEFILLATTRIBUTE: Final = 16
STARTF_FORCEONFEEDBACK: Final = 64
STARTF_FORCEOFFFEEDBACK: Final = 128
STARTF_USESTDHANDLES: Final = 256
STARTF_USEHOTKEY: Final = 512
SHUTDOWN_NORETRY: Final = 1
DONT_RESOLVE_DLL_REFERENCES: Final = 1
LOAD_LIBRARY_AS_DATAFILE: Final = 2
LOAD_WITH_ALTERED_SEARCH_PATH: Final = 8
DDD_RAW_TARGET_PATH: Final = 1
DDD_REMOVE_DEFINITION: Final = 2
DDD_EXACT_MATCH_ON_REMOVE: Final = 4
MOVEFILE_REPLACE_EXISTING: Final = 1
MOVEFILE_COPY_ALLOWED: Final = 2
MOVEFILE_DELAY_UNTIL_REBOOT: Final = 4
MAX_COMPUTERNAME_LENGTH: Final = 15
LOGON32_LOGON_INTERACTIVE: Final = 2
LOGON32_LOGON_NETWORK: Final = 3
LOGON32_LOGON_BATCH: Final = 4
LOGON32_LOGON_SERVICE: Final = 5
LOGON32_LOGON_UNLOCK: Final = 7
LOGON32_LOGON_NETWORK_CLEARTEXT: Final = 8
LOGON32_LOGON_NEW_CREDENTIALS: Final = 9
LOGON32_PROVIDER_DEFAULT: Final = 0
LOGON32_PROVIDER_WINNT35: Final = 1
LOGON32_PROVIDER_WINNT40: Final = 2
LOGON32_PROVIDER_WINNT50: Final = 3
VER_PLATFORM_WIN32s: Final = 0
VER_PLATFORM_WIN32_WINDOWS: Final = 1
VER_PLATFORM_WIN32_NT: Final = 2
TC_NORMAL: Final = 0
TC_HARDERR: Final = 1
TC_GP_TRAP: Final = 2
TC_SIGNAL: Final = 3
AC_LINE_OFFLINE: Final = 0
AC_LINE_ONLINE: Final = 1
AC_LINE_BACKUP_POWER: Final = 2
AC_LINE_UNKNOWN: Final = 255
BATTERY_FLAG_HIGH: Final = 1
BATTERY_FLAG_LOW: Final = 2
BATTERY_FLAG_CRITICAL: Final = 4
BATTERY_FLAG_CHARGING: Final = 8
BATTERY_FLAG_NO_BATTERY: Final = 128
BATTERY_FLAG_UNKNOWN: Final = 255
BATTERY_PERCENTAGE_UNKNOWN: Final = 255
BATTERY_LIFE_UNKNOWN: Final = -1

cchTextLimitDefault: Final = 32767
WM_CONTEXTMENU: Final = 123
WM_PRINTCLIENT: Final = 792
EN_MSGFILTER: Final = 1792
EN_REQUESTRESIZE: Final = 1793
EN_SELCHANGE: Final = 1794
EN_DROPFILES: Final = 1795
EN_PROTECTED: Final = 1796
EN_CORRECTTEXT: Final = 1797
EN_STOPNOUNDO: Final = 1798
EN_IMECHANGE: Final = 1799
EN_SAVECLIPBOARD: Final = 1800
EN_OLEOPFAILED: Final = 1801
ENM_NONE: Final = 0
ENM_CHANGE: Final = 1
ENM_UPDATE: Final = 2
ENM_SCROLL: Final = 4
ENM_KEYEVENTS: Final = 65536
ENM_MOUSEEVENTS: Final = 131072
ENM_REQUESTRESIZE: Final = 262144
ENM_SELCHANGE: Final = 524288
ENM_DROPFILES: Final = 1048576
ENM_PROTECTED: Final = 2097152
ENM_CORRECTTEXT: Final = 4194304
ENM_IMECHANGE: Final = 8388608
ES_SAVESEL: Final = 32768
ES_SUNKEN: Final = 16384
ES_DISABLENOSCROLL: Final = 8192
ES_SELECTIONBAR: Final = 16777216
ES_EX_NOCALLOLEINIT: Final = 16777216
ES_VERTICAL: Final = 4194304
ES_NOIME: Final = 524288
ES_SELFIME: Final = 262144
ECO_AUTOWORDSELECTION: Final = 1
ECO_AUTOVSCROLL: Final = 64
ECO_AUTOHSCROLL: Final = 128
ECO_NOHIDESEL: Final = 256
ECO_READONLY: Final = 2048
ECO_WANTRETURN: Final = 4096
ECO_SAVESEL: Final = 32768
ECO_SELECTIONBAR: Final = 16777216
ECO_VERTICAL: Final = 4194304
ECOOP_SET: Final = 1
ECOOP_OR: Final = 2
ECOOP_AND: Final = 3
ECOOP_XOR: Final = 4
WB_CLASSIFY: Final = 3
WB_MOVEWORDLEFT: Final = 4
WB_MOVEWORDRIGHT: Final = 5
WB_LEFTBREAK: Final = 6
WB_RIGHTBREAK: Final = 7
WB_MOVEWORDPREV: Final = 4
WB_MOVEWORDNEXT: Final = 5
WB_PREVBREAK: Final = 6
WB_NEXTBREAK: Final = 7
PC_FOLLOWING: Final = 1
PC_LEADING: Final = 2
PC_OVERFLOW: Final = 3
PC_DELIMITER: Final = 4
WBF_WORDWRAP: Final = 16
WBF_WORDBREAK: Final = 32
WBF_OVERFLOW: Final = 64
WBF_LEVEL1: Final = 128
WBF_LEVEL2: Final = 256
WBF_CUSTOM: Final = 512
CFM_BOLD: Final = 1
CFM_ITALIC: Final = 2
CFM_UNDERLINE: Final = 4
CFM_STRIKEOUT: Final = 8
CFM_PROTECTED: Final = 16
CFM_SIZE: Final = -**********
CFM_COLOR: Final = **********
CFM_FACE: Final = *********
CFM_OFFSET: Final = *********
CFM_CHARSET: Final = *********
CFE_BOLD: Final = 1
CFE_ITALIC: Final = 2
CFE_UNDERLINE: Final = 4
CFE_STRIKEOUT: Final = 8
CFE_PROTECTED: Final = 16
CFE_AUTOCOLOR: Final = **********
yHeightCharPtsMost: Final = 1638
SCF_SELECTION: Final = 1
SCF_WORD: Final = 2
SF_TEXT: Final = 1
SF_RTF: Final = 2
SF_RTFNOOBJS: Final = 3
SF_TEXTIZED: Final = 4
SFF_SELECTION: Final = 32768
SFF_PLAINRTF: Final = 16384
MAX_TAB_STOPS: Final = 32
lDefaultTab: Final = 720
PFM_STARTINDENT: Final = 1
PFM_RIGHTINDENT: Final = 2
PFM_OFFSET: Final = 4
PFM_ALIGNMENT: Final = 8
PFM_TABSTOPS: Final = 16
PFM_NUMBERING: Final = 32
PFM_OFFSETINDENT: Final = -**********
PFN_BULLET: Final = 1
PFA_LEFT: Final = 1
PFA_RIGHT: Final = 2
PFA_CENTER: Final = 3
WM_NOTIFY: Final = 78
SEL_EMPTY: Final = 0
SEL_TEXT: Final = 1
SEL_OBJECT: Final = 2
SEL_MULTICHAR: Final = 4
SEL_MULTIOBJECT: Final = 8
OLEOP_DOVERB: Final = 1
CF_RTF: Final = "Rich Text Format"
CF_RTFNOOBJS: Final = "Rich Text Format Without Objects"
CF_RETEXTOBJ: Final = "RichEdit Text and Objects"

RIGHT_ALT_PRESSED: Final = 1
LEFT_ALT_PRESSED: Final = 2
RIGHT_CTRL_PRESSED: Final = 4
LEFT_CTRL_PRESSED: Final = 8
SHIFT_PRESSED: Final = 16
NUMLOCK_ON: Final = 32
SCROLLLOCK_ON: Final = 64
CAPSLOCK_ON: Final = 128
ENHANCED_KEY: Final = 256
NLS_DBCSCHAR: Final = 65536
NLS_ALPHANUMERIC: Final = 0
NLS_KATAKANA: Final = 131072
NLS_HIRAGANA: Final = 262144
NLS_ROMAN: Final = 4194304
NLS_IME_CONVERSION: Final = 8388608
NLS_IME_DISABLE: Final = *********

FROM_LEFT_1ST_BUTTON_PRESSED: Final = 1
RIGHTMOST_BUTTON_PRESSED: Final = 2
FROM_LEFT_2ND_BUTTON_PRESSED: Final = 4
FROM_LEFT_3RD_BUTTON_PRESSED: Final = 8
FROM_LEFT_4TH_BUTTON_PRESSED: Final = 16

CTRL_C_EVENT: Final = 0
CTRL_BREAK_EVENT: Final = 1
CTRL_CLOSE_EVENT: Final = 2
CTRL_LOGOFF_EVENT: Final = 5
CTRL_SHUTDOWN_EVENT: Final = 6

MOUSE_MOVED: Final = 1
DOUBLE_CLICK: Final = 2
MOUSE_WHEELED: Final = 4

PSM_SETCURSEL: Final[int]
PSM_REMOVEPAGE: Final[int]
PSM_ADDPAGE: Final[int]
PSM_CHANGED: Final[int]
PSM_RESTARTWINDOWS: Final[int]
PSM_REBOOTSYSTEM: Final[int]
PSM_CANCELTOCLOSE: Final[int]
PSM_QUERYSIBLINGS: Final[int]
PSM_UNCHANGED: Final[int]
PSM_APPLY: Final[int]
PSM_SETTITLEA: Final[int]
PSM_SETTITLEW: Final[int]
PSM_SETWIZBUTTONS: Final[int]
PSM_PRESSBUTTON: Final[int]
PSM_SETCURSELID: Final[int]
PSM_SETFINISHTEXTA: Final[int]
PSM_SETFINISHTEXTW: Final[int]
PSM_GETTABCONTROL: Final[int]
PSM_ISDIALOGMESSAGE: Final[int]
PSM_GETCURRENTPAGEHWND: Final[int]
PSM_INSERTPAGE: Final[int]
PSM_SETHEADERTITLEA: Final[int]
PSM_SETHEADERTITLEW: Final[int]
PSM_SETHEADERSUBTITLEA: Final[int]
PSM_SETHEADERSUBTITLEW: Final[int]
PSM_HWNDTOINDEX: Final[int]
PSM_INDEXTOHWND: Final[int]
PSM_PAGETOINDEX: Final[int]
PSM_INDEXTOPAGE: Final[int]
PSM_IDTOINDEX: Final[int]
PSM_INDEXTOID: Final[int]
PSM_GETRESULT: Final[int]
PSM_RECALCPAGESIZES: Final[int]

NameUnknown: Final = 0
NameFullyQualifiedDN: Final = 1
NameSamCompatible: Final = 2
NameDisplay: Final = 3
NameUniqueId: Final = 6
NameCanonical: Final = 7
NameUserPrincipal: Final = 8
NameCanonicalEx: Final = 9
NameServicePrincipal: Final = 10
NameDnsDomain: Final = 12

ComputerNameNetBIOS: Final = 0
ComputerNameDnsHostname: Final = 1
ComputerNameDnsDomain: Final = 2
ComputerNameDnsFullyQualified: Final = 3
ComputerNamePhysicalNetBIOS: Final = 4
ComputerNamePhysicalDnsHostname: Final = 5
ComputerNamePhysicalDnsDomain: Final = 6
ComputerNamePhysicalDnsFullyQualified: Final = 7

LWA_COLORKEY: Final = 0x00000001
LWA_ALPHA: Final = 0x00000002
ULW_COLORKEY: Final = 0x00000001
ULW_ALPHA: Final = 0x00000002
ULW_OPAQUE: Final = 0x00000004

TRUE: Final = 1
FALSE: Final = 0
MAX_PATH: Final = 260

AC_SRC_OVER: Final = 0
AC_SRC_ALPHA: Final = 1
GRADIENT_FILL_RECT_H: Final = 0
GRADIENT_FILL_RECT_V: Final = 1
GRADIENT_FILL_TRIANGLE: Final = 2
GRADIENT_FILL_OP_FLAG: Final = 255

MM_WORKING_SET_MAX_HARD_ENABLE: Final = 1
MM_WORKING_SET_MAX_HARD_DISABLE: Final = 2
MM_WORKING_SET_MIN_HARD_ENABLE: Final = 4
MM_WORKING_SET_MIN_HARD_DISABLE: Final = 8

VOLUME_NAME_DOS: Final = 0
VOLUME_NAME_GUID: Final = 1
VOLUME_NAME_NT: Final = 2
VOLUME_NAME_NONE: Final = 4
FILE_NAME_NORMALIZED: Final = 0
FILE_NAME_OPENED: Final = 8

DEVICE_NOTIFY_WINDOW_HANDLE: Final = 0x00000000
DEVICE_NOTIFY_SERVICE_HANDLE: Final = 0x00000001

WM_DEVICECHANGE: Final = 0x0219
BSF_QUERY: Final = 0x00000001
BSF_IGNORECURRENTTASK: Final = 0x00000002
BSF_FLUSHDISK: Final = 0x00000004
BSF_NOHANG: Final = 0x00000008
BSF_POSTMESSAGE: Final = 0x00000010
BSF_FORCEIFHUNG: Final = 0x00000020
BSF_NOTIMEOUTIFNOTHUNG: Final = 0x00000040
BSF_MSGSRV32ISOK: Final = -**********
BSF_MSGSRV32ISOK_BIT: Final = 31
BSM_ALLCOMPONENTS: Final = 0x00000000
BSM_VXDS: Final = 0x00000001
BSM_NETDRIVER: Final = 0x00000002
BSM_INSTALLABLEDRIVERS: Final = 0x00000004
BSM_APPLICATIONS: Final = 0x00000008
DBT_APPYBEGIN: Final = 0x0000
DBT_APPYEND: Final = 0x0001
DBT_DEVNODES_CHANGED: Final = 0x0007
DBT_QUERYCHANGECONFIG: Final = 0x0017
DBT_CONFIGCHANGED: Final = 0x0018
DBT_CONFIGCHANGECANCELED: Final = 0x0019
DBT_MONITORCHANGE: Final = 0x001B
DBT_SHELLLOGGEDON: Final = 0x0020
DBT_CONFIGMGAPI32: Final = 0x0022
DBT_VXDINITCOMPLETE: Final = 0x0023
DBT_VOLLOCKQUERYLOCK: Final = 0x8041
DBT_VOLLOCKLOCKTAKEN: Final = 0x8042
DBT_VOLLOCKLOCKFAILED: Final = 0x8043
DBT_VOLLOCKQUERYUNLOCK: Final = 0x8044
DBT_VOLLOCKLOCKRELEASED: Final = 0x8045
DBT_VOLLOCKUNLOCKFAILED: Final = 0x8046
LOCKP_ALLOW_WRITES: Final = 0x01
LOCKP_FAIL_WRITES: Final = 0x00
LOCKP_FAIL_MEM_MAPPING: Final = 0x02
LOCKP_ALLOW_MEM_MAPPING: Final = 0x00
LOCKP_USER_MASK: Final = 0x03
LOCKP_LOCK_FOR_FORMAT: Final = 0x04
LOCKF_LOGICAL_LOCK: Final = 0x00
LOCKF_PHYSICAL_LOCK: Final = 0x01
DBT_NO_DISK_SPACE: Final = 0x0047
DBT_LOW_DISK_SPACE: Final = 0x0048
DBT_CONFIGMGPRIVATE: Final = 0x7FFF
DBT_DEVICEARRIVAL: Final = 0x8000
DBT_DEVICEQUERYREMOVE: Final = 0x8001
DBT_DEVICEQUERYREMOVEFAILED: Final = 0x8002
DBT_DEVICEREMOVEPENDING: Final = 0x8003
DBT_DEVICEREMOVECOMPLETE: Final = 0x8004
DBT_DEVICETYPESPECIFIC: Final = 0x8005
DBT_CUSTOMEVENT: Final = 0x8006
DBT_DEVTYP_OEM: Final = 0x00000000
DBT_DEVTYP_DEVNODE: Final = 0x00000001
DBT_DEVTYP_VOLUME: Final = 0x00000002
DBT_DEVTYP_PORT: Final = 0x00000003
DBT_DEVTYP_NET: Final = 0x00000004
DBT_DEVTYP_DEVICEINTERFACE: Final = 0x00000005
DBT_DEVTYP_HANDLE: Final = 0x00000006
DBTF_MEDIA: Final = 0x0001
DBTF_NET: Final = 0x0002
DBTF_RESOURCE: Final = 0x00000001
DBTF_XPORT: Final = 0x00000002
DBTF_SLOWNET: Final = 0x00000004
DBT_VPOWERDAPI: Final = 0x8100
DBT_USERDEFINED: Final = 0xFFFF

IME_CMODE_ALPHANUMERIC: Final = 0x0000
IME_CMODE_NATIVE: Final = 0x0001
IME_CMODE_CHINESE: Final = IME_CMODE_NATIVE
IME_CMODE_HANGUL: Final = IME_CMODE_NATIVE
IME_CMODE_JAPANESE: Final = IME_CMODE_NATIVE
IME_CMODE_KATAKANA: Final = 0x0002
IME_CMODE_LANGUAGE: Final = 0x0003
IME_CMODE_FULLSHAPE: Final = 0x0008
IME_CMODE_ROMAN: Final = 0x0010
IME_CMODE_CHARCODE: Final = 0x0020
IME_CMODE_HANJACONVERT: Final = 0x0040
IME_CMODE_NATIVESYMBOL: Final = 0x0080
