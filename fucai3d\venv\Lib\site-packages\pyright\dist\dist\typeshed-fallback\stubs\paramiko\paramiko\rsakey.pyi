from _typeshed import FileD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReadableBuffer
from collections.abc import Callable
from typing import IO

from cryptography.hazmat.primitives.asymmetric.rsa import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RSAPublic<PERSON>ey, RSAPublicNumbers
from paramiko.message import Message
from paramiko.pkey import PKey

class RSAKey(PKey):
    key: None | RSAPublicKey | RSAPrivateKey
    public_blob: None
    def __init__(
        self,
        msg: Message | None = None,
        data: ReadableBuffer | None = None,
        filename: FileDescriptorOrPath | None = None,
        password: str | None = None,
        key: None | RSAPublicKey | RSAPrivateKey = None,
        file_obj: IO[str] | None = None,
    ) -> None: ...
    @property
    def size(self) -> int: ...
    @property
    def public_numbers(self) -> RSAPublicNumbers: ...
    def asbytes(self) -> bytes: ...
    def __hash__(self) -> int: ...
    def get_name(self) -> str: ...
    def get_bits(self) -> int: ...
    def can_sign(self) -> bool: ...
    def sign_ssh_data(self, data: bytes, algorithm: str | None = None) -> Message: ...
    def verify_ssh_sig(self, data: bytes, msg: Message) -> bool: ...
    def write_private_key_file(self, filename: FileDescriptorOrPath, password: str | None = None) -> None: ...
    def write_private_key(self, file_obj: IO[str], password: str | None = None) -> None: ...
    @staticmethod
    def generate(bits: int, progress_func: Callable[..., object] | None = None) -> RSAKey: ...
