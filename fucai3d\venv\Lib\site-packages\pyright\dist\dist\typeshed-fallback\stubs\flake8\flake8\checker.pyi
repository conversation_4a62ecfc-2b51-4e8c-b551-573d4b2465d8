import argparse
import tokenize
from _typeshed import Incomplete
from collections.abc import Sequence
from logging import Logger
from typing import Any
from typing_extensions import TypeAlias

from .plugins.finder import Checkers, LoadedPlugin
from .processor import _LogicalMapping
from .style_guide import StyleGuideManager

Results: TypeAlias = list[tuple[str, int, int, str, str | None]]

LOG: Logger
SERIAL_RETRY_ERRNOS: Incomplete

class Manager:
    style_guide: Incomplete
    options: Incomplete
    plugins: Incomplete
    jobs: Incomplete
    statistics: Incomplete
    exclude: Incomplete
    argv: Incomplete
    results: Incomplete
    def __init__(self, style_guide: StyleGuideManager, plugins: Checkers, argv: Sequence[str]) -> None: ...
    def report(self) -> tuple[int, int]: ...
    def run_parallel(self) -> None: ...
    def run_serial(self) -> None: ...
    def run(self) -> None: ...
    filenames: Incomplete
    def start(self) -> None: ...
    def stop(self) -> None: ...

class FileChecker:
    options: Incomplete
    filename: Incomplete
    plugins: Incomplete
    results: Incomplete
    statistics: Incomplete
    processor: Incomplete
    display_name: Incomplete
    should_process: bool
    def __init__(self, *, filename: str, plugins: Checkers, options: argparse.Namespace) -> None: ...
    def report(self, error_code: str | None, line_number: int, column: int, text: str) -> str: ...
    def run_check(self, plugin: LoadedPlugin, **arguments: Any) -> Any: ...
    def run_ast_checks(self) -> None: ...
    def run_logical_checks(self) -> None: ...
    def run_physical_checks(self, physical_line: str) -> None: ...
    def process_tokens(self) -> None: ...
    def run_checks(self) -> tuple[str, Results, dict[str, int]]: ...
    def handle_newline(self, token_type: int) -> None: ...
    def check_physical_eol(self, token: tokenize.TokenInfo, prev_physical: str) -> None: ...

def find_offset(offset: int, mapping: _LogicalMapping) -> tuple[int, int]: ...
