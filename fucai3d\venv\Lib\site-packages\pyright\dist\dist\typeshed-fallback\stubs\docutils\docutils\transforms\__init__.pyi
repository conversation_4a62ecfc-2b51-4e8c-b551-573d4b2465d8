from _typeshed import Incomplete

from docutils.nodes import Node, document

class Transform:
    def __init__(self, document: document, startnode: Node | None = None): ...
    def __getattr__(self, name: str, /) -> Incomplete: ...

class Transformer:
    def __init__(self, document: document): ...
    def add_transform(self, transform_class: type[Transform], priority: int | None = None, **kwargs) -> None: ...
    def __getattr__(self, name: str, /) -> Incomplete: ...

def __getattr__(name: str): ...  # incomplete module
