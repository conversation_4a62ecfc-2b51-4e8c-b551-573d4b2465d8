from _typeshed import Incomplete

from ._inputstream import _InputStream

entitiesTrie: Incomplete
attributeMap = dict

class HTMLTokenizer:
    stream: Incomplete
    parser: Incomplete
    escapeFlag: bool
    lastFourChars: Incomplete
    state: Incomplete
    escape: bool
    currentToken: Incomplete
    def __init__(self, stream: _InputStream, parser=None, **kwargs) -> None: ...
    tokenQueue: Incomplete
    def __iter__(self): ...
    def consumeNumberEntity(self, isHex): ...
    def consumeEntity(self, allowedChar=None, fromAttribute: bool = False) -> None: ...
    def processEntityInAttribute(self, allowedChar) -> None: ...
    def emitCurrentToken(self) -> None: ...
    def dataState(self): ...
    def entityDataState(self): ...
    def rcdataState(self): ...
    def characterReferenceInRcdata(self): ...
    def rawtextState(self): ...
    def scriptDataState(self): ...
    def plaintextState(self): ...
    def tagOpenState(self): ...
    def closeTagOpenState(self): ...
    def tagNameState(self): ...
    temporaryBuffer: str
    def rcdataLessThanSignState(self): ...
    def rcdataEndTagOpenState(self): ...
    def rcdataEndTagNameState(self): ...
    def rawtextLessThanSignState(self): ...
    def rawtextEndTagOpenState(self): ...
    def rawtextEndTagNameState(self): ...
    def scriptDataLessThanSignState(self) -> bool: ...
    def scriptDataEndTagOpenState(self) -> bool: ...
    def scriptDataEndTagNameState(self) -> bool: ...
    def scriptDataEscapeStartState(self) -> bool: ...
    def scriptDataEscapeStartDashState(self) -> bool: ...
    def scriptDataEscapedState(self) -> bool: ...
    def scriptDataEscapedDashState(self) -> bool: ...
    def scriptDataEscapedDashDashState(self) -> bool: ...
    def scriptDataEscapedLessThanSignState(self) -> bool: ...
    def scriptDataEscapedEndTagOpenState(self) -> bool: ...
    def scriptDataEscapedEndTagNameState(self) -> bool: ...
    def scriptDataDoubleEscapeStartState(self) -> bool: ...
    def scriptDataDoubleEscapedState(self) -> bool: ...
    def scriptDataDoubleEscapedDashState(self) -> bool: ...
    def scriptDataDoubleEscapedDashDashState(self) -> bool: ...
    def scriptDataDoubleEscapedLessThanSignState(self) -> bool: ...
    def scriptDataDoubleEscapeEndState(self) -> bool: ...
    def beforeAttributeNameState(self): ...
    def attributeNameState(self): ...
    def afterAttributeNameState(self): ...
    def beforeAttributeValueState(self): ...
    def attributeValueDoubleQuotedState(self): ...
    def attributeValueSingleQuotedState(self): ...
    def attributeValueUnQuotedState(self): ...
    def afterAttributeValueState(self): ...
    def selfClosingStartTagState(self): ...
    def bogusCommentState(self): ...
    def markupDeclarationOpenState(self): ...
    def commentStartState(self) -> bool: ...
    def commentStartDashState(self) -> bool: ...
    def commentState(self) -> bool: ...
    def commentEndDashState(self) -> bool: ...
    def commentEndState(self) -> bool: ...
    def commentEndBangState(self) -> bool: ...
    def doctypeState(self) -> bool: ...
    def beforeDoctypeNameState(self) -> bool: ...
    def doctypeNameState(self) -> bool: ...
    def afterDoctypeNameState(self) -> bool: ...
    def afterDoctypePublicKeywordState(self) -> bool: ...
    def beforeDoctypePublicIdentifierState(self): ...
    def doctypePublicIdentifierDoubleQuotedState(self): ...
    def doctypePublicIdentifierSingleQuotedState(self): ...
    def afterDoctypePublicIdentifierState(self): ...
    def betweenDoctypePublicAndSystemIdentifiersState(self): ...
    def afterDoctypeSystemKeywordState(self): ...
    def beforeDoctypeSystemIdentifierState(self): ...
    def doctypeSystemIdentifierDoubleQuotedState(self): ...
    def doctypeSystemIdentifierSingleQuotedState(self): ...
    def afterDoctypeSystemIdentifierState(self): ...
    def bogusDoctypeState(self): ...
    def cdataSectionState(self): ...
