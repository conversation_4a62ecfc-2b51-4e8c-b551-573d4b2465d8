from collections.abc import Callable
from logging import Logger

from ._types import Reader

logger: Logger

class WrongBox(Exception): ...
class NoParser(Exception): ...
class BoxVersion(Exception): ...
class BadSize(Exception): ...

class Box:
    version: int
    minor_version: int
    item_count: int
    size: int
    after: int
    pos: int
    compat: list[bytes]
    base_offset: int
    subs: dict[str, Box]
    locs: dict[int, list[tuple[int, int]]]
    exif_infe: Box | None
    item_id: int
    item_type: bytes
    item_name: bytes
    item_protection_index: int
    major_brand: bytes
    offset_size: int
    length_size: int
    base_offset_size: int
    index_size: int
    flags: int
    name: str
    def __init__(self, name: str) -> None: ...
    def set_sizes(self, offset: int, length: int, base_offset: int, index: int) -> None: ...
    def set_full(self, vflags: int) -> None: ...

class HEICExifFinder:
    file_handle: Reader
    def __init__(self, file_handle: Reader) -> None: ...
    def get(self, nbytes: int) -> bytes: ...
    def get16(self) -> int: ...
    def get32(self) -> int: ...
    def get64(self) -> int: ...
    def get_int4x2(self) -> tuple[int, int]: ...
    def get_int(self, size: int) -> int: ...
    def get_string(self) -> bytes: ...
    def next_box(self) -> Box: ...
    def get_full(self, box: Box) -> None: ...
    def skip(self, box: Box) -> None: ...
    def expect_parse(self, name: str) -> Box: ...
    def get_parser(self, box: Box) -> Callable[[Box], None]: ...
    def parse_box(self, box: Box) -> Box: ...
    def find_exif(self) -> tuple[int, bytes]: ...
