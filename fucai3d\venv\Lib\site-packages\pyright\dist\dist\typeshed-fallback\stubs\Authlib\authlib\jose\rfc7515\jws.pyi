from _typeshed import Incomplete

class JsonWebSignature:
    REGISTERED_HEADER_PARAMETER_NAMES: Incomplete
    ALGORITHMS_REGISTRY: Incomplete
    def __init__(self, algorithms=None, private_headers=None) -> None: ...
    @classmethod
    def register_algorithm(cls, algorithm) -> None: ...
    def serialize_compact(self, protected, payload, key): ...
    def deserialize_compact(self, s, key, decode=None): ...
    def serialize_json(self, header_obj, payload, key): ...
    def deserialize_json(self, obj, key, decode=None): ...
    def serialize(self, header, payload, key): ...
    def deserialize(self, s, key, decode=None): ...
